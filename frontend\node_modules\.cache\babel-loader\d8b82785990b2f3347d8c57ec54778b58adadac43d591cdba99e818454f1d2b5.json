{"ast": null, "code": "import { parse, format, resolve } from \"url\";\nimport { deprecation } from \"./logging/deprecation.mjs\";\nconst url = {\n  /**\n   * @deprecated since 7.3.0\n   */\n  get parse() {\n    return deprecation(\"7.3.0\", \"utils.url.parse is deprecated, use native URL API instead.\"), parse;\n  },\n  /**\n   * @deprecated since 7.3.0\n   */\n  get format() {\n    return deprecation(\"7.3.0\", \"utils.url.format is deprecated, use native URL API instead.\"), format;\n  },\n  /**\n   * @deprecated since 7.3.0\n   */\n  get resolve() {\n    return deprecation(\"7.3.0\", \"utils.url.resolve is deprecated, use native URL API instead.\"), resolve;\n  }\n};\nexport { url };", "map": {"version": 3, "names": ["url", "parse", "deprecation", "format", "resolve"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\utils\\src\\url.ts"], "sourcesContent": ["/*\n * This file contains redeclared types for Node `url` and `querystring` modules. These modules\n * don't provide their own typings but instead are a part of the full Node typings. The purpose of\n * this file is to redeclare the required types to avoid having the whole Node types as a\n * dependency.\n */\n\nimport { format as _format, parse as _parse, resolve as _resolve } from 'url';\nimport { deprecation } from './logging/deprecation';\n\ninterface ParsedUrlQuery\n{\n    [key: string]: string | string[];\n}\n\ninterface ParsedUrlQueryInput\n{\n    [key: string]: unknown;\n}\n\ninterface UrlObjectCommon\n{\n    auth?: string;\n    hash?: string;\n    host?: string;\n    hostname?: string;\n    href?: string;\n    path?: string;\n    pathname?: string;\n    protocol?: string;\n    search?: string;\n    slashes?: boolean;\n}\n\n// Input to `url.format`\ninterface UrlObject extends UrlObjectCommon\n{\n    port?: string | number;\n    query?: string | null | ParsedUrlQueryInput;\n}\n\n// Output of `url.parse`\ninterface Url extends UrlObjectCommon\n{\n    port?: string;\n    query?: string | null | ParsedUrlQuery;\n}\n\ninterface UrlWithParsedQuery extends Url\n{\n    query: ParsedUrlQuery;\n}\n\ninterface UrlWithStringQuery extends Url\n{\n    query: string | null;\n}\n\ninterface URLFormatOptions\n{\n    auth?: boolean;\n    fragment?: boolean;\n    search?: boolean;\n    unicode?: boolean;\n}\n\ntype ParseFunction = {\n    (urlStr: string): UrlWithStringQuery;\n    (urlStr: string, parseQueryString: false | undefined, slashesDenoteHost?: boolean): UrlWithStringQuery;\n    (urlStr: string, parseQueryString: true, slashesDenoteHost?: boolean): UrlWithParsedQuery;\n    (urlStr: string, parseQueryString: boolean, slashesDenoteHost?: boolean): Url;\n};\n\ntype FormatFunction = {\n    (URL: URL, options?: URLFormatOptions): string;\n    (urlObject: UrlObject | string): string;\n};\n\ntype ResolveFunction = {\n    (from: string, to: string): string;\n};\n\nexport const url = {\n    /**\n     * @deprecated since 7.3.0\n     */\n    get parse()\n    {\n        if (process.env.DEBUG)\n        {\n            deprecation('7.3.0', 'utils.url.parse is deprecated, use native URL API instead.');\n        }\n\n        return _parse as ParseFunction;\n    },\n    /**\n     * @deprecated since 7.3.0\n     */\n    get format()\n    {\n        if (process.env.DEBUG)\n        {\n            deprecation('7.3.0', 'utils.url.format is deprecated, use native URL API instead.');\n        }\n\n        return _format as FormatFunction;\n    },\n    /**\n     * @deprecated since 7.3.0\n     */\n    get resolve()\n    {\n        if (process.env.DEBUG)\n        {\n            deprecation('7.3.0', 'utils.url.resolve is deprecated, use native URL API instead.');\n        }\n\n        return _resolve as ResolveFunction;\n    }\n};\n"], "mappings": ";;AAkFO,MAAMA,GAAA,GAAM;EAAA;AAAA;AAAA;EAIf,IAAIC,MAAA,EACJ;IAGoB,OAAAC,WAAA,UAAS,4DAA4D,GAG9ED,KAAA;EACX;EAAA;AAAA;AAAA;EAIA,IAAIE,OAAA,EACJ;IAGoB,OAAAD,WAAA,UAAS,6DAA6D,GAG/EC,MAAA;EACX;EAAA;AAAA;AAAA;EAIA,IAAIC,QAAA,EACJ;IAGoB,OAAAF,WAAA,UAAS,8DAA8D,GAGhFE,OAAA;EACX;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}