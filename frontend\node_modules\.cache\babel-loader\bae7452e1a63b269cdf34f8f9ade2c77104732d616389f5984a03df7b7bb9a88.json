{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Projects\\\\Python\\\\EU4\\\\frontend\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport axios from \"axios\";\nimport WorldMap from './WorldMap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst INITIAL_STATS = {\n  population: 1000000,\n  resources: 100,\n  stability: 100\n};\nconst WIN_RESOURCES = 1000;\nconst LOSE_STABILITY = 0;\nexport default function App() {\n  _s();\n  var _playerCountry$proper2, _playerCountry$proper3;\n  const [countries, setCountries] = useState([]);\n  const [playerCountry, setPlayerCountry] = useState(null);\n  const [countryStats, setCountryStats] = useState({});\n  const [turn, setTurn] = useState(1);\n  const [message, setMessage] = useState(\"\");\n  const [actionTaken, setActionTaken] = useState(false);\n  const [event, setEvent] = useState(null);\n  const [diplomacy, setDiplomacy] = useState([]);\n\n  // Load countries on mount\n  useEffect(() => {\n    axios.get(\"http://localhost:8000/countries\").then(r => r.data).then(data => {\n      console.log(\"Fetched countries:\", data); // DEBUG LOG\n      setCountries(data);\n      // Initialize stats for all countries with a valid NAME\n      const stats = {};\n      data.forEach(c => {\n        var _c$properties;\n        const name = (_c$properties = c.properties) === null || _c$properties === void 0 ? void 0 : _c$properties.NAME;\n        if (name) {\n          stats[name] = {\n            ...INITIAL_STATS\n          };\n        }\n      });\n      setCountryStats(stats);\n    });\n  }, []);\n\n  // Fetch event and diplomacy at start and on turn change\n  useEffect(() => {\n    var _playerCountry$proper;\n    if (!playerCountry || !((_playerCountry$proper = playerCountry.properties) !== null && _playerCountry$proper !== void 0 && _playerCountry$proper.NAME)) return;\n    axios.get(`http://localhost:8000/event?country=${encodeURIComponent(playerCountry.properties.NAME)}`).then(r => setEvent(r.data));\n    axios.get(`http://localhost:8000/diplomacy?country=${encodeURIComponent(playerCountry.properties.NAME)}`).then(r => setDiplomacy(r.data));\n  }, [turn, playerCountry]);\n\n  // Player picks a country at start\n  if (!countries.length || !Object.keys(countryStats).length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading world data...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 12\n    }, this);\n  }\n  if (!playerCountry || !((_playerCountry$proper2 = playerCountry.properties) !== null && _playerCountry$proper2 !== void 0 && _playerCountry$proper2.NAME)) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Choose Your Country\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(WorldMap, {\n        onSelectCountry: c => {\n          var _c$properties2;\n          return (c === null || c === void 0 ? void 0 : (_c$properties2 = c.properties) === null || _c$properties2 === void 0 ? void 0 : _c$properties2.NAME) && setPlayerCountry(c);\n        },\n        selectedCountry: null\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Click a country to play as it.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this);\n  }\n  const stats = countryStats[playerCountry.properties.NAME];\n  if (!stats) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading country stats...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Player actions\n  const invest = () => {\n    if (actionTaken) return;\n    setCountryStats(stats => ({\n      ...stats,\n      [playerCountry.properties.NAME]: {\n        ...stats[playerCountry.properties.NAME],\n        resources: stats[playerCountry.properties.NAME].resources + 50,\n        stability: stats[playerCountry.properties.NAME].stability - 2\n      }\n    }));\n    setMessage(\"You invested in your economy! Resources +50, Stability -2\");\n    setActionTaken(true);\n  };\n  const build = () => {\n    if (actionTaken) return;\n    setCountryStats(stats => ({\n      ...stats,\n      [playerCountry.properties.NAME]: {\n        ...stats[playerCountry.properties.NAME],\n        population: Math.floor(stats[playerCountry.properties.NAME].population * 1.02),\n        resources: stats[playerCountry.properties.NAME].resources - 20,\n        stability: stats[playerCountry.properties.NAME].stability + 1\n      }\n    }));\n    setMessage(\"You built infrastructure! Population +2%, Resources -20, Stability +1\");\n    setActionTaken(true);\n  };\n  const propaganda = () => {\n    if (actionTaken) return;\n    setCountryStats(stats => ({\n      ...stats,\n      [playerCountry.properties.NAME]: {\n        ...stats[playerCountry.properties.NAME],\n        stability: stats[playerCountry.properties.NAME].stability + 10,\n        resources: stats[playerCountry.properties.NAME].resources - 10\n      }\n    }));\n    setMessage(\"You ran propaganda! Stability +10, Resources -10\");\n    setActionTaken(true);\n  };\n\n  // Apply event effect to player country\n  const applyEvent = () => {\n    if (!event) return;\n    axios.post(\"http://localhost:8000/event/apply\", {\n      country: playerCountry.properties.NAME,\n      effect: event.effect\n    }).then(res => {\n      if (res.data.success && res.data.country) {\n        setCountryStats(stats => ({\n          ...stats,\n          [playerCountry.properties.NAME]: {\n            ...stats[playerCountry.properties.NAME],\n            ...res.data.country\n          }\n        }));\n        setMessage(`Event applied: ${event.effect}`);\n      } else {\n        setMessage(\"Failed to apply event.\");\n      }\n    });\n  };\n\n  // Perform diplomacy action\n  const performDiplomacy = (action, target) => {\n    axios.post(\"http://localhost:8000/diplomacy/perform\", {\n      country: playerCountry.properties.NAME,\n      action,\n      target\n    }).then(res => {\n      setMessage(res.data.result || \"Diplomacy action performed.\");\n    });\n  };\n\n  // End turn: AI and player stats update\n  const nextTurn = () => {\n    setTurn(t => t + 1);\n    setActionTaken(false);\n    setMessage(\"\");\n    setCountryStats(stats => {\n      const newStats = {\n        ...stats\n      };\n      Object.keys(newStats).forEach(name => {\n        // AI: grow pop/resources, random stability change\n        if (name !== playerCountry.properties.NAME) {\n          newStats[name] = {\n            ...newStats[name],\n            population: Math.floor(newStats[name].population * (1.01 + Math.random() * 0.01)),\n            resources: newStats[name].resources + Math.floor(Math.random() * 20),\n            stability: Math.max(0, Math.min(100, newStats[name].stability + Math.floor(Math.random() * 5 - 2)))\n          };\n        } else {\n          // Player: small passive growth\n          newStats[name] = {\n            ...newStats[name],\n            population: Math.floor(newStats[name].population * 1.01),\n            resources: newStats[name].resources + 5,\n            stability: Math.max(0, Math.min(100, newStats[name].stability))\n          };\n        }\n      });\n      return newStats;\n    });\n  };\n\n  // Win/Lose conditions\n  if (stats.resources >= WIN_RESOURCES) {\n    return /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Victory! You built a prosperous nation.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 12\n    }, this);\n  }\n  if (stats.stability <= LOSE_STABILITY) {\n    return /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Your country collapsed due to instability. Game Over.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Empires & Revolutions\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [\"Turn: \", turn]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(WorldMap, {\n      onSelectCountry: () => {},\n      selectedCountry: playerCountry\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), event && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#333',\n        color: '#fff',\n        padding: 10,\n        margin: 10,\n        borderRadius: 8\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: [\"Event: \", event.title]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: event.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: 'lightgreen'\n        },\n        children: event.effect\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: applyEvent,\n        children: \"Apply Event Effect\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#222',\n        color: '#fff',\n        padding: 10,\n        margin: 10,\n        borderRadius: 8\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: (_playerCountry$proper3 = playerCountry.properties) === null || _playerCountry$proper3 === void 0 ? void 0 : _playerCountry$proper3.NAME\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Population: \", stats.population.toLocaleString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Resources: \", stats.resources]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Stability: \", stats.stability]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: invest,\n        disabled: actionTaken,\n        children: \"Invest\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: build,\n        disabled: actionTaken,\n        style: {\n          marginLeft: 8\n        },\n        children: \"Build\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: propaganda,\n        disabled: actionTaken,\n        style: {\n          marginLeft: 8\n        },\n        children: \"Propaganda\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#223',\n        color: '#fff',\n        padding: 10,\n        margin: 10,\n        borderRadius: 8\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Diplomacy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), diplomacy.length === 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No diplomatic actions available.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 36\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: diplomacy.map((d, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: [d.action, \" \", d.target && /*#__PURE__*/_jsxDEV(\"b\", {\n            children: [\"with \", d.target]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 39\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              marginLeft: 8\n            },\n            onClick: () => performDiplomacy(d.action, d.target),\n            children: \"Do\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this)]\n        }, i, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: nextTurn,\n      children: \"End Turn\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        color: 'yellow',\n        margin: 10\n      },\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 19\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"c760sk3bkS0WjL4Xygqt/Hrnv/Y=\");\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "WorldMap", "jsxDEV", "_jsxDEV", "INITIAL_STATS", "population", "resources", "stability", "WIN_RESOURCES", "LOSE_STABILITY", "App", "_s", "_playerCountry$proper2", "_playerCountry$proper3", "countries", "setCountries", "playerCountry", "setPlayerCountry", "countryStats", "setCountryStats", "turn", "setTurn", "message", "setMessage", "actionTaken", "setActionTaken", "event", "setEvent", "diplomacy", "setDiplomacy", "get", "then", "r", "data", "console", "log", "stats", "for<PERSON>ach", "c", "_c$properties", "name", "properties", "NAME", "_playerCountry$proper", "encodeURIComponent", "length", "Object", "keys", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSelectCountry", "_c$properties2", "selectedCountry", "invest", "build", "Math", "floor", "propaganda", "applyEvent", "post", "country", "effect", "res", "success", "performDiplomacy", "action", "target", "result", "nextTurn", "t", "newStats", "random", "max", "min", "style", "background", "color", "padding", "margin", "borderRadius", "title", "description", "onClick", "toLocaleString", "disabled", "marginLeft", "map", "d", "i", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Projects/Python/EU4/frontend/src/App.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport axios from \"axios\";\r\nimport WorldMap from './WorldMap';\r\n\r\nconst INITIAL_STATS = { population: 1000000, resources: 100, stability: 100 };\r\nconst WIN_RESOURCES = 1000;\r\nconst LOSE_STABILITY = 0;\r\n\r\nexport default function App() {\r\n  const [countries, setCountries] = useState([]);\r\n  const [playerCountry, setPlayerCountry] = useState(null);\r\n  const [countryStats, setCountryStats] = useState({});\r\n  const [turn, setTurn] = useState(1);\r\n  const [message, setMessage] = useState(\"\");\r\n  const [actionTaken, setActionTaken] = useState(false);\r\n  const [event, setEvent] = useState(null);\r\n  const [diplomacy, setDiplomacy] = useState([]);\r\n\r\n  // Load countries on mount\r\n  useEffect(() => {\r\n    axios.get(\"http://localhost:8000/countries\")\r\n      .then(r => r.data)\r\n      .then(data => {\r\n        console.log(\"Fetched countries:\", data); // DEBUG LOG\r\n        setCountries(data);\r\n        // Initialize stats for all countries with a valid NAME\r\n        const stats = {};\r\n        data.forEach(c => {\r\n          const name = c.properties?.NAME;\r\n          if (name) {\r\n            stats[name] = { ...INITIAL_STATS };\r\n          }\r\n        });\r\n        setCountryStats(stats);\r\n      });\r\n  }, []);\r\n\r\n  // Fetch event and diplomacy at start and on turn change\r\n  useEffect(() => {\r\n    if (!playerCountry || !playerCountry.properties?.NAME) return;\r\n    axios.get(`http://localhost:8000/event?country=${encodeURIComponent(playerCountry.properties.NAME)}`)\r\n      .then(r => setEvent(r.data));\r\n    axios.get(`http://localhost:8000/diplomacy?country=${encodeURIComponent(playerCountry.properties.NAME)}`)\r\n      .then(r => setDiplomacy(r.data));\r\n  }, [turn, playerCountry]);\r\n\r\n  // Player picks a country at start\r\n  if (!countries.length || !Object.keys(countryStats).length) {\r\n    return <div>Loading world data...</div>;\r\n  }\r\n  if (!playerCountry || !playerCountry.properties?.NAME) {\r\n    return (\r\n      <div>\r\n        <h1>Choose Your Country</h1>\r\n        <WorldMap onSelectCountry={c => c?.properties?.NAME && setPlayerCountry(c)} selectedCountry={null} />\r\n        <p>Click a country to play as it.</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const stats = countryStats[playerCountry.properties.NAME];\r\n  if (!stats) {\r\n    return <div>Loading country stats...</div>;\r\n  }\r\n\r\n  // Player actions\r\n  const invest = () => {\r\n    if (actionTaken) return;\r\n    setCountryStats(stats => ({\r\n      ...stats,\r\n      [playerCountry.properties.NAME]: {\r\n        ...stats[playerCountry.properties.NAME],\r\n        resources: stats[playerCountry.properties.NAME].resources + 50,\r\n        stability: stats[playerCountry.properties.NAME].stability - 2\r\n      }\r\n    }));\r\n    setMessage(\"You invested in your economy! Resources +50, Stability -2\");\r\n    setActionTaken(true);\r\n  };\r\n  const build = () => {\r\n    if (actionTaken) return;\r\n    setCountryStats(stats => ({\r\n      ...stats,\r\n      [playerCountry.properties.NAME]: {\r\n        ...stats[playerCountry.properties.NAME],\r\n        population: Math.floor(stats[playerCountry.properties.NAME].population * 1.02),\r\n        resources: stats[playerCountry.properties.NAME].resources - 20,\r\n        stability: stats[playerCountry.properties.NAME].stability + 1\r\n      }\r\n    }));\r\n    setMessage(\"You built infrastructure! Population +2%, Resources -20, Stability +1\");\r\n    setActionTaken(true);\r\n  };\r\n  const propaganda = () => {\r\n    if (actionTaken) return;\r\n    setCountryStats(stats => ({\r\n      ...stats,\r\n      [playerCountry.properties.NAME]: {\r\n        ...stats[playerCountry.properties.NAME],\r\n        stability: stats[playerCountry.properties.NAME].stability + 10,\r\n        resources: stats[playerCountry.properties.NAME].resources - 10\r\n      }\r\n    }));\r\n    setMessage(\"You ran propaganda! Stability +10, Resources -10\");\r\n    setActionTaken(true);\r\n  };\r\n\r\n  // Apply event effect to player country\r\n  const applyEvent = () => {\r\n    if (!event) return;\r\n    axios.post(\"http://localhost:8000/event/apply\", {\r\n      country: playerCountry.properties.NAME,\r\n      effect: event.effect\r\n    }).then(res => {\r\n      if (res.data.success && res.data.country) {\r\n        setCountryStats(stats => ({\r\n          ...stats,\r\n          [playerCountry.properties.NAME]: {\r\n            ...stats[playerCountry.properties.NAME],\r\n            ...res.data.country\r\n          }\r\n        }));\r\n        setMessage(`Event applied: ${event.effect}`);\r\n      } else {\r\n        setMessage(\"Failed to apply event.\");\r\n      }\r\n    });\r\n  };\r\n\r\n  // Perform diplomacy action\r\n  const performDiplomacy = (action, target) => {\r\n    axios.post(\"http://localhost:8000/diplomacy/perform\", {\r\n      country: playerCountry.properties.NAME,\r\n      action,\r\n      target\r\n    }).then(res => {\r\n      setMessage(res.data.result || \"Diplomacy action performed.\");\r\n    });\r\n  };\r\n\r\n  // End turn: AI and player stats update\r\n  const nextTurn = () => {\r\n    setTurn(t => t + 1);\r\n    setActionTaken(false);\r\n    setMessage(\"\");\r\n    setCountryStats(stats => {\r\n      const newStats = { ...stats };\r\n      Object.keys(newStats).forEach(name => {\r\n        // AI: grow pop/resources, random stability change\r\n        if (name !== playerCountry.properties.NAME) {\r\n          newStats[name] = {\r\n            ...newStats[name],\r\n            population: Math.floor(newStats[name].population * (1.01 + Math.random() * 0.01)),\r\n            resources: newStats[name].resources + Math.floor(Math.random() * 20),\r\n            stability: Math.max(0, Math.min(100, newStats[name].stability + Math.floor(Math.random() * 5 - 2)))\r\n          };\r\n        } else {\r\n          // Player: small passive growth\r\n          newStats[name] = {\r\n            ...newStats[name],\r\n            population: Math.floor(newStats[name].population * 1.01),\r\n            resources: newStats[name].resources + 5,\r\n            stability: Math.max(0, Math.min(100, newStats[name].stability))\r\n          };\r\n        }\r\n      });\r\n      return newStats;\r\n    });\r\n  };\r\n\r\n  // Win/Lose conditions\r\n  if (stats.resources >= WIN_RESOURCES) {\r\n    return <h1>Victory! You built a prosperous nation.</h1>;\r\n  }\r\n  if (stats.stability <= LOSE_STABILITY) {\r\n    return <h1>Your country collapsed due to instability. Game Over.</h1>;\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <h1>Empires & Revolutions</h1>\r\n      <div>Turn: {turn}</div>\r\n      <WorldMap onSelectCountry={() => {}} selectedCountry={playerCountry} />\r\n      {/* Event UI */}\r\n      {event && (\r\n        <div style={{ background: '#333', color: '#fff', padding: 10, margin: 10, borderRadius: 8 }}>\r\n          <h3>Event: {event.title}</h3>\r\n          <p>{event.description}</p>\r\n          <p style={{ color: 'lightgreen' }}>{event.effect}</p>\r\n          <button onClick={applyEvent}>Apply Event Effect</button>\r\n        </div>\r\n      )}\r\n      {/* Country stats and actions */}\r\n      <div style={{ background: '#222', color: '#fff', padding: 10, margin: 10, borderRadius: 8 }}>\r\n        <h2>{playerCountry.properties?.NAME}</h2>\r\n        <p>Population: {stats.population.toLocaleString()}</p>\r\n        <p>Resources: {stats.resources}</p>\r\n        <p>Stability: {stats.stability}</p>\r\n        <button onClick={invest} disabled={actionTaken}>Invest</button>\r\n        <button onClick={build} disabled={actionTaken} style={{marginLeft: 8}}>Build</button>\r\n        <button onClick={propaganda} disabled={actionTaken} style={{marginLeft: 8}}>Propaganda</button>\r\n      </div>\r\n      {/* Diplomacy UI */}\r\n      <div style={{ background: '#223', color: '#fff', padding: 10, margin: 10, borderRadius: 8 }}>\r\n        <h3>Diplomacy</h3>\r\n        {diplomacy.length === 0 && <p>No diplomatic actions available.</p>}\r\n        <ul>\r\n          {diplomacy.map((d, i) => (\r\n            <li key={i}>\r\n              {d.action} {d.target && <b>with {d.target}</b>}\r\n              <button style={{marginLeft: 8}} onClick={() => performDiplomacy(d.action, d.target)}>Do</button>\r\n            </li>\r\n          ))}\r\n        </ul>\r\n      </div>\r\n      <button onClick={nextTurn}>End Turn</button>\r\n      {message && <div style={{ color: 'yellow', margin: 10 }}>{message}</div>}\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,aAAa,GAAG;EAAEC,UAAU,EAAE,OAAO;EAAEC,SAAS,EAAE,GAAG;EAAEC,SAAS,EAAE;AAAI,CAAC;AAC7E,MAAMC,aAAa,GAAG,IAAI;AAC1B,MAAMC,cAAc,GAAG,CAAC;AAExB,eAAe,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA;EAC5B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACqB,IAAI,EAAEC,OAAO,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACAD,SAAS,CAAC,MAAM;IACdE,KAAK,CAAC8B,GAAG,CAAC,iCAAiC,CAAC,CACzCC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CACjBF,IAAI,CAACE,IAAI,IAAI;MACZC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,IAAI,CAAC,CAAC,CAAC;MACzClB,YAAY,CAACkB,IAAI,CAAC;MAClB;MACA,MAAMG,KAAK,GAAG,CAAC,CAAC;MAChBH,IAAI,CAACI,OAAO,CAACC,CAAC,IAAI;QAAA,IAAAC,aAAA;QAChB,MAAMC,IAAI,IAAAD,aAAA,GAAGD,CAAC,CAACG,UAAU,cAAAF,aAAA,uBAAZA,aAAA,CAAcG,IAAI;QAC/B,IAAIF,IAAI,EAAE;UACRJ,KAAK,CAACI,IAAI,CAAC,GAAG;YAAE,GAAGpC;UAAc,CAAC;QACpC;MACF,CAAC,CAAC;MACFe,eAAe,CAACiB,KAAK,CAAC;IACxB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAtC,SAAS,CAAC,MAAM;IAAA,IAAA6C,qBAAA;IACd,IAAI,CAAC3B,aAAa,IAAI,GAAA2B,qBAAA,GAAC3B,aAAa,CAACyB,UAAU,cAAAE,qBAAA,eAAxBA,qBAAA,CAA0BD,IAAI,GAAE;IACvD1C,KAAK,CAAC8B,GAAG,CAAC,uCAAuCc,kBAAkB,CAAC5B,aAAa,CAACyB,UAAU,CAACC,IAAI,CAAC,EAAE,CAAC,CAClGX,IAAI,CAACC,CAAC,IAAIL,QAAQ,CAACK,CAAC,CAACC,IAAI,CAAC,CAAC;IAC9BjC,KAAK,CAAC8B,GAAG,CAAC,2CAA2Cc,kBAAkB,CAAC5B,aAAa,CAACyB,UAAU,CAACC,IAAI,CAAC,EAAE,CAAC,CACtGX,IAAI,CAACC,CAAC,IAAIH,YAAY,CAACG,CAAC,CAACC,IAAI,CAAC,CAAC;EACpC,CAAC,EAAE,CAACb,IAAI,EAAEJ,aAAa,CAAC,CAAC;;EAEzB;EACA,IAAI,CAACF,SAAS,CAAC+B,MAAM,IAAI,CAACC,MAAM,CAACC,IAAI,CAAC7B,YAAY,CAAC,CAAC2B,MAAM,EAAE;IAC1D,oBAAO1C,OAAA;MAAA6C,QAAA,EAAK;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACzC;EACA,IAAI,CAACpC,aAAa,IAAI,GAAAJ,sBAAA,GAACI,aAAa,CAACyB,UAAU,cAAA7B,sBAAA,eAAxBA,sBAAA,CAA0B8B,IAAI,GAAE;IACrD,oBACEvC,OAAA;MAAA6C,QAAA,gBACE7C,OAAA;QAAA6C,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BjD,OAAA,CAACF,QAAQ;QAACoD,eAAe,EAAEf,CAAC;UAAA,IAAAgB,cAAA;UAAA,OAAI,CAAAhB,CAAC,aAADA,CAAC,wBAAAgB,cAAA,GAADhB,CAAC,CAAEG,UAAU,cAAAa,cAAA,uBAAbA,cAAA,CAAeZ,IAAI,KAAIzB,gBAAgB,CAACqB,CAAC,CAAC;QAAA,CAAC;QAACiB,eAAe,EAAE;MAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrGjD,OAAA;QAAA6C,QAAA,EAAG;MAA8B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAEV;EAEA,MAAMhB,KAAK,GAAGlB,YAAY,CAACF,aAAa,CAACyB,UAAU,CAACC,IAAI,CAAC;EACzD,IAAI,CAACN,KAAK,EAAE;IACV,oBAAOjC,OAAA;MAAA6C,QAAA,EAAK;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC5C;;EAEA;EACA,MAAMI,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAIhC,WAAW,EAAE;IACjBL,eAAe,CAACiB,KAAK,KAAK;MACxB,GAAGA,KAAK;MACR,CAACpB,aAAa,CAACyB,UAAU,CAACC,IAAI,GAAG;QAC/B,GAAGN,KAAK,CAACpB,aAAa,CAACyB,UAAU,CAACC,IAAI,CAAC;QACvCpC,SAAS,EAAE8B,KAAK,CAACpB,aAAa,CAACyB,UAAU,CAACC,IAAI,CAAC,CAACpC,SAAS,GAAG,EAAE;QAC9DC,SAAS,EAAE6B,KAAK,CAACpB,aAAa,CAACyB,UAAU,CAACC,IAAI,CAAC,CAACnC,SAAS,GAAG;MAC9D;IACF,CAAC,CAAC,CAAC;IACHgB,UAAU,CAAC,2DAA2D,CAAC;IACvEE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAMgC,KAAK,GAAGA,CAAA,KAAM;IAClB,IAAIjC,WAAW,EAAE;IACjBL,eAAe,CAACiB,KAAK,KAAK;MACxB,GAAGA,KAAK;MACR,CAACpB,aAAa,CAACyB,UAAU,CAACC,IAAI,GAAG;QAC/B,GAAGN,KAAK,CAACpB,aAAa,CAACyB,UAAU,CAACC,IAAI,CAAC;QACvCrC,UAAU,EAAEqD,IAAI,CAACC,KAAK,CAACvB,KAAK,CAACpB,aAAa,CAACyB,UAAU,CAACC,IAAI,CAAC,CAACrC,UAAU,GAAG,IAAI,CAAC;QAC9EC,SAAS,EAAE8B,KAAK,CAACpB,aAAa,CAACyB,UAAU,CAACC,IAAI,CAAC,CAACpC,SAAS,GAAG,EAAE;QAC9DC,SAAS,EAAE6B,KAAK,CAACpB,aAAa,CAACyB,UAAU,CAACC,IAAI,CAAC,CAACnC,SAAS,GAAG;MAC9D;IACF,CAAC,CAAC,CAAC;IACHgB,UAAU,CAAC,uEAAuE,CAAC;IACnFE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAMmC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIpC,WAAW,EAAE;IACjBL,eAAe,CAACiB,KAAK,KAAK;MACxB,GAAGA,KAAK;MACR,CAACpB,aAAa,CAACyB,UAAU,CAACC,IAAI,GAAG;QAC/B,GAAGN,KAAK,CAACpB,aAAa,CAACyB,UAAU,CAACC,IAAI,CAAC;QACvCnC,SAAS,EAAE6B,KAAK,CAACpB,aAAa,CAACyB,UAAU,CAACC,IAAI,CAAC,CAACnC,SAAS,GAAG,EAAE;QAC9DD,SAAS,EAAE8B,KAAK,CAACpB,aAAa,CAACyB,UAAU,CAACC,IAAI,CAAC,CAACpC,SAAS,GAAG;MAC9D;IACF,CAAC,CAAC,CAAC;IACHiB,UAAU,CAAC,kDAAkD,CAAC;IAC9DE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMoC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACnC,KAAK,EAAE;IACZ1B,KAAK,CAAC8D,IAAI,CAAC,mCAAmC,EAAE;MAC9CC,OAAO,EAAE/C,aAAa,CAACyB,UAAU,CAACC,IAAI;MACtCsB,MAAM,EAAEtC,KAAK,CAACsC;IAChB,CAAC,CAAC,CAACjC,IAAI,CAACkC,GAAG,IAAI;MACb,IAAIA,GAAG,CAAChC,IAAI,CAACiC,OAAO,IAAID,GAAG,CAAChC,IAAI,CAAC8B,OAAO,EAAE;QACxC5C,eAAe,CAACiB,KAAK,KAAK;UACxB,GAAGA,KAAK;UACR,CAACpB,aAAa,CAACyB,UAAU,CAACC,IAAI,GAAG;YAC/B,GAAGN,KAAK,CAACpB,aAAa,CAACyB,UAAU,CAACC,IAAI,CAAC;YACvC,GAAGuB,GAAG,CAAChC,IAAI,CAAC8B;UACd;QACF,CAAC,CAAC,CAAC;QACHxC,UAAU,CAAC,kBAAkBG,KAAK,CAACsC,MAAM,EAAE,CAAC;MAC9C,CAAC,MAAM;QACLzC,UAAU,CAAC,wBAAwB,CAAC;MACtC;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM4C,gBAAgB,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;IAC3CrE,KAAK,CAAC8D,IAAI,CAAC,yCAAyC,EAAE;MACpDC,OAAO,EAAE/C,aAAa,CAACyB,UAAU,CAACC,IAAI;MACtC0B,MAAM;MACNC;IACF,CAAC,CAAC,CAACtC,IAAI,CAACkC,GAAG,IAAI;MACb1C,UAAU,CAAC0C,GAAG,CAAChC,IAAI,CAACqC,MAAM,IAAI,6BAA6B,CAAC;IAC9D,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrBlD,OAAO,CAACmD,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC;IACnB/C,cAAc,CAAC,KAAK,CAAC;IACrBF,UAAU,CAAC,EAAE,CAAC;IACdJ,eAAe,CAACiB,KAAK,IAAI;MACvB,MAAMqC,QAAQ,GAAG;QAAE,GAAGrC;MAAM,CAAC;MAC7BU,MAAM,CAACC,IAAI,CAAC0B,QAAQ,CAAC,CAACpC,OAAO,CAACG,IAAI,IAAI;QACpC;QACA,IAAIA,IAAI,KAAKxB,aAAa,CAACyB,UAAU,CAACC,IAAI,EAAE;UAC1C+B,QAAQ,CAACjC,IAAI,CAAC,GAAG;YACf,GAAGiC,QAAQ,CAACjC,IAAI,CAAC;YACjBnC,UAAU,EAAEqD,IAAI,CAACC,KAAK,CAACc,QAAQ,CAACjC,IAAI,CAAC,CAACnC,UAAU,IAAI,IAAI,GAAGqD,IAAI,CAACgB,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YACjFpE,SAAS,EAAEmE,QAAQ,CAACjC,IAAI,CAAC,CAAClC,SAAS,GAAGoD,IAAI,CAACC,KAAK,CAACD,IAAI,CAACgB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;YACpEnE,SAAS,EAAEmD,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAEjB,IAAI,CAACkB,GAAG,CAAC,GAAG,EAAEH,QAAQ,CAACjC,IAAI,CAAC,CAACjC,SAAS,GAAGmD,IAAI,CAACC,KAAK,CAACD,IAAI,CAACgB,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;UACpG,CAAC;QACH,CAAC,MAAM;UACL;UACAD,QAAQ,CAACjC,IAAI,CAAC,GAAG;YACf,GAAGiC,QAAQ,CAACjC,IAAI,CAAC;YACjBnC,UAAU,EAAEqD,IAAI,CAACC,KAAK,CAACc,QAAQ,CAACjC,IAAI,CAAC,CAACnC,UAAU,GAAG,IAAI,CAAC;YACxDC,SAAS,EAAEmE,QAAQ,CAACjC,IAAI,CAAC,CAAClC,SAAS,GAAG,CAAC;YACvCC,SAAS,EAAEmD,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAEjB,IAAI,CAACkB,GAAG,CAAC,GAAG,EAAEH,QAAQ,CAACjC,IAAI,CAAC,CAACjC,SAAS,CAAC;UAChE,CAAC;QACH;MACF,CAAC,CAAC;MACF,OAAOkE,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAIrC,KAAK,CAAC9B,SAAS,IAAIE,aAAa,EAAE;IACpC,oBAAOL,OAAA;MAAA6C,QAAA,EAAI;IAAuC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EACzD;EACA,IAAIhB,KAAK,CAAC7B,SAAS,IAAIE,cAAc,EAAE;IACrC,oBAAON,OAAA;MAAA6C,QAAA,EAAI;IAAqD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EACvE;EAEA,oBACEjD,OAAA;IAAA6C,QAAA,gBACE7C,OAAA;MAAA6C,QAAA,EAAI;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC9BjD,OAAA;MAAA6C,QAAA,GAAK,QAAM,EAAC5B,IAAI;IAAA;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACvBjD,OAAA,CAACF,QAAQ;MAACoD,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAE;MAACE,eAAe,EAAEvC;IAAc;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAEtE1B,KAAK,iBACJvB,OAAA;MAAK0E,KAAK,EAAE;QAAEC,UAAU,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,YAAY,EAAE;MAAE,CAAE;MAAAlC,QAAA,gBAC1F7C,OAAA;QAAA6C,QAAA,GAAI,SAAO,EAACtB,KAAK,CAACyD,KAAK;MAAA;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7BjD,OAAA;QAAA6C,QAAA,EAAItB,KAAK,CAAC0D;MAAW;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BjD,OAAA;QAAG0E,KAAK,EAAE;UAAEE,KAAK,EAAE;QAAa,CAAE;QAAA/B,QAAA,EAAEtB,KAAK,CAACsC;MAAM;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrDjD,OAAA;QAAQkF,OAAO,EAAExB,UAAW;QAAAb,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CACN,eAEDjD,OAAA;MAAK0E,KAAK,EAAE;QAAEC,UAAU,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,YAAY,EAAE;MAAE,CAAE;MAAAlC,QAAA,gBAC1F7C,OAAA;QAAA6C,QAAA,GAAAnC,sBAAA,GAAKG,aAAa,CAACyB,UAAU,cAAA5B,sBAAA,uBAAxBA,sBAAA,CAA0B6B;MAAI;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACzCjD,OAAA;QAAA6C,QAAA,GAAG,cAAY,EAACZ,KAAK,CAAC/B,UAAU,CAACiF,cAAc,CAAC,CAAC;MAAA;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtDjD,OAAA;QAAA6C,QAAA,GAAG,aAAW,EAACZ,KAAK,CAAC9B,SAAS;MAAA;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCjD,OAAA;QAAA6C,QAAA,GAAG,aAAW,EAACZ,KAAK,CAAC7B,SAAS;MAAA;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCjD,OAAA;QAAQkF,OAAO,EAAE7B,MAAO;QAAC+B,QAAQ,EAAE/D,WAAY;QAAAwB,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC/DjD,OAAA;QAAQkF,OAAO,EAAE5B,KAAM;QAAC8B,QAAQ,EAAE/D,WAAY;QAACqD,KAAK,EAAE;UAACW,UAAU,EAAE;QAAC,CAAE;QAAAxC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACrFjD,OAAA;QAAQkF,OAAO,EAAEzB,UAAW;QAAC2B,QAAQ,EAAE/D,WAAY;QAACqD,KAAK,EAAE;UAACW,UAAU,EAAE;QAAC,CAAE;QAAAxC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,eAENjD,OAAA;MAAK0E,KAAK,EAAE;QAAEC,UAAU,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,YAAY,EAAE;MAAE,CAAE;MAAAlC,QAAA,gBAC1F7C,OAAA;QAAA6C,QAAA,EAAI;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACjBxB,SAAS,CAACiB,MAAM,KAAK,CAAC,iBAAI1C,OAAA;QAAA6C,QAAA,EAAG;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAClEjD,OAAA;QAAA6C,QAAA,EACGpB,SAAS,CAAC6D,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBAClBxF,OAAA;UAAA6C,QAAA,GACG0C,CAAC,CAACtB,MAAM,EAAC,GAAC,EAACsB,CAAC,CAACrB,MAAM,iBAAIlE,OAAA;YAAA6C,QAAA,GAAG,OAAK,EAAC0C,CAAC,CAACrB,MAAM;UAAA;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CjD,OAAA;YAAQ0E,KAAK,EAAE;cAACW,UAAU,EAAE;YAAC,CAAE;YAACH,OAAO,EAAEA,CAAA,KAAMlB,gBAAgB,CAACuB,CAAC,CAACtB,MAAM,EAAEsB,CAAC,CAACrB,MAAM,CAAE;YAAArB,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,GAFzFuC,CAAC;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGN,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACNjD,OAAA;MAAQkF,OAAO,EAAEd,QAAS;MAAAvB,QAAA,EAAC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAC3C9B,OAAO,iBAAInB,OAAA;MAAK0E,KAAK,EAAE;QAAEE,KAAK,EAAE,QAAQ;QAAEE,MAAM,EAAE;MAAG,CAAE;MAAAjC,QAAA,EAAE1B;IAAO;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrE,CAAC;AAEV;AAACzC,EAAA,CAnNuBD,GAAG;AAAAkF,EAAA,GAAHlF,GAAG;AAAA,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}