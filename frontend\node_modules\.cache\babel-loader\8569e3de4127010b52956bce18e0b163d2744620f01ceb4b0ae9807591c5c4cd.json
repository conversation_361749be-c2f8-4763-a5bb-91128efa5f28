{"ast": null, "code": "import { BufferResource, ViewableBuffer } from \"@pixi/core\";\nclass BlobResource extends BufferResource {\n  /**\n   * @param source - The buffer/URL of the texture file.\n   * @param {PIXI.IBlobResourceOptions} [options]\n   * @param {boolean} [options.autoLoad=false] - Whether to fetch the data immediately;\n   *  you can fetch it later via {@link PIXI.BlobResource#load}.\n   * @param {number} [options.width=1] - The width in pixels.\n   * @param {number} [options.height=1] - The height in pixels.\n   * @param {1|2|4|8} [options.unpackAlignment=4] - The alignment of the pixel rows.\n   */\n  constructor(source, options = {\n    width: 1,\n    height: 1,\n    autoLoad: !0\n  }) {\n    let origin, data;\n    typeof source == \"string\" ? (origin = source, data = new Uint8Array()) : (origin = null, data = source), super(data, options), this.origin = origin, this.buffer = data ? new ViewableBuffer(data) : null, this._load = null, this.loaded = !1, this.origin !== null && options.autoLoad !== !1 && this.load(), this.origin === null && this.buffer && (this._load = Promise.resolve(this), this.loaded = !0, this.onBlobLoaded(this.buffer.rawBinaryData));\n  }\n  onBlobLoaded(_data) {}\n  /** Loads the blob */\n  load() {\n    return this._load ? this._load : (this._load = fetch(this.origin).then(response => response.blob()).then(blob => blob.arrayBuffer()).then(arrayBuffer => (this.data = new Uint32Array(arrayBuffer), this.buffer = new ViewableBuffer(arrayBuffer), this.loaded = !0, this.onBlobLoaded(arrayBuffer), this.update(), this)), this._load);\n  }\n}\nexport { BlobResource };", "map": {"version": 3, "names": ["BlobResource", "BufferResource", "constructor", "source", "options", "width", "height", "autoLoad", "origin", "data", "Uint8Array", "buffer", "ViewableBuffer", "_load", "loaded", "load", "Promise", "resolve", "onBlobLoaded", "rawBinaryData", "_data", "fetch", "then", "response", "blob", "arrayBuffer", "Uint32Array", "update"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\compressed-textures\\src\\resources\\BlobResource.ts"], "sourcesContent": ["import { BufferResource, ViewableBuffer } from '@pixi/core';\n\nimport type { BufferType, IBufferResourceOptions } from '@pixi/core';\n\n/**\n * Constructor options for BlobResource.\n * @memberof PIXI\n */\nexport interface IBlobResourceOptions extends IBufferResourceOptions\n{\n    autoLoad?: boolean;\n}\n\n/**\n * Resource that fetches texture data over the network and stores it in a buffer.\n * @class\n * @extends PIXI.Resource\n * @memberof PIXI\n */\nexport abstract class BlobResource extends BufferResource\n{\n    /** The URL of the texture file. */\n    protected origin: string | null;\n\n    /** The viewable buffer on the data. */\n    protected buffer: ViewableBuffer | null;\n\n    protected loaded: boolean;\n\n    /**\n     * Promise when loading.\n     * @default null\n     */\n    private _load: Promise<this>;\n\n    /**\n     * @param source - The buffer/URL of the texture file.\n     * @param {PIXI.IBlobResourceOptions} [options]\n     * @param {boolean} [options.autoLoad=false] - Whether to fetch the data immediately;\n     *  you can fetch it later via {@link PIXI.BlobResource#load}.\n     * @param {number} [options.width=1] - The width in pixels.\n     * @param {number} [options.height=1] - The height in pixels.\n     * @param {1|2|4|8} [options.unpackAlignment=4] - The alignment of the pixel rows.\n     */\n    constructor(source: string | BufferType, options: IBlobResourceOptions = { width: 1, height: 1, autoLoad: true })\n    {\n        let origin: string | null;\n        let data: BufferType;\n\n        if (typeof source === 'string')\n        {\n            origin = source;\n            data = new Uint8Array();\n        }\n        else\n        {\n            origin = null;\n            data = source;\n        }\n\n        super(data, options);\n\n        this.origin = origin;\n        this.buffer = data ? new ViewableBuffer(data) : null;\n\n        this._load = null;\n        this.loaded = false;\n\n        // Allow autoLoad = \"undefined\" still load the resource by default\n        if (this.origin !== null && options.autoLoad !== false)\n        {\n            this.load();\n        }\n        if (this.origin === null && this.buffer)\n        {\n            this._load = Promise.resolve(this);\n            this.loaded = true;\n            this.onBlobLoaded(this.buffer.rawBinaryData);\n        }\n    }\n\n    protected onBlobLoaded(_data: ArrayBuffer): void\n    {\n        // TODO: Override this method\n    }\n\n    /** Loads the blob */\n    load(): Promise<this>\n    {\n        if (this._load)\n        {\n            return this._load;\n        }\n\n        this._load = fetch(this.origin)\n            .then((response) => response.blob())\n            .then((blob) => blob.arrayBuffer())\n            .then((arrayBuffer) =>\n            {\n                this.data = new Uint32Array(arrayBuffer);\n                this.buffer = new ViewableBuffer(arrayBuffer);\n                this.loaded = true;\n\n                this.onBlobLoaded(arrayBuffer);\n                this.update();\n\n                return this;\n            });\n\n        return this._load;\n    }\n}\n"], "mappings": ";AAmBO,MAAeA,YAAA,SAAqBC,cAAA,CAC3C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAwBIC,YAAYC,MAAA,EAA6BC,OAAA,GAAgC;IAAEC,KAAA,EAAO;IAAGC,MAAA,EAAQ;IAAGC,QAAA,EAAU;EAAA,GAC1G;IACI,IAAIC,MAAA,EACAC,IAAA;IAEA,OAAON,MAAA,IAAW,YAElBK,MAAA,GAASL,MAAA,EACTM,IAAA,GAAO,IAAIC,UAAA,CAAW,MAItBF,MAAA,GAAS,MACTC,IAAA,GAAON,MAAA,GAGX,MAAMM,IAAA,EAAML,OAAO,GAEnB,KAAKI,MAAA,GAASA,MAAA,EACd,KAAKG,MAAA,GAASF,IAAA,GAAO,IAAIG,cAAA,CAAeH,IAAI,IAAI,MAEhD,KAAKI,KAAA,GAAQ,MACb,KAAKC,MAAA,GAAS,IAGV,KAAKN,MAAA,KAAW,QAAQJ,OAAA,CAAQG,QAAA,KAAa,MAE7C,KAAKQ,IAAA,IAEL,KAAKP,MAAA,KAAW,QAAQ,KAAKG,MAAA,KAE7B,KAAKE,KAAA,GAAQG,OAAA,CAAQC,OAAA,CAAQ,IAAI,GACjC,KAAKH,MAAA,GAAS,IACd,KAAKI,YAAA,CAAa,KAAKP,MAAA,CAAOQ,aAAa;EAEnD;EAEUD,aAAaE,KAAA,EACvB,CAEA;EAAA;EAGAL,KAAA,EACA;IACQ,YAAKF,KAAA,GAEE,KAAKA,KAAA,IAGhB,KAAKA,KAAA,GAAQQ,KAAA,CAAM,KAAKb,MAAM,EACzBc,IAAA,CAAMC,QAAA,IAAaA,QAAA,CAASC,IAAA,EAAM,EAClCF,IAAA,CAAME,IAAA,IAASA,IAAA,CAAKC,WAAA,CAAY,CAAC,EACjCH,IAAA,CAAMG,WAAA,KAEH,KAAKhB,IAAA,GAAO,IAAIiB,WAAA,CAAYD,WAAW,GACvC,KAAKd,MAAA,GAAS,IAAIC,cAAA,CAAea,WAAW,GAC5C,KAAKX,MAAA,GAAS,IAEd,KAAKI,YAAA,CAAaO,WAAW,GAC7B,KAAKE,MAAA,CAAO,GAEL,KACV,GAEE,KAAKd,KAAA;EAChB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}