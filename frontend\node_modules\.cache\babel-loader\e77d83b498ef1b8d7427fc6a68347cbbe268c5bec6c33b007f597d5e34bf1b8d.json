{"ast": null, "code": "var UPDATE_PRIORITY = /* @__PURE__ */(UPDATE_PRIORITY2 => (UPDATE_PRIORITY2[UPDATE_PRIORITY2.INTERACTION = 50] = \"INTERACTION\", UPDATE_PRIORITY2[UPDATE_PRIORITY2.HIGH = 25] = \"HIGH\", UPDATE_PRIORITY2[UPDATE_PRIORITY2.NORMAL = 0] = \"NORMAL\", UPDATE_PRIORITY2[UPDATE_PRIORITY2.LOW = -25] = \"LOW\", UPDATE_PRIORITY2[UPDATE_PRIORITY2.UTILITY = -50] = \"UTILITY\", UPDATE_PRIORITY2))(UPDATE_PRIORITY || {});\nexport { UPDATE_PRIORITY };", "map": {"version": 3, "names": ["UPDATE_PRIORITY", "UPDATE_PRIORITY2", "INTERACTION", "HIGH", "NORMAL", "LOW", "UTILITY"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\ticker\\src\\const.ts"], "sourcesContent": ["/**\n * Represents the update priorities used by internal PIXI classes when registered with\n * the {@link PIXI.Ticker} object. Higher priority items are updated first and lower\n * priority items, such as render, should go later.\n * @static\n * @memberof PIXI\n * @enum {number}\n */\nexport enum UPDATE_PRIORITY\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    /**\n     * Highest priority used for interaction events in {@link PIXI.EventSystem}\n     * @default 50\n     */\n    INTERACTION = 50,\n    /**\n     * High priority updating, used by {@link PIXI.AnimatedSprite}\n     * @default 25\n     */\n    HIGH = 25,\n    /**\n     * Default priority for ticker events, see {@link PIXI.Ticker#add}.\n     * @default 0\n     */\n    NORMAL = 0,\n    /**\n     * Low priority used for {@link PIXI.Application} rendering.\n     * @default -25\n     */\n    LOW = -25,\n    /**\n     * Lowest priority used for {@link PIXI.BasePrepare} utility.\n     * @default -50\n     */\n    UTILITY = -50,\n}\n"], "mappings": "AAQY,IAAAA,eAAA,mBAAAC,gBAAA,KAORA,gBAAA,CAAAA,gBAAA,CAAAC,WAAA,GAAc,EAAd,mBAKAD,gBAAA,CAAAA,gBAAA,CAAAE,IAAA,GAAO,EAAP,YAKAF,gBAAA,CAAAA,gBAAA,CAAAG,MAAA,GAAS,KAAT,UAKAH,gBAAA,CAAAA,gBAAA,CAAAI,GAAA,GAAM,OAAN,OAKAJ,gBAAA,CAAAA,gBAAA,CAAAK,OAAA,GAAU,GAAV,eA3BQL,gBAAA,GAAAD,eAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}