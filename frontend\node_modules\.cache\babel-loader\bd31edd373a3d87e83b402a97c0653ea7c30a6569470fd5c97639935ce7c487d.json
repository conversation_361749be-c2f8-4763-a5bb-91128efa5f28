{"ast": null, "code": "function getBufferType(array) {\n  if (array.BYTES_PER_ELEMENT === 4) return array instanceof Float32Array ? \"Float32Array\" : array instanceof Uint32Array ? \"Uint32Array\" : \"Int32Array\";\n  if (array.BYTES_PER_ELEMENT === 2) {\n    if (array instanceof Uint16Array) return \"Uint16Array\";\n  } else if (array.BYTES_PER_ELEMENT === 1 && array instanceof Uint8Array) return \"Uint8Array\";\n  return null;\n}\nexport { getBufferType };", "map": {"version": 3, "names": ["getBufferType", "array", "BYTES_PER_ELEMENT", "Float32Array", "Uint32Array", "Uint16Array", "Uint8Array"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\utils\\src\\data\\getBufferType.ts"], "sourcesContent": ["import type { ITypedArray } from '@pixi/core';\n\nexport function getBufferType(\n    array: ITypedArray\n): 'Float32Array' | 'Uint32Array' | 'Int32Array' | 'Uint16Array' | 'Uint8Array' | null\n{\n    if (array.BYTES_PER_ELEMENT === 4)\n    {\n        if (array instanceof Float32Array)\n        {\n            return 'Float32Array';\n        }\n        else if (array instanceof Uint32Array)\n        {\n            return 'Uint32Array';\n        }\n\n        return 'Int32Array';\n    }\n    else if (array.BYTES_PER_ELEMENT === 2)\n    {\n        if (array instanceof Uint16Array)\n        {\n            return 'Uint16Array';\n        }\n    }\n    else if (array.BYTES_PER_ELEMENT === 1)\n    {\n        if (array instanceof Uint8Array)\n        {\n            return 'Uint8Array';\n        }\n    }\n\n    // TODO map out the rest of the array elements!\n    return null;\n}\n"], "mappings": "AAEO,SAASA,cACZC,KAAA,EAEJ;EACI,IAAIA,KAAA,CAAMC,iBAAA,KAAsB,GAE5B,OAAID,KAAA,YAAiBE,YAAA,GAEV,iBAEFF,KAAA,YAAiBG,WAAA,GAEf,gBAGJ;EAEN,IAAIH,KAAA,CAAMC,iBAAA,KAAsB;IAEjC,IAAID,KAAA,YAAiBI,WAAA,EAEV;EAAA,WAGNJ,KAAA,CAAMC,iBAAA,KAAsB,KAE7BD,KAAA,YAAiBK,UAAA,EAEV;EAKR;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}