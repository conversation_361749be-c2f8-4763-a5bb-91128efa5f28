{"ast": null, "code": "const fragTemplate = [\"precision mediump float;\", \"void main(void){\", \"float test = 0.1;\", \"%forloop%\", \"gl_FragColor = vec4(0.0);\", \"}\"].join(`\n`);\nfunction generateIfTestSrc(maxIfs) {\n  let src = \"\";\n  for (let i = 0; i < maxIfs; ++i) i > 0 && (src += `\nelse `), i < maxIfs - 1 && (src += `if(test == ${i}.0){}`);\n  return src;\n}\nfunction checkMaxIfStatementsInShader(maxIfs, gl) {\n  if (maxIfs === 0) throw new Error(\"Invalid value of `0` passed to `checkMaxIfStatementsInShader`\");\n  const shader = gl.createShader(gl.FRAGMENT_SHADER);\n  for (;;) {\n    const fragmentSrc = fragTemplate.replace(/%forloop%/gi, generateIfTestSrc(maxIfs));\n    if (gl.shaderSource(shader, fragmentSrc), gl.compileShader(shader), !gl.getShaderParameter(shader, gl.COMPILE_STATUS)) maxIfs = maxIfs / 2 | 0;else break;\n  }\n  return maxIfs;\n}\nexport { checkMaxIfStatementsInShader };", "map": {"version": 3, "names": ["fragTemplate", "join", "generateIfTestSrc", "maxIfs", "src", "i", "checkMaxIfStatementsInShader", "gl", "Error", "shader", "createShader", "FRAGMENT_SHADER", "fragmentSrc", "replace", "shaderSource", "compileShader", "getShaderParameter", "COMPILE_STATUS"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\shader\\utils\\checkMaxIfStatementsInShader.ts"], "sourcesContent": ["import type { IRenderingContext } from '../../IRenderer';\n\nconst fragTemplate = [\n    'precision mediump float;',\n    'void main(void){',\n    'float test = 0.1;',\n    '%forloop%',\n    'gl_FragColor = vec4(0.0);',\n    '}',\n].join('\\n');\n\nfunction generateIfTestSrc(maxIfs: number): string\n{\n    let src = '';\n\n    for (let i = 0; i < maxIfs; ++i)\n    {\n        if (i > 0)\n        {\n            src += '\\nelse ';\n        }\n\n        if (i < maxIfs - 1)\n        {\n            src += `if(test == ${i}.0){}`;\n        }\n    }\n\n    return src;\n}\n\nexport function checkMaxIfStatementsInShader(maxIfs: number, gl: IRenderingContext): number\n{\n    if (maxIfs === 0)\n    {\n        throw new Error('Invalid value of `0` passed to `checkMaxIfStatementsInShader`');\n    }\n\n    const shader = gl.createShader(gl.FRAGMENT_SHADER);\n\n    while (true) // eslint-disable-line no-constant-condition\n    {\n        const fragmentSrc = fragTemplate.replace(/%forloop%/gi, generateIfTestSrc(maxIfs));\n\n        gl.shaderSource(shader, fragmentSrc);\n        gl.compileShader(shader);\n\n        if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS))\n        {\n            maxIfs = (maxIfs / 2) | 0;\n        }\n        else\n        {\n            // valid!\n            break;\n        }\n    }\n\n    return maxIfs;\n}\n"], "mappings": "AAEA,MAAMA,YAAA,GAAe,CACjB,4BACA,oBACA,qBACA,aACA,6BACA,IACJ,CAAEC,IAAA,CAAK;AAAA,CAAI;AAEX,SAASC,kBAAkBC,MAAA,EAC3B;EACI,IAAIC,GAAA,GAAM;EAEV,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIF,MAAA,EAAQ,EAAEE,CAAA,EAEtBA,CAAA,GAAI,MAEJD,GAAA,IAAO;AAAA,SAGPC,CAAA,GAAIF,MAAA,GAAS,MAEbC,GAAA,IAAO,cAAcC,CAAC;EAIvB,OAAAD,GAAA;AACX;AAEgB,SAAAE,6BAA6BH,MAAA,EAAgBI,EAAA,EAC7D;EACI,IAAIJ,MAAA,KAAW,GAEL,UAAIK,KAAA,CAAM,+DAA+D;EAGnF,MAAMC,MAAA,GAASF,EAAA,CAAGG,YAAA,CAAaH,EAAA,CAAGI,eAAe;EAGjD;IACI,MAAMC,WAAA,GAAcZ,YAAA,CAAaa,OAAA,CAAQ,eAAeX,iBAAA,CAAkBC,MAAM,CAAC;IAKjF,IAHAI,EAAA,CAAGO,YAAA,CAAaL,MAAA,EAAQG,WAAW,GACnCL,EAAA,CAAGQ,aAAA,CAAcN,MAAM,GAEnB,CAACF,EAAA,CAAGS,kBAAA,CAAmBP,MAAA,EAAQF,EAAA,CAAGU,cAAc,GAEhDd,MAAA,GAAUA,MAAA,GAAS,IAAK,OAKxB;EAER;EAEO,OAAAA,MAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}