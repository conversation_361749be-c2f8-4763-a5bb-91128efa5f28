using UnityEngine;
using UnityEngine.Events;

public class ProvinceClickHandler : MonoBehaviour
{
    [Header("Events")]
    public UnityEvent<Province> OnProvinceClicked;
    
    private Province province;
    private Renderer provinceRenderer;
    private Color originalColor;
    private Color hoverColor = Color.yellow;
    private Color selectedColor = Color.green;
    
    private bool isSelected = false;
    private bool isHovered = false;

    void Start()
    {
        provinceRenderer = GetComponent<Renderer>();
        if (provinceRenderer != null)
        {
            originalColor = provinceRenderer.material.color;
        }
    }

    public void SetProvince(Province newProvince)
    {
        province = newProvince;
    }

    void OnMouseEnter()
    {
        if (!isSelected)
        {
            isHovered = true;
            SetColor(hoverColor);
        }
    }

    void OnMouseExit()
    {
        if (!isSelected)
        {
            isHovered = false;
            SetColor(originalColor);
        }
    }

    void OnMouseDown()
    {
        if (province != null)
        {
            Debug.Log($"Clicked on province: {province.Name}");
            OnProvinceClicked?.Invoke(province);
            
            // Notify GameManager or other systems
            var gameManager = FindObjectOfType<GameManager>();
            if (gameManager != null)
            {
                // gameManager.OnProvinceSelected(province);
            }
        }
    }

    public void SetSelected(bool selected)
    {
        isSelected = selected;
        if (selected)
        {
            SetColor(selectedColor);
        }
        else if (isHovered)
        {
            SetColor(hoverColor);
        }
        else
        {
            SetColor(originalColor);
        }
    }

    private void SetColor(Color color)
    {
        if (provinceRenderer != null && provinceRenderer.material != null)
        {
            provinceRenderer.material.color = color;
        }
    }

    public Province GetProvince()
    {
        return province;
    }
}
