{"ast": null, "code": "/*\n * Copyright Joyent, Inc. and other Node contributors.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a\n * copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to permit\n * persons to whom the Software is furnished to do so, subject to the\n * following conditions:\n *\n * The above copyright notice and this permission notice shall be included\n * in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n * NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n * DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n * OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n * USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\n'use strict';\n\nvar punycode = require('punycode/');\nfunction Url() {\n  this.protocol = null;\n  this.slashes = null;\n  this.auth = null;\n  this.host = null;\n  this.port = null;\n  this.hostname = null;\n  this.hash = null;\n  this.search = null;\n  this.query = null;\n  this.pathname = null;\n  this.path = null;\n  this.href = null;\n}\n\n// Reference: RFC 3986, RFC 1808, RFC 2396\n\n/*\n * define these here so at least they only have to be\n * compiled once on the first module load.\n */\nvar protocolPattern = /^([a-z0-9.+-]+:)/i,\n  portPattern = /:[0-9]*$/,\n  // Special case for a simple path URL\n  simplePathPattern = /^(\\/\\/?(?!\\/)[^?\\s]*)(\\?[^\\s]*)?$/,\n  /*\n   * RFC 2396: characters reserved for delimiting URLs.\n   * We actually just auto-escape these.\n   */\n  delims = ['<', '>', '\"', '`', ' ', '\\r', '\\n', '\\t'],\n  // RFC 2396: characters not allowed for various reasons.\n  unwise = ['{', '}', '|', '\\\\', '^', '`'].concat(delims),\n  // Allowed by RFCs, but cause of XSS attacks.  Always escape these.\n  autoEscape = ['\\''].concat(unwise),\n  /*\n   * Characters that are never ever allowed in a hostname.\n   * Note that any invalid chars are also handled, but these\n   * are the ones that are *expected* to be seen, so we fast-path\n   * them.\n   */\n  nonHostChars = ['%', '/', '?', ';', '#'].concat(autoEscape),\n  hostEndingChars = ['/', '?', '#'],\n  hostnameMaxLen = 255,\n  hostnamePartPattern = /^[+a-z0-9A-Z_-]{0,63}$/,\n  hostnamePartStart = /^([+a-z0-9A-Z_-]{0,63})(.*)$/,\n  // protocols that can allow \"unsafe\" and \"unwise\" chars.\n  unsafeProtocol = {\n    javascript: true,\n    'javascript:': true\n  },\n  // protocols that never have a hostname.\n  hostlessProtocol = {\n    javascript: true,\n    'javascript:': true\n  },\n  // protocols that always contain a // bit.\n  slashedProtocol = {\n    http: true,\n    https: true,\n    ftp: true,\n    gopher: true,\n    file: true,\n    'http:': true,\n    'https:': true,\n    'ftp:': true,\n    'gopher:': true,\n    'file:': true\n  },\n  querystring = require('qs');\nfunction urlParse(url, parseQueryString, slashesDenoteHost) {\n  if (url && typeof url === 'object' && url instanceof Url) {\n    return url;\n  }\n  var u = new Url();\n  u.parse(url, parseQueryString, slashesDenoteHost);\n  return u;\n}\nUrl.prototype.parse = function (url, parseQueryString, slashesDenoteHost) {\n  if (typeof url !== 'string') {\n    throw new TypeError(\"Parameter 'url' must be a string, not \" + typeof url);\n  }\n\n  /*\n   * Copy chrome, IE, opera backslash-handling behavior.\n   * Back slashes before the query string get converted to forward slashes\n   * See: https://code.google.com/p/chromium/issues/detail?id=25916\n   */\n  var queryIndex = url.indexOf('?'),\n    splitter = queryIndex !== -1 && queryIndex < url.indexOf('#') ? '?' : '#',\n    uSplit = url.split(splitter),\n    slashRegex = /\\\\/g;\n  uSplit[0] = uSplit[0].replace(slashRegex, '/');\n  url = uSplit.join(splitter);\n  var rest = url;\n\n  /*\n   * trim before proceeding.\n   * This is to support parse stuff like \"  http://foo.com  \\n\"\n   */\n  rest = rest.trim();\n  if (!slashesDenoteHost && url.split('#').length === 1) {\n    // Try fast path regexp\n    var simplePath = simplePathPattern.exec(rest);\n    if (simplePath) {\n      this.path = rest;\n      this.href = rest;\n      this.pathname = simplePath[1];\n      if (simplePath[2]) {\n        this.search = simplePath[2];\n        if (parseQueryString) {\n          this.query = querystring.parse(this.search.substr(1));\n        } else {\n          this.query = this.search.substr(1);\n        }\n      } else if (parseQueryString) {\n        this.search = '';\n        this.query = {};\n      }\n      return this;\n    }\n  }\n  var proto = protocolPattern.exec(rest);\n  if (proto) {\n    proto = proto[0];\n    var lowerProto = proto.toLowerCase();\n    this.protocol = lowerProto;\n    rest = rest.substr(proto.length);\n  }\n\n  /*\n   * figure out if it's got a host\n   * user@server is *always* interpreted as a hostname, and url\n   * resolution will treat //foo/bar as host=foo,path=bar because that's\n   * how the browser resolves relative URLs.\n   */\n  if (slashesDenoteHost || proto || rest.match(/^\\/\\/[^@/]+@[^@/]+/)) {\n    var slashes = rest.substr(0, 2) === '//';\n    if (slashes && !(proto && hostlessProtocol[proto])) {\n      rest = rest.substr(2);\n      this.slashes = true;\n    }\n  }\n  if (!hostlessProtocol[proto] && (slashes || proto && !slashedProtocol[proto])) {\n    /*\n     * there's a hostname.\n     * the first instance of /, ?, ;, or # ends the host.\n     *\n     * If there is an @ in the hostname, then non-host chars *are* allowed\n     * to the left of the last @ sign, unless some host-ending character\n     * comes *before* the @-sign.\n     * URLs are obnoxious.\n     *\n     * ex:\n     * http://a@b@c/ => user:a@b host:c\n     * http://a@b?@c => user:a host:c path:/?@c\n     */\n\n    /*\n     * v0.12 TODO(isaacs): This is not quite how Chrome does things.\n     * Review our test case against browsers more comprehensively.\n     */\n\n    // find the first instance of any hostEndingChars\n    var hostEnd = -1;\n    for (var i = 0; i < hostEndingChars.length; i++) {\n      var hec = rest.indexOf(hostEndingChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) {\n        hostEnd = hec;\n      }\n    }\n\n    /*\n     * at this point, either we have an explicit point where the\n     * auth portion cannot go past, or the last @ char is the decider.\n     */\n    var auth, atSign;\n    if (hostEnd === -1) {\n      // atSign can be anywhere.\n      atSign = rest.lastIndexOf('@');\n    } else {\n      /*\n       * atSign must be in auth portion.\n       * http://a@b/c@d => host:b auth:a path:/c@d\n       */\n      atSign = rest.lastIndexOf('@', hostEnd);\n    }\n\n    /*\n     * Now we have a portion which is definitely the auth.\n     * Pull that off.\n     */\n    if (atSign !== -1) {\n      auth = rest.slice(0, atSign);\n      rest = rest.slice(atSign + 1);\n      this.auth = decodeURIComponent(auth);\n    }\n\n    // the host is the remaining to the left of the first non-host char\n    hostEnd = -1;\n    for (var i = 0; i < nonHostChars.length; i++) {\n      var hec = rest.indexOf(nonHostChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) {\n        hostEnd = hec;\n      }\n    }\n    // if we still have not hit it, then the entire thing is a host.\n    if (hostEnd === -1) {\n      hostEnd = rest.length;\n    }\n    this.host = rest.slice(0, hostEnd);\n    rest = rest.slice(hostEnd);\n\n    // pull out port.\n    this.parseHost();\n\n    /*\n     * we've indicated that there is a hostname,\n     * so even if it's empty, it has to be present.\n     */\n    this.hostname = this.hostname || '';\n\n    /*\n     * if hostname begins with [ and ends with ]\n     * assume that it's an IPv6 address.\n     */\n    var ipv6Hostname = this.hostname[0] === '[' && this.hostname[this.hostname.length - 1] === ']';\n\n    // validate a little.\n    if (!ipv6Hostname) {\n      var hostparts = this.hostname.split(/\\./);\n      for (var i = 0, l = hostparts.length; i < l; i++) {\n        var part = hostparts[i];\n        if (!part) {\n          continue;\n        }\n        if (!part.match(hostnamePartPattern)) {\n          var newpart = '';\n          for (var j = 0, k = part.length; j < k; j++) {\n            if (part.charCodeAt(j) > 127) {\n              /*\n               * we replace non-ASCII char with a temporary placeholder\n               * we need this to make sure size of hostname is not\n               * broken by replacing non-ASCII by nothing\n               */\n              newpart += 'x';\n            } else {\n              newpart += part[j];\n            }\n          }\n          // we test again with ASCII char only\n          if (!newpart.match(hostnamePartPattern)) {\n            var validParts = hostparts.slice(0, i);\n            var notHost = hostparts.slice(i + 1);\n            var bit = part.match(hostnamePartStart);\n            if (bit) {\n              validParts.push(bit[1]);\n              notHost.unshift(bit[2]);\n            }\n            if (notHost.length) {\n              rest = '/' + notHost.join('.') + rest;\n            }\n            this.hostname = validParts.join('.');\n            break;\n          }\n        }\n      }\n    }\n    if (this.hostname.length > hostnameMaxLen) {\n      this.hostname = '';\n    } else {\n      // hostnames are always lower case.\n      this.hostname = this.hostname.toLowerCase();\n    }\n    if (!ipv6Hostname) {\n      /*\n       * IDNA Support: Returns a punycoded representation of \"domain\".\n       * It only converts parts of the domain name that\n       * have non-ASCII characters, i.e. it doesn't matter if\n       * you call it with a domain that already is ASCII-only.\n       */\n      this.hostname = punycode.toASCII(this.hostname);\n    }\n    var p = this.port ? ':' + this.port : '';\n    var h = this.hostname || '';\n    this.host = h + p;\n    this.href += this.host;\n\n    /*\n     * strip [ and ] from the hostname\n     * the host field still retains them, though\n     */\n    if (ipv6Hostname) {\n      this.hostname = this.hostname.substr(1, this.hostname.length - 2);\n      if (rest[0] !== '/') {\n        rest = '/' + rest;\n      }\n    }\n  }\n\n  /*\n   * now rest is set to the post-host stuff.\n   * chop off any delim chars.\n   */\n  if (!unsafeProtocol[lowerProto]) {\n    /*\n     * First, make 100% sure that any \"autoEscape\" chars get\n     * escaped, even if encodeURIComponent doesn't think they\n     * need to be.\n     */\n    for (var i = 0, l = autoEscape.length; i < l; i++) {\n      var ae = autoEscape[i];\n      if (rest.indexOf(ae) === -1) {\n        continue;\n      }\n      var esc = encodeURIComponent(ae);\n      if (esc === ae) {\n        esc = escape(ae);\n      }\n      rest = rest.split(ae).join(esc);\n    }\n  }\n\n  // chop off from the tail first.\n  var hash = rest.indexOf('#');\n  if (hash !== -1) {\n    // got a fragment string.\n    this.hash = rest.substr(hash);\n    rest = rest.slice(0, hash);\n  }\n  var qm = rest.indexOf('?');\n  if (qm !== -1) {\n    this.search = rest.substr(qm);\n    this.query = rest.substr(qm + 1);\n    if (parseQueryString) {\n      this.query = querystring.parse(this.query);\n    }\n    rest = rest.slice(0, qm);\n  } else if (parseQueryString) {\n    // no query string, but parseQueryString still requested\n    this.search = '';\n    this.query = {};\n  }\n  if (rest) {\n    this.pathname = rest;\n  }\n  if (slashedProtocol[lowerProto] && this.hostname && !this.pathname) {\n    this.pathname = '/';\n  }\n\n  // to support http.request\n  if (this.pathname || this.search) {\n    var p = this.pathname || '';\n    var s = this.search || '';\n    this.path = p + s;\n  }\n\n  // finally, reconstruct the href based on what has been validated.\n  this.href = this.format();\n  return this;\n};\n\n// format a parsed object into a url string\nfunction urlFormat(obj) {\n  /*\n   * ensure it's an object, and not a string url.\n   * If it's an obj, this is a no-op.\n   * this way, you can call url_format() on strings\n   * to clean up potentially wonky urls.\n   */\n  if (typeof obj === 'string') {\n    obj = urlParse(obj);\n  }\n  if (!(obj instanceof Url)) {\n    return Url.prototype.format.call(obj);\n  }\n  return obj.format();\n}\nUrl.prototype.format = function () {\n  var auth = this.auth || '';\n  if (auth) {\n    auth = encodeURIComponent(auth);\n    auth = auth.replace(/%3A/i, ':');\n    auth += '@';\n  }\n  var protocol = this.protocol || '',\n    pathname = this.pathname || '',\n    hash = this.hash || '',\n    host = false,\n    query = '';\n  if (this.host) {\n    host = auth + this.host;\n  } else if (this.hostname) {\n    host = auth + (this.hostname.indexOf(':') === -1 ? this.hostname : '[' + this.hostname + ']');\n    if (this.port) {\n      host += ':' + this.port;\n    }\n  }\n  if (this.query && typeof this.query === 'object' && Object.keys(this.query).length) {\n    query = querystring.stringify(this.query, {\n      arrayFormat: 'repeat',\n      addQueryPrefix: false\n    });\n  }\n  var search = this.search || query && '?' + query || '';\n  if (protocol && protocol.substr(-1) !== ':') {\n    protocol += ':';\n  }\n\n  /*\n   * only the slashedProtocols get the //.  Not mailto:, xmpp:, etc.\n   * unless they had them to begin with.\n   */\n  if (this.slashes || (!protocol || slashedProtocol[protocol]) && host !== false) {\n    host = '//' + (host || '');\n    if (pathname && pathname.charAt(0) !== '/') {\n      pathname = '/' + pathname;\n    }\n  } else if (!host) {\n    host = '';\n  }\n  if (hash && hash.charAt(0) !== '#') {\n    hash = '#' + hash;\n  }\n  if (search && search.charAt(0) !== '?') {\n    search = '?' + search;\n  }\n  pathname = pathname.replace(/[?#]/g, function (match) {\n    return encodeURIComponent(match);\n  });\n  search = search.replace('#', '%23');\n  return protocol + host + pathname + search + hash;\n};\nfunction urlResolve(source, relative) {\n  return urlParse(source, false, true).resolve(relative);\n}\nUrl.prototype.resolve = function (relative) {\n  return this.resolveObject(urlParse(relative, false, true)).format();\n};\nfunction urlResolveObject(source, relative) {\n  if (!source) {\n    return relative;\n  }\n  return urlParse(source, false, true).resolveObject(relative);\n}\nUrl.prototype.resolveObject = function (relative) {\n  if (typeof relative === 'string') {\n    var rel = new Url();\n    rel.parse(relative, false, true);\n    relative = rel;\n  }\n  var result = new Url();\n  var tkeys = Object.keys(this);\n  for (var tk = 0; tk < tkeys.length; tk++) {\n    var tkey = tkeys[tk];\n    result[tkey] = this[tkey];\n  }\n\n  /*\n   * hash is always overridden, no matter what.\n   * even href=\"\" will remove it.\n   */\n  result.hash = relative.hash;\n\n  // if the relative url is empty, then there's nothing left to do here.\n  if (relative.href === '') {\n    result.href = result.format();\n    return result;\n  }\n\n  // hrefs like //foo/bar always cut to the protocol.\n  if (relative.slashes && !relative.protocol) {\n    // take everything except the protocol from relative\n    var rkeys = Object.keys(relative);\n    for (var rk = 0; rk < rkeys.length; rk++) {\n      var rkey = rkeys[rk];\n      if (rkey !== 'protocol') {\n        result[rkey] = relative[rkey];\n      }\n    }\n\n    // urlParse appends trailing / to urls like http://www.example.com\n    if (slashedProtocol[result.protocol] && result.hostname && !result.pathname) {\n      result.pathname = '/';\n      result.path = result.pathname;\n    }\n    result.href = result.format();\n    return result;\n  }\n  if (relative.protocol && relative.protocol !== result.protocol) {\n    /*\n     * if it's a known url protocol, then changing\n     * the protocol does weird things\n     * first, if it's not file:, then we MUST have a host,\n     * and if there was a path\n     * to begin with, then we MUST have a path.\n     * if it is file:, then the host is dropped,\n     * because that's known to be hostless.\n     * anything else is assumed to be absolute.\n     */\n    if (!slashedProtocol[relative.protocol]) {\n      var keys = Object.keys(relative);\n      for (var v = 0; v < keys.length; v++) {\n        var k = keys[v];\n        result[k] = relative[k];\n      }\n      result.href = result.format();\n      return result;\n    }\n    result.protocol = relative.protocol;\n    if (!relative.host && !hostlessProtocol[relative.protocol]) {\n      var relPath = (relative.pathname || '').split('/');\n      while (relPath.length && !(relative.host = relPath.shift())) {}\n      if (!relative.host) {\n        relative.host = '';\n      }\n      if (!relative.hostname) {\n        relative.hostname = '';\n      }\n      if (relPath[0] !== '') {\n        relPath.unshift('');\n      }\n      if (relPath.length < 2) {\n        relPath.unshift('');\n      }\n      result.pathname = relPath.join('/');\n    } else {\n      result.pathname = relative.pathname;\n    }\n    result.search = relative.search;\n    result.query = relative.query;\n    result.host = relative.host || '';\n    result.auth = relative.auth;\n    result.hostname = relative.hostname || relative.host;\n    result.port = relative.port;\n    // to support http.request\n    if (result.pathname || result.search) {\n      var p = result.pathname || '';\n      var s = result.search || '';\n      result.path = p + s;\n    }\n    result.slashes = result.slashes || relative.slashes;\n    result.href = result.format();\n    return result;\n  }\n  var isSourceAbs = result.pathname && result.pathname.charAt(0) === '/',\n    isRelAbs = relative.host || relative.pathname && relative.pathname.charAt(0) === '/',\n    mustEndAbs = isRelAbs || isSourceAbs || result.host && relative.pathname,\n    removeAllDots = mustEndAbs,\n    srcPath = result.pathname && result.pathname.split('/') || [],\n    relPath = relative.pathname && relative.pathname.split('/') || [],\n    psychotic = result.protocol && !slashedProtocol[result.protocol];\n\n  /*\n   * if the url is a non-slashed url, then relative\n   * links like ../.. should be able\n   * to crawl up to the hostname, as well.  This is strange.\n   * result.protocol has already been set by now.\n   * Later on, put the first path part into the host field.\n   */\n  if (psychotic) {\n    result.hostname = '';\n    result.port = null;\n    if (result.host) {\n      if (srcPath[0] === '') {\n        srcPath[0] = result.host;\n      } else {\n        srcPath.unshift(result.host);\n      }\n    }\n    result.host = '';\n    if (relative.protocol) {\n      relative.hostname = null;\n      relative.port = null;\n      if (relative.host) {\n        if (relPath[0] === '') {\n          relPath[0] = relative.host;\n        } else {\n          relPath.unshift(relative.host);\n        }\n      }\n      relative.host = null;\n    }\n    mustEndAbs = mustEndAbs && (relPath[0] === '' || srcPath[0] === '');\n  }\n  if (isRelAbs) {\n    // it's absolute.\n    result.host = relative.host || relative.host === '' ? relative.host : result.host;\n    result.hostname = relative.hostname || relative.hostname === '' ? relative.hostname : result.hostname;\n    result.search = relative.search;\n    result.query = relative.query;\n    srcPath = relPath;\n    // fall through to the dot-handling below.\n  } else if (relPath.length) {\n    /*\n     * it's relative\n     * throw away the existing file, and take the new path instead.\n     */\n    if (!srcPath) {\n      srcPath = [];\n    }\n    srcPath.pop();\n    srcPath = srcPath.concat(relPath);\n    result.search = relative.search;\n    result.query = relative.query;\n  } else if (relative.search != null) {\n    /*\n     * just pull out the search.\n     * like href='?foo'.\n     * Put this after the other two cases because it simplifies the booleans\n     */\n    if (psychotic) {\n      result.host = srcPath.shift();\n      result.hostname = result.host;\n      /*\n       * occationaly the auth can get stuck only in host\n       * this especially happens in cases like\n       * url.resolveObject('mailto:local1@domain1', 'local2@domain2')\n       */\n      var authInHost = result.host && result.host.indexOf('@') > 0 ? result.host.split('@') : false;\n      if (authInHost) {\n        result.auth = authInHost.shift();\n        result.hostname = authInHost.shift();\n        result.host = result.hostname;\n      }\n    }\n    result.search = relative.search;\n    result.query = relative.query;\n    // to support http.request\n    if (result.pathname !== null || result.search !== null) {\n      result.path = (result.pathname ? result.pathname : '') + (result.search ? result.search : '');\n    }\n    result.href = result.format();\n    return result;\n  }\n  if (!srcPath.length) {\n    /*\n     * no path at all.  easy.\n     * we've already handled the other stuff above.\n     */\n    result.pathname = null;\n    // to support http.request\n    if (result.search) {\n      result.path = '/' + result.search;\n    } else {\n      result.path = null;\n    }\n    result.href = result.format();\n    return result;\n  }\n\n  /*\n   * if a url ENDs in . or .., then it must get a trailing slash.\n   * however, if it ends in anything else non-slashy,\n   * then it must NOT get a trailing slash.\n   */\n  var last = srcPath.slice(-1)[0];\n  var hasTrailingSlash = (result.host || relative.host || srcPath.length > 1) && (last === '.' || last === '..') || last === '';\n\n  /*\n   * strip single dots, resolve double dots to parent dir\n   * if the path tries to go above the root, `up` ends up > 0\n   */\n  var up = 0;\n  for (var i = srcPath.length; i >= 0; i--) {\n    last = srcPath[i];\n    if (last === '.') {\n      srcPath.splice(i, 1);\n    } else if (last === '..') {\n      srcPath.splice(i, 1);\n      up++;\n    } else if (up) {\n      srcPath.splice(i, 1);\n      up--;\n    }\n  }\n\n  // if the path is allowed to go above the root, restore leading ..s\n  if (!mustEndAbs && !removeAllDots) {\n    for (; up--; up) {\n      srcPath.unshift('..');\n    }\n  }\n  if (mustEndAbs && srcPath[0] !== '' && (!srcPath[0] || srcPath[0].charAt(0) !== '/')) {\n    srcPath.unshift('');\n  }\n  if (hasTrailingSlash && srcPath.join('/').substr(-1) !== '/') {\n    srcPath.push('');\n  }\n  var isAbsolute = srcPath[0] === '' || srcPath[0] && srcPath[0].charAt(0) === '/';\n\n  // put the host back\n  if (psychotic) {\n    result.hostname = isAbsolute ? '' : srcPath.length ? srcPath.shift() : '';\n    result.host = result.hostname;\n    /*\n     * occationaly the auth can get stuck only in host\n     * this especially happens in cases like\n     * url.resolveObject('mailto:local1@domain1', 'local2@domain2')\n     */\n    var authInHost = result.host && result.host.indexOf('@') > 0 ? result.host.split('@') : false;\n    if (authInHost) {\n      result.auth = authInHost.shift();\n      result.hostname = authInHost.shift();\n      result.host = result.hostname;\n    }\n  }\n  mustEndAbs = mustEndAbs || result.host && srcPath.length;\n  if (mustEndAbs && !isAbsolute) {\n    srcPath.unshift('');\n  }\n  if (srcPath.length > 0) {\n    result.pathname = srcPath.join('/');\n  } else {\n    result.pathname = null;\n    result.path = null;\n  }\n\n  // to support request.http\n  if (result.pathname !== null || result.search !== null) {\n    result.path = (result.pathname ? result.pathname : '') + (result.search ? result.search : '');\n  }\n  result.auth = relative.auth || result.auth;\n  result.slashes = result.slashes || relative.slashes;\n  result.href = result.format();\n  return result;\n};\nUrl.prototype.parseHost = function () {\n  var host = this.host;\n  var port = portPattern.exec(host);\n  if (port) {\n    port = port[0];\n    if (port !== ':') {\n      this.port = port.substr(1);\n    }\n    host = host.substr(0, host.length - port.length);\n  }\n  if (host) {\n    this.hostname = host;\n  }\n};\nexports.parse = urlParse;\nexports.resolve = urlResolve;\nexports.resolveObject = urlResolveObject;\nexports.format = urlFormat;\nexports.Url = Url;", "map": {"version": 3, "names": ["punycode", "require", "Url", "protocol", "slashes", "auth", "host", "port", "hostname", "hash", "search", "query", "pathname", "path", "href", "protocolPattern", "portPattern", "simplePathPattern", "delims", "unwise", "concat", "autoEscape", "nonHostChars", "hostEndingChars", "hostnameMaxLen", "hostnamePartPattern", "hostnamePartStart", "unsafeProtocol", "javascript", "hostlessProtocol", "slashedProtocol", "http", "https", "ftp", "gopher", "file", "querystring", "urlParse", "url", "parseQueryString", "slashesDenoteHost", "u", "parse", "prototype", "TypeError", "queryIndex", "indexOf", "splitter", "uSplit", "split", "slashRegex", "replace", "join", "rest", "trim", "length", "simplePath", "exec", "substr", "proto", "lowerProto", "toLowerCase", "match", "hostEnd", "i", "hec", "atSign", "lastIndexOf", "slice", "decodeURIComponent", "parseHost", "ipv6Hostname", "hostparts", "l", "part", "newpart", "j", "k", "charCodeAt", "validParts", "notHost", "bit", "push", "unshift", "toASCII", "p", "h", "ae", "esc", "encodeURIComponent", "escape", "qm", "s", "format", "urlFormat", "obj", "call", "Object", "keys", "stringify", "arrayFormat", "addQueryPrefix", "char<PERSON>t", "urlResolve", "source", "relative", "resolve", "resolveObject", "urlResolveObject", "rel", "result", "tkeys", "tk", "tkey", "rkeys", "rk", "rkey", "v", "re<PERSON><PERSON><PERSON>", "shift", "isSourceAbs", "isRelAbs", "mustEndAbs", "removeAllDots", "srcPath", "psychotic", "pop", "authInHost", "last", "hasTrailingSlash", "up", "splice", "isAbsolute", "exports"], "sources": ["C:/Users/<USER>/Projects/Python/EU4/frontend/node_modules/url/url.js"], "sourcesContent": ["/*\n * Copyright Joyent, Inc. and other Node contributors.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a\n * copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to permit\n * persons to whom the Software is furnished to do so, subject to the\n * following conditions:\n *\n * The above copyright notice and this permission notice shall be included\n * in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n * NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n * DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n * OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n * USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\n'use strict';\n\nvar punycode = require('punycode/');\n\nfunction Url() {\n  this.protocol = null;\n  this.slashes = null;\n  this.auth = null;\n  this.host = null;\n  this.port = null;\n  this.hostname = null;\n  this.hash = null;\n  this.search = null;\n  this.query = null;\n  this.pathname = null;\n  this.path = null;\n  this.href = null;\n}\n\n// Reference: RFC 3986, RFC 1808, RFC 2396\n\n/*\n * define these here so at least they only have to be\n * compiled once on the first module load.\n */\nvar protocolPattern = /^([a-z0-9.+-]+:)/i,\n  portPattern = /:[0-9]*$/,\n\n  // Special case for a simple path URL\n  simplePathPattern = /^(\\/\\/?(?!\\/)[^?\\s]*)(\\?[^\\s]*)?$/,\n\n  /*\n   * RFC 2396: characters reserved for delimiting URLs.\n   * We actually just auto-escape these.\n   */\n  delims = [\n    '<', '>', '\"', '`', ' ', '\\r', '\\n', '\\t'\n  ],\n\n  // RFC 2396: characters not allowed for various reasons.\n  unwise = [\n    '{', '}', '|', '\\\\', '^', '`'\n  ].concat(delims),\n\n  // Allowed by RFCs, but cause of XSS attacks.  Always escape these.\n  autoEscape = ['\\''].concat(unwise),\n  /*\n   * Characters that are never ever allowed in a hostname.\n   * Note that any invalid chars are also handled, but these\n   * are the ones that are *expected* to be seen, so we fast-path\n   * them.\n   */\n  nonHostChars = [\n    '%', '/', '?', ';', '#'\n  ].concat(autoEscape),\n  hostEndingChars = [\n    '/', '?', '#'\n  ],\n  hostnameMaxLen = 255,\n  hostnamePartPattern = /^[+a-z0-9A-Z_-]{0,63}$/,\n  hostnamePartStart = /^([+a-z0-9A-Z_-]{0,63})(.*)$/,\n  // protocols that can allow \"unsafe\" and \"unwise\" chars.\n  unsafeProtocol = {\n    javascript: true,\n    'javascript:': true\n  },\n  // protocols that never have a hostname.\n  hostlessProtocol = {\n    javascript: true,\n    'javascript:': true\n  },\n  // protocols that always contain a // bit.\n  slashedProtocol = {\n    http: true,\n    https: true,\n    ftp: true,\n    gopher: true,\n    file: true,\n    'http:': true,\n    'https:': true,\n    'ftp:': true,\n    'gopher:': true,\n    'file:': true\n  },\n  querystring = require('qs');\n\nfunction urlParse(url, parseQueryString, slashesDenoteHost) {\n  if (url && typeof url === 'object' && url instanceof Url) { return url; }\n\n  var u = new Url();\n  u.parse(url, parseQueryString, slashesDenoteHost);\n  return u;\n}\n\nUrl.prototype.parse = function (url, parseQueryString, slashesDenoteHost) {\n  if (typeof url !== 'string') {\n    throw new TypeError(\"Parameter 'url' must be a string, not \" + typeof url);\n  }\n\n  /*\n   * Copy chrome, IE, opera backslash-handling behavior.\n   * Back slashes before the query string get converted to forward slashes\n   * See: https://code.google.com/p/chromium/issues/detail?id=25916\n   */\n  var queryIndex = url.indexOf('?'),\n    splitter = queryIndex !== -1 && queryIndex < url.indexOf('#') ? '?' : '#',\n    uSplit = url.split(splitter),\n    slashRegex = /\\\\/g;\n  uSplit[0] = uSplit[0].replace(slashRegex, '/');\n  url = uSplit.join(splitter);\n\n  var rest = url;\n\n  /*\n   * trim before proceeding.\n   * This is to support parse stuff like \"  http://foo.com  \\n\"\n   */\n  rest = rest.trim();\n\n  if (!slashesDenoteHost && url.split('#').length === 1) {\n    // Try fast path regexp\n    var simplePath = simplePathPattern.exec(rest);\n    if (simplePath) {\n      this.path = rest;\n      this.href = rest;\n      this.pathname = simplePath[1];\n      if (simplePath[2]) {\n        this.search = simplePath[2];\n        if (parseQueryString) {\n          this.query = querystring.parse(this.search.substr(1));\n        } else {\n          this.query = this.search.substr(1);\n        }\n      } else if (parseQueryString) {\n        this.search = '';\n        this.query = {};\n      }\n      return this;\n    }\n  }\n\n  var proto = protocolPattern.exec(rest);\n  if (proto) {\n    proto = proto[0];\n    var lowerProto = proto.toLowerCase();\n    this.protocol = lowerProto;\n    rest = rest.substr(proto.length);\n  }\n\n  /*\n   * figure out if it's got a host\n   * user@server is *always* interpreted as a hostname, and url\n   * resolution will treat //foo/bar as host=foo,path=bar because that's\n   * how the browser resolves relative URLs.\n   */\n  if (slashesDenoteHost || proto || rest.match(/^\\/\\/[^@/]+@[^@/]+/)) {\n    var slashes = rest.substr(0, 2) === '//';\n    if (slashes && !(proto && hostlessProtocol[proto])) {\n      rest = rest.substr(2);\n      this.slashes = true;\n    }\n  }\n\n  if (!hostlessProtocol[proto] && (slashes || (proto && !slashedProtocol[proto]))) {\n\n    /*\n     * there's a hostname.\n     * the first instance of /, ?, ;, or # ends the host.\n     *\n     * If there is an @ in the hostname, then non-host chars *are* allowed\n     * to the left of the last @ sign, unless some host-ending character\n     * comes *before* the @-sign.\n     * URLs are obnoxious.\n     *\n     * ex:\n     * http://a@b@c/ => user:a@b host:c\n     * http://a@b?@c => user:a host:c path:/?@c\n     */\n\n    /*\n     * v0.12 TODO(isaacs): This is not quite how Chrome does things.\n     * Review our test case against browsers more comprehensively.\n     */\n\n    // find the first instance of any hostEndingChars\n    var hostEnd = -1;\n    for (var i = 0; i < hostEndingChars.length; i++) {\n      var hec = rest.indexOf(hostEndingChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) { hostEnd = hec; }\n    }\n\n    /*\n     * at this point, either we have an explicit point where the\n     * auth portion cannot go past, or the last @ char is the decider.\n     */\n    var auth, atSign;\n    if (hostEnd === -1) {\n      // atSign can be anywhere.\n      atSign = rest.lastIndexOf('@');\n    } else {\n      /*\n       * atSign must be in auth portion.\n       * http://a@b/c@d => host:b auth:a path:/c@d\n       */\n      atSign = rest.lastIndexOf('@', hostEnd);\n    }\n\n    /*\n     * Now we have a portion which is definitely the auth.\n     * Pull that off.\n     */\n    if (atSign !== -1) {\n      auth = rest.slice(0, atSign);\n      rest = rest.slice(atSign + 1);\n      this.auth = decodeURIComponent(auth);\n    }\n\n    // the host is the remaining to the left of the first non-host char\n    hostEnd = -1;\n    for (var i = 0; i < nonHostChars.length; i++) {\n      var hec = rest.indexOf(nonHostChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) { hostEnd = hec; }\n    }\n    // if we still have not hit it, then the entire thing is a host.\n    if (hostEnd === -1) { hostEnd = rest.length; }\n\n    this.host = rest.slice(0, hostEnd);\n    rest = rest.slice(hostEnd);\n\n    // pull out port.\n    this.parseHost();\n\n    /*\n     * we've indicated that there is a hostname,\n     * so even if it's empty, it has to be present.\n     */\n    this.hostname = this.hostname || '';\n\n    /*\n     * if hostname begins with [ and ends with ]\n     * assume that it's an IPv6 address.\n     */\n    var ipv6Hostname = this.hostname[0] === '[' && this.hostname[this.hostname.length - 1] === ']';\n\n    // validate a little.\n    if (!ipv6Hostname) {\n      var hostparts = this.hostname.split(/\\./);\n      for (var i = 0, l = hostparts.length; i < l; i++) {\n        var part = hostparts[i];\n        if (!part) { continue; }\n        if (!part.match(hostnamePartPattern)) {\n          var newpart = '';\n          for (var j = 0, k = part.length; j < k; j++) {\n            if (part.charCodeAt(j) > 127) {\n              /*\n               * we replace non-ASCII char with a temporary placeholder\n               * we need this to make sure size of hostname is not\n               * broken by replacing non-ASCII by nothing\n               */\n              newpart += 'x';\n            } else {\n              newpart += part[j];\n            }\n          }\n          // we test again with ASCII char only\n          if (!newpart.match(hostnamePartPattern)) {\n            var validParts = hostparts.slice(0, i);\n            var notHost = hostparts.slice(i + 1);\n            var bit = part.match(hostnamePartStart);\n            if (bit) {\n              validParts.push(bit[1]);\n              notHost.unshift(bit[2]);\n            }\n            if (notHost.length) {\n              rest = '/' + notHost.join('.') + rest;\n            }\n            this.hostname = validParts.join('.');\n            break;\n          }\n        }\n      }\n    }\n\n    if (this.hostname.length > hostnameMaxLen) {\n      this.hostname = '';\n    } else {\n      // hostnames are always lower case.\n      this.hostname = this.hostname.toLowerCase();\n    }\n\n    if (!ipv6Hostname) {\n      /*\n       * IDNA Support: Returns a punycoded representation of \"domain\".\n       * It only converts parts of the domain name that\n       * have non-ASCII characters, i.e. it doesn't matter if\n       * you call it with a domain that already is ASCII-only.\n       */\n      this.hostname = punycode.toASCII(this.hostname);\n    }\n\n    var p = this.port ? ':' + this.port : '';\n    var h = this.hostname || '';\n    this.host = h + p;\n    this.href += this.host;\n\n    /*\n     * strip [ and ] from the hostname\n     * the host field still retains them, though\n     */\n    if (ipv6Hostname) {\n      this.hostname = this.hostname.substr(1, this.hostname.length - 2);\n      if (rest[0] !== '/') {\n        rest = '/' + rest;\n      }\n    }\n  }\n\n  /*\n   * now rest is set to the post-host stuff.\n   * chop off any delim chars.\n   */\n  if (!unsafeProtocol[lowerProto]) {\n\n    /*\n     * First, make 100% sure that any \"autoEscape\" chars get\n     * escaped, even if encodeURIComponent doesn't think they\n     * need to be.\n     */\n    for (var i = 0, l = autoEscape.length; i < l; i++) {\n      var ae = autoEscape[i];\n      if (rest.indexOf(ae) === -1) { continue; }\n      var esc = encodeURIComponent(ae);\n      if (esc === ae) {\n        esc = escape(ae);\n      }\n      rest = rest.split(ae).join(esc);\n    }\n  }\n\n  // chop off from the tail first.\n  var hash = rest.indexOf('#');\n  if (hash !== -1) {\n    // got a fragment string.\n    this.hash = rest.substr(hash);\n    rest = rest.slice(0, hash);\n  }\n  var qm = rest.indexOf('?');\n  if (qm !== -1) {\n    this.search = rest.substr(qm);\n    this.query = rest.substr(qm + 1);\n    if (parseQueryString) {\n      this.query = querystring.parse(this.query);\n    }\n    rest = rest.slice(0, qm);\n  } else if (parseQueryString) {\n    // no query string, but parseQueryString still requested\n    this.search = '';\n    this.query = {};\n  }\n  if (rest) { this.pathname = rest; }\n  if (slashedProtocol[lowerProto] && this.hostname && !this.pathname) {\n    this.pathname = '/';\n  }\n\n  // to support http.request\n  if (this.pathname || this.search) {\n    var p = this.pathname || '';\n    var s = this.search || '';\n    this.path = p + s;\n  }\n\n  // finally, reconstruct the href based on what has been validated.\n  this.href = this.format();\n  return this;\n};\n\n// format a parsed object into a url string\nfunction urlFormat(obj) {\n  /*\n   * ensure it's an object, and not a string url.\n   * If it's an obj, this is a no-op.\n   * this way, you can call url_format() on strings\n   * to clean up potentially wonky urls.\n   */\n  if (typeof obj === 'string') { obj = urlParse(obj); }\n  if (!(obj instanceof Url)) { return Url.prototype.format.call(obj); }\n  return obj.format();\n}\n\nUrl.prototype.format = function () {\n  var auth = this.auth || '';\n  if (auth) {\n    auth = encodeURIComponent(auth);\n    auth = auth.replace(/%3A/i, ':');\n    auth += '@';\n  }\n\n  var protocol = this.protocol || '',\n    pathname = this.pathname || '',\n    hash = this.hash || '',\n    host = false,\n    query = '';\n\n  if (this.host) {\n    host = auth + this.host;\n  } else if (this.hostname) {\n    host = auth + (this.hostname.indexOf(':') === -1 ? this.hostname : '[' + this.hostname + ']');\n    if (this.port) {\n      host += ':' + this.port;\n    }\n  }\n\n  if (this.query && typeof this.query === 'object' && Object.keys(this.query).length) {\n    query = querystring.stringify(this.query, {\n      arrayFormat: 'repeat',\n      addQueryPrefix: false\n    });\n  }\n\n  var search = this.search || (query && ('?' + query)) || '';\n\n  if (protocol && protocol.substr(-1) !== ':') { protocol += ':'; }\n\n  /*\n   * only the slashedProtocols get the //.  Not mailto:, xmpp:, etc.\n   * unless they had them to begin with.\n   */\n  if (this.slashes || (!protocol || slashedProtocol[protocol]) && host !== false) {\n    host = '//' + (host || '');\n    if (pathname && pathname.charAt(0) !== '/') { pathname = '/' + pathname; }\n  } else if (!host) {\n    host = '';\n  }\n\n  if (hash && hash.charAt(0) !== '#') { hash = '#' + hash; }\n  if (search && search.charAt(0) !== '?') { search = '?' + search; }\n\n  pathname = pathname.replace(/[?#]/g, function (match) {\n    return encodeURIComponent(match);\n  });\n  search = search.replace('#', '%23');\n\n  return protocol + host + pathname + search + hash;\n};\n\nfunction urlResolve(source, relative) {\n  return urlParse(source, false, true).resolve(relative);\n}\n\nUrl.prototype.resolve = function (relative) {\n  return this.resolveObject(urlParse(relative, false, true)).format();\n};\n\nfunction urlResolveObject(source, relative) {\n  if (!source) { return relative; }\n  return urlParse(source, false, true).resolveObject(relative);\n}\n\nUrl.prototype.resolveObject = function (relative) {\n  if (typeof relative === 'string') {\n    var rel = new Url();\n    rel.parse(relative, false, true);\n    relative = rel;\n  }\n\n  var result = new Url();\n  var tkeys = Object.keys(this);\n  for (var tk = 0; tk < tkeys.length; tk++) {\n    var tkey = tkeys[tk];\n    result[tkey] = this[tkey];\n  }\n\n  /*\n   * hash is always overridden, no matter what.\n   * even href=\"\" will remove it.\n   */\n  result.hash = relative.hash;\n\n  // if the relative url is empty, then there's nothing left to do here.\n  if (relative.href === '') {\n    result.href = result.format();\n    return result;\n  }\n\n  // hrefs like //foo/bar always cut to the protocol.\n  if (relative.slashes && !relative.protocol) {\n    // take everything except the protocol from relative\n    var rkeys = Object.keys(relative);\n    for (var rk = 0; rk < rkeys.length; rk++) {\n      var rkey = rkeys[rk];\n      if (rkey !== 'protocol') { result[rkey] = relative[rkey]; }\n    }\n\n    // urlParse appends trailing / to urls like http://www.example.com\n    if (slashedProtocol[result.protocol] && result.hostname && !result.pathname) {\n      result.pathname = '/';\n      result.path = result.pathname;\n    }\n\n    result.href = result.format();\n    return result;\n  }\n\n  if (relative.protocol && relative.protocol !== result.protocol) {\n    /*\n     * if it's a known url protocol, then changing\n     * the protocol does weird things\n     * first, if it's not file:, then we MUST have a host,\n     * and if there was a path\n     * to begin with, then we MUST have a path.\n     * if it is file:, then the host is dropped,\n     * because that's known to be hostless.\n     * anything else is assumed to be absolute.\n     */\n    if (!slashedProtocol[relative.protocol]) {\n      var keys = Object.keys(relative);\n      for (var v = 0; v < keys.length; v++) {\n        var k = keys[v];\n        result[k] = relative[k];\n      }\n      result.href = result.format();\n      return result;\n    }\n\n    result.protocol = relative.protocol;\n    if (!relative.host && !hostlessProtocol[relative.protocol]) {\n      var relPath = (relative.pathname || '').split('/');\n      while (relPath.length && !(relative.host = relPath.shift())) { }\n      if (!relative.host) { relative.host = ''; }\n      if (!relative.hostname) { relative.hostname = ''; }\n      if (relPath[0] !== '') { relPath.unshift(''); }\n      if (relPath.length < 2) { relPath.unshift(''); }\n      result.pathname = relPath.join('/');\n    } else {\n      result.pathname = relative.pathname;\n    }\n    result.search = relative.search;\n    result.query = relative.query;\n    result.host = relative.host || '';\n    result.auth = relative.auth;\n    result.hostname = relative.hostname || relative.host;\n    result.port = relative.port;\n    // to support http.request\n    if (result.pathname || result.search) {\n      var p = result.pathname || '';\n      var s = result.search || '';\n      result.path = p + s;\n    }\n    result.slashes = result.slashes || relative.slashes;\n    result.href = result.format();\n    return result;\n  }\n\n  var isSourceAbs = result.pathname && result.pathname.charAt(0) === '/',\n    isRelAbs = relative.host || relative.pathname && relative.pathname.charAt(0) === '/',\n    mustEndAbs = isRelAbs || isSourceAbs || (result.host && relative.pathname),\n    removeAllDots = mustEndAbs,\n    srcPath = result.pathname && result.pathname.split('/') || [],\n    relPath = relative.pathname && relative.pathname.split('/') || [],\n    psychotic = result.protocol && !slashedProtocol[result.protocol];\n\n  /*\n   * if the url is a non-slashed url, then relative\n   * links like ../.. should be able\n   * to crawl up to the hostname, as well.  This is strange.\n   * result.protocol has already been set by now.\n   * Later on, put the first path part into the host field.\n   */\n  if (psychotic) {\n    result.hostname = '';\n    result.port = null;\n    if (result.host) {\n      if (srcPath[0] === '') { srcPath[0] = result.host; } else { srcPath.unshift(result.host); }\n    }\n    result.host = '';\n    if (relative.protocol) {\n      relative.hostname = null;\n      relative.port = null;\n      if (relative.host) {\n        if (relPath[0] === '') { relPath[0] = relative.host; } else { relPath.unshift(relative.host); }\n      }\n      relative.host = null;\n    }\n    mustEndAbs = mustEndAbs && (relPath[0] === '' || srcPath[0] === '');\n  }\n\n  if (isRelAbs) {\n    // it's absolute.\n    result.host = relative.host || relative.host === '' ? relative.host : result.host;\n    result.hostname = relative.hostname || relative.hostname === '' ? relative.hostname : result.hostname;\n    result.search = relative.search;\n    result.query = relative.query;\n    srcPath = relPath;\n    // fall through to the dot-handling below.\n  } else if (relPath.length) {\n    /*\n     * it's relative\n     * throw away the existing file, and take the new path instead.\n     */\n    if (!srcPath) { srcPath = []; }\n    srcPath.pop();\n    srcPath = srcPath.concat(relPath);\n    result.search = relative.search;\n    result.query = relative.query;\n  } else if (relative.search != null) {\n    /*\n     * just pull out the search.\n     * like href='?foo'.\n     * Put this after the other two cases because it simplifies the booleans\n     */\n    if (psychotic) {\n      result.host = srcPath.shift();\n      result.hostname = result.host;\n      /*\n       * occationaly the auth can get stuck only in host\n       * this especially happens in cases like\n       * url.resolveObject('mailto:local1@domain1', 'local2@domain2')\n       */\n      var authInHost = result.host && result.host.indexOf('@') > 0 ? result.host.split('@') : false;\n      if (authInHost) {\n        result.auth = authInHost.shift();\n        result.hostname = authInHost.shift();\n        result.host = result.hostname;\n      }\n    }\n    result.search = relative.search;\n    result.query = relative.query;\n    // to support http.request\n    if (result.pathname !== null || result.search !== null) {\n      result.path = (result.pathname ? result.pathname : '') + (result.search ? result.search : '');\n    }\n    result.href = result.format();\n    return result;\n  }\n\n  if (!srcPath.length) {\n    /*\n     * no path at all.  easy.\n     * we've already handled the other stuff above.\n     */\n    result.pathname = null;\n    // to support http.request\n    if (result.search) {\n      result.path = '/' + result.search;\n    } else {\n      result.path = null;\n    }\n    result.href = result.format();\n    return result;\n  }\n\n  /*\n   * if a url ENDs in . or .., then it must get a trailing slash.\n   * however, if it ends in anything else non-slashy,\n   * then it must NOT get a trailing slash.\n   */\n  var last = srcPath.slice(-1)[0];\n  var hasTrailingSlash = (result.host || relative.host || srcPath.length > 1) && (last === '.' || last === '..') || last === '';\n\n  /*\n   * strip single dots, resolve double dots to parent dir\n   * if the path tries to go above the root, `up` ends up > 0\n   */\n  var up = 0;\n  for (var i = srcPath.length; i >= 0; i--) {\n    last = srcPath[i];\n    if (last === '.') {\n      srcPath.splice(i, 1);\n    } else if (last === '..') {\n      srcPath.splice(i, 1);\n      up++;\n    } else if (up) {\n      srcPath.splice(i, 1);\n      up--;\n    }\n  }\n\n  // if the path is allowed to go above the root, restore leading ..s\n  if (!mustEndAbs && !removeAllDots) {\n    for (; up--; up) {\n      srcPath.unshift('..');\n    }\n  }\n\n  if (mustEndAbs && srcPath[0] !== '' && (!srcPath[0] || srcPath[0].charAt(0) !== '/')) {\n    srcPath.unshift('');\n  }\n\n  if (hasTrailingSlash && (srcPath.join('/').substr(-1) !== '/')) {\n    srcPath.push('');\n  }\n\n  var isAbsolute = srcPath[0] === '' || (srcPath[0] && srcPath[0].charAt(0) === '/');\n\n  // put the host back\n  if (psychotic) {\n    result.hostname = isAbsolute ? '' : srcPath.length ? srcPath.shift() : '';\n    result.host = result.hostname;\n    /*\n     * occationaly the auth can get stuck only in host\n     * this especially happens in cases like\n     * url.resolveObject('mailto:local1@domain1', 'local2@domain2')\n     */\n    var authInHost = result.host && result.host.indexOf('@') > 0 ? result.host.split('@') : false;\n    if (authInHost) {\n      result.auth = authInHost.shift();\n      result.hostname = authInHost.shift();\n      result.host = result.hostname;\n    }\n  }\n\n  mustEndAbs = mustEndAbs || (result.host && srcPath.length);\n\n  if (mustEndAbs && !isAbsolute) {\n    srcPath.unshift('');\n  }\n\n  if (srcPath.length > 0) {\n    result.pathname = srcPath.join('/');\n  } else {\n    result.pathname = null;\n    result.path = null;\n  }\n\n  // to support request.http\n  if (result.pathname !== null || result.search !== null) {\n    result.path = (result.pathname ? result.pathname : '') + (result.search ? result.search : '');\n  }\n  result.auth = relative.auth || result.auth;\n  result.slashes = result.slashes || relative.slashes;\n  result.href = result.format();\n  return result;\n};\n\nUrl.prototype.parseHost = function () {\n  var host = this.host;\n  var port = portPattern.exec(host);\n  if (port) {\n    port = port[0];\n    if (port !== ':') {\n      this.port = port.substr(1);\n    }\n    host = host.substr(0, host.length - port.length);\n  }\n  if (host) { this.hostname = host; }\n};\n\nexports.parse = urlParse;\nexports.resolve = urlResolve;\nexports.resolveObject = urlResolveObject;\nexports.format = urlFormat;\n\nexports.Url = Url;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,WAAW,CAAC;AAEnC,SAASC,GAAGA,CAAA,EAAG;EACb,IAAI,CAACC,QAAQ,GAAG,IAAI;EACpB,IAAI,CAACC,OAAO,GAAG,IAAI;EACnB,IAAI,CAACC,IAAI,GAAG,IAAI;EAChB,IAAI,CAACC,IAAI,GAAG,IAAI;EAChB,IAAI,CAACC,IAAI,GAAG,IAAI;EAChB,IAAI,CAACC,QAAQ,GAAG,IAAI;EACpB,IAAI,CAACC,IAAI,GAAG,IAAI;EAChB,IAAI,CAACC,MAAM,GAAG,IAAI;EAClB,IAAI,CAACC,KAAK,GAAG,IAAI;EACjB,IAAI,CAACC,QAAQ,GAAG,IAAI;EACpB,IAAI,CAACC,IAAI,GAAG,IAAI;EAChB,IAAI,CAACC,IAAI,GAAG,IAAI;AAClB;;AAEA;;AAEA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,mBAAmB;EACvCC,WAAW,GAAG,UAAU;EAExB;EACAC,iBAAiB,GAAG,mCAAmC;EAEvD;AACF;AACA;AACA;EACEC,MAAM,GAAG,CACP,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAC1C;EAED;EACAC,MAAM,GAAG,CACP,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAC9B,CAACC,MAAM,CAACF,MAAM,CAAC;EAEhB;EACAG,UAAU,GAAG,CAAC,IAAI,CAAC,CAACD,MAAM,CAACD,MAAM,CAAC;EAClC;AACF;AACA;AACA;AACA;AACA;EACEG,YAAY,GAAG,CACb,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CACxB,CAACF,MAAM,CAACC,UAAU,CAAC;EACpBE,eAAe,GAAG,CAChB,GAAG,EAAE,GAAG,EAAE,GAAG,CACd;EACDC,cAAc,GAAG,GAAG;EACpBC,mBAAmB,GAAG,wBAAwB;EAC9CC,iBAAiB,GAAG,8BAA8B;EAClD;EACAC,cAAc,GAAG;IACfC,UAAU,EAAE,IAAI;IAChB,aAAa,EAAE;EACjB,CAAC;EACD;EACAC,gBAAgB,GAAG;IACjBD,UAAU,EAAE,IAAI;IAChB,aAAa,EAAE;EACjB,CAAC;EACD;EACAE,eAAe,GAAG;IAChBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,IAAI;IACV,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,IAAI;IACd,MAAM,EAAE,IAAI;IACZ,SAAS,EAAE,IAAI;IACf,OAAO,EAAE;EACX,CAAC;EACDC,WAAW,GAAGnC,OAAO,CAAC,IAAI,CAAC;AAE7B,SAASoC,QAAQA,CAACC,GAAG,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAE;EAC1D,IAAIF,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,YAAYpC,GAAG,EAAE;IAAE,OAAOoC,GAAG;EAAE;EAExE,IAAIG,CAAC,GAAG,IAAIvC,GAAG,CAAC,CAAC;EACjBuC,CAAC,CAACC,KAAK,CAACJ,GAAG,EAAEC,gBAAgB,EAAEC,iBAAiB,CAAC;EACjD,OAAOC,CAAC;AACV;AAEAvC,GAAG,CAACyC,SAAS,CAACD,KAAK,GAAG,UAAUJ,GAAG,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAE;EACxE,IAAI,OAAOF,GAAG,KAAK,QAAQ,EAAE;IAC3B,MAAM,IAAIM,SAAS,CAAC,wCAAwC,GAAG,OAAON,GAAG,CAAC;EAC5E;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIO,UAAU,GAAGP,GAAG,CAACQ,OAAO,CAAC,GAAG,CAAC;IAC/BC,QAAQ,GAAGF,UAAU,KAAK,CAAC,CAAC,IAAIA,UAAU,GAAGP,GAAG,CAACQ,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;IACzEE,MAAM,GAAGV,GAAG,CAACW,KAAK,CAACF,QAAQ,CAAC;IAC5BG,UAAU,GAAG,KAAK;EACpBF,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAACG,OAAO,CAACD,UAAU,EAAE,GAAG,CAAC;EAC9CZ,GAAG,GAAGU,MAAM,CAACI,IAAI,CAACL,QAAQ,CAAC;EAE3B,IAAIM,IAAI,GAAGf,GAAG;;EAEd;AACF;AACA;AACA;EACEe,IAAI,GAAGA,IAAI,CAACC,IAAI,CAAC,CAAC;EAElB,IAAI,CAACd,iBAAiB,IAAIF,GAAG,CAACW,KAAK,CAAC,GAAG,CAAC,CAACM,MAAM,KAAK,CAAC,EAAE;IACrD;IACA,IAAIC,UAAU,GAAGvC,iBAAiB,CAACwC,IAAI,CAACJ,IAAI,CAAC;IAC7C,IAAIG,UAAU,EAAE;MACd,IAAI,CAAC3C,IAAI,GAAGwC,IAAI;MAChB,IAAI,CAACvC,IAAI,GAAGuC,IAAI;MAChB,IAAI,CAACzC,QAAQ,GAAG4C,UAAU,CAAC,CAAC,CAAC;MAC7B,IAAIA,UAAU,CAAC,CAAC,CAAC,EAAE;QACjB,IAAI,CAAC9C,MAAM,GAAG8C,UAAU,CAAC,CAAC,CAAC;QAC3B,IAAIjB,gBAAgB,EAAE;UACpB,IAAI,CAAC5B,KAAK,GAAGyB,WAAW,CAACM,KAAK,CAAC,IAAI,CAAChC,MAAM,CAACgD,MAAM,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,MAAM;UACL,IAAI,CAAC/C,KAAK,GAAG,IAAI,CAACD,MAAM,CAACgD,MAAM,CAAC,CAAC,CAAC;QACpC;MACF,CAAC,MAAM,IAAInB,gBAAgB,EAAE;QAC3B,IAAI,CAAC7B,MAAM,GAAG,EAAE;QAChB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;MACjB;MACA,OAAO,IAAI;IACb;EACF;EAEA,IAAIgD,KAAK,GAAG5C,eAAe,CAAC0C,IAAI,CAACJ,IAAI,CAAC;EACtC,IAAIM,KAAK,EAAE;IACTA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;IAChB,IAAIC,UAAU,GAAGD,KAAK,CAACE,WAAW,CAAC,CAAC;IACpC,IAAI,CAAC1D,QAAQ,GAAGyD,UAAU;IAC1BP,IAAI,GAAGA,IAAI,CAACK,MAAM,CAACC,KAAK,CAACJ,MAAM,CAAC;EAClC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIf,iBAAiB,IAAImB,KAAK,IAAIN,IAAI,CAACS,KAAK,CAAC,oBAAoB,CAAC,EAAE;IAClE,IAAI1D,OAAO,GAAGiD,IAAI,CAACK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI;IACxC,IAAItD,OAAO,IAAI,EAAEuD,KAAK,IAAI9B,gBAAgB,CAAC8B,KAAK,CAAC,CAAC,EAAE;MAClDN,IAAI,GAAGA,IAAI,CAACK,MAAM,CAAC,CAAC,CAAC;MACrB,IAAI,CAACtD,OAAO,GAAG,IAAI;IACrB;EACF;EAEA,IAAI,CAACyB,gBAAgB,CAAC8B,KAAK,CAAC,KAAKvD,OAAO,IAAKuD,KAAK,IAAI,CAAC7B,eAAe,CAAC6B,KAAK,CAAE,CAAC,EAAE;IAE/E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEI;AACJ;AACA;AACA;;IAEI;IACA,IAAII,OAAO,GAAG,CAAC,CAAC;IAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,eAAe,CAACgC,MAAM,EAAES,CAAC,EAAE,EAAE;MAC/C,IAAIC,GAAG,GAAGZ,IAAI,CAACP,OAAO,CAACvB,eAAe,CAACyC,CAAC,CAAC,CAAC;MAC1C,IAAIC,GAAG,KAAK,CAAC,CAAC,KAAKF,OAAO,KAAK,CAAC,CAAC,IAAIE,GAAG,GAAGF,OAAO,CAAC,EAAE;QAAEA,OAAO,GAAGE,GAAG;MAAE;IACxE;;IAEA;AACJ;AACA;AACA;IACI,IAAI5D,IAAI,EAAE6D,MAAM;IAChB,IAAIH,OAAO,KAAK,CAAC,CAAC,EAAE;MAClB;MACAG,MAAM,GAAGb,IAAI,CAACc,WAAW,CAAC,GAAG,CAAC;IAChC,CAAC,MAAM;MACL;AACN;AACA;AACA;MACMD,MAAM,GAAGb,IAAI,CAACc,WAAW,CAAC,GAAG,EAAEJ,OAAO,CAAC;IACzC;;IAEA;AACJ;AACA;AACA;IACI,IAAIG,MAAM,KAAK,CAAC,CAAC,EAAE;MACjB7D,IAAI,GAAGgD,IAAI,CAACe,KAAK,CAAC,CAAC,EAAEF,MAAM,CAAC;MAC5Bb,IAAI,GAAGA,IAAI,CAACe,KAAK,CAACF,MAAM,GAAG,CAAC,CAAC;MAC7B,IAAI,CAAC7D,IAAI,GAAGgE,kBAAkB,CAAChE,IAAI,CAAC;IACtC;;IAEA;IACA0D,OAAO,GAAG,CAAC,CAAC;IACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1C,YAAY,CAACiC,MAAM,EAAES,CAAC,EAAE,EAAE;MAC5C,IAAIC,GAAG,GAAGZ,IAAI,CAACP,OAAO,CAACxB,YAAY,CAAC0C,CAAC,CAAC,CAAC;MACvC,IAAIC,GAAG,KAAK,CAAC,CAAC,KAAKF,OAAO,KAAK,CAAC,CAAC,IAAIE,GAAG,GAAGF,OAAO,CAAC,EAAE;QAAEA,OAAO,GAAGE,GAAG;MAAE;IACxE;IACA;IACA,IAAIF,OAAO,KAAK,CAAC,CAAC,EAAE;MAAEA,OAAO,GAAGV,IAAI,CAACE,MAAM;IAAE;IAE7C,IAAI,CAACjD,IAAI,GAAG+C,IAAI,CAACe,KAAK,CAAC,CAAC,EAAEL,OAAO,CAAC;IAClCV,IAAI,GAAGA,IAAI,CAACe,KAAK,CAACL,OAAO,CAAC;;IAE1B;IACA,IAAI,CAACO,SAAS,CAAC,CAAC;;IAEhB;AACJ;AACA;AACA;IACI,IAAI,CAAC9D,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI,EAAE;;IAEnC;AACJ;AACA;AACA;IACI,IAAI+D,YAAY,GAAG,IAAI,CAAC/D,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAACA,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAAC+C,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG;;IAE9F;IACA,IAAI,CAACgB,YAAY,EAAE;MACjB,IAAIC,SAAS,GAAG,IAAI,CAAChE,QAAQ,CAACyC,KAAK,CAAC,IAAI,CAAC;MACzC,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAES,CAAC,GAAGD,SAAS,CAACjB,MAAM,EAAES,CAAC,GAAGS,CAAC,EAAET,CAAC,EAAE,EAAE;QAChD,IAAIU,IAAI,GAAGF,SAAS,CAACR,CAAC,CAAC;QACvB,IAAI,CAACU,IAAI,EAAE;UAAE;QAAU;QACvB,IAAI,CAACA,IAAI,CAACZ,KAAK,CAACrC,mBAAmB,CAAC,EAAE;UACpC,IAAIkD,OAAO,GAAG,EAAE;UAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,IAAI,CAACnB,MAAM,EAAEqB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;YAC3C,IAAIF,IAAI,CAACI,UAAU,CAACF,CAAC,CAAC,GAAG,GAAG,EAAE;cAC5B;AACd;AACA;AACA;AACA;cACcD,OAAO,IAAI,GAAG;YAChB,CAAC,MAAM;cACLA,OAAO,IAAID,IAAI,CAACE,CAAC,CAAC;YACpB;UACF;UACA;UACA,IAAI,CAACD,OAAO,CAACb,KAAK,CAACrC,mBAAmB,CAAC,EAAE;YACvC,IAAIsD,UAAU,GAAGP,SAAS,CAACJ,KAAK,CAAC,CAAC,EAAEJ,CAAC,CAAC;YACtC,IAAIgB,OAAO,GAAGR,SAAS,CAACJ,KAAK,CAACJ,CAAC,GAAG,CAAC,CAAC;YACpC,IAAIiB,GAAG,GAAGP,IAAI,CAACZ,KAAK,CAACpC,iBAAiB,CAAC;YACvC,IAAIuD,GAAG,EAAE;cACPF,UAAU,CAACG,IAAI,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC;cACvBD,OAAO,CAACG,OAAO,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;YACzB;YACA,IAAID,OAAO,CAACzB,MAAM,EAAE;cAClBF,IAAI,GAAG,GAAG,GAAG2B,OAAO,CAAC5B,IAAI,CAAC,GAAG,CAAC,GAAGC,IAAI;YACvC;YACA,IAAI,CAAC7C,QAAQ,GAAGuE,UAAU,CAAC3B,IAAI,CAAC,GAAG,CAAC;YACpC;UACF;QACF;MACF;IACF;IAEA,IAAI,IAAI,CAAC5C,QAAQ,CAAC+C,MAAM,GAAG/B,cAAc,EAAE;MACzC,IAAI,CAAChB,QAAQ,GAAG,EAAE;IACpB,CAAC,MAAM;MACL;MACA,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACqD,WAAW,CAAC,CAAC;IAC7C;IAEA,IAAI,CAACU,YAAY,EAAE;MACjB;AACN;AACA;AACA;AACA;AACA;MACM,IAAI,CAAC/D,QAAQ,GAAGR,QAAQ,CAACoF,OAAO,CAAC,IAAI,CAAC5E,QAAQ,CAAC;IACjD;IAEA,IAAI6E,CAAC,GAAG,IAAI,CAAC9E,IAAI,GAAG,GAAG,GAAG,IAAI,CAACA,IAAI,GAAG,EAAE;IACxC,IAAI+E,CAAC,GAAG,IAAI,CAAC9E,QAAQ,IAAI,EAAE;IAC3B,IAAI,CAACF,IAAI,GAAGgF,CAAC,GAAGD,CAAC;IACjB,IAAI,CAACvE,IAAI,IAAI,IAAI,CAACR,IAAI;;IAEtB;AACJ;AACA;AACA;IACI,IAAIiE,YAAY,EAAE;MAChB,IAAI,CAAC/D,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACkD,MAAM,CAAC,CAAC,EAAE,IAAI,CAAClD,QAAQ,CAAC+C,MAAM,GAAG,CAAC,CAAC;MACjE,IAAIF,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACnBA,IAAI,GAAG,GAAG,GAAGA,IAAI;MACnB;IACF;EACF;;EAEA;AACF;AACA;AACA;EACE,IAAI,CAAC1B,cAAc,CAACiC,UAAU,CAAC,EAAE;IAE/B;AACJ;AACA;AACA;AACA;IACI,KAAK,IAAII,CAAC,GAAG,CAAC,EAAES,CAAC,GAAGpD,UAAU,CAACkC,MAAM,EAAES,CAAC,GAAGS,CAAC,EAAET,CAAC,EAAE,EAAE;MACjD,IAAIuB,EAAE,GAAGlE,UAAU,CAAC2C,CAAC,CAAC;MACtB,IAAIX,IAAI,CAACP,OAAO,CAACyC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAAE;MAAU;MACzC,IAAIC,GAAG,GAAGC,kBAAkB,CAACF,EAAE,CAAC;MAChC,IAAIC,GAAG,KAAKD,EAAE,EAAE;QACdC,GAAG,GAAGE,MAAM,CAACH,EAAE,CAAC;MAClB;MACAlC,IAAI,GAAGA,IAAI,CAACJ,KAAK,CAACsC,EAAE,CAAC,CAACnC,IAAI,CAACoC,GAAG,CAAC;IACjC;EACF;;EAEA;EACA,IAAI/E,IAAI,GAAG4C,IAAI,CAACP,OAAO,CAAC,GAAG,CAAC;EAC5B,IAAIrC,IAAI,KAAK,CAAC,CAAC,EAAE;IACf;IACA,IAAI,CAACA,IAAI,GAAG4C,IAAI,CAACK,MAAM,CAACjD,IAAI,CAAC;IAC7B4C,IAAI,GAAGA,IAAI,CAACe,KAAK,CAAC,CAAC,EAAE3D,IAAI,CAAC;EAC5B;EACA,IAAIkF,EAAE,GAAGtC,IAAI,CAACP,OAAO,CAAC,GAAG,CAAC;EAC1B,IAAI6C,EAAE,KAAK,CAAC,CAAC,EAAE;IACb,IAAI,CAACjF,MAAM,GAAG2C,IAAI,CAACK,MAAM,CAACiC,EAAE,CAAC;IAC7B,IAAI,CAAChF,KAAK,GAAG0C,IAAI,CAACK,MAAM,CAACiC,EAAE,GAAG,CAAC,CAAC;IAChC,IAAIpD,gBAAgB,EAAE;MACpB,IAAI,CAAC5B,KAAK,GAAGyB,WAAW,CAACM,KAAK,CAAC,IAAI,CAAC/B,KAAK,CAAC;IAC5C;IACA0C,IAAI,GAAGA,IAAI,CAACe,KAAK,CAAC,CAAC,EAAEuB,EAAE,CAAC;EAC1B,CAAC,MAAM,IAAIpD,gBAAgB,EAAE;IAC3B;IACA,IAAI,CAAC7B,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACjB;EACA,IAAI0C,IAAI,EAAE;IAAE,IAAI,CAACzC,QAAQ,GAAGyC,IAAI;EAAE;EAClC,IAAIvB,eAAe,CAAC8B,UAAU,CAAC,IAAI,IAAI,CAACpD,QAAQ,IAAI,CAAC,IAAI,CAACI,QAAQ,EAAE;IAClE,IAAI,CAACA,QAAQ,GAAG,GAAG;EACrB;;EAEA;EACA,IAAI,IAAI,CAACA,QAAQ,IAAI,IAAI,CAACF,MAAM,EAAE;IAChC,IAAI2E,CAAC,GAAG,IAAI,CAACzE,QAAQ,IAAI,EAAE;IAC3B,IAAIgF,CAAC,GAAG,IAAI,CAAClF,MAAM,IAAI,EAAE;IACzB,IAAI,CAACG,IAAI,GAAGwE,CAAC,GAAGO,CAAC;EACnB;;EAEA;EACA,IAAI,CAAC9E,IAAI,GAAG,IAAI,CAAC+E,MAAM,CAAC,CAAC;EACzB,OAAO,IAAI;AACb,CAAC;;AAED;AACA,SAASC,SAASA,CAACC,GAAG,EAAE;EACtB;AACF;AACA;AACA;AACA;AACA;EACE,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAAEA,GAAG,GAAG1D,QAAQ,CAAC0D,GAAG,CAAC;EAAE;EACpD,IAAI,EAAEA,GAAG,YAAY7F,GAAG,CAAC,EAAE;IAAE,OAAOA,GAAG,CAACyC,SAAS,CAACkD,MAAM,CAACG,IAAI,CAACD,GAAG,CAAC;EAAE;EACpE,OAAOA,GAAG,CAACF,MAAM,CAAC,CAAC;AACrB;AAEA3F,GAAG,CAACyC,SAAS,CAACkD,MAAM,GAAG,YAAY;EACjC,IAAIxF,IAAI,GAAG,IAAI,CAACA,IAAI,IAAI,EAAE;EAC1B,IAAIA,IAAI,EAAE;IACRA,IAAI,GAAGoF,kBAAkB,CAACpF,IAAI,CAAC;IAC/BA,IAAI,GAAGA,IAAI,CAAC8C,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;IAChC9C,IAAI,IAAI,GAAG;EACb;EAEA,IAAIF,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI,EAAE;IAChCS,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI,EAAE;IAC9BH,IAAI,GAAG,IAAI,CAACA,IAAI,IAAI,EAAE;IACtBH,IAAI,GAAG,KAAK;IACZK,KAAK,GAAG,EAAE;EAEZ,IAAI,IAAI,CAACL,IAAI,EAAE;IACbA,IAAI,GAAGD,IAAI,GAAG,IAAI,CAACC,IAAI;EACzB,CAAC,MAAM,IAAI,IAAI,CAACE,QAAQ,EAAE;IACxBF,IAAI,GAAGD,IAAI,IAAI,IAAI,CAACG,QAAQ,CAACsC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAACtC,QAAQ,GAAG,GAAG,GAAG,IAAI,CAACA,QAAQ,GAAG,GAAG,CAAC;IAC7F,IAAI,IAAI,CAACD,IAAI,EAAE;MACbD,IAAI,IAAI,GAAG,GAAG,IAAI,CAACC,IAAI;IACzB;EACF;EAEA,IAAI,IAAI,CAACI,KAAK,IAAI,OAAO,IAAI,CAACA,KAAK,KAAK,QAAQ,IAAIsF,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvF,KAAK,CAAC,CAAC4C,MAAM,EAAE;IAClF5C,KAAK,GAAGyB,WAAW,CAAC+D,SAAS,CAAC,IAAI,CAACxF,KAAK,EAAE;MACxCyF,WAAW,EAAE,QAAQ;MACrBC,cAAc,EAAE;IAClB,CAAC,CAAC;EACJ;EAEA,IAAI3F,MAAM,GAAG,IAAI,CAACA,MAAM,IAAKC,KAAK,IAAK,GAAG,GAAGA,KAAO,IAAI,EAAE;EAE1D,IAAIR,QAAQ,IAAIA,QAAQ,CAACuD,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAAEvD,QAAQ,IAAI,GAAG;EAAE;;EAEhE;AACF;AACA;AACA;EACE,IAAI,IAAI,CAACC,OAAO,IAAI,CAAC,CAACD,QAAQ,IAAI2B,eAAe,CAAC3B,QAAQ,CAAC,KAAKG,IAAI,KAAK,KAAK,EAAE;IAC9EA,IAAI,GAAG,IAAI,IAAIA,IAAI,IAAI,EAAE,CAAC;IAC1B,IAAIM,QAAQ,IAAIA,QAAQ,CAAC0F,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAAE1F,QAAQ,GAAG,GAAG,GAAGA,QAAQ;IAAE;EAC3E,CAAC,MAAM,IAAI,CAACN,IAAI,EAAE;IAChBA,IAAI,GAAG,EAAE;EACX;EAEA,IAAIG,IAAI,IAAIA,IAAI,CAAC6F,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAAE7F,IAAI,GAAG,GAAG,GAAGA,IAAI;EAAE;EACzD,IAAIC,MAAM,IAAIA,MAAM,CAAC4F,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAAE5F,MAAM,GAAG,GAAG,GAAGA,MAAM;EAAE;EAEjEE,QAAQ,GAAGA,QAAQ,CAACuC,OAAO,CAAC,OAAO,EAAE,UAAUW,KAAK,EAAE;IACpD,OAAO2B,kBAAkB,CAAC3B,KAAK,CAAC;EAClC,CAAC,CAAC;EACFpD,MAAM,GAAGA,MAAM,CAACyC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC;EAEnC,OAAOhD,QAAQ,GAAGG,IAAI,GAAGM,QAAQ,GAAGF,MAAM,GAAGD,IAAI;AACnD,CAAC;AAED,SAAS8F,UAAUA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EACpC,OAAOpE,QAAQ,CAACmE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAACE,OAAO,CAACD,QAAQ,CAAC;AACxD;AAEAvG,GAAG,CAACyC,SAAS,CAAC+D,OAAO,GAAG,UAAUD,QAAQ,EAAE;EAC1C,OAAO,IAAI,CAACE,aAAa,CAACtE,QAAQ,CAACoE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAACZ,MAAM,CAAC,CAAC;AACrE,CAAC;AAED,SAASe,gBAAgBA,CAACJ,MAAM,EAAEC,QAAQ,EAAE;EAC1C,IAAI,CAACD,MAAM,EAAE;IAAE,OAAOC,QAAQ;EAAE;EAChC,OAAOpE,QAAQ,CAACmE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAACG,aAAa,CAACF,QAAQ,CAAC;AAC9D;AAEAvG,GAAG,CAACyC,SAAS,CAACgE,aAAa,GAAG,UAAUF,QAAQ,EAAE;EAChD,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAChC,IAAII,GAAG,GAAG,IAAI3G,GAAG,CAAC,CAAC;IACnB2G,GAAG,CAACnE,KAAK,CAAC+D,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC;IAChCA,QAAQ,GAAGI,GAAG;EAChB;EAEA,IAAIC,MAAM,GAAG,IAAI5G,GAAG,CAAC,CAAC;EACtB,IAAI6G,KAAK,GAAGd,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC;EAC7B,KAAK,IAAIc,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGD,KAAK,CAACxD,MAAM,EAAEyD,EAAE,EAAE,EAAE;IACxC,IAAIC,IAAI,GAAGF,KAAK,CAACC,EAAE,CAAC;IACpBF,MAAM,CAACG,IAAI,CAAC,GAAG,IAAI,CAACA,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;EACEH,MAAM,CAACrG,IAAI,GAAGgG,QAAQ,CAAChG,IAAI;;EAE3B;EACA,IAAIgG,QAAQ,CAAC3F,IAAI,KAAK,EAAE,EAAE;IACxBgG,MAAM,CAAChG,IAAI,GAAGgG,MAAM,CAACjB,MAAM,CAAC,CAAC;IAC7B,OAAOiB,MAAM;EACf;;EAEA;EACA,IAAIL,QAAQ,CAACrG,OAAO,IAAI,CAACqG,QAAQ,CAACtG,QAAQ,EAAE;IAC1C;IACA,IAAI+G,KAAK,GAAGjB,MAAM,CAACC,IAAI,CAACO,QAAQ,CAAC;IACjC,KAAK,IAAIU,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGD,KAAK,CAAC3D,MAAM,EAAE4D,EAAE,EAAE,EAAE;MACxC,IAAIC,IAAI,GAAGF,KAAK,CAACC,EAAE,CAAC;MACpB,IAAIC,IAAI,KAAK,UAAU,EAAE;QAAEN,MAAM,CAACM,IAAI,CAAC,GAAGX,QAAQ,CAACW,IAAI,CAAC;MAAE;IAC5D;;IAEA;IACA,IAAItF,eAAe,CAACgF,MAAM,CAAC3G,QAAQ,CAAC,IAAI2G,MAAM,CAACtG,QAAQ,IAAI,CAACsG,MAAM,CAAClG,QAAQ,EAAE;MAC3EkG,MAAM,CAAClG,QAAQ,GAAG,GAAG;MACrBkG,MAAM,CAACjG,IAAI,GAAGiG,MAAM,CAAClG,QAAQ;IAC/B;IAEAkG,MAAM,CAAChG,IAAI,GAAGgG,MAAM,CAACjB,MAAM,CAAC,CAAC;IAC7B,OAAOiB,MAAM;EACf;EAEA,IAAIL,QAAQ,CAACtG,QAAQ,IAAIsG,QAAQ,CAACtG,QAAQ,KAAK2G,MAAM,CAAC3G,QAAQ,EAAE;IAC9D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAAC2B,eAAe,CAAC2E,QAAQ,CAACtG,QAAQ,CAAC,EAAE;MACvC,IAAI+F,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACO,QAAQ,CAAC;MAChC,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,IAAI,CAAC3C,MAAM,EAAE8D,CAAC,EAAE,EAAE;QACpC,IAAIxC,CAAC,GAAGqB,IAAI,CAACmB,CAAC,CAAC;QACfP,MAAM,CAACjC,CAAC,CAAC,GAAG4B,QAAQ,CAAC5B,CAAC,CAAC;MACzB;MACAiC,MAAM,CAAChG,IAAI,GAAGgG,MAAM,CAACjB,MAAM,CAAC,CAAC;MAC7B,OAAOiB,MAAM;IACf;IAEAA,MAAM,CAAC3G,QAAQ,GAAGsG,QAAQ,CAACtG,QAAQ;IACnC,IAAI,CAACsG,QAAQ,CAACnG,IAAI,IAAI,CAACuB,gBAAgB,CAAC4E,QAAQ,CAACtG,QAAQ,CAAC,EAAE;MAC1D,IAAImH,OAAO,GAAG,CAACb,QAAQ,CAAC7F,QAAQ,IAAI,EAAE,EAAEqC,KAAK,CAAC,GAAG,CAAC;MAClD,OAAOqE,OAAO,CAAC/D,MAAM,IAAI,EAAEkD,QAAQ,CAACnG,IAAI,GAAGgH,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAE;MAC/D,IAAI,CAACd,QAAQ,CAACnG,IAAI,EAAE;QAAEmG,QAAQ,CAACnG,IAAI,GAAG,EAAE;MAAE;MAC1C,IAAI,CAACmG,QAAQ,CAACjG,QAAQ,EAAE;QAAEiG,QAAQ,CAACjG,QAAQ,GAAG,EAAE;MAAE;MAClD,IAAI8G,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;QAAEA,OAAO,CAACnC,OAAO,CAAC,EAAE,CAAC;MAAE;MAC9C,IAAImC,OAAO,CAAC/D,MAAM,GAAG,CAAC,EAAE;QAAE+D,OAAO,CAACnC,OAAO,CAAC,EAAE,CAAC;MAAE;MAC/C2B,MAAM,CAAClG,QAAQ,GAAG0G,OAAO,CAAClE,IAAI,CAAC,GAAG,CAAC;IACrC,CAAC,MAAM;MACL0D,MAAM,CAAClG,QAAQ,GAAG6F,QAAQ,CAAC7F,QAAQ;IACrC;IACAkG,MAAM,CAACpG,MAAM,GAAG+F,QAAQ,CAAC/F,MAAM;IAC/BoG,MAAM,CAACnG,KAAK,GAAG8F,QAAQ,CAAC9F,KAAK;IAC7BmG,MAAM,CAACxG,IAAI,GAAGmG,QAAQ,CAACnG,IAAI,IAAI,EAAE;IACjCwG,MAAM,CAACzG,IAAI,GAAGoG,QAAQ,CAACpG,IAAI;IAC3ByG,MAAM,CAACtG,QAAQ,GAAGiG,QAAQ,CAACjG,QAAQ,IAAIiG,QAAQ,CAACnG,IAAI;IACpDwG,MAAM,CAACvG,IAAI,GAAGkG,QAAQ,CAAClG,IAAI;IAC3B;IACA,IAAIuG,MAAM,CAAClG,QAAQ,IAAIkG,MAAM,CAACpG,MAAM,EAAE;MACpC,IAAI2E,CAAC,GAAGyB,MAAM,CAAClG,QAAQ,IAAI,EAAE;MAC7B,IAAIgF,CAAC,GAAGkB,MAAM,CAACpG,MAAM,IAAI,EAAE;MAC3BoG,MAAM,CAACjG,IAAI,GAAGwE,CAAC,GAAGO,CAAC;IACrB;IACAkB,MAAM,CAAC1G,OAAO,GAAG0G,MAAM,CAAC1G,OAAO,IAAIqG,QAAQ,CAACrG,OAAO;IACnD0G,MAAM,CAAChG,IAAI,GAAGgG,MAAM,CAACjB,MAAM,CAAC,CAAC;IAC7B,OAAOiB,MAAM;EACf;EAEA,IAAIU,WAAW,GAAGV,MAAM,CAAClG,QAAQ,IAAIkG,MAAM,CAAClG,QAAQ,CAAC0F,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;IACpEmB,QAAQ,GAAGhB,QAAQ,CAACnG,IAAI,IAAImG,QAAQ,CAAC7F,QAAQ,IAAI6F,QAAQ,CAAC7F,QAAQ,CAAC0F,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;IACpFoB,UAAU,GAAGD,QAAQ,IAAID,WAAW,IAAKV,MAAM,CAACxG,IAAI,IAAImG,QAAQ,CAAC7F,QAAS;IAC1E+G,aAAa,GAAGD,UAAU;IAC1BE,OAAO,GAAGd,MAAM,CAAClG,QAAQ,IAAIkG,MAAM,CAAClG,QAAQ,CAACqC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE;IAC7DqE,OAAO,GAAGb,QAAQ,CAAC7F,QAAQ,IAAI6F,QAAQ,CAAC7F,QAAQ,CAACqC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE;IACjE4E,SAAS,GAAGf,MAAM,CAAC3G,QAAQ,IAAI,CAAC2B,eAAe,CAACgF,MAAM,CAAC3G,QAAQ,CAAC;;EAElE;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAI0H,SAAS,EAAE;IACbf,MAAM,CAACtG,QAAQ,GAAG,EAAE;IACpBsG,MAAM,CAACvG,IAAI,GAAG,IAAI;IAClB,IAAIuG,MAAM,CAACxG,IAAI,EAAE;MACf,IAAIsH,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;QAAEA,OAAO,CAAC,CAAC,CAAC,GAAGd,MAAM,CAACxG,IAAI;MAAE,CAAC,MAAM;QAAEsH,OAAO,CAACzC,OAAO,CAAC2B,MAAM,CAACxG,IAAI,CAAC;MAAE;IAC5F;IACAwG,MAAM,CAACxG,IAAI,GAAG,EAAE;IAChB,IAAImG,QAAQ,CAACtG,QAAQ,EAAE;MACrBsG,QAAQ,CAACjG,QAAQ,GAAG,IAAI;MACxBiG,QAAQ,CAAClG,IAAI,GAAG,IAAI;MACpB,IAAIkG,QAAQ,CAACnG,IAAI,EAAE;QACjB,IAAIgH,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;UAAEA,OAAO,CAAC,CAAC,CAAC,GAAGb,QAAQ,CAACnG,IAAI;QAAE,CAAC,MAAM;UAAEgH,OAAO,CAACnC,OAAO,CAACsB,QAAQ,CAACnG,IAAI,CAAC;QAAE;MAChG;MACAmG,QAAQ,CAACnG,IAAI,GAAG,IAAI;IACtB;IACAoH,UAAU,GAAGA,UAAU,KAAKJ,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,IAAIM,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;EACrE;EAEA,IAAIH,QAAQ,EAAE;IACZ;IACAX,MAAM,CAACxG,IAAI,GAAGmG,QAAQ,CAACnG,IAAI,IAAImG,QAAQ,CAACnG,IAAI,KAAK,EAAE,GAAGmG,QAAQ,CAACnG,IAAI,GAAGwG,MAAM,CAACxG,IAAI;IACjFwG,MAAM,CAACtG,QAAQ,GAAGiG,QAAQ,CAACjG,QAAQ,IAAIiG,QAAQ,CAACjG,QAAQ,KAAK,EAAE,GAAGiG,QAAQ,CAACjG,QAAQ,GAAGsG,MAAM,CAACtG,QAAQ;IACrGsG,MAAM,CAACpG,MAAM,GAAG+F,QAAQ,CAAC/F,MAAM;IAC/BoG,MAAM,CAACnG,KAAK,GAAG8F,QAAQ,CAAC9F,KAAK;IAC7BiH,OAAO,GAAGN,OAAO;IACjB;EACF,CAAC,MAAM,IAAIA,OAAO,CAAC/D,MAAM,EAAE;IACzB;AACJ;AACA;AACA;IACI,IAAI,CAACqE,OAAO,EAAE;MAAEA,OAAO,GAAG,EAAE;IAAE;IAC9BA,OAAO,CAACE,GAAG,CAAC,CAAC;IACbF,OAAO,GAAGA,OAAO,CAACxG,MAAM,CAACkG,OAAO,CAAC;IACjCR,MAAM,CAACpG,MAAM,GAAG+F,QAAQ,CAAC/F,MAAM;IAC/BoG,MAAM,CAACnG,KAAK,GAAG8F,QAAQ,CAAC9F,KAAK;EAC/B,CAAC,MAAM,IAAI8F,QAAQ,CAAC/F,MAAM,IAAI,IAAI,EAAE;IAClC;AACJ;AACA;AACA;AACA;IACI,IAAImH,SAAS,EAAE;MACbf,MAAM,CAACxG,IAAI,GAAGsH,OAAO,CAACL,KAAK,CAAC,CAAC;MAC7BT,MAAM,CAACtG,QAAQ,GAAGsG,MAAM,CAACxG,IAAI;MAC7B;AACN;AACA;AACA;AACA;MACM,IAAIyH,UAAU,GAAGjB,MAAM,CAACxG,IAAI,IAAIwG,MAAM,CAACxG,IAAI,CAACwC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGgE,MAAM,CAACxG,IAAI,CAAC2C,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK;MAC7F,IAAI8E,UAAU,EAAE;QACdjB,MAAM,CAACzG,IAAI,GAAG0H,UAAU,CAACR,KAAK,CAAC,CAAC;QAChCT,MAAM,CAACtG,QAAQ,GAAGuH,UAAU,CAACR,KAAK,CAAC,CAAC;QACpCT,MAAM,CAACxG,IAAI,GAAGwG,MAAM,CAACtG,QAAQ;MAC/B;IACF;IACAsG,MAAM,CAACpG,MAAM,GAAG+F,QAAQ,CAAC/F,MAAM;IAC/BoG,MAAM,CAACnG,KAAK,GAAG8F,QAAQ,CAAC9F,KAAK;IAC7B;IACA,IAAImG,MAAM,CAAClG,QAAQ,KAAK,IAAI,IAAIkG,MAAM,CAACpG,MAAM,KAAK,IAAI,EAAE;MACtDoG,MAAM,CAACjG,IAAI,GAAG,CAACiG,MAAM,CAAClG,QAAQ,GAAGkG,MAAM,CAAClG,QAAQ,GAAG,EAAE,KAAKkG,MAAM,CAACpG,MAAM,GAAGoG,MAAM,CAACpG,MAAM,GAAG,EAAE,CAAC;IAC/F;IACAoG,MAAM,CAAChG,IAAI,GAAGgG,MAAM,CAACjB,MAAM,CAAC,CAAC;IAC7B,OAAOiB,MAAM;EACf;EAEA,IAAI,CAACc,OAAO,CAACrE,MAAM,EAAE;IACnB;AACJ;AACA;AACA;IACIuD,MAAM,CAAClG,QAAQ,GAAG,IAAI;IACtB;IACA,IAAIkG,MAAM,CAACpG,MAAM,EAAE;MACjBoG,MAAM,CAACjG,IAAI,GAAG,GAAG,GAAGiG,MAAM,CAACpG,MAAM;IACnC,CAAC,MAAM;MACLoG,MAAM,CAACjG,IAAI,GAAG,IAAI;IACpB;IACAiG,MAAM,CAAChG,IAAI,GAAGgG,MAAM,CAACjB,MAAM,CAAC,CAAC;IAC7B,OAAOiB,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIkB,IAAI,GAAGJ,OAAO,CAACxD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B,IAAI6D,gBAAgB,GAAG,CAACnB,MAAM,CAACxG,IAAI,IAAImG,QAAQ,CAACnG,IAAI,IAAIsH,OAAO,CAACrE,MAAM,GAAG,CAAC,MAAMyE,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,IAAI,CAAC,IAAIA,IAAI,KAAK,EAAE;;EAE7H;AACF;AACA;AACA;EACE,IAAIE,EAAE,GAAG,CAAC;EACV,KAAK,IAAIlE,CAAC,GAAG4D,OAAO,CAACrE,MAAM,EAAES,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACxCgE,IAAI,GAAGJ,OAAO,CAAC5D,CAAC,CAAC;IACjB,IAAIgE,IAAI,KAAK,GAAG,EAAE;MAChBJ,OAAO,CAACO,MAAM,CAACnE,CAAC,EAAE,CAAC,CAAC;IACtB,CAAC,MAAM,IAAIgE,IAAI,KAAK,IAAI,EAAE;MACxBJ,OAAO,CAACO,MAAM,CAACnE,CAAC,EAAE,CAAC,CAAC;MACpBkE,EAAE,EAAE;IACN,CAAC,MAAM,IAAIA,EAAE,EAAE;MACbN,OAAO,CAACO,MAAM,CAACnE,CAAC,EAAE,CAAC,CAAC;MACpBkE,EAAE,EAAE;IACN;EACF;;EAEA;EACA,IAAI,CAACR,UAAU,IAAI,CAACC,aAAa,EAAE;IACjC,OAAOO,EAAE,EAAE,EAAEA,EAAE,EAAE;MACfN,OAAO,CAACzC,OAAO,CAAC,IAAI,CAAC;IACvB;EACF;EAEA,IAAIuC,UAAU,IAAIE,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAACA,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC,CAACtB,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;IACpFsB,OAAO,CAACzC,OAAO,CAAC,EAAE,CAAC;EACrB;EAEA,IAAI8C,gBAAgB,IAAKL,OAAO,CAACxE,IAAI,CAAC,GAAG,CAAC,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAI,EAAE;IAC9DkE,OAAO,CAAC1C,IAAI,CAAC,EAAE,CAAC;EAClB;EAEA,IAAIkD,UAAU,GAAGR,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,IAAKA,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC,CAACtB,MAAM,CAAC,CAAC,CAAC,KAAK,GAAI;;EAElF;EACA,IAAIuB,SAAS,EAAE;IACbf,MAAM,CAACtG,QAAQ,GAAG4H,UAAU,GAAG,EAAE,GAAGR,OAAO,CAACrE,MAAM,GAAGqE,OAAO,CAACL,KAAK,CAAC,CAAC,GAAG,EAAE;IACzET,MAAM,CAACxG,IAAI,GAAGwG,MAAM,CAACtG,QAAQ;IAC7B;AACJ;AACA;AACA;AACA;IACI,IAAIuH,UAAU,GAAGjB,MAAM,CAACxG,IAAI,IAAIwG,MAAM,CAACxG,IAAI,CAACwC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGgE,MAAM,CAACxG,IAAI,CAAC2C,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK;IAC7F,IAAI8E,UAAU,EAAE;MACdjB,MAAM,CAACzG,IAAI,GAAG0H,UAAU,CAACR,KAAK,CAAC,CAAC;MAChCT,MAAM,CAACtG,QAAQ,GAAGuH,UAAU,CAACR,KAAK,CAAC,CAAC;MACpCT,MAAM,CAACxG,IAAI,GAAGwG,MAAM,CAACtG,QAAQ;IAC/B;EACF;EAEAkH,UAAU,GAAGA,UAAU,IAAKZ,MAAM,CAACxG,IAAI,IAAIsH,OAAO,CAACrE,MAAO;EAE1D,IAAImE,UAAU,IAAI,CAACU,UAAU,EAAE;IAC7BR,OAAO,CAACzC,OAAO,CAAC,EAAE,CAAC;EACrB;EAEA,IAAIyC,OAAO,CAACrE,MAAM,GAAG,CAAC,EAAE;IACtBuD,MAAM,CAAClG,QAAQ,GAAGgH,OAAO,CAACxE,IAAI,CAAC,GAAG,CAAC;EACrC,CAAC,MAAM;IACL0D,MAAM,CAAClG,QAAQ,GAAG,IAAI;IACtBkG,MAAM,CAACjG,IAAI,GAAG,IAAI;EACpB;;EAEA;EACA,IAAIiG,MAAM,CAAClG,QAAQ,KAAK,IAAI,IAAIkG,MAAM,CAACpG,MAAM,KAAK,IAAI,EAAE;IACtDoG,MAAM,CAACjG,IAAI,GAAG,CAACiG,MAAM,CAAClG,QAAQ,GAAGkG,MAAM,CAAClG,QAAQ,GAAG,EAAE,KAAKkG,MAAM,CAACpG,MAAM,GAAGoG,MAAM,CAACpG,MAAM,GAAG,EAAE,CAAC;EAC/F;EACAoG,MAAM,CAACzG,IAAI,GAAGoG,QAAQ,CAACpG,IAAI,IAAIyG,MAAM,CAACzG,IAAI;EAC1CyG,MAAM,CAAC1G,OAAO,GAAG0G,MAAM,CAAC1G,OAAO,IAAIqG,QAAQ,CAACrG,OAAO;EACnD0G,MAAM,CAAChG,IAAI,GAAGgG,MAAM,CAACjB,MAAM,CAAC,CAAC;EAC7B,OAAOiB,MAAM;AACf,CAAC;AAED5G,GAAG,CAACyC,SAAS,CAAC2B,SAAS,GAAG,YAAY;EACpC,IAAIhE,IAAI,GAAG,IAAI,CAACA,IAAI;EACpB,IAAIC,IAAI,GAAGS,WAAW,CAACyC,IAAI,CAACnD,IAAI,CAAC;EACjC,IAAIC,IAAI,EAAE;IACRA,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC;IACd,IAAIA,IAAI,KAAK,GAAG,EAAE;MAChB,IAAI,CAACA,IAAI,GAAGA,IAAI,CAACmD,MAAM,CAAC,CAAC,CAAC;IAC5B;IACApD,IAAI,GAAGA,IAAI,CAACoD,MAAM,CAAC,CAAC,EAAEpD,IAAI,CAACiD,MAAM,GAAGhD,IAAI,CAACgD,MAAM,CAAC;EAClD;EACA,IAAIjD,IAAI,EAAE;IAAE,IAAI,CAACE,QAAQ,GAAGF,IAAI;EAAE;AACpC,CAAC;AAED+H,OAAO,CAAC3F,KAAK,GAAGL,QAAQ;AACxBgG,OAAO,CAAC3B,OAAO,GAAGH,UAAU;AAC5B8B,OAAO,CAAC1B,aAAa,GAAGC,gBAAgB;AACxCyB,OAAO,CAACxC,MAAM,GAAGC,SAAS;AAE1BuC,OAAO,CAACnI,GAAG,GAAGA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}