{"ast": null, "code": "import { settings } from \"@pixi/settings\";\nimport { settings as settings2 } from \"@pixi/settings\";\nimport { deprecation } from \"@pixi/utils\";\nimport { Ticker } from \"./Ticker.mjs\";\nObject.defineProperties(settings, {\n  /**\n   * Target frames per millisecond.\n   * @static\n   * @name TARGET_FPMS\n   * @memberof PIXI.settings\n   * @type {number}\n   * @deprecated since 7.1.0\n   * @see PIXI.Ticker.targetFPMS\n   */\n  TARGET_FPMS: {\n    get() {\n      return Ticker.targetFPMS;\n    },\n    set(value) {\n      deprecation(\"7.1.0\", \"settings.TARGET_FPMS is deprecated, use Ticker.targetFPMS\"), Ticker.targetFPMS = value;\n    }\n  }\n});\nexport { settings2 as settings };", "map": {"version": 3, "names": ["Object", "defineProperties", "settings", "TARGET_FPMS", "get", "Ticker", "targetFPMS", "set", "value", "deprecation"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\ticker\\src\\settings.ts"], "sourcesContent": ["import { settings } from '@pixi/settings';\nimport { deprecation } from '@pixi/utils';\nimport { Ticker } from './Ticker';\n\nObject.defineProperties(settings, {\n    /**\n     * Target frames per millisecond.\n     * @static\n     * @name TARGET_FPMS\n     * @memberof PIXI.settings\n     * @type {number}\n     * @deprecated since 7.1.0\n     * @see PIXI.Ticker.targetFPMS\n     */\n    TARGET_FPMS: {\n        get()\n        {\n            return Ticker.targetFPMS;\n        },\n        set(value: number)\n        {\n            if (process.env.DEBUG)\n            {\n                deprecation('7.1.0', 'settings.TARGET_FPMS is deprecated, use Ticker.targetFPMS');\n            }\n\n            Ticker.targetFPMS = value;\n        },\n    },\n});\n\nexport { settings };\n"], "mappings": ";;;;AAIAA,MAAA,CAAOC,gBAAA,CAAiBC,QAAA,EAAU;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAU9BC,WAAA,EAAa;IACTC,IAAA,EACA;MACI,OAAOC,MAAA,CAAOC,UAAA;IAClB;IACAC,IAAIC,KAAA,EACJ;MAGQC,WAAA,CAAY,SAAS,2DAA2D,GAGpFJ,MAAA,CAAOC,UAAA,GAAaE,KAAA;IACxB;EACJ;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}