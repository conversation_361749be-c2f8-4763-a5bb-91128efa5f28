{"ast": null, "code": "import { utils } from \"@pixi/core\";\nfunction fixOrientation(points, hole = !1) {\n  const m = points.length;\n  if (m < 6) return;\n  let area = 0;\n  for (let i = 0, x1 = points[m - 2], y1 = points[m - 1]; i < m; i += 2) {\n    const x2 = points[i],\n      y2 = points[i + 1];\n    area += (x2 - x1) * (y2 + y1), x1 = x2, y1 = y2;\n  }\n  if (!hole && area > 0 || hole && area <= 0) {\n    const n = m / 2;\n    for (let i = n + n % 2; i < m; i += 2) {\n      const i1 = m - i - 2,\n        i2 = m - i - 1,\n        i3 = i,\n        i4 = i + 1;\n      [points[i1], points[i3]] = [points[i3], points[i1]], [points[i2], points[i4]] = [points[i4], points[i2]];\n    }\n  }\n}\nconst buildPoly = {\n  build(graphicsData) {\n    graphicsData.points = graphicsData.shape.points.slice();\n  },\n  triangulate(graphicsData, graphicsGeometry) {\n    let points = graphicsData.points;\n    const holes = graphicsData.holes,\n      verts = graphicsGeometry.points,\n      indices = graphicsGeometry.indices;\n    if (points.length >= 6) {\n      fixOrientation(points, !1);\n      const holeArray = [];\n      for (let i = 0; i < holes.length; i++) {\n        const hole = holes[i];\n        fixOrientation(hole.points, !0), holeArray.push(points.length / 2), points = points.concat(hole.points);\n      }\n      const triangles = utils.earcut(points, holeArray, 2);\n      if (!triangles) return;\n      const vertPos = verts.length / 2;\n      for (let i = 0; i < triangles.length; i += 3) indices.push(triangles[i] + vertPos), indices.push(triangles[i + 1] + vertPos), indices.push(triangles[i + 2] + vertPos);\n      for (let i = 0; i < points.length; i++) verts.push(points[i]);\n    }\n  }\n};\nexport { buildPoly };", "map": {"version": 3, "names": ["fixOrientation", "points", "hole", "m", "length", "area", "i", "x1", "y1", "x2", "y2", "n", "i1", "i2", "i3", "i4", "buildPoly", "build", "graphicsData", "shape", "slice", "triangulate", "graphicsGeometry", "holes", "verts", "indices", "holeArray", "push", "concat", "triangles", "utils", "earcut", "vertPos"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\graphics\\src\\utils\\buildPoly.ts"], "sourcesContent": ["import { utils } from '@pixi/core';\n\nimport type { Polygon } from '@pixi/core';\nimport type { IShapeBuildCommand } from './IShapeBuildCommand';\n\nfunction fixOrientation(points: number[], hole = false)\n{\n    const m = points.length;\n\n    if (m < 6)\n    {\n        return;\n    }\n\n    let area = 0;\n\n    for (let i = 0, x1 = points[m - 2], y1 = points[m - 1]; i < m; i += 2)\n    {\n        const x2 = points[i];\n        const y2 = points[i + 1];\n\n        area += (x2 - x1) * (y2 + y1);\n\n        x1 = x2;\n        y1 = y2;\n    }\n\n    if ((!hole && area > 0) || (hole && area <= 0))\n    {\n        const n = m / 2;\n\n        for (let i = n + (n % 2); i < m; i += 2)\n        {\n            const i1 = m - i - 2;\n            const i2 = m - i - 1;\n            const i3 = i;\n            const i4 = i + 1;\n\n            [points[i1], points[i3]] = [points[i3], points[i1]];\n            [points[i2], points[i4]] = [points[i4], points[i2]];\n        }\n    }\n}\n/**\n * Builds a polygon to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.WebGLGraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {object} webGLData - an object containing all the WebGL-specific information to create this shape\n * @param {object} webGLDataNativeLines - an object containing all the WebGL-specific information to create nativeLines\n */\nexport const buildPoly: IShapeBuildCommand = {\n\n    build(graphicsData)\n    {\n        graphicsData.points = (graphicsData.shape as Polygon).points.slice();\n    },\n\n    triangulate(graphicsData, graphicsGeometry)\n    {\n        let points = graphicsData.points;\n        const holes = graphicsData.holes;\n        const verts = graphicsGeometry.points;\n        const indices = graphicsGeometry.indices;\n\n        if (points.length >= 6)\n        {\n            fixOrientation(points, false);\n\n            const holeArray = [];\n            // Process holes..\n\n            for (let i = 0; i < holes.length; i++)\n            {\n                const hole = holes[i];\n\n                fixOrientation(hole.points, true);\n\n                holeArray.push(points.length / 2);\n                points = points.concat(hole.points);\n            }\n\n            // sort color\n            const triangles = utils.earcut(points, holeArray, 2);\n\n            if (!triangles)\n            {\n                return;\n            }\n\n            const vertPos = verts.length / 2;\n\n            for (let i = 0; i < triangles.length; i += 3)\n            {\n                indices.push(triangles[i] + vertPos);\n                indices.push(triangles[i + 1] + vertPos);\n                indices.push(triangles[i + 2] + vertPos);\n            }\n\n            for (let i = 0; i < points.length; i++)\n            {\n                verts.push(points[i]);\n            }\n        }\n    },\n};\n"], "mappings": ";AAKA,SAASA,eAAeC,MAAA,EAAkBC,IAAA,GAAO,IACjD;EACI,MAAMC,CAAA,GAAIF,MAAA,CAAOG,MAAA;EAEjB,IAAID,CAAA,GAAI,GAEJ;EAGJ,IAAIE,IAAA,GAAO;EAEX,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKN,MAAA,CAAOE,CAAA,GAAI,CAAC,GAAGK,EAAA,GAAKP,MAAA,CAAOE,CAAA,GAAI,CAAC,GAAGG,CAAA,GAAIH,CAAA,EAAGG,CAAA,IAAK,GACpE;IACI,MAAMG,EAAA,GAAKR,MAAA,CAAOK,CAAC;MACbI,EAAA,GAAKT,MAAA,CAAOK,CAAA,GAAI,CAAC;IAEvBD,IAAA,KAASI,EAAA,GAAKF,EAAA,KAAOG,EAAA,GAAKF,EAAA,GAE1BD,EAAA,GAAKE,EAAA,EACLD,EAAA,GAAKE,EAAA;EACT;EAEA,IAAK,CAACR,IAAA,IAAQG,IAAA,GAAO,KAAOH,IAAA,IAAQG,IAAA,IAAQ,GAC5C;IACI,MAAMM,CAAA,GAAIR,CAAA,GAAI;IAEd,SAASG,CAAA,GAAIK,CAAA,GAAKA,CAAA,GAAI,GAAIL,CAAA,GAAIH,CAAA,EAAGG,CAAA,IAAK,GACtC;MACU,MAAAM,EAAA,GAAKT,CAAA,GAAIG,CAAA,GAAI;QACbO,EAAA,GAAKV,CAAA,GAAIG,CAAA,GAAI;QACbQ,EAAA,GAAKR,CAAA;QACLS,EAAA,GAAKT,CAAA,GAAI;MAEf,CAACL,MAAA,CAAOW,EAAE,GAAGX,MAAA,CAAOa,EAAE,CAAC,IAAI,CAACb,MAAA,CAAOa,EAAE,GAAGb,MAAA,CAAOW,EAAE,CAAC,GAClD,CAACX,MAAA,CAAOY,EAAE,GAAGZ,MAAA,CAAOc,EAAE,CAAC,IAAI,CAACd,MAAA,CAAOc,EAAE,GAAGd,MAAA,CAAOY,EAAE,CAAC;IACtD;EACJ;AACJ;AAWO,MAAMG,SAAA,GAAgC;EAEzCC,MAAMC,YAAA,EACN;IACIA,YAAA,CAAajB,MAAA,GAAUiB,YAAA,CAAaC,KAAA,CAAkBlB,MAAA,CAAOmB,KAAA,CAAM;EACvE;EAEAC,YAAYH,YAAA,EAAcI,gBAAA,EAC1B;IACI,IAAIrB,MAAA,GAASiB,YAAA,CAAajB,MAAA;IAC1B,MAAMsB,KAAA,GAAQL,YAAA,CAAaK,KAAA;MACrBC,KAAA,GAAQF,gBAAA,CAAiBrB,MAAA;MACzBwB,OAAA,GAAUH,gBAAA,CAAiBG,OAAA;IAE7B,IAAAxB,MAAA,CAAOG,MAAA,IAAU,GACrB;MACIJ,cAAA,CAAeC,MAAA,EAAQ,EAAK;MAE5B,MAAMyB,SAAA,GAAY;MAGlB,SAASpB,CAAA,GAAI,GAAGA,CAAA,GAAIiB,KAAA,CAAMnB,MAAA,EAAQE,CAAA,IAClC;QACU,MAAAJ,IAAA,GAAOqB,KAAA,CAAMjB,CAAC;QAEpBN,cAAA,CAAeE,IAAA,CAAKD,MAAA,EAAQ,EAAI,GAEhCyB,SAAA,CAAUC,IAAA,CAAK1B,MAAA,CAAOG,MAAA,GAAS,CAAC,GAChCH,MAAA,GAASA,MAAA,CAAO2B,MAAA,CAAO1B,IAAA,CAAKD,MAAM;MACtC;MAGA,MAAM4B,SAAA,GAAYC,KAAA,CAAMC,MAAA,CAAO9B,MAAA,EAAQyB,SAAA,EAAW,CAAC;MAEnD,IAAI,CAACG,SAAA,EAED;MAGE,MAAAG,OAAA,GAAUR,KAAA,CAAMpB,MAAA,GAAS;MAE/B,SAASE,CAAA,GAAI,GAAGA,CAAA,GAAIuB,SAAA,CAAUzB,MAAA,EAAQE,CAAA,IAAK,GAE/BmB,OAAA,CAAAE,IAAA,CAAKE,SAAA,CAAUvB,CAAC,IAAI0B,OAAO,GACnCP,OAAA,CAAQE,IAAA,CAAKE,SAAA,CAAUvB,CAAA,GAAI,CAAC,IAAI0B,OAAO,GACvCP,OAAA,CAAQE,IAAA,CAAKE,SAAA,CAAUvB,CAAA,GAAI,CAAC,IAAI0B,OAAO;MAG3C,SAAS1B,CAAA,GAAI,GAAGA,CAAA,GAAIL,MAAA,CAAOG,MAAA,EAAQE,CAAA,IAEzBkB,KAAA,CAAAG,IAAA,CAAK1B,MAAA,CAAOK,CAAC,CAAC;IAE5B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}