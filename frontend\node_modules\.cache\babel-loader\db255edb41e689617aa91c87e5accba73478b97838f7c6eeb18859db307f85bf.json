{"ast": null, "code": "import { FederatedMouseEvent } from \"./FederatedMouseEvent.mjs\";\nclass FederatedWheelEvent extends FederatedMouseEvent {\n  constructor() {\n    super(...arguments), this.DOM_DELTA_PIXEL = 0, this.DOM_DELTA_LINE = 1, this.DOM_DELTA_PAGE = 2;\n  }\n}\nFederatedWheelEvent.DOM_DELTA_PIXEL = 0, /** Units specified in lines. */\nFederatedWheelEvent.DOM_DELTA_LINE = 1, /** Units specified in pages. */\nFederatedWheelEvent.DOM_DELTA_PAGE = 2;\nexport { FederatedWheelEvent };", "map": {"version": 3, "names": ["FederatedWheelEvent", "FederatedMouseEvent", "constructor", "arguments", "DOM_DELTA_PIXEL", "DOM_DELTA_LINE", "DOM_DELTA_PAGE"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\events\\src\\FederatedWheelEvent.ts"], "sourcesContent": ["import { FederatedMouseEvent } from './FederatedMouseEvent';\n\n/**\n * A {@link PIXI.FederatedEvent} for wheel events.\n * @memberof PIXI\n */\nexport class FederatedWheelEvent extends FederatedMouseEvent implements WheelEvent\n{\n    /**\n     * The units of `deltaX`, `deltaY`, and `deltaZ`. This is one of `DOM_DELTA_LINE`,\n     * `DOM_DELTA_PAGE`, `DOM_DELTA_PIXEL`.\n     */\n    deltaMode: number;\n\n    /** Horizontal scroll amount */\n    deltaX: number;\n\n    /** Vertical scroll amount */\n    deltaY: number;\n\n    /** z-axis scroll amount. */\n    deltaZ: number;\n\n    /** Units specified in pixels. */\n    static readonly DOM_DELTA_PIXEL = 0;\n\n    /** Units specified in pixels. */\n    readonly DOM_DELTA_PIXEL = 0;\n\n    /** Units specified in lines. */\n    static readonly DOM_DELTA_LINE = 1;\n\n    /** Units specified in lines. */\n    readonly DOM_DELTA_LINE = 1;\n\n    /** Units specified in pages. */\n    static readonly DOM_DELTA_PAGE = 2;\n\n    /** Units specified in pages. */\n    readonly DOM_DELTA_PAGE = 2;\n}\n"], "mappings": ";AAMO,MAAMA,mBAAA,SAA4BC,mBAAA,CACzC;EADOC,YAAA;IAAA,SAAAC,SAAA,GAqBH,KAASC,eAAA,GAAkB,GAM3B,KAASC,cAAA,GAAiB,GAM1B,KAASC,cAAA,GAAiB;EAAA;AAC9B;AAlCaN,mBAAA,CAkBOI,eAAA,GAAkB;AAlBzBJ,mBAAA,CAwBOK,cAAA,GAAiB;AAxBxBL,mBAAA,CA8BOM,cAAA,GAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}