{"ast": null, "code": "import { MSAA_QUALITY } from \"@pixi/constants\";\nimport { nextPow2 } from \"@pixi/utils\";\nimport { BaseRenderTexture } from \"./BaseRenderTexture.mjs\";\nimport { RenderTexture } from \"./RenderTexture.mjs\";\nclass RenderTexturePool {\n  /**\n   * @param textureOptions - options that will be passed to BaseRenderTexture constructor\n   * @param {PIXI.SCALE_MODES} [textureOptions.scaleMode] - See {@link PIXI.SCALE_MODES} for possible values.\n   */\n  constructor(textureOptions) {\n    this.texturePool = {}, this.textureOptions = textureOptions || {}, this.enableFullScreen = !1, this._pixelsWidth = 0, this._pixelsHeight = 0;\n  }\n  /**\n   * Creates texture with params that were specified in pool constructor.\n   * @param realWidth - Width of texture in pixels.\n   * @param realHeight - Height of texture in pixels.\n   * @param multisample - Number of samples of the framebuffer.\n   */\n  createTexture(realWidth, realHeight, multisample = MSAA_QUALITY.NONE) {\n    const baseRenderTexture = new BaseRenderTexture(Object.assign({\n      width: realWidth,\n      height: realHeight,\n      resolution: 1,\n      multisample\n    }, this.textureOptions));\n    return new RenderTexture(baseRenderTexture);\n  }\n  /**\n   * Gets a Power-of-Two render texture or fullScreen texture\n   * @param minWidth - The minimum width of the render texture.\n   * @param minHeight - The minimum height of the render texture.\n   * @param resolution - The resolution of the render texture.\n   * @param multisample - Number of samples of the render texture.\n   * @returns The new render texture.\n   */\n  getOptimalTexture(minWidth, minHeight, resolution = 1, multisample = MSAA_QUALITY.NONE) {\n    let key;\n    minWidth = Math.max(Math.ceil(minWidth * resolution - 1e-6), 1), minHeight = Math.max(Math.ceil(minHeight * resolution - 1e-6), 1), !this.enableFullScreen || minWidth !== this._pixelsWidth || minHeight !== this._pixelsHeight ? (minWidth = nextPow2(minWidth), minHeight = nextPow2(minHeight), key = ((minWidth & 65535) << 16 | minHeight & 65535) >>> 0, multisample > 1 && (key += multisample * 4294967296)) : key = multisample > 1 ? -multisample : -1, this.texturePool[key] || (this.texturePool[key] = []);\n    let renderTexture = this.texturePool[key].pop();\n    return renderTexture || (renderTexture = this.createTexture(minWidth, minHeight, multisample)), renderTexture.filterPoolKey = key, renderTexture.setResolution(resolution), renderTexture;\n  }\n  /**\n   * Gets extra texture of the same size as input renderTexture\n   *\n   * `getFilterTexture(input, 0.5)` or `getFilterTexture(0.5, input)`\n   * @param input - renderTexture from which size and resolution will be copied\n   * @param resolution - override resolution of the renderTexture\n   *  It overrides, it does not multiply\n   * @param multisample - number of samples of the renderTexture\n   */\n  getFilterTexture(input, resolution, multisample) {\n    const filterTexture = this.getOptimalTexture(input.width, input.height, resolution || input.resolution, multisample || MSAA_QUALITY.NONE);\n    return filterTexture.filterFrame = input.filterFrame, filterTexture;\n  }\n  /**\n   * Place a render texture back into the pool.\n   * @param renderTexture - The renderTexture to free\n   */\n  returnTexture(renderTexture) {\n    const key = renderTexture.filterPoolKey;\n    renderTexture.filterFrame = null, this.texturePool[key].push(renderTexture);\n  }\n  /**\n   * Alias for returnTexture, to be compliant with FilterSystem interface.\n   * @param renderTexture - The renderTexture to free\n   */\n  returnFilterTexture(renderTexture) {\n    this.returnTexture(renderTexture);\n  }\n  /**\n   * Clears the pool.\n   * @param destroyTextures - Destroy all stored textures.\n   */\n  clear(destroyTextures) {\n    if (destroyTextures = destroyTextures !== !1, destroyTextures) for (const i in this.texturePool) {\n      const textures = this.texturePool[i];\n      if (textures) for (let j = 0; j < textures.length; j++) textures[j].destroy(!0);\n    }\n    this.texturePool = {};\n  }\n  /**\n   * If screen size was changed, drops all screen-sized textures,\n   * sets new screen size, sets `enableFullScreen` to true\n   *\n   * Size is measured in pixels, `renderer.view` can be passed here, not `renderer.screen`\n   * @param size - Initial size of screen.\n   */\n  setScreenSize(size) {\n    if (!(size.width === this._pixelsWidth && size.height === this._pixelsHeight)) {\n      this.enableFullScreen = size.width > 0 && size.height > 0;\n      for (const i in this.texturePool) {\n        if (!(Number(i) < 0)) continue;\n        const textures = this.texturePool[i];\n        if (textures) for (let j = 0; j < textures.length; j++) textures[j].destroy(!0);\n        this.texturePool[i] = [];\n      }\n      this._pixelsWidth = size.width, this._pixelsHeight = size.height;\n    }\n  }\n}\nRenderTexturePool.SCREEN_KEY = -1;\nexport { RenderTexturePool };", "map": {"version": 3, "names": ["RenderTexturePool", "constructor", "textureOptions", "texturePool", "enableFullScreen", "_pixelsWidth", "_pixelsHeight", "createTexture", "realWidth", "realHeight", "multisample", "MSAA_QUALITY", "NONE", "baseRenderTexture", "BaseRenderTexture", "Object", "assign", "width", "height", "resolution", "RenderTexture", "getOptimalTexture", "min<PERSON><PERSON><PERSON>", "minHeight", "key", "Math", "max", "ceil", "nextPow2", "renderTexture", "pop", "filterPoolKey", "setResolution", "getFilterTexture", "input", "filterTexture", "filterFrame", "returnTexture", "push", "returnFilterTexture", "clear", "destroyTextures", "i", "textures", "j", "length", "destroy", "setScreenSize", "size", "Number", "SCREEN_KEY"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\renderTexture\\RenderTexturePool.ts"], "sourcesContent": ["import { MSAA_QUALITY } from '@pixi/constants';\nimport { nextPow2 } from '@pixi/utils';\nimport { BaseRenderTexture } from './BaseRenderTexture';\nimport { RenderTexture } from './RenderTexture';\n\nimport type { ISize } from '@pixi/math';\nimport type { IBaseTextureOptions } from '../textures/BaseTexture';\n\n/**\n * Texture pool, used by FilterSystem and plugins.\n *\n * Stores collection of temporary pow2 or screen-sized renderTextures\n *\n * If you use custom RenderTexturePool for your filters, you can use methods\n * `getFilterTexture` and `returnFilterTexture` same as in\n * @memberof PIXI\n */\nexport class RenderTexturePool\n{\n    public textureOptions: IBaseTextureOptions;\n\n    /**\n     * Allow renderTextures of the same size as screen, not just pow2\n     *\n     * Automatically sets to true after `setScreenSize`\n     * @default false\n     */\n    public enableFullScreen: boolean;\n    texturePool: {[x in string | number]: RenderTexture[]};\n    private _pixelsWidth: number;\n    private _pixelsHeight: number;\n\n    /**\n     * @param textureOptions - options that will be passed to BaseRenderTexture constructor\n     * @param {PIXI.SCALE_MODES} [textureOptions.scaleMode] - See {@link PIXI.SCALE_MODES} for possible values.\n     */\n    constructor(textureOptions?: IBaseTextureOptions)\n    {\n        this.texturePool = {};\n        this.textureOptions = textureOptions || {};\n        this.enableFullScreen = false;\n\n        this._pixelsWidth = 0;\n        this._pixelsHeight = 0;\n    }\n\n    /**\n     * Creates texture with params that were specified in pool constructor.\n     * @param realWidth - Width of texture in pixels.\n     * @param realHeight - Height of texture in pixels.\n     * @param multisample - Number of samples of the framebuffer.\n     */\n    createTexture(realWidth: number, realHeight: number, multisample = MSAA_QUALITY.NONE): RenderTexture\n    {\n        const baseRenderTexture = new BaseRenderTexture(Object.assign({\n            width: realWidth,\n            height: realHeight,\n            resolution: 1,\n            multisample,\n        }, this.textureOptions));\n\n        return new RenderTexture(baseRenderTexture);\n    }\n\n    /**\n     * Gets a Power-of-Two render texture or fullScreen texture\n     * @param minWidth - The minimum width of the render texture.\n     * @param minHeight - The minimum height of the render texture.\n     * @param resolution - The resolution of the render texture.\n     * @param multisample - Number of samples of the render texture.\n     * @returns The new render texture.\n     */\n    getOptimalTexture(minWidth: number, minHeight: number, resolution = 1, multisample = MSAA_QUALITY.NONE): RenderTexture\n    {\n        let key;\n\n        minWidth = Math.max(Math.ceil((minWidth * resolution) - 1e-6), 1);\n        minHeight = Math.max(Math.ceil((minHeight * resolution) - 1e-6), 1);\n\n        if (!this.enableFullScreen || minWidth !== this._pixelsWidth || minHeight !== this._pixelsHeight)\n        {\n            minWidth = nextPow2(minWidth);\n            minHeight = nextPow2(minHeight);\n            key = (((minWidth & 0xFFFF) << 16) | (minHeight & 0xFFFF)) >>> 0;\n\n            if (multisample > 1)\n            {\n                key += multisample * 0x100000000;\n            }\n        }\n        else\n        {\n            key = multisample > 1 ? -multisample : -1;\n        }\n\n        if (!this.texturePool[key])\n        {\n            this.texturePool[key] = [];\n        }\n\n        let renderTexture = this.texturePool[key].pop();\n\n        if (!renderTexture)\n        {\n            renderTexture = this.createTexture(minWidth, minHeight, multisample);\n        }\n\n        renderTexture.filterPoolKey = key;\n        renderTexture.setResolution(resolution);\n\n        return renderTexture;\n    }\n\n    /**\n     * Gets extra texture of the same size as input renderTexture\n     *\n     * `getFilterTexture(input, 0.5)` or `getFilterTexture(0.5, input)`\n     * @param input - renderTexture from which size and resolution will be copied\n     * @param resolution - override resolution of the renderTexture\n     *  It overrides, it does not multiply\n     * @param multisample - number of samples of the renderTexture\n     */\n    getFilterTexture(input: RenderTexture, resolution?: number, multisample?: MSAA_QUALITY): RenderTexture\n    {\n        const filterTexture = this.getOptimalTexture(input.width, input.height, resolution || input.resolution,\n            multisample || MSAA_QUALITY.NONE);\n\n        filterTexture.filterFrame = input.filterFrame;\n\n        return filterTexture;\n    }\n\n    /**\n     * Place a render texture back into the pool.\n     * @param renderTexture - The renderTexture to free\n     */\n    returnTexture(renderTexture: RenderTexture): void\n    {\n        const key = renderTexture.filterPoolKey;\n\n        renderTexture.filterFrame = null;\n        this.texturePool[key].push(renderTexture);\n    }\n\n    /**\n     * Alias for returnTexture, to be compliant with FilterSystem interface.\n     * @param renderTexture - The renderTexture to free\n     */\n    returnFilterTexture(renderTexture: RenderTexture): void\n    {\n        this.returnTexture(renderTexture);\n    }\n\n    /**\n     * Clears the pool.\n     * @param destroyTextures - Destroy all stored textures.\n     */\n    clear(destroyTextures?: boolean): void\n    {\n        destroyTextures = destroyTextures !== false;\n        if (destroyTextures)\n        {\n            for (const i in this.texturePool)\n            {\n                const textures = this.texturePool[i];\n\n                if (textures)\n                {\n                    for (let j = 0; j < textures.length; j++)\n                    {\n                        textures[j].destroy(true);\n                    }\n                }\n            }\n        }\n\n        this.texturePool = {};\n    }\n\n    /**\n     * If screen size was changed, drops all screen-sized textures,\n     * sets new screen size, sets `enableFullScreen` to true\n     *\n     * Size is measured in pixels, `renderer.view` can be passed here, not `renderer.screen`\n     * @param size - Initial size of screen.\n     */\n    setScreenSize(size: ISize): void\n    {\n        if (size.width === this._pixelsWidth\n            && size.height === this._pixelsHeight)\n        {\n            return;\n        }\n\n        this.enableFullScreen = size.width > 0 && size.height > 0;\n\n        for (const i in this.texturePool)\n        {\n            if (!(Number(i) < 0))\n            {\n                continue;\n            }\n\n            const textures = this.texturePool[i];\n\n            if (textures)\n            {\n                for (let j = 0; j < textures.length; j++)\n                {\n                    textures[j].destroy(true);\n                }\n            }\n\n            this.texturePool[i] = [];\n        }\n\n        this._pixelsWidth = size.width;\n        this._pixelsHeight = size.height;\n    }\n\n    /**\n     * Key that is used to store fullscreen renderTextures in a pool\n     * @readonly\n     */\n    static SCREEN_KEY = -1;\n}\n"], "mappings": ";;;;AAiBO,MAAMA,iBAAA,CACb;EAAA;AAAA;AAAA;AAAA;EAkBIC,YAAYC,cAAA,EACZ;IACI,KAAKC,WAAA,GAAc,IACnB,KAAKD,cAAA,GAAiBA,cAAA,IAAkB,IACxC,KAAKE,gBAAA,GAAmB,IAExB,KAAKC,YAAA,GAAe,GACpB,KAAKC,aAAA,GAAgB;EACzB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAC,cAAcC,SAAA,EAAmBC,UAAA,EAAoBC,WAAA,GAAcC,YAAA,CAAaC,IAAA,EAChF;IACI,MAAMC,iBAAA,GAAoB,IAAIC,iBAAA,CAAkBC,MAAA,CAAOC,MAAA,CAAO;MAC1DC,KAAA,EAAOT,SAAA;MACPU,MAAA,EAAQT,UAAA;MACRU,UAAA,EAAY;MACZT;IAAA,GACD,KAAKR,cAAc,CAAC;IAEhB,WAAIkB,aAAA,CAAcP,iBAAiB;EAC9C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUAQ,kBAAkBC,QAAA,EAAkBC,SAAA,EAAmBJ,UAAA,GAAa,GAAGT,WAAA,GAAcC,YAAA,CAAaC,IAAA,EAClG;IACQ,IAAAY,GAAA;IAEJF,QAAA,GAAWG,IAAA,CAAKC,GAAA,CAAID,IAAA,CAAKE,IAAA,CAAML,QAAA,GAAWH,UAAA,GAAc,IAAI,GAAG,CAAC,GAChEI,SAAA,GAAYE,IAAA,CAAKC,GAAA,CAAID,IAAA,CAAKE,IAAA,CAAMJ,SAAA,GAAYJ,UAAA,GAAc,IAAI,GAAG,CAAC,GAE9D,CAAC,KAAKf,gBAAA,IAAoBkB,QAAA,KAAa,KAAKjB,YAAA,IAAgBkB,SAAA,KAAc,KAAKjB,aAAA,IAE/EgB,QAAA,GAAWM,QAAA,CAASN,QAAQ,GAC5BC,SAAA,GAAYK,QAAA,CAASL,SAAS,GAC9BC,GAAA,KAASF,QAAA,GAAW,UAAW,KAAOC,SAAA,GAAY,WAAa,GAE3Db,WAAA,GAAc,MAEdc,GAAA,IAAOd,WAAA,GAAc,eAKzBc,GAAA,GAAMd,WAAA,GAAc,IAAI,CAACA,WAAA,GAAc,IAGtC,KAAKP,WAAA,CAAYqB,GAAG,MAErB,KAAKrB,WAAA,CAAYqB,GAAG,IAAI;IAG5B,IAAIK,aAAA,GAAgB,KAAK1B,WAAA,CAAYqB,GAAG,EAAEM,GAAA,CAAI;IAE9C,OAAKD,aAAA,KAEDA,aAAA,GAAgB,KAAKtB,aAAA,CAAce,QAAA,EAAUC,SAAA,EAAWb,WAAW,IAGvEmB,aAAA,CAAcE,aAAA,GAAgBP,GAAA,EAC9BK,aAAA,CAAcG,aAAA,CAAcb,UAAU,GAE/BU,aAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWAI,iBAAiBC,KAAA,EAAsBf,UAAA,EAAqBT,WAAA,EAC5D;IACI,MAAMyB,aAAA,GAAgB,KAAKd,iBAAA,CAAkBa,KAAA,CAAMjB,KAAA,EAAOiB,KAAA,CAAMhB,MAAA,EAAQC,UAAA,IAAce,KAAA,CAAMf,UAAA,EACxFT,WAAA,IAAeC,YAAA,CAAaC,IAAA;IAElB,OAAAuB,aAAA,CAAAC,WAAA,GAAcF,KAAA,CAAME,WAAA,EAE3BD,aAAA;EACX;EAAA;AAAA;AAAA;AAAA;EAMAE,cAAcR,aAAA,EACd;IACI,MAAML,GAAA,GAAMK,aAAA,CAAcE,aAAA;IAE1BF,aAAA,CAAcO,WAAA,GAAc,MAC5B,KAAKjC,WAAA,CAAYqB,GAAG,EAAEc,IAAA,CAAKT,aAAa;EAC5C;EAAA;AAAA;AAAA;AAAA;EAMAU,oBAAoBV,aAAA,EACpB;IACI,KAAKQ,aAAA,CAAcR,aAAa;EACpC;EAAA;AAAA;AAAA;AAAA;EAMAW,MAAMC,eAAA,EACN;IACI,IAAAA,eAAA,GAAkBA,eAAA,KAAoB,IAClCA,eAAA,EAEW,WAAAC,CAAA,IAAK,KAAKvC,WAAA,EACrB;MACU,MAAAwC,QAAA,GAAW,KAAKxC,WAAA,CAAYuC,CAAC;MAE/B,IAAAC,QAAA,EAEA,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAID,QAAA,CAASE,MAAA,EAAQD,CAAA,IAExBD,QAAA,CAAAC,CAAC,EAAEE,OAAA,CAAQ,EAAI;IAGpC;IAGJ,KAAK3C,WAAA,GAAc;EACvB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASA4C,cAAcC,IAAA,EACd;IACI,IAAI,EAAAA,IAAA,CAAK/B,KAAA,KAAU,KAAKZ,YAAA,IACjB2C,IAAA,CAAK9B,MAAA,KAAW,KAAKZ,aAAA,GAK5B;MAAA,KAAKF,gBAAA,GAAmB4C,IAAA,CAAK/B,KAAA,GAAQ,KAAK+B,IAAA,CAAK9B,MAAA,GAAS;MAE7C,WAAAwB,CAAA,IAAK,KAAKvC,WAAA,EACrB;QACQ,MAAE8C,MAAA,CAAOP,CAAC,IAAI,IAEd;QAGE,MAAAC,QAAA,GAAW,KAAKxC,WAAA,CAAYuC,CAAC;QAE/B,IAAAC,QAAA,EAEA,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAID,QAAA,CAASE,MAAA,EAAQD,CAAA,IAExBD,QAAA,CAAAC,CAAC,EAAEE,OAAA,CAAQ,EAAI;QAI3B,KAAA3C,WAAA,CAAYuC,CAAC,IAAI;MAC1B;MAEA,KAAKrC,YAAA,GAAe2C,IAAA,CAAK/B,KAAA,EACzB,KAAKX,aAAA,GAAgB0C,IAAA,CAAK9B,MAAA;IAAA;EAC9B;AAOJ;AAhNalB,iBAAA,CA+MFkD,UAAA,GAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}