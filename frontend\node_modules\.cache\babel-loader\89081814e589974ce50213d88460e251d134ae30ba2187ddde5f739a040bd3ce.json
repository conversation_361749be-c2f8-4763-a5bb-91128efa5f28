{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Projects\\\\Python\\\\EU4\\\\frontend\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport axios from \"axios\";\nimport WorldMap from './WorldMap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst INITIAL_STATS = {\n  population: 1000000,\n  resources: 100,\n  stability: 100\n};\nconst WIN_RESOURCES = 1000;\nconst LOSE_STABILITY = 0;\nexport default function App() {\n  _s();\n  var _playerCountry$proper, _playerCountry$proper2;\n  const [countries, setCountries] = useState([]);\n  const [playerCountry, setPlayerCountry] = useState(null);\n  const [countryStats, setCountryStats] = useState({});\n  const [turn, setTurn] = useState(1);\n  const [message, setMessage] = useState(\"\");\n  const [actionTaken, setActionTaken] = useState(false);\n\n  // Load countries on mount\n  useEffect(() => {\n    axios.get(\"http://localhost:8000/countries\").then(r => r.data).then(data => {\n      console.log(\"Fetched countries:\", data); // DEBUG LOG\n      setCountries(data);\n      // Initialize stats for all countries with a valid NAME\n      const stats = {};\n      data.forEach(c => {\n        var _c$properties;\n        const name = (_c$properties = c.properties) === null || _c$properties === void 0 ? void 0 : _c$properties.NAME;\n        if (name) {\n          stats[name] = {\n            ...INITIAL_STATS\n          };\n        }\n      });\n      setCountryStats(stats);\n    });\n  }, []);\n\n  // Player picks a country at start\n  if (!countries.length || !Object.keys(countryStats).length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading world data...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 12\n    }, this);\n  }\n  if (!playerCountry || !((_playerCountry$proper = playerCountry.properties) !== null && _playerCountry$proper !== void 0 && _playerCountry$proper.NAME)) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Choose Your Country\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(WorldMap, {\n        onSelectCountry: c => {\n          var _c$properties2;\n          return (c === null || c === void 0 ? void 0 : (_c$properties2 = c.properties) === null || _c$properties2 === void 0 ? void 0 : _c$properties2.NAME) && setPlayerCountry(c);\n        },\n        selectedCountry: null\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Click a country to play as it.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this);\n  }\n  const stats = countryStats[playerCountry.properties.NAME];\n  if (!stats) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading country stats...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Player actions\n  const invest = () => {\n    if (actionTaken) return;\n    setCountryStats(stats => ({\n      ...stats,\n      [playerCountry.properties.NAME]: {\n        ...stats[playerCountry.properties.NAME],\n        resources: stats[playerCountry.properties.NAME].resources + 50,\n        stability: stats[playerCountry.properties.NAME].stability - 2\n      }\n    }));\n    setMessage(\"You invested in your economy! Resources +50, Stability -2\");\n    setActionTaken(true);\n  };\n  const build = () => {\n    if (actionTaken) return;\n    setCountryStats(stats => ({\n      ...stats,\n      [playerCountry.properties.NAME]: {\n        ...stats[playerCountry.properties.NAME],\n        population: Math.floor(stats[playerCountry.properties.NAME].population * 1.02),\n        resources: stats[playerCountry.properties.NAME].resources - 20,\n        stability: stats[playerCountry.properties.NAME].stability + 1\n      }\n    }));\n    setMessage(\"You built infrastructure! Population +2%, Resources -20, Stability +1\");\n    setActionTaken(true);\n  };\n  const propaganda = () => {\n    if (actionTaken) return;\n    setCountryStats(stats => ({\n      ...stats,\n      [playerCountry.properties.NAME]: {\n        ...stats[playerCountry.properties.NAME],\n        stability: stats[playerCountry.properties.NAME].stability + 10,\n        resources: stats[playerCountry.properties.NAME].resources - 10\n      }\n    }));\n    setMessage(\"You ran propaganda! Stability +10, Resources -10\");\n    setActionTaken(true);\n  };\n\n  // End turn: AI and player stats update\n  const nextTurn = () => {\n    setTurn(t => t + 1);\n    setActionTaken(false);\n    setMessage(\"\");\n    setCountryStats(stats => {\n      const newStats = {\n        ...stats\n      };\n      Object.keys(newStats).forEach(name => {\n        // AI: grow pop/resources, random stability change\n        if (name !== playerCountry.properties.NAME) {\n          newStats[name] = {\n            ...newStats[name],\n            population: Math.floor(newStats[name].population * (1.01 + Math.random() * 0.01)),\n            resources: newStats[name].resources + Math.floor(Math.random() * 20),\n            stability: Math.max(0, Math.min(100, newStats[name].stability + Math.floor(Math.random() * 5 - 2)))\n          };\n        } else {\n          // Player: small passive growth\n          newStats[name] = {\n            ...newStats[name],\n            population: Math.floor(newStats[name].population * 1.01),\n            resources: newStats[name].resources + 5,\n            stability: Math.max(0, Math.min(100, newStats[name].stability))\n          };\n        }\n      });\n      return newStats;\n    });\n  };\n\n  // Win/Lose conditions\n  if (stats.resources >= WIN_RESOURCES) {\n    return /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Victory! You built a prosperous nation.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 12\n    }, this);\n  }\n  if (stats.stability <= LOSE_STABILITY) {\n    return /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Your country collapsed due to instability. Game Over.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Empires & Revolutions\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [\"Turn: \", turn]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(WorldMap, {\n      onSelectCountry: () => {},\n      selectedCountry: playerCountry\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#222',\n        color: '#fff',\n        padding: 10,\n        margin: 10,\n        borderRadius: 8\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: (_playerCountry$proper2 = playerCountry.properties) === null || _playerCountry$proper2 === void 0 ? void 0 : _playerCountry$proper2.NAME\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Population: \", stats.population.toLocaleString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Resources: \", stats.resources]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Stability: \", stats.stability]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: invest,\n        disabled: actionTaken,\n        children: \"Invest\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: build,\n        disabled: actionTaken,\n        style: {\n          marginLeft: 8\n        },\n        children: \"Build\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: propaganda,\n        disabled: actionTaken,\n        style: {\n          marginLeft: 8\n        },\n        children: \"Propaganda\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: nextTurn,\n      children: \"End Turn\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        color: 'yellow',\n        margin: 10\n      },\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 19\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"WYr2gpaVacVC02uE2zJqp5JYKKI=\");\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "WorldMap", "jsxDEV", "_jsxDEV", "INITIAL_STATS", "population", "resources", "stability", "WIN_RESOURCES", "LOSE_STABILITY", "App", "_s", "_playerCountry$proper", "_playerCountry$proper2", "countries", "setCountries", "playerCountry", "setPlayerCountry", "countryStats", "setCountryStats", "turn", "setTurn", "message", "setMessage", "actionTaken", "setActionTaken", "get", "then", "r", "data", "console", "log", "stats", "for<PERSON>ach", "c", "_c$properties", "name", "properties", "NAME", "length", "Object", "keys", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSelectCountry", "_c$properties2", "selectedCountry", "invest", "build", "Math", "floor", "propaganda", "nextTurn", "t", "newStats", "random", "max", "min", "style", "background", "color", "padding", "margin", "borderRadius", "toLocaleString", "onClick", "disabled", "marginLeft", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Projects/Python/EU4/frontend/src/App.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport axios from \"axios\";\r\nimport WorldMap from './WorldMap';\r\n\r\nconst INITIAL_STATS = { population: 1000000, resources: 100, stability: 100 };\r\nconst WIN_RESOURCES = 1000;\r\nconst LOSE_STABILITY = 0;\r\n\r\nexport default function App() {\r\n  const [countries, setCountries] = useState([]);\r\n  const [playerCountry, setPlayerCountry] = useState(null);\r\n  const [countryStats, setCountryStats] = useState({});\r\n  const [turn, setTurn] = useState(1);\r\n  const [message, setMessage] = useState(\"\");\r\n  const [actionTaken, setActionTaken] = useState(false);\r\n\r\n  // Load countries on mount\r\n  useEffect(() => {\r\n    axios.get(\"http://localhost:8000/countries\")\r\n      .then(r => r.data)\r\n      .then(data => {\r\n        console.log(\"Fetched countries:\", data); // DEBUG LOG\r\n        setCountries(data);\r\n        // Initialize stats for all countries with a valid NAME\r\n        const stats = {};\r\n        data.forEach(c => {\r\n          const name = c.properties?.NAME;\r\n          if (name) {\r\n            stats[name] = { ...INITIAL_STATS };\r\n          }\r\n        });\r\n        setCountryStats(stats);\r\n      });\r\n  }, []);\r\n\r\n  // Player picks a country at start\r\n  if (!countries.length || !Object.keys(countryStats).length) {\r\n    return <div>Loading world data...</div>;\r\n  }\r\n  if (!playerCountry || !playerCountry.properties?.NAME) {\r\n    return (\r\n      <div>\r\n        <h1>Choose Your Country</h1>\r\n        <WorldMap onSelectCountry={c => c?.properties?.NAME && setPlayerCountry(c)} selectedCountry={null} />\r\n        <p>Click a country to play as it.</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const stats = countryStats[playerCountry.properties.NAME];\r\n  if (!stats) {\r\n    return <div>Loading country stats...</div>;\r\n  }\r\n\r\n  // Player actions\r\n  const invest = () => {\r\n    if (actionTaken) return;\r\n    setCountryStats(stats => ({\r\n      ...stats,\r\n      [playerCountry.properties.NAME]: {\r\n        ...stats[playerCountry.properties.NAME],\r\n        resources: stats[playerCountry.properties.NAME].resources + 50,\r\n        stability: stats[playerCountry.properties.NAME].stability - 2\r\n      }\r\n    }));\r\n    setMessage(\"You invested in your economy! Resources +50, Stability -2\");\r\n    setActionTaken(true);\r\n  };\r\n  const build = () => {\r\n    if (actionTaken) return;\r\n    setCountryStats(stats => ({\r\n      ...stats,\r\n      [playerCountry.properties.NAME]: {\r\n        ...stats[playerCountry.properties.NAME],\r\n        population: Math.floor(stats[playerCountry.properties.NAME].population * 1.02),\r\n        resources: stats[playerCountry.properties.NAME].resources - 20,\r\n        stability: stats[playerCountry.properties.NAME].stability + 1\r\n      }\r\n    }));\r\n    setMessage(\"You built infrastructure! Population +2%, Resources -20, Stability +1\");\r\n    setActionTaken(true);\r\n  };\r\n  const propaganda = () => {\r\n    if (actionTaken) return;\r\n    setCountryStats(stats => ({\r\n      ...stats,\r\n      [playerCountry.properties.NAME]: {\r\n        ...stats[playerCountry.properties.NAME],\r\n        stability: stats[playerCountry.properties.NAME].stability + 10,\r\n        resources: stats[playerCountry.properties.NAME].resources - 10\r\n      }\r\n    }));\r\n    setMessage(\"You ran propaganda! Stability +10, Resources -10\");\r\n    setActionTaken(true);\r\n  };\r\n\r\n  // End turn: AI and player stats update\r\n  const nextTurn = () => {\r\n    setTurn(t => t + 1);\r\n    setActionTaken(false);\r\n    setMessage(\"\");\r\n    setCountryStats(stats => {\r\n      const newStats = { ...stats };\r\n      Object.keys(newStats).forEach(name => {\r\n        // AI: grow pop/resources, random stability change\r\n        if (name !== playerCountry.properties.NAME) {\r\n          newStats[name] = {\r\n            ...newStats[name],\r\n            population: Math.floor(newStats[name].population * (1.01 + Math.random() * 0.01)),\r\n            resources: newStats[name].resources + Math.floor(Math.random() * 20),\r\n            stability: Math.max(0, Math.min(100, newStats[name].stability + Math.floor(Math.random() * 5 - 2)))\r\n          };\r\n        } else {\r\n          // Player: small passive growth\r\n          newStats[name] = {\r\n            ...newStats[name],\r\n            population: Math.floor(newStats[name].population * 1.01),\r\n            resources: newStats[name].resources + 5,\r\n            stability: Math.max(0, Math.min(100, newStats[name].stability))\r\n          };\r\n        }\r\n      });\r\n      return newStats;\r\n    });\r\n  };\r\n\r\n  // Win/Lose conditions\r\n  if (stats.resources >= WIN_RESOURCES) {\r\n    return <h1>Victory! You built a prosperous nation.</h1>;\r\n  }\r\n  if (stats.stability <= LOSE_STABILITY) {\r\n    return <h1>Your country collapsed due to instability. Game Over.</h1>;\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <h1>Empires & Revolutions</h1>\r\n      <div>Turn: {turn}</div>\r\n      <WorldMap onSelectCountry={() => {}} selectedCountry={playerCountry} />\r\n      <div style={{ background: '#222', color: '#fff', padding: 10, margin: 10, borderRadius: 8 }}>\r\n        <h2>{playerCountry.properties?.NAME}</h2>\r\n        <p>Population: {stats.population.toLocaleString()}</p>\r\n        <p>Resources: {stats.resources}</p>\r\n        <p>Stability: {stats.stability}</p>\r\n        <button onClick={invest} disabled={actionTaken}>Invest</button>\r\n        <button onClick={build} disabled={actionTaken} style={{marginLeft: 8}}>Build</button>\r\n        <button onClick={propaganda} disabled={actionTaken} style={{marginLeft: 8}}>Propaganda</button>\r\n      </div>\r\n      <button onClick={nextTurn}>End Turn</button>\r\n      {message && <div style={{ color: 'yellow', margin: 10 }}>{message}</div>}\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,aAAa,GAAG;EAAEC,UAAU,EAAE,OAAO;EAAEC,SAAS,EAAE,GAAG;EAAEC,SAAS,EAAE;AAAI,CAAC;AAC7E,MAAMC,aAAa,GAAG,IAAI;AAC1B,MAAMC,cAAc,GAAG,CAAC;AAExB,eAAe,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC5B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACqB,IAAI,EAAEC,OAAO,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACAD,SAAS,CAAC,MAAM;IACdE,KAAK,CAAC0B,GAAG,CAAC,iCAAiC,CAAC,CACzCC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CACjBF,IAAI,CAACE,IAAI,IAAI;MACZC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,IAAI,CAAC,CAAC,CAAC;MACzCd,YAAY,CAACc,IAAI,CAAC;MAClB;MACA,MAAMG,KAAK,GAAG,CAAC,CAAC;MAChBH,IAAI,CAACI,OAAO,CAACC,CAAC,IAAI;QAAA,IAAAC,aAAA;QAChB,MAAMC,IAAI,IAAAD,aAAA,GAAGD,CAAC,CAACG,UAAU,cAAAF,aAAA,uBAAZA,aAAA,CAAcG,IAAI;QAC/B,IAAIF,IAAI,EAAE;UACRJ,KAAK,CAACI,IAAI,CAAC,GAAG;YAAE,GAAGhC;UAAc,CAAC;QACpC;MACF,CAAC,CAAC;MACFe,eAAe,CAACa,KAAK,CAAC;IACxB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAI,CAAClB,SAAS,CAACyB,MAAM,IAAI,CAACC,MAAM,CAACC,IAAI,CAACvB,YAAY,CAAC,CAACqB,MAAM,EAAE;IAC1D,oBAAOpC,OAAA;MAAAuC,QAAA,EAAK;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACzC;EACA,IAAI,CAAC9B,aAAa,IAAI,GAAAJ,qBAAA,GAACI,aAAa,CAACqB,UAAU,cAAAzB,qBAAA,eAAxBA,qBAAA,CAA0B0B,IAAI,GAAE;IACrD,oBACEnC,OAAA;MAAAuC,QAAA,gBACEvC,OAAA;QAAAuC,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5B3C,OAAA,CAACF,QAAQ;QAAC8C,eAAe,EAAEb,CAAC;UAAA,IAAAc,cAAA;UAAA,OAAI,CAAAd,CAAC,aAADA,CAAC,wBAAAc,cAAA,GAADd,CAAC,CAAEG,UAAU,cAAAW,cAAA,uBAAbA,cAAA,CAAeV,IAAI,KAAIrB,gBAAgB,CAACiB,CAAC,CAAC;QAAA,CAAC;QAACe,eAAe,EAAE;MAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrG3C,OAAA;QAAAuC,QAAA,EAAG;MAA8B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAEV;EAEA,MAAMd,KAAK,GAAGd,YAAY,CAACF,aAAa,CAACqB,UAAU,CAACC,IAAI,CAAC;EACzD,IAAI,CAACN,KAAK,EAAE;IACV,oBAAO7B,OAAA;MAAAuC,QAAA,EAAK;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC5C;;EAEA;EACA,MAAMI,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAI1B,WAAW,EAAE;IACjBL,eAAe,CAACa,KAAK,KAAK;MACxB,GAAGA,KAAK;MACR,CAAChB,aAAa,CAACqB,UAAU,CAACC,IAAI,GAAG;QAC/B,GAAGN,KAAK,CAAChB,aAAa,CAACqB,UAAU,CAACC,IAAI,CAAC;QACvChC,SAAS,EAAE0B,KAAK,CAAChB,aAAa,CAACqB,UAAU,CAACC,IAAI,CAAC,CAAChC,SAAS,GAAG,EAAE;QAC9DC,SAAS,EAAEyB,KAAK,CAAChB,aAAa,CAACqB,UAAU,CAACC,IAAI,CAAC,CAAC/B,SAAS,GAAG;MAC9D;IACF,CAAC,CAAC,CAAC;IACHgB,UAAU,CAAC,2DAA2D,CAAC;IACvEE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAM0B,KAAK,GAAGA,CAAA,KAAM;IAClB,IAAI3B,WAAW,EAAE;IACjBL,eAAe,CAACa,KAAK,KAAK;MACxB,GAAGA,KAAK;MACR,CAAChB,aAAa,CAACqB,UAAU,CAACC,IAAI,GAAG;QAC/B,GAAGN,KAAK,CAAChB,aAAa,CAACqB,UAAU,CAACC,IAAI,CAAC;QACvCjC,UAAU,EAAE+C,IAAI,CAACC,KAAK,CAACrB,KAAK,CAAChB,aAAa,CAACqB,UAAU,CAACC,IAAI,CAAC,CAACjC,UAAU,GAAG,IAAI,CAAC;QAC9EC,SAAS,EAAE0B,KAAK,CAAChB,aAAa,CAACqB,UAAU,CAACC,IAAI,CAAC,CAAChC,SAAS,GAAG,EAAE;QAC9DC,SAAS,EAAEyB,KAAK,CAAChB,aAAa,CAACqB,UAAU,CAACC,IAAI,CAAC,CAAC/B,SAAS,GAAG;MAC9D;IACF,CAAC,CAAC,CAAC;IACHgB,UAAU,CAAC,uEAAuE,CAAC;IACnFE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAM6B,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI9B,WAAW,EAAE;IACjBL,eAAe,CAACa,KAAK,KAAK;MACxB,GAAGA,KAAK;MACR,CAAChB,aAAa,CAACqB,UAAU,CAACC,IAAI,GAAG;QAC/B,GAAGN,KAAK,CAAChB,aAAa,CAACqB,UAAU,CAACC,IAAI,CAAC;QACvC/B,SAAS,EAAEyB,KAAK,CAAChB,aAAa,CAACqB,UAAU,CAACC,IAAI,CAAC,CAAC/B,SAAS,GAAG,EAAE;QAC9DD,SAAS,EAAE0B,KAAK,CAAChB,aAAa,CAACqB,UAAU,CAACC,IAAI,CAAC,CAAChC,SAAS,GAAG;MAC9D;IACF,CAAC,CAAC,CAAC;IACHiB,UAAU,CAAC,kDAAkD,CAAC;IAC9DE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAM8B,QAAQ,GAAGA,CAAA,KAAM;IACrBlC,OAAO,CAACmC,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC;IACnB/B,cAAc,CAAC,KAAK,CAAC;IACrBF,UAAU,CAAC,EAAE,CAAC;IACdJ,eAAe,CAACa,KAAK,IAAI;MACvB,MAAMyB,QAAQ,GAAG;QAAE,GAAGzB;MAAM,CAAC;MAC7BQ,MAAM,CAACC,IAAI,CAACgB,QAAQ,CAAC,CAACxB,OAAO,CAACG,IAAI,IAAI;QACpC;QACA,IAAIA,IAAI,KAAKpB,aAAa,CAACqB,UAAU,CAACC,IAAI,EAAE;UAC1CmB,QAAQ,CAACrB,IAAI,CAAC,GAAG;YACf,GAAGqB,QAAQ,CAACrB,IAAI,CAAC;YACjB/B,UAAU,EAAE+C,IAAI,CAACC,KAAK,CAACI,QAAQ,CAACrB,IAAI,CAAC,CAAC/B,UAAU,IAAI,IAAI,GAAG+C,IAAI,CAACM,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YACjFpD,SAAS,EAAEmD,QAAQ,CAACrB,IAAI,CAAC,CAAC9B,SAAS,GAAG8C,IAAI,CAACC,KAAK,CAACD,IAAI,CAACM,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;YACpEnD,SAAS,EAAE6C,IAAI,CAACO,GAAG,CAAC,CAAC,EAAEP,IAAI,CAACQ,GAAG,CAAC,GAAG,EAAEH,QAAQ,CAACrB,IAAI,CAAC,CAAC7B,SAAS,GAAG6C,IAAI,CAACC,KAAK,CAACD,IAAI,CAACM,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;UACpG,CAAC;QACH,CAAC,MAAM;UACL;UACAD,QAAQ,CAACrB,IAAI,CAAC,GAAG;YACf,GAAGqB,QAAQ,CAACrB,IAAI,CAAC;YACjB/B,UAAU,EAAE+C,IAAI,CAACC,KAAK,CAACI,QAAQ,CAACrB,IAAI,CAAC,CAAC/B,UAAU,GAAG,IAAI,CAAC;YACxDC,SAAS,EAAEmD,QAAQ,CAACrB,IAAI,CAAC,CAAC9B,SAAS,GAAG,CAAC;YACvCC,SAAS,EAAE6C,IAAI,CAACO,GAAG,CAAC,CAAC,EAAEP,IAAI,CAACQ,GAAG,CAAC,GAAG,EAAEH,QAAQ,CAACrB,IAAI,CAAC,CAAC7B,SAAS,CAAC;UAChE,CAAC;QACH;MACF,CAAC,CAAC;MACF,OAAOkD,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAIzB,KAAK,CAAC1B,SAAS,IAAIE,aAAa,EAAE;IACpC,oBAAOL,OAAA;MAAAuC,QAAA,EAAI;IAAuC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EACzD;EACA,IAAId,KAAK,CAACzB,SAAS,IAAIE,cAAc,EAAE;IACrC,oBAAON,OAAA;MAAAuC,QAAA,EAAI;IAAqD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EACvE;EAEA,oBACE3C,OAAA;IAAAuC,QAAA,gBACEvC,OAAA;MAAAuC,QAAA,EAAI;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC9B3C,OAAA;MAAAuC,QAAA,GAAK,QAAM,EAACtB,IAAI;IAAA;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACvB3C,OAAA,CAACF,QAAQ;MAAC8C,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAE;MAACE,eAAe,EAAEjC;IAAc;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACvE3C,OAAA;MAAK0D,KAAK,EAAE;QAAEC,UAAU,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,YAAY,EAAE;MAAE,CAAE;MAAAxB,QAAA,gBAC1FvC,OAAA;QAAAuC,QAAA,GAAA7B,sBAAA,GAAKG,aAAa,CAACqB,UAAU,cAAAxB,sBAAA,uBAAxBA,sBAAA,CAA0ByB;MAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACzC3C,OAAA;QAAAuC,QAAA,GAAG,cAAY,EAACV,KAAK,CAAC3B,UAAU,CAAC8D,cAAc,CAAC,CAAC;MAAA;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtD3C,OAAA;QAAAuC,QAAA,GAAG,aAAW,EAACV,KAAK,CAAC1B,SAAS;MAAA;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3C,OAAA;QAAAuC,QAAA,GAAG,aAAW,EAACV,KAAK,CAACzB,SAAS;MAAA;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC3C,OAAA;QAAQiE,OAAO,EAAElB,MAAO;QAACmB,QAAQ,EAAE7C,WAAY;QAAAkB,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC/D3C,OAAA;QAAQiE,OAAO,EAAEjB,KAAM;QAACkB,QAAQ,EAAE7C,WAAY;QAACqC,KAAK,EAAE;UAACS,UAAU,EAAE;QAAC,CAAE;QAAA5B,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACrF3C,OAAA;QAAQiE,OAAO,EAAEd,UAAW;QAACe,QAAQ,EAAE7C,WAAY;QAACqC,KAAK,EAAE;UAACS,UAAU,EAAE;QAAC,CAAE;QAAA5B,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,eACN3C,OAAA;MAAQiE,OAAO,EAAEb,QAAS;MAAAb,QAAA,EAAC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAC3CxB,OAAO,iBAAInB,OAAA;MAAK0D,KAAK,EAAE;QAAEE,KAAK,EAAE,QAAQ;QAAEE,MAAM,EAAE;MAAG,CAAE;MAAAvB,QAAA,EAAEpB;IAAO;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrE,CAAC;AAEV;AAACnC,EAAA,CAhJuBD,GAAG;AAAA6D,EAAA,GAAH7D,GAAG;AAAA,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}