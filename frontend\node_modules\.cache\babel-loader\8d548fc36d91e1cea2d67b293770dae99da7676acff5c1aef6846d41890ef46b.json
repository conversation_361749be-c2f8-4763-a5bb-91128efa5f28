{"ast": null, "code": "class ObservablePoint {\n  /**\n   * Creates a new `ObservablePoint`\n   * @param cb - callback function triggered when `x` and/or `y` are changed\n   * @param scope - owner of callback\n   * @param {number} [x=0] - position of the point on the x axis\n   * @param {number} [y=0] - position of the point on the y axis\n   */\n  constructor(cb, scope, x = 0, y = 0) {\n    this._x = x, this._y = y, this.cb = cb, this.scope = scope;\n  }\n  /**\n   * Creates a clone of this point.\n   * The callback and scope params can be overridden otherwise they will default\n   * to the clone object's values.\n   * @override\n   * @param cb - The callback function triggered when `x` and/or `y` are changed\n   * @param scope - The owner of the callback\n   * @returns a copy of this observable point\n   */\n  clone(cb = this.cb, scope = this.scope) {\n    return new ObservablePoint(cb, scope, this._x, this._y);\n  }\n  /**\n   * Sets the point to a new `x` and `y` position.\n   * If `y` is omitted, both `x` and `y` will be set to `x`.\n   * @param {number} [x=0] - position of the point on the x axis\n   * @param {number} [y=x] - position of the point on the y axis\n   * @returns The observable point instance itself\n   */\n  set(x = 0, y = x) {\n    return (this._x !== x || this._y !== y) && (this._x = x, this._y = y, this.cb.call(this.scope)), this;\n  }\n  /**\n   * Copies x and y from the given point (`p`)\n   * @param p - The point to copy from. Can be any of type that is or extends `IPointData`\n   * @returns The observable point instance itself\n   */\n  copyFrom(p) {\n    return (this._x !== p.x || this._y !== p.y) && (this._x = p.x, this._y = p.y, this.cb.call(this.scope)), this;\n  }\n  /**\n   * Copies this point's x and y into that of the given point (`p`)\n   * @param p - The point to copy to. Can be any of type that is or extends `IPointData`\n   * @returns The point (`p`) with values updated\n   */\n  copyTo(p) {\n    return p.set(this._x, this._y), p;\n  }\n  /**\n   * Accepts another point (`p`) and returns `true` if the given point is equal to this point\n   * @param p - The point to check\n   * @returns Returns `true` if both `x` and `y` are equal\n   */\n  equals(p) {\n    return p.x === this._x && p.y === this._y;\n  }\n  /** Position of the observable point on the x axis. */\n  get x() {\n    return this._x;\n  }\n  set x(value) {\n    this._x !== value && (this._x = value, this.cb.call(this.scope));\n  }\n  /** Position of the observable point on the y axis. */\n  get y() {\n    return this._y;\n  }\n  set y(value) {\n    this._y !== value && (this._y = value, this.cb.call(this.scope));\n  }\n}\nObservablePoint.prototype.toString = function () {\n  return `[@pixi/math:ObservablePoint x=${this.x} y=${this.y} scope=${this.scope}]`;\n};\nexport { ObservablePoint };", "map": {"version": 3, "names": ["ObservablePoint", "constructor", "cb", "scope", "x", "y", "_x", "_y", "clone", "set", "call", "copyFrom", "p", "copyTo", "equals", "value", "prototype", "toString"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\math\\src\\ObservablePoint.ts"], "sourcesContent": ["import type { IPoint } from './IPoint';\nimport type { IPointData } from './IPointData';\n\nexport interface ObservablePoint extends GlobalMixins.Point, IPoint {}\n\n/**\n * The ObservablePoint object represents a location in a two-dimensional coordinate system, where `x` represents\n * the position on the horizontal axis and `y` represents the position on the vertical axis.\n *\n * An `ObservablePoint` is a point that triggers a callback when the point's position is changed.\n * @memberof PIXI\n */\nexport class ObservablePoint<T = any> implements IPoint\n{\n    /** The callback function triggered when `x` and/or `y` are changed */\n    public cb: (this: T) => any;\n\n    /** The owner of the callback */\n    public scope: any;\n\n    _x: number;\n    _y: number;\n\n    /**\n     * Creates a new `ObservablePoint`\n     * @param cb - callback function triggered when `x` and/or `y` are changed\n     * @param scope - owner of callback\n     * @param {number} [x=0] - position of the point on the x axis\n     * @param {number} [y=0] - position of the point on the y axis\n     */\n    constructor(cb: (this: T) => any, scope: T, x = 0, y = 0)\n    {\n        this._x = x;\n        this._y = y;\n\n        this.cb = cb;\n        this.scope = scope;\n    }\n\n    /**\n     * Creates a clone of this point.\n     * The callback and scope params can be overridden otherwise they will default\n     * to the clone object's values.\n     * @override\n     * @param cb - The callback function triggered when `x` and/or `y` are changed\n     * @param scope - The owner of the callback\n     * @returns a copy of this observable point\n     */\n    clone(cb = this.cb, scope = this.scope): ObservablePoint\n    {\n        return new ObservablePoint(cb, scope, this._x, this._y);\n    }\n\n    /**\n     * Sets the point to a new `x` and `y` position.\n     * If `y` is omitted, both `x` and `y` will be set to `x`.\n     * @param {number} [x=0] - position of the point on the x axis\n     * @param {number} [y=x] - position of the point on the y axis\n     * @returns The observable point instance itself\n     */\n    set(x = 0, y = x): this\n    {\n        if (this._x !== x || this._y !== y)\n        {\n            this._x = x;\n            this._y = y;\n            this.cb.call(this.scope);\n        }\n\n        return this;\n    }\n\n    /**\n     * Copies x and y from the given point (`p`)\n     * @param p - The point to copy from. Can be any of type that is or extends `IPointData`\n     * @returns The observable point instance itself\n     */\n    copyFrom(p: IPointData): this\n    {\n        if (this._x !== p.x || this._y !== p.y)\n        {\n            this._x = p.x;\n            this._y = p.y;\n            this.cb.call(this.scope);\n        }\n\n        return this;\n    }\n\n    /**\n     * Copies this point's x and y into that of the given point (`p`)\n     * @param p - The point to copy to. Can be any of type that is or extends `IPointData`\n     * @returns The point (`p`) with values updated\n     */\n    copyTo<T extends IPoint>(p: T): T\n    {\n        p.set(this._x, this._y);\n\n        return p;\n    }\n\n    /**\n     * Accepts another point (`p`) and returns `true` if the given point is equal to this point\n     * @param p - The point to check\n     * @returns Returns `true` if both `x` and `y` are equal\n     */\n    equals(p: IPointData): boolean\n    {\n        return (p.x === this._x) && (p.y === this._y);\n    }\n\n    /** Position of the observable point on the x axis. */\n    get x(): number\n    {\n        return this._x;\n    }\n\n    set x(value: number)\n    {\n        if (this._x !== value)\n        {\n            this._x = value;\n            this.cb.call(this.scope);\n        }\n    }\n\n    /** Position of the observable point on the y axis. */\n    get y(): number\n    {\n        return this._y;\n    }\n\n    set y(value: number)\n    {\n        if (this._y !== value)\n        {\n            this._y = value;\n            this.cb.call(this.scope);\n        }\n    }\n}\n\nif (process.env.DEBUG)\n{\n    ObservablePoint.prototype.toString = function toString(): string\n    {\n        return `[@pixi/math:ObservablePoint x=${this.x} y=${this.y} scope=${this.scope}]`;\n    };\n}\n"], "mappings": "AAYO,MAAMA,eAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAiBIC,YAAYC,EAAA,EAAsBC,KAAA,EAAUC,CAAA,GAAI,GAAGC,CAAA,GAAI,GACvD;IACS,KAAAC,EAAA,GAAKF,CAAA,EACV,KAAKG,EAAA,GAAKF,CAAA,EAEV,KAAKH,EAAA,GAAKA,EAAA,EACV,KAAKC,KAAA,GAAQA,KAAA;EACjB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWAK,MAAMN,EAAA,GAAK,KAAKA,EAAA,EAAIC,KAAA,GAAQ,KAAKA,KAAA,EACjC;IACI,OAAO,IAAIH,eAAA,CAAgBE,EAAA,EAAIC,KAAA,EAAO,KAAKG,EAAA,EAAI,KAAKC,EAAE;EAC1D;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAE,IAAIL,CAAA,GAAI,GAAGC,CAAA,GAAID,CAAA,EACf;IACI,QAAI,KAAKE,EAAA,KAAOF,CAAA,IAAK,KAAKG,EAAA,KAAOF,CAAA,MAE7B,KAAKC,EAAA,GAAKF,CAAA,EACV,KAAKG,EAAA,GAAKF,CAAA,EACV,KAAKH,EAAA,CAAGQ,IAAA,CAAK,KAAKP,KAAK,IAGpB;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAQ,SAASC,CAAA,EACT;IACQ,aAAKN,EAAA,KAAOM,CAAA,CAAER,CAAA,IAAK,KAAKG,EAAA,KAAOK,CAAA,CAAEP,CAAA,MAEjC,KAAKC,EAAA,GAAKM,CAAA,CAAER,CAAA,EACZ,KAAKG,EAAA,GAAKK,CAAA,CAAEP,CAAA,EACZ,KAAKH,EAAA,CAAGQ,IAAA,CAAK,KAAKP,KAAK,IAGpB;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAU,OAAyBD,CAAA,EACzB;IACI,OAAAA,CAAA,CAAEH,GAAA,CAAI,KAAKH,EAAA,EAAI,KAAKC,EAAE,GAEfK,CAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAE,OAAOF,CAAA,EACP;IACI,OAAQA,CAAA,CAAER,CAAA,KAAM,KAAKE,EAAA,IAAQM,CAAA,CAAEP,CAAA,KAAM,KAAKE,EAAA;EAC9C;EAAA;EAGA,IAAIH,EAAA,EACJ;IACI,OAAO,KAAKE,EAAA;EAChB;EAEA,IAAIF,EAAEW,KAAA,EACN;IACQ,KAAKT,EAAA,KAAOS,KAAA,KAEZ,KAAKT,EAAA,GAAKS,KAAA,EACV,KAAKb,EAAA,CAAGQ,IAAA,CAAK,KAAKP,KAAK;EAE/B;EAAA;EAGA,IAAIE,EAAA,EACJ;IACI,OAAO,KAAKE,EAAA;EAChB;EAEA,IAAIF,EAAEU,KAAA,EACN;IACQ,KAAKR,EAAA,KAAOQ,KAAA,KAEZ,KAAKR,EAAA,GAAKQ,KAAA,EACV,KAAKb,EAAA,CAAGQ,IAAA,CAAK,KAAKP,KAAK;EAE/B;AACJ;AAIIH,eAAA,CAAgBgB,SAAA,CAAUC,QAAA,GAAW,YACrC;EACW,wCAAiC,KAAKb,CAAC,MAAM,KAAKC,CAAC,UAAU,KAAKF,KAAK;AAClF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}