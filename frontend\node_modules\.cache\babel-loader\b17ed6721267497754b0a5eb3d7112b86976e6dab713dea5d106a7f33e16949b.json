{"ast": null, "code": "import { MASK_TYPES } from \"@pixi/constants\";\nimport { ExtensionType, extensions } from \"@pixi/extensions\";\nimport { SpriteMaskFilter } from \"../filters/spriteMask/SpriteMaskFilter.mjs\";\nimport { MaskData } from \"./MaskData.mjs\";\nclass MaskSystem {\n  /**\n   * @param renderer - The renderer this System works for.\n   */\n  constructor(renderer) {\n    this.renderer = renderer, this.enableScissor = !0, this.alphaMaskPool = [], this.maskDataPool = [], this.maskStack = [], this.alphaMaskIndex = 0;\n  }\n  /**\n   * Changes the mask stack that is used by this System.\n   * @param maskStack - The mask stack\n   */\n  setMaskStack(maskStack) {\n    this.maskStack = maskStack, this.renderer.scissor.setMaskStack(maskStack), this.renderer.stencil.setMaskStack(maskStack);\n  }\n  /**\n   * Enables the mask and appends it to the current mask stack.\n   *\n   * NOTE: The batch renderer should be flushed beforehand to prevent pending renders from being masked.\n   * @param {PIXI.DisplayObject} target - Display Object to push the mask to\n   * @param {PIXI.MaskData|PIXI.Sprite|PIXI.Graphics|PIXI.DisplayObject} maskDataOrTarget - The masking data.\n   */\n  push(target, maskDataOrTarget) {\n    let maskData = maskDataOrTarget;\n    if (!maskData.isMaskData) {\n      const d = this.maskDataPool.pop() || new MaskData();\n      d.pooled = !0, d.maskObject = maskDataOrTarget, maskData = d;\n    }\n    const maskAbove = this.maskStack.length !== 0 ? this.maskStack[this.maskStack.length - 1] : null;\n    if (maskData.copyCountersOrReset(maskAbove), maskData._colorMask = maskAbove ? maskAbove._colorMask : 15, maskData.autoDetect && this.detect(maskData), maskData._target = target, maskData.type !== MASK_TYPES.SPRITE && this.maskStack.push(maskData), maskData.enabled) switch (maskData.type) {\n      case MASK_TYPES.SCISSOR:\n        this.renderer.scissor.push(maskData);\n        break;\n      case MASK_TYPES.STENCIL:\n        this.renderer.stencil.push(maskData);\n        break;\n      case MASK_TYPES.SPRITE:\n        maskData.copyCountersOrReset(null), this.pushSpriteMask(maskData);\n        break;\n      case MASK_TYPES.COLOR:\n        this.pushColorMask(maskData);\n        break;\n      default:\n        break;\n    }\n    maskData.type === MASK_TYPES.SPRITE && this.maskStack.push(maskData);\n  }\n  /**\n   * Removes the last mask from the mask stack and doesn't return it.\n   *\n   * NOTE: The batch renderer should be flushed beforehand to render the masked contents before the mask is removed.\n   * @param {PIXI.IMaskTarget} target - Display Object to pop the mask from\n   */\n  pop(target) {\n    const maskData = this.maskStack.pop();\n    if (!(!maskData || maskData._target !== target)) {\n      if (maskData.enabled) switch (maskData.type) {\n        case MASK_TYPES.SCISSOR:\n          this.renderer.scissor.pop(maskData);\n          break;\n        case MASK_TYPES.STENCIL:\n          this.renderer.stencil.pop(maskData.maskObject);\n          break;\n        case MASK_TYPES.SPRITE:\n          this.popSpriteMask(maskData);\n          break;\n        case MASK_TYPES.COLOR:\n          this.popColorMask(maskData);\n          break;\n        default:\n          break;\n      }\n      if (maskData.reset(), maskData.pooled && this.maskDataPool.push(maskData), this.maskStack.length !== 0) {\n        const maskCurrent = this.maskStack[this.maskStack.length - 1];\n        maskCurrent.type === MASK_TYPES.SPRITE && maskCurrent._filters && (maskCurrent._filters[0].maskSprite = maskCurrent.maskObject);\n      }\n    }\n  }\n  /**\n   * Sets type of MaskData based on its maskObject.\n   * @param maskData\n   */\n  detect(maskData) {\n    const maskObject = maskData.maskObject;\n    maskObject ? maskObject.isSprite ? maskData.type = MASK_TYPES.SPRITE : this.enableScissor && this.renderer.scissor.testScissor(maskData) ? maskData.type = MASK_TYPES.SCISSOR : maskData.type = MASK_TYPES.STENCIL : maskData.type = MASK_TYPES.COLOR;\n  }\n  /**\n   * Applies the Mask and adds it to the current filter stack.\n   * @param maskData - Sprite to be used as the mask.\n   */\n  pushSpriteMask(maskData) {\n    const {\n        maskObject\n      } = maskData,\n      target = maskData._target;\n    let alphaMaskFilter = maskData._filters;\n    alphaMaskFilter || (alphaMaskFilter = this.alphaMaskPool[this.alphaMaskIndex], alphaMaskFilter || (alphaMaskFilter = this.alphaMaskPool[this.alphaMaskIndex] = [new SpriteMaskFilter()])), alphaMaskFilter[0].resolution = maskData.resolution, alphaMaskFilter[0].multisample = maskData.multisample, alphaMaskFilter[0].maskSprite = maskObject;\n    const stashFilterArea = target.filterArea;\n    target.filterArea = maskObject.getBounds(!0), this.renderer.filter.push(target, alphaMaskFilter), target.filterArea = stashFilterArea, maskData._filters || this.alphaMaskIndex++;\n  }\n  /**\n   * Removes the last filter from the filter stack and doesn't return it.\n   * @param maskData - Sprite to be used as the mask.\n   */\n  popSpriteMask(maskData) {\n    this.renderer.filter.pop(), maskData._filters ? maskData._filters[0].maskSprite = null : (this.alphaMaskIndex--, this.alphaMaskPool[this.alphaMaskIndex][0].maskSprite = null);\n  }\n  /**\n   * Pushes the color mask.\n   * @param maskData - The mask data\n   */\n  pushColorMask(maskData) {\n    const currColorMask = maskData._colorMask,\n      nextColorMask = maskData._colorMask = currColorMask & maskData.colorMask;\n    nextColorMask !== currColorMask && this.renderer.gl.colorMask((nextColorMask & 1) !== 0, (nextColorMask & 2) !== 0, (nextColorMask & 4) !== 0, (nextColorMask & 8) !== 0);\n  }\n  /**\n   * Pops the color mask.\n   * @param maskData - The mask data\n   */\n  popColorMask(maskData) {\n    const currColorMask = maskData._colorMask,\n      nextColorMask = this.maskStack.length > 0 ? this.maskStack[this.maskStack.length - 1]._colorMask : 15;\n    nextColorMask !== currColorMask && this.renderer.gl.colorMask((nextColorMask & 1) !== 0, (nextColorMask & 2) !== 0, (nextColorMask & 4) !== 0, (nextColorMask & 8) !== 0);\n  }\n  destroy() {\n    this.renderer = null;\n  }\n}\nMaskSystem.extension = {\n  type: ExtensionType.RendererSystem,\n  name: \"mask\"\n};\nextensions.add(MaskSystem);\nexport { MaskSystem };", "map": {"version": 3, "names": ["MaskSystem", "constructor", "renderer", "enableScissor", "alphaMaskPool", "maskDataPool", "maskStack", "alphaMaskIndex", "setMaskStack", "scissor", "stencil", "push", "target", "maskDataOrTarget", "maskData", "isMaskData", "d", "pop", "MaskData", "pooled", "maskObject", "maskAbove", "length", "copyCountersOrReset", "_colorMask", "autoDetect", "detect", "_target", "type", "MASK_TYPES", "SPRITE", "enabled", "SCISSOR", "STENCIL", "pushSpriteMask", "COLOR", "pushColorMask", "popSpriteMask", "popColorMask", "reset", "mask<PERSON>urrent", "_filters", "maskSprite", "isSprite", "testScissor", "alphaMaskFilter", "SpriteMaskFilter", "resolution", "multisample", "stashFilterArea", "filterArea", "getBounds", "filter", "currColorMask", "nextColorMask", "colorMask", "gl", "destroy", "extension", "ExtensionType", "RendererSystem", "name", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\mask\\MaskSystem.ts"], "sourcesContent": ["import { MASK_TYPES } from '@pixi/constants';\nimport { extensions, ExtensionType } from '@pixi/extensions';\nimport { SpriteMaskFilter } from '../filters/spriteMask/SpriteMaskFilter';\nimport { MaskData } from './MaskData';\n\nimport type { ExtensionMetadata } from '@pixi/extensions';\nimport type { Renderer } from '../Renderer';\nimport type { ISystem } from '../system/ISystem';\nimport type { IMaskTarget } from './MaskData';\n\n/**\n * System plugin to the renderer to manage masks.\n *\n * There are three built-in types of masking:\n * **Scissor Masking**: Scissor masking discards pixels that are outside of a rectangle called the scissor box. It is\n *  the most performant as the scissor test is inexpensive. However, it can only be used when the mask is rectangular.\n * **Stencil Masking**: Stencil masking discards pixels that don't overlap with the pixels rendered into the stencil\n *  buffer. It is the next fastest option as it does not require rendering into a separate framebuffer. However, it does\n *  cause the mask to be rendered **twice** for each masking operation; hence, minimize the rendering cost of your masks.\n * **Sprite Mask Filtering**: Sprite mask filtering discards pixels based on the red channel of the sprite-mask's\n *  texture. (Generally, the masking texture is grayscale). Using advanced techniques, you might be able to embed this\n *  type of masking in a custom shader - and hence, bypassing the masking system fully for performance wins.\n *\n * The best type of masking is auto-detected when you `push` one. To use scissor masking, you must pass in a `Graphics`\n * object with just a rectangle drawn.\n *\n * ## Mask Stacks\n *\n * In the scene graph, masks can be applied recursively, i.e. a mask can be applied during a masking operation. The mask\n * stack stores the currently applied masks in order. Each {@link PIXI.BaseRenderTexture} holds its own mask stack, i.e.\n * when you switch render-textures, the old masks only applied when you switch back to rendering to the old render-target.\n * @memberof PIXI\n */\nexport class MaskSystem implements ISystem\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        type: ExtensionType.RendererSystem,\n        name: 'mask',\n    };\n\n    /**\n     * Flag to enable scissor masking.\n     * @default true\n     */\n    public enableScissor: boolean;\n\n    /** Pool of used sprite mask filters. */\n    protected readonly alphaMaskPool: Array<SpriteMaskFilter[]>;\n\n    /**\n     * Current index of alpha mask pool.\n     * @default 0\n     * @readonly\n     */\n    protected alphaMaskIndex: number;\n\n    /** Pool of mask data. */\n    private readonly maskDataPool: Array<MaskData>;\n    private maskStack: Array<MaskData>;\n    private renderer: Renderer;\n\n    /**\n     * @param renderer - The renderer this System works for.\n     */\n    constructor(renderer: Renderer)\n    {\n        this.renderer = renderer;\n\n        this.enableScissor = true;\n        this.alphaMaskPool = [];\n        this.maskDataPool = [];\n\n        this.maskStack = [];\n        this.alphaMaskIndex = 0;\n    }\n\n    /**\n     * Changes the mask stack that is used by this System.\n     * @param maskStack - The mask stack\n     */\n    setMaskStack(maskStack: Array<MaskData>): void\n    {\n        this.maskStack = maskStack;\n        this.renderer.scissor.setMaskStack(maskStack);\n        this.renderer.stencil.setMaskStack(maskStack);\n    }\n\n    /**\n     * Enables the mask and appends it to the current mask stack.\n     *\n     * NOTE: The batch renderer should be flushed beforehand to prevent pending renders from being masked.\n     * @param {PIXI.DisplayObject} target - Display Object to push the mask to\n     * @param {PIXI.MaskData|PIXI.Sprite|PIXI.Graphics|PIXI.DisplayObject} maskDataOrTarget - The masking data.\n     */\n    push(target: IMaskTarget, maskDataOrTarget: MaskData | IMaskTarget): void\n    {\n        let maskData = maskDataOrTarget as MaskData;\n\n        if (!maskData.isMaskData)\n        {\n            const d = this.maskDataPool.pop() || new MaskData();\n\n            d.pooled = true;\n            d.maskObject = maskDataOrTarget as IMaskTarget;\n            maskData = d;\n        }\n\n        const maskAbove = this.maskStack.length !== 0 ? this.maskStack[this.maskStack.length - 1] : null;\n\n        maskData.copyCountersOrReset(maskAbove);\n        maskData._colorMask = maskAbove ? maskAbove._colorMask : 0xf;\n\n        if (maskData.autoDetect)\n        {\n            this.detect(maskData);\n        }\n\n        maskData._target = target;\n\n        if (maskData.type !== MASK_TYPES.SPRITE)\n        {\n            this.maskStack.push(maskData);\n        }\n\n        if (maskData.enabled)\n        {\n            switch (maskData.type)\n            {\n                case MASK_TYPES.SCISSOR:\n                    this.renderer.scissor.push(maskData);\n                    break;\n                case MASK_TYPES.STENCIL:\n                    this.renderer.stencil.push(maskData);\n                    break;\n                case MASK_TYPES.SPRITE:\n                    maskData.copyCountersOrReset(null);\n                    this.pushSpriteMask(maskData);\n                    break;\n                case MASK_TYPES.COLOR:\n                    this.pushColorMask(maskData);\n                    break;\n                default:\n                    break;\n            }\n        }\n\n        if (maskData.type === MASK_TYPES.SPRITE)\n        {\n            this.maskStack.push(maskData);\n        }\n    }\n\n    /**\n     * Removes the last mask from the mask stack and doesn't return it.\n     *\n     * NOTE: The batch renderer should be flushed beforehand to render the masked contents before the mask is removed.\n     * @param {PIXI.IMaskTarget} target - Display Object to pop the mask from\n     */\n    pop(target: IMaskTarget): void\n    {\n        const maskData = this.maskStack.pop();\n\n        if (!maskData || maskData._target !== target)\n        {\n            // TODO: add an assert when we have it\n\n            return;\n        }\n\n        if (maskData.enabled)\n        {\n            switch (maskData.type)\n            {\n                case MASK_TYPES.SCISSOR:\n                    this.renderer.scissor.pop(maskData);\n                    break;\n                case MASK_TYPES.STENCIL:\n                    this.renderer.stencil.pop(maskData.maskObject);\n                    break;\n                case MASK_TYPES.SPRITE:\n                    this.popSpriteMask(maskData);\n                    break;\n                case MASK_TYPES.COLOR:\n                    this.popColorMask(maskData);\n                    break;\n                default:\n                    break;\n            }\n        }\n\n        maskData.reset();\n\n        if (maskData.pooled)\n        {\n            this.maskDataPool.push(maskData);\n        }\n\n        if (this.maskStack.length !== 0)\n        {\n            const maskCurrent = this.maskStack[this.maskStack.length - 1];\n\n            if (maskCurrent.type === MASK_TYPES.SPRITE && maskCurrent._filters)\n            {\n                maskCurrent._filters[0].maskSprite = maskCurrent.maskObject;\n            }\n        }\n    }\n\n    /**\n     * Sets type of MaskData based on its maskObject.\n     * @param maskData\n     */\n    detect(maskData: MaskData): void\n    {\n        const maskObject = maskData.maskObject;\n\n        if (!maskObject)\n        {\n            maskData.type = MASK_TYPES.COLOR;\n        }\n        else if (maskObject.isSprite)\n        {\n            maskData.type = MASK_TYPES.SPRITE;\n        }\n        else if (this.enableScissor && this.renderer.scissor.testScissor(maskData))\n        {\n            maskData.type = MASK_TYPES.SCISSOR;\n        }\n        else\n        {\n            maskData.type = MASK_TYPES.STENCIL;\n        }\n    }\n\n    /**\n     * Applies the Mask and adds it to the current filter stack.\n     * @param maskData - Sprite to be used as the mask.\n     */\n    pushSpriteMask(maskData: MaskData): void\n    {\n        const { maskObject } = maskData;\n        const target = maskData._target;\n        let alphaMaskFilter = maskData._filters;\n\n        if (!alphaMaskFilter)\n        {\n            alphaMaskFilter = this.alphaMaskPool[this.alphaMaskIndex];\n\n            if (!alphaMaskFilter)\n            {\n                alphaMaskFilter = this.alphaMaskPool[this.alphaMaskIndex] = [new SpriteMaskFilter()];\n            }\n        }\n\n        alphaMaskFilter[0].resolution = maskData.resolution;\n        alphaMaskFilter[0].multisample = maskData.multisample;\n        alphaMaskFilter[0].maskSprite = maskObject;\n\n        const stashFilterArea = target.filterArea;\n\n        target.filterArea = maskObject.getBounds(true);\n        this.renderer.filter.push(target, alphaMaskFilter);\n        target.filterArea = stashFilterArea;\n\n        if (!maskData._filters)\n        {\n            this.alphaMaskIndex++;\n        }\n    }\n\n    /**\n     * Removes the last filter from the filter stack and doesn't return it.\n     * @param maskData - Sprite to be used as the mask.\n     */\n    popSpriteMask(maskData: MaskData): void\n    {\n        this.renderer.filter.pop();\n\n        if (maskData._filters)\n        {\n            maskData._filters[0].maskSprite = null;\n        }\n        else\n        {\n            this.alphaMaskIndex--;\n            this.alphaMaskPool[this.alphaMaskIndex][0].maskSprite = null;\n        }\n    }\n\n    /**\n     * Pushes the color mask.\n     * @param maskData - The mask data\n     */\n    pushColorMask(maskData: MaskData): void\n    {\n        const currColorMask = maskData._colorMask;\n        const nextColorMask = maskData._colorMask = currColorMask & maskData.colorMask;\n\n        if (nextColorMask !== currColorMask)\n        {\n            this.renderer.gl.colorMask(\n                (nextColorMask & 0x1) !== 0,\n                (nextColorMask & 0x2) !== 0,\n                (nextColorMask & 0x4) !== 0,\n                (nextColorMask & 0x8) !== 0\n            );\n        }\n    }\n\n    /**\n     * Pops the color mask.\n     * @param maskData - The mask data\n     */\n    popColorMask(maskData: MaskData): void\n    {\n        const currColorMask = maskData._colorMask;\n        const nextColorMask = this.maskStack.length > 0\n            ? this.maskStack[this.maskStack.length - 1]._colorMask : 0xf;\n\n        if (nextColorMask !== currColorMask)\n        {\n            this.renderer.gl.colorMask(\n                (nextColorMask & 0x1) !== 0,\n                (nextColorMask & 0x2) !== 0,\n                (nextColorMask & 0x4) !== 0,\n                (nextColorMask & 0x8) !== 0\n            );\n        }\n    }\n\n    destroy(): void\n    {\n        this.renderer = null;\n    }\n}\n\nextensions.add(MaskSystem);\n"], "mappings": ";;;;AAiCO,MAAMA,UAAA,CACb;EAAA;AAAA;AAAA;EA+BIC,YAAYC,QAAA,EACZ;IACI,KAAKA,QAAA,GAAWA,QAAA,EAEhB,KAAKC,aAAA,GAAgB,IACrB,KAAKC,aAAA,GAAgB,IACrB,KAAKC,YAAA,GAAe,EAEpB,OAAKC,SAAA,GAAY,IACjB,KAAKC,cAAA,GAAiB;EAC1B;EAAA;AAAA;AAAA;AAAA;EAMAC,aAAaF,SAAA,EACb;IACI,KAAKA,SAAA,GAAYA,SAAA,EACjB,KAAKJ,QAAA,CAASO,OAAA,CAAQD,YAAA,CAAaF,SAAS,GAC5C,KAAKJ,QAAA,CAASQ,OAAA,CAAQF,YAAA,CAAaF,SAAS;EAChD;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAK,KAAKC,MAAA,EAAqBC,gBAAA,EAC1B;IACI,IAAIC,QAAA,GAAWD,gBAAA;IAEX,KAACC,QAAA,CAASC,UAAA,EACd;MACI,MAAMC,CAAA,GAAI,KAAKX,YAAA,CAAaY,GAAA,CAAI,KAAK,IAAIC,QAAA;MAEzCF,CAAA,CAAEG,MAAA,GAAS,IACXH,CAAA,CAAEI,UAAA,GAAaP,gBAAA,EACfC,QAAA,GAAWE,CAAA;IACf;IAEM,MAAAK,SAAA,GAAY,KAAKf,SAAA,CAAUgB,MAAA,KAAW,IAAI,KAAKhB,SAAA,CAAU,KAAKA,SAAA,CAAUgB,MAAA,GAAS,CAAC,IAAI;IAiB5F,IAfAR,QAAA,CAASS,mBAAA,CAAoBF,SAAS,GACtCP,QAAA,CAASU,UAAA,GAAaH,SAAA,GAAYA,SAAA,CAAUG,UAAA,GAAa,IAErDV,QAAA,CAASW,UAAA,IAET,KAAKC,MAAA,CAAOZ,QAAQ,GAGxBA,QAAA,CAASa,OAAA,GAAUf,MAAA,EAEfE,QAAA,CAASc,IAAA,KAASC,UAAA,CAAWC,MAAA,IAE7B,KAAKxB,SAAA,CAAUK,IAAA,CAAKG,QAAQ,GAG5BA,QAAA,CAASiB,OAAA,EAET,QAAQjB,QAAA,CAASc,IAAA;MAEb,KAAKC,UAAA,CAAWG,OAAA;QACP,KAAA9B,QAAA,CAASO,OAAA,CAAQE,IAAA,CAAKG,QAAQ;QACnC;MACJ,KAAKe,UAAA,CAAWI,OAAA;QACP,KAAA/B,QAAA,CAASQ,OAAA,CAAQC,IAAA,CAAKG,QAAQ;QACnC;MACJ,KAAKe,UAAA,CAAWC,MAAA;QACZhB,QAAA,CAASS,mBAAA,CAAoB,IAAI,GACjC,KAAKW,cAAA,CAAepB,QAAQ;QAC5B;MACJ,KAAKe,UAAA,CAAWM,KAAA;QACZ,KAAKC,aAAA,CAActB,QAAQ;QAC3B;MACJ;QACI;IACR;IAGAA,QAAA,CAASc,IAAA,KAASC,UAAA,CAAWC,MAAA,IAE7B,KAAKxB,SAAA,CAAUK,IAAA,CAAKG,QAAQ;EAEpC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAG,IAAIL,MAAA,EACJ;IACU,MAAAE,QAAA,GAAW,KAAKR,SAAA,CAAUW,GAAA,CAAI;IAEpC,IAAI,EAAC,CAAAH,QAAA,IAAYA,QAAA,CAASa,OAAA,KAAYf,MAAA,GAOtC;MAAA,IAAIE,QAAA,CAASiB,OAAA,EAET,QAAQjB,QAAA,CAASc,IAAA;QAEb,KAAKC,UAAA,CAAWG,OAAA;UACP,KAAA9B,QAAA,CAASO,OAAA,CAAQQ,GAAA,CAAIH,QAAQ;UAClC;QACJ,KAAKe,UAAA,CAAWI,OAAA;UACZ,KAAK/B,QAAA,CAASQ,OAAA,CAAQO,GAAA,CAAIH,QAAA,CAASM,UAAU;UAC7C;QACJ,KAAKS,UAAA,CAAWC,MAAA;UACZ,KAAKO,aAAA,CAAcvB,QAAQ;UAC3B;QACJ,KAAKe,UAAA,CAAWM,KAAA;UACZ,KAAKG,YAAA,CAAaxB,QAAQ;UAC1B;QACJ;UACI;MACR;MAUJ,IAPAA,QAAA,CAASyB,KAAA,IAELzB,QAAA,CAASK,MAAA,IAET,KAAKd,YAAA,CAAaM,IAAA,CAAKG,QAAQ,GAG/B,KAAKR,SAAA,CAAUgB,MAAA,KAAW,GAC9B;QACI,MAAMkB,WAAA,GAAc,KAAKlC,SAAA,CAAU,KAAKA,SAAA,CAAUgB,MAAA,GAAS,CAAC;QAExDkB,WAAA,CAAYZ,IAAA,KAASC,UAAA,CAAWC,MAAA,IAAUU,WAAA,CAAYC,QAAA,KAEtDD,WAAA,CAAYC,QAAA,CAAS,CAAC,EAAEC,UAAA,GAAaF,WAAA,CAAYpB,UAAA;MAEzD;IAAA;EACJ;EAAA;AAAA;AAAA;AAAA;EAMAM,OAAOZ,QAAA,EACP;IACI,MAAMM,UAAA,GAAaN,QAAA,CAASM,UAAA;IAEvBA,UAAA,GAIIA,UAAA,CAAWuB,QAAA,GAEhB7B,QAAA,CAASc,IAAA,GAAOC,UAAA,CAAWC,MAAA,GAEtB,KAAK3B,aAAA,IAAiB,KAAKD,QAAA,CAASO,OAAA,CAAQmC,WAAA,CAAY9B,QAAQ,IAErEA,QAAA,CAASc,IAAA,GAAOC,UAAA,CAAWG,OAAA,GAI3BlB,QAAA,CAASc,IAAA,GAAOC,UAAA,CAAWI,OAAA,GAZ3BnB,QAAA,CAASc,IAAA,GAAOC,UAAA,CAAWM,KAAA;EAcnC;EAAA;AAAA;AAAA;AAAA;EAMAD,eAAepB,QAAA,EACf;IACI,MAAM;QAAEM;MAAA,IAAeN,QAAA;MACjBF,MAAA,GAASE,QAAA,CAASa,OAAA;IACxB,IAAIkB,eAAA,GAAkB/B,QAAA,CAAS2B,QAAA;IAE1BI,eAAA,KAEDA,eAAA,GAAkB,KAAKzC,aAAA,CAAc,KAAKG,cAAc,GAEnDsC,eAAA,KAEDA,eAAA,GAAkB,KAAKzC,aAAA,CAAc,KAAKG,cAAc,IAAI,CAAC,IAAIuC,gBAAA,EAAkB,KAI3FD,eAAA,CAAgB,CAAC,EAAEE,UAAA,GAAajC,QAAA,CAASiC,UAAA,EACzCF,eAAA,CAAgB,CAAC,EAAEG,WAAA,GAAclC,QAAA,CAASkC,WAAA,EAC1CH,eAAA,CAAgB,CAAC,EAAEH,UAAA,GAAatB,UAAA;IAEhC,MAAM6B,eAAA,GAAkBrC,MAAA,CAAOsC,UAAA;IAE/BtC,MAAA,CAAOsC,UAAA,GAAa9B,UAAA,CAAW+B,SAAA,CAAU,EAAI,GAC7C,KAAKjD,QAAA,CAASkD,MAAA,CAAOzC,IAAA,CAAKC,MAAA,EAAQiC,eAAe,GACjDjC,MAAA,CAAOsC,UAAA,GAAaD,eAAA,EAEfnC,QAAA,CAAS2B,QAAA,IAEV,KAAKlC,cAAA;EAEb;EAAA;AAAA;AAAA;AAAA;EAMA8B,cAAcvB,QAAA,EACd;IACS,KAAAZ,QAAA,CAASkD,MAAA,CAAOnC,GAAA,IAEjBH,QAAA,CAAS2B,QAAA,GAET3B,QAAA,CAAS2B,QAAA,CAAS,CAAC,EAAEC,UAAA,GAAa,QAIlC,KAAKnC,cAAA,IACL,KAAKH,aAAA,CAAc,KAAKG,cAAc,EAAE,CAAC,EAAEmC,UAAA,GAAa;EAEhE;EAAA;AAAA;AAAA;AAAA;EAMAN,cAActB,QAAA,EACd;IACI,MAAMuC,aAAA,GAAgBvC,QAAA,CAASU,UAAA;MACzB8B,aAAA,GAAgBxC,QAAA,CAASU,UAAA,GAAa6B,aAAA,GAAgBvC,QAAA,CAASyC,SAAA;IAEjED,aAAA,KAAkBD,aAAA,IAElB,KAAKnD,QAAA,CAASsD,EAAA,CAAGD,SAAA,EACZD,aAAA,GAAgB,OAAS,IACzBA,aAAA,GAAgB,OAAS,IACzBA,aAAA,GAAgB,OAAS,IACzBA,aAAA,GAAgB,OAAS;EAGtC;EAAA;AAAA;AAAA;AAAA;EAMAhB,aAAaxB,QAAA,EACb;IACI,MAAMuC,aAAA,GAAgBvC,QAAA,CAASU,UAAA;MACzB8B,aAAA,GAAgB,KAAKhD,SAAA,CAAUgB,MAAA,GAAS,IACxC,KAAKhB,SAAA,CAAU,KAAKA,SAAA,CAAUgB,MAAA,GAAS,CAAC,EAAEE,UAAA,GAAa;IAEzD8B,aAAA,KAAkBD,aAAA,IAElB,KAAKnD,QAAA,CAASsD,EAAA,CAAGD,SAAA,EACZD,aAAA,GAAgB,OAAS,IACzBA,aAAA,GAAgB,OAAS,IACzBA,aAAA,GAAgB,OAAS,IACzBA,aAAA,GAAgB,OAAS;EAGtC;EAEAG,QAAA,EACA;IACI,KAAKvD,QAAA,GAAW;EACpB;AACJ;AA9SaF,UAAA,CAGF0D,SAAA,GAA+B;EAClC9B,IAAA,EAAM+B,aAAA,CAAcC,cAAA;EACpBC,IAAA,EAAM;AACV;AA0SJC,UAAA,CAAWC,GAAA,CAAI/D,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}