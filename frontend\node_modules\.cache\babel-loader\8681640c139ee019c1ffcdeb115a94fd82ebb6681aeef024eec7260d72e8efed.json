{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Projects\\\\Python\\\\EU4\\\\frontend\\\\src\\\\WorldMap.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport * as PIXI from 'pixi.js';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MAP_WIDTH = 800;\nconst MAP_HEIGHT = 600;\nconst colors = [0x88ccff, 0xe67e22, 0x2ecc71, 0xf39c12, 0x9b59b6, 0x1abc9c, 0xe74c3c, 0x34495e, 0x27ae60, 0x2980b9];\nconst WorldMap = () => {\n  _s();\n  const pixiContainer = useRef(null);\n  const appRef = useRef(null);\n  const stageRef = useRef(null);\n  const [countries, setCountries] = React.useState([]);\n  const [dragging, setDragging] = React.useState(false);\n  const [lastPos, setLastPos] = React.useState({\n    x: 0,\n    y: 0\n  });\n  useEffect(() => {\n    axios.get('http://localhost:8000/countries').then(r => setCountries(r.data));\n  }, []);\n  useEffect(() => {\n    if (!countries.length) return;\n    const app = new PIXI.Application({\n      width: MAP_WIDTH,\n      height: MAP_HEIGHT,\n      backgroundColor: 0x222233,\n      antialias: true\n    });\n    appRef.current = app;\n    pixiContainer.current.appendChild(app.view);\n\n    // Create a container for panning/zooming\n    const stage = new PIXI.Container();\n    stageRef.current = stage;\n    app.stage.addChild(stage);\n\n    // Draw countries as polygons\n    countries.forEach((country, i) => {\n      if (!country.geometry || country.geometry.type !== 'Polygon') return;\n      const graphics = new PIXI.Graphics();\n      graphics.beginFill(colors[i % colors.length], 0.7);\n      country.geometry.coordinates.forEach(ring => {\n        if (!ring.length) return;\n        graphics.moveTo(ring[0][0] * 4 + 400, 300 - ring[0][1] * 4);\n        for (let j = 1; j < ring.length; j++) {\n          graphics.lineTo(ring[j][0] * 4 + 400, 300 - ring[j][1] * 4);\n        }\n      });\n      graphics.endFill();\n      graphics.eventMode = 'static';\n      graphics.buttonMode = true;\n      graphics.on('pointerdown', () => {\n        alert(`Clicked: ${country.name}`);\n      });\n      stage.addChild(graphics);\n    });\n\n    // Pan\n    let dragging = false;\n    let last = {\n      x: 0,\n      y: 0\n    };\n    app.view.addEventListener('mousedown', e => {\n      dragging = true;\n      last = {\n        x: e.clientX,\n        y: e.clientY\n      };\n    });\n    app.view.addEventListener('mouseup', () => {\n      dragging = false;\n    });\n    app.view.addEventListener('mouseleave', () => {\n      dragging = false;\n    });\n    app.view.addEventListener('mousemove', e => {\n      if (!dragging) return;\n      const dx = e.clientX - last.x;\n      const dy = e.clientY - last.y;\n      stage.x += dx;\n      stage.y += dy;\n      last = {\n        x: e.clientX,\n        y: e.clientY\n      };\n    });\n\n    // Zoom\n    app.view.addEventListener('wheel', e => {\n      e.preventDefault();\n      const scale = Math.max(0.2, Math.min(5, stage.scale.x + (e.deltaY < 0 ? 0.1 : -0.1)));\n      stage.scale.set(scale);\n    });\n    return () => {\n      app.destroy(true, {\n        children: true\n      });\n    };\n  }, [countries]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: pixiContainer,\n    style: {\n      width: MAP_WIDTH,\n      height: MAP_HEIGHT,\n      border: '2px solid #444',\n      margin: 'auto',\n      cursor: 'grab'\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n};\n_s(WorldMap, \"batdOQRKuUjtCIY/BtAQiUoeapw=\");\n_c = WorldMap;\nexport default WorldMap;\nvar _c;\n$RefreshReg$(_c, \"WorldMap\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "PIXI", "axios", "jsxDEV", "_jsxDEV", "MAP_WIDTH", "MAP_HEIGHT", "colors", "WorldMap", "_s", "pixiContainer", "appRef", "stageRef", "countries", "setCountries", "useState", "dragging", "setDragging", "lastPos", "setLastPos", "x", "y", "get", "then", "r", "data", "length", "app", "Application", "width", "height", "backgroundColor", "antialias", "current", "append<PERSON><PERSON><PERSON>", "view", "stage", "Container", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "country", "i", "geometry", "type", "graphics", "Graphics", "beginFill", "coordinates", "ring", "moveTo", "j", "lineTo", "endFill", "eventMode", "buttonMode", "on", "alert", "name", "last", "addEventListener", "e", "clientX", "clientY", "dx", "dy", "preventDefault", "scale", "Math", "max", "min", "deltaY", "set", "destroy", "children", "ref", "style", "border", "margin", "cursor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Projects/Python/EU4/frontend/src/WorldMap.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\nimport * as PIXI from 'pixi.js';\r\nimport axios from 'axios';\r\n\r\nconst MAP_WIDTH = 800;\r\nconst MAP_HEIGHT = 600;\r\n\r\nconst colors = [0x88ccff, 0xe67e22, 0x2ecc71, 0xf39c12, 0x9b59b6, 0x1abc9c, 0xe74c3c, 0x34495e, 0x27ae60, 0x2980b9];\r\n\r\nconst WorldMap = () => {\r\n  const pixiContainer = useRef(null);\r\n  const appRef = useRef(null);\r\n  const stageRef = useRef(null);\r\n  const [countries, setCountries] = React.useState([]);\r\n  const [dragging, setDragging] = React.useState(false);\r\n  const [lastPos, setLastPos] = React.useState({ x: 0, y: 0 });\r\n\r\n  useEffect(() => {\r\n    axios.get('http://localhost:8000/countries').then(r => setCountries(r.data));\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (!countries.length) return;\r\n    const app = new PIXI.Application({\r\n      width: MAP_WIDTH,\r\n      height: MAP_HEIGHT,\r\n      backgroundColor: 0x222233,\r\n      antialias: true,\r\n    });\r\n    appRef.current = app;\r\n    pixiContainer.current.appendChild(app.view);\r\n\r\n    // Create a container for panning/zooming\r\n    const stage = new PIXI.Container();\r\n    stageRef.current = stage;\r\n    app.stage.addChild(stage);\r\n\r\n    // Draw countries as polygons\r\n    countries.forEach((country, i) => {\r\n      if (!country.geometry || country.geometry.type !== 'Polygon') return;\r\n      const graphics = new PIXI.Graphics();\r\n      graphics.beginFill(colors[i % colors.length], 0.7);\r\n      country.geometry.coordinates.forEach(ring => {\r\n        if (!ring.length) return;\r\n        graphics.moveTo(ring[0][0] * 4 + 400, 300 - ring[0][1] * 4);\r\n        for (let j = 1; j < ring.length; j++) {\r\n          graphics.lineTo(ring[j][0] * 4 + 400, 300 - ring[j][1] * 4);\r\n        }\r\n      });\r\n      graphics.endFill();\r\n      graphics.eventMode = 'static';\r\n      graphics.buttonMode = true;\r\n      graphics.on('pointerdown', () => {\r\n        alert(`Clicked: ${country.name}`);\r\n      });\r\n      stage.addChild(graphics);\r\n    });\r\n\r\n    // Pan\r\n    let dragging = false;\r\n    let last = { x: 0, y: 0 };\r\n    app.view.addEventListener('mousedown', (e) => {\r\n      dragging = true;\r\n      last = { x: e.clientX, y: e.clientY };\r\n    });\r\n    app.view.addEventListener('mouseup', () => { dragging = false; });\r\n    app.view.addEventListener('mouseleave', () => { dragging = false; });\r\n    app.view.addEventListener('mousemove', (e) => {\r\n      if (!dragging) return;\r\n      const dx = e.clientX - last.x;\r\n      const dy = e.clientY - last.y;\r\n      stage.x += dx;\r\n      stage.y += dy;\r\n      last = { x: e.clientX, y: e.clientY };\r\n    });\r\n\r\n    // Zoom\r\n    app.view.addEventListener('wheel', (e) => {\r\n      e.preventDefault();\r\n      const scale = Math.max(0.2, Math.min(5, stage.scale.x + (e.deltaY < 0 ? 0.1 : -0.1)));\r\n      stage.scale.set(scale);\r\n    });\r\n\r\n    return () => {\r\n      app.destroy(true, { children: true });\r\n    };\r\n  }, [countries]);\r\n\r\n  return (\r\n    <div\r\n      ref={pixiContainer}\r\n      style={{ width: MAP_WIDTH, height: MAP_HEIGHT, border: '2px solid #444', margin: 'auto', cursor: 'grab' }}\r\n    />\r\n  );\r\n};\r\n\r\nexport default WorldMap;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAC/B,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAG,GAAG;AACrB,MAAMC,UAAU,GAAG,GAAG;AAEtB,MAAMC,MAAM,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;AAEnH,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,aAAa,GAAGV,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMW,MAAM,GAAGX,MAAM,CAAC,IAAI,CAAC;EAC3B,MAAMY,QAAQ,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGhB,KAAK,CAACiB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,KAAK,CAACiB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAGrB,KAAK,CAACiB,QAAQ,CAAC;IAAEK,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAE5DtB,SAAS,CAAC,MAAM;IACdG,KAAK,CAACoB,GAAG,CAAC,iCAAiC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIV,YAAY,CAACU,CAAC,CAACC,IAAI,CAAC,CAAC;EAC9E,CAAC,EAAE,EAAE,CAAC;EAEN1B,SAAS,CAAC,MAAM;IACd,IAAI,CAACc,SAAS,CAACa,MAAM,EAAE;IACvB,MAAMC,GAAG,GAAG,IAAI1B,IAAI,CAAC2B,WAAW,CAAC;MAC/BC,KAAK,EAAExB,SAAS;MAChByB,MAAM,EAAExB,UAAU;MAClByB,eAAe,EAAE,QAAQ;MACzBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFrB,MAAM,CAACsB,OAAO,GAAGN,GAAG;IACpBjB,aAAa,CAACuB,OAAO,CAACC,WAAW,CAACP,GAAG,CAACQ,IAAI,CAAC;;IAE3C;IACA,MAAMC,KAAK,GAAG,IAAInC,IAAI,CAACoC,SAAS,CAAC,CAAC;IAClCzB,QAAQ,CAACqB,OAAO,GAAGG,KAAK;IACxBT,GAAG,CAACS,KAAK,CAACE,QAAQ,CAACF,KAAK,CAAC;;IAEzB;IACAvB,SAAS,CAAC0B,OAAO,CAAC,CAACC,OAAO,EAAEC,CAAC,KAAK;MAChC,IAAI,CAACD,OAAO,CAACE,QAAQ,IAAIF,OAAO,CAACE,QAAQ,CAACC,IAAI,KAAK,SAAS,EAAE;MAC9D,MAAMC,QAAQ,GAAG,IAAI3C,IAAI,CAAC4C,QAAQ,CAAC,CAAC;MACpCD,QAAQ,CAACE,SAAS,CAACvC,MAAM,CAACkC,CAAC,GAAGlC,MAAM,CAACmB,MAAM,CAAC,EAAE,GAAG,CAAC;MAClDc,OAAO,CAACE,QAAQ,CAACK,WAAW,CAACR,OAAO,CAACS,IAAI,IAAI;QAC3C,IAAI,CAACA,IAAI,CAACtB,MAAM,EAAE;QAClBkB,QAAQ,CAACK,MAAM,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,GAAGA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3D,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACtB,MAAM,EAAEwB,CAAC,EAAE,EAAE;UACpCN,QAAQ,CAACO,MAAM,CAACH,IAAI,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,GAAGF,IAAI,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7D;MACF,CAAC,CAAC;MACFN,QAAQ,CAACQ,OAAO,CAAC,CAAC;MAClBR,QAAQ,CAACS,SAAS,GAAG,QAAQ;MAC7BT,QAAQ,CAACU,UAAU,GAAG,IAAI;MAC1BV,QAAQ,CAACW,EAAE,CAAC,aAAa,EAAE,MAAM;QAC/BC,KAAK,CAAC,YAAYhB,OAAO,CAACiB,IAAI,EAAE,CAAC;MACnC,CAAC,CAAC;MACFrB,KAAK,CAACE,QAAQ,CAACM,QAAQ,CAAC;IAC1B,CAAC,CAAC;;IAEF;IACA,IAAI5B,QAAQ,GAAG,KAAK;IACpB,IAAI0C,IAAI,GAAG;MAAEtC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACzBM,GAAG,CAACQ,IAAI,CAACwB,gBAAgB,CAAC,WAAW,EAAGC,CAAC,IAAK;MAC5C5C,QAAQ,GAAG,IAAI;MACf0C,IAAI,GAAG;QAAEtC,CAAC,EAAEwC,CAAC,CAACC,OAAO;QAAExC,CAAC,EAAEuC,CAAC,CAACE;MAAQ,CAAC;IACvC,CAAC,CAAC;IACFnC,GAAG,CAACQ,IAAI,CAACwB,gBAAgB,CAAC,SAAS,EAAE,MAAM;MAAE3C,QAAQ,GAAG,KAAK;IAAE,CAAC,CAAC;IACjEW,GAAG,CAACQ,IAAI,CAACwB,gBAAgB,CAAC,YAAY,EAAE,MAAM;MAAE3C,QAAQ,GAAG,KAAK;IAAE,CAAC,CAAC;IACpEW,GAAG,CAACQ,IAAI,CAACwB,gBAAgB,CAAC,WAAW,EAAGC,CAAC,IAAK;MAC5C,IAAI,CAAC5C,QAAQ,EAAE;MACf,MAAM+C,EAAE,GAAGH,CAAC,CAACC,OAAO,GAAGH,IAAI,CAACtC,CAAC;MAC7B,MAAM4C,EAAE,GAAGJ,CAAC,CAACE,OAAO,GAAGJ,IAAI,CAACrC,CAAC;MAC7Be,KAAK,CAAChB,CAAC,IAAI2C,EAAE;MACb3B,KAAK,CAACf,CAAC,IAAI2C,EAAE;MACbN,IAAI,GAAG;QAAEtC,CAAC,EAAEwC,CAAC,CAACC,OAAO;QAAExC,CAAC,EAAEuC,CAAC,CAACE;MAAQ,CAAC;IACvC,CAAC,CAAC;;IAEF;IACAnC,GAAG,CAACQ,IAAI,CAACwB,gBAAgB,CAAC,OAAO,EAAGC,CAAC,IAAK;MACxCA,CAAC,CAACK,cAAc,CAAC,CAAC;MAClB,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEjC,KAAK,CAAC8B,KAAK,CAAC9C,CAAC,IAAIwC,CAAC,CAACU,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACrFlC,KAAK,CAAC8B,KAAK,CAACK,GAAG,CAACL,KAAK,CAAC;IACxB,CAAC,CAAC;IAEF,OAAO,MAAM;MACXvC,GAAG,CAAC6C,OAAO,CAAC,IAAI,EAAE;QAAEC,QAAQ,EAAE;MAAK,CAAC,CAAC;IACvC,CAAC;EACH,CAAC,EAAE,CAAC5D,SAAS,CAAC,CAAC;EAEf,oBACET,OAAA;IACEsE,GAAG,EAAEhE,aAAc;IACnBiE,KAAK,EAAE;MAAE9C,KAAK,EAAExB,SAAS;MAAEyB,MAAM,EAAExB,UAAU;MAAEsE,MAAM,EAAE,gBAAgB;MAAEC,MAAM,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3G,CAAC;AAEN,CAAC;AAACzE,EAAA,CArFID,QAAQ;AAAA2E,EAAA,GAAR3E,QAAQ;AAuFd,eAAeA,QAAQ;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}