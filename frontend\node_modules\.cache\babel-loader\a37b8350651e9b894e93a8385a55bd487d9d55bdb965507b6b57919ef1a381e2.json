{"ast": null, "code": "import { WRAP_MODES } from \"@pixi/core\";\nimport { Mesh, MeshMaterial } from \"@pixi/mesh\";\nimport { RopeGeometry } from \"./geometry/RopeGeometry.mjs\";\nclass SimpleRope extends Mesh {\n  /**\n   * Note: The wrap mode of the texture is set to REPEAT if `textureScale` is positive.\n   * @param texture - The texture to use on the rope.\n   * @param points - An array of {@link PIXI.Point} objects to construct this rope.\n   * @param {number} textureScale - Optional. Positive values scale rope texture\n   * keeping its aspect ratio. You can reduce alpha channel artifacts by providing a larger texture\n   * and downsampling here. If set to zero, texture will be stretched instead.\n   */\n  constructor(texture, points, textureScale = 0) {\n    const ropeGeometry = new RopeGeometry(texture.height, points, textureScale),\n      meshMaterial = new MeshMaterial(texture);\n    textureScale > 0 && (texture.baseTexture.wrapMode = WRAP_MODES.REPEAT), super(ropeGeometry, meshMaterial), this.autoUpdate = !0;\n  }\n  _render(renderer) {\n    const geometry = this.geometry;\n    (this.autoUpdate || geometry._width !== this.shader.texture.height) && (geometry._width = this.shader.texture.height, geometry.update()), super._render(renderer);\n  }\n}\nexport { SimpleRope };", "map": {"version": 3, "names": ["SimpleRope", "<PERSON><PERSON>", "constructor", "texture", "points", "textureScale", "ropeGeometry", "RopeGeometry", "height", "meshMaterial", "MeshMaterial", "baseTexture", "wrapMode", "WRAP_MODES", "REPEAT", "autoUpdate", "_render", "renderer", "geometry", "_width", "shader", "update"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\mesh-extras\\src\\SimpleRope.ts"], "sourcesContent": ["import { WRAP_MODES } from '@pixi/core';\nimport { Mesh, MeshMaterial } from '@pixi/mesh';\nimport { RopeGeometry } from './geometry/RopeGeometry';\n\nimport type { IPoint, Renderer, Texture } from '@pixi/core';\n\n/**\n * The rope allows you to draw a texture across several points and then manipulate these points\n * @example\n * import { Point, SimpleRope, Texture } from 'pixi.js';\n *\n * for (let i = 0; i < 20; i++) {\n *     points.push(new Point(i * 50, 0));\n * };\n * const rope = new SimpleRope(Texture.from('snake.png'), points);\n * @memberof PIXI\n */\nexport class SimpleRope extends Mesh\n{\n    public autoUpdate: boolean;\n\n    /**\n     * Note: The wrap mode of the texture is set to REPEAT if `textureScale` is positive.\n     * @param texture - The texture to use on the rope.\n     * @param points - An array of {@link PIXI.Point} objects to construct this rope.\n     * @param {number} textureScale - Optional. Positive values scale rope texture\n     * keeping its aspect ratio. You can reduce alpha channel artifacts by providing a larger texture\n     * and downsampling here. If set to zero, texture will be stretched instead.\n     */\n    constructor(texture: Texture, points: IPoint[], textureScale = 0)\n    {\n        const ropeGeometry = new RopeGeometry(texture.height, points, textureScale);\n        const meshMaterial = new MeshMaterial(texture);\n\n        if (textureScale > 0)\n        {\n            // attempt to set UV wrapping, will fail on non-power of two textures\n            texture.baseTexture.wrapMode = WRAP_MODES.REPEAT;\n        }\n        super(ropeGeometry, meshMaterial);\n\n        /**\n         * re-calculate vertices by rope points each frame\n         * @member {boolean}\n         */\n        this.autoUpdate = true;\n    }\n\n    _render(renderer: Renderer): void\n    {\n        const geometry: RopeGeometry = this.geometry as any;\n\n        if (this.autoUpdate || geometry._width !== this.shader.texture.height)\n        {\n            geometry._width = this.shader.texture.height;\n            geometry.update();\n        }\n\n        super._render(renderer);\n    }\n}\n"], "mappings": ";;;AAiBO,MAAMA,UAAA,SAAmBC,IAAA,CAChC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWIC,YAAYC,OAAA,EAAkBC,MAAA,EAAkBC,YAAA,GAAe,GAC/D;IACU,MAAAC,YAAA,GAAe,IAAIC,YAAA,CAAaJ,OAAA,CAAQK,MAAA,EAAQJ,MAAA,EAAQC,YAAY;MACpEI,YAAA,GAAe,IAAIC,YAAA,CAAaP,OAAO;IAEzCE,YAAA,GAAe,MAGfF,OAAA,CAAQQ,WAAA,CAAYC,QAAA,GAAWC,UAAA,CAAWC,MAAA,GAE9C,MAAMR,YAAA,EAAcG,YAAY,GAMhC,KAAKM,UAAA,GAAa;EACtB;EAEAC,QAAQC,QAAA,EACR;IACI,MAAMC,QAAA,GAAyB,KAAKA,QAAA;IAEpC,CAAI,KAAKH,UAAA,IAAcG,QAAA,CAASC,MAAA,KAAW,KAAKC,MAAA,CAAOjB,OAAA,CAAQK,MAAA,MAE3DU,QAAA,CAASC,MAAA,GAAS,KAAKC,MAAA,CAAOjB,OAAA,CAAQK,MAAA,EACtCU,QAAA,CAASG,MAAA,CAGb,UAAML,OAAA,CAAQC,QAAQ;EAC1B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}