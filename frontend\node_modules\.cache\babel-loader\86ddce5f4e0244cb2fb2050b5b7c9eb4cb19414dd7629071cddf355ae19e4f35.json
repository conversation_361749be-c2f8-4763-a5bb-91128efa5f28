{"ast": null, "code": "import { ExtensionType, extensions } from \"@pixi/core\";\nimport { EventBoundary } from \"./EventBoundary.mjs\";\nimport { EventsTicker } from \"./EventTicker.mjs\";\nimport { FederatedPointerEvent } from \"./FederatedPointerEvent.mjs\";\nimport { FederatedWheelEvent } from \"./FederatedWheelEvent.mjs\";\nconst MOUSE_POINTER_ID = 1,\n  TOUCH_TO_POINTER = {\n    touchstart: \"pointerdown\",\n    touchend: \"pointerup\",\n    touchendoutside: \"pointerupoutside\",\n    touchmove: \"pointermove\",\n    touchcancel: \"pointercancel\"\n  },\n  _EventSystem = class _EventSystem2 {\n    /**\n     * @param {PIXI.Renderer} renderer\n     */\n    constructor(renderer) {\n      this.supportsTouchEvents = \"ontouchstart\" in globalThis, this.supportsPointerEvents = !!globalThis.PointerEvent, this.domElement = null, this.resolution = 1, this.renderer = renderer, this.rootBoundary = new EventBoundary(null), EventsTicker.init(this), this.autoPreventDefault = !0, this.eventsAdded = !1, this.rootPointerEvent = new FederatedPointerEvent(null), this.rootWheelEvent = new FederatedWheelEvent(null), this.cursorStyles = {\n        default: \"inherit\",\n        pointer: \"pointer\"\n      }, this.features = new Proxy({\n        ..._EventSystem2.defaultEventFeatures\n      }, {\n        set: (target, key, value) => (key === \"globalMove\" && (this.rootBoundary.enableGlobalMoveEvents = value), target[key] = value, !0)\n      }), this.onPointerDown = this.onPointerDown.bind(this), this.onPointerMove = this.onPointerMove.bind(this), this.onPointerUp = this.onPointerUp.bind(this), this.onPointerOverOut = this.onPointerOverOut.bind(this), this.onWheel = this.onWheel.bind(this);\n    }\n    /**\n     * The default interaction mode for all display objects.\n     * @see PIXI.DisplayObject.eventMode\n     * @type {PIXI.EventMode}\n     * @readonly\n     * @since 7.2.0\n     */\n    static get defaultEventMode() {\n      return this._defaultEventMode;\n    }\n    /**\n     * Runner init called, view is available at this point.\n     * @ignore\n     */\n    init(options) {\n      const {\n        view,\n        resolution\n      } = this.renderer;\n      this.setTargetElement(view), this.resolution = resolution, _EventSystem2._defaultEventMode = options.eventMode ?? \"auto\", Object.assign(this.features, options.eventFeatures ?? {}), this.rootBoundary.enableGlobalMoveEvents = this.features.globalMove;\n    }\n    /**\n     * Handle changing resolution.\n     * @ignore\n     */\n    resolutionChange(resolution) {\n      this.resolution = resolution;\n    }\n    /** Destroys all event listeners and detaches the renderer. */\n    destroy() {\n      this.setTargetElement(null), this.renderer = null;\n    }\n    /**\n     * Sets the current cursor mode, handling any callbacks or CSS style changes.\n     * @param mode - cursor mode, a key from the cursorStyles dictionary\n     */\n    setCursor(mode) {\n      mode = mode || \"default\";\n      let applyStyles = !0;\n      if (globalThis.OffscreenCanvas && this.domElement instanceof OffscreenCanvas && (applyStyles = !1), this.currentCursor === mode) return;\n      this.currentCursor = mode;\n      const style = this.cursorStyles[mode];\n      if (style) switch (typeof style) {\n        case \"string\":\n          applyStyles && (this.domElement.style.cursor = style);\n          break;\n        case \"function\":\n          style(mode);\n          break;\n        case \"object\":\n          applyStyles && Object.assign(this.domElement.style, style);\n          break;\n      } else applyStyles && typeof mode == \"string\" && !Object.prototype.hasOwnProperty.call(this.cursorStyles, mode) && (this.domElement.style.cursor = mode);\n    }\n    /**\n     * The global pointer event.\n     * Useful for getting the pointer position without listening to events.\n     * @since 7.2.0\n     */\n    get pointer() {\n      return this.rootPointerEvent;\n    }\n    /**\n     * Event handler for pointer down events on {@link PIXI.EventSystem#domElement this.domElement}.\n     * @param nativeEvent - The native mouse/pointer/touch event.\n     */\n    onPointerDown(nativeEvent) {\n      if (!this.features.click) return;\n      this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n      const events = this.normalizeToPointerData(nativeEvent);\n      this.autoPreventDefault && events[0].isNormalized && (nativeEvent.cancelable || !(\"cancelable\" in nativeEvent)) && nativeEvent.preventDefault();\n      for (let i = 0, j = events.length; i < j; i++) {\n        const nativeEvent2 = events[i],\n          federatedEvent = this.bootstrapEvent(this.rootPointerEvent, nativeEvent2);\n        this.rootBoundary.mapEvent(federatedEvent);\n      }\n      this.setCursor(this.rootBoundary.cursor);\n    }\n    /**\n     * Event handler for pointer move events on on {@link PIXI.EventSystem#domElement this.domElement}.\n     * @param nativeEvent - The native mouse/pointer/touch events.\n     */\n    onPointerMove(nativeEvent) {\n      if (!this.features.move) return;\n      this.rootBoundary.rootTarget = this.renderer.lastObjectRendered, EventsTicker.pointerMoved();\n      const normalizedEvents = this.normalizeToPointerData(nativeEvent);\n      for (let i = 0, j = normalizedEvents.length; i < j; i++) {\n        const event = this.bootstrapEvent(this.rootPointerEvent, normalizedEvents[i]);\n        this.rootBoundary.mapEvent(event);\n      }\n      this.setCursor(this.rootBoundary.cursor);\n    }\n    /**\n     * Event handler for pointer up events on {@link PIXI.EventSystem#domElement this.domElement}.\n     * @param nativeEvent - The native mouse/pointer/touch event.\n     */\n    onPointerUp(nativeEvent) {\n      if (!this.features.click) return;\n      this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n      let target = nativeEvent.target;\n      nativeEvent.composedPath && nativeEvent.composedPath().length > 0 && (target = nativeEvent.composedPath()[0]);\n      const outside = target !== this.domElement ? \"outside\" : \"\",\n        normalizedEvents = this.normalizeToPointerData(nativeEvent);\n      for (let i = 0, j = normalizedEvents.length; i < j; i++) {\n        const event = this.bootstrapEvent(this.rootPointerEvent, normalizedEvents[i]);\n        event.type += outside, this.rootBoundary.mapEvent(event);\n      }\n      this.setCursor(this.rootBoundary.cursor);\n    }\n    /**\n     * Event handler for pointer over & out events on {@link PIXI.EventSystem#domElement this.domElement}.\n     * @param nativeEvent - The native mouse/pointer/touch event.\n     */\n    onPointerOverOut(nativeEvent) {\n      if (!this.features.click) return;\n      this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n      const normalizedEvents = this.normalizeToPointerData(nativeEvent);\n      for (let i = 0, j = normalizedEvents.length; i < j; i++) {\n        const event = this.bootstrapEvent(this.rootPointerEvent, normalizedEvents[i]);\n        this.rootBoundary.mapEvent(event);\n      }\n      this.setCursor(this.rootBoundary.cursor);\n    }\n    /**\n     * Passive handler for `wheel` events on {@link PIXI.EventSystem.domElement this.domElement}.\n     * @param nativeEvent - The native wheel event.\n     */\n    onWheel(nativeEvent) {\n      if (!this.features.wheel) return;\n      const wheelEvent = this.normalizeWheelEvent(nativeEvent);\n      this.rootBoundary.rootTarget = this.renderer.lastObjectRendered, this.rootBoundary.mapEvent(wheelEvent);\n    }\n    /**\n     * Sets the {@link PIXI.EventSystem#domElement domElement} and binds event listeners.\n     *\n     * To deregister the current DOM element without setting a new one, pass {@code null}.\n     * @param element - The new DOM element.\n     */\n    setTargetElement(element) {\n      this.removeEvents(), this.domElement = element, EventsTicker.domElement = element, this.addEvents();\n    }\n    /** Register event listeners on {@link PIXI.Renderer#domElement this.domElement}. */\n    addEvents() {\n      if (this.eventsAdded || !this.domElement) return;\n      EventsTicker.addTickerListener();\n      const style = this.domElement.style;\n      style && (globalThis.navigator.msPointerEnabled ? (style.msContentZooming = \"none\", style.msTouchAction = \"none\") : this.supportsPointerEvents && (style.touchAction = \"none\")), this.supportsPointerEvents ? (globalThis.document.addEventListener(\"pointermove\", this.onPointerMove, !0), this.domElement.addEventListener(\"pointerdown\", this.onPointerDown, !0), this.domElement.addEventListener(\"pointerleave\", this.onPointerOverOut, !0), this.domElement.addEventListener(\"pointerover\", this.onPointerOverOut, !0), globalThis.addEventListener(\"pointerup\", this.onPointerUp, !0)) : (globalThis.document.addEventListener(\"mousemove\", this.onPointerMove, !0), this.domElement.addEventListener(\"mousedown\", this.onPointerDown, !0), this.domElement.addEventListener(\"mouseout\", this.onPointerOverOut, !0), this.domElement.addEventListener(\"mouseover\", this.onPointerOverOut, !0), globalThis.addEventListener(\"mouseup\", this.onPointerUp, !0), this.supportsTouchEvents && (this.domElement.addEventListener(\"touchstart\", this.onPointerDown, !0), this.domElement.addEventListener(\"touchend\", this.onPointerUp, !0), this.domElement.addEventListener(\"touchmove\", this.onPointerMove, !0))), this.domElement.addEventListener(\"wheel\", this.onWheel, {\n        passive: !0,\n        capture: !0\n      }), this.eventsAdded = !0;\n    }\n    /** Unregister event listeners on {@link PIXI.EventSystem#domElement this.domElement}. */\n    removeEvents() {\n      if (!this.eventsAdded || !this.domElement) return;\n      EventsTicker.removeTickerListener();\n      const style = this.domElement.style;\n      globalThis.navigator.msPointerEnabled ? (style.msContentZooming = \"\", style.msTouchAction = \"\") : this.supportsPointerEvents && (style.touchAction = \"\"), this.supportsPointerEvents ? (globalThis.document.removeEventListener(\"pointermove\", this.onPointerMove, !0), this.domElement.removeEventListener(\"pointerdown\", this.onPointerDown, !0), this.domElement.removeEventListener(\"pointerleave\", this.onPointerOverOut, !0), this.domElement.removeEventListener(\"pointerover\", this.onPointerOverOut, !0), globalThis.removeEventListener(\"pointerup\", this.onPointerUp, !0)) : (globalThis.document.removeEventListener(\"mousemove\", this.onPointerMove, !0), this.domElement.removeEventListener(\"mousedown\", this.onPointerDown, !0), this.domElement.removeEventListener(\"mouseout\", this.onPointerOverOut, !0), this.domElement.removeEventListener(\"mouseover\", this.onPointerOverOut, !0), globalThis.removeEventListener(\"mouseup\", this.onPointerUp, !0), this.supportsTouchEvents && (this.domElement.removeEventListener(\"touchstart\", this.onPointerDown, !0), this.domElement.removeEventListener(\"touchend\", this.onPointerUp, !0), this.domElement.removeEventListener(\"touchmove\", this.onPointerMove, !0))), this.domElement.removeEventListener(\"wheel\", this.onWheel, !0), this.domElement = null, this.eventsAdded = !1;\n    }\n    /**\n     * Maps x and y coords from a DOM object and maps them correctly to the PixiJS view. The\n     * resulting value is stored in the point. This takes into account the fact that the DOM\n     * element could be scaled and positioned anywhere on the screen.\n     * @param  {PIXI.IPointData} point - the point that the result will be stored in\n     * @param  {number} x - the x coord of the position to map\n     * @param  {number} y - the y coord of the position to map\n     */\n    mapPositionToPoint(point, x, y) {\n      const rect = this.domElement.isConnected ? this.domElement.getBoundingClientRect() : {\n          x: 0,\n          y: 0,\n          width: this.domElement.width,\n          height: this.domElement.height,\n          left: 0,\n          top: 0\n        },\n        resolutionMultiplier = 1 / this.resolution;\n      point.x = (x - rect.left) * (this.domElement.width / rect.width) * resolutionMultiplier, point.y = (y - rect.top) * (this.domElement.height / rect.height) * resolutionMultiplier;\n    }\n    /**\n     * Ensures that the original event object contains all data that a regular pointer event would have\n     * @param event - The original event data from a touch or mouse event\n     * @returns An array containing a single normalized pointer event, in the case of a pointer\n     *  or mouse event, or a multiple normalized pointer events if there are multiple changed touches\n     */\n    normalizeToPointerData(event) {\n      const normalizedEvents = [];\n      if (this.supportsTouchEvents && event instanceof TouchEvent) for (let i = 0, li = event.changedTouches.length; i < li; i++) {\n        const touch = event.changedTouches[i];\n        typeof touch.button > \"u\" && (touch.button = 0), typeof touch.buttons > \"u\" && (touch.buttons = 1), typeof touch.isPrimary > \"u\" && (touch.isPrimary = event.touches.length === 1 && event.type === \"touchstart\"), typeof touch.width > \"u\" && (touch.width = touch.radiusX || 1), typeof touch.height > \"u\" && (touch.height = touch.radiusY || 1), typeof touch.tiltX > \"u\" && (touch.tiltX = 0), typeof touch.tiltY > \"u\" && (touch.tiltY = 0), typeof touch.pointerType > \"u\" && (touch.pointerType = \"touch\"), typeof touch.pointerId > \"u\" && (touch.pointerId = touch.identifier || 0), typeof touch.pressure > \"u\" && (touch.pressure = touch.force || 0.5), typeof touch.twist > \"u\" && (touch.twist = 0), typeof touch.tangentialPressure > \"u\" && (touch.tangentialPressure = 0), typeof touch.layerX > \"u\" && (touch.layerX = touch.offsetX = touch.clientX), typeof touch.layerY > \"u\" && (touch.layerY = touch.offsetY = touch.clientY), touch.isNormalized = !0, touch.type = event.type, normalizedEvents.push(touch);\n      } else if (!globalThis.MouseEvent || event instanceof MouseEvent && (!this.supportsPointerEvents || !(event instanceof globalThis.PointerEvent))) {\n        const tempEvent = event;\n        typeof tempEvent.isPrimary > \"u\" && (tempEvent.isPrimary = !0), typeof tempEvent.width > \"u\" && (tempEvent.width = 1), typeof tempEvent.height > \"u\" && (tempEvent.height = 1), typeof tempEvent.tiltX > \"u\" && (tempEvent.tiltX = 0), typeof tempEvent.tiltY > \"u\" && (tempEvent.tiltY = 0), typeof tempEvent.pointerType > \"u\" && (tempEvent.pointerType = \"mouse\"), typeof tempEvent.pointerId > \"u\" && (tempEvent.pointerId = MOUSE_POINTER_ID), typeof tempEvent.pressure > \"u\" && (tempEvent.pressure = 0.5), typeof tempEvent.twist > \"u\" && (tempEvent.twist = 0), typeof tempEvent.tangentialPressure > \"u\" && (tempEvent.tangentialPressure = 0), tempEvent.isNormalized = !0, normalizedEvents.push(tempEvent);\n      } else normalizedEvents.push(event);\n      return normalizedEvents;\n    }\n    /**\n     * Normalizes the native {@link https://w3c.github.io/uievents/#interface-wheelevent WheelEvent}.\n     *\n     * The returned {@link PIXI.FederatedWheelEvent} is a shared instance. It will not persist across\n     * multiple native wheel events.\n     * @param nativeEvent - The native wheel event that occurred on the canvas.\n     * @returns A federated wheel event.\n     */\n    normalizeWheelEvent(nativeEvent) {\n      const event = this.rootWheelEvent;\n      return this.transferMouseData(event, nativeEvent), event.deltaX = nativeEvent.deltaX, event.deltaY = nativeEvent.deltaY, event.deltaZ = nativeEvent.deltaZ, event.deltaMode = nativeEvent.deltaMode, this.mapPositionToPoint(event.screen, nativeEvent.clientX, nativeEvent.clientY), event.global.copyFrom(event.screen), event.offset.copyFrom(event.screen), event.nativeEvent = nativeEvent, event.type = nativeEvent.type, event;\n    }\n    /**\n     * Normalizes the `nativeEvent` into a federateed {@link PIXI.FederatedPointerEvent}.\n     * @param event\n     * @param nativeEvent\n     */\n    bootstrapEvent(event, nativeEvent) {\n      return event.originalEvent = null, event.nativeEvent = nativeEvent, event.pointerId = nativeEvent.pointerId, event.width = nativeEvent.width, event.height = nativeEvent.height, event.isPrimary = nativeEvent.isPrimary, event.pointerType = nativeEvent.pointerType, event.pressure = nativeEvent.pressure, event.tangentialPressure = nativeEvent.tangentialPressure, event.tiltX = nativeEvent.tiltX, event.tiltY = nativeEvent.tiltY, event.twist = nativeEvent.twist, this.transferMouseData(event, nativeEvent), this.mapPositionToPoint(event.screen, nativeEvent.clientX, nativeEvent.clientY), event.global.copyFrom(event.screen), event.offset.copyFrom(event.screen), event.isTrusted = nativeEvent.isTrusted, event.type === \"pointerleave\" && (event.type = \"pointerout\"), event.type.startsWith(\"mouse\") && (event.type = event.type.replace(\"mouse\", \"pointer\")), event.type.startsWith(\"touch\") && (event.type = TOUCH_TO_POINTER[event.type] || event.type), event;\n    }\n    /**\n     * Transfers base & mouse event data from the {@code nativeEvent} to the federated event.\n     * @param event\n     * @param nativeEvent\n     */\n    transferMouseData(event, nativeEvent) {\n      event.isTrusted = nativeEvent.isTrusted, event.srcElement = nativeEvent.srcElement, event.timeStamp = performance.now(), event.type = nativeEvent.type, event.altKey = nativeEvent.altKey, event.button = nativeEvent.button, event.buttons = nativeEvent.buttons, event.client.x = nativeEvent.clientX, event.client.y = nativeEvent.clientY, event.ctrlKey = nativeEvent.ctrlKey, event.metaKey = nativeEvent.metaKey, event.movement.x = nativeEvent.movementX, event.movement.y = nativeEvent.movementY, event.page.x = nativeEvent.pageX, event.page.y = nativeEvent.pageY, event.relatedTarget = null, event.shiftKey = nativeEvent.shiftKey;\n    }\n  };\n_EventSystem.extension = {\n  name: \"events\",\n  type: [ExtensionType.RendererSystem, ExtensionType.CanvasRendererSystem]\n},\n/**\n* The event features that are enabled by the EventSystem\n* This option only is available when using **@pixi/events** package\n* (included in the **pixi.js** and **pixi.js-legacy** bundle), otherwise it will be ignored.\n* @since 7.2.0\n*/\n_EventSystem.defaultEventFeatures = {\n  move: !0,\n  globalMove: !0,\n  click: !0,\n  wheel: !0\n};\nlet EventSystem = _EventSystem;\nextensions.add(EventSystem);\nexport { EventSystem };", "map": {"version": 3, "names": ["MOUSE_POINTER_ID", "TOUCH_TO_POINTER", "touchstart", "touchend", "touchendoutside", "touchmove", "touchcancel", "_EventSystem", "_EventSystem2", "constructor", "renderer", "supportsTouchEvents", "globalThis", "supportsPointerEvents", "PointerEvent", "dom<PERSON>lement", "resolution", "rootBoundary", "EventBoundary", "EventsTicker", "init", "autoPreventDefault", "eventsAdded", "rootPointerEvent", "FederatedPointerEvent", "rootWheelEvent", "FederatedWheelEvent", "cursorStyles", "default", "pointer", "features", "Proxy", "defaultEventFeatures", "set", "target", "key", "value", "enableGlobalMoveEvents", "onPointerDown", "bind", "onPointerMove", "onPointerUp", "onPointerOverOut", "onWheel", "defaultEventMode", "_defaultEventMode", "options", "view", "setTargetElement", "eventMode", "Object", "assign", "eventFeatures", "globalMove", "resolutionChange", "destroy", "setCursor", "mode", "applyStyles", "OffscreenCanvas", "currentCursor", "style", "cursor", "prototype", "hasOwnProperty", "call", "nativeEvent", "click", "rootTarget", "lastObjectRendered", "events", "normalizeToPointerData", "isNormalized", "cancelable", "preventDefault", "i", "j", "length", "nativeEvent2", "federatedEvent", "bootstrapEvent", "mapEvent", "move", "pointerMoved", "normalizedEvents", "event", "<PERSON><PERSON><PERSON>", "outside", "type", "wheel", "wheelEvent", "normalizeWheelEvent", "element", "removeEvents", "addEvents", "addTickerListener", "navigator", "msPointer<PERSON><PERSON><PERSON>", "msContentZooming", "msTouchAction", "touchAction", "document", "addEventListener", "passive", "capture", "removeTickerListener", "removeEventListener", "mapPositionToPoint", "point", "x", "y", "rect", "isConnected", "getBoundingClientRect", "width", "height", "left", "top", "resolutionMultiplier", "TouchEvent", "li", "changedTouches", "touch", "button", "buttons", "isPrimary", "touches", "radiusX", "radiusY", "tiltX", "tiltY", "pointerType", "pointerId", "identifier", "pressure", "force", "twist", "tangentialPressure", "layerX", "offsetX", "clientX", "layerY", "offsetY", "clientY", "push", "MouseEvent", "tempEvent", "transferMouseData", "deltaX", "deltaY", "deltaZ", "deltaMode", "screen", "global", "copyFrom", "offset", "originalEvent", "isTrusted", "startsWith", "replace", "srcElement", "timeStamp", "performance", "now", "altKey", "client", "ctrl<PERSON>ey", "metaKey", "movement", "movementX", "movementY", "page", "pageX", "pageY", "relatedTarget", "shift<PERSON>ey", "extension", "name", "ExtensionType", "RendererSystem", "CanvasRendererSystem", "EventSystem", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\events\\src\\EventSystem.ts"], "sourcesContent": ["import { extensions, ExtensionType } from '@pixi/core';\nimport { EventBoundary } from './EventBoundary';\nimport { EventsTicker } from './EventTicker';\nimport { FederatedPointerEvent } from './FederatedPointerEvent';\nimport { FederatedWheelEvent } from './FederatedWheelEvent';\n\nimport type { ExtensionMetadata, IPointData, IRenderer, ISystem } from '@pixi/core';\nimport type { DisplayObject } from '@pixi/display';\nimport type { PixiTouch } from './FederatedEvent';\nimport type { EventMode } from './FederatedEventTarget';\nimport type { FederatedMouseEvent } from './FederatedMouseEvent';\n\nconst MOUSE_POINTER_ID = 1;\nconst TOUCH_TO_POINTER: Record<string, string> = {\n    touchstart: 'pointerdown',\n    touchend: 'pointerup',\n    touchendoutside: 'pointerupoutside',\n    touchmove: 'pointermove',\n    touchcancel: 'pointercancel',\n};\n\n/** @ignore */\nexport interface EventSystemOptions\n{\n    /**\n     * The default event mode mode for all display objects.\n     * This option only is available when using **@pixi/events** package\n     * (included in the **pixi.js** and **pixi.js-legacy** bundle), otherwise it will be ignored.\n     * @memberof PIXI.IRendererOptions\n     */\n    eventMode?: EventMode;\n\n    /**\n     * The event features that are enabled by the EventSystem\n     * This option only is available when using **@pixi/events** package\n     * (included in the **pixi.js** and **pixi.js-legacy** bundle), otherwise it will be ignored.\n     * @memberof PIXI.IRendererOptions\n     * @example\n     * const app = new PIXI.Application({\n     *   view: canvas,\n     *   events: {\n     *     move: true,\n     *     globalMove: false,\n     *     click: true,\n     *     wheel: true,\n     *   },\n     * });\n     */\n    eventFeatures?: Partial<EventSystemFeatures>\n}\n\n/**\n * The event features that are enabled by the EventSystem\n * This option only is available when using **@pixi/events** package\n * (included in the **pixi.js** and **pixi.js-legacy** bundle), otherwise it will be ignored.\n * @memberof PIXI\n * @since 7.2.0\n */\ninterface EventSystemFeatures\n{\n    /**\n     * Enables pointer events associated with pointer movement:\n     * - `pointermove` / `mousemove` / `touchmove`\n     * - `pointerout` / `mouseout`\n     * - `pointerover` / `mouseover`\n     */\n    move: boolean;\n    // eslint-disable-next-line jsdoc/multiline-blocks\n    /**\n     * Enables global pointer move events:\n     * - `globalpointermove`\n     * - `globalmousemove`\n     * - `globaltouchemove`\n     */\n    globalMove: boolean;\n    /**\n     * Enables pointer events associated with clicking:\n     * - `pointerup` / `mouseup` / `touchend` / 'rightup'\n     * - `pointerupoutside` / `mouseupoutside` / `touchendoutside` / 'rightupoutside'\n     * - `pointerdown` / 'mousedown' / `touchstart` / 'rightdown'\n     * - `click` / `tap`\n     */\n    click: boolean;\n    /** - Enables wheel events. */\n    wheel: boolean;\n}\n\n/**\n * The system for handling UI events.\n * @memberof PIXI\n */\nexport class EventSystem implements ISystem<EventSystemOptions>\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'events',\n        type: [\n            ExtensionType.RendererSystem,\n            ExtensionType.CanvasRendererSystem\n        ],\n    };\n\n    /**\n     * The event features that are enabled by the EventSystem\n     * This option only is available when using **@pixi/events** package\n     * (included in the **pixi.js** and **pixi.js-legacy** bundle), otherwise it will be ignored.\n     * @since 7.2.0\n     */\n    public static defaultEventFeatures: EventSystemFeatures = {\n        move: true,\n        globalMove: true,\n        click: true,\n        wheel: true,\n    };\n\n    private static _defaultEventMode: EventMode;\n\n    /**\n     * The default interaction mode for all display objects.\n     * @see PIXI.DisplayObject.eventMode\n     * @type {PIXI.EventMode}\n     * @readonly\n     * @since 7.2.0\n     */\n    public static get defaultEventMode()\n    {\n        return this._defaultEventMode;\n    }\n\n    /**\n     * The {@link PIXI.EventBoundary} for the stage.\n     *\n     * The {@link PIXI.EventBoundary#rootTarget rootTarget} of this root boundary is automatically set to\n     * the last rendered object before any event processing is initiated. This means the main scene\n     * needs to be rendered atleast once before UI events will start propagating.\n     *\n     * The root boundary should only be changed during initialization. Otherwise, any state held by the\n     * event boundary may be lost (like hovered & pressed DisplayObjects).\n     */\n    public readonly rootBoundary: EventBoundary;\n\n    /** Does the device support touch events https://www.w3.org/TR/touch-events/ */\n    public readonly supportsTouchEvents = 'ontouchstart' in globalThis;\n\n    /** Does the device support pointer events https://www.w3.org/Submission/pointer-events/ */\n    public readonly supportsPointerEvents = !!globalThis.PointerEvent;\n\n    /**\n     * Should default browser actions automatically be prevented.\n     * Does not apply to pointer events for backwards compatibility\n     * preventDefault on pointer events stops mouse events from firing\n     * Thus, for every pointer event, there will always be either a mouse of touch event alongside it.\n     * @default true\n     */\n    public autoPreventDefault: boolean;\n\n    /**\n     * Dictionary of how different cursor modes are handled. Strings are handled as CSS cursor\n     * values, objects are handled as dictionaries of CSS values for {@code domElement},\n     * and functions are called instead of changing the CSS.\n     * Default CSS cursor values are provided for 'default' and 'pointer' modes.\n     */\n    public cursorStyles: Record<string, string | ((mode: string) => void) | CSSStyleDeclaration>;\n\n    /**\n     * The DOM element to which the root event listeners are bound. This is automatically set to\n     * the renderer's {@link PIXI.Renderer#view view}.\n     */\n    public domElement: HTMLElement = null;\n\n    /** The resolution used to convert between the DOM client space into world space. */\n    public resolution = 1;\n\n    /** The renderer managing this {@link PIXI.EventSystem}. */\n    public renderer: IRenderer;\n\n    /**\n     * The event features that are enabled by the EventSystem\n     * This option only is available when using **@pixi/events** package\n     * (included in the **pixi.js** and **pixi.js-legacy** bundle), otherwise it will be ignored.\n     * @since 7.2.0\n     * @example\n     * const app = new PIXI.Application()\n     * app.renderer.events.features.globalMove = false\n     *\n     * // to override all features use Object.assign\n     * Object.assign(app.renderer.events.features, {\n     *  move: false,\n     *  globalMove: false,\n     *  click: false,\n     *  wheel: false,\n     * })\n     */\n    public readonly features: EventSystemFeatures;\n\n    private currentCursor: string;\n    private rootPointerEvent: FederatedPointerEvent;\n    private rootWheelEvent: FederatedWheelEvent;\n    private eventsAdded: boolean;\n\n    /**\n     * @param {PIXI.Renderer} renderer\n     */\n    constructor(renderer: IRenderer)\n    {\n        this.renderer = renderer;\n        this.rootBoundary = new EventBoundary(null);\n        EventsTicker.init(this);\n\n        this.autoPreventDefault = true;\n        this.eventsAdded = false;\n\n        this.rootPointerEvent = new FederatedPointerEvent(null);\n        this.rootWheelEvent = new FederatedWheelEvent(null);\n\n        this.cursorStyles = {\n            default: 'inherit',\n            pointer: 'pointer',\n        };\n\n        this.features = new Proxy({ ...EventSystem.defaultEventFeatures }, {\n            set: (target, key, value) =>\n            {\n                if (key === 'globalMove')\n                {\n                    this.rootBoundary.enableGlobalMoveEvents = value;\n                }\n                target[key as keyof EventSystemFeatures] = value;\n\n                return true;\n            }\n        });\n\n        this.onPointerDown = this.onPointerDown.bind(this);\n        this.onPointerMove = this.onPointerMove.bind(this);\n        this.onPointerUp = this.onPointerUp.bind(this);\n        this.onPointerOverOut = this.onPointerOverOut.bind(this);\n        this.onWheel = this.onWheel.bind(this);\n    }\n\n    /**\n     * Runner init called, view is available at this point.\n     * @ignore\n     */\n    init(options: EventSystemOptions): void\n    {\n        const { view, resolution } = this.renderer;\n\n        this.setTargetElement(view as HTMLCanvasElement);\n        this.resolution = resolution;\n        EventSystem._defaultEventMode = options.eventMode ?? 'auto';\n        Object.assign(this.features, options.eventFeatures ?? {});\n        this.rootBoundary.enableGlobalMoveEvents = this.features.globalMove;\n    }\n\n    /**\n     * Handle changing resolution.\n     * @ignore\n     */\n    resolutionChange(resolution: number): void\n    {\n        this.resolution = resolution;\n    }\n\n    /** Destroys all event listeners and detaches the renderer. */\n    destroy(): void\n    {\n        this.setTargetElement(null);\n        this.renderer = null;\n    }\n\n    /**\n     * Sets the current cursor mode, handling any callbacks or CSS style changes.\n     * @param mode - cursor mode, a key from the cursorStyles dictionary\n     */\n    public setCursor(mode: string): void\n    {\n        mode = mode || 'default';\n        let applyStyles = true;\n\n        // offscreen canvas does not support setting styles, but cursor modes can be functions,\n        // in order to handle pixi rendered cursors, so we can't bail\n        if (globalThis.OffscreenCanvas && this.domElement instanceof OffscreenCanvas)\n        {\n            applyStyles = false;\n        }\n        // if the mode didn't actually change, bail early\n        if (this.currentCursor === mode)\n        {\n            return;\n        }\n        this.currentCursor = mode;\n        const style = this.cursorStyles[mode];\n\n        // only do things if there is a cursor style for it\n        if (style)\n        {\n            switch (typeof style)\n            {\n                case 'string':\n                    // string styles are handled as cursor CSS\n                    if (applyStyles)\n                    {\n                        this.domElement.style.cursor = style;\n                    }\n                    break;\n                case 'function':\n                    // functions are just called, and passed the cursor mode\n                    style(mode);\n                    break;\n                case 'object':\n                    // if it is an object, assume that it is a dictionary of CSS styles,\n                    // apply it to the interactionDOMElement\n                    if (applyStyles)\n                    {\n                        Object.assign(this.domElement.style, style);\n                    }\n                    break;\n            }\n        }\n        else if (applyStyles && typeof mode === 'string' && !Object.prototype.hasOwnProperty.call(this.cursorStyles, mode))\n        {\n            // if it mode is a string (not a Symbol) and cursorStyles doesn't have any entry\n            // for the mode, then assume that the dev wants it to be CSS for the cursor.\n            this.domElement.style.cursor = mode;\n        }\n    }\n\n    /**\n     * The global pointer event.\n     * Useful for getting the pointer position without listening to events.\n     * @since 7.2.0\n     */\n    public get pointer(): Readonly<FederatedPointerEvent>\n    {\n        return this.rootPointerEvent;\n    }\n\n    /**\n     * Event handler for pointer down events on {@link PIXI.EventSystem#domElement this.domElement}.\n     * @param nativeEvent - The native mouse/pointer/touch event.\n     */\n    private onPointerDown(nativeEvent: MouseEvent | PointerEvent | TouchEvent): void\n    {\n        if (!this.features.click) return;\n        this.rootBoundary.rootTarget = this.renderer.lastObjectRendered as DisplayObject;\n\n        const events = this.normalizeToPointerData(nativeEvent);\n\n        /*\n         * No need to prevent default on natural pointer events, as there are no side effects\n         * Normalized events, however, may have the double mousedown/touchstart issue on the native android browser,\n         * so still need to be prevented.\n         */\n\n        // Guaranteed that there will be at least one event in events, and all events must have the same pointer type\n\n        if (this.autoPreventDefault && (events[0] as any).isNormalized)\n        {\n            const cancelable = nativeEvent.cancelable || !('cancelable' in nativeEvent);\n\n            if (cancelable)\n            {\n                nativeEvent.preventDefault();\n            }\n        }\n\n        for (let i = 0, j = events.length; i < j; i++)\n        {\n            const nativeEvent = events[i];\n            const federatedEvent = this.bootstrapEvent(this.rootPointerEvent, nativeEvent);\n\n            this.rootBoundary.mapEvent(federatedEvent);\n        }\n\n        this.setCursor(this.rootBoundary.cursor);\n    }\n\n    /**\n     * Event handler for pointer move events on on {@link PIXI.EventSystem#domElement this.domElement}.\n     * @param nativeEvent - The native mouse/pointer/touch events.\n     */\n    private onPointerMove(nativeEvent: MouseEvent | PointerEvent | TouchEvent): void\n    {\n        if (!this.features.move) return;\n        this.rootBoundary.rootTarget = this.renderer.lastObjectRendered as DisplayObject;\n\n        EventsTicker.pointerMoved();\n\n        const normalizedEvents = this.normalizeToPointerData(nativeEvent);\n\n        for (let i = 0, j = normalizedEvents.length; i < j; i++)\n        {\n            const event = this.bootstrapEvent(this.rootPointerEvent, normalizedEvents[i]);\n\n            this.rootBoundary.mapEvent(event);\n        }\n\n        this.setCursor(this.rootBoundary.cursor);\n    }\n\n    /**\n     * Event handler for pointer up events on {@link PIXI.EventSystem#domElement this.domElement}.\n     * @param nativeEvent - The native mouse/pointer/touch event.\n     */\n    private onPointerUp(nativeEvent: MouseEvent | PointerEvent | TouchEvent): void\n    {\n        if (!this.features.click) return;\n        this.rootBoundary.rootTarget = this.renderer.lastObjectRendered as DisplayObject;\n\n        let target = nativeEvent.target;\n\n        // if in shadow DOM use composedPath to access target\n        if (nativeEvent.composedPath && nativeEvent.composedPath().length > 0)\n        {\n            target = nativeEvent.composedPath()[0];\n        }\n\n        const outside = target !== this.domElement ? 'outside' : '';\n        const normalizedEvents = this.normalizeToPointerData(nativeEvent);\n\n        for (let i = 0, j = normalizedEvents.length; i < j; i++)\n        {\n            const event = this.bootstrapEvent(this.rootPointerEvent, normalizedEvents[i]);\n\n            event.type += outside;\n\n            this.rootBoundary.mapEvent(event);\n        }\n\n        this.setCursor(this.rootBoundary.cursor);\n    }\n\n    /**\n     * Event handler for pointer over & out events on {@link PIXI.EventSystem#domElement this.domElement}.\n     * @param nativeEvent - The native mouse/pointer/touch event.\n     */\n    private onPointerOverOut(nativeEvent: MouseEvent | PointerEvent | TouchEvent): void\n    {\n        if (!this.features.click) return;\n        this.rootBoundary.rootTarget = this.renderer.lastObjectRendered as DisplayObject;\n\n        const normalizedEvents = this.normalizeToPointerData(nativeEvent);\n\n        for (let i = 0, j = normalizedEvents.length; i < j; i++)\n        {\n            const event = this.bootstrapEvent(this.rootPointerEvent, normalizedEvents[i]);\n\n            this.rootBoundary.mapEvent(event);\n        }\n\n        this.setCursor(this.rootBoundary.cursor);\n    }\n\n    /**\n     * Passive handler for `wheel` events on {@link PIXI.EventSystem.domElement this.domElement}.\n     * @param nativeEvent - The native wheel event.\n     */\n    protected onWheel(nativeEvent: WheelEvent): void\n    {\n        if (!this.features.wheel) return;\n        const wheelEvent = this.normalizeWheelEvent(nativeEvent);\n\n        this.rootBoundary.rootTarget = this.renderer.lastObjectRendered as DisplayObject;\n        this.rootBoundary.mapEvent(wheelEvent);\n    }\n\n    /**\n     * Sets the {@link PIXI.EventSystem#domElement domElement} and binds event listeners.\n     *\n     * To deregister the current DOM element without setting a new one, pass {@code null}.\n     * @param element - The new DOM element.\n     */\n    public setTargetElement(element: HTMLElement): void\n    {\n        this.removeEvents();\n        this.domElement = element;\n        EventsTicker.domElement = element;\n        this.addEvents();\n    }\n\n    /** Register event listeners on {@link PIXI.Renderer#domElement this.domElement}. */\n    private addEvents(): void\n    {\n        if (this.eventsAdded || !this.domElement)\n        {\n            return;\n        }\n\n        EventsTicker.addTickerListener();\n\n        const style = this.domElement.style as CrossCSSStyleDeclaration;\n\n        if (style)\n        {\n            if ((globalThis.navigator as any).msPointerEnabled)\n            {\n                style.msContentZooming = 'none';\n                style.msTouchAction = 'none';\n            }\n            else if (this.supportsPointerEvents)\n            {\n                style.touchAction = 'none';\n            }\n        }\n\n        /*\n         * These events are added first, so that if pointer events are normalized, they are fired\n         * in the same order as non-normalized events. ie. pointer event 1st, mouse / touch 2nd\n         */\n        if (this.supportsPointerEvents)\n        {\n            globalThis.document.addEventListener('pointermove', this.onPointerMove, true);\n            this.domElement.addEventListener('pointerdown', this.onPointerDown, true);\n            // pointerout is fired in addition to pointerup (for touch events) and pointercancel\n            // we already handle those, so for the purposes of what we do in onPointerOut, we only\n            // care about the pointerleave event\n            this.domElement.addEventListener('pointerleave', this.onPointerOverOut, true);\n            this.domElement.addEventListener('pointerover', this.onPointerOverOut, true);\n            // globalThis.addEventListener('pointercancel', this.onPointerCancel, true);\n            globalThis.addEventListener('pointerup', this.onPointerUp, true);\n        }\n        else\n        {\n            globalThis.document.addEventListener('mousemove', this.onPointerMove, true);\n            this.domElement.addEventListener('mousedown', this.onPointerDown, true);\n            this.domElement.addEventListener('mouseout', this.onPointerOverOut, true);\n            this.domElement.addEventListener('mouseover', this.onPointerOverOut, true);\n            globalThis.addEventListener('mouseup', this.onPointerUp, true);\n\n            if (this.supportsTouchEvents)\n            {\n                this.domElement.addEventListener('touchstart', this.onPointerDown, true);\n                // this.domElement.addEventListener('touchcancel', this.onPointerCancel, true);\n                this.domElement.addEventListener('touchend', this.onPointerUp, true);\n                this.domElement.addEventListener('touchmove', this.onPointerMove, true);\n            }\n        }\n\n        this.domElement.addEventListener('wheel', this.onWheel, {\n            passive: true,\n            capture: true,\n        });\n\n        this.eventsAdded = true;\n    }\n\n    /** Unregister event listeners on {@link PIXI.EventSystem#domElement this.domElement}. */\n    private removeEvents(): void\n    {\n        if (!this.eventsAdded || !this.domElement)\n        {\n            return;\n        }\n\n        EventsTicker.removeTickerListener();\n\n        const style = this.domElement.style as CrossCSSStyleDeclaration;\n\n        if ((globalThis.navigator as any).msPointerEnabled)\n        {\n            style.msContentZooming = '';\n            style.msTouchAction = '';\n        }\n        else if (this.supportsPointerEvents)\n        {\n            style.touchAction = '';\n        }\n\n        if (this.supportsPointerEvents)\n        {\n            globalThis.document.removeEventListener('pointermove', this.onPointerMove, true);\n            this.domElement.removeEventListener('pointerdown', this.onPointerDown, true);\n            this.domElement.removeEventListener('pointerleave', this.onPointerOverOut, true);\n            this.domElement.removeEventListener('pointerover', this.onPointerOverOut, true);\n            // globalThis.removeEventListener('pointercancel', this.onPointerCancel, true);\n            globalThis.removeEventListener('pointerup', this.onPointerUp, true);\n        }\n        else\n        {\n            globalThis.document.removeEventListener('mousemove', this.onPointerMove, true);\n            this.domElement.removeEventListener('mousedown', this.onPointerDown, true);\n            this.domElement.removeEventListener('mouseout', this.onPointerOverOut, true);\n            this.domElement.removeEventListener('mouseover', this.onPointerOverOut, true);\n            globalThis.removeEventListener('mouseup', this.onPointerUp, true);\n\n            if (this.supportsTouchEvents)\n            {\n                this.domElement.removeEventListener('touchstart', this.onPointerDown, true);\n                // this.domElement.removeEventListener('touchcancel', this.onPointerCancel, true);\n                this.domElement.removeEventListener('touchend', this.onPointerUp, true);\n                this.domElement.removeEventListener('touchmove', this.onPointerMove, true);\n            }\n        }\n\n        this.domElement.removeEventListener('wheel', this.onWheel, true);\n\n        this.domElement = null;\n        this.eventsAdded = false;\n    }\n\n    /**\n     * Maps x and y coords from a DOM object and maps them correctly to the PixiJS view. The\n     * resulting value is stored in the point. This takes into account the fact that the DOM\n     * element could be scaled and positioned anywhere on the screen.\n     * @param  {PIXI.IPointData} point - the point that the result will be stored in\n     * @param  {number} x - the x coord of the position to map\n     * @param  {number} y - the y coord of the position to map\n     */\n    public mapPositionToPoint(point: IPointData, x: number, y: number): void\n    {\n        const rect = this.domElement.isConnected\n            ? this.domElement.getBoundingClientRect()\n            : {\n                x: 0,\n                y: 0,\n                width: (this.domElement as any).width,\n                height: (this.domElement as any).height,\n                left: 0,\n                top: 0\n            };\n\n        const resolutionMultiplier = 1.0 / this.resolution;\n\n        point.x = ((x - rect.left) * ((this.domElement as any).width / rect.width)) * resolutionMultiplier;\n        point.y = ((y - rect.top) * ((this.domElement as any).height / rect.height)) * resolutionMultiplier;\n    }\n\n    /**\n     * Ensures that the original event object contains all data that a regular pointer event would have\n     * @param event - The original event data from a touch or mouse event\n     * @returns An array containing a single normalized pointer event, in the case of a pointer\n     *  or mouse event, or a multiple normalized pointer events if there are multiple changed touches\n     */\n    private normalizeToPointerData(event: TouchEvent | MouseEvent | PointerEvent): PointerEvent[]\n    {\n        const normalizedEvents = [];\n\n        if (this.supportsTouchEvents && event instanceof TouchEvent)\n        {\n            for (let i = 0, li = event.changedTouches.length; i < li; i++)\n            {\n                const touch = event.changedTouches[i] as PixiTouch;\n\n                if (typeof touch.button === 'undefined') touch.button = 0;\n                if (typeof touch.buttons === 'undefined') touch.buttons = 1;\n                if (typeof touch.isPrimary === 'undefined')\n                {\n                    touch.isPrimary = event.touches.length === 1 && event.type === 'touchstart';\n                }\n                if (typeof touch.width === 'undefined') touch.width = touch.radiusX || 1;\n                if (typeof touch.height === 'undefined') touch.height = touch.radiusY || 1;\n                if (typeof touch.tiltX === 'undefined') touch.tiltX = 0;\n                if (typeof touch.tiltY === 'undefined') touch.tiltY = 0;\n                if (typeof touch.pointerType === 'undefined') touch.pointerType = 'touch';\n                if (typeof touch.pointerId === 'undefined') touch.pointerId = touch.identifier || 0;\n                if (typeof touch.pressure === 'undefined') touch.pressure = touch.force || 0.5;\n                if (typeof touch.twist === 'undefined') touch.twist = 0;\n                if (typeof touch.tangentialPressure === 'undefined') touch.tangentialPressure = 0;\n                // TODO: Remove these, as layerX/Y is not a standard, is deprecated, has uneven\n                // support, and the fill ins are not quite the same\n                // offsetX/Y might be okay, but is not the same as clientX/Y when the canvas's top\n                // left is not 0,0 on the page\n                if (typeof touch.layerX === 'undefined') touch.layerX = touch.offsetX = touch.clientX;\n                if (typeof touch.layerY === 'undefined') touch.layerY = touch.offsetY = touch.clientY;\n\n                // mark the touch as normalized, just so that we know we did it\n                touch.isNormalized = true;\n                touch.type = event.type;\n\n                normalizedEvents.push(touch);\n            }\n        }\n        // apparently PointerEvent subclasses MouseEvent, so yay\n        else if (!globalThis.MouseEvent\n            || (event instanceof MouseEvent && (!this.supportsPointerEvents || !(event instanceof globalThis.PointerEvent))))\n        {\n            const tempEvent = event as PixiPointerEvent;\n\n            if (typeof tempEvent.isPrimary === 'undefined') tempEvent.isPrimary = true;\n            if (typeof tempEvent.width === 'undefined') tempEvent.width = 1;\n            if (typeof tempEvent.height === 'undefined') tempEvent.height = 1;\n            if (typeof tempEvent.tiltX === 'undefined') tempEvent.tiltX = 0;\n            if (typeof tempEvent.tiltY === 'undefined') tempEvent.tiltY = 0;\n            if (typeof tempEvent.pointerType === 'undefined') tempEvent.pointerType = 'mouse';\n            if (typeof tempEvent.pointerId === 'undefined') tempEvent.pointerId = MOUSE_POINTER_ID;\n            if (typeof tempEvent.pressure === 'undefined') tempEvent.pressure = 0.5;\n            if (typeof tempEvent.twist === 'undefined') tempEvent.twist = 0;\n            if (typeof tempEvent.tangentialPressure === 'undefined') tempEvent.tangentialPressure = 0;\n\n            // mark the mouse event as normalized, just so that we know we did it\n            tempEvent.isNormalized = true;\n\n            normalizedEvents.push(tempEvent);\n        }\n        else\n        {\n            normalizedEvents.push(event);\n        }\n\n        return normalizedEvents as PointerEvent[];\n    }\n\n    /**\n     * Normalizes the native {@link https://w3c.github.io/uievents/#interface-wheelevent WheelEvent}.\n     *\n     * The returned {@link PIXI.FederatedWheelEvent} is a shared instance. It will not persist across\n     * multiple native wheel events.\n     * @param nativeEvent - The native wheel event that occurred on the canvas.\n     * @returns A federated wheel event.\n     */\n    protected normalizeWheelEvent(nativeEvent: WheelEvent): FederatedWheelEvent\n    {\n        const event = this.rootWheelEvent;\n\n        this.transferMouseData(event, nativeEvent);\n\n        // When WheelEvent is triggered by scrolling with mouse wheel, reading WheelEvent.deltaMode\n        // before deltaX/deltaY/deltaZ on Firefox will result in WheelEvent.DOM_DELTA_LINE (1),\n        // while reading WheelEvent.deltaMode after deltaX/deltaY/deltaZ on Firefox or reading\n        // in any order on other browsers will result in WheelEvent.DOM_DELTA_PIXEL (0).\n        // Therefore, we need to read WheelEvent.deltaMode after deltaX/deltaY/deltaZ in order to\n        // make its behavior more consistent across browsers.\n        // @see https://github.com/pixijs/pixijs/issues/8970\n        event.deltaX = nativeEvent.deltaX;\n        event.deltaY = nativeEvent.deltaY;\n        event.deltaZ = nativeEvent.deltaZ;\n        event.deltaMode = nativeEvent.deltaMode;\n\n        this.mapPositionToPoint(event.screen, nativeEvent.clientX, nativeEvent.clientY);\n        event.global.copyFrom(event.screen);\n        event.offset.copyFrom(event.screen);\n\n        event.nativeEvent = nativeEvent;\n        event.type = nativeEvent.type;\n\n        return event;\n    }\n\n    /**\n     * Normalizes the `nativeEvent` into a federateed {@link PIXI.FederatedPointerEvent}.\n     * @param event\n     * @param nativeEvent\n     */\n    private bootstrapEvent(event: FederatedPointerEvent, nativeEvent: PointerEvent): FederatedPointerEvent\n    {\n        event.originalEvent = null;\n        event.nativeEvent = nativeEvent;\n\n        event.pointerId = nativeEvent.pointerId;\n        event.width = nativeEvent.width;\n        event.height = nativeEvent.height;\n        event.isPrimary = nativeEvent.isPrimary;\n        event.pointerType = nativeEvent.pointerType;\n        event.pressure = nativeEvent.pressure;\n        event.tangentialPressure = nativeEvent.tangentialPressure;\n        event.tiltX = nativeEvent.tiltX;\n        event.tiltY = nativeEvent.tiltY;\n        event.twist = nativeEvent.twist;\n        this.transferMouseData(event, nativeEvent);\n\n        this.mapPositionToPoint(event.screen, nativeEvent.clientX, nativeEvent.clientY);\n        event.global.copyFrom(event.screen);// global = screen for top-level\n        event.offset.copyFrom(event.screen);// EventBoundary recalculates using its rootTarget\n\n        event.isTrusted = nativeEvent.isTrusted;\n        if (event.type === 'pointerleave')\n        {\n            event.type = 'pointerout';\n        }\n        if (event.type.startsWith('mouse'))\n        {\n            event.type = event.type.replace('mouse', 'pointer');\n        }\n        if (event.type.startsWith('touch'))\n        {\n            event.type = TOUCH_TO_POINTER[event.type] || event.type;\n        }\n\n        return event;\n    }\n\n    /**\n     * Transfers base & mouse event data from the {@code nativeEvent} to the federated event.\n     * @param event\n     * @param nativeEvent\n     */\n    private transferMouseData(event: FederatedMouseEvent, nativeEvent: MouseEvent): void\n    {\n        event.isTrusted = nativeEvent.isTrusted;\n        event.srcElement = nativeEvent.srcElement;\n        event.timeStamp = performance.now();\n        event.type = nativeEvent.type;\n\n        event.altKey = nativeEvent.altKey;\n        event.button = nativeEvent.button;\n        event.buttons = nativeEvent.buttons;\n        event.client.x = nativeEvent.clientX;\n        event.client.y = nativeEvent.clientY;\n        event.ctrlKey = nativeEvent.ctrlKey;\n        event.metaKey = nativeEvent.metaKey;\n        event.movement.x = nativeEvent.movementX;\n        event.movement.y = nativeEvent.movementY;\n        event.page.x = nativeEvent.pageX;\n        event.page.y = nativeEvent.pageY;\n        event.relatedTarget = null;\n        event.shiftKey = nativeEvent.shiftKey;\n    }\n}\n\ninterface CrossCSSStyleDeclaration extends CSSStyleDeclaration\n{\n    msContentZooming: string;\n    msTouchAction: string;\n}\n\ninterface PixiPointerEvent extends PointerEvent\n{\n    isPrimary: boolean;\n    width: number;\n    height: number;\n    tiltX: number;\n    tiltY: number;\n    pointerType: string;\n    pointerId: number;\n    pressure: number;\n    twist: number;\n    tangentialPressure: number;\n    isNormalized: boolean;\n    type: string;\n}\n\nextensions.add(EventSystem);\n"], "mappings": ";;;;;AAYA,MAAMA,gBAAA,GAAmB;EACnBC,gBAAA,GAA2C;IAC7CC,UAAA,EAAY;IACZC,QAAA,EAAU;IACVC,eAAA,EAAiB;IACjBC,SAAA,EAAW;IACXC,WAAA,EAAa;EACjB;EAwEaC,YAAA,GAAN,MAAMC,aAAA,CACb;IAAA;AAAA;AAAA;IA+GIC,YAAYC,QAAA,EACZ;MA9DA,KAAgBC,mBAAA,GAAsB,kBAAkBC,UAAA,EAGxC,KAAAC,qBAAA,GAAwB,CAAC,CAACD,UAAA,CAAWE,YAAA,EAuBrD,KAAOC,UAAA,GAA0B,MAGjC,KAAOC,UAAA,GAAa,GAkChB,KAAKN,QAAA,GAAWA,QAAA,EAChB,KAAKO,YAAA,GAAe,IAAIC,aAAA,CAAc,IAAI,GAC1CC,YAAA,CAAaC,IAAA,CAAK,IAAI,GAEtB,KAAKC,kBAAA,GAAqB,IAC1B,KAAKC,WAAA,GAAc,IAEnB,KAAKC,gBAAA,GAAmB,IAAIC,qBAAA,CAAsB,IAAI,GACtD,KAAKC,cAAA,GAAiB,IAAIC,mBAAA,CAAoB,IAAI,GAElD,KAAKC,YAAA,GAAe;QAChBC,OAAA,EAAS;QACTC,OAAA,EAAS;MAAA,GAGb,KAAKC,QAAA,GAAW,IAAIC,KAAA,CAAM;QAAE,GAAGvB,aAAA,CAAYwB;MAAA,GAAwB;QAC/DC,GAAA,EAAKA,CAACC,MAAA,EAAQC,GAAA,EAAKC,KAAA,MAEXD,GAAA,KAAQ,iBAER,KAAKlB,YAAA,CAAaoB,sBAAA,GAAyBD,KAAA,GAE/CF,MAAA,CAAOC,GAAgC,IAAIC,KAAA,EAEpC;MAAA,CAEd,GAED,KAAKE,aAAA,GAAgB,KAAKA,aAAA,CAAcC,IAAA,CAAK,IAAI,GACjD,KAAKC,aAAA,GAAgB,KAAKA,aAAA,CAAcD,IAAA,CAAK,IAAI,GACjD,KAAKE,WAAA,GAAc,KAAKA,WAAA,CAAYF,IAAA,CAAK,IAAI,GAC7C,KAAKG,gBAAA,GAAmB,KAAKA,gBAAA,CAAiBH,IAAA,CAAK,IAAI,GACvD,KAAKI,OAAA,GAAU,KAAKA,OAAA,CAAQJ,IAAA,CAAK,IAAI;IACzC;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAlHA,WAAkBK,iBAAA,EAClB;MACI,OAAO,KAAKC,iBAAA;IAChB;IAAA;AAAA;AAAA;AAAA;IAqHAzB,KAAK0B,OAAA,EACL;MACI,MAAM;QAAEC,IAAA;QAAM/B;MAAA,IAAe,KAAKN,QAAA;MAE7B,KAAAsC,gBAAA,CAAiBD,IAAyB,GAC/C,KAAK/B,UAAA,GAAaA,UAAA,EAClBR,aAAA,CAAYqC,iBAAA,GAAoBC,OAAA,CAAQG,SAAA,IAAa,QACrDC,MAAA,CAAOC,MAAA,CAAO,KAAKrB,QAAA,EAAUgB,OAAA,CAAQM,aAAA,IAAiB,CAAE,IACxD,KAAKnC,YAAA,CAAaoB,sBAAA,GAAyB,KAAKP,QAAA,CAASuB,UAAA;IAC7D;IAAA;AAAA;AAAA;AAAA;IAMAC,iBAAiBtC,UAAA,EACjB;MACI,KAAKA,UAAA,GAAaA,UAAA;IACtB;IAAA;IAGAuC,QAAA,EACA;MACI,KAAKP,gBAAA,CAAiB,IAAI,GAC1B,KAAKtC,QAAA,GAAW;IACpB;IAAA;AAAA;AAAA;AAAA;IAMO8C,UAAUC,IAAA,EACjB;MACIA,IAAA,GAAOA,IAAA,IAAQ;MACf,IAAIC,WAAA,GAAc;MAId,IAAA9C,UAAA,CAAW+C,eAAA,IAAmB,KAAK5C,UAAA,YAAsB4C,eAAA,KAEzDD,WAAA,GAAc,KAGd,KAAKE,aAAA,KAAkBH,IAAA,EAEvB;MAEJ,KAAKG,aAAA,GAAgBH,IAAA;MACf,MAAAI,KAAA,GAAQ,KAAKlC,YAAA,CAAa8B,IAAI;MAGhC,IAAAI,KAAA,EAEA,QAAQ,OAAOA,KAAA;QAEX,KAAK;UAEGH,WAAA,KAEA,KAAK3C,UAAA,CAAW8C,KAAA,CAAMC,MAAA,GAASD,KAAA;UAEnC;QACJ,KAAK;UAEDA,KAAA,CAAMJ,IAAI;UACV;QACJ,KAAK;UAGGC,WAAA,IAEAR,MAAA,CAAOC,MAAA,CAAO,KAAKpC,UAAA,CAAW8C,KAAA,EAAOA,KAAK;UAE9C;MACR,OAEKH,WAAA,IAAe,OAAOD,IAAA,IAAS,YAAY,CAACP,MAAA,CAAOa,SAAA,CAAUC,cAAA,CAAeC,IAAA,CAAK,KAAKtC,YAAA,EAAc8B,IAAI,MAI7G,KAAK1C,UAAA,CAAW8C,KAAA,CAAMC,MAAA,GAASL,IAAA;IAEvC;IAAA;AAAA;AAAA;AAAA;AAAA;IAOA,IAAW5B,QAAA,EACX;MACI,OAAO,KAAKN,gBAAA;IAChB;IAAA;AAAA;AAAA;AAAA;IAMQe,cAAc4B,WAAA,EACtB;MACQ,KAAC,KAAKpC,QAAA,CAASqC,KAAA,EAAO;MACrB,KAAAlD,YAAA,CAAamD,UAAA,GAAa,KAAK1D,QAAA,CAAS2D,kBAAA;MAEvC,MAAAC,MAAA,GAAS,KAAKC,sBAAA,CAAuBL,WAAW;MAUlD,KAAK7C,kBAAA,IAAuBiD,MAAA,CAAO,CAAC,EAAUE,YAAA,KAE3BN,WAAA,CAAYO,UAAA,IAAc,EAAE,gBAAgBP,WAAA,MAI3DA,WAAA,CAAYQ,cAAA,CAAe;MAInC,SAASC,CAAA,GAAI,GAAGC,CAAA,GAAIN,MAAA,CAAOO,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAC1C;QACU,MAAAG,YAAA,GAAcR,MAAA,CAAOK,CAAC;UACtBI,cAAA,GAAiB,KAAKC,cAAA,CAAe,KAAKzD,gBAAA,EAAkBuD,YAAW;QAExE,KAAA7D,YAAA,CAAagE,QAAA,CAASF,cAAc;MAC7C;MAEK,KAAAvB,SAAA,CAAU,KAAKvC,YAAA,CAAa6C,MAAM;IAC3C;IAAA;AAAA;AAAA;AAAA;IAMQtB,cAAc0B,WAAA,EACtB;MACQ,KAAC,KAAKpC,QAAA,CAASoD,IAAA,EAAM;MACzB,KAAKjE,YAAA,CAAamD,UAAA,GAAa,KAAK1D,QAAA,CAAS2D,kBAAA,EAE7ClD,YAAA,CAAagE,YAAA;MAEP,MAAAC,gBAAA,GAAmB,KAAKb,sBAAA,CAAuBL,WAAW;MAEhE,SAASS,CAAA,GAAI,GAAGC,CAAA,GAAIQ,gBAAA,CAAiBP,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IACpD;QACI,MAAMU,KAAA,GAAQ,KAAKL,cAAA,CAAe,KAAKzD,gBAAA,EAAkB6D,gBAAA,CAAiBT,CAAC,CAAC;QAEvE,KAAA1D,YAAA,CAAagE,QAAA,CAASI,KAAK;MACpC;MAEK,KAAA7B,SAAA,CAAU,KAAKvC,YAAA,CAAa6C,MAAM;IAC3C;IAAA;AAAA;AAAA;AAAA;IAMQrB,YAAYyB,WAAA,EACpB;MACQ,KAAC,KAAKpC,QAAA,CAASqC,KAAA,EAAO;MACrB,KAAAlD,YAAA,CAAamD,UAAA,GAAa,KAAK1D,QAAA,CAAS2D,kBAAA;MAE7C,IAAInC,MAAA,GAASgC,WAAA,CAAYhC,MAAA;MAGrBgC,WAAA,CAAYoB,YAAA,IAAgBpB,WAAA,CAAYoB,YAAA,CAAa,EAAET,MAAA,GAAS,MAEhE3C,MAAA,GAASgC,WAAA,CAAYoB,YAAA,GAAe,CAAC;MAGnC,MAAAC,OAAA,GAAUrD,MAAA,KAAW,KAAKnB,UAAA,GAAa,YAAY;QACnDqE,gBAAA,GAAmB,KAAKb,sBAAA,CAAuBL,WAAW;MAEhE,SAASS,CAAA,GAAI,GAAGC,CAAA,GAAIQ,gBAAA,CAAiBP,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IACpD;QACI,MAAMU,KAAA,GAAQ,KAAKL,cAAA,CAAe,KAAKzD,gBAAA,EAAkB6D,gBAAA,CAAiBT,CAAC,CAAC;QAE5EU,KAAA,CAAMG,IAAA,IAAQD,OAAA,EAEd,KAAKtE,YAAA,CAAagE,QAAA,CAASI,KAAK;MACpC;MAEK,KAAA7B,SAAA,CAAU,KAAKvC,YAAA,CAAa6C,MAAM;IAC3C;IAAA;AAAA;AAAA;AAAA;IAMQpB,iBAAiBwB,WAAA,EACzB;MACQ,KAAC,KAAKpC,QAAA,CAASqC,KAAA,EAAO;MACrB,KAAAlD,YAAA,CAAamD,UAAA,GAAa,KAAK1D,QAAA,CAAS2D,kBAAA;MAEvC,MAAAe,gBAAA,GAAmB,KAAKb,sBAAA,CAAuBL,WAAW;MAEhE,SAASS,CAAA,GAAI,GAAGC,CAAA,GAAIQ,gBAAA,CAAiBP,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IACpD;QACI,MAAMU,KAAA,GAAQ,KAAKL,cAAA,CAAe,KAAKzD,gBAAA,EAAkB6D,gBAAA,CAAiBT,CAAC,CAAC;QAEvE,KAAA1D,YAAA,CAAagE,QAAA,CAASI,KAAK;MACpC;MAEK,KAAA7B,SAAA,CAAU,KAAKvC,YAAA,CAAa6C,MAAM;IAC3C;IAAA;AAAA;AAAA;AAAA;IAMUnB,QAAQuB,WAAA,EAClB;MACQ,KAAC,KAAKpC,QAAA,CAAS2D,KAAA,EAAO;MACpB,MAAAC,UAAA,GAAa,KAAKC,mBAAA,CAAoBzB,WAAW;MAElD,KAAAjD,YAAA,CAAamD,UAAA,GAAa,KAAK1D,QAAA,CAAS2D,kBAAA,EAC7C,KAAKpD,YAAA,CAAagE,QAAA,CAASS,UAAU;IACzC;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAQO1C,iBAAiB4C,OAAA,EACxB;MACS,KAAAC,YAAA,IACL,KAAK9E,UAAA,GAAa6E,OAAA,EAClBzE,YAAA,CAAaJ,UAAA,GAAa6E,OAAA,EAC1B,KAAKE,SAAA,CAAU;IACnB;IAAA;IAGQA,UAAA,EACR;MACQ,SAAKxE,WAAA,IAAe,CAAC,KAAKP,UAAA,EAE1B;MAGJI,YAAA,CAAa4E,iBAAA,CAAkB;MAEzB,MAAAlC,KAAA,GAAQ,KAAK9C,UAAA,CAAW8C,KAAA;MAE1BA,KAAA,KAEKjD,UAAA,CAAWoF,SAAA,CAAkBC,gBAAA,IAE9BpC,KAAA,CAAMqC,gBAAA,GAAmB,QACzBrC,KAAA,CAAMsC,aAAA,GAAgB,UAEjB,KAAKtF,qBAAA,KAEVgD,KAAA,CAAMuC,WAAA,GAAc,UAQxB,KAAKvF,qBAAA,IAELD,UAAA,CAAWyF,QAAA,CAASC,gBAAA,CAAiB,eAAe,KAAK9D,aAAA,EAAe,EAAI,GAC5E,KAAKzB,UAAA,CAAWuF,gBAAA,CAAiB,eAAe,KAAKhE,aAAA,EAAe,EAAI,GAIxE,KAAKvB,UAAA,CAAWuF,gBAAA,CAAiB,gBAAgB,KAAK5D,gBAAA,EAAkB,EAAI,GAC5E,KAAK3B,UAAA,CAAWuF,gBAAA,CAAiB,eAAe,KAAK5D,gBAAA,EAAkB,EAAI,GAE3E9B,UAAA,CAAW0F,gBAAA,CAAiB,aAAa,KAAK7D,WAAA,EAAa,EAAI,MAI/D7B,UAAA,CAAWyF,QAAA,CAASC,gBAAA,CAAiB,aAAa,KAAK9D,aAAA,EAAe,EAAI,GAC1E,KAAKzB,UAAA,CAAWuF,gBAAA,CAAiB,aAAa,KAAKhE,aAAA,EAAe,EAAI,GACtE,KAAKvB,UAAA,CAAWuF,gBAAA,CAAiB,YAAY,KAAK5D,gBAAA,EAAkB,EAAI,GACxE,KAAK3B,UAAA,CAAWuF,gBAAA,CAAiB,aAAa,KAAK5D,gBAAA,EAAkB,EAAI,GACzE9B,UAAA,CAAW0F,gBAAA,CAAiB,WAAW,KAAK7D,WAAA,EAAa,EAAI,GAEzD,KAAK9B,mBAAA,KAEL,KAAKI,UAAA,CAAWuF,gBAAA,CAAiB,cAAc,KAAKhE,aAAA,EAAe,EAAI,GAEvE,KAAKvB,UAAA,CAAWuF,gBAAA,CAAiB,YAAY,KAAK7D,WAAA,EAAa,EAAI,GACnE,KAAK1B,UAAA,CAAWuF,gBAAA,CAAiB,aAAa,KAAK9D,aAAA,EAAe,EAAI,KAI9E,KAAKzB,UAAA,CAAWuF,gBAAA,CAAiB,SAAS,KAAK3D,OAAA,EAAS;QACpD4D,OAAA,EAAS;QACTC,OAAA,EAAS;MACZ,IAED,KAAKlF,WAAA,GAAc;IACvB;IAAA;IAGQuE,aAAA,EACR;MACI,IAAI,CAAC,KAAKvE,WAAA,IAAe,CAAC,KAAKP,UAAA,EAE3B;MAGJI,YAAA,CAAasF,oBAAA,CAAqB;MAE5B,MAAA5C,KAAA,GAAQ,KAAK9C,UAAA,CAAW8C,KAAA;MAEzBjD,UAAA,CAAWoF,SAAA,CAAkBC,gBAAA,IAE9BpC,KAAA,CAAMqC,gBAAA,GAAmB,IACzBrC,KAAA,CAAMsC,aAAA,GAAgB,MAEjB,KAAKtF,qBAAA,KAEVgD,KAAA,CAAMuC,WAAA,GAAc,KAGpB,KAAKvF,qBAAA,IAELD,UAAA,CAAWyF,QAAA,CAASK,mBAAA,CAAoB,eAAe,KAAKlE,aAAA,EAAe,EAAI,GAC/E,KAAKzB,UAAA,CAAW2F,mBAAA,CAAoB,eAAe,KAAKpE,aAAA,EAAe,EAAI,GAC3E,KAAKvB,UAAA,CAAW2F,mBAAA,CAAoB,gBAAgB,KAAKhE,gBAAA,EAAkB,EAAI,GAC/E,KAAK3B,UAAA,CAAW2F,mBAAA,CAAoB,eAAe,KAAKhE,gBAAA,EAAkB,EAAI,GAE9E9B,UAAA,CAAW8F,mBAAA,CAAoB,aAAa,KAAKjE,WAAA,EAAa,EAAI,MAIlE7B,UAAA,CAAWyF,QAAA,CAASK,mBAAA,CAAoB,aAAa,KAAKlE,aAAA,EAAe,EAAI,GAC7E,KAAKzB,UAAA,CAAW2F,mBAAA,CAAoB,aAAa,KAAKpE,aAAA,EAAe,EAAI,GACzE,KAAKvB,UAAA,CAAW2F,mBAAA,CAAoB,YAAY,KAAKhE,gBAAA,EAAkB,EAAI,GAC3E,KAAK3B,UAAA,CAAW2F,mBAAA,CAAoB,aAAa,KAAKhE,gBAAA,EAAkB,EAAI,GAC5E9B,UAAA,CAAW8F,mBAAA,CAAoB,WAAW,KAAKjE,WAAA,EAAa,EAAI,GAE5D,KAAK9B,mBAAA,KAEL,KAAKI,UAAA,CAAW2F,mBAAA,CAAoB,cAAc,KAAKpE,aAAA,EAAe,EAAI,GAE1E,KAAKvB,UAAA,CAAW2F,mBAAA,CAAoB,YAAY,KAAKjE,WAAA,EAAa,EAAI,GACtE,KAAK1B,UAAA,CAAW2F,mBAAA,CAAoB,aAAa,KAAKlE,aAAA,EAAe,EAAI,KAIjF,KAAKzB,UAAA,CAAW2F,mBAAA,CAAoB,SAAS,KAAK/D,OAAA,EAAS,EAAI,GAE/D,KAAK5B,UAAA,GAAa,MAClB,KAAKO,WAAA,GAAc;IACvB;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAUOqF,mBAAmBC,KAAA,EAAmBC,CAAA,EAAWC,CAAA,EACxD;MACI,MAAMC,IAAA,GAAO,KAAKhG,UAAA,CAAWiG,WAAA,GACvB,KAAKjG,UAAA,CAAWkG,qBAAA,KAChB;UACEJ,CAAA,EAAG;UACHC,CAAA,EAAG;UACHI,KAAA,EAAQ,KAAKnG,UAAA,CAAmBmG,KAAA;UAChCC,MAAA,EAAS,KAAKpG,UAAA,CAAmBoG,MAAA;UACjCC,IAAA,EAAM;UACNC,GAAA,EAAK;QACT;QAEEC,oBAAA,GAAuB,IAAM,KAAKtG,UAAA;MAExC4F,KAAA,CAAMC,CAAA,IAAMA,CAAA,GAAIE,IAAA,CAAKK,IAAA,KAAU,KAAKrG,UAAA,CAAmBmG,KAAA,GAAQH,IAAA,CAAKG,KAAA,IAAUI,oBAAA,EAC9EV,KAAA,CAAME,CAAA,IAAMA,CAAA,GAAIC,IAAA,CAAKM,GAAA,KAAS,KAAKtG,UAAA,CAAmBoG,MAAA,GAASJ,IAAA,CAAKI,MAAA,IAAWG,oBAAA;IACnF;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAQQ/C,uBAAuBc,KAAA,EAC/B;MACI,MAAMD,gBAAA,GAAmB;MAErB,SAAKzE,mBAAA,IAAuB0E,KAAA,YAAiBkC,UAAA,EAEpC,SAAA5C,CAAA,GAAI,GAAG6C,EAAA,GAAKnC,KAAA,CAAMoC,cAAA,CAAe5C,MAAA,EAAQF,CAAA,GAAI6C,EAAA,EAAI7C,CAAA,IAC1D;QACU,MAAA+C,KAAA,GAAQrC,KAAA,CAAMoC,cAAA,CAAe9C,CAAC;QAEhC,OAAO+C,KAAA,CAAMC,MAAA,GAAW,QAAaD,KAAA,CAAMC,MAAA,GAAS,IACpD,OAAOD,KAAA,CAAME,OAAA,GAAY,QAAaF,KAAA,CAAME,OAAA,GAAU,IACtD,OAAOF,KAAA,CAAMG,SAAA,GAAc,QAE3BH,KAAA,CAAMG,SAAA,GAAYxC,KAAA,CAAMyC,OAAA,CAAQjD,MAAA,KAAW,KAAKQ,KAAA,CAAMG,IAAA,KAAS,eAE/D,OAAOkC,KAAA,CAAMR,KAAA,GAAU,QAAaQ,KAAA,CAAMR,KAAA,GAAQQ,KAAA,CAAMK,OAAA,IAAW,IACnE,OAAOL,KAAA,CAAMP,MAAA,GAAW,QAAaO,KAAA,CAAMP,MAAA,GAASO,KAAA,CAAMM,OAAA,IAAW,IACrE,OAAON,KAAA,CAAMO,KAAA,GAAU,QAAaP,KAAA,CAAMO,KAAA,GAAQ,IAClD,OAAOP,KAAA,CAAMQ,KAAA,GAAU,QAAaR,KAAA,CAAMQ,KAAA,GAAQ,IAClD,OAAOR,KAAA,CAAMS,WAAA,GAAgB,QAAaT,KAAA,CAAMS,WAAA,GAAc,UAC9D,OAAOT,KAAA,CAAMU,SAAA,GAAc,QAAaV,KAAA,CAAMU,SAAA,GAAYV,KAAA,CAAMW,UAAA,IAAc,IAC9E,OAAOX,KAAA,CAAMY,QAAA,GAAa,QAAaZ,KAAA,CAAMY,QAAA,GAAWZ,KAAA,CAAMa,KAAA,IAAS,MACvE,OAAOb,KAAA,CAAMc,KAAA,GAAU,QAAad,KAAA,CAAMc,KAAA,GAAQ,IAClD,OAAOd,KAAA,CAAMe,kBAAA,GAAuB,QAAaf,KAAA,CAAMe,kBAAA,GAAqB,IAK5E,OAAOf,KAAA,CAAMgB,MAAA,GAAW,QAAahB,KAAA,CAAMgB,MAAA,GAAShB,KAAA,CAAMiB,OAAA,GAAUjB,KAAA,CAAMkB,OAAA,GAC1E,OAAOlB,KAAA,CAAMmB,MAAA,GAAW,QAAanB,KAAA,CAAMmB,MAAA,GAASnB,KAAA,CAAMoB,OAAA,GAAUpB,KAAA,CAAMqB,OAAA,GAG9ErB,KAAA,CAAMlD,YAAA,GAAe,IACrBkD,KAAA,CAAMlC,IAAA,GAAOH,KAAA,CAAMG,IAAA,EAEnBJ,gBAAA,CAAiB4D,IAAA,CAAKtB,KAAK;MAC/B,WAGK,CAAC9G,UAAA,CAAWqI,UAAA,IACb5D,KAAA,YAAiB4D,UAAA,KAAe,CAAC,KAAKpI,qBAAA,IAAyB,EAAEwE,KAAA,YAAiBzE,UAAA,CAAWE,YAAA,IACrG;QACI,MAAMoI,SAAA,GAAY7D,KAAA;QAEd,OAAO6D,SAAA,CAAUrB,SAAA,GAAc,QAAaqB,SAAA,CAAUrB,SAAA,GAAY,KAClE,OAAOqB,SAAA,CAAUhC,KAAA,GAAU,QAAagC,SAAA,CAAUhC,KAAA,GAAQ,IAC1D,OAAOgC,SAAA,CAAU/B,MAAA,GAAW,QAAa+B,SAAA,CAAU/B,MAAA,GAAS,IAC5D,OAAO+B,SAAA,CAAUjB,KAAA,GAAU,QAAaiB,SAAA,CAAUjB,KAAA,GAAQ,IAC1D,OAAOiB,SAAA,CAAUhB,KAAA,GAAU,QAAagB,SAAA,CAAUhB,KAAA,GAAQ,IAC1D,OAAOgB,SAAA,CAAUf,WAAA,GAAgB,QAAae,SAAA,CAAUf,WAAA,GAAc,UACtE,OAAOe,SAAA,CAAUd,SAAA,GAAc,QAAac,SAAA,CAAUd,SAAA,GAAYpI,gBAAA,GAClE,OAAOkJ,SAAA,CAAUZ,QAAA,GAAa,QAAaY,SAAA,CAAUZ,QAAA,GAAW,MAChE,OAAOY,SAAA,CAAUV,KAAA,GAAU,QAAaU,SAAA,CAAUV,KAAA,GAAQ,IAC1D,OAAOU,SAAA,CAAUT,kBAAA,GAAuB,QAAaS,SAAA,CAAUT,kBAAA,GAAqB,IAGxFS,SAAA,CAAU1E,YAAA,GAAe,IAEzBY,gBAAA,CAAiB4D,IAAA,CAAKE,SAAS;MACnC,OAGI9D,gBAAA,CAAiB4D,IAAA,CAAK3D,KAAK;MAGxB,OAAAD,gBAAA;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAUUO,oBAAoBzB,WAAA,EAC9B;MACI,MAAMmB,KAAA,GAAQ,KAAK5D,cAAA;MAEd,YAAA0H,iBAAA,CAAkB9D,KAAA,EAAOnB,WAAW,GASzCmB,KAAA,CAAM+D,MAAA,GAASlF,WAAA,CAAYkF,MAAA,EAC3B/D,KAAA,CAAMgE,MAAA,GAASnF,WAAA,CAAYmF,MAAA,EAC3BhE,KAAA,CAAMiE,MAAA,GAASpF,WAAA,CAAYoF,MAAA,EAC3BjE,KAAA,CAAMkE,SAAA,GAAYrF,WAAA,CAAYqF,SAAA,EAE9B,KAAK5C,kBAAA,CAAmBtB,KAAA,CAAMmE,MAAA,EAAQtF,WAAA,CAAY0E,OAAA,EAAS1E,WAAA,CAAY6E,OAAO,GAC9E1D,KAAA,CAAMoE,MAAA,CAAOC,QAAA,CAASrE,KAAA,CAAMmE,MAAM,GAClCnE,KAAA,CAAMsE,MAAA,CAAOD,QAAA,CAASrE,KAAA,CAAMmE,MAAM,GAElCnE,KAAA,CAAMnB,WAAA,GAAcA,WAAA,EACpBmB,KAAA,CAAMG,IAAA,GAAOtB,WAAA,CAAYsB,IAAA,EAElBH,KAAA;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;IAOQL,eAAeK,KAAA,EAA8BnB,WAAA,EACrD;MACU,OAAAmB,KAAA,CAAAuE,aAAA,GAAgB,MACtBvE,KAAA,CAAMnB,WAAA,GAAcA,WAAA,EAEpBmB,KAAA,CAAM+C,SAAA,GAAYlE,WAAA,CAAYkE,SAAA,EAC9B/C,KAAA,CAAM6B,KAAA,GAAQhD,WAAA,CAAYgD,KAAA,EAC1B7B,KAAA,CAAM8B,MAAA,GAASjD,WAAA,CAAYiD,MAAA,EAC3B9B,KAAA,CAAMwC,SAAA,GAAY3D,WAAA,CAAY2D,SAAA,EAC9BxC,KAAA,CAAM8C,WAAA,GAAcjE,WAAA,CAAYiE,WAAA,EAChC9C,KAAA,CAAMiD,QAAA,GAAWpE,WAAA,CAAYoE,QAAA,EAC7BjD,KAAA,CAAMoD,kBAAA,GAAqBvE,WAAA,CAAYuE,kBAAA,EACvCpD,KAAA,CAAM4C,KAAA,GAAQ/D,WAAA,CAAY+D,KAAA,EAC1B5C,KAAA,CAAM6C,KAAA,GAAQhE,WAAA,CAAYgE,KAAA,EAC1B7C,KAAA,CAAMmD,KAAA,GAAQtE,WAAA,CAAYsE,KAAA,EAC1B,KAAKW,iBAAA,CAAkB9D,KAAA,EAAOnB,WAAW,GAEzC,KAAKyC,kBAAA,CAAmBtB,KAAA,CAAMmE,MAAA,EAAQtF,WAAA,CAAY0E,OAAA,EAAS1E,WAAA,CAAY6E,OAAO,GAC9E1D,KAAA,CAAMoE,MAAA,CAAOC,QAAA,CAASrE,KAAA,CAAMmE,MAAM,GAClCnE,KAAA,CAAMsE,MAAA,CAAOD,QAAA,CAASrE,KAAA,CAAMmE,MAAM,GAElCnE,KAAA,CAAMwE,SAAA,GAAY3F,WAAA,CAAY2F,SAAA,EAC1BxE,KAAA,CAAMG,IAAA,KAAS,mBAEfH,KAAA,CAAMG,IAAA,GAAO,eAEbH,KAAA,CAAMG,IAAA,CAAKsE,UAAA,CAAW,OAAO,MAE7BzE,KAAA,CAAMG,IAAA,GAAOH,KAAA,CAAMG,IAAA,CAAKuE,OAAA,CAAQ,SAAS,SAAS,IAElD1E,KAAA,CAAMG,IAAA,CAAKsE,UAAA,CAAW,OAAO,MAE7BzE,KAAA,CAAMG,IAAA,GAAOvF,gBAAA,CAAiBoF,KAAA,CAAMG,IAAI,KAAKH,KAAA,CAAMG,IAAA,GAGhDH,KAAA;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;IAOQ8D,kBAAkB9D,KAAA,EAA4BnB,WAAA,EACtD;MACImB,KAAA,CAAMwE,SAAA,GAAY3F,WAAA,CAAY2F,SAAA,EAC9BxE,KAAA,CAAM2E,UAAA,GAAa9F,WAAA,CAAY8F,UAAA,EAC/B3E,KAAA,CAAM4E,SAAA,GAAYC,WAAA,CAAYC,GAAA,CAC9B,GAAA9E,KAAA,CAAMG,IAAA,GAAOtB,WAAA,CAAYsB,IAAA,EAEzBH,KAAA,CAAM+E,MAAA,GAASlG,WAAA,CAAYkG,MAAA,EAC3B/E,KAAA,CAAMsC,MAAA,GAASzD,WAAA,CAAYyD,MAAA,EAC3BtC,KAAA,CAAMuC,OAAA,GAAU1D,WAAA,CAAY0D,OAAA,EAC5BvC,KAAA,CAAMgF,MAAA,CAAOxD,CAAA,GAAI3C,WAAA,CAAY0E,OAAA,EAC7BvD,KAAA,CAAMgF,MAAA,CAAOvD,CAAA,GAAI5C,WAAA,CAAY6E,OAAA,EAC7B1D,KAAA,CAAMiF,OAAA,GAAUpG,WAAA,CAAYoG,OAAA,EAC5BjF,KAAA,CAAMkF,OAAA,GAAUrG,WAAA,CAAYqG,OAAA,EAC5BlF,KAAA,CAAMmF,QAAA,CAAS3D,CAAA,GAAI3C,WAAA,CAAYuG,SAAA,EAC/BpF,KAAA,CAAMmF,QAAA,CAAS1D,CAAA,GAAI5C,WAAA,CAAYwG,SAAA,EAC/BrF,KAAA,CAAMsF,IAAA,CAAK9D,CAAA,GAAI3C,WAAA,CAAY0G,KAAA,EAC3BvF,KAAA,CAAMsF,IAAA,CAAK7D,CAAA,GAAI5C,WAAA,CAAY2G,KAAA,EAC3BxF,KAAA,CAAMyF,aAAA,GAAgB,MACtBzF,KAAA,CAAM0F,QAAA,GAAW7G,WAAA,CAAY6G,QAAA;IACjC;EACJ;AA7sBaxK,YAAA,CAGFyK,SAAA,GAA+B;EAClCC,IAAA,EAAM;EACNzF,IAAA,EAAM,CACF0F,aAAA,CAAcC,cAAA,EACdD,aAAA,CAAcE,oBAAA;AAEtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATS7K,YAAA,CAiBKyB,oBAAA,GAA4C;EACtDkD,IAAA,EAAM;EACN7B,UAAA,EAAY;EACZc,KAAA,EAAO;EACPsB,KAAA,EAAO;AACX;AAtBG,IAAM4F,WAAA,GAAN9K,YAAA;AAquBP+K,UAAA,CAAWC,GAAA,CAAIF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}