{"ast": null, "code": "import { settings, ExtensionType, utils, BaseTexture, extensions } from \"@pixi/core\";\nimport { checkDataUrl } from \"../../../utils/checkDataUrl.mjs\";\nimport { checkExtension } from \"../../../utils/checkExtension.mjs\";\nimport { LoaderParserPriority } from \"../LoaderParser.mjs\";\nimport { WorkerManager } from \"../WorkerManager.mjs\";\nimport { createTexture } from \"./utils/createTexture.mjs\";\nconst validImageExtensions = [\".jpeg\", \".jpg\", \".png\", \".webp\", \".avif\"],\n  validImageMIMEs = [\"image/jpeg\", \"image/png\", \"image/webp\", \"image/avif\"];\nasync function loadImageBitmap(url) {\n  const response = await settings.ADAPTER.fetch(url);\n  if (!response.ok) throw new Error(`[loadImageBitmap] Failed to fetch ${url}: ${response.status} ${response.statusText}`);\n  const imageBlob = await response.blob();\n  return await createImageBitmap(imageBlob);\n}\nconst loadTextures = {\n  name: \"loadTextures\",\n  extension: {\n    type: ExtensionType.LoadParser,\n    priority: LoaderParserPriority.High\n  },\n  config: {\n    preferWorkers: !0,\n    preferCreateImageBitmap: !0,\n    crossOrigin: \"anonymous\"\n  },\n  test(url) {\n    return checkDataUrl(url, validImageMIMEs) || checkExtension(url, validImageExtensions);\n  },\n  async load(url, asset, loader) {\n    const useImageBitmap = globalThis.createImageBitmap && this.config.preferCreateImageBitmap;\n    let src;\n    useImageBitmap ? this.config.preferWorkers && (await WorkerManager.isImageBitmapSupported()) ? src = await WorkerManager.loadImageBitmap(url) : src = await loadImageBitmap(url) : src = await new Promise((resolve, reject) => {\n      const src2 = new Image();\n      src2.crossOrigin = this.config.crossOrigin, src2.src = url, src2.complete ? resolve(src2) : (src2.onload = () => resolve(src2), src2.onerror = e => reject(e));\n    });\n    const options = {\n      ...asset.data\n    };\n    options.resolution ?? (options.resolution = utils.getResolutionOfUrl(url)), useImageBitmap && options.resourceOptions?.ownsImageBitmap === void 0 && (options.resourceOptions = {\n      ...options.resourceOptions\n    }, options.resourceOptions.ownsImageBitmap = !0);\n    const base = new BaseTexture(src, options);\n    return base.resource.src = url, createTexture(base, loader, url);\n  },\n  unload(texture) {\n    texture.destroy(!0);\n  }\n};\nextensions.add(loadTextures);\nexport { loadImageBitmap, loadTextures };", "map": {"version": 3, "names": ["validImageExtensions", "validImageMIMEs", "loadImageBitmap", "url", "response", "settings", "ADAPTER", "fetch", "ok", "Error", "status", "statusText", "imageBlob", "blob", "createImageBitmap", "loadTextures", "name", "extension", "type", "ExtensionType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "priority", "LoaderParserPriority", "High", "config", "preferWorkers", "preferCreateImageBitmap", "crossOrigin", "test", "checkDataUrl", "checkExtension", "load", "asset", "loader", "useImageBitmap", "globalThis", "src", "WorkerManager", "isImageBitmapSupported", "Promise", "resolve", "reject", "src2", "Image", "complete", "onload", "onerror", "e", "options", "data", "resolution", "utils", "getResolutionOfUrl", "resourceOptions", "ownsImageBitmap", "base", "BaseTexture", "resource", "createTexture", "unload", "texture", "destroy", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\assets\\src\\loader\\parsers\\textures\\loadTextures.ts"], "sourcesContent": ["import { BaseTexture, extensions, ExtensionType, settings, utils } from '@pixi/core';\nimport { checkDataUrl } from '../../../utils/checkDataUrl';\nimport { checkExtension } from '../../../utils/checkExtension';\nimport { LoaderParserPriority } from '../LoaderParser';\nimport { WorkerManager } from '../WorkerManager';\nimport { createTexture } from './utils/createTexture';\n\nimport type { IBaseTextureOptions, Texture } from '@pixi/core';\nimport type { ResolvedAsset } from '../../../types';\nimport type { Loader } from '../../Loader';\nimport type { LoaderParser } from '../LoaderParser';\n\nconst validImageExtensions = ['.jpeg', '.jpg', '.png', '.webp', '.avif'];\nconst validImageMIMEs = [\n    'image/jpeg',\n    'image/png',\n    'image/webp',\n    'image/avif',\n];\n\n/**\n * Configuration for the `loadTextures` loader plugin.\n * @memberof PIXI\n * @see PIXI.loadTextures\n */\nexport interface LoadTextureConfig\n{\n    /**\n     * When set to `true`, loading and decoding images will happen with Worker thread,\n     * if available on the browser. This is much more performant as network requests\n     * and decoding can be expensive on the CPU. However, not all environments support\n     * Workers, in some cases it can be helpful to disable by setting to `false`.\n     * @default true\n     */\n    preferWorkers: boolean;\n    /**\n     * When set to `true`, loading and decoding images will happen with `createImageBitmap`,\n     * otherwise it will use `new Image()`.\n     * @default true\n     */\n    preferCreateImageBitmap: boolean;\n    /**\n     * The crossOrigin value to use for images when `preferCreateImageBitmap` is `false`.\n     * @default 'anonymous'\n     */\n    crossOrigin: HTMLImageElement['crossOrigin'];\n}\n\n/**\n * Returns a promise that resolves an ImageBitmaps.\n * This function is designed to be used by a worker.\n * Part of WorkerManager!\n * @param url - The image to load an image bitmap for\n */\nexport async function loadImageBitmap(url: string): Promise<ImageBitmap>\n{\n    const response = await settings.ADAPTER.fetch(url);\n\n    if (!response.ok)\n    {\n        throw new Error(`[loadImageBitmap] Failed to fetch ${url}: `\n            + `${response.status} ${response.statusText}`);\n    }\n\n    const imageBlob = await response.blob();\n    const imageBitmap = await createImageBitmap(imageBlob);\n\n    return imageBitmap;\n}\n\n/**\n * Loads our textures!\n * this makes use of imageBitmaps where available.\n * We load the ImageBitmap on a different thread using the WorkerManager\n * We can then use the ImageBitmap as a source for a Pixi Texture\n *\n * You can customize the behavior of this loader by setting the `config` property.\n * ```js\n * // Set the config\n * import { loadTextures } from '@pixi/assets';\n * loadTextures.config = {\n *    // If true we will use a worker to load the ImageBitmap\n *    preferWorkers: true,\n *    // If false we will use new Image() instead of createImageBitmap\n *    // If false then this will also disable the use of workers as it requires createImageBitmap\n *    preferCreateImageBitmap: true,\n *    crossOrigin: 'anonymous',\n * };\n * ```\n * @memberof PIXI\n */\nexport const loadTextures = {\n\n    name: 'loadTextures',\n\n    extension: {\n        type: ExtensionType.LoadParser,\n        priority: LoaderParserPriority.High,\n    },\n\n    config: {\n        preferWorkers: true,\n        preferCreateImageBitmap: true,\n        crossOrigin: 'anonymous',\n    },\n\n    test(url: string): boolean\n    {\n        return checkDataUrl(url, validImageMIMEs) || checkExtension(url, validImageExtensions);\n    },\n\n    async load(url: string, asset: ResolvedAsset<IBaseTextureOptions>, loader: Loader): Promise<Texture>\n    {\n        const useImageBitmap = globalThis.createImageBitmap && this.config.preferCreateImageBitmap;\n        let src: HTMLImageElement | ImageBitmap;\n\n        if (useImageBitmap)\n        {\n            if (this.config.preferWorkers && await WorkerManager.isImageBitmapSupported())\n            {\n                src = await WorkerManager.loadImageBitmap(url);\n            }\n            else\n            {\n                src = await loadImageBitmap(url);\n            }\n        }\n        else\n        {\n            src = await new Promise((resolve, reject) =>\n            {\n                const src = new Image();\n\n                src.crossOrigin = this.config.crossOrigin;\n                src.src = url;\n                if (src.complete)\n                {\n                    resolve(src);\n                }\n                else\n                {\n                    src.onload = () => resolve(src);\n                    src.onerror = (e) => reject(e);\n                }\n            });\n        }\n\n        const options = { ...asset.data };\n\n        options.resolution ??= utils.getResolutionOfUrl(url);\n        if (useImageBitmap && options.resourceOptions?.ownsImageBitmap === undefined)\n        {\n            options.resourceOptions = { ...options.resourceOptions };\n            options.resourceOptions.ownsImageBitmap = true;\n        }\n\n        const base = new BaseTexture(src, options);\n\n        base.resource.src = url;\n\n        return createTexture(base, loader, url);\n    },\n\n    unload(texture: Texture): void\n    {\n        texture.destroy(true);\n    }\n} as LoaderParser<Texture, IBaseTextureOptions, LoadTextureConfig>;\n\nextensions.add(loadTextures);\n"], "mappings": ";;;;;;AAYA,MAAMA,oBAAA,GAAuB,CAAC,SAAS,QAAQ,QAAQ,SAAS,OAAO;EACjEC,eAAA,GAAkB,CACpB,cACA,aACA,cACA,aACJ;AAoCA,eAAsBC,gBAAgBC,GAAA,EACtC;EACI,MAAMC,QAAA,GAAW,MAAMC,QAAA,CAASC,OAAA,CAAQC,KAAA,CAAMJ,GAAG;EAEjD,IAAI,CAACC,QAAA,CAASI,EAAA,EAEJ,UAAIC,KAAA,CAAM,qCAAqCN,GAAG,KAC/CC,QAAA,CAASM,MAAM,IAAIN,QAAA,CAASO,UAAU,EAAE;EAG/C,MAAAC,SAAA,GAAY,MAAMR,QAAA,CAASS,IAAA;EACb,aAAMC,iBAAA,CAAkBF,SAAS;AAGzD;AAuBO,MAAMG,YAAA,GAAe;EAExBC,IAAA,EAAM;EAENC,SAAA,EAAW;IACPC,IAAA,EAAMC,aAAA,CAAcC,UAAA;IACpBC,QAAA,EAAUC,oBAAA,CAAqBC;EACnC;EAEAC,MAAA,EAAQ;IACJC,aAAA,EAAe;IACfC,uBAAA,EAAyB;IACzBC,WAAA,EAAa;EACjB;EAEAC,KAAKzB,GAAA,EACL;IACI,OAAO0B,YAAA,CAAa1B,GAAA,EAAKF,eAAe,KAAK6B,cAAA,CAAe3B,GAAA,EAAKH,oBAAoB;EACzF;EAEA,MAAM+B,KAAK5B,GAAA,EAAa6B,KAAA,EAA2CC,MAAA,EACnE;IACI,MAAMC,cAAA,GAAiBC,UAAA,CAAWrB,iBAAA,IAAqB,KAAKU,MAAA,CAAOE,uBAAA;IAC/D,IAAAU,GAAA;IAEAF,cAAA,GAEI,KAAKV,MAAA,CAAOC,aAAA,KAAiB,MAAMY,aAAA,CAAcC,sBAAA,MAEjDF,GAAA,GAAM,MAAMC,aAAA,CAAcnC,eAAA,CAAgBC,GAAG,IAI7CiC,GAAA,GAAM,MAAMlC,eAAA,CAAgBC,GAAG,IAKnCiC,GAAA,GAAM,MAAM,IAAIG,OAAA,CAAQ,CAACC,OAAA,EAASC,MAAA,KAClC;MACU,MAAAC,IAAA,GAAM,IAAIC,KAAA;MAEhBD,IAAA,CAAIf,WAAA,GAAc,KAAKH,MAAA,CAAOG,WAAA,EAC9Be,IAAA,CAAIN,GAAA,GAAMjC,GAAA,EACNuC,IAAA,CAAIE,QAAA,GAEJJ,OAAA,CAAQE,IAAG,KAIXA,IAAA,CAAIG,MAAA,GAAS,MAAML,OAAA,CAAQE,IAAG,GAC9BA,IAAA,CAAII,OAAA,GAAWC,CAAA,IAAMN,MAAA,CAAOM,CAAC;IAAA,CAEpC;IAGL,MAAMC,OAAA,GAAU;MAAE,GAAGhB,KAAA,CAAMiB;IAAK;IAExBD,OAAA,CAAAE,UAAA,KAARF,OAAA,CAAQE,UAAA,GAAeC,KAAA,CAAMC,kBAAA,CAAmBjD,GAAG,IAC/C+B,cAAA,IAAkBc,OAAA,CAAQK,eAAA,EAAiBC,eAAA,KAAoB,WAE/DN,OAAA,CAAQK,eAAA,GAAkB;MAAE,GAAGL,OAAA,CAAQK;IAAA,GACvCL,OAAA,CAAQK,eAAA,CAAgBC,eAAA,GAAkB;IAG9C,MAAMC,IAAA,GAAO,IAAIC,WAAA,CAAYpB,GAAA,EAAKY,OAAO;IAEzC,OAAAO,IAAA,CAAKE,QAAA,CAASrB,GAAA,GAAMjC,GAAA,EAEbuD,aAAA,CAAcH,IAAA,EAAMtB,MAAA,EAAQ9B,GAAG;EAC1C;EAEAwD,OAAOC,OAAA,EACP;IACIA,OAAA,CAAQC,OAAA,CAAQ,EAAI;EACxB;AACJ;AAEAC,UAAA,CAAWC,GAAA,CAAIhD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}