{"ast": null, "code": "import { TYPES, FORMATS, BufferResource } from \"@pixi/core\";\nimport { INTERNAL_FORMAT_TO_BYTES_PER_PIXEL } from \"../const.mjs\";\nimport \"../resources/index.mjs\";\nimport { CompressedTextureResource } from \"../resources/CompressedTextureResource.mjs\";\nconst FILE_IDENTIFIER = [171, 75, 84, 88, 32, 49, 49, 187, 13, 10, 26, 10],\n  ENDIANNESS = 67305985,\n  KTX_FIELDS = {\n    FILE_IDENTIFIER: 0,\n    ENDIANNESS: 12,\n    GL_TYPE: 16,\n    GL_TYPE_SIZE: 20,\n    GL_FORMAT: 24,\n    GL_INTERNAL_FORMAT: 28,\n    GL_BASE_INTERNAL_FORMAT: 32,\n    PIXEL_WIDTH: 36,\n    PIXEL_HEIGHT: 40,\n    PIXEL_DEPTH: 44,\n    NUMBER_OF_ARRAY_ELEMENTS: 48,\n    NUMBER_OF_FACES: 52,\n    NUMBER_OF_MIPMAP_LEVELS: 56,\n    BYTES_OF_KEY_VALUE_DATA: 60\n  },\n  FILE_HEADER_SIZE = 64,\n  TYPES_TO_BYTES_PER_COMPONENT = {\n    [TYPES.UNSIGNED_BYTE]: 1,\n    [TYPES.UNSIGNED_SHORT]: 2,\n    [TYPES.INT]: 4,\n    [TYPES.UNSIGNED_INT]: 4,\n    [TYPES.FLOAT]: 4,\n    [TYPES.HALF_FLOAT]: 8\n  },\n  FORMATS_TO_COMPONENTS = {\n    [FORMATS.RGBA]: 4,\n    [FORMATS.RGB]: 3,\n    [FORMATS.RG]: 2,\n    [FORMATS.RED]: 1,\n    [FORMATS.LUMINANCE]: 1,\n    [FORMATS.LUMINANCE_ALPHA]: 2,\n    [FORMATS.ALPHA]: 1\n  },\n  TYPES_TO_BYTES_PER_PIXEL = {\n    [TYPES.UNSIGNED_SHORT_4_4_4_4]: 2,\n    [TYPES.UNSIGNED_SHORT_5_5_5_1]: 2,\n    [TYPES.UNSIGNED_SHORT_5_6_5]: 2\n  };\nfunction parseKTX(url, arrayBuffer, loadKeyValueData = !1) {\n  const dataView = new DataView(arrayBuffer);\n  if (!validate(url, dataView)) return null;\n  const littleEndian = dataView.getUint32(KTX_FIELDS.ENDIANNESS, !0) === ENDIANNESS,\n    glType = dataView.getUint32(KTX_FIELDS.GL_TYPE, littleEndian),\n    glFormat = dataView.getUint32(KTX_FIELDS.GL_FORMAT, littleEndian),\n    glInternalFormat = dataView.getUint32(KTX_FIELDS.GL_INTERNAL_FORMAT, littleEndian),\n    pixelWidth = dataView.getUint32(KTX_FIELDS.PIXEL_WIDTH, littleEndian),\n    pixelHeight = dataView.getUint32(KTX_FIELDS.PIXEL_HEIGHT, littleEndian) || 1,\n    pixelDepth = dataView.getUint32(KTX_FIELDS.PIXEL_DEPTH, littleEndian) || 1,\n    numberOfArrayElements = dataView.getUint32(KTX_FIELDS.NUMBER_OF_ARRAY_ELEMENTS, littleEndian) || 1,\n    numberOfFaces = dataView.getUint32(KTX_FIELDS.NUMBER_OF_FACES, littleEndian),\n    numberOfMipmapLevels = dataView.getUint32(KTX_FIELDS.NUMBER_OF_MIPMAP_LEVELS, littleEndian),\n    bytesOfKeyValueData = dataView.getUint32(KTX_FIELDS.BYTES_OF_KEY_VALUE_DATA, littleEndian);\n  if (pixelHeight === 0 || pixelDepth !== 1) throw new Error(\"Only 2D textures are supported\");\n  if (numberOfFaces !== 1) throw new Error(\"CubeTextures are not supported by KTXLoader yet!\");\n  if (numberOfArrayElements !== 1) throw new Error(\"WebGL does not support array textures\");\n  const blockWidth = 4,\n    blockHeight = 4,\n    alignedWidth = pixelWidth + 3 & -4,\n    alignedHeight = pixelHeight + 3 & -4,\n    imageBuffers = new Array(numberOfArrayElements);\n  let imagePixels = pixelWidth * pixelHeight;\n  glType === 0 && (imagePixels = alignedWidth * alignedHeight);\n  let imagePixelByteSize;\n  if (glType !== 0 ? TYPES_TO_BYTES_PER_COMPONENT[glType] ? imagePixelByteSize = TYPES_TO_BYTES_PER_COMPONENT[glType] * FORMATS_TO_COMPONENTS[glFormat] : imagePixelByteSize = TYPES_TO_BYTES_PER_PIXEL[glType] : imagePixelByteSize = INTERNAL_FORMAT_TO_BYTES_PER_PIXEL[glInternalFormat], imagePixelByteSize === void 0) throw new Error(\"Unable to resolve the pixel format stored in the *.ktx file!\");\n  const kvData = loadKeyValueData ? parseKvData(dataView, bytesOfKeyValueData, littleEndian) : null;\n  let mipByteSize = imagePixels * imagePixelByteSize,\n    mipWidth = pixelWidth,\n    mipHeight = pixelHeight,\n    alignedMipWidth = alignedWidth,\n    alignedMipHeight = alignedHeight,\n    imageOffset = FILE_HEADER_SIZE + bytesOfKeyValueData;\n  for (let mipmapLevel = 0; mipmapLevel < numberOfMipmapLevels; mipmapLevel++) {\n    const imageSize = dataView.getUint32(imageOffset, littleEndian);\n    let elementOffset = imageOffset + 4;\n    for (let arrayElement = 0; arrayElement < numberOfArrayElements; arrayElement++) {\n      let mips = imageBuffers[arrayElement];\n      mips || (mips = imageBuffers[arrayElement] = new Array(numberOfMipmapLevels)), mips[mipmapLevel] = {\n        levelID: mipmapLevel,\n        // don't align mipWidth when texture not compressed! (glType not zero)\n        levelWidth: numberOfMipmapLevels > 1 || glType !== 0 ? mipWidth : alignedMipWidth,\n        levelHeight: numberOfMipmapLevels > 1 || glType !== 0 ? mipHeight : alignedMipHeight,\n        levelBuffer: new Uint8Array(arrayBuffer, elementOffset, mipByteSize)\n      }, elementOffset += mipByteSize;\n    }\n    imageOffset += imageSize + 4, imageOffset = imageOffset % 4 !== 0 ? imageOffset + 4 - imageOffset % 4 : imageOffset, mipWidth = mipWidth >> 1 || 1, mipHeight = mipHeight >> 1 || 1, alignedMipWidth = mipWidth + blockWidth - 1 & ~(blockWidth - 1), alignedMipHeight = mipHeight + blockHeight - 1 & ~(blockHeight - 1), mipByteSize = alignedMipWidth * alignedMipHeight * imagePixelByteSize;\n  }\n  return glType !== 0 ? {\n    uncompressed: imageBuffers.map(levelBuffers => {\n      let buffer = levelBuffers[0].levelBuffer,\n        convertToInt = !1;\n      return glType === TYPES.FLOAT ? buffer = new Float32Array(levelBuffers[0].levelBuffer.buffer, levelBuffers[0].levelBuffer.byteOffset, levelBuffers[0].levelBuffer.byteLength / 4) : glType === TYPES.UNSIGNED_INT ? (convertToInt = !0, buffer = new Uint32Array(levelBuffers[0].levelBuffer.buffer, levelBuffers[0].levelBuffer.byteOffset, levelBuffers[0].levelBuffer.byteLength / 4)) : glType === TYPES.INT && (convertToInt = !0, buffer = new Int32Array(levelBuffers[0].levelBuffer.buffer, levelBuffers[0].levelBuffer.byteOffset, levelBuffers[0].levelBuffer.byteLength / 4)), {\n        resource: new BufferResource(buffer, {\n          width: levelBuffers[0].levelWidth,\n          height: levelBuffers[0].levelHeight\n        }),\n        type: glType,\n        format: convertToInt ? convertFormatToInteger(glFormat) : glFormat\n      };\n    }),\n    kvData\n  } : {\n    compressed: imageBuffers.map(levelBuffers => new CompressedTextureResource(null, {\n      format: glInternalFormat,\n      width: pixelWidth,\n      height: pixelHeight,\n      levels: numberOfMipmapLevels,\n      levelBuffers\n    })),\n    kvData\n  };\n}\nfunction validate(url, dataView) {\n  for (let i = 0; i < FILE_IDENTIFIER.length; i++) if (dataView.getUint8(i) !== FILE_IDENTIFIER[i]) return console.error(`${url} is not a valid *.ktx file!`), !1;\n  return !0;\n}\nfunction convertFormatToInteger(format) {\n  switch (format) {\n    case FORMATS.RGBA:\n      return FORMATS.RGBA_INTEGER;\n    case FORMATS.RGB:\n      return FORMATS.RGB_INTEGER;\n    case FORMATS.RG:\n      return FORMATS.RG_INTEGER;\n    case FORMATS.RED:\n      return FORMATS.RED_INTEGER;\n    default:\n      return format;\n  }\n}\nfunction parseKvData(dataView, bytesOfKeyValueData, littleEndian) {\n  const kvData = /* @__PURE__ */new Map();\n  let bytesIntoKeyValueData = 0;\n  for (; bytesIntoKeyValueData < bytesOfKeyValueData;) {\n    const keyAndValueByteSize = dataView.getUint32(FILE_HEADER_SIZE + bytesIntoKeyValueData, littleEndian),\n      keyAndValueByteOffset = FILE_HEADER_SIZE + bytesIntoKeyValueData + 4,\n      valuePadding = 3 - (keyAndValueByteSize + 3) % 4;\n    if (keyAndValueByteSize === 0 || keyAndValueByteSize > bytesOfKeyValueData - bytesIntoKeyValueData) {\n      console.error(\"KTXLoader: keyAndValueByteSize out of bounds\");\n      break;\n    }\n    let keyNulByte = 0;\n    for (; keyNulByte < keyAndValueByteSize && dataView.getUint8(keyAndValueByteOffset + keyNulByte) !== 0; keyNulByte++);\n    if (keyNulByte === -1) {\n      console.error(\"KTXLoader: Failed to find null byte terminating kvData key\");\n      break;\n    }\n    const key = new TextDecoder().decode(new Uint8Array(dataView.buffer, keyAndValueByteOffset, keyNulByte)),\n      value = new DataView(dataView.buffer, keyAndValueByteOffset + keyNulByte + 1, keyAndValueByteSize - keyNulByte - 1);\n    kvData.set(key, value), bytesIntoKeyValueData += 4 + keyAndValueByteSize + valuePadding;\n  }\n  return kvData;\n}\nexport { FORMATS_TO_COMPONENTS, TYPES_TO_BYTES_PER_COMPONENT, TYPES_TO_BYTES_PER_PIXEL, parseKTX };", "map": {"version": 3, "names": ["FILE_IDENTIFIER", "ENDIANNESS", "KTX_FIELDS", "GL_TYPE", "GL_TYPE_SIZE", "GL_FORMAT", "GL_INTERNAL_FORMAT", "GL_BASE_INTERNAL_FORMAT", "PIXEL_WIDTH", "PIXEL_HEIGHT", "PIXEL_DEPTH", "NUMBER_OF_ARRAY_ELEMENTS", "NUMBER_OF_FACES", "NUMBER_OF_MIPMAP_LEVELS", "BYTES_OF_KEY_VALUE_DATA", "FILE_HEADER_SIZE", "TYPES_TO_BYTES_PER_COMPONENT", "TYPES", "UNSIGNED_BYTE", "UNSIGNED_SHORT", "INT", "UNSIGNED_INT", "FLOAT", "HALF_FLOAT", "FORMATS_TO_COMPONENTS", "FORMATS", "RGBA", "RGB", "RG", "RED", "LUMINANCE", "LUMINANCE_ALPHA", "ALPHA", "TYPES_TO_BYTES_PER_PIXEL", "UNSIGNED_SHORT_4_4_4_4", "UNSIGNED_SHORT_5_5_5_1", "UNSIGNED_SHORT_5_6_5", "parseKTX", "url", "arrayBuffer", "loadKeyValueData", "dataView", "DataView", "validate", "littleEndian", "getUint32", "glType", "glFormat", "glInternalFormat", "pixelWidth", "pixelHeight", "pixelDepth", "numberOfArrayElements", "numberOfFaces", "numberOfMipmapLevels", "bytesOfKeyValueData", "Error", "blockWidth", "blockHeight", "alignedWidth", "alignedHeight", "imageBuffers", "Array", "imagePixels", "imagePixelByteSize", "INTERNAL_FORMAT_TO_BYTES_PER_PIXEL", "kvData", "parseKvData", "mipByteSize", "mip<PERSON><PERSON><PERSON>", "mipHeight", "alignedMipW<PERSON>th", "alignedMipHeight", "imageOffset", "mipmapLevel", "imageSize", "elementOffset", "arrayElement", "mips", "levelID", "levelWidth", "levelHeight", "<PERSON><PERSON><PERSON><PERSON>", "Uint8Array", "uncompressed", "map", "levelBuffers", "buffer", "convertToInt", "Float32Array", "byteOffset", "byteLength", "Uint32Array", "Int32Array", "resource", "BufferResource", "width", "height", "type", "format", "convertFormatToInteger", "compressed", "CompressedTextureResource", "levels", "i", "length", "getUint8", "console", "error", "RGBA_INTEGER", "RGB_INTEGER", "RG_INTEGER", "RED_INTEGER", "Map", "bytesIntoKeyValueData", "keyAndValueByteSize", "keyAndValueByteOffset", "valuePadding", "keyNulByte", "key", "TextDecoder", "decode", "value", "set"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\compressed-textures\\src\\parsers\\parseKTX.ts"], "sourcesContent": ["import { BufferResource, FORMATS, TYPES } from '@pixi/core';\nimport { INTERNAL_FORMAT_TO_BYTES_PER_PIXEL } from '../const';\nimport { CompressedTextureResource } from '../resources';\n\nimport type { CompressedLevelBuffer } from '../resources';\n\n/**\n * The 12-byte KTX file identifier\n * @see https://www.khronos.org/opengles/sdk/tools/KTX/file_format_spec/#2.1\n * @ignore\n */\nconst FILE_IDENTIFIER = [0xAB, 0x4B, 0x54, 0x58, 0x20, 0x31, 0x31, 0xBB, 0x0D, 0x0A, 0x1A, 0x0A];\n\n/**\n * The value stored in the \"endianness\" field.\n * @see https://www.khronos.org/opengles/sdk/tools/KTX/file_format_spec/#2.2\n * @ignore\n */\nconst ENDIANNESS = 0x04030201;\n\n/**\n * Byte offsets of the KTX file header fields\n * @ignore\n */\nconst KTX_FIELDS = {\n    FILE_IDENTIFIER: 0,\n    ENDIANNESS: 12,\n    GL_TYPE: 16,\n    GL_TYPE_SIZE: 20,\n    GL_FORMAT: 24,\n    GL_INTERNAL_FORMAT: 28,\n    GL_BASE_INTERNAL_FORMAT: 32,\n    PIXEL_WIDTH: 36,\n    PIXEL_HEIGHT: 40,\n    PIXEL_DEPTH: 44,\n    NUMBER_OF_ARRAY_ELEMENTS: 48,\n    NUMBER_OF_FACES: 52,\n    NUMBER_OF_MIPMAP_LEVELS: 56,\n    BYTES_OF_KEY_VALUE_DATA: 60\n};\n\n/**\n * Byte size of the file header fields in {@code KTX_FIELDS}\n * @ignore\n */\nconst FILE_HEADER_SIZE = 64;\n\n/**\n * Maps {@link PIXI.TYPES} to the bytes taken per component, excluding those ones that are bit-fields.\n * @ignore\n */\nexport const TYPES_TO_BYTES_PER_COMPONENT: { [id: number]: number } = {\n    [TYPES.UNSIGNED_BYTE]: 1,\n    [TYPES.UNSIGNED_SHORT]: 2,\n    [TYPES.INT]: 4,\n    [TYPES.UNSIGNED_INT]: 4,\n    [TYPES.FLOAT]: 4,\n    [TYPES.HALF_FLOAT]: 8\n};\n\n/**\n * Number of components in each {@link PIXI.FORMATS}\n * @ignore\n */\nexport const FORMATS_TO_COMPONENTS: { [id: number]: number } = {\n    [FORMATS.RGBA]: 4,\n    [FORMATS.RGB]: 3,\n    [FORMATS.RG]: 2,\n    [FORMATS.RED]: 1,\n    [FORMATS.LUMINANCE]: 1,\n    [FORMATS.LUMINANCE_ALPHA]: 2,\n    [FORMATS.ALPHA]: 1\n};\n\n/**\n * Number of bytes per pixel in bit-field types in {@link PIXI.TYPES}\n * @ignore\n */\nexport const TYPES_TO_BYTES_PER_PIXEL: { [id: number]: number } = {\n    [TYPES.UNSIGNED_SHORT_4_4_4_4]: 2,\n    [TYPES.UNSIGNED_SHORT_5_5_5_1]: 2,\n    [TYPES.UNSIGNED_SHORT_5_6_5]: 2\n};\n\nexport function parseKTX(url: string, arrayBuffer: ArrayBuffer, loadKeyValueData = false): {\n    compressed?: CompressedTextureResource[]\n    uncompressed?: { resource: BufferResource, type: TYPES, format: FORMATS }[]\n    kvData: Map<string, DataView> | null\n}\n{\n    const dataView = new DataView(arrayBuffer);\n\n    if (!validate(url, dataView))\n    {\n        return null;\n    }\n\n    const littleEndian = dataView.getUint32(KTX_FIELDS.ENDIANNESS, true) === ENDIANNESS;\n    const glType = dataView.getUint32(KTX_FIELDS.GL_TYPE, littleEndian);\n    // const glTypeSize = dataView.getUint32(KTX_FIELDS.GL_TYPE_SIZE, littleEndian);\n    const glFormat = dataView.getUint32(KTX_FIELDS.GL_FORMAT, littleEndian);\n    const glInternalFormat = dataView.getUint32(KTX_FIELDS.GL_INTERNAL_FORMAT, littleEndian);\n    const pixelWidth = dataView.getUint32(KTX_FIELDS.PIXEL_WIDTH, littleEndian);\n    const pixelHeight = dataView.getUint32(KTX_FIELDS.PIXEL_HEIGHT, littleEndian) || 1;// \"pixelHeight = 0\" -> \"1\"\n    const pixelDepth = dataView.getUint32(KTX_FIELDS.PIXEL_DEPTH, littleEndian) || 1;// ^^\n    const numberOfArrayElements = dataView.getUint32(KTX_FIELDS.NUMBER_OF_ARRAY_ELEMENTS, littleEndian) || 1;// ^^\n    const numberOfFaces = dataView.getUint32(KTX_FIELDS.NUMBER_OF_FACES, littleEndian);\n    const numberOfMipmapLevels = dataView.getUint32(KTX_FIELDS.NUMBER_OF_MIPMAP_LEVELS, littleEndian);\n    const bytesOfKeyValueData = dataView.getUint32(KTX_FIELDS.BYTES_OF_KEY_VALUE_DATA, littleEndian);\n\n    // Whether the platform architecture is little endian. If littleEndian !== platformLittleEndian, then the\n    // file contents must be endian-converted!\n    // TODO: Endianness conversion\n    // const platformLittleEndian = new Uint8Array((new Uint32Array([ENDIANNESS])).buffer)[0] === 0x01;\n\n    if (pixelHeight === 0 || pixelDepth !== 1)\n    {\n        throw new Error('Only 2D textures are supported');\n    }\n    if (numberOfFaces !== 1)\n    {\n        throw new Error('CubeTextures are not supported by KTXLoader yet!');\n    }\n    if (numberOfArrayElements !== 1)\n    {\n        // TODO: Support splitting array-textures into multiple BaseTextures\n        throw new Error('WebGL does not support array textures');\n    }\n\n    // TODO: 8x4 blocks for 2bpp pvrtc\n    const blockWidth = 4;\n    const blockHeight = 4;\n\n    const alignedWidth = (pixelWidth + 3) & ~3;\n    const alignedHeight = (pixelHeight + 3) & ~3;\n    const imageBuffers = new Array<CompressedLevelBuffer[]>(numberOfArrayElements);\n    let imagePixels = pixelWidth * pixelHeight;\n\n    if (glType === 0)\n    {\n        // Align to 16 pixels (4x4 blocks)\n        imagePixels = alignedWidth * alignedHeight;\n    }\n\n    let imagePixelByteSize: number;\n\n    if (glType !== 0)\n    {\n        // Uncompressed texture format\n        if (TYPES_TO_BYTES_PER_COMPONENT[glType])\n        {\n            imagePixelByteSize = TYPES_TO_BYTES_PER_COMPONENT[glType] * FORMATS_TO_COMPONENTS[glFormat];\n        }\n        else\n        {\n            imagePixelByteSize = TYPES_TO_BYTES_PER_PIXEL[glType];\n        }\n    }\n    else\n    {\n        imagePixelByteSize = INTERNAL_FORMAT_TO_BYTES_PER_PIXEL[glInternalFormat];\n    }\n\n    if (imagePixelByteSize === undefined)\n    {\n        throw new Error('Unable to resolve the pixel format stored in the *.ktx file!');\n    }\n\n    const kvData: Map<string, DataView> | null = loadKeyValueData\n        ? parseKvData(dataView, bytesOfKeyValueData, littleEndian)\n        : null;\n\n    const imageByteSize = imagePixels * imagePixelByteSize;\n    let mipByteSize = imageByteSize;\n    let mipWidth = pixelWidth;\n    let mipHeight = pixelHeight;\n    let alignedMipWidth = alignedWidth;\n    let alignedMipHeight = alignedHeight;\n    let imageOffset = FILE_HEADER_SIZE + bytesOfKeyValueData;\n\n    for (let mipmapLevel = 0; mipmapLevel < numberOfMipmapLevels; mipmapLevel++)\n    {\n        const imageSize = dataView.getUint32(imageOffset, littleEndian);\n        let elementOffset = imageOffset + 4;\n\n        for (let arrayElement = 0; arrayElement < numberOfArrayElements; arrayElement++)\n        {\n            // TODO: Maybe support 3D textures? :-)\n            // for (let zSlice = 0; zSlice < pixelDepth; zSlice)\n\n            let mips = imageBuffers[arrayElement];\n\n            if (!mips)\n            {\n                mips = imageBuffers[arrayElement] = new Array(numberOfMipmapLevels);\n            }\n\n            mips[mipmapLevel] = {\n                levelID: mipmapLevel,\n\n                // don't align mipWidth when texture not compressed! (glType not zero)\n                levelWidth: numberOfMipmapLevels > 1 || glType !== 0 ? mipWidth : alignedMipWidth,\n                levelHeight: numberOfMipmapLevels > 1 || glType !== 0 ? mipHeight : alignedMipHeight,\n                levelBuffer: new Uint8Array(arrayBuffer, elementOffset, mipByteSize)\n            };\n            elementOffset += mipByteSize;\n        }\n\n        // HINT: Aligns to 4-byte boundary after jumping imageSize (in lieu of mipPadding)\n        imageOffset += imageSize + 4;// (+4 to jump the imageSize field itself)\n        imageOffset = imageOffset % 4 !== 0 ? imageOffset + 4 - (imageOffset % 4) : imageOffset;\n\n        // Calculate mipWidth, mipHeight for _next_ iteration\n        mipWidth = (mipWidth >> 1) || 1;\n        mipHeight = (mipHeight >> 1) || 1;\n        alignedMipWidth = (mipWidth + blockWidth - 1) & ~(blockWidth - 1);\n        alignedMipHeight = (mipHeight + blockHeight - 1) & ~(blockHeight - 1);\n\n        // Each mipmap level is 4-times smaller?\n        mipByteSize = alignedMipWidth * alignedMipHeight * imagePixelByteSize;\n    }\n\n    // We use the levelBuffers feature of CompressedTextureResource b/c texture data is image-major, not level-major.\n    if (glType !== 0)\n    {\n        return {\n            uncompressed: imageBuffers.map((levelBuffers) =>\n            {\n                let buffer: Float32Array | Uint32Array | Int32Array | Uint8Array = levelBuffers[0].levelBuffer;\n                let convertToInt = false;\n\n                if (glType === TYPES.FLOAT)\n                {\n                    buffer = new Float32Array(\n                        levelBuffers[0].levelBuffer.buffer,\n                        levelBuffers[0].levelBuffer.byteOffset,\n                        levelBuffers[0].levelBuffer.byteLength / 4);\n                }\n                else if (glType === TYPES.UNSIGNED_INT)\n                {\n                    convertToInt = true;\n                    buffer = new Uint32Array(\n                        levelBuffers[0].levelBuffer.buffer,\n                        levelBuffers[0].levelBuffer.byteOffset,\n                        levelBuffers[0].levelBuffer.byteLength / 4);\n                }\n                else if (glType === TYPES.INT)\n                {\n                    convertToInt = true;\n                    buffer = new Int32Array(\n                        levelBuffers[0].levelBuffer.buffer,\n                        levelBuffers[0].levelBuffer.byteOffset,\n                        levelBuffers[0].levelBuffer.byteLength / 4);\n                }\n\n                return {\n                    resource: new BufferResource(\n                        buffer,\n                        {\n                            width: levelBuffers[0].levelWidth,\n                            height: levelBuffers[0].levelHeight,\n                        }\n                    ),\n                    type: glType,\n                    format: convertToInt ? convertFormatToInteger(glFormat) : glFormat,\n                };\n            }),\n            kvData\n        };\n    }\n\n    return {\n        compressed: imageBuffers.map((levelBuffers) => new CompressedTextureResource(null, {\n            format: glInternalFormat,\n            width: pixelWidth,\n            height: pixelHeight,\n            levels: numberOfMipmapLevels,\n            levelBuffers,\n        })),\n        kvData\n    };\n}\n\n/**\n * Checks whether the arrayBuffer contains a valid *.ktx file.\n * @param url\n * @param dataView\n */\nfunction validate(url: string, dataView: DataView): boolean\n{\n    // NOTE: Do not optimize this into 3 32-bit integer comparison because the endianness\n    // of the data is not specified.\n    for (let i = 0; i < FILE_IDENTIFIER.length; i++)\n    {\n        if (dataView.getUint8(i) !== FILE_IDENTIFIER[i])\n        {\n            if (process.env.DEBUG)\n            {\n                console.error(`${url} is not a valid *.ktx file!`);\n            }\n\n            return false;\n        }\n    }\n\n    return true;\n}\n\nfunction convertFormatToInteger(format: FORMATS)\n{\n    switch (format)\n    {\n        case FORMATS.RGBA: return FORMATS.RGBA_INTEGER;\n        case FORMATS.RGB: return FORMATS.RGB_INTEGER;\n        case FORMATS.RG: return FORMATS.RG_INTEGER;\n        case FORMATS.RED: return FORMATS.RED_INTEGER;\n        default: return format;\n    }\n}\n\nfunction parseKvData(dataView: DataView, bytesOfKeyValueData: number, littleEndian: boolean): Map<string, DataView>\n{\n    const kvData = new Map<string, DataView>();\n    let bytesIntoKeyValueData = 0;\n\n    while (bytesIntoKeyValueData < bytesOfKeyValueData)\n    {\n        const keyAndValueByteSize = dataView.getUint32(FILE_HEADER_SIZE + bytesIntoKeyValueData, littleEndian);\n        const keyAndValueByteOffset = FILE_HEADER_SIZE + bytesIntoKeyValueData + 4;\n        const valuePadding = 3 - ((keyAndValueByteSize + 3) % 4);\n\n        // Bounds check\n        if (keyAndValueByteSize === 0 || keyAndValueByteSize > bytesOfKeyValueData - bytesIntoKeyValueData)\n        {\n            console.error('KTXLoader: keyAndValueByteSize out of bounds');\n            break;\n        }\n\n        // Note: keyNulByte can't be 0 otherwise the key is an empty string.\n        let keyNulByte = 0;\n\n        for (; keyNulByte < keyAndValueByteSize; keyNulByte++)\n        {\n            if (dataView.getUint8(keyAndValueByteOffset + keyNulByte) === 0x00)\n            {\n                break;\n            }\n        }\n\n        if (keyNulByte === -1)\n        {\n            console.error('KTXLoader: Failed to find null byte terminating kvData key');\n            break;\n        }\n\n        const key = new TextDecoder().decode(\n            new Uint8Array(dataView.buffer, keyAndValueByteOffset, keyNulByte)\n        );\n        const value = new DataView(\n            dataView.buffer,\n            keyAndValueByteOffset + keyNulByte + 1,\n            keyAndValueByteSize - keyNulByte - 1,\n        );\n\n        kvData.set(key, value);\n\n        // 4 = the keyAndValueByteSize field itself\n        // keyAndValueByteSize = the bytes taken by the key and value\n        // valuePadding = extra padding to align with 4 bytes\n        bytesIntoKeyValueData += 4 + keyAndValueByteSize + valuePadding;\n    }\n\n    return kvData;\n}\n"], "mappings": ";;;;AAWA,MAAMA,eAAA,GAAkB,CAAC,KAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,KAAM,IAAM,IAAM,IAAM,EAAI;EAOzFC,UAAA,GAAa;EAMbC,UAAA,GAAa;IACfF,eAAA,EAAiB;IACjBC,UAAA,EAAY;IACZE,OAAA,EAAS;IACTC,YAAA,EAAc;IACdC,SAAA,EAAW;IACXC,kBAAA,EAAoB;IACpBC,uBAAA,EAAyB;IACzBC,WAAA,EAAa;IACbC,YAAA,EAAc;IACdC,WAAA,EAAa;IACbC,wBAAA,EAA0B;IAC1BC,eAAA,EAAiB;IACjBC,uBAAA,EAAyB;IACzBC,uBAAA,EAAyB;EAC7B;EAMMC,gBAAA,GAAmB;EAMZC,4BAAA,GAAyD;IAClE,CAACC,KAAA,CAAMC,aAAa,GAAG;IACvB,CAACD,KAAA,CAAME,cAAc,GAAG;IACxB,CAACF,KAAA,CAAMG,GAAG,GAAG;IACb,CAACH,KAAA,CAAMI,YAAY,GAAG;IACtB,CAACJ,KAAA,CAAMK,KAAK,GAAG;IACf,CAACL,KAAA,CAAMM,UAAU,GAAG;EACxB;EAMaC,qBAAA,GAAkD;IAC3D,CAACC,OAAA,CAAQC,IAAI,GAAG;IAChB,CAACD,OAAA,CAAQE,GAAG,GAAG;IACf,CAACF,OAAA,CAAQG,EAAE,GAAG;IACd,CAACH,OAAA,CAAQI,GAAG,GAAG;IACf,CAACJ,OAAA,CAAQK,SAAS,GAAG;IACrB,CAACL,OAAA,CAAQM,eAAe,GAAG;IAC3B,CAACN,OAAA,CAAQO,KAAK,GAAG;EACrB;EAMaC,wBAAA,GAAqD;IAC9D,CAAChB,KAAA,CAAMiB,sBAAsB,GAAG;IAChC,CAACjB,KAAA,CAAMkB,sBAAsB,GAAG;IAChC,CAAClB,KAAA,CAAMmB,oBAAoB,GAAG;EAClC;AAEO,SAASC,SAASC,GAAA,EAAaC,WAAA,EAA0BC,gBAAA,GAAmB,IAKnF;EACU,MAAAC,QAAA,GAAW,IAAIC,QAAA,CAASH,WAAW;EAErC,KAACI,QAAA,CAASL,GAAA,EAAKG,QAAQ,GAEhB;EAGX,MAAMG,YAAA,GAAeH,QAAA,CAASI,SAAA,CAAU3C,UAAA,CAAWD,UAAA,EAAY,EAAI,MAAMA,UAAA;IACnE6C,MAAA,GAASL,QAAA,CAASI,SAAA,CAAU3C,UAAA,CAAWC,OAAA,EAASyC,YAAY;IAE5DG,QAAA,GAAWN,QAAA,CAASI,SAAA,CAAU3C,UAAA,CAAWG,SAAA,EAAWuC,YAAY;IAChEI,gBAAA,GAAmBP,QAAA,CAASI,SAAA,CAAU3C,UAAA,CAAWI,kBAAA,EAAoBsC,YAAY;IACjFK,UAAA,GAAaR,QAAA,CAASI,SAAA,CAAU3C,UAAA,CAAWM,WAAA,EAAaoC,YAAY;IACpEM,WAAA,GAAcT,QAAA,CAASI,SAAA,CAAU3C,UAAA,CAAWO,YAAA,EAAcmC,YAAY,KAAK;IAC3EO,UAAA,GAAaV,QAAA,CAASI,SAAA,CAAU3C,UAAA,CAAWQ,WAAA,EAAakC,YAAY,KAAK;IACzEQ,qBAAA,GAAwBX,QAAA,CAASI,SAAA,CAAU3C,UAAA,CAAWS,wBAAA,EAA0BiC,YAAY,KAAK;IACjGS,aAAA,GAAgBZ,QAAA,CAASI,SAAA,CAAU3C,UAAA,CAAWU,eAAA,EAAiBgC,YAAY;IAC3EU,oBAAA,GAAuBb,QAAA,CAASI,SAAA,CAAU3C,UAAA,CAAWW,uBAAA,EAAyB+B,YAAY;IAC1FW,mBAAA,GAAsBd,QAAA,CAASI,SAAA,CAAU3C,UAAA,CAAWY,uBAAA,EAAyB8B,YAAY;EAO3F,IAAAM,WAAA,KAAgB,KAAKC,UAAA,KAAe,GAE9B,UAAIK,KAAA,CAAM,gCAAgC;EAEpD,IAAIH,aAAA,KAAkB,GAEZ,UAAIG,KAAA,CAAM,kDAAkD;EAEtE,IAAIJ,qBAAA,KAA0B,GAGpB,UAAII,KAAA,CAAM,uCAAuC;EAI3D,MAAMC,UAAA,GAAa;IACbC,WAAA,GAAc;IAEdC,YAAA,GAAgBV,UAAA,GAAa,IAAK;IAClCW,aAAA,GAAiBV,WAAA,GAAc,IAAK;IACpCW,YAAA,GAAe,IAAIC,KAAA,CAA+BV,qBAAqB;EAC7E,IAAIW,WAAA,GAAcd,UAAA,GAAaC,WAAA;EAE3BJ,MAAA,KAAW,MAGXiB,WAAA,GAAcJ,YAAA,GAAeC,aAAA;EAG7B,IAAAI,kBAAA;EAEA,IAAAlB,MAAA,KAAW,IAGP9B,4BAAA,CAA6B8B,MAAM,IAEnCkB,kBAAA,GAAqBhD,4BAAA,CAA6B8B,MAAM,IAAItB,qBAAA,CAAsBuB,QAAQ,IAI1FiB,kBAAA,GAAqB/B,wBAAA,CAAyBa,MAAM,IAKxDkB,kBAAA,GAAqBC,kCAAA,CAAmCjB,gBAAgB,GAGxEgB,kBAAA,KAAuB,QAEjB,UAAIR,KAAA,CAAM,8DAA8D;EAGlF,MAAMU,MAAA,GAAuC1B,gBAAA,GACvC2B,WAAA,CAAY1B,QAAA,EAAUc,mBAAA,EAAqBX,YAAY,IACvD;EAGN,IAAIwB,WAAA,GADkBL,WAAA,GAAcC,kBAAA;IAEhCK,QAAA,GAAWpB,UAAA;IACXqB,SAAA,GAAYpB,WAAA;IACZqB,eAAA,GAAkBZ,YAAA;IAClBa,gBAAA,GAAmBZ,aAAA;IACnBa,WAAA,GAAc1D,gBAAA,GAAmBwC,mBAAA;EAErC,SAASmB,WAAA,GAAc,GAAGA,WAAA,GAAcpB,oBAAA,EAAsBoB,WAAA,IAC9D;IACI,MAAMC,SAAA,GAAYlC,QAAA,CAASI,SAAA,CAAU4B,WAAA,EAAa7B,YAAY;IAC9D,IAAIgC,aAAA,GAAgBH,WAAA,GAAc;IAElC,SAASI,YAAA,GAAe,GAAGA,YAAA,GAAezB,qBAAA,EAAuByB,YAAA,IACjE;MAIQ,IAAAC,IAAA,GAAOjB,YAAA,CAAagB,YAAY;MAE/BC,IAAA,KAEDA,IAAA,GAAOjB,YAAA,CAAagB,YAAY,IAAI,IAAIf,KAAA,CAAMR,oBAAoB,IAGtEwB,IAAA,CAAKJ,WAAW,IAAI;QAChBK,OAAA,EAASL,WAAA;QAAA;QAGTM,UAAA,EAAY1B,oBAAA,GAAuB,KAAKR,MAAA,KAAW,IAAIuB,QAAA,GAAWE,eAAA;QAClEU,WAAA,EAAa3B,oBAAA,GAAuB,KAAKR,MAAA,KAAW,IAAIwB,SAAA,GAAYE,gBAAA;QACpEU,WAAA,EAAa,IAAIC,UAAA,CAAW5C,WAAA,EAAaqC,aAAA,EAAeR,WAAW;MAAA,GAEvEQ,aAAA,IAAiBR,WAAA;IACrB;IAGAK,WAAA,IAAeE,SAAA,GAAY,GAC3BF,WAAA,GAAcA,WAAA,GAAc,MAAM,IAAIA,WAAA,GAAc,IAAKA,WAAA,GAAc,IAAKA,WAAA,EAG5EJ,QAAA,GAAYA,QAAA,IAAY,KAAM,GAC9BC,SAAA,GAAaA,SAAA,IAAa,KAAM,GAChCC,eAAA,GAAmBF,QAAA,GAAWZ,UAAA,GAAa,IAAK,EAAEA,UAAA,GAAa,IAC/De,gBAAA,GAAoBF,SAAA,GAAYZ,WAAA,GAAc,IAAK,EAAEA,WAAA,GAAc,IAGnEU,WAAA,GAAcG,eAAA,GAAkBC,gBAAA,GAAmBR,kBAAA;EACvD;EAGA,OAAIlB,MAAA,KAAW,IAEJ;IACHsC,YAAA,EAAcvB,YAAA,CAAawB,GAAA,CAAKC,YAAA,IAChC;MACI,IAAIC,MAAA,GAA+DD,YAAA,CAAa,CAAC,EAAEJ,WAAA;QAC/EM,YAAA,GAAe;MAEnB,OAAI1C,MAAA,KAAW7B,KAAA,CAAMK,KAAA,GAEjBiE,MAAA,GAAS,IAAIE,YAAA,CACTH,YAAA,CAAa,CAAC,EAAEJ,WAAA,CAAYK,MAAA,EAC5BD,YAAA,CAAa,CAAC,EAAEJ,WAAA,CAAYQ,UAAA,EAC5BJ,YAAA,CAAa,CAAC,EAAEJ,WAAA,CAAYS,UAAA,GAAa,KAExC7C,MAAA,KAAW7B,KAAA,CAAMI,YAAA,IAEtBmE,YAAA,GAAe,IACfD,MAAA,GAAS,IAAIK,WAAA,CACTN,YAAA,CAAa,CAAC,EAAEJ,WAAA,CAAYK,MAAA,EAC5BD,YAAA,CAAa,CAAC,EAAEJ,WAAA,CAAYQ,UAAA,EAC5BJ,YAAA,CAAa,CAAC,EAAEJ,WAAA,CAAYS,UAAA,GAAa,MAExC7C,MAAA,KAAW7B,KAAA,CAAMG,GAAA,KAEtBoE,YAAA,GAAe,IACfD,MAAA,GAAS,IAAIM,UAAA,CACTP,YAAA,CAAa,CAAC,EAAEJ,WAAA,CAAYK,MAAA,EAC5BD,YAAA,CAAa,CAAC,EAAEJ,WAAA,CAAYQ,UAAA,EAC5BJ,YAAA,CAAa,CAAC,EAAEJ,WAAA,CAAYS,UAAA,GAAa,KAG1C;QACHG,QAAA,EAAU,IAAIC,cAAA,CACVR,MAAA,EACA;UACIS,KAAA,EAAOV,YAAA,CAAa,CAAC,EAAEN,UAAA;UACvBiB,MAAA,EAAQX,YAAA,CAAa,CAAC,EAAEL;QAC5B,CACJ;QACAiB,IAAA,EAAMpD,MAAA;QACNqD,MAAA,EAAQX,YAAA,GAAeY,sBAAA,CAAuBrD,QAAQ,IAAIA;MAAA;IAC9D,CACH;IACDmB;EAAA,IAID;IACHmC,UAAA,EAAYxC,YAAA,CAAawB,GAAA,CAAKC,YAAA,IAAiB,IAAIgB,yBAAA,CAA0B,MAAM;MAC/EH,MAAA,EAAQnD,gBAAA;MACRgD,KAAA,EAAO/C,UAAA;MACPgD,MAAA,EAAQ/C,WAAA;MACRqD,MAAA,EAAQjD,oBAAA;MACRgC;IAAA,CACH,CAAC;IACFpB;EAAA;AAER;AAOA,SAASvB,SAASL,GAAA,EAAaG,QAAA,EAC/B;EAGI,SAAS+D,CAAA,GAAI,GAAGA,CAAA,GAAIxG,eAAA,CAAgByG,MAAA,EAAQD,CAAA,IAExC,IAAI/D,QAAA,CAASiE,QAAA,CAASF,CAAC,MAAMxG,eAAA,CAAgBwG,CAAC,GAItC,OAAAG,OAAA,CAAQC,KAAA,CAAM,GAAGtE,GAAG,6BAA6B,GAG9C;EAIR;AACX;AAEA,SAAS8D,uBAAuBD,MAAA,EAChC;EACI,QAAQA,MAAA;IAEJ,KAAK1E,OAAA,CAAQC,IAAA;MAAM,OAAOD,OAAA,CAAQoF,YAAA;IAClC,KAAKpF,OAAA,CAAQE,GAAA;MAAK,OAAOF,OAAA,CAAQqF,WAAA;IACjC,KAAKrF,OAAA,CAAQG,EAAA;MAAI,OAAOH,OAAA,CAAQsF,UAAA;IAChC,KAAKtF,OAAA,CAAQI,GAAA;MAAK,OAAOJ,OAAA,CAAQuF,WAAA;IACjC;MAAgB,OAAAb,MAAA;EACpB;AACJ;AAEA,SAAShC,YAAY1B,QAAA,EAAoBc,mBAAA,EAA6BX,YAAA,EACtE;EACU,MAAAsB,MAAA,sBAAa+C,GAAA;EACnB,IAAIC,qBAAA,GAAwB;EAE5B,OAAOA,qBAAA,GAAwB3D,mBAAA,GAC/B;IACI,MAAM4D,mBAAA,GAAsB1E,QAAA,CAASI,SAAA,CAAU9B,gBAAA,GAAmBmG,qBAAA,EAAuBtE,YAAY;MAC/FwE,qBAAA,GAAwBrG,gBAAA,GAAmBmG,qBAAA,GAAwB;MACnEG,YAAA,GAAe,KAAMF,mBAAA,GAAsB,KAAK;IAGtD,IAAIA,mBAAA,KAAwB,KAAKA,mBAAA,GAAsB5D,mBAAA,GAAsB2D,qBAAA,EAC7E;MACIP,OAAA,CAAQC,KAAA,CAAM,8CAA8C;MAC5D;IACJ;IAGA,IAAIU,UAAA,GAAa;IAEjB,OAAOA,UAAA,GAAaH,mBAAA,IAEZ1E,QAAA,CAASiE,QAAA,CAASU,qBAAA,GAAwBE,UAAU,MAAM,GAFzBA,UAAA,GAErC;IAMJ,IAAIA,UAAA,KAAe,IACnB;MACIX,OAAA,CAAQC,KAAA,CAAM,4DAA4D;MAC1E;IACJ;IAEM,MAAAW,GAAA,GAAM,IAAIC,WAAA,GAAcC,MAAA,CAC1B,IAAItC,UAAA,CAAW1C,QAAA,CAAS8C,MAAA,EAAQ6B,qBAAA,EAAuBE,UAAU;MAE/DI,KAAA,GAAQ,IAAIhF,QAAA,CACdD,QAAA,CAAS8C,MAAA,EACT6B,qBAAA,GAAwBE,UAAA,GAAa,GACrCH,mBAAA,GAAsBG,UAAA,GAAa;IAGvCpD,MAAA,CAAOyD,GAAA,CAAIJ,GAAA,EAAKG,KAAK,GAKrBR,qBAAA,IAAyB,IAAIC,mBAAA,GAAsBE,YAAA;EACvD;EAEO,OAAAnD,MAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}