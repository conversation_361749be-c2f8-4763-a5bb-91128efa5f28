{"ast": null, "code": "import { BoundingBox } from \"./BoundingBox.mjs\";\nfunction checkRow(data, width, y) {\n  for (let x = 0, index = 4 * y * width; x < width; ++x, index += 4) if (data[index + 3] !== 0) return !1;\n  return !0;\n}\nfunction checkColumn(data, width, x, top, bottom) {\n  const stride = 4 * width;\n  for (let y = top, index = top * stride + 4 * x; y <= bottom; ++y, index += stride) if (data[index + 3] !== 0) return !1;\n  return !0;\n}\nfunction getCanvasBoundingBox(canvas) {\n  const {\n      width,\n      height\n    } = canvas,\n    context = canvas.getContext(\"2d\", {\n      willReadFrequently: !0\n    });\n  if (context === null) throw new TypeError(\"Failed to get canvas 2D context\");\n  const data = context.getImageData(0, 0, width, height).data;\n  let left = 0,\n    top = 0,\n    right = width - 1,\n    bottom = height - 1;\n  for (; top < height && checkRow(data, width, top);) ++top;\n  if (top === height) return BoundingBox.EMPTY;\n  for (; checkRow(data, width, bottom);) --bottom;\n  for (; checkColumn(data, width, left, top, bottom);) ++left;\n  for (; checkColumn(data, width, right, top, bottom);) --right;\n  return ++right, ++bottom, new BoundingBox(left, top, right, bottom);\n}\nexport { getCanvasBoundingBox };", "map": {"version": 3, "names": ["checkRow", "data", "width", "y", "x", "index", "checkColumn", "top", "bottom", "stride", "getCanvasBoundingBox", "canvas", "height", "context", "getContext", "willReadFrequently", "TypeError", "getImageData", "left", "right", "BoundingBox", "EMPTY"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\utils\\src\\media\\getCanvasBoundingBox.ts"], "sourcesContent": ["import { BoundingBox } from './BoundingBox';\n\nimport type { ICanvas } from '@pixi/settings';\n\nfunction checkRow(data: Uint8ClampedArray, width: number, y: number)\n{\n    for (let x = 0, index = 4 * y * width; x < width; ++x, index += 4)\n    {\n        if (data[index + 3] !== 0) return false;\n    }\n\n    return true;\n}\n\nfunction checkColumn(data: Uint8ClampedArray, width: number, x: number, top: number, bottom: number)\n{\n    const stride = 4 * width;\n\n    for (let y = top, index = (top * stride) + (4 * x); y <= bottom; ++y, index += stride)\n    {\n        if (data[index + 3] !== 0) return false;\n    }\n\n    return true;\n}\n\n/**\n * Measuring the bounds of a canvas' visible (non-transparent) pixels.\n * @memberof PIXI.utils\n * @param {PIXI.ICanvas} canvas - The canvas to measure.\n * @returns {PIXI.utils.BoundingBox} The bounding box of the canvas' visible pixels.\n * @since 7.1.0\n */\nexport function getCanvasBoundingBox(canvas: ICanvas): BoundingBox\n{\n    // https://gist.github.com/timdown/021d9c8f2aabc7092df564996f5afbbf\n\n    const { width, height } = canvas;\n\n    const context = canvas.getContext('2d', {\n        willReadFrequently: true,\n    });\n\n    if (context === null)\n    {\n        throw new TypeError('Failed to get canvas 2D context');\n    }\n\n    const imageData = context.getImageData(0, 0, width, height);\n    const data = imageData.data;\n\n    let left = 0;\n    let top = 0;\n    let right = width - 1;\n    let bottom = height - 1;\n\n    while (top < height && checkRow(data, width, top)) ++top;\n    if (top === height) return BoundingBox.EMPTY;\n    while (checkRow(data, width, bottom)) --bottom;\n    while (checkColumn(data, width, left, top, bottom)) ++left;\n    while (checkColumn(data, width, right, top, bottom)) --right;\n\n    ++right;\n    ++bottom;\n\n    return new BoundingBox(left, top, right, bottom);\n}\n"], "mappings": ";AAIA,SAASA,SAASC,IAAA,EAAyBC,KAAA,EAAeC,CAAA,EAC1D;EACa,SAAAC,CAAA,GAAI,GAAGC,KAAA,GAAQ,IAAIF,CAAA,GAAID,KAAA,EAAOE,CAAA,GAAIF,KAAA,EAAO,EAAEE,CAAA,EAAGC,KAAA,IAAS,GAExD,IAAAJ,IAAA,CAAKI,KAAA,GAAQ,CAAC,MAAM,GAAU;EAG/B;AACX;AAEA,SAASC,YAAYL,IAAA,EAAyBC,KAAA,EAAeE,CAAA,EAAWG,GAAA,EAAaC,MAAA,EACrF;EACI,MAAMC,MAAA,GAAS,IAAIP,KAAA;EAEV,SAAAC,CAAA,GAAII,GAAA,EAAKF,KAAA,GAASE,GAAA,GAAME,MAAA,GAAW,IAAIL,CAAA,EAAID,CAAA,IAAKK,MAAA,EAAQ,EAAEL,CAAA,EAAGE,KAAA,IAASI,MAAA,EAEvE,IAAAR,IAAA,CAAKI,KAAA,GAAQ,CAAC,MAAM,GAAU;EAG/B;AACX;AASO,SAASK,qBAAqBC,MAAA,EACrC;EAGU;MAAET,KAAA;MAAOU;IAAA,IAAWD,MAAA;IAEpBE,OAAA,GAAUF,MAAA,CAAOG,UAAA,CAAW,MAAM;MACpCC,kBAAA,EAAoB;IAAA,CACvB;EAED,IAAIF,OAAA,KAAY,MAEN,UAAIG,SAAA,CAAU,iCAAiC;EAIzD,MAAMf,IAAA,GADYY,OAAA,CAAQI,YAAA,CAAa,GAAG,GAAGf,KAAA,EAAOU,MAAM,EACnCX,IAAA;EAEnB,IAAAiB,IAAA,GAAO;IACPX,GAAA,GAAM;IACNY,KAAA,GAAQjB,KAAA,GAAQ;IAChBM,MAAA,GAASI,MAAA,GAAS;EAEtB,OAAOL,GAAA,GAAMK,MAAA,IAAUZ,QAAA,CAASC,IAAA,EAAMC,KAAA,EAAOK,GAAG,IAAK,EAAAA,GAAA;EACrD,IAAIA,GAAA,KAAQK,MAAA,EAAQ,OAAOQ,WAAA,CAAYC,KAAA;EAChC,OAAArB,QAAA,CAASC,IAAA,EAAMC,KAAA,EAAOM,MAAM,IAAK,EAAAA,MAAA;EACxC,OAAOF,WAAA,CAAYL,IAAA,EAAMC,KAAA,EAAOgB,IAAA,EAAMX,GAAA,EAAKC,MAAM,IAAK,EAAAU,IAAA;EACtD,OAAOZ,WAAA,CAAYL,IAAA,EAAMC,KAAA,EAAOiB,KAAA,EAAOZ,GAAA,EAAKC,MAAM,IAAK,EAAAW,KAAA;EAErD,SAAAA,KAAA,EACF,EAAEX,MAAA,EAEK,IAAIY,WAAA,CAAYF,IAAA,EAAMX,GAAA,EAAKY,KAAA,EAAOX,MAAM;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}