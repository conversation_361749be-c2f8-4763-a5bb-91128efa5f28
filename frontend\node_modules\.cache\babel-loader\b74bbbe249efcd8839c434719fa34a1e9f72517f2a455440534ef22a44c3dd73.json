{"ast": null, "code": "import { settings } from \"@pixi/core\";\nconst contextSettings = {\n    // TextMetrics requires getImageData readback for measuring fonts.\n    willReadFrequently: !0\n  },\n  _TextMetrics = class _TextMetrics2 {\n    /**\n     * Checking that we can use modern canvas 2D API.\n     *\n     * Note: This is an unstable API, Chrome < 94 use `textLetterSpacing`, later versions use `letterSpacing`.\n     * @see PIXI.TextMetrics.experimentalLetterSpacing\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/letterSpacing\n     * @see https://developer.chrome.com/origintrials/#/view_trial/3585991203293757441\n     */\n    static get experimentalLetterSpacingSupported() {\n      let result = _TextMetrics2._experimentalLetterSpacingSupported;\n      if (result !== void 0) {\n        const proto = settings.ADAPTER.getCanvasRenderingContext2D().prototype;\n        result = _TextMetrics2._experimentalLetterSpacingSupported = \"letterSpacing\" in proto || \"textLetterSpacing\" in proto;\n      }\n      return result;\n    }\n    /**\n     * @param text - the text that was measured\n     * @param style - the style that was measured\n     * @param width - the measured width of the text\n     * @param height - the measured height of the text\n     * @param lines - an array of the lines of text broken by new lines and wrapping if specified in style\n     * @param lineWidths - an array of the line widths for each line matched to `lines`\n     * @param lineHeight - the measured line height for this style\n     * @param maxLineWidth - the maximum line width for all measured lines\n     * @param {PIXI.IFontMetrics} fontProperties - the font properties object from TextMetrics.measureFont\n     */\n    constructor(text, style, width, height, lines, lineWidths, lineHeight, maxLineWidth, fontProperties) {\n      this.text = text, this.style = style, this.width = width, this.height = height, this.lines = lines, this.lineWidths = lineWidths, this.lineHeight = lineHeight, this.maxLineWidth = maxLineWidth, this.fontProperties = fontProperties;\n    }\n    /**\n     * Measures the supplied string of text and returns a Rectangle.\n     * @param text - The text to measure.\n     * @param style - The text style to use for measuring\n     * @param wordWrap - Override for if word-wrap should be applied to the text.\n     * @param canvas - optional specification of the canvas to use for measuring.\n     * @returns Measured width and height of the text.\n     */\n    static measureText(text, style, wordWrap, canvas = _TextMetrics2._canvas) {\n      wordWrap = wordWrap ?? style.wordWrap;\n      const font = style.toFontString(),\n        fontProperties = _TextMetrics2.measureFont(font);\n      fontProperties.fontSize === 0 && (fontProperties.fontSize = style.fontSize, fontProperties.ascent = style.fontSize);\n      const context = canvas.getContext(\"2d\", contextSettings);\n      context.font = font;\n      const lines = (wordWrap ? _TextMetrics2.wordWrap(text, style, canvas) : text).split(/(?:\\r\\n|\\r|\\n)/),\n        lineWidths = new Array(lines.length);\n      let maxLineWidth = 0;\n      for (let i = 0; i < lines.length; i++) {\n        const lineWidth = _TextMetrics2._measureText(lines[i], style.letterSpacing, context);\n        lineWidths[i] = lineWidth, maxLineWidth = Math.max(maxLineWidth, lineWidth);\n      }\n      let width = maxLineWidth + style.strokeThickness;\n      style.dropShadow && (width += style.dropShadowDistance);\n      const lineHeight = style.lineHeight || fontProperties.fontSize + style.strokeThickness;\n      let height = Math.max(lineHeight, fontProperties.fontSize + style.strokeThickness * 2) + style.leading + (lines.length - 1) * (lineHeight + style.leading);\n      return style.dropShadow && (height += style.dropShadowDistance), new _TextMetrics2(text, style, width, height, lines, lineWidths, lineHeight + style.leading, maxLineWidth, fontProperties);\n    }\n    static _measureText(text, letterSpacing, context) {\n      let useExperimentalLetterSpacing = !1;\n      _TextMetrics2.experimentalLetterSpacingSupported && (_TextMetrics2.experimentalLetterSpacing ? (context.letterSpacing = `${letterSpacing}px`, context.textLetterSpacing = `${letterSpacing}px`, useExperimentalLetterSpacing = !0) : (context.letterSpacing = \"0px\", context.textLetterSpacing = \"0px\"));\n      let width = context.measureText(text).width;\n      return width > 0 && (useExperimentalLetterSpacing ? width -= letterSpacing : width += (_TextMetrics2.graphemeSegmenter(text).length - 1) * letterSpacing), width;\n    }\n    /**\n     * Applies newlines to a string to have it optimally fit into the horizontal\n     * bounds set by the Text object's wordWrapWidth property.\n     * @param text - String to apply word wrapping to\n     * @param style - the style to use when wrapping\n     * @param canvas - optional specification of the canvas to use for measuring.\n     * @returns New string with new lines applied where required\n     */\n    static wordWrap(text, style, canvas = _TextMetrics2._canvas) {\n      const context = canvas.getContext(\"2d\", contextSettings);\n      let width = 0,\n        line = \"\",\n        lines = \"\";\n      const cache = /* @__PURE__ */Object.create(null),\n        {\n          letterSpacing,\n          whiteSpace\n        } = style,\n        collapseSpaces = _TextMetrics2.collapseSpaces(whiteSpace),\n        collapseNewlines = _TextMetrics2.collapseNewlines(whiteSpace);\n      let canPrependSpaces = !collapseSpaces;\n      const wordWrapWidth = style.wordWrapWidth + letterSpacing,\n        tokens = _TextMetrics2.tokenize(text);\n      for (let i = 0; i < tokens.length; i++) {\n        let token = tokens[i];\n        if (_TextMetrics2.isNewline(token)) {\n          if (!collapseNewlines) {\n            lines += _TextMetrics2.addLine(line), canPrependSpaces = !collapseSpaces, line = \"\", width = 0;\n            continue;\n          }\n          token = \" \";\n        }\n        if (collapseSpaces) {\n          const currIsBreakingSpace = _TextMetrics2.isBreakingSpace(token),\n            lastIsBreakingSpace = _TextMetrics2.isBreakingSpace(line[line.length - 1]);\n          if (currIsBreakingSpace && lastIsBreakingSpace) continue;\n        }\n        const tokenWidth = _TextMetrics2.getFromCache(token, letterSpacing, cache, context);\n        if (tokenWidth > wordWrapWidth) {\n          if (line !== \"\" && (lines += _TextMetrics2.addLine(line), line = \"\", width = 0), _TextMetrics2.canBreakWords(token, style.breakWords)) {\n            const characters = _TextMetrics2.wordWrapSplit(token);\n            for (let j = 0; j < characters.length; j++) {\n              let char = characters[j],\n                lastChar = char,\n                k = 1;\n              for (; characters[j + k];) {\n                const nextChar = characters[j + k];\n                if (!_TextMetrics2.canBreakChars(lastChar, nextChar, token, j, style.breakWords)) char += nextChar;else break;\n                lastChar = nextChar, k++;\n              }\n              j += k - 1;\n              const characterWidth = _TextMetrics2.getFromCache(char, letterSpacing, cache, context);\n              characterWidth + width > wordWrapWidth && (lines += _TextMetrics2.addLine(line), canPrependSpaces = !1, line = \"\", width = 0), line += char, width += characterWidth;\n            }\n          } else {\n            line.length > 0 && (lines += _TextMetrics2.addLine(line), line = \"\", width = 0);\n            const isLastToken = i === tokens.length - 1;\n            lines += _TextMetrics2.addLine(token, !isLastToken), canPrependSpaces = !1, line = \"\", width = 0;\n          }\n        } else tokenWidth + width > wordWrapWidth && (canPrependSpaces = !1, lines += _TextMetrics2.addLine(line), line = \"\", width = 0), (line.length > 0 || !_TextMetrics2.isBreakingSpace(token) || canPrependSpaces) && (line += token, width += tokenWidth);\n      }\n      return lines += _TextMetrics2.addLine(line, !1), lines;\n    }\n    /**\n     * Convienience function for logging each line added during the wordWrap method.\n     * @param line    - The line of text to add\n     * @param newLine - Add new line character to end\n     * @returns A formatted line\n     */\n    static addLine(line, newLine = !0) {\n      return line = _TextMetrics2.trimRight(line), line = newLine ? `${line}\n` : line, line;\n    }\n    /**\n     * Gets & sets the widths of calculated characters in a cache object\n     * @param key            - The key\n     * @param letterSpacing  - The letter spacing\n     * @param cache          - The cache\n     * @param context        - The canvas context\n     * @returns The from cache.\n     */\n    static getFromCache(key, letterSpacing, cache, context) {\n      let width = cache[key];\n      return typeof width != \"number\" && (width = _TextMetrics2._measureText(key, letterSpacing, context) + letterSpacing, cache[key] = width), width;\n    }\n    /**\n     * Determines whether we should collapse breaking spaces.\n     * @param whiteSpace - The TextStyle property whiteSpace\n     * @returns Should collapse\n     */\n    static collapseSpaces(whiteSpace) {\n      return whiteSpace === \"normal\" || whiteSpace === \"pre-line\";\n    }\n    /**\n     * Determines whether we should collapse newLine chars.\n     * @param whiteSpace - The white space\n     * @returns should collapse\n     */\n    static collapseNewlines(whiteSpace) {\n      return whiteSpace === \"normal\";\n    }\n    /**\n     * Trims breaking whitespaces from string.\n     * @param text - The text\n     * @returns Trimmed string\n     */\n    static trimRight(text) {\n      if (typeof text != \"string\") return \"\";\n      for (let i = text.length - 1; i >= 0; i--) {\n        const char = text[i];\n        if (!_TextMetrics2.isBreakingSpace(char)) break;\n        text = text.slice(0, -1);\n      }\n      return text;\n    }\n    /**\n     * Determines if char is a newline.\n     * @param char - The character\n     * @returns True if newline, False otherwise.\n     */\n    static isNewline(char) {\n      return typeof char != \"string\" ? !1 : _TextMetrics2._newlines.includes(char.charCodeAt(0));\n    }\n    /**\n     * Determines if char is a breaking whitespace.\n     *\n     * It allows one to determine whether char should be a breaking whitespace\n     * For example certain characters in CJK langs or numbers.\n     * It must return a boolean.\n     * @param char - The character\n     * @param [_nextChar] - The next character\n     * @returns True if whitespace, False otherwise.\n     */\n    static isBreakingSpace(char, _nextChar) {\n      return typeof char != \"string\" ? !1 : _TextMetrics2._breakingSpaces.includes(char.charCodeAt(0));\n    }\n    /**\n     * Splits a string into words, breaking-spaces and newLine characters\n     * @param text - The text\n     * @returns A tokenized array\n     */\n    static tokenize(text) {\n      const tokens = [];\n      let token = \"\";\n      if (typeof text != \"string\") return tokens;\n      for (let i = 0; i < text.length; i++) {\n        const char = text[i],\n          nextChar = text[i + 1];\n        if (_TextMetrics2.isBreakingSpace(char, nextChar) || _TextMetrics2.isNewline(char)) {\n          token !== \"\" && (tokens.push(token), token = \"\"), tokens.push(char);\n          continue;\n        }\n        token += char;\n      }\n      return token !== \"\" && tokens.push(token), tokens;\n    }\n    /**\n     * Overridable helper method used internally by TextMetrics, exposed to allow customizing the class's behavior.\n     *\n     * It allows one to customise which words should break\n     * Examples are if the token is CJK or numbers.\n     * It must return a boolean.\n     * @param _token - The token\n     * @param breakWords - The style attr break words\n     * @returns Whether to break word or not\n     */\n    static canBreakWords(_token, breakWords) {\n      return breakWords;\n    }\n    /**\n     * Overridable helper method used internally by TextMetrics, exposed to allow customizing the class's behavior.\n     *\n     * It allows one to determine whether a pair of characters\n     * should be broken by newlines\n     * For example certain characters in CJK langs or numbers.\n     * It must return a boolean.\n     * @param _char - The character\n     * @param _nextChar - The next character\n     * @param _token - The token/word the characters are from\n     * @param _index - The index in the token of the char\n     * @param _breakWords - The style attr break words\n     * @returns whether to break word or not\n     */\n    static canBreakChars(_char, _nextChar, _token, _index, _breakWords) {\n      return !0;\n    }\n    /**\n     * Overridable helper method used internally by TextMetrics, exposed to allow customizing the class's behavior.\n     *\n     * It is called when a token (usually a word) has to be split into separate pieces\n     * in order to determine the point to break a word.\n     * It must return an array of characters.\n     * @param token - The token to split\n     * @returns The characters of the token\n     * @see TextMetrics.graphemeSegmenter\n     */\n    static wordWrapSplit(token) {\n      return _TextMetrics2.graphemeSegmenter(token);\n    }\n    /**\n     * Calculates the ascent, descent and fontSize of a given font-style\n     * @param font - String representing the style of the font\n     * @returns Font properties object\n     */\n    static measureFont(font) {\n      if (_TextMetrics2._fonts[font]) return _TextMetrics2._fonts[font];\n      const properties = {\n          ascent: 0,\n          descent: 0,\n          fontSize: 0\n        },\n        canvas = _TextMetrics2._canvas,\n        context = _TextMetrics2._context;\n      context.font = font;\n      const metricsString = _TextMetrics2.METRICS_STRING + _TextMetrics2.BASELINE_SYMBOL,\n        width = Math.ceil(context.measureText(metricsString).width);\n      let baseline = Math.ceil(context.measureText(_TextMetrics2.BASELINE_SYMBOL).width);\n      const height = Math.ceil(_TextMetrics2.HEIGHT_MULTIPLIER * baseline);\n      if (baseline = baseline * _TextMetrics2.BASELINE_MULTIPLIER | 0, width === 0 || height === 0) return _TextMetrics2._fonts[font] = properties, properties;\n      canvas.width = width, canvas.height = height, context.fillStyle = \"#f00\", context.fillRect(0, 0, width, height), context.font = font, context.textBaseline = \"alphabetic\", context.fillStyle = \"#000\", context.fillText(metricsString, 0, baseline);\n      const imagedata = context.getImageData(0, 0, width, height).data,\n        pixels = imagedata.length,\n        line = width * 4;\n      let i = 0,\n        idx = 0,\n        stop = !1;\n      for (i = 0; i < baseline; ++i) {\n        for (let j = 0; j < line; j += 4) if (imagedata[idx + j] !== 255) {\n          stop = !0;\n          break;\n        }\n        if (!stop) idx += line;else break;\n      }\n      for (properties.ascent = baseline - i, idx = pixels - line, stop = !1, i = height; i > baseline; --i) {\n        for (let j = 0; j < line; j += 4) if (imagedata[idx + j] !== 255) {\n          stop = !0;\n          break;\n        }\n        if (!stop) idx -= line;else break;\n      }\n      return properties.descent = i - baseline, properties.fontSize = properties.ascent + properties.descent, _TextMetrics2._fonts[font] = properties, properties;\n    }\n    /**\n     * Clear font metrics in metrics cache.\n     * @param {string} [font] - font name. If font name not set then clear cache for all fonts.\n     */\n    static clearMetrics(font = \"\") {\n      font ? delete _TextMetrics2._fonts[font] : _TextMetrics2._fonts = {};\n    }\n    /**\n     * Cached canvas element for measuring text\n     * TODO: this should be private, but isn't because of backward compat, will fix later.\n     * @ignore\n     */\n    static get _canvas() {\n      if (!_TextMetrics2.__canvas) {\n        let canvas;\n        try {\n          const c = new OffscreenCanvas(0, 0);\n          if (c.getContext(\"2d\", contextSettings)?.measureText) return _TextMetrics2.__canvas = c, c;\n          canvas = settings.ADAPTER.createCanvas();\n        } catch {\n          canvas = settings.ADAPTER.createCanvas();\n        }\n        canvas.width = canvas.height = 10, _TextMetrics2.__canvas = canvas;\n      }\n      return _TextMetrics2.__canvas;\n    }\n    /**\n     * TODO: this should be private, but isn't because of backward compat, will fix later.\n     * @ignore\n     */\n    static get _context() {\n      return _TextMetrics2.__context || (_TextMetrics2.__context = _TextMetrics2._canvas.getContext(\"2d\", contextSettings)), _TextMetrics2.__context;\n    }\n  };\n_TextMetrics.METRICS_STRING = \"|\\xC9q\\xC5\", /** Baseline symbol for calculate font metrics. */\n_TextMetrics.BASELINE_SYMBOL = \"M\", /** Baseline multiplier for calculate font metrics. */\n_TextMetrics.BASELINE_MULTIPLIER = 1.4, /** Height multiplier for setting height of canvas to calculate font metrics. */\n_TextMetrics.HEIGHT_MULTIPLIER = 2,\n/**\n* A Unicode \"character\", or \"grapheme cluster\", can be composed of multiple Unicode code points,\n* such as letters with diacritical marks (e.g. `'\\u0065\\u0301'`, letter e with acute)\n* or emojis with modifiers (e.g. `'\\uD83E\\uDDD1\\u200D\\uD83D\\uDCBB'`, technologist).\n* The new `Intl.Segmenter` API in ES2022 can split the string into grapheme clusters correctly. If it is not available,\n* PixiJS will fallback to use the iterator of String, which can only spilt the string into code points.\n* If you want to get full functionality in environments that don't support `Intl.Segmenter` (such as Firefox),\n* you can use other libraries such as [grapheme-splitter]{@link https://www.npmjs.com/package/grapheme-splitter}\n* or [graphemer]{@link https://www.npmjs.com/package/graphemer} to create a polyfill. Since these libraries can be\n* relatively large in size to handle various Unicode grapheme clusters properly, PixiJS won't use them directly.\n*/\n_TextMetrics.graphemeSegmenter = (() => {\n  if (typeof Intl?.Segmenter == \"function\") {\n    const segmenter = new Intl.Segmenter();\n    return s => [...segmenter.segment(s)].map(x => x.segment);\n  }\n  return s => [...s];\n})(),\n/**\n* New rendering behavior for letter-spacing which uses Chrome's new native API. This will\n* lead to more accurate letter-spacing results because it does not try to manually draw\n* each character. However, this Chrome API is experimental and may not serve all cases yet.\n* @see PIXI.TextMetrics.experimentalLetterSpacingSupported\n*/\n_TextMetrics.experimentalLetterSpacing = !1, /** Cache of {@see PIXI.TextMetrics.FontMetrics} objects. */\n_TextMetrics._fonts = {}, /** Cache of new line chars. */\n_TextMetrics._newlines = [10,\n// line feed\n13\n// carriage return\n], /** Cache of breaking spaces. */\n_TextMetrics._breakingSpaces = [9,\n// character tabulation\n32,\n// space\n8192,\n// en quad\n8193,\n// em quad\n8194,\n// en space\n8195,\n// em space\n8196,\n// three-per-em space\n8197,\n// four-per-em space\n8198,\n// six-per-em space\n8200,\n// punctuation space\n8201,\n// thin space\n8202,\n// hair space\n8287,\n// medium mathematical space\n12288\n// ideographic space\n];\nlet TextMetrics = _TextMetrics;\nexport { TextMetrics };", "map": {"version": 3, "names": ["contextSettings", "willReadFrequently", "_TextMetrics", "_TextMetrics2", "experimentalLetterSpacingSupported", "result", "_experimentalLetterSpacingSupported", "proto", "settings", "ADAPTER", "getCanvasRenderingContext2D", "prototype", "constructor", "text", "style", "width", "height", "lines", "lineWidths", "lineHeight", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "fontProperties", "measureText", "wordWrap", "canvas", "_canvas", "font", "toFontString", "measureFont", "fontSize", "ascent", "context", "getContext", "split", "Array", "length", "i", "lineWidth", "_measureText", "letterSpacing", "Math", "max", "strokeThickness", "dropShadow", "dropShadowDistance", "leading", "useExperimentalLetterSpacing", "experimentalLetterSpacing", "textLetterSpacing", "graphemeSegmenter", "line", "cache", "Object", "create", "whiteSpace", "collapseSpaces", "collapseNewlines", "canPrependSpaces", "wordWrapWidth", "tokens", "tokenize", "token", "isNewline", "addLine", "currIsBreakingSpace", "isBreakingSpace", "lastIsBreakingSpace", "tokenWidth", "getFromCache", "canBreakWords", "breakWords", "characters", "wordWrapSplit", "j", "char", "lastChar", "k", "nextChar", "canBreakChars", "<PERSON><PERSON><PERSON><PERSON>", "isLastToken", "newLine", "trimRight", "key", "slice", "_newlines", "includes", "charCodeAt", "_nextChar", "_breakingSpaces", "push", "_token", "_char", "_index", "_breakWords", "_fonts", "properties", "descent", "_context", "metricsString", "METRICS_STRING", "BASELINE_SYMBOL", "ceil", "baseline", "HEIGHT_MULTIPLIER", "BASELINE_MULTIPLIER", "fillStyle", "fillRect", "textBaseline", "fillText", "imagedata", "getImageData", "data", "pixels", "idx", "stop", "clearMetrics", "__canvas", "c", "OffscreenCanvas", "createCanvas", "__context", "Intl", "Segmenter", "segmenter", "s", "segment", "map", "x", "TextMetrics"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\text\\src\\TextMetrics.ts"], "sourcesContent": ["import { settings } from '@pixi/core';\n\nimport type { ICanvas, ICanvasRenderingContext2D, ICanvasRenderingContext2DSettings } from '@pixi/core';\nimport type { TextStyle, TextStyleWhiteSpace } from './TextStyle';\n\n// The type for Intl.Segmenter is only available since TypeScript 4.7.2, so let's make a polyfill for it.\ninterface ISegmentData\n{\n    segment: string;\n}\ninterface ISegments\n{\n    [Symbol.iterator](): IterableIterator<ISegmentData>;\n}\ninterface ISegmenter\n{\n    segment(input: string): ISegments;\n}\ninterface IIntl\n{\n    Segmenter?: {\n        prototype: ISegmenter;\n        new(): ISegmenter;\n    };\n}\n\n/**\n * Internal return object for {@link PIXI.TextMetrics.measureFont `TextMetrics.measureFont`}.\n * @typedef {object} FontMetrics\n * @property {number} ascent - The ascent distance\n * @property {number} descent - The descent distance\n * @property {number} fontSize - Font size from ascent to descent\n * @memberof PIXI.TextMetrics\n * @private\n */\n\n/**\n * A number, or a string containing a number.\n * @memberof PIXI\n * @typedef {object} IFontMetrics\n * @property {number} ascent - Font ascent\n * @property {number} descent - Font descent\n * @property {number} fontSize - Font size\n */\ninterface IFontMetrics\n{\n    ascent: number;\n    descent: number;\n    fontSize: number;\n}\n\ntype CharacterWidthCache = Record<string, number>;\n\n// Default settings used for all getContext calls\nconst contextSettings: ICanvasRenderingContext2DSettings = {\n    // TextMetrics requires getImageData readback for measuring fonts.\n    willReadFrequently: true,\n};\n\n/**\n * The TextMetrics object represents the measurement of a block of text with a specified style.\n * @example\n * import { TextMetrics, TextStyle } from 'pixi.js';\n *\n * const style = new TextStyle({\n *     fontFamily: 'Arial',\n *     fontSize: 24,\n *     fill: 0xff1010,\n *     align: 'center',\n * });\n * const textMetrics = TextMetrics.measureText('Your text', style);\n * @memberof PIXI\n */\nexport class TextMetrics\n{\n    /** The text that was measured. */\n    public text: string;\n\n    /** The style that was measured. */\n    public style: TextStyle;\n\n    /** The measured width of the text. */\n    public width: number;\n\n    /** The measured height of the text. */\n    public height: number;\n\n    /** An array of lines of the text broken by new lines and wrapping is specified in style. */\n    public lines: string[];\n\n    /** An array of the line widths for each line matched to `lines`. */\n    public lineWidths: number[];\n\n    /** The measured line height for this style. */\n    public lineHeight: number;\n\n    /** The maximum line width for all measured lines. */\n    public maxLineWidth: number;\n\n    /** The font properties object from TextMetrics.measureFont. */\n    public fontProperties: IFontMetrics;\n\n    /**\n     * String used for calculate font metrics.\n     * These characters are all tall to help calculate the height required for text.\n     */\n    public static METRICS_STRING = '|ÉqÅ';\n\n    /** Baseline symbol for calculate font metrics. */\n    public static BASELINE_SYMBOL = 'M';\n\n    /** Baseline multiplier for calculate font metrics. */\n    public static BASELINE_MULTIPLIER = 1.4;\n\n    /** Height multiplier for setting height of canvas to calculate font metrics. */\n    public static HEIGHT_MULTIPLIER = 2.0;\n\n    /**\n     * A Unicode \"character\", or \"grapheme cluster\", can be composed of multiple Unicode code points,\n     * such as letters with diacritical marks (e.g. `'\\u0065\\u0301'`, letter e with acute)\n     * or emojis with modifiers (e.g. `'\\uD83E\\uDDD1\\u200D\\uD83D\\uDCBB'`, technologist).\n     * The new `Intl.Segmenter` API in ES2022 can split the string into grapheme clusters correctly. If it is not available,\n     * PixiJS will fallback to use the iterator of String, which can only spilt the string into code points.\n     * If you want to get full functionality in environments that don't support `Intl.Segmenter` (such as Firefox),\n     * you can use other libraries such as [grapheme-splitter]{@link https://www.npmjs.com/package/grapheme-splitter}\n     * or [graphemer]{@link https://www.npmjs.com/package/graphemer} to create a polyfill. Since these libraries can be\n     * relatively large in size to handle various Unicode grapheme clusters properly, PixiJS won't use them directly.\n     */\n    public static graphemeSegmenter: (s: string) => string[] = (() =>\n    {\n        if (typeof (Intl as IIntl)?.Segmenter === 'function')\n        {\n            const segmenter = new (Intl as IIntl).Segmenter();\n\n            return (s: string) => [...segmenter.segment(s)].map((x) => x.segment);\n        }\n\n        return (s: string) => [...s];\n    })();\n\n    public static _experimentalLetterSpacingSupported?: boolean;\n\n    /**\n     * Checking that we can use modern canvas 2D API.\n     *\n     * Note: This is an unstable API, Chrome < 94 use `textLetterSpacing`, later versions use `letterSpacing`.\n     * @see PIXI.TextMetrics.experimentalLetterSpacing\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/letterSpacing\n     * @see https://developer.chrome.com/origintrials/#/view_trial/3585991203293757441\n     */\n    public static get experimentalLetterSpacingSupported(): boolean\n    {\n        let result = TextMetrics._experimentalLetterSpacingSupported;\n\n        if (result !== undefined)\n        {\n            const proto = settings.ADAPTER.getCanvasRenderingContext2D().prototype;\n\n            result\n                = TextMetrics._experimentalLetterSpacingSupported\n                = 'letterSpacing' in proto || 'textLetterSpacing' in proto;\n        }\n\n        return result;\n    }\n\n    /**\n     * New rendering behavior for letter-spacing which uses Chrome's new native API. This will\n     * lead to more accurate letter-spacing results because it does not try to manually draw\n     * each character. However, this Chrome API is experimental and may not serve all cases yet.\n     * @see PIXI.TextMetrics.experimentalLetterSpacingSupported\n     */\n    public static experimentalLetterSpacing = false;\n\n    /** Cache of {@see PIXI.TextMetrics.FontMetrics} objects. */\n    private static _fonts: Record<string, IFontMetrics> = {};\n\n    /** Cache of new line chars. */\n    private static _newlines: number[] = [\n        0x000A, // line feed\n        0x000D, // carriage return\n    ];\n\n    /** Cache of breaking spaces. */\n    private static _breakingSpaces: number[] = [\n        0x0009, // character tabulation\n        0x0020, // space\n        0x2000, // en quad\n        0x2001, // em quad\n        0x2002, // en space\n        0x2003, // em space\n        0x2004, // three-per-em space\n        0x2005, // four-per-em space\n        0x2006, // six-per-em space\n        0x2008, // punctuation space\n        0x2009, // thin space\n        0x200A, // hair space\n        0x205F, // medium mathematical space\n        0x3000, // ideographic space\n    ];\n\n    private static __canvas: ICanvas;\n    private static __context: ICanvasRenderingContext2D;\n\n    /**\n     * @param text - the text that was measured\n     * @param style - the style that was measured\n     * @param width - the measured width of the text\n     * @param height - the measured height of the text\n     * @param lines - an array of the lines of text broken by new lines and wrapping if specified in style\n     * @param lineWidths - an array of the line widths for each line matched to `lines`\n     * @param lineHeight - the measured line height for this style\n     * @param maxLineWidth - the maximum line width for all measured lines\n     * @param {PIXI.IFontMetrics} fontProperties - the font properties object from TextMetrics.measureFont\n     */\n    constructor(text: string, style: TextStyle, width: number, height: number, lines: string[], lineWidths: number[],\n        lineHeight: number, maxLineWidth: number, fontProperties: IFontMetrics)\n    {\n        this.text = text;\n        this.style = style;\n        this.width = width;\n        this.height = height;\n        this.lines = lines;\n        this.lineWidths = lineWidths;\n        this.lineHeight = lineHeight;\n        this.maxLineWidth = maxLineWidth;\n        this.fontProperties = fontProperties;\n    }\n\n    /**\n     * Measures the supplied string of text and returns a Rectangle.\n     * @param text - The text to measure.\n     * @param style - The text style to use for measuring\n     * @param wordWrap - Override for if word-wrap should be applied to the text.\n     * @param canvas - optional specification of the canvas to use for measuring.\n     * @returns Measured width and height of the text.\n     */\n    public static measureText(\n        text: string,\n        style: TextStyle,\n        wordWrap?: boolean,\n        canvas: ICanvas = TextMetrics._canvas\n    ): TextMetrics\n    {\n        wordWrap = (wordWrap === undefined || wordWrap === null) ? style.wordWrap : wordWrap;\n        const font = style.toFontString();\n        const fontProperties = TextMetrics.measureFont(font);\n\n        // fallback in case UA disallow canvas data extraction\n        // (toDataURI, getImageData functions)\n        if (fontProperties.fontSize === 0)\n        {\n            fontProperties.fontSize = style.fontSize as number;\n            fontProperties.ascent = style.fontSize as number;\n        }\n\n        const context = canvas.getContext('2d', contextSettings);\n\n        context.font = font;\n\n        const outputText = wordWrap ? TextMetrics.wordWrap(text, style, canvas) : text;\n        const lines = outputText.split(/(?:\\r\\n|\\r|\\n)/);\n        const lineWidths = new Array<number>(lines.length);\n        let maxLineWidth = 0;\n\n        for (let i = 0; i < lines.length; i++)\n        {\n            const lineWidth = TextMetrics._measureText(lines[i], style.letterSpacing, context);\n\n            lineWidths[i] = lineWidth;\n            maxLineWidth = Math.max(maxLineWidth, lineWidth);\n        }\n        let width = maxLineWidth + style.strokeThickness;\n\n        if (style.dropShadow)\n        {\n            width += style.dropShadowDistance;\n        }\n\n        const lineHeight = style.lineHeight || fontProperties.fontSize + style.strokeThickness;\n        let height\n            = Math.max(lineHeight, fontProperties.fontSize + (style.strokeThickness * 2)) + style.leading\n            + ((lines.length - 1) * (lineHeight + style.leading));\n\n        if (style.dropShadow)\n        {\n            height += style.dropShadowDistance;\n        }\n\n        return new TextMetrics(\n            text,\n            style,\n            width,\n            height,\n            lines,\n            lineWidths,\n            lineHeight + style.leading,\n            maxLineWidth,\n            fontProperties\n        );\n    }\n\n    private static _measureText(\n        text: string,\n        letterSpacing: number,\n        context: ICanvasRenderingContext2D\n    )\n    {\n        let useExperimentalLetterSpacing = false;\n\n        if (TextMetrics.experimentalLetterSpacingSupported)\n        {\n            if (TextMetrics.experimentalLetterSpacing)\n            {\n                context.letterSpacing = `${letterSpacing}px`;\n                context.textLetterSpacing = `${letterSpacing}px`;\n                useExperimentalLetterSpacing = true;\n            }\n            else\n            {\n                context.letterSpacing = '0px';\n                context.textLetterSpacing = '0px';\n            }\n        }\n\n        let width = context.measureText(text).width;\n\n        if (width > 0)\n        {\n            if (useExperimentalLetterSpacing)\n            {\n                width -= letterSpacing;\n            }\n            else\n            {\n                width += (TextMetrics.graphemeSegmenter(text).length - 1) * letterSpacing;\n            }\n        }\n\n        return width;\n    }\n\n    /**\n     * Applies newlines to a string to have it optimally fit into the horizontal\n     * bounds set by the Text object's wordWrapWidth property.\n     * @param text - String to apply word wrapping to\n     * @param style - the style to use when wrapping\n     * @param canvas - optional specification of the canvas to use for measuring.\n     * @returns New string with new lines applied where required\n     */\n    private static wordWrap(\n        text: string,\n        style: TextStyle,\n        canvas: ICanvas = TextMetrics._canvas\n    ): string\n    {\n        const context = canvas.getContext('2d', contextSettings);\n\n        let width = 0;\n        let line = '';\n        let lines = '';\n\n        const cache: CharacterWidthCache = Object.create(null);\n        const { letterSpacing, whiteSpace } = style;\n\n        // How to handle whitespaces\n        const collapseSpaces = TextMetrics.collapseSpaces(whiteSpace);\n        const collapseNewlines = TextMetrics.collapseNewlines(whiteSpace);\n\n        // whether or not spaces may be added to the beginning of lines\n        let canPrependSpaces = !collapseSpaces;\n\n        // There is letterSpacing after every char except the last one\n        // t_h_i_s_' '_i_s_' '_a_n_' '_e_x_a_m_p_l_e_' '_!\n        // so for convenience the above needs to be compared to width + 1 extra letterSpace\n        // t_h_i_s_' '_i_s_' '_a_n_' '_e_x_a_m_p_l_e_' '_!_\n        // ________________________________________________\n        // And then the final space is simply no appended to each line\n        const wordWrapWidth = style.wordWrapWidth + letterSpacing;\n\n        // break text into words, spaces and newline chars\n        const tokens = TextMetrics.tokenize(text);\n\n        for (let i = 0; i < tokens.length; i++)\n        {\n            // get the word, space or newlineChar\n            let token = tokens[i];\n\n            // if word is a new line\n            if (TextMetrics.isNewline(token))\n            {\n                // keep the new line\n                if (!collapseNewlines)\n                {\n                    lines += TextMetrics.addLine(line);\n                    canPrependSpaces = !collapseSpaces;\n                    line = '';\n                    width = 0;\n                    continue;\n                }\n\n                // if we should collapse new lines\n                // we simply convert it into a space\n                token = ' ';\n            }\n\n            // if we should collapse repeated whitespaces\n            if (collapseSpaces)\n            {\n                // check both this and the last tokens for spaces\n                const currIsBreakingSpace = TextMetrics.isBreakingSpace(token);\n                const lastIsBreakingSpace = TextMetrics.isBreakingSpace(line[line.length - 1]);\n\n                if (currIsBreakingSpace && lastIsBreakingSpace)\n                {\n                    continue;\n                }\n            }\n\n            // get word width from cache if possible\n            const tokenWidth = TextMetrics.getFromCache(token, letterSpacing, cache, context);\n\n            // word is longer than desired bounds\n            if (tokenWidth > wordWrapWidth)\n            {\n                // if we are not already at the beginning of a line\n                if (line !== '')\n                {\n                    // start newlines for overflow words\n                    lines += TextMetrics.addLine(line);\n                    line = '';\n                    width = 0;\n                }\n\n                // break large word over multiple lines\n                if (TextMetrics.canBreakWords(token, style.breakWords))\n                {\n                    // break word into characters\n                    const characters = TextMetrics.wordWrapSplit(token);\n\n                    // loop the characters\n                    for (let j = 0; j < characters.length; j++)\n                    {\n                        let char = characters[j];\n                        let lastChar = char;\n\n                        let k = 1;\n\n                        // we are not at the end of the token\n                        while (characters[j + k])\n                        {\n                            const nextChar = characters[j + k];\n\n                            // should not split chars\n                            if (!TextMetrics.canBreakChars(lastChar, nextChar, token, j, style.breakWords))\n                            {\n                                // combine chars & move forward one\n                                char += nextChar;\n                            }\n                            else\n                            {\n                                break;\n                            }\n\n                            lastChar = nextChar;\n                            k++;\n                        }\n\n                        j += k - 1;\n\n                        const characterWidth = TextMetrics.getFromCache(char, letterSpacing, cache, context);\n\n                        if (characterWidth + width > wordWrapWidth)\n                        {\n                            lines += TextMetrics.addLine(line);\n                            canPrependSpaces = false;\n                            line = '';\n                            width = 0;\n                        }\n\n                        line += char;\n                        width += characterWidth;\n                    }\n                }\n\n                // run word out of the bounds\n                else\n                {\n                    // if there are words in this line already\n                    // finish that line and start a new one\n                    if (line.length > 0)\n                    {\n                        lines += TextMetrics.addLine(line);\n                        line = '';\n                        width = 0;\n                    }\n\n                    const isLastToken = i === tokens.length - 1;\n\n                    // give it its own line if it's not the end\n                    lines += TextMetrics.addLine(token, !isLastToken);\n                    canPrependSpaces = false;\n                    line = '';\n                    width = 0;\n                }\n            }\n\n            // word could fit\n            else\n            {\n                // word won't fit because of existing words\n                // start a new line\n                if (tokenWidth + width > wordWrapWidth)\n                {\n                    // if its a space we don't want it\n                    canPrependSpaces = false;\n\n                    // add a new line\n                    lines += TextMetrics.addLine(line);\n\n                    // start a new line\n                    line = '';\n                    width = 0;\n                }\n\n                // don't add spaces to the beginning of lines\n                if (line.length > 0 || !TextMetrics.isBreakingSpace(token) || canPrependSpaces)\n                {\n                    // add the word to the current line\n                    line += token;\n\n                    // update width counter\n                    width += tokenWidth;\n                }\n            }\n        }\n\n        lines += TextMetrics.addLine(line, false);\n\n        return lines;\n    }\n\n    /**\n     * Convienience function for logging each line added during the wordWrap method.\n     * @param line    - The line of text to add\n     * @param newLine - Add new line character to end\n     * @returns A formatted line\n     */\n    private static addLine(line: string, newLine = true): string\n    {\n        line = TextMetrics.trimRight(line);\n\n        line = (newLine) ? `${line}\\n` : line;\n\n        return line;\n    }\n\n    /**\n     * Gets & sets the widths of calculated characters in a cache object\n     * @param key            - The key\n     * @param letterSpacing  - The letter spacing\n     * @param cache          - The cache\n     * @param context        - The canvas context\n     * @returns The from cache.\n     */\n    private static getFromCache(key: string, letterSpacing: number, cache: CharacterWidthCache,\n        context: ICanvasRenderingContext2D): number\n    {\n        let width = cache[key];\n\n        if (typeof width !== 'number')\n        {\n            width = TextMetrics._measureText(key, letterSpacing, context) + letterSpacing;\n            cache[key] = width;\n        }\n\n        return width;\n    }\n\n    /**\n     * Determines whether we should collapse breaking spaces.\n     * @param whiteSpace - The TextStyle property whiteSpace\n     * @returns Should collapse\n     */\n    private static collapseSpaces(whiteSpace: TextStyleWhiteSpace): boolean\n    {\n        return (whiteSpace === 'normal' || whiteSpace === 'pre-line');\n    }\n\n    /**\n     * Determines whether we should collapse newLine chars.\n     * @param whiteSpace - The white space\n     * @returns should collapse\n     */\n    private static collapseNewlines(whiteSpace: TextStyleWhiteSpace): boolean\n    {\n        return (whiteSpace === 'normal');\n    }\n\n    /**\n     * Trims breaking whitespaces from string.\n     * @param text - The text\n     * @returns Trimmed string\n     */\n    private static trimRight(text: string): string\n    {\n        if (typeof text !== 'string')\n        {\n            return '';\n        }\n\n        for (let i = text.length - 1; i >= 0; i--)\n        {\n            const char = text[i];\n\n            if (!TextMetrics.isBreakingSpace(char))\n            {\n                break;\n            }\n\n            text = text.slice(0, -1);\n        }\n\n        return text;\n    }\n\n    /**\n     * Determines if char is a newline.\n     * @param char - The character\n     * @returns True if newline, False otherwise.\n     */\n    private static isNewline(char: string): boolean\n    {\n        if (typeof char !== 'string')\n        {\n            return false;\n        }\n\n        return TextMetrics._newlines.includes(char.charCodeAt(0));\n    }\n\n    /**\n     * Determines if char is a breaking whitespace.\n     *\n     * It allows one to determine whether char should be a breaking whitespace\n     * For example certain characters in CJK langs or numbers.\n     * It must return a boolean.\n     * @param char - The character\n     * @param [_nextChar] - The next character\n     * @returns True if whitespace, False otherwise.\n     */\n    static isBreakingSpace(char: string, _nextChar?: string): boolean\n    {\n        if (typeof char !== 'string')\n        {\n            return false;\n        }\n\n        return TextMetrics._breakingSpaces.includes(char.charCodeAt(0));\n    }\n\n    /**\n     * Splits a string into words, breaking-spaces and newLine characters\n     * @param text - The text\n     * @returns A tokenized array\n     */\n    private static tokenize(text: string): string[]\n    {\n        const tokens: string[] = [];\n        let token = '';\n\n        if (typeof text !== 'string')\n        {\n            return tokens;\n        }\n\n        for (let i = 0; i < text.length; i++)\n        {\n            const char = text[i];\n            const nextChar = text[i + 1];\n\n            if (TextMetrics.isBreakingSpace(char, nextChar) || TextMetrics.isNewline(char))\n            {\n                if (token !== '')\n                {\n                    tokens.push(token);\n                    token = '';\n                }\n\n                tokens.push(char);\n\n                continue;\n            }\n\n            token += char;\n        }\n\n        if (token !== '')\n        {\n            tokens.push(token);\n        }\n\n        return tokens;\n    }\n\n    /**\n     * Overridable helper method used internally by TextMetrics, exposed to allow customizing the class's behavior.\n     *\n     * It allows one to customise which words should break\n     * Examples are if the token is CJK or numbers.\n     * It must return a boolean.\n     * @param _token - The token\n     * @param breakWords - The style attr break words\n     * @returns Whether to break word or not\n     */\n    static canBreakWords(_token: string, breakWords: boolean): boolean\n    {\n        return breakWords;\n    }\n\n    /**\n     * Overridable helper method used internally by TextMetrics, exposed to allow customizing the class's behavior.\n     *\n     * It allows one to determine whether a pair of characters\n     * should be broken by newlines\n     * For example certain characters in CJK langs or numbers.\n     * It must return a boolean.\n     * @param _char - The character\n     * @param _nextChar - The next character\n     * @param _token - The token/word the characters are from\n     * @param _index - The index in the token of the char\n     * @param _breakWords - The style attr break words\n     * @returns whether to break word or not\n     */\n    static canBreakChars(_char: string, _nextChar: string, _token: string, _index: number,\n        _breakWords: boolean): boolean\n    {\n        return true;\n    }\n\n    /**\n     * Overridable helper method used internally by TextMetrics, exposed to allow customizing the class's behavior.\n     *\n     * It is called when a token (usually a word) has to be split into separate pieces\n     * in order to determine the point to break a word.\n     * It must return an array of characters.\n     * @param token - The token to split\n     * @returns The characters of the token\n     * @see TextMetrics.graphemeSegmenter\n     */\n    static wordWrapSplit(token: string): string[]\n    {\n        return TextMetrics.graphemeSegmenter(token);\n    }\n\n    /**\n     * Calculates the ascent, descent and fontSize of a given font-style\n     * @param font - String representing the style of the font\n     * @returns Font properties object\n     */\n    public static measureFont(font: string): IFontMetrics\n    {\n        // as this method is used for preparing assets, don't recalculate things if we don't need to\n        if (TextMetrics._fonts[font])\n        {\n            return TextMetrics._fonts[font];\n        }\n\n        const properties: IFontMetrics = {\n            ascent: 0,\n            descent: 0,\n            fontSize: 0,\n        };\n\n        const canvas = TextMetrics._canvas;\n        const context = TextMetrics._context;\n\n        context.font = font;\n\n        const metricsString = TextMetrics.METRICS_STRING + TextMetrics.BASELINE_SYMBOL;\n        const width = Math.ceil(context.measureText(metricsString).width);\n        let baseline = Math.ceil(context.measureText(TextMetrics.BASELINE_SYMBOL).width);\n        const height = Math.ceil(TextMetrics.HEIGHT_MULTIPLIER * baseline);\n\n        baseline = baseline * TextMetrics.BASELINE_MULTIPLIER | 0;\n\n        if (width === 0 || height === 0)\n        {\n            TextMetrics._fonts[font] = properties;\n\n            return properties;\n        }\n\n        canvas.width = width;\n        canvas.height = height;\n\n        context.fillStyle = '#f00';\n        context.fillRect(0, 0, width, height);\n\n        context.font = font;\n\n        context.textBaseline = 'alphabetic';\n        context.fillStyle = '#000';\n        context.fillText(metricsString, 0, baseline);\n\n        const imagedata = context.getImageData(0, 0, width, height).data;\n        const pixels = imagedata.length;\n        const line = width * 4;\n\n        let i = 0;\n        let idx = 0;\n        let stop = false;\n\n        // ascent. scan from top to bottom until we find a non red pixel\n        for (i = 0; i < baseline; ++i)\n        {\n            for (let j = 0; j < line; j += 4)\n            {\n                if (imagedata[idx + j] !== 255)\n                {\n                    stop = true;\n                    break;\n                }\n            }\n            if (!stop)\n            {\n                idx += line;\n            }\n            else\n            {\n                break;\n            }\n        }\n\n        properties.ascent = baseline - i;\n\n        idx = pixels - line;\n        stop = false;\n\n        // descent. scan from bottom to top until we find a non red pixel\n        for (i = height; i > baseline; --i)\n        {\n            for (let j = 0; j < line; j += 4)\n            {\n                if (imagedata[idx + j] !== 255)\n                {\n                    stop = true;\n                    break;\n                }\n            }\n\n            if (!stop)\n            {\n                idx -= line;\n            }\n            else\n            {\n                break;\n            }\n        }\n\n        properties.descent = i - baseline;\n        properties.fontSize = properties.ascent + properties.descent;\n\n        TextMetrics._fonts[font] = properties;\n\n        return properties;\n    }\n\n    /**\n     * Clear font metrics in metrics cache.\n     * @param {string} [font] - font name. If font name not set then clear cache for all fonts.\n     */\n    public static clearMetrics(font = ''): void\n    {\n        if (font)\n        {\n            delete TextMetrics._fonts[font];\n        }\n        else\n        {\n            TextMetrics._fonts = {};\n        }\n    }\n\n    /**\n     * Cached canvas element for measuring text\n     * TODO: this should be private, but isn't because of backward compat, will fix later.\n     * @ignore\n     */\n    public static get _canvas(): ICanvas\n    {\n        if (!TextMetrics.__canvas)\n        {\n            let canvas: ICanvas;\n\n            try\n            {\n                // OffscreenCanvas2D measureText can be up to 40% faster.\n                const c = new OffscreenCanvas(0, 0);\n                const context = c.getContext('2d', contextSettings);\n\n                if (context?.measureText)\n                {\n                    TextMetrics.__canvas = c;\n\n                    return c;\n                }\n\n                canvas = settings.ADAPTER.createCanvas();\n            }\n            catch (ex)\n            {\n                canvas = settings.ADAPTER.createCanvas();\n            }\n            canvas.width = canvas.height = 10;\n            TextMetrics.__canvas = canvas;\n        }\n\n        return TextMetrics.__canvas;\n    }\n\n    /**\n     * TODO: this should be private, but isn't because of backward compat, will fix later.\n     * @ignore\n     */\n    public static get _context(): ICanvasRenderingContext2D\n    {\n        if (!TextMetrics.__context)\n        {\n            TextMetrics.__context = TextMetrics._canvas.getContext('2d', contextSettings);\n        }\n\n        return TextMetrics.__context;\n    }\n}\n"], "mappings": ";AAsDA,MAAMA,eAAA,GAAqD;IAAA;IAEvDC,kBAAA,EAAoB;EACxB;EAgBaC,YAAA,GAAN,MAAMC,aAAA,CACb;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IA4EI,WAAkBC,mCAAA,EAClB;MACI,IAAIC,MAAA,GAASF,aAAA,CAAYG,mCAAA;MAEzB,IAAID,MAAA,KAAW,QACf;QACI,MAAME,KAAA,GAAQC,QAAA,CAASC,OAAA,CAAQC,2BAAA,GAA8BC,SAAA;QAE7DN,MAAA,GACMF,aAAA,CAAYG,mCAAA,GACZ,mBAAmBC,KAAA,IAAS,uBAAuBA,KAAA;MAC7D;MAEO,OAAAF,MAAA;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAmDAO,YAAYC,IAAA,EAAcC,KAAA,EAAkBC,KAAA,EAAeC,MAAA,EAAgBC,KAAA,EAAiBC,UAAA,EACxFC,UAAA,EAAoBC,YAAA,EAAsBC,cAAA,EAC9C;MACS,KAAAR,IAAA,GAAOA,IAAA,EACZ,KAAKC,KAAA,GAAQA,KAAA,EACb,KAAKC,KAAA,GAAQA,KAAA,EACb,KAAKC,MAAA,GAASA,MAAA,EACd,KAAKC,KAAA,GAAQA,KAAA,EACb,KAAKC,UAAA,GAAaA,UAAA,EAClB,KAAKC,UAAA,GAAaA,UAAA,EAClB,KAAKC,YAAA,GAAeA,YAAA,EACpB,KAAKC,cAAA,GAAiBA,cAAA;IAC1B;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAUA,OAAcC,YACVT,IAAA,EACAC,KAAA,EACAS,QAAA,EACAC,MAAA,GAAkBrB,aAAA,CAAYsB,OAAA,EAElC;MACIF,QAAA,GAAsCA,QAAA,IAAqBT,KAAA,CAAMS,QAAA;MACjE,MAAMG,IAAA,GAAOZ,KAAA,CAAMa,YAAA;QACbN,cAAA,GAAiBlB,aAAA,CAAYyB,WAAA,CAAYF,IAAI;MAI/CL,cAAA,CAAeQ,QAAA,KAAa,MAE5BR,cAAA,CAAeQ,QAAA,GAAWf,KAAA,CAAMe,QAAA,EAChCR,cAAA,CAAeS,MAAA,GAAShB,KAAA,CAAMe,QAAA;MAGlC,MAAME,OAAA,GAAUP,MAAA,CAAOQ,UAAA,CAAW,MAAMhC,eAAe;MAEvD+B,OAAA,CAAQL,IAAA,GAAOA,IAAA;MAGf,MAAMT,KAAA,IADaM,QAAA,GAAWpB,aAAA,CAAYoB,QAAA,CAASV,IAAA,EAAMC,KAAA,EAAOU,MAAM,IAAIX,IAAA,EACjDoB,KAAA,CAAM,gBAAgB;QACzCf,UAAA,GAAa,IAAIgB,KAAA,CAAcjB,KAAA,CAAMkB,MAAM;MACjD,IAAIf,YAAA,GAAe;MAEnB,SAASgB,CAAA,GAAI,GAAGA,CAAA,GAAInB,KAAA,CAAMkB,MAAA,EAAQC,CAAA,IAClC;QACU,MAAAC,SAAA,GAAYlC,aAAA,CAAYmC,YAAA,CAAarB,KAAA,CAAMmB,CAAC,GAAGtB,KAAA,CAAMyB,aAAA,EAAeR,OAAO;QAEjFb,UAAA,CAAWkB,CAAC,IAAIC,SAAA,EAChBjB,YAAA,GAAeoB,IAAA,CAAKC,GAAA,CAAIrB,YAAA,EAAciB,SAAS;MACnD;MACI,IAAAtB,KAAA,GAAQK,YAAA,GAAeN,KAAA,CAAM4B,eAAA;MAE7B5B,KAAA,CAAM6B,UAAA,KAEN5B,KAAA,IAASD,KAAA,CAAM8B,kBAAA;MAGnB,MAAMzB,UAAA,GAAaL,KAAA,CAAMK,UAAA,IAAcE,cAAA,CAAeQ,QAAA,GAAWf,KAAA,CAAM4B,eAAA;MACvE,IAAI1B,MAAA,GACEwB,IAAA,CAAKC,GAAA,CAAItB,UAAA,EAAYE,cAAA,CAAeQ,QAAA,GAAYf,KAAA,CAAM4B,eAAA,GAAkB,CAAE,IAAI5B,KAAA,CAAM+B,OAAA,IAClF5B,KAAA,CAAMkB,MAAA,GAAS,MAAMhB,UAAA,GAAaL,KAAA,CAAM+B,OAAA;MAEhD,OAAI/B,KAAA,CAAM6B,UAAA,KAEN3B,MAAA,IAAUF,KAAA,CAAM8B,kBAAA,GAGb,IAAIzC,aAAA,CACPU,IAAA,EACAC,KAAA,EACAC,KAAA,EACAC,MAAA,EACAC,KAAA,EACAC,UAAA,EACAC,UAAA,GAAaL,KAAA,CAAM+B,OAAA,EACnBzB,YAAA,EACAC,cAAA;IAER;IAEA,OAAeiB,aACXzB,IAAA,EACA0B,aAAA,EACAR,OAAA,EAEJ;MACI,IAAIe,4BAAA,GAA+B;MAE/B3C,aAAA,CAAYC,kCAAA,KAERD,aAAA,CAAY4C,yBAAA,IAEZhB,OAAA,CAAQQ,aAAA,GAAgB,GAAGA,aAAa,MACxCR,OAAA,CAAQiB,iBAAA,GAAoB,GAAGT,aAAa,MAC5CO,4BAAA,GAA+B,OAI/Bf,OAAA,CAAQQ,aAAA,GAAgB,OACxBR,OAAA,CAAQiB,iBAAA,GAAoB;MAIpC,IAAIjC,KAAA,GAAQgB,OAAA,CAAQT,WAAA,CAAYT,IAAI,EAAEE,KAAA;MAEtC,OAAIA,KAAA,GAAQ,MAEJ+B,4BAAA,GAEA/B,KAAA,IAASwB,aAAA,GAITxB,KAAA,KAAUZ,aAAA,CAAY8C,iBAAA,CAAkBpC,IAAI,EAAEsB,MAAA,GAAS,KAAKI,aAAA,GAI7DxB,KAAA;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAUA,OAAeQ,SACXV,IAAA,EACAC,KAAA,EACAU,MAAA,GAAkBrB,aAAA,CAAYsB,OAAA,EAElC;MACI,MAAMM,OAAA,GAAUP,MAAA,CAAOQ,UAAA,CAAW,MAAMhC,eAAe;MAEvD,IAAIe,KAAA,GAAQ;QACRmC,IAAA,GAAO;QACPjC,KAAA,GAAQ;MAEZ,MAAMkC,KAAA,GAAoC,eAAAC,MAAA,CAAAC,MAAA,CAAO,IAAI;QAC/C;UAAEd,aAAA;UAAee;QAAA,IAAexC,KAAA;QAGhCyC,cAAA,GAAiBpD,aAAA,CAAYoD,cAAA,CAAeD,UAAU;QACtDE,gBAAA,GAAmBrD,aAAA,CAAYqD,gBAAA,CAAiBF,UAAU;MAGhE,IAAIG,gBAAA,GAAmB,CAACF,cAAA;MAQxB,MAAMG,aAAA,GAAgB5C,KAAA,CAAM4C,aAAA,GAAgBnB,aAAA;QAGtCoB,MAAA,GAASxD,aAAA,CAAYyD,QAAA,CAAS/C,IAAI;MAExC,SAASuB,CAAA,GAAI,GAAGA,CAAA,GAAIuB,MAAA,CAAOxB,MAAA,EAAQC,CAAA,IACnC;QAEQ,IAAAyB,KAAA,GAAQF,MAAA,CAAOvB,CAAC;QAGhB,IAAAjC,aAAA,CAAY2D,SAAA,CAAUD,KAAK,GAC/B;UAEI,IAAI,CAACL,gBAAA,EACL;YACavC,KAAA,IAAAd,aAAA,CAAY4D,OAAA,CAAQb,IAAI,GACjCO,gBAAA,GAAmB,CAACF,cAAA,EACpBL,IAAA,GAAO,IACPnC,KAAA,GAAQ;YACR;UACJ;UAIQ8C,KAAA;QACZ;QAGA,IAAIN,cAAA,EACJ;UAEI,MAAMS,mBAAA,GAAsB7D,aAAA,CAAY8D,eAAA,CAAgBJ,KAAK;YACvDK,mBAAA,GAAsB/D,aAAA,CAAY8D,eAAA,CAAgBf,IAAA,CAAKA,IAAA,CAAKf,MAAA,GAAS,CAAC,CAAC;UAE7E,IAAI6B,mBAAA,IAAuBE,mBAAA,EAEvB;QAER;QAGA,MAAMC,UAAA,GAAahE,aAAA,CAAYiE,YAAA,CAAaP,KAAA,EAAOtB,aAAA,EAAeY,KAAA,EAAOpB,OAAO;QAGhF,IAAIoC,UAAA,GAAaT,aAAA;UAYb,IATIR,IAAA,KAAS,OAGTjC,KAAA,IAASd,aAAA,CAAY4D,OAAA,CAAQb,IAAI,GACjCA,IAAA,GAAO,IACPnC,KAAA,GAAQ,IAIRZ,aAAA,CAAYkE,aAAA,CAAcR,KAAA,EAAO/C,KAAA,CAAMwD,UAAU,GACrD;YAEU,MAAAC,UAAA,GAAapE,aAAA,CAAYqE,aAAA,CAAcX,KAAK;YAGlD,SAASY,CAAA,GAAI,GAAGA,CAAA,GAAIF,UAAA,CAAWpC,MAAA,EAAQsC,CAAA,IACvC;cACI,IAAIC,IAAA,GAAOH,UAAA,CAAWE,CAAC;gBACnBE,QAAA,GAAWD,IAAA;gBAEXE,CAAA,GAAI;cAGD,OAAAL,UAAA,CAAWE,CAAA,GAAIG,CAAC,IACvB;gBACU,MAAAC,QAAA,GAAWN,UAAA,CAAWE,CAAA,GAAIG,CAAC;gBAG7B,KAACzE,aAAA,CAAY2E,aAAA,CAAcH,QAAA,EAAUE,QAAA,EAAUhB,KAAA,EAAOY,CAAA,EAAG3D,KAAA,CAAMwD,UAAU,GAGjEI,IAAA,IAAAG,QAAA,MAIR;gBAGJF,QAAA,GAAWE,QAAA,EACXD,CAAA;cACJ;cAEAH,CAAA,IAAKG,CAAA,GAAI;cAET,MAAMG,cAAA,GAAiB5E,aAAA,CAAYiE,YAAA,CAAaM,IAAA,EAAMnC,aAAA,EAAeY,KAAA,EAAOpB,OAAO;cAE/EgD,cAAA,GAAiBhE,KAAA,GAAQ2C,aAAA,KAEzBzC,KAAA,IAASd,aAAA,CAAY4D,OAAA,CAAQb,IAAI,GACjCO,gBAAA,GAAmB,IACnBP,IAAA,GAAO,IACPnC,KAAA,GAAQ,IAGZmC,IAAA,IAAQwB,IAAA,EACR3D,KAAA,IAASgE,cAAA;YACb;UAAA,OAKJ;YAGQ7B,IAAA,CAAKf,MAAA,GAAS,MAEdlB,KAAA,IAASd,aAAA,CAAY4D,OAAA,CAAQb,IAAI,GACjCA,IAAA,GAAO,IACPnC,KAAA,GAAQ;YAGN,MAAAiE,WAAA,GAAc5C,CAAA,KAAMuB,MAAA,CAAOxB,MAAA,GAAS;YAGjClB,KAAA,IAAAd,aAAA,CAAY4D,OAAA,CAAQF,KAAA,EAAO,CAACmB,WAAW,GAChDvB,gBAAA,GAAmB,IACnBP,IAAA,GAAO,IACPnC,KAAA,GAAQ;UACZ;QAAA,OAQIoD,UAAA,GAAapD,KAAA,GAAQ2C,aAAA,KAGrBD,gBAAA,GAAmB,IAGnBxC,KAAA,IAASd,aAAA,CAAY4D,OAAA,CAAQb,IAAI,GAGjCA,IAAA,GAAO,IACPnC,KAAA,GAAQ,KAIRmC,IAAA,CAAKf,MAAA,GAAS,KAAK,CAAChC,aAAA,CAAY8D,eAAA,CAAgBJ,KAAK,KAAKJ,gBAAA,MAG1DP,IAAA,IAAQW,KAAA,EAGR9C,KAAA,IAASoD,UAAA;MAGrB;MAEA,OAAAlD,KAAA,IAASd,aAAA,CAAY4D,OAAA,CAAQb,IAAA,EAAM,EAAK,GAEjCjC,KAAA;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAQA,OAAe8C,QAAQb,IAAA,EAAc+B,OAAA,GAAU,IAC/C;MACI,OAAA/B,IAAA,GAAO/C,aAAA,CAAY+E,SAAA,CAAUhC,IAAI,GAEjCA,IAAA,GAAQ+B,OAAA,GAAW,GAAG/B,IAAI;AAAA,IAAOA,IAAA,EAE1BA,IAAA;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAUA,OAAekB,aAAae,GAAA,EAAa5C,aAAA,EAAuBY,KAAA,EAC5DpB,OAAA,EACJ;MACQ,IAAAhB,KAAA,GAAQoC,KAAA,CAAMgC,GAAG;MAErB,OAAI,OAAOpE,KAAA,IAAU,aAEjBA,KAAA,GAAQZ,aAAA,CAAYmC,YAAA,CAAa6C,GAAA,EAAK5C,aAAA,EAAeR,OAAO,IAAIQ,aAAA,EAChEY,KAAA,CAAMgC,GAAG,IAAIpE,KAAA,GAGVA,KAAA;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;IAOA,OAAewC,eAAeD,UAAA,EAC9B;MACY,OAAAA,UAAA,KAAe,YAAYA,UAAA,KAAe;IACtD;IAAA;AAAA;AAAA;AAAA;AAAA;IAOA,OAAeE,iBAAiBF,UAAA,EAChC;MACI,OAAQA,UAAA,KAAe;IAC3B;IAAA;AAAA;AAAA;AAAA;AAAA;IAOA,OAAe4B,UAAUrE,IAAA,EACzB;MACI,IAAI,OAAOA,IAAA,IAAS,UAET;MAGX,SAASuB,CAAA,GAAIvB,IAAA,CAAKsB,MAAA,GAAS,GAAGC,CAAA,IAAK,GAAGA,CAAA,IACtC;QACU,MAAAsC,IAAA,GAAO7D,IAAA,CAAKuB,CAAC;QAEf,KAACjC,aAAA,CAAY8D,eAAA,CAAgBS,IAAI,GAEjC;QAGG7D,IAAA,GAAAA,IAAA,CAAKuE,KAAA,CAAM,GAAG,EAAE;MAC3B;MAEO,OAAAvE,IAAA;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;IAOA,OAAeiD,UAAUY,IAAA,EACzB;MACQ,cAAOA,IAAA,IAAS,WAET,KAGJvE,aAAA,CAAYkF,SAAA,CAAUC,QAAA,CAASZ,IAAA,CAAKa,UAAA,CAAW,CAAC,CAAC;IAC5D;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAYA,OAAOtB,gBAAgBS,IAAA,EAAcc,SAAA,EACrC;MACQ,cAAOd,IAAA,IAAS,WAET,KAGJvE,aAAA,CAAYsF,eAAA,CAAgBH,QAAA,CAASZ,IAAA,CAAKa,UAAA,CAAW,CAAC,CAAC;IAClE;IAAA;AAAA;AAAA;AAAA;AAAA;IAOA,OAAe3B,SAAS/C,IAAA,EACxB;MACI,MAAM8C,MAAA,GAAmB;MACzB,IAAIE,KAAA,GAAQ;MAEZ,IAAI,OAAOhD,IAAA,IAAS,UAET,OAAA8C,MAAA;MAGX,SAASvB,CAAA,GAAI,GAAGA,CAAA,GAAIvB,IAAA,CAAKsB,MAAA,EAAQC,CAAA,IACjC;QACI,MAAMsC,IAAA,GAAO7D,IAAA,CAAKuB,CAAC;UACbyC,QAAA,GAAWhE,IAAA,CAAKuB,CAAA,GAAI,CAAC;QAEvB,IAAAjC,aAAA,CAAY8D,eAAA,CAAgBS,IAAA,EAAMG,QAAQ,KAAK1E,aAAA,CAAY2D,SAAA,CAAUY,IAAI,GAC7E;UACQb,KAAA,KAAU,OAEVF,MAAA,CAAO+B,IAAA,CAAK7B,KAAK,GACjBA,KAAA,GAAQ,KAGZF,MAAA,CAAO+B,IAAA,CAAKhB,IAAI;UAEhB;QACJ;QAESb,KAAA,IAAAa,IAAA;MACb;MAEA,OAAIb,KAAA,KAAU,MAEVF,MAAA,CAAO+B,IAAA,CAAK7B,KAAK,GAGdF,MAAA;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAYA,OAAOU,cAAcsB,MAAA,EAAgBrB,UAAA,EACrC;MACW,OAAAA,UAAA;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAgBA,OAAOQ,cAAcc,KAAA,EAAeJ,SAAA,EAAmBG,MAAA,EAAgBE,MAAA,EACnEC,WAAA,EACJ;MACW;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAYA,OAAOtB,cAAcX,KAAA,EACrB;MACW,OAAA1D,aAAA,CAAY8C,iBAAA,CAAkBY,KAAK;IAC9C;IAAA;AAAA;AAAA;AAAA;AAAA;IAOA,OAAcjC,YAAYF,IAAA,EAC1B;MAEQ,IAAAvB,aAAA,CAAY4F,MAAA,CAAOrE,IAAI,GAEhB,OAAAvB,aAAA,CAAY4F,MAAA,CAAOrE,IAAI;MAGlC,MAAMsE,UAAA,GAA2B;UAC7BlE,MAAA,EAAQ;UACRmE,OAAA,EAAS;UACTpE,QAAA,EAAU;QAAA;QAGRL,MAAA,GAASrB,aAAA,CAAYsB,OAAA;QACrBM,OAAA,GAAU5B,aAAA,CAAY+F,QAAA;MAE5BnE,OAAA,CAAQL,IAAA,GAAOA,IAAA;MAEf,MAAMyE,aAAA,GAAgBhG,aAAA,CAAYiG,cAAA,GAAiBjG,aAAA,CAAYkG,eAAA;QACzDtF,KAAA,GAAQyB,IAAA,CAAK8D,IAAA,CAAKvE,OAAA,CAAQT,WAAA,CAAY6E,aAAa,EAAEpF,KAAK;MAC5D,IAAAwF,QAAA,GAAW/D,IAAA,CAAK8D,IAAA,CAAKvE,OAAA,CAAQT,WAAA,CAAYnB,aAAA,CAAYkG,eAAe,EAAEtF,KAAK;MAC/E,MAAMC,MAAA,GAASwB,IAAA,CAAK8D,IAAA,CAAKnG,aAAA,CAAYqG,iBAAA,GAAoBD,QAAQ;MAIjE,IAFAA,QAAA,GAAWA,QAAA,GAAWpG,aAAA,CAAYsG,mBAAA,GAAsB,GAEpD1F,KAAA,KAAU,KAAKC,MAAA,KAAW,GAEd,OAAAb,aAAA,CAAA4F,MAAA,CAAOrE,IAAI,IAAIsE,UAAA,EAEpBA,UAAA;MAGXxE,MAAA,CAAOT,KAAA,GAAQA,KAAA,EACfS,MAAA,CAAOR,MAAA,GAASA,MAAA,EAEhBe,OAAA,CAAQ2E,SAAA,GAAY,QACpB3E,OAAA,CAAQ4E,QAAA,CAAS,GAAG,GAAG5F,KAAA,EAAOC,MAAM,GAEpCe,OAAA,CAAQL,IAAA,GAAOA,IAAA,EAEfK,OAAA,CAAQ6E,YAAA,GAAe,cACvB7E,OAAA,CAAQ2E,SAAA,GAAY,QACpB3E,OAAA,CAAQ8E,QAAA,CAASV,aAAA,EAAe,GAAGI,QAAQ;MAE3C,MAAMO,SAAA,GAAY/E,OAAA,CAAQgF,YAAA,CAAa,GAAG,GAAGhG,KAAA,EAAOC,MAAM,EAAEgG,IAAA;QACtDC,MAAA,GAASH,SAAA,CAAU3E,MAAA;QACnBe,IAAA,GAAOnC,KAAA,GAAQ;MAErB,IAAIqB,CAAA,GAAI;QACJ8E,GAAA,GAAM;QACNC,IAAA,GAAO;MAGX,KAAK/E,CAAA,GAAI,GAAGA,CAAA,GAAImE,QAAA,EAAU,EAAEnE,CAAA,EAC5B;QACI,SAASqC,CAAA,GAAI,GAAGA,CAAA,GAAIvB,IAAA,EAAMuB,CAAA,IAAK,GAE3B,IAAIqC,SAAA,CAAUI,GAAA,GAAMzC,CAAC,MAAM,KAC3B;UACW0C,IAAA;UACP;QACJ;QAEJ,IAAI,CAACA,IAAA,EAEMD,GAAA,IAAAhE,IAAA,MAIP;MAER;MAQA,KANA8C,UAAA,CAAWlE,MAAA,GAASyE,QAAA,GAAWnE,CAAA,EAE/B8E,GAAA,GAAMD,MAAA,GAAS/D,IAAA,EACfiE,IAAA,GAAO,IAGF/E,CAAA,GAAIpB,MAAA,EAAQoB,CAAA,GAAImE,QAAA,EAAU,EAAEnE,CAAA,EACjC;QACI,SAASqC,CAAA,GAAI,GAAGA,CAAA,GAAIvB,IAAA,EAAMuB,CAAA,IAAK,GAE3B,IAAIqC,SAAA,CAAUI,GAAA,GAAMzC,CAAC,MAAM,KAC3B;UACW0C,IAAA;UACP;QACJ;QAGJ,IAAI,CAACA,IAAA,EAEMD,GAAA,IAAAhE,IAAA,MAIP;MAER;MAEA,OAAA8C,UAAA,CAAWC,OAAA,GAAU7D,CAAA,GAAImE,QAAA,EACzBP,UAAA,CAAWnE,QAAA,GAAWmE,UAAA,CAAWlE,MAAA,GAASkE,UAAA,CAAWC,OAAA,EAErD9F,aAAA,CAAY4F,MAAA,CAAOrE,IAAI,IAAIsE,UAAA,EAEpBA,UAAA;IACX;IAAA;AAAA;AAAA;AAAA;IAMA,OAAcoB,aAAa1F,IAAA,GAAO,IAClC;MACQA,IAAA,GAEA,OAAOvB,aAAA,CAAY4F,MAAA,CAAOrE,IAAI,IAI9BvB,aAAA,CAAY4F,MAAA,GAAS;IAE7B;IAAA;AAAA;AAAA;AAAA;AAAA;IAOA,WAAkBtE,QAAA,EAClB;MACQ,KAACtB,aAAA,CAAYkH,QAAA,EACjB;QACQ,IAAA7F,MAAA;QAGJ;UAEI,MAAM8F,CAAA,GAAI,IAAIC,eAAA,CAAgB,GAAG,CAAC;UAGlC,IAFgBD,CAAA,CAAEtF,UAAA,CAAW,MAAMhC,eAAe,GAErCsB,WAAA,EAET,OAAAnB,aAAA,CAAYkH,QAAA,GAAWC,CAAA,EAEhBA,CAAA;UAGF9F,MAAA,GAAAhB,QAAA,CAASC,OAAA,CAAQ+G,YAAA;QAAa,QAG3C;UACahG,MAAA,GAAAhB,QAAA,CAASC,OAAA,CAAQ+G,YAAA;QAC9B;QACAhG,MAAA,CAAOT,KAAA,GAAQS,MAAA,CAAOR,MAAA,GAAS,IAC/Bb,aAAA,CAAYkH,QAAA,GAAW7F,MAAA;MAC3B;MAEA,OAAOrB,aAAA,CAAYkH,QAAA;IACvB;IAAA;AAAA;AAAA;AAAA;IAMA,WAAkBnB,SAAA,EAClB;MACS,OAAA/F,aAAA,CAAYsH,SAAA,KAEbtH,aAAA,CAAYsH,SAAA,GAAYtH,aAAA,CAAYsB,OAAA,CAAQO,UAAA,CAAW,MAAMhC,eAAe,IAGzEG,aAAA,CAAYsH,SAAA;IACvB;EACJ;AA91BavH,YAAA,CAiCKkG,cAAA,GAAiB;AAjCtBlG,YAAA,CAoCKmG,eAAA,GAAkB;AApCvBnG,YAAA,CAuCKuG,mBAAA,GAAsB;AAvC3BvG,YAAA,CA0CKsG,iBAAA,GAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA1CzBtG,YAAA,CAuDK+C,iBAAA,IAA8C,MAC5D;EACQ,WAAQyE,IAAA,EAAgBC,SAAA,IAAc,YAC1C;IACU,MAAAC,SAAA,GAAY,IAAKF,IAAA,CAAeC,SAAA;IAEtC,OAAQE,CAAA,IAAc,CAAC,GAAGD,SAAA,CAAUE,OAAA,CAAQD,CAAC,CAAC,EAAEE,GAAA,CAAKC,CAAA,IAAMA,CAAA,CAAEF,OAAO;EACxE;EAEA,OAAQD,CAAA,IAAc,CAAC,GAAGA,CAAC;AAC/B,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAjEM3H,YAAA,CAmGK6C,yBAAA,GAA4B;AAnGjC7C,YAAA,CAsGM6F,MAAA,GAAuC,CAAC;AAtG9C7F,YAAA,CAyGMmF,SAAA,GAAsB,CACjC;AAAA;AACA;AAAA;AAAA,CACJ;AA5GSnF,YAAA,CA+GMuF,eAAA,GAA4B,CACvC;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA,CACJ;AA9HG,IAAMwC,WAAA,GAAN/H,YAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}