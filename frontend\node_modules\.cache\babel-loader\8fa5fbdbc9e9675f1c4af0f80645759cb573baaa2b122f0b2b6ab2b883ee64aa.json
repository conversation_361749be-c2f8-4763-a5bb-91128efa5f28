{"ast": null, "code": "import { Color } from \"@pixi/color\";\nimport { ExtensionType, extensions } from \"@pixi/extensions\";\nimport { Rectangle } from \"@pixi/math\";\nconst tempRect = new Rectangle(),\n  tempRect2 = new Rectangle();\nclass RenderTextureSystem {\n  /**\n   * @param renderer - The renderer this System works for.\n   */\n  constructor(renderer) {\n    this.renderer = renderer, this.defaultMaskStack = [], this.current = null, this.sourceFrame = new Rectangle(), this.destinationFrame = new Rectangle(), this.viewportFrame = new Rectangle();\n  }\n  contextChange() {\n    const attributes = this.renderer?.gl.getContextAttributes();\n    this._rendererPremultipliedAlpha = !!(attributes && attributes.alpha && attributes.premultipliedAlpha);\n  }\n  /**\n   * Bind the current render texture.\n   * @param renderTexture - RenderTexture to bind, by default its `null` - the screen.\n   * @param sourceFrame - Part of world that is mapped to the renderTexture.\n   * @param destinationFrame - Part of renderTexture, by default it has the same size as sourceFrame.\n   */\n  bind(renderTexture = null, sourceFrame, destinationFrame) {\n    const renderer = this.renderer;\n    this.current = renderTexture;\n    let baseTexture, framebuffer, resolution;\n    renderTexture ? (baseTexture = renderTexture.baseTexture, resolution = baseTexture.resolution, sourceFrame || (tempRect.width = renderTexture.frame.width, tempRect.height = renderTexture.frame.height, sourceFrame = tempRect), destinationFrame || (tempRect2.x = renderTexture.frame.x, tempRect2.y = renderTexture.frame.y, tempRect2.width = sourceFrame.width, tempRect2.height = sourceFrame.height, destinationFrame = tempRect2), framebuffer = baseTexture.framebuffer) : (resolution = renderer.resolution, sourceFrame || (tempRect.width = renderer._view.screen.width, tempRect.height = renderer._view.screen.height, sourceFrame = tempRect), destinationFrame || (destinationFrame = tempRect, destinationFrame.width = sourceFrame.width, destinationFrame.height = sourceFrame.height));\n    const viewportFrame = this.viewportFrame;\n    viewportFrame.x = destinationFrame.x * resolution, viewportFrame.y = destinationFrame.y * resolution, viewportFrame.width = destinationFrame.width * resolution, viewportFrame.height = destinationFrame.height * resolution, renderTexture || (viewportFrame.y = renderer.view.height - (viewportFrame.y + viewportFrame.height)), viewportFrame.ceil(), this.renderer.framebuffer.bind(framebuffer, viewportFrame), this.renderer.projection.update(destinationFrame, sourceFrame, resolution, !framebuffer), renderTexture ? this.renderer.mask.setMaskStack(baseTexture.maskStack) : this.renderer.mask.setMaskStack(this.defaultMaskStack), this.sourceFrame.copyFrom(sourceFrame), this.destinationFrame.copyFrom(destinationFrame);\n  }\n  /**\n   * Erases the render texture and fills the drawing area with a colour.\n   * @param clearColor - The color as rgba, default to use the renderer backgroundColor\n   * @param [mask=BUFFER_BITS.COLOR | BUFFER_BITS.DEPTH] - Bitwise OR of masks\n   *  that indicate the buffers to be cleared, by default COLOR and DEPTH buffers.\n   */\n  clear(clearColor, mask) {\n    const fallbackColor = this.current ? this.current.baseTexture.clear : this.renderer.background.backgroundColor,\n      color = Color.shared.setValue(clearColor || fallbackColor);\n    (this.current && this.current.baseTexture.alphaMode > 0 || !this.current && this._rendererPremultipliedAlpha) && color.premultiply(color.alpha);\n    const destinationFrame = this.destinationFrame,\n      baseFrame = this.current ? this.current.baseTexture : this.renderer._view.screen,\n      clearMask = destinationFrame.width !== baseFrame.width || destinationFrame.height !== baseFrame.height;\n    if (clearMask) {\n      let {\n        x,\n        y,\n        width,\n        height\n      } = this.viewportFrame;\n      x = Math.round(x), y = Math.round(y), width = Math.round(width), height = Math.round(height), this.renderer.gl.enable(this.renderer.gl.SCISSOR_TEST), this.renderer.gl.scissor(x, y, width, height);\n    }\n    this.renderer.framebuffer.clear(color.red, color.green, color.blue, color.alpha, mask), clearMask && this.renderer.scissor.pop();\n  }\n  resize() {\n    this.bind(null);\n  }\n  /** Resets render-texture state. */\n  reset() {\n    this.bind(null);\n  }\n  destroy() {\n    this.renderer = null;\n  }\n}\nRenderTextureSystem.extension = {\n  type: ExtensionType.RendererSystem,\n  name: \"renderTexture\"\n};\nextensions.add(RenderTextureSystem);\nexport { RenderTextureSystem };", "map": {"version": 3, "names": ["tempRect", "Rectangle", "tempRect2", "RenderTextureSystem", "constructor", "renderer", "defaultMaskStack", "current", "sourceFrame", "destinationFrame", "viewportFrame", "contextChange", "attributes", "gl", "getContextAttributes", "_rendererPremultipliedAlpha", "alpha", "premultipliedAlpha", "bind", "renderTexture", "baseTexture", "framebuffer", "resolution", "width", "frame", "height", "x", "y", "_view", "screen", "view", "ceil", "projection", "update", "mask", "setMaskStack", "maskStack", "copyFrom", "clear", "clearColor", "fallbackColor", "background", "backgroundColor", "color", "Color", "shared", "setValue", "alphaMode", "premultiply", "baseFrame", "clearMask", "Math", "round", "enable", "SCISSOR_TEST", "scissor", "red", "green", "blue", "pop", "resize", "reset", "destroy", "extension", "type", "ExtensionType", "RendererSystem", "name", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\renderTexture\\RenderTextureSystem.ts"], "sourcesContent": ["import { Color } from '@pixi/color';\nimport { extensions, ExtensionType } from '@pixi/extensions';\nimport { Rectangle } from '@pixi/math';\n\nimport type { ColorSource } from '@pixi/color';\nimport type { BUFFER_BITS } from '@pixi/constants';\nimport type { ExtensionMetadata } from '@pixi/extensions';\nimport type { ISize } from '@pixi/math';\nimport type { MaskData } from '../mask/MaskData';\nimport type { Renderer } from '../Renderer';\nimport type { ISystem } from '../system/ISystem';\nimport type { BaseRenderTexture } from './BaseRenderTexture';\nimport type { RenderTexture } from './RenderTexture';\n\n// Temporary rectangle for assigned sourceFrame or destinationFrame\nconst tempRect = new Rectangle();\n\n// Temporary rectangle for renderTexture destinationFrame\nconst tempRect2 = new Rectangle();\n\n/* eslint-disable max-len */\n/**\n * System plugin to the renderer to manage render textures.\n *\n * Should be added after FramebufferSystem\n *\n * ### Frames\n *\n * The `RenderTextureSystem` holds a sourceFrame → destinationFrame projection. The following table explains the different\n * coordinate spaces used:\n *\n * | Frame                  | Description                                                      | Coordinate System                                       |\n * | ---------------------- | ---------------------------------------------------------------- | ------------------------------------------------------- |\n * | sourceFrame            | The rectangle inside of which display-objects are being rendered | **World Space**: The origin on the top-left             |\n * | destinationFrame       | The rectangle in the render-target (canvas or texture) into which contents should be rendered | If rendering to the canvas, this is in screen space and the origin is on the top-left. If rendering to a render-texture, this is in its base-texture's space with the origin on the bottom-left.  |\n * | viewportFrame          | The framebuffer viewport corresponding to the destination-frame  | **Window Coordinates**: The origin is always on the bottom-left. |\n * @memberof PIXI\n */\nexport class RenderTextureSystem implements ISystem\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        type: ExtensionType.RendererSystem,\n        name: 'renderTexture',\n    };\n\n    /* eslint-enable max-len */\n\n    /**\n     * List of masks for the {@link PIXI.StencilSystem}.\n     * @readonly\n     */\n    public defaultMaskStack: Array<MaskData>;\n\n    /**\n     * Render texture currently bound. {@code null} if rendering to the canvas.\n     * @readonly\n     */\n    public current: RenderTexture | null;\n\n    /**\n     * The source frame for the render-target's projection mapping.\n     *\n     * See {@link PIXI.ProjectionSystem#sourceFrame} for more details\n     */\n    public readonly sourceFrame: Rectangle;\n\n    /**\n     * The destination frame for the render-target's projection mapping.\n     *\n     * See {@link PIXI.ProjectionSystem#destinationFrame} for more details.\n     */\n    public readonly destinationFrame: Rectangle;\n\n    /**\n     * The viewport frame for the render-target's viewport binding. This is equal to the destination-frame\n     * for render-textures, while it is y-flipped when rendering to the screen (i.e. its origin is always on\n     * the bottom-left).\n     */\n    public readonly viewportFrame: Rectangle;\n\n    private renderer: Renderer;\n\n    /** Does the renderer have alpha and are its color channels stored premultipled by the alpha channel? */\n    private _rendererPremultipliedAlpha: boolean;\n\n    /**\n     * @param renderer - The renderer this System works for.\n     */\n    constructor(renderer: Renderer)\n    {\n        this.renderer = renderer;\n\n        this.defaultMaskStack = [];\n        this.current = null;\n        this.sourceFrame = new Rectangle();\n        this.destinationFrame = new Rectangle();\n        this.viewportFrame = new Rectangle();\n    }\n\n    protected contextChange(): void\n    {\n        const attributes = this.renderer?.gl.getContextAttributes();\n\n        this._rendererPremultipliedAlpha = !!(attributes && attributes.alpha && attributes.premultipliedAlpha);\n    }\n\n    /**\n     * Bind the current render texture.\n     * @param renderTexture - RenderTexture to bind, by default its `null` - the screen.\n     * @param sourceFrame - Part of world that is mapped to the renderTexture.\n     * @param destinationFrame - Part of renderTexture, by default it has the same size as sourceFrame.\n     */\n    bind(renderTexture: RenderTexture = null, sourceFrame?: Rectangle, destinationFrame?: Rectangle): void\n    {\n        const renderer = this.renderer;\n\n        this.current = renderTexture;\n\n        let baseTexture: BaseRenderTexture;\n        let framebuffer;\n        let resolution;\n\n        if (renderTexture)\n        {\n            baseTexture = renderTexture.baseTexture as BaseRenderTexture;\n\n            resolution = baseTexture.resolution;\n\n            if (!sourceFrame)\n            {\n                tempRect.width = renderTexture.frame.width;\n                tempRect.height = renderTexture.frame.height;\n\n                sourceFrame = tempRect;\n            }\n\n            if (!destinationFrame)\n            {\n                tempRect2.x = renderTexture.frame.x;\n                tempRect2.y = renderTexture.frame.y;\n                tempRect2.width = sourceFrame.width;\n                tempRect2.height = sourceFrame.height;\n\n                destinationFrame = tempRect2;\n            }\n\n            framebuffer = baseTexture.framebuffer;\n        }\n        else\n        {\n            resolution = renderer.resolution;\n\n            if (!sourceFrame)\n            {\n                tempRect.width = renderer._view.screen.width;\n                tempRect.height = renderer._view.screen.height;\n\n                sourceFrame = tempRect;\n            }\n\n            if (!destinationFrame)\n            {\n                destinationFrame = tempRect;\n\n                destinationFrame.width = sourceFrame.width;\n                destinationFrame.height = sourceFrame.height;\n            }\n        }\n\n        const viewportFrame = this.viewportFrame;\n\n        viewportFrame.x = destinationFrame.x * resolution;\n        viewportFrame.y = destinationFrame.y * resolution;\n        viewportFrame.width = destinationFrame.width * resolution;\n        viewportFrame.height = destinationFrame.height * resolution;\n\n        if (!renderTexture)\n        {\n            viewportFrame.y = renderer.view.height - (viewportFrame.y + viewportFrame.height);\n        }\n\n        viewportFrame.ceil();\n\n        this.renderer.framebuffer.bind(framebuffer, viewportFrame);\n        this.renderer.projection.update(destinationFrame, sourceFrame, resolution, !framebuffer);\n\n        if (renderTexture)\n        {\n            this.renderer.mask.setMaskStack(baseTexture.maskStack);\n        }\n        else\n        {\n            this.renderer.mask.setMaskStack(this.defaultMaskStack);\n        }\n\n        this.sourceFrame.copyFrom(sourceFrame);\n        this.destinationFrame.copyFrom(destinationFrame);\n    }\n\n    /**\n     * Erases the render texture and fills the drawing area with a colour.\n     * @param clearColor - The color as rgba, default to use the renderer backgroundColor\n     * @param [mask=BUFFER_BITS.COLOR | BUFFER_BITS.DEPTH] - Bitwise OR of masks\n     *  that indicate the buffers to be cleared, by default COLOR and DEPTH buffers.\n     */\n    clear(clearColor?: ColorSource, mask?: BUFFER_BITS): void\n    {\n        const fallbackColor = this.current\n            ? this.current.baseTexture.clear\n            : this.renderer.background.backgroundColor;\n        const color = Color.shared.setValue(clearColor ? clearColor : fallbackColor);\n\n        if ((this.current && this.current.baseTexture.alphaMode > 0)\n            || (!this.current && this._rendererPremultipliedAlpha))\n        {\n            color.premultiply(color.alpha);\n        }\n\n        const destinationFrame = this.destinationFrame;\n        const baseFrame: ISize = this.current ? this.current.baseTexture : this.renderer._view.screen;\n        const clearMask = destinationFrame.width !== baseFrame.width || destinationFrame.height !== baseFrame.height;\n\n        if (clearMask)\n        {\n            let { x, y, width, height } = this.viewportFrame;\n\n            x = Math.round(x);\n            y = Math.round(y);\n            width = Math.round(width);\n            height = Math.round(height);\n\n            // TODO: ScissorSystem should cache whether the scissor test is enabled or not.\n            this.renderer.gl.enable(this.renderer.gl.SCISSOR_TEST);\n            this.renderer.gl.scissor(x, y, width, height);\n        }\n\n        this.renderer.framebuffer.clear(color.red, color.green, color.blue, color.alpha, mask);\n\n        if (clearMask)\n        {\n            // Restore the scissor box\n            this.renderer.scissor.pop();\n        }\n    }\n\n    resize(): void // screenWidth, screenHeight)\n    {\n        // resize the root only!\n        this.bind(null);\n    }\n\n    /** Resets render-texture state. */\n    reset(): void\n    {\n        this.bind(null);\n    }\n\n    destroy(): void\n    {\n        this.renderer = null;\n    }\n}\n\nextensions.add(RenderTextureSystem);\n"], "mappings": ";;;AAeA,MAAMA,QAAA,GAAW,IAAIC,SAAA;EAGfC,SAAA,GAAY,IAAID,SAAA,CAAU;AAoBzB,MAAME,mBAAA,CACb;EAAA;AAAA;AAAA;EAkDIC,YAAYC,QAAA,EACZ;IACS,KAAAA,QAAA,GAAWA,QAAA,EAEhB,KAAKC,gBAAA,GAAmB,IACxB,KAAKC,OAAA,GAAU,MACf,KAAKC,WAAA,GAAc,IAAIP,SAAA,CAAU,GACjC,KAAKQ,gBAAA,GAAmB,IAAIR,SAAA,CAC5B,QAAKS,aAAA,GAAgB,IAAIT,SAAA;EAC7B;EAEUU,cAAA,EACV;IACI,MAAMC,UAAA,GAAa,KAAKP,QAAA,EAAUQ,EAAA,CAAGC,oBAAA,CAAqB;IAE1D,KAAKC,2BAAA,GAA8B,CAAC,EAAEH,UAAA,IAAcA,UAAA,CAAWI,KAAA,IAASJ,UAAA,CAAWK,kBAAA;EACvF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAC,KAAKC,aAAA,GAA+B,MAAMX,WAAA,EAAyBC,gBAAA,EACnE;IACI,MAAMJ,QAAA,GAAW,KAAKA,QAAA;IAEtB,KAAKE,OAAA,GAAUY,aAAA;IAEf,IAAIC,WAAA,EACAC,WAAA,EACAC,UAAA;IAEAH,aAAA,IAEAC,WAAA,GAAcD,aAAA,CAAcC,WAAA,EAE5BE,UAAA,GAAaF,WAAA,CAAYE,UAAA,EAEpBd,WAAA,KAEDR,QAAA,CAASuB,KAAA,GAAQJ,aAAA,CAAcK,KAAA,CAAMD,KAAA,EACrCvB,QAAA,CAASyB,MAAA,GAASN,aAAA,CAAcK,KAAA,CAAMC,MAAA,EAEtCjB,WAAA,GAAcR,QAAA,GAGbS,gBAAA,KAEDP,SAAA,CAAUwB,CAAA,GAAIP,aAAA,CAAcK,KAAA,CAAME,CAAA,EAClCxB,SAAA,CAAUyB,CAAA,GAAIR,aAAA,CAAcK,KAAA,CAAMG,CAAA,EAClCzB,SAAA,CAAUqB,KAAA,GAAQf,WAAA,CAAYe,KAAA,EAC9BrB,SAAA,CAAUuB,MAAA,GAASjB,WAAA,CAAYiB,MAAA,EAE/BhB,gBAAA,GAAmBP,SAAA,GAGvBmB,WAAA,GAAcD,WAAA,CAAYC,WAAA,KAI1BC,UAAA,GAAajB,QAAA,CAASiB,UAAA,EAEjBd,WAAA,KAEDR,QAAA,CAASuB,KAAA,GAAQlB,QAAA,CAASuB,KAAA,CAAMC,MAAA,CAAON,KAAA,EACvCvB,QAAA,CAASyB,MAAA,GAASpB,QAAA,CAASuB,KAAA,CAAMC,MAAA,CAAOJ,MAAA,EAExCjB,WAAA,GAAcR,QAAA,GAGbS,gBAAA,KAEDA,gBAAA,GAAmBT,QAAA,EAEnBS,gBAAA,CAAiBc,KAAA,GAAQf,WAAA,CAAYe,KAAA,EACrCd,gBAAA,CAAiBgB,MAAA,GAASjB,WAAA,CAAYiB,MAAA;IAI9C,MAAMf,aAAA,GAAgB,KAAKA,aAAA;IAE3BA,aAAA,CAAcgB,CAAA,GAAIjB,gBAAA,CAAiBiB,CAAA,GAAIJ,UAAA,EACvCZ,aAAA,CAAciB,CAAA,GAAIlB,gBAAA,CAAiBkB,CAAA,GAAIL,UAAA,EACvCZ,aAAA,CAAca,KAAA,GAAQd,gBAAA,CAAiBc,KAAA,GAAQD,UAAA,EAC/CZ,aAAA,CAAce,MAAA,GAAShB,gBAAA,CAAiBgB,MAAA,GAASH,UAAA,EAE5CH,aAAA,KAEDT,aAAA,CAAciB,CAAA,GAAItB,QAAA,CAASyB,IAAA,CAAKL,MAAA,IAAUf,aAAA,CAAciB,CAAA,GAAIjB,aAAA,CAAce,MAAA,IAG9Ef,aAAA,CAAcqB,IAAA,IAEd,KAAK1B,QAAA,CAASgB,WAAA,CAAYH,IAAA,CAAKG,WAAA,EAAaX,aAAa,GACzD,KAAKL,QAAA,CAAS2B,UAAA,CAAWC,MAAA,CAAOxB,gBAAA,EAAkBD,WAAA,EAAac,UAAA,EAAY,CAACD,WAAW,GAEnFF,aAAA,GAEA,KAAKd,QAAA,CAAS6B,IAAA,CAAKC,YAAA,CAAaf,WAAA,CAAYgB,SAAS,IAIrD,KAAK/B,QAAA,CAAS6B,IAAA,CAAKC,YAAA,CAAa,KAAK7B,gBAAgB,GAGzD,KAAKE,WAAA,CAAY6B,QAAA,CAAS7B,WAAW,GACrC,KAAKC,gBAAA,CAAiB4B,QAAA,CAAS5B,gBAAgB;EACnD;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQA6B,MAAMC,UAAA,EAA0BL,IAAA,EAChC;IACI,MAAMM,aAAA,GAAgB,KAAKjC,OAAA,GACrB,KAAKA,OAAA,CAAQa,WAAA,CAAYkB,KAAA,GACzB,KAAKjC,QAAA,CAASoC,UAAA,CAAWC,eAAA;MACzBC,KAAA,GAAQC,KAAA,CAAMC,MAAA,CAAOC,QAAA,CAASP,UAAA,IAA0BC,aAAa;IAE3E,CAAK,KAAKjC,OAAA,IAAW,KAAKA,OAAA,CAAQa,WAAA,CAAY2B,SAAA,GAAY,KAClD,CAAC,KAAKxC,OAAA,IAAW,KAAKQ,2BAAA,KAE1B4B,KAAA,CAAMK,WAAA,CAAYL,KAAA,CAAM3B,KAAK;IAG3B,MAAAP,gBAAA,GAAmB,KAAKA,gBAAA;MACxBwC,SAAA,GAAmB,KAAK1C,OAAA,GAAU,KAAKA,OAAA,CAAQa,WAAA,GAAc,KAAKf,QAAA,CAASuB,KAAA,CAAMC,MAAA;MACjFqB,SAAA,GAAYzC,gBAAA,CAAiBc,KAAA,KAAU0B,SAAA,CAAU1B,KAAA,IAASd,gBAAA,CAAiBgB,MAAA,KAAWwB,SAAA,CAAUxB,MAAA;IAEtG,IAAIyB,SAAA,EACJ;MACI,IAAI;QAAExB,CAAA;QAAGC,CAAA;QAAGJ,KAAA;QAAOE;MAAA,IAAW,KAAKf,aAAA;MAEnCgB,CAAA,GAAIyB,IAAA,CAAKC,KAAA,CAAM1B,CAAC,GAChBC,CAAA,GAAIwB,IAAA,CAAKC,KAAA,CAAMzB,CAAC,GAChBJ,KAAA,GAAQ4B,IAAA,CAAKC,KAAA,CAAM7B,KAAK,GACxBE,MAAA,GAAS0B,IAAA,CAAKC,KAAA,CAAM3B,MAAM,GAG1B,KAAKpB,QAAA,CAASQ,EAAA,CAAGwC,MAAA,CAAO,KAAKhD,QAAA,CAASQ,EAAA,CAAGyC,YAAY,GACrD,KAAKjD,QAAA,CAASQ,EAAA,CAAG0C,OAAA,CAAQ7B,CAAA,EAAGC,CAAA,EAAGJ,KAAA,EAAOE,MAAM;IAChD;IAEA,KAAKpB,QAAA,CAASgB,WAAA,CAAYiB,KAAA,CAAMK,KAAA,CAAMa,GAAA,EAAKb,KAAA,CAAMc,KAAA,EAAOd,KAAA,CAAMe,IAAA,EAAMf,KAAA,CAAM3B,KAAA,EAAOkB,IAAI,GAEjFgB,SAAA,IAGA,KAAK7C,QAAA,CAASkD,OAAA,CAAQI,GAAA;EAE9B;EAEAC,OAAA,EACA;IAEI,KAAK1C,IAAA,CAAK,IAAI;EAClB;EAAA;EAGA2C,MAAA,EACA;IACI,KAAK3C,IAAA,CAAK,IAAI;EAClB;EAEA4C,QAAA,EACA;IACI,KAAKzD,QAAA,GAAW;EACpB;AACJ;AAhOaF,mBAAA,CAGF4D,SAAA,GAA+B;EAClCC,IAAA,EAAMC,aAAA,CAAcC,cAAA;EACpBC,IAAA,EAAM;AACV;AA4NJC,UAAA,CAAWC,GAAA,CAAIlE,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}