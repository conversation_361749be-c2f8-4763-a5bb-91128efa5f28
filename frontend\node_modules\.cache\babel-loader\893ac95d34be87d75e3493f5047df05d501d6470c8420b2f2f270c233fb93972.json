{"ast": null, "code": "import { ExtensionType, extensions } from \"@pixi/core\";\nimport { testVideoFormat } from \"../utils/testVideoFormat.mjs\";\nconst detectWebm = {\n  extension: {\n    type: ExtensionType.DetectionParser,\n    priority: 0\n  },\n  test: async () => testVideoFormat(\"video/webm\"),\n  add: async formats => [...formats, \"webm\"],\n  remove: async formats => formats.filter(f => f !== \"webm\")\n};\nextensions.add(detectWebm);\nexport { detectWebm };", "map": {"version": 3, "names": ["detectWebm", "extension", "type", "ExtensionType", "DetectionParser", "priority", "test", "testVideoFormat", "add", "formats", "remove", "filter", "f", "extensions"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\assets\\src\\detections\\parsers\\detectWebm.ts"], "sourcesContent": ["import { extensions, ExtensionType } from '@pixi/core';\nimport { testVideoFormat } from '../utils/testVideoFormat';\n\nimport type { FormatDetectionParser } from '..';\n\nexport const detectWebm = {\n    extension: {\n        type: ExtensionType.DetectionParser,\n        priority: 0,\n    },\n    test: async (): Promise<boolean> => testVideoFormat('video/webm'),\n    add: async (formats) => [...formats, 'webm'],\n    remove: async (formats) => formats.filter((f) => f !== 'webm'),\n} as FormatDetectionParser;\n\nextensions.add(detectWebm);\n"], "mappings": ";;AAKO,MAAMA,UAAA,GAAa;EACtBC,SAAA,EAAW;IACPC,IAAA,EAAMC,aAAA,CAAcC,eAAA;IACpBC,QAAA,EAAU;EACd;EACAC,IAAA,EAAM,MAAAA,CAAA,KAA8BC,eAAA,CAAgB,YAAY;EAChEC,GAAA,EAAK,MAAOC,OAAA,IAAY,CAAC,GAAGA,OAAA,EAAS,MAAM;EAC3CC,MAAA,EAAQ,MAAOD,OAAA,IAAYA,OAAA,CAAQE,MAAA,CAAQC,CAAA,IAAMA,CAAA,KAAM,MAAM;AACjE;AAEAC,UAAA,CAAWL,GAAA,CAAIR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}