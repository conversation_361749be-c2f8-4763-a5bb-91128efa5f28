{"ast": null, "code": "import { TEXT_GRADIENT } from \"@pixi/text\";\nfunction generateFillStyle(canvas, context, style, resolution, lines, metrics) {\n  const fillStyle = style.fill;\n  if (Array.isArray(fillStyle)) {\n    if (fillStyle.length === 1) return fillStyle[0];\n  } else return fillStyle;\n  let gradient;\n  const dropShadowCorrection = style.dropShadow ? style.dropShadowDistance : 0,\n    padding = style.padding || 0,\n    width = canvas.width / resolution - dropShadowCorrection - padding * 2,\n    height = canvas.height / resolution - dropShadowCorrection - padding * 2,\n    fill = fillStyle.slice(),\n    fillGradientStops = style.fillGradientStops.slice();\n  if (!fillGradientStops.length) {\n    const lengthPlus1 = fill.length + 1;\n    for (let i = 1; i < lengthPlus1; ++i) fillGradientStops.push(i / lengthPlus1);\n  }\n  if (fill.unshift(fillStyle[0]), fillGradientStops.unshift(0), fill.push(fillStyle[fillStyle.length - 1]), fillGradientStops.push(1), style.fillGradientType === TEXT_GRADIENT.LINEAR_VERTICAL) {\n    gradient = context.createLinearGradient(width / 2, padding, width / 2, height + padding);\n    let lastIterationStop = 0;\n    const gradStopLineHeight = (metrics.fontProperties.fontSize + style.strokeThickness) / height;\n    for (let i = 0; i < lines.length; i++) {\n      const thisLineTop = metrics.lineHeight * i;\n      for (let j = 0; j < fill.length; j++) {\n        let lineStop = 0;\n        typeof fillGradientStops[j] == \"number\" ? lineStop = fillGradientStops[j] : lineStop = j / fill.length;\n        const globalStop = thisLineTop / height + lineStop * gradStopLineHeight;\n        let clampedStop = Math.max(lastIterationStop, globalStop);\n        clampedStop = Math.min(clampedStop, 1), gradient.addColorStop(clampedStop, fill[j]), lastIterationStop = clampedStop;\n      }\n    }\n  } else {\n    gradient = context.createLinearGradient(padding, height / 2, width + padding, height / 2);\n    const totalIterations = fill.length + 1;\n    let currentIteration = 1;\n    for (let i = 0; i < fill.length; i++) {\n      let stop;\n      typeof fillGradientStops[i] == \"number\" ? stop = fillGradientStops[i] : stop = currentIteration / totalIterations, gradient.addColorStop(stop, fill[i]), currentIteration++;\n    }\n  }\n  return gradient;\n}\nexport { generateFillStyle };", "map": {"version": 3, "names": ["generateFillStyle", "canvas", "context", "style", "resolution", "lines", "metrics", "fillStyle", "fill", "Array", "isArray", "length", "gradient", "dropShadowCorrection", "dropShadow", "dropShadowDistance", "padding", "width", "height", "slice", "fillGradientStops", "lengthPlus1", "i", "push", "unshift", "fillGradientType", "TEXT_GRADIENT", "LINEAR_VERTICAL", "createLinearGradient", "lastIterationStop", "gradStopLineHeight", "fontProperties", "fontSize", "strokeThickness", "thisLineTop", "lineHeight", "j", "lineStop", "globalStop", "clampedStop", "Math", "max", "min", "addColorStop", "totalIterations", "currentIteration", "stop"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\text-bitmap\\src\\utils\\generateFillStyle.ts"], "sourcesContent": ["import { TEXT_GRADIENT } from '@pixi/text';\n\nimport type { ICanvas, ICanvasRenderingContext2D } from '@pixi/core';\nimport type { TextMetrics, TextStyle } from '@pixi/text';\n\n// TODO: Prevent code duplication b/w generateFillStyle & Text#generateFillStyle\n\n/**\n * Generates the fill style. Can automatically generate a gradient based on the fill style being an array\n * @private\n * @param canvas\n * @param context\n * @param {object} style - The style.\n * @param resolution\n * @param {string[]} lines - The lines of text.\n * @param metrics\n * @returns {string|number|CanvasGradient} The fill style\n */\nexport function generateFillStyle(\n    canvas: ICanvas,\n    context: ICanvasRenderingContext2D,\n    style: TextStyle,\n    resolution: number,\n    lines: string[],\n    metrics: TextMetrics\n): string | CanvasGradient | CanvasPattern\n{\n    // TODO: Can't have different types for getter and setter. The getter shouldn't have the number type as\n    //       the setter converts to string. See this thread for more details:\n    //       https://github.com/microsoft/TypeScript/issues/2521\n    const fillStyle: string | string[] | CanvasGradient | CanvasPattern = style.fill as any;\n\n    if (!Array.isArray(fillStyle))\n    {\n        return fillStyle;\n    }\n    else if (fillStyle.length === 1)\n    {\n        return fillStyle[0];\n    }\n\n    // the gradient will be evenly spaced out according to how large the array is.\n    // ['#FF0000', '#00FF00', '#0000FF'] would created stops at 0.25, 0.5 and 0.75\n    let gradient: string[] | CanvasGradient;\n\n    // a dropshadow will enlarge the canvas and result in the gradient being\n    // generated with the incorrect dimensions\n    const dropShadowCorrection = (style.dropShadow) ? style.dropShadowDistance : 0;\n\n    // should also take padding into account, padding can offset the gradient\n    const padding = style.padding || 0;\n\n    const width = (canvas.width / resolution) - dropShadowCorrection - (padding * 2);\n    const height = (canvas.height / resolution) - dropShadowCorrection - (padding * 2);\n\n    // make a copy of the style settings, so we can manipulate them later\n    const fill = fillStyle.slice();\n    const fillGradientStops = style.fillGradientStops.slice();\n\n    // wanting to evenly distribute the fills. So an array of 4 colours should give fills of 0.25, 0.5 and 0.75\n    if (!fillGradientStops.length)\n    {\n        const lengthPlus1 = fill.length + 1;\n\n        for (let i = 1; i < lengthPlus1; ++i)\n        {\n            fillGradientStops.push(i / lengthPlus1);\n        }\n    }\n\n    // stop the bleeding of the last gradient on the line above to the top gradient of the this line\n    // by hard defining the first gradient colour at point 0, and last gradient colour at point 1\n    fill.unshift(fillStyle[0]);\n    fillGradientStops.unshift(0);\n\n    fill.push(fillStyle[fillStyle.length - 1]);\n    fillGradientStops.push(1);\n\n    if (style.fillGradientType === TEXT_GRADIENT.LINEAR_VERTICAL)\n    {\n        // start the gradient at the top center of the canvas, and end at the bottom middle of the canvas\n        gradient = context.createLinearGradient(width / 2, padding, width / 2, height + padding);\n\n        // we need to repeat the gradient so that each individual line of text has the same vertical gradient effect\n        // ['#FF0000', '#00FF00', '#0000FF'] over 2 lines would create stops at 0.125, 0.25, 0.375, 0.625, 0.75, 0.875\n\n        // There's potential for floating point precision issues at the seams between gradient repeats.\n        // The loop below generates the stops in order, so track the last generated one to prevent\n        // floating point precision from making us go the teeniest bit backwards, resulting in\n        // the first and last colors getting swapped.\n        let lastIterationStop = 0;\n\n        // Actual height of the text itself, not counting spacing for lineHeight/leading/dropShadow etc\n        const textHeight = metrics.fontProperties.fontSize + style.strokeThickness;\n\n        // textHeight, but as a 0-1 size in global gradient stop space\n        const gradStopLineHeight = textHeight / height;\n\n        for (let i = 0; i < lines.length; i++)\n        {\n            const thisLineTop = metrics.lineHeight * i;\n\n            for (let j = 0; j < fill.length; j++)\n            {\n                // 0-1 stop point for the current line, multiplied to global space afterwards\n                let lineStop = 0;\n\n                if (typeof fillGradientStops[j] === 'number')\n                {\n                    lineStop = fillGradientStops[j];\n                }\n                else\n                {\n                    lineStop = j / fill.length;\n                }\n\n                const globalStop = (thisLineTop / height) + (lineStop * gradStopLineHeight);\n\n                // Prevent color stop generation going backwards from floating point imprecision\n                let clampedStop = Math.max(lastIterationStop, globalStop);\n\n                clampedStop = Math.min(clampedStop, 1); // Cap at 1 as well for safety's sake to avoid a possible throw.\n                gradient.addColorStop(clampedStop, fill[j]);\n                lastIterationStop = clampedStop;\n            }\n        }\n    }\n    else\n    {\n        // start the gradient at the center left of the canvas, and end at the center right of the canvas\n        gradient = context.createLinearGradient(padding, height / 2, width + padding, height / 2);\n\n        // can just evenly space out the gradients in this case, as multiple lines makes no difference\n        // to an even left to right gradient\n        const totalIterations = fill.length + 1;\n        let currentIteration = 1;\n\n        for (let i = 0; i < fill.length; i++)\n        {\n            let stop: number;\n\n            if (typeof fillGradientStops[i] === 'number')\n            {\n                stop = fillGradientStops[i];\n            }\n            else\n            {\n                stop = currentIteration / totalIterations;\n            }\n            gradient.addColorStop(stop, fill[i]);\n            currentIteration++;\n        }\n    }\n\n    return gradient;\n}\n"], "mappings": ";AAkBO,SAASA,kBACZC,MAAA,EACAC,OAAA,EACAC,KAAA,EACAC,UAAA,EACAC,KAAA,EACAC,OAAA,EAEJ;EAII,MAAMC,SAAA,GAAgEJ,KAAA,CAAMK,IAAA;EAEvE,IAAAC,KAAA,CAAMC,OAAA,CAAQH,SAAS;IAIvB,IAAIA,SAAA,CAAUI,MAAA,KAAW,GAE1B,OAAOJ,SAAA,CAAU,CAAC;EAAA,OAJX,OAAAA,SAAA;EASP,IAAAK,QAAA;EAIJ,MAAMC,oBAAA,GAAwBV,KAAA,CAAMW,UAAA,GAAcX,KAAA,CAAMY,kBAAA,GAAqB;IAGvEC,OAAA,GAAUb,KAAA,CAAMa,OAAA,IAAW;IAE3BC,KAAA,GAAShB,MAAA,CAAOgB,KAAA,GAAQb,UAAA,GAAcS,oBAAA,GAAwBG,OAAA,GAAU;IACxEE,MAAA,GAAUjB,MAAA,CAAOiB,MAAA,GAASd,UAAA,GAAcS,oBAAA,GAAwBG,OAAA,GAAU;IAG1ER,IAAA,GAAOD,SAAA,CAAUY,KAAA,CAAM;IACvBC,iBAAA,GAAoBjB,KAAA,CAAMiB,iBAAA,CAAkBD,KAAA;EAG9C,KAACC,iBAAA,CAAkBT,MAAA,EACvB;IACU,MAAAU,WAAA,GAAcb,IAAA,CAAKG,MAAA,GAAS;IAElC,SAASW,CAAA,GAAI,GAAGA,CAAA,GAAID,WAAA,EAAa,EAAEC,CAAA,EAEbF,iBAAA,CAAAG,IAAA,CAAKD,CAAA,GAAID,WAAW;EAE9C;EAIA,IAAAb,IAAA,CAAKgB,OAAA,CAAQjB,SAAA,CAAU,CAAC,CAAC,GACzBa,iBAAA,CAAkBI,OAAA,CAAQ,CAAC,GAE3BhB,IAAA,CAAKe,IAAA,CAAKhB,SAAA,CAAUA,SAAA,CAAUI,MAAA,GAAS,CAAC,CAAC,GACzCS,iBAAA,CAAkBG,IAAA,CAAK,CAAC,GAEpBpB,KAAA,CAAMsB,gBAAA,KAAqBC,aAAA,CAAcC,eAAA,EAC7C;IAEef,QAAA,GAAAV,OAAA,CAAQ0B,oBAAA,CAAqBX,KAAA,GAAQ,GAAGD,OAAA,EAASC,KAAA,GAAQ,GAAGC,MAAA,GAASF,OAAO;IASvF,IAAIa,iBAAA,GAAoB;IAMxB,MAAMC,kBAAA,IAHaxB,OAAA,CAAQyB,cAAA,CAAeC,QAAA,GAAW7B,KAAA,CAAM8B,eAAA,IAGnBf,MAAA;IAExC,SAASI,CAAA,GAAI,GAAGA,CAAA,GAAIjB,KAAA,CAAMM,MAAA,EAAQW,CAAA,IAClC;MACU,MAAAY,WAAA,GAAc5B,OAAA,CAAQ6B,UAAA,GAAab,CAAA;MAEzC,SAASc,CAAA,GAAI,GAAGA,CAAA,GAAI5B,IAAA,CAAKG,MAAA,EAAQyB,CAAA,IACjC;QAEI,IAAIC,QAAA,GAAW;QAEX,OAAOjB,iBAAA,CAAkBgB,CAAC,KAAM,WAEhCC,QAAA,GAAWjB,iBAAA,CAAkBgB,CAAC,IAI9BC,QAAA,GAAWD,CAAA,GAAI5B,IAAA,CAAKG,MAAA;QAGlB,MAAA2B,UAAA,GAAcJ,WAAA,GAAchB,MAAA,GAAWmB,QAAA,GAAWP,kBAAA;QAGxD,IAAIS,WAAA,GAAcC,IAAA,CAAKC,GAAA,CAAIZ,iBAAA,EAAmBS,UAAU;QAExDC,WAAA,GAAcC,IAAA,CAAKE,GAAA,CAAIH,WAAA,EAAa,CAAC,GACrC3B,QAAA,CAAS+B,YAAA,CAAaJ,WAAA,EAAa/B,IAAA,CAAK4B,CAAC,CAAC,GAC1CP,iBAAA,GAAoBU,WAAA;MACxB;IACJ;EAAA,OAGJ;IAEe3B,QAAA,GAAAV,OAAA,CAAQ0B,oBAAA,CAAqBZ,OAAA,EAASE,MAAA,GAAS,GAAGD,KAAA,GAAQD,OAAA,EAASE,MAAA,GAAS,CAAC;IAIlF,MAAA0B,eAAA,GAAkBpC,IAAA,CAAKG,MAAA,GAAS;IACtC,IAAIkC,gBAAA,GAAmB;IAEvB,SAASvB,CAAA,GAAI,GAAGA,CAAA,GAAId,IAAA,CAAKG,MAAA,EAAQW,CAAA,IACjC;MACQ,IAAAwB,IAAA;MAEA,OAAO1B,iBAAA,CAAkBE,CAAC,KAAM,WAEhCwB,IAAA,GAAO1B,iBAAA,CAAkBE,CAAC,IAI1BwB,IAAA,GAAOD,gBAAA,GAAmBD,eAAA,EAE9BhC,QAAA,CAAS+B,YAAA,CAAaG,IAAA,EAAMtC,IAAA,CAAKc,CAAC,CAAC,GACnCuB,gBAAA;IACJ;EACJ;EAEO,OAAAjC,QAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}