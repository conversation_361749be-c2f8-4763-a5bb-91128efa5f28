using UnityEngine;
using UnityEngine.Events;

public class GameManager : MonoBehaviour
{
    public static GameManager Instance { get; private set; }

    [Header("Game State")]
    public enum GameState { MainMenu, Playing, Paused, GameOver }
    public GameState CurrentState { get; private set; }

    [Header("System References")]
    public MapManager mapManager;
    public PopSystem popSystem;
    public EconomySystem economySystem;
    public DiplomacySystem diplomacySystem;
    public EventSystem eventSystem;

    [<PERSON><PERSON>("Game Settings")]
    public int currentTurn = 1;
    public float turnDuration = 30f; // seconds per turn in real-time mode

    [Header("Events")]
    public UnityEvent<Province> OnProvinceSelected;
    public UnityEvent<int> OnTurnChanged;
    public UnityEvent<GameState> OnGameStateChanged;

    private Province selectedProvince;
    private bool isRealTimeMode = false;
    private float turnTimer = 0f;

    void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    void Start()
    {
        CurrentState = GameState.MainMenu;
        InitializeSystems();
    }

    void Update()
    {
        if (CurrentState == GameState.Playing && isRealTimeMode)
        {
            turnTimer += Time.deltaTime;
            if (turnTimer >= turnDuration)
            {
                AdvanceTurn();
                turnTimer = 0f;
            }
        }
    }

    void InitializeSystems()
    {
        // Find system references if not assigned
        if (mapManager == null) mapManager = FindObjectOfType<MapManager>();
        if (popSystem == null) popSystem = FindObjectOfType<PopSystem>();
        if (economySystem == null) economySystem = FindObjectOfType<EconomySystem>();
        if (diplomacySystem == null) diplomacySystem = FindObjectOfType<DiplomacySystem>();
        if (eventSystem == null) eventSystem = FindObjectOfType<EventSystem>();

        Debug.Log("GameManager systems initialized.");
    }

    public void StartGame()
    {
        CurrentState = GameState.Playing;
        OnGameStateChanged?.Invoke(CurrentState);
        Debug.Log("Game started!");
    }

    public void PauseGame()
    {
        CurrentState = GameState.Paused;
        OnGameStateChanged?.Invoke(CurrentState);
        Debug.Log("Game paused.");
    }

    public void ResumeGame()
    {
        CurrentState = GameState.Playing;
        OnGameStateChanged?.Invoke(CurrentState);
        Debug.Log("Game resumed.");
    }

    public void EndGame()
    {
        CurrentState = GameState.GameOver;
        OnGameStateChanged?.Invoke(CurrentState);
        Debug.Log("Game ended.");
    }

    public void AdvanceTurn()
    {
        if (CurrentState != GameState.Playing) return;

        currentTurn++;
        OnTurnChanged?.Invoke(currentTurn);

        // Update all systems
        if (popSystem != null) popSystem.UpdateSystem();
        if (economySystem != null) economySystem.UpdateSystem();
        if (diplomacySystem != null) diplomacySystem.UpdateSystem();
        if (eventSystem != null) eventSystem.UpdateSystem();

        Debug.Log($"Advanced to turn {currentTurn}");
    }

    public void OnProvinceClicked(Province province)
    {
        selectedProvince = province;
        OnProvinceSelected?.Invoke(province);
        Debug.Log($"Selected province: {province?.Name}");
    }

    public void SetRealTimeMode(bool enabled)
    {
        isRealTimeMode = enabled;
        turnTimer = 0f;
        Debug.Log($"Real-time mode: {(enabled ? "Enabled" : "Disabled")}");
    }

    public Province GetSelectedProvince()
    {
        return selectedProvince;
    }

    public int GetCurrentTurn()
    {
        return currentTurn;
    }
}
