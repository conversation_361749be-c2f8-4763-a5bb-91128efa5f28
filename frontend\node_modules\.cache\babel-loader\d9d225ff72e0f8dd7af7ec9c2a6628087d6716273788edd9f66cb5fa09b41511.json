{"ast": null, "code": "import { Point, Transform, TextureMatrix, Rectangle, Texture } from \"@pixi/core\";\nimport { Sprite } from \"@pixi/sprite\";\nconst tempPoint = new Point();\nclass TilingSprite extends Sprite {\n  /**\n   * Note: The wrap mode of the texture is forced to REPEAT on render if the size of the texture\n   * is a power of two, the texture's wrap mode is CLAMP, and the texture hasn't been bound yet.\n   * @param texture - The texture of the tiling sprite.\n   * @param width - The width of the tiling sprite.\n   * @param height - The height of the tiling sprite.\n   */\n  constructor(texture, width = 100, height = 100) {\n    super(texture), this.tileTransform = new Transform(), this._width = width, this._height = height, this.uvMatrix = this.texture.uvMatrix || new TextureMatrix(texture), this.pluginName = \"tilingSprite\", this.uvRespectAnchor = !1;\n  }\n  /**\n   * Changes frame clamping in corresponding textureTransform, shortcut\n   * Change to -0.5 to add a pixel to the edge, recommended for transparent trimmed textures in atlas\n   * @default 0.5\n   * @member {number}\n   */\n  get clampMargin() {\n    return this.uvMatrix.clampMargin;\n  }\n  set clampMargin(value) {\n    this.uvMatrix.clampMargin = value, this.uvMatrix.update(!0);\n  }\n  /** The scaling of the image that is being tiled. */\n  get tileScale() {\n    return this.tileTransform.scale;\n  }\n  set tileScale(value) {\n    this.tileTransform.scale.copyFrom(value);\n  }\n  /** The offset of the image that is being tiled. */\n  get tilePosition() {\n    return this.tileTransform.position;\n  }\n  set tilePosition(value) {\n    this.tileTransform.position.copyFrom(value);\n  }\n  /**\n   * @protected\n   */\n  _onTextureUpdate() {\n    this.uvMatrix && (this.uvMatrix.texture = this._texture), this._cachedTint = 16777215;\n  }\n  /**\n   * Renders the object using the WebGL renderer\n   * @param renderer - The renderer\n   */\n  _render(renderer) {\n    const texture = this._texture;\n    !texture || !texture.valid || (this.tileTransform.updateLocalTransform(), this.uvMatrix.update(), renderer.batch.setObjectRenderer(renderer.plugins[this.pluginName]), renderer.plugins[this.pluginName].render(this));\n  }\n  /** Updates the bounds of the tiling sprite. */\n  _calculateBounds() {\n    const minX = this._width * -this._anchor._x,\n      minY = this._height * -this._anchor._y,\n      maxX = this._width * (1 - this._anchor._x),\n      maxY = this._height * (1 - this._anchor._y);\n    this._bounds.addFrame(this.transform, minX, minY, maxX, maxY);\n  }\n  /**\n   * Gets the local bounds of the sprite object.\n   * @param rect - Optional output rectangle.\n   * @returns The bounds.\n   */\n  getLocalBounds(rect) {\n    return this.children.length === 0 ? (this._bounds.minX = this._width * -this._anchor._x, this._bounds.minY = this._height * -this._anchor._y, this._bounds.maxX = this._width * (1 - this._anchor._x), this._bounds.maxY = this._height * (1 - this._anchor._y), rect || (this._localBoundsRect || (this._localBoundsRect = new Rectangle()), rect = this._localBoundsRect), this._bounds.getRectangle(rect)) : super.getLocalBounds.call(this, rect);\n  }\n  /**\n   * Checks if a point is inside this tiling sprite.\n   * @param point - The point to check.\n   * @returns Whether or not the sprite contains the point.\n   */\n  containsPoint(point) {\n    this.worldTransform.applyInverse(point, tempPoint);\n    const width = this._width,\n      height = this._height,\n      x1 = -width * this.anchor._x;\n    if (tempPoint.x >= x1 && tempPoint.x < x1 + width) {\n      const y1 = -height * this.anchor._y;\n      if (tempPoint.y >= y1 && tempPoint.y < y1 + height) return !0;\n    }\n    return !1;\n  }\n  /**\n   * Destroys this sprite and optionally its texture and children\n   * @param {object|boolean} [options] - Options parameter. A boolean will act as if all options\n   *  have been set to that value\n   * @param {boolean} [options.children=false] - if set to true, all the children will have their destroy\n   *      method called as well. 'options' will be passed on to those calls.\n   * @param {boolean} [options.texture=false] - Should it destroy the current texture of the sprite as well\n   * @param {boolean} [options.baseTexture=false] - Should it destroy the base texture of the sprite as well\n   */\n  destroy(options) {\n    super.destroy(options), this.tileTransform = null, this.uvMatrix = null;\n  }\n  /**\n   * Helper function that creates a new tiling sprite based on the source you provide.\n   * The source can be - frame id, image url, video url, canvas element, video element, base texture\n   * @static\n   * @param {string|PIXI.Texture|HTMLCanvasElement|HTMLVideoElement} source - Source to create texture from\n   * @param {object} options - See {@link PIXI.BaseTexture}'s constructor for options.\n   * @param {number} options.width - required width of the tiling sprite\n   * @param {number} options.height - required height of the tiling sprite\n   * @returns {PIXI.TilingSprite} The newly created texture\n   */\n  static from(source, options) {\n    const texture = source instanceof Texture ? source : Texture.from(source, options);\n    return new TilingSprite(texture, options.width, options.height);\n  }\n  /** The width of the sprite, setting this will actually modify the scale to achieve the value set. */\n  get width() {\n    return this._width;\n  }\n  set width(value) {\n    this._width = value;\n  }\n  /** The height of the TilingSprite, setting this will actually modify the scale to achieve the value set. */\n  get height() {\n    return this._height;\n  }\n  set height(value) {\n    this._height = value;\n  }\n}\nexport { TilingSprite };", "map": {"version": 3, "names": ["tempPoint", "Point", "TilingSprite", "Sprite", "constructor", "texture", "width", "height", "tileTransform", "Transform", "_width", "_height", "uvMatrix", "TextureMatrix", "pluginName", "uvRespectAnchor", "clamp<PERSON><PERSON><PERSON>", "value", "update", "tileScale", "scale", "copyFrom", "tilePosition", "position", "_onTextureUpdate", "_texture", "_cachedTint", "_render", "renderer", "valid", "updateLocalTransform", "batch", "setObjectR<PERSON><PERSON>", "plugins", "render", "_calculateBounds", "minX", "_anchor", "_x", "minY", "_y", "maxX", "maxY", "_bounds", "addFrame", "transform", "getLocalBounds", "rect", "children", "length", "_localBoundsRect", "Rectangle", "getRectangle", "call", "containsPoint", "point", "worldTransform", "applyInverse", "x1", "anchor", "x", "y1", "y", "destroy", "options", "from", "source", "Texture"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\sprite-tiling\\src\\TilingSprite.ts"], "sourcesContent": ["import { Point, Rectangle, Texture, TextureMatrix, Transform } from '@pixi/core';\nimport { Sprite } from '@pixi/sprite';\n\nimport type { IBaseTextureOptions, IPoint, IPointData, ISize, ObservablePoint, Renderer, TextureSource } from '@pixi/core';\nimport type { IDestroyOptions } from '@pixi/display';\n\nconst tempPoint = new Point();\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface TilingSprite extends GlobalMixins.TilingSprite {}\n\n/**\n * A tiling sprite is a fast way of rendering a tiling image.\n * @memberof PIXI\n */\nexport class TilingSprite extends Sprite\n{\n    /** Tile transform */\n    public tileTransform: Transform;\n\n    /** Matrix that is applied to UV to get the coords in Texture normalized space to coords in BaseTexture space. */\n    public uvMatrix: TextureMatrix;\n\n    /**\n     * Flags whether the tiling pattern should originate from the origin instead of the top-left corner in\n     * local space.\n     *\n     * This will make the texture coordinates assigned to each vertex dependent on the value of the anchor. Without\n     * this, the top-left corner always gets the (0, 0) texture coordinate.\n     * @default false\n     */\n    public uvRespectAnchor: boolean;\n\n    /**\n     * Note: The wrap mode of the texture is forced to REPEAT on render if the size of the texture\n     * is a power of two, the texture's wrap mode is CLAMP, and the texture hasn't been bound yet.\n     * @param texture - The texture of the tiling sprite.\n     * @param width - The width of the tiling sprite.\n     * @param height - The height of the tiling sprite.\n     */\n    constructor(texture: Texture, width = 100, height = 100)\n    {\n        super(texture);\n\n        this.tileTransform = new Transform();\n\n        // The width of the tiling sprite\n        this._width = width;\n\n        // The height of the tiling sprite\n        this._height = height;\n\n        this.uvMatrix = this.texture.uvMatrix || new TextureMatrix(texture);\n\n        /**\n         * Plugin that is responsible for rendering this element.\n         * Allows to customize the rendering process without overriding '_render' method.\n         * @default 'tilingSprite'\n         */\n        this.pluginName = 'tilingSprite';\n\n        this.uvRespectAnchor = false;\n    }\n    /**\n     * Changes frame clamping in corresponding textureTransform, shortcut\n     * Change to -0.5 to add a pixel to the edge, recommended for transparent trimmed textures in atlas\n     * @default 0.5\n     * @member {number}\n     */\n    get clampMargin(): number\n    {\n        return this.uvMatrix.clampMargin;\n    }\n\n    set clampMargin(value: number)\n    {\n        this.uvMatrix.clampMargin = value;\n        this.uvMatrix.update(true);\n    }\n\n    /** The scaling of the image that is being tiled. */\n    get tileScale(): ObservablePoint\n    {\n        return this.tileTransform.scale;\n    }\n\n    set tileScale(value: IPointData)\n    {\n        this.tileTransform.scale.copyFrom(value as IPoint);\n    }\n\n    /** The offset of the image that is being tiled. */\n    get tilePosition(): ObservablePoint\n    {\n        return this.tileTransform.position;\n    }\n\n    set tilePosition(value: ObservablePoint)\n    {\n        this.tileTransform.position.copyFrom(value as IPoint);\n    }\n\n    /**\n     * @protected\n     */\n    protected _onTextureUpdate(): void\n    {\n        if (this.uvMatrix)\n        {\n            this.uvMatrix.texture = this._texture;\n        }\n        this._cachedTint = 0xFFFFFF;\n    }\n\n    /**\n     * Renders the object using the WebGL renderer\n     * @param renderer - The renderer\n     */\n    protected _render(renderer: Renderer): void\n    {\n        // tweak our texture temporarily..\n        const texture = this._texture;\n\n        if (!texture || !texture.valid)\n        {\n            return;\n        }\n\n        this.tileTransform.updateLocalTransform();\n        this.uvMatrix.update();\n\n        renderer.batch.setObjectRenderer(renderer.plugins[this.pluginName]);\n        renderer.plugins[this.pluginName].render(this);\n    }\n\n    /** Updates the bounds of the tiling sprite. */\n    protected _calculateBounds(): void\n    {\n        const minX = this._width * -this._anchor._x;\n        const minY = this._height * -this._anchor._y;\n        const maxX = this._width * (1 - this._anchor._x);\n        const maxY = this._height * (1 - this._anchor._y);\n\n        this._bounds.addFrame(this.transform, minX, minY, maxX, maxY);\n    }\n\n    /**\n     * Gets the local bounds of the sprite object.\n     * @param rect - Optional output rectangle.\n     * @returns The bounds.\n     */\n    public getLocalBounds(rect?: Rectangle): Rectangle\n    {\n        // we can do a fast local bounds if the sprite has no children!\n        if (this.children.length === 0)\n        {\n            this._bounds.minX = this._width * -this._anchor._x;\n            this._bounds.minY = this._height * -this._anchor._y;\n            this._bounds.maxX = this._width * (1 - this._anchor._x);\n            this._bounds.maxY = this._height * (1 - this._anchor._y);\n\n            if (!rect)\n            {\n                if (!this._localBoundsRect)\n                {\n                    this._localBoundsRect = new Rectangle();\n                }\n\n                rect = this._localBoundsRect;\n            }\n\n            return this._bounds.getRectangle(rect);\n        }\n\n        return super.getLocalBounds.call(this, rect);\n    }\n\n    /**\n     * Checks if a point is inside this tiling sprite.\n     * @param point - The point to check.\n     * @returns Whether or not the sprite contains the point.\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        this.worldTransform.applyInverse(point, tempPoint);\n\n        const width = this._width;\n        const height = this._height;\n        const x1 = -width * this.anchor._x;\n\n        if (tempPoint.x >= x1 && tempPoint.x < x1 + width)\n        {\n            const y1 = -height * this.anchor._y;\n\n            if (tempPoint.y >= y1 && tempPoint.y < y1 + height)\n            {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Destroys this sprite and optionally its texture and children\n     * @param {object|boolean} [options] - Options parameter. A boolean will act as if all options\n     *  have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have their destroy\n     *      method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Should it destroy the current texture of the sprite as well\n     * @param {boolean} [options.baseTexture=false] - Should it destroy the base texture of the sprite as well\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        super.destroy(options);\n\n        this.tileTransform = null;\n        this.uvMatrix = null;\n    }\n\n    /**\n     * Helper function that creates a new tiling sprite based on the source you provide.\n     * The source can be - frame id, image url, video url, canvas element, video element, base texture\n     * @static\n     * @param {string|PIXI.Texture|HTMLCanvasElement|HTMLVideoElement} source - Source to create texture from\n     * @param {object} options - See {@link PIXI.BaseTexture}'s constructor for options.\n     * @param {number} options.width - required width of the tiling sprite\n     * @param {number} options.height - required height of the tiling sprite\n     * @returns {PIXI.TilingSprite} The newly created texture\n     */\n    static from(source: TextureSource | Texture, options: ISize & IBaseTextureOptions): TilingSprite\n    {\n        const texture = (source instanceof Texture)\n            ? source\n            : Texture.from(source, options);\n\n        return new TilingSprite(\n            texture,\n            options.width,\n            options.height\n        );\n    }\n\n    /** The width of the sprite, setting this will actually modify the scale to achieve the value set. */\n    get width(): number\n    {\n        return this._width;\n    }\n\n    set width(value: number)\n    {\n        this._width = value;\n    }\n\n    /** The height of the TilingSprite, setting this will actually modify the scale to achieve the value set. */\n    get height(): number\n    {\n        return this._height;\n    }\n\n    set height(value: number)\n    {\n        this._height = value;\n    }\n}\n"], "mappings": ";;AAMA,MAAMA,SAAA,GAAY,IAAIC,KAAA;AASf,MAAMC,YAAA,SAAqBC,MAAA,CAClC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAwBIC,YAAYC,OAAA,EAAkBC,KAAA,GAAQ,KAAKC,MAAA,GAAS,KACpD;IACI,MAAMF,OAAO,GAER,KAAAG,aAAA,GAAgB,IAAIC,SAAA,CAAU,GAGnC,KAAKC,MAAA,GAASJ,KAAA,EAGd,KAAKK,OAAA,GAAUJ,MAAA,EAEf,KAAKK,QAAA,GAAW,KAAKP,OAAA,CAAQO,QAAA,IAAY,IAAIC,aAAA,CAAcR,OAAO,GAOlE,KAAKS,UAAA,GAAa,gBAElB,KAAKC,eAAA,GAAkB;EAC3B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOA,IAAIC,YAAA,EACJ;IACI,OAAO,KAAKJ,QAAA,CAASI,WAAA;EACzB;EAEA,IAAIA,YAAYC,KAAA,EAChB;IACI,KAAKL,QAAA,CAASI,WAAA,GAAcC,KAAA,EAC5B,KAAKL,QAAA,CAASM,MAAA,CAAO,EAAI;EAC7B;EAAA;EAGA,IAAIC,UAAA,EACJ;IACI,OAAO,KAAKX,aAAA,CAAcY,KAAA;EAC9B;EAEA,IAAID,UAAUF,KAAA,EACd;IACS,KAAAT,aAAA,CAAcY,KAAA,CAAMC,QAAA,CAASJ,KAAe;EACrD;EAAA;EAGA,IAAIK,aAAA,EACJ;IACI,OAAO,KAAKd,aAAA,CAAce,QAAA;EAC9B;EAEA,IAAID,aAAaL,KAAA,EACjB;IACS,KAAAT,aAAA,CAAce,QAAA,CAASF,QAAA,CAASJ,KAAe;EACxD;EAAA;AAAA;AAAA;EAKUO,iBAAA,EACV;IACQ,KAAKZ,QAAA,KAEL,KAAKA,QAAA,CAASP,OAAA,GAAU,KAAKoB,QAAA,GAEjC,KAAKC,WAAA,GAAc;EACvB;EAAA;AAAA;AAAA;AAAA;EAMUC,QAAQC,QAAA,EAClB;IAEI,MAAMvB,OAAA,GAAU,KAAKoB,QAAA;IAEjB,CAACpB,OAAA,IAAW,CAACA,OAAA,CAAQwB,KAAA,KAKzB,KAAKrB,aAAA,CAAcsB,oBAAA,CACnB,QAAKlB,QAAA,CAASM,MAAA,CAEd,GAAAU,QAAA,CAASG,KAAA,CAAMC,iBAAA,CAAkBJ,QAAA,CAASK,OAAA,CAAQ,KAAKnB,UAAU,CAAC,GAClEc,QAAA,CAASK,OAAA,CAAQ,KAAKnB,UAAU,EAAEoB,MAAA,CAAO,IAAI;EACjD;EAAA;EAGUC,iBAAA,EACV;IACU,MAAAC,IAAA,GAAO,KAAK1B,MAAA,GAAS,CAAC,KAAK2B,OAAA,CAAQC,EAAA;MACnCC,IAAA,GAAO,KAAK5B,OAAA,GAAU,CAAC,KAAK0B,OAAA,CAAQG,EAAA;MACpCC,IAAA,GAAO,KAAK/B,MAAA,IAAU,IAAI,KAAK2B,OAAA,CAAQC,EAAA;MACvCI,IAAA,GAAO,KAAK/B,OAAA,IAAW,IAAI,KAAK0B,OAAA,CAAQG,EAAA;IAE9C,KAAKG,OAAA,CAAQC,QAAA,CAAS,KAAKC,SAAA,EAAWT,IAAA,EAAMG,IAAA,EAAME,IAAA,EAAMC,IAAI;EAChE;EAAA;AAAA;AAAA;AAAA;AAAA;EAOOI,eAAeC,IAAA,EACtB;IAEI,OAAI,KAAKC,QAAA,CAASC,MAAA,KAAW,KAEzB,KAAKN,OAAA,CAAQP,IAAA,GAAO,KAAK1B,MAAA,GAAS,CAAC,KAAK2B,OAAA,CAAQC,EAAA,EAChD,KAAKK,OAAA,CAAQJ,IAAA,GAAO,KAAK5B,OAAA,GAAU,CAAC,KAAK0B,OAAA,CAAQG,EAAA,EACjD,KAAKG,OAAA,CAAQF,IAAA,GAAO,KAAK/B,MAAA,IAAU,IAAI,KAAK2B,OAAA,CAAQC,EAAA,GACpD,KAAKK,OAAA,CAAQD,IAAA,GAAO,KAAK/B,OAAA,IAAW,IAAI,KAAK0B,OAAA,CAAQG,EAAA,GAEhDO,IAAA,KAEI,KAAKG,gBAAA,KAEN,KAAKA,gBAAA,GAAmB,IAAIC,SAAA,CAAU,IAG1CJ,IAAA,GAAO,KAAKG,gBAAA,GAGT,KAAKP,OAAA,CAAQS,YAAA,CAAaL,IAAI,KAGlC,MAAMD,cAAA,CAAeO,IAAA,CAAK,MAAMN,IAAI;EAC/C;EAAA;AAAA;AAAA;AAAA;AAAA;EAOOO,cAAcC,KAAA,EACrB;IACS,KAAAC,cAAA,CAAeC,YAAA,CAAaF,KAAA,EAAOvD,SAAS;IAE3C,MAAAM,KAAA,GAAQ,KAAKI,MAAA;MACbH,MAAA,GAAS,KAAKI,OAAA;MACd+C,EAAA,GAAK,CAACpD,KAAA,GAAQ,KAAKqD,MAAA,CAAOrB,EAAA;IAEhC,IAAItC,SAAA,CAAU4D,CAAA,IAAKF,EAAA,IAAM1D,SAAA,CAAU4D,CAAA,GAAIF,EAAA,GAAKpD,KAAA,EAC5C;MACI,MAAMuD,EAAA,GAAK,CAACtD,MAAA,GAAS,KAAKoD,MAAA,CAAOnB,EAAA;MAEjC,IAAIxC,SAAA,CAAU8D,CAAA,IAAKD,EAAA,IAAM7D,SAAA,CAAU8D,CAAA,GAAID,EAAA,GAAKtD,MAAA,EAEjC;IAEf;IAEO;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWOwD,QAAQC,OAAA,EACf;IACI,MAAMD,OAAA,CAAQC,OAAO,GAErB,KAAKxD,aAAA,GAAgB,MACrB,KAAKI,QAAA,GAAW;EACpB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYA,OAAOqD,KAAKC,MAAA,EAAiCF,OAAA,EAC7C;IACI,MAAM3D,OAAA,GAAW6D,MAAA,YAAkBC,OAAA,GAC7BD,MAAA,GACAC,OAAA,CAAQF,IAAA,CAAKC,MAAA,EAAQF,OAAO;IAElC,OAAO,IAAI9D,YAAA,CACPG,OAAA,EACA2D,OAAA,CAAQ1D,KAAA,EACR0D,OAAA,CAAQzD,MAAA;EAEhB;EAAA;EAGA,IAAID,MAAA,EACJ;IACI,OAAO,KAAKI,MAAA;EAChB;EAEA,IAAIJ,MAAMW,KAAA,EACV;IACI,KAAKP,MAAA,GAASO,KAAA;EAClB;EAAA;EAGA,IAAIV,OAAA,EACJ;IACI,OAAO,KAAKI,OAAA;EAChB;EAEA,IAAIJ,OAAOU,KAAA,EACX;IACI,KAAKN,OAAA,GAAUM,KAAA;EACnB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}