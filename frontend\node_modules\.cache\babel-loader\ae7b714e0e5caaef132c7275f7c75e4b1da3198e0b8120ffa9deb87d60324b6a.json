{"ast": null, "code": "const WORKER_CODE = `(function() {\n  \"use strict\";\n  const WHITE_PNG = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAwMCAO+ip1sAAAAASUVORK5CYII=\";\n  async function checkImageBitmap() {\n    try {\n      if (typeof createImageBitmap != \"function\")\n        return !1;\n      const imageBlob = await (await fetch(WHITE_PNG)).blob(), imageBitmap = await createImageBitmap(imageBlob);\n      return imageBitmap.width === 1 && imageBitmap.height === 1;\n    } catch {\n      return !1;\n    }\n  }\n  checkImageBitmap().then((result) => {\n    self.postMessage(result);\n  });\n})();\n`;\nlet WORKER_URL = null;\nclass WorkerInstance {\n  constructor() {\n    WORKER_URL || (WORKER_URL = URL.createObjectURL(new Blob([WORKER_CODE], {\n      type: \"application/javascript\"\n    }))), this.worker = new Worker(WORKER_URL);\n  }\n}\nWorkerInstance.revokeObjectURL = function () {\n  WORKER_URL && (URL.revokeObjectURL(WORKER_URL), WORKER_URL = null);\n};\nexport { WorkerInstance as default };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["const WORKER_CODE = `(function() {\n  \"use strict\";\n  const WHITE_PNG = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAwMCAO+ip1sAAAAASUVORK5CYII=\";\n  async function checkImageBitmap() {\n    try {\n      if (typeof createImageBitmap != \"function\")\n        return !1;\n      const imageBlob = await (await fetch(WHITE_PNG)).blob(), imageBitmap = await createImageBitmap(imageBlob);\n      return imageBitmap.width === 1 && imageBitmap.height === 1;\n    } catch {\n      return !1;\n    }\n  }\n  checkImageBitmap().then((result) => {\n    self.postMessage(result);\n  });\n})();\n`;\nlet WORKER_URL = null;\nclass WorkerInstance {\n  constructor() {\n    WORKER_URL || (WORKER_URL = URL.createObjectURL(new Blob([WORKER_CODE], { type: \"application/javascript\" }))), this.worker = new Worker(WORKER_URL);\n  }\n}\nWorkerInstance.revokeObjectURL = function() {\n  WORKER_URL && (URL.revokeObjectURL(WORKER_URL), WORKER_URL = null);\n};\nexport {\n  WorkerInstance as default\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}