{"ast": null, "code": "import { Point, BatchGeometry, WRAP_MODES, BaseTexture, BatchDrawCall, BatchTextureArray, DRAW_MODES, Color } from \"@pixi/core\";\nimport { Bounds } from \"@pixi/display\";\nimport { GraphicsData } from \"./GraphicsData.mjs\";\nimport { DRAW_CALL_POOL, BATCH_POOL, FILL_COMMANDS } from \"./utils/index.mjs\";\nimport { BatchPart } from \"./utils/BatchPart.mjs\";\nimport { buildPoly } from \"./utils/buildPoly.mjs\";\nimport { buildLine } from \"./utils/buildLine.mjs\";\nconst tmpPoint = new Point(),\n  _GraphicsGeometry = class _GraphicsGeometry2 extends BatchGeometry {\n    // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n    constructor() {\n      super(), this.closePointEps = 1e-4, this.boundsPadding = 0, this.uvsFloat32 = null, this.indicesUint16 = null, this.batchable = !1, this.points = [], this.colors = [], this.uvs = [], this.indices = [], this.textureIds = [], this.graphicsData = [], this.drawCalls = [], this.batchDirty = -1, this.batches = [], this.dirty = 0, this.cacheDirty = -1, this.clearDirty = 0, this.shapeIndex = 0, this._bounds = new Bounds(), this.boundsDirty = -1;\n    }\n    /**\n     * Get the current bounds of the graphic geometry.\n     *\n     * Since 6.5.0, bounds of the graphics geometry are calculated based on the vertices of generated geometry.\n     * Since shapes or strokes with full transparency (`alpha: 0`) will not generate geometry, they are not considered\n     * when calculating bounds for the graphics geometry. See PR [#8343]{@link https://github.com/pixijs/pixijs/pull/8343}\n     * and issue [#8623]{@link https://github.com/pixijs/pixijs/pull/8623}.\n     * @readonly\n     */\n    get bounds() {\n      return this.updateBatches(), this.boundsDirty !== this.dirty && (this.boundsDirty = this.dirty, this.calculateBounds()), this._bounds;\n    }\n    /** Call if you changed graphicsData manually. Empties all batch buffers. */\n    invalidate() {\n      this.boundsDirty = -1, this.dirty++, this.batchDirty++, this.shapeIndex = 0, this.points.length = 0, this.colors.length = 0, this.uvs.length = 0, this.indices.length = 0, this.textureIds.length = 0;\n      for (let i = 0; i < this.drawCalls.length; i++) this.drawCalls[i].texArray.clear(), DRAW_CALL_POOL.push(this.drawCalls[i]);\n      this.drawCalls.length = 0;\n      for (let i = 0; i < this.batches.length; i++) {\n        const batchPart = this.batches[i];\n        batchPart.reset(), BATCH_POOL.push(batchPart);\n      }\n      this.batches.length = 0;\n    }\n    /**\n     * Clears the graphics that were drawn to this Graphics object, and resets fill and line style settings.\n     * @returns - This GraphicsGeometry object. Good for chaining method calls\n     */\n    clear() {\n      return this.graphicsData.length > 0 && (this.invalidate(), this.clearDirty++, this.graphicsData.length = 0), this;\n    }\n    /**\n     * Draws the given shape to this Graphics object. Can be any of Circle, Rectangle, Ellipse, Line or Polygon.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param fillStyle - Defines style of the fill.\n     * @param lineStyle - Defines style of the lines.\n     * @param matrix - Transform applied to the points of the shape.\n     * @returns - Returns geometry for chaining.\n     */\n    drawShape(shape, fillStyle = null, lineStyle = null, matrix = null) {\n      const data = new GraphicsData(shape, fillStyle, lineStyle, matrix);\n      return this.graphicsData.push(data), this.dirty++, this;\n    }\n    /**\n     * Draws the given shape to this Graphics object. Can be any of Circle, Rectangle, Ellipse, Line or Polygon.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param matrix - Transform applied to the points of the shape.\n     * @returns - Returns geometry for chaining.\n     */\n    drawHole(shape, matrix = null) {\n      if (!this.graphicsData.length) return null;\n      const data = new GraphicsData(shape, null, null, matrix),\n        lastShape = this.graphicsData[this.graphicsData.length - 1];\n      return data.lineStyle = lastShape.lineStyle, lastShape.holes.push(data), this.dirty++, this;\n    }\n    /** Destroys the GraphicsGeometry object. */\n    destroy() {\n      super.destroy();\n      for (let i = 0; i < this.graphicsData.length; ++i) this.graphicsData[i].destroy();\n      this.points.length = 0, this.points = null, this.colors.length = 0, this.colors = null, this.uvs.length = 0, this.uvs = null, this.indices.length = 0, this.indices = null, this.indexBuffer.destroy(), this.indexBuffer = null, this.graphicsData.length = 0, this.graphicsData = null, this.drawCalls.length = 0, this.drawCalls = null, this.batches.length = 0, this.batches = null, this._bounds = null;\n    }\n    /**\n     * Check to see if a point is contained within this geometry.\n     * @param point - Point to check if it's contained.\n     * @returns {boolean} `true` if the point is contained within geometry.\n     */\n    containsPoint(point) {\n      const graphicsData = this.graphicsData;\n      for (let i = 0; i < graphicsData.length; ++i) {\n        const data = graphicsData[i];\n        if (data.fillStyle.visible && data.shape && (data.matrix ? data.matrix.applyInverse(point, tmpPoint) : tmpPoint.copyFrom(point), data.shape.contains(tmpPoint.x, tmpPoint.y))) {\n          let hitHole = !1;\n          if (data.holes) {\n            for (let i2 = 0; i2 < data.holes.length; i2++) if (data.holes[i2].shape.contains(tmpPoint.x, tmpPoint.y)) {\n              hitHole = !0;\n              break;\n            }\n          }\n          if (!hitHole) return !0;\n        }\n      }\n      return !1;\n    }\n    /**\n     * Generates intermediate batch data. Either gets converted to drawCalls\n     * or used to convert to batch objects directly by the Graphics object.\n     */\n    updateBatches() {\n      if (!this.graphicsData.length) {\n        this.batchable = !0;\n        return;\n      }\n      if (!this.validateBatching()) return;\n      this.cacheDirty = this.dirty;\n      const uvs = this.uvs,\n        graphicsData = this.graphicsData;\n      let batchPart = null,\n        currentStyle = null;\n      this.batches.length > 0 && (batchPart = this.batches[this.batches.length - 1], currentStyle = batchPart.style);\n      for (let i = this.shapeIndex; i < graphicsData.length; i++) {\n        this.shapeIndex++;\n        const data = graphicsData[i],\n          fillStyle = data.fillStyle,\n          lineStyle = data.lineStyle;\n        FILL_COMMANDS[data.type].build(data), data.matrix && this.transformPoints(data.points, data.matrix), (fillStyle.visible || lineStyle.visible) && this.processHoles(data.holes);\n        for (let j = 0; j < 2; j++) {\n          const style = j === 0 ? fillStyle : lineStyle;\n          if (!style.visible) continue;\n          const nextTexture = style.texture.baseTexture,\n            index2 = this.indices.length,\n            attribIndex = this.points.length / 2;\n          nextTexture.wrapMode = WRAP_MODES.REPEAT, j === 0 ? this.processFill(data) : this.processLine(data);\n          const size = this.points.length / 2 - attribIndex;\n          size !== 0 && (batchPart && !this._compareStyles(currentStyle, style) && (batchPart.end(index2, attribIndex), batchPart = null), batchPart || (batchPart = BATCH_POOL.pop() || new BatchPart(), batchPart.begin(style, index2, attribIndex), this.batches.push(batchPart), currentStyle = style), this.addUvs(this.points, uvs, style.texture, attribIndex, size, style.matrix));\n        }\n      }\n      const index = this.indices.length,\n        attrib = this.points.length / 2;\n      if (batchPart && batchPart.end(index, attrib), this.batches.length === 0) {\n        this.batchable = !0;\n        return;\n      }\n      const need32 = attrib > 65535;\n      this.indicesUint16 && this.indices.length === this.indicesUint16.length && need32 === this.indicesUint16.BYTES_PER_ELEMENT > 2 ? this.indicesUint16.set(this.indices) : this.indicesUint16 = need32 ? new Uint32Array(this.indices) : new Uint16Array(this.indices), this.batchable = this.isBatchable(), this.batchable ? this.packBatches() : this.buildDrawCalls();\n    }\n    /**\n     * Affinity check\n     * @param styleA\n     * @param styleB\n     */\n    _compareStyles(styleA, styleB) {\n      return !(!styleA || !styleB || styleA.texture.baseTexture !== styleB.texture.baseTexture || styleA.color + styleA.alpha !== styleB.color + styleB.alpha || !!styleA.native != !!styleB.native);\n    }\n    /** Test geometry for batching process. */\n    validateBatching() {\n      if (this.dirty === this.cacheDirty || !this.graphicsData.length) return !1;\n      for (let i = 0, l = this.graphicsData.length; i < l; i++) {\n        const data = this.graphicsData[i],\n          fill = data.fillStyle,\n          line = data.lineStyle;\n        if (fill && !fill.texture.baseTexture.valid || line && !line.texture.baseTexture.valid) return !1;\n      }\n      return !0;\n    }\n    /** Offset the indices so that it works with the batcher. */\n    packBatches() {\n      this.batchDirty++, this.uvsFloat32 = new Float32Array(this.uvs);\n      const batches = this.batches;\n      for (let i = 0, l = batches.length; i < l; i++) {\n        const batch = batches[i];\n        for (let j = 0; j < batch.size; j++) {\n          const index = batch.start + j;\n          this.indicesUint16[index] = this.indicesUint16[index] - batch.attribStart;\n        }\n      }\n    }\n    /**\n     * Checks to see if this graphics geometry can be batched.\n     * Currently it needs to be small enough and not contain any native lines.\n     */\n    isBatchable() {\n      if (this.points.length > 65535 * 2) return !1;\n      const batches = this.batches;\n      for (let i = 0; i < batches.length; i++) if (batches[i].style.native) return !1;\n      return this.points.length < _GraphicsGeometry2.BATCHABLE_SIZE * 2;\n    }\n    /** Converts intermediate batches data to drawCalls. */\n    buildDrawCalls() {\n      let TICK = ++BaseTexture._globalBatch;\n      for (let i = 0; i < this.drawCalls.length; i++) this.drawCalls[i].texArray.clear(), DRAW_CALL_POOL.push(this.drawCalls[i]);\n      this.drawCalls.length = 0;\n      const colors = this.colors,\n        textureIds = this.textureIds;\n      let currentGroup = DRAW_CALL_POOL.pop();\n      currentGroup || (currentGroup = new BatchDrawCall(), currentGroup.texArray = new BatchTextureArray()), currentGroup.texArray.count = 0, currentGroup.start = 0, currentGroup.size = 0, currentGroup.type = DRAW_MODES.TRIANGLES;\n      let textureCount = 0,\n        currentTexture = null,\n        textureId = 0,\n        native = !1,\n        drawMode = DRAW_MODES.TRIANGLES,\n        index = 0;\n      this.drawCalls.push(currentGroup);\n      for (let i = 0; i < this.batches.length; i++) {\n        const data = this.batches[i],\n          maxTextures = 8,\n          style = data.style,\n          nextTexture = style.texture.baseTexture;\n        native !== !!style.native && (native = !!style.native, drawMode = native ? DRAW_MODES.LINES : DRAW_MODES.TRIANGLES, currentTexture = null, textureCount = maxTextures, TICK++), currentTexture !== nextTexture && (currentTexture = nextTexture, nextTexture._batchEnabled !== TICK && (textureCount === maxTextures && (TICK++, textureCount = 0, currentGroup.size > 0 && (currentGroup = DRAW_CALL_POOL.pop(), currentGroup || (currentGroup = new BatchDrawCall(), currentGroup.texArray = new BatchTextureArray()), this.drawCalls.push(currentGroup)), currentGroup.start = index, currentGroup.size = 0, currentGroup.texArray.count = 0, currentGroup.type = drawMode), nextTexture.touched = 1, nextTexture._batchEnabled = TICK, nextTexture._batchLocation = textureCount, nextTexture.wrapMode = WRAP_MODES.REPEAT, currentGroup.texArray.elements[currentGroup.texArray.count++] = nextTexture, textureCount++)), currentGroup.size += data.size, index += data.size, textureId = nextTexture._batchLocation, this.addColors(colors, style.color, style.alpha, data.attribSize, data.attribStart), this.addTextureIds(textureIds, textureId, data.attribSize, data.attribStart);\n      }\n      BaseTexture._globalBatch = TICK, this.packAttributes();\n    }\n    /** Packs attributes to single buffer. */\n    packAttributes() {\n      const verts = this.points,\n        uvs = this.uvs,\n        colors = this.colors,\n        textureIds = this.textureIds,\n        glPoints = new ArrayBuffer(verts.length * 3 * 4),\n        f32 = new Float32Array(glPoints),\n        u32 = new Uint32Array(glPoints);\n      let p = 0;\n      for (let i = 0; i < verts.length / 2; i++) f32[p++] = verts[i * 2], f32[p++] = verts[i * 2 + 1], f32[p++] = uvs[i * 2], f32[p++] = uvs[i * 2 + 1], u32[p++] = colors[i], f32[p++] = textureIds[i];\n      this._buffer.update(glPoints), this._indexBuffer.update(this.indicesUint16);\n    }\n    /**\n     * Process fill part of Graphics.\n     * @param data\n     */\n    processFill(data) {\n      data.holes.length ? buildPoly.triangulate(data, this) : FILL_COMMANDS[data.type].triangulate(data, this);\n    }\n    /**\n     * Process line part of Graphics.\n     * @param data\n     */\n    processLine(data) {\n      buildLine(data, this);\n      for (let i = 0; i < data.holes.length; i++) buildLine(data.holes[i], this);\n    }\n    /**\n     * Process the holes data.\n     * @param holes\n     */\n    processHoles(holes) {\n      for (let i = 0; i < holes.length; i++) {\n        const hole = holes[i];\n        FILL_COMMANDS[hole.type].build(hole), hole.matrix && this.transformPoints(hole.points, hole.matrix);\n      }\n    }\n    /** Update the local bounds of the object. Expensive to use performance-wise. */\n    calculateBounds() {\n      const bounds = this._bounds;\n      bounds.clear(), bounds.addVertexData(this.points, 0, this.points.length), bounds.pad(this.boundsPadding, this.boundsPadding);\n    }\n    /**\n     * Transform points using matrix.\n     * @param points - Points to transform\n     * @param matrix - Transform matrix\n     */\n    transformPoints(points, matrix) {\n      for (let i = 0; i < points.length / 2; i++) {\n        const x = points[i * 2],\n          y = points[i * 2 + 1];\n        points[i * 2] = matrix.a * x + matrix.c * y + matrix.tx, points[i * 2 + 1] = matrix.b * x + matrix.d * y + matrix.ty;\n      }\n    }\n    /**\n     * Add colors.\n     * @param colors - List of colors to add to\n     * @param color - Color to add\n     * @param alpha - Alpha to use\n     * @param size - Number of colors to add\n     * @param offset\n     */\n    addColors(colors, color, alpha, size, offset = 0) {\n      const bgr = Color.shared.setValue(color).toLittleEndianNumber(),\n        result = Color.shared.setValue(bgr).toPremultiplied(alpha);\n      colors.length = Math.max(colors.length, offset + size);\n      for (let i = 0; i < size; i++) colors[offset + i] = result;\n    }\n    /**\n     * Add texture id that the shader/fragment wants to use.\n     * @param textureIds\n     * @param id\n     * @param size\n     * @param offset\n     */\n    addTextureIds(textureIds, id, size, offset = 0) {\n      textureIds.length = Math.max(textureIds.length, offset + size);\n      for (let i = 0; i < size; i++) textureIds[offset + i] = id;\n    }\n    /**\n     * Generates the UVs for a shape.\n     * @param verts - Vertices\n     * @param uvs - UVs\n     * @param texture - Reference to Texture\n     * @param start - Index buffer start index.\n     * @param size - The size/length for index buffer.\n     * @param matrix - Optional transform for all points.\n     */\n    addUvs(verts, uvs, texture, start, size, matrix = null) {\n      let index = 0;\n      const uvsStart = uvs.length,\n        frame = texture.frame;\n      for (; index < size;) {\n        let x = verts[(start + index) * 2],\n          y = verts[(start + index) * 2 + 1];\n        if (matrix) {\n          const nx = matrix.a * x + matrix.c * y + matrix.tx;\n          y = matrix.b * x + matrix.d * y + matrix.ty, x = nx;\n        }\n        index++, uvs.push(x / frame.width, y / frame.height);\n      }\n      const baseTexture = texture.baseTexture;\n      (frame.width < baseTexture.width || frame.height < baseTexture.height) && this.adjustUvs(uvs, texture, uvsStart, size);\n    }\n    /**\n     * Modify uvs array according to position of texture region\n     * Does not work with rotated or trimmed textures\n     * @param uvs - array\n     * @param texture - region\n     * @param start - starting index for uvs\n     * @param size - how many points to adjust\n     */\n    adjustUvs(uvs, texture, start, size) {\n      const baseTexture = texture.baseTexture,\n        eps = 1e-6,\n        finish = start + size * 2,\n        frame = texture.frame,\n        scaleX = frame.width / baseTexture.width,\n        scaleY = frame.height / baseTexture.height;\n      let offsetX = frame.x / frame.width,\n        offsetY = frame.y / frame.height,\n        minX = Math.floor(uvs[start] + eps),\n        minY = Math.floor(uvs[start + 1] + eps);\n      for (let i = start + 2; i < finish; i += 2) minX = Math.min(minX, Math.floor(uvs[i] + eps)), minY = Math.min(minY, Math.floor(uvs[i + 1] + eps));\n      offsetX -= minX, offsetY -= minY;\n      for (let i = start; i < finish; i += 2) uvs[i] = (uvs[i] + offsetX) * scaleX, uvs[i + 1] = (uvs[i + 1] + offsetY) * scaleY;\n    }\n  };\n_GraphicsGeometry.BATCHABLE_SIZE = 100;\nlet GraphicsGeometry = _GraphicsGeometry;\nexport { GraphicsGeometry };", "map": {"version": 3, "names": ["tmpPoint", "Point", "_GraphicsGeometry", "_GraphicsGeometry2", "BatchGeometry", "constructor", "closePointEps", "boundsPadding", "uvsFloat32", "indicesUint16", "batchable", "points", "colors", "uvs", "indices", "textureIds", "graphicsData", "drawCalls", "batchDirty", "batches", "dirty", "cacheDirty", "clearDirty", "shapeIndex", "_bounds", "Bounds", "boundsDirty", "bounds", "updateBatches", "calculateBounds", "invalidate", "length", "i", "texArray", "clear", "DRAW_CALL_POOL", "push", "batchPart", "reset", "BATCH_POOL", "drawShape", "shape", "fillStyle", "lineStyle", "matrix", "data", "GraphicsData", "drawHole", "lastShape", "holes", "destroy", "indexBuffer", "containsPoint", "point", "visible", "applyInverse", "copyFrom", "contains", "x", "y", "hitHole", "i2", "validateBatching", "currentStyle", "style", "FILL_COMMANDS", "type", "build", "transformPoints", "processHoles", "j", "nextTexture", "texture", "baseTexture", "index2", "attribIndex", "wrapMode", "WRAP_MODES", "REPEAT", "processFill", "processLine", "size", "_compareStyles", "end", "pop", "<PERSON><PERSON><PERSON>art", "begin", "addUvs", "index", "attrib", "need32", "BYTES_PER_ELEMENT", "set", "Uint32Array", "Uint16Array", "isBatchable", "packBatches", "buildDrawCalls", "styleA", "styleB", "color", "alpha", "native", "l", "fill", "line", "valid", "Float32Array", "batch", "start", "attribStart", "BATCHABLE_SIZE", "TICK", "BaseTexture", "_globalBatch", "currentGroup", "BatchDrawCall", "BatchTextureArray", "count", "DRAW_MODES", "TRIANGLES", "textureCount", "currentTexture", "textureId", "drawMode", "maxTextures", "LINES", "_batchEnabled", "touched", "_batchLocation", "elements", "addColors", "attribSize", "addTextureIds", "packAttributes", "verts", "glPoints", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f32", "u32", "p", "_buffer", "update", "_indexBuffer", "buildPoly", "triangulate", "buildLine", "hole", "addVertexData", "pad", "a", "c", "tx", "b", "d", "ty", "offset", "bgr", "Color", "shared", "setValue", "toLittleEndianNumber", "result", "toPremultiplied", "Math", "max", "id", "uvsStart", "frame", "nx", "width", "height", "adjustUvs", "eps", "finish", "scaleX", "scaleY", "offsetX", "offsetY", "minX", "floor", "minY", "min", "GraphicsGeometry"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\graphics\\src\\GraphicsGeometry.ts"], "sourcesContent": ["import {\n    <PERSON>Texture,\n    Batch<PERSON>rawCall,\n    BatchGeometry,\n    BatchTextureArray,\n    Color,\n    DRAW_MODES,\n    Point,\n    WRAP_MODES\n} from '@pixi/core';\nimport { Bounds } from '@pixi/display';\nimport { GraphicsData } from './GraphicsData';\nimport {\n    BATCH_POOL, BatchPart, buildLine,\n    buildPoly,\n    DRAW_CALL_POOL,\n    FILL_COMMANDS\n} from './utils';\n\nimport type { IPointData, IShape, Matrix, Texture } from '@pixi/core';\nimport type { FillStyle } from './styles/FillStyle';\nimport type { LineStyle } from './styles/LineStyle';\n\nconst tmpPoint = new Point();\n\n/**\n * The Graphics class contains methods used to draw primitive shapes such as lines, circles and\n * rectangles to the display, and to color and fill them.\n *\n * GraphicsGeometry is designed to not be continually updating the geometry since it's expensive\n * to re-tesselate using **earcut**. Consider using {@link PIXI.Mesh} for this use-case, it's much faster.\n * @memberof PIXI\n */\nexport class GraphicsGeometry extends BatchGeometry\n{\n    /** The maximum number of points to consider an object \"batchable\", able to be batched by the renderer's batch system. */\n    public static BATCHABLE_SIZE = 100;\n\n    /** Minimal distance between points that are considered different. Affects line tesselation. */\n    public closePointEps = 1e-4;\n\n    /** Padding to add to the bounds. */\n    public boundsPadding = 0;\n\n    uvsFloat32: Float32Array = null;\n    indicesUint16: Uint16Array | Uint32Array = null;\n    batchable = false;\n\n    /** An array of points to draw, 2 numbers per point */\n    points: number[] = [];\n\n    /** The collection of colors */\n    colors: number[] = [];\n\n    /** The UVs collection */\n    uvs: number[] = [];\n\n    /** The indices of the vertices */\n    indices: number[] = [];\n\n    /** Reference to the texture IDs. */\n    textureIds: number[] = [];\n\n    /**\n     * The collection of drawn shapes.\n     * @member {PIXI.GraphicsData[]}\n     */\n    graphicsData: Array<GraphicsData> = [];\n\n    /**\n     * List of current draw calls drived from the batches.\n     * @member {PIXI.BatchDrawCall[]}\n     */\n    drawCalls: Array<BatchDrawCall> = [];\n\n    /** Batches need to regenerated if the geometry is updated. */\n    batchDirty = -1;\n\n    /**\n     * Intermediate abstract format sent to batch system.\n     * Can be converted to drawCalls or to batchable objects.\n     * @member {PIXI.graphicsUtils.BatchPart[]}\n     */\n    batches: Array<BatchPart> = [];\n\n    /** Used to detect if the graphics object has changed. */\n    protected dirty = 0;\n\n    /** Used to check if the cache is dirty. */\n    protected cacheDirty = -1;\n\n    /** Used to detect if we cleared the graphicsData. */\n    protected clearDirty = 0;\n\n    /** Index of the last batched shape in the stack of calls. */\n    protected shapeIndex = 0;\n\n    /** Cached bounds. */\n    protected _bounds: Bounds = new Bounds();\n\n    /** The bounds dirty flag. */\n    protected boundsDirty = -1;\n\n    // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n    constructor()\n    {\n        super();\n    }\n\n    /**\n     * Get the current bounds of the graphic geometry.\n     *\n     * Since 6.5.0, bounds of the graphics geometry are calculated based on the vertices of generated geometry.\n     * Since shapes or strokes with full transparency (`alpha: 0`) will not generate geometry, they are not considered\n     * when calculating bounds for the graphics geometry. See PR [#8343]{@link https://github.com/pixijs/pixijs/pull/8343}\n     * and issue [#8623]{@link https://github.com/pixijs/pixijs/pull/8623}.\n     * @readonly\n     */\n    public get bounds(): Bounds\n    {\n        this.updateBatches();\n\n        if (this.boundsDirty !== this.dirty)\n        {\n            this.boundsDirty = this.dirty;\n            this.calculateBounds();\n        }\n\n        return this._bounds;\n    }\n\n    /** Call if you changed graphicsData manually. Empties all batch buffers. */\n    protected invalidate(): void\n    {\n        this.boundsDirty = -1;\n        this.dirty++;\n        this.batchDirty++;\n        this.shapeIndex = 0;\n\n        this.points.length = 0;\n        this.colors.length = 0;\n        this.uvs.length = 0;\n        this.indices.length = 0;\n        this.textureIds.length = 0;\n\n        for (let i = 0; i < this.drawCalls.length; i++)\n        {\n            this.drawCalls[i].texArray.clear();\n            DRAW_CALL_POOL.push(this.drawCalls[i]);\n        }\n\n        this.drawCalls.length = 0;\n\n        for (let i = 0; i < this.batches.length; i++)\n        {\n            const batchPart = this.batches[i];\n\n            batchPart.reset();\n            BATCH_POOL.push(batchPart);\n        }\n\n        this.batches.length = 0;\n    }\n\n    /**\n     * Clears the graphics that were drawn to this Graphics object, and resets fill and line style settings.\n     * @returns - This GraphicsGeometry object. Good for chaining method calls\n     */\n    public clear(): GraphicsGeometry\n    {\n        if (this.graphicsData.length > 0)\n        {\n            this.invalidate();\n            this.clearDirty++;\n            this.graphicsData.length = 0;\n        }\n\n        return this;\n    }\n\n    /**\n     * Draws the given shape to this Graphics object. Can be any of Circle, Rectangle, Ellipse, Line or Polygon.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param fillStyle - Defines style of the fill.\n     * @param lineStyle - Defines style of the lines.\n     * @param matrix - Transform applied to the points of the shape.\n     * @returns - Returns geometry for chaining.\n     */\n    public drawShape(\n        shape: IShape,\n        fillStyle: FillStyle = null,\n        lineStyle: LineStyle = null,\n        matrix: Matrix = null): GraphicsGeometry\n    {\n        const data = new GraphicsData(shape, fillStyle, lineStyle, matrix);\n\n        this.graphicsData.push(data);\n        this.dirty++;\n\n        return this;\n    }\n\n    /**\n     * Draws the given shape to this Graphics object. Can be any of Circle, Rectangle, Ellipse, Line or Polygon.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param matrix - Transform applied to the points of the shape.\n     * @returns - Returns geometry for chaining.\n     */\n    public drawHole(shape: IShape, matrix: Matrix = null): GraphicsGeometry\n    {\n        if (!this.graphicsData.length)\n        {\n            return null;\n        }\n\n        const data = new GraphicsData(shape, null, null, matrix);\n\n        const lastShape = this.graphicsData[this.graphicsData.length - 1];\n\n        data.lineStyle = lastShape.lineStyle;\n\n        lastShape.holes.push(data);\n\n        this.dirty++;\n\n        return this;\n    }\n\n    /** Destroys the GraphicsGeometry object. */\n    public destroy(): void\n    {\n        super.destroy();\n\n        // destroy each of the GraphicsData objects\n        for (let i = 0; i < this.graphicsData.length; ++i)\n        {\n            this.graphicsData[i].destroy();\n        }\n\n        this.points.length = 0;\n        this.points = null;\n        this.colors.length = 0;\n        this.colors = null;\n        this.uvs.length = 0;\n        this.uvs = null;\n        this.indices.length = 0;\n        this.indices = null;\n        this.indexBuffer.destroy();\n        this.indexBuffer = null;\n        this.graphicsData.length = 0;\n        this.graphicsData = null;\n        this.drawCalls.length = 0;\n        this.drawCalls = null;\n        this.batches.length = 0;\n        this.batches = null;\n        this._bounds = null;\n    }\n\n    /**\n     * Check to see if a point is contained within this geometry.\n     * @param point - Point to check if it's contained.\n     * @returns {boolean} `true` if the point is contained within geometry.\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        const graphicsData = this.graphicsData;\n\n        for (let i = 0; i < graphicsData.length; ++i)\n        {\n            const data = graphicsData[i];\n\n            if (!data.fillStyle.visible)\n            {\n                continue;\n            }\n\n            // only deal with fills..\n            if (data.shape)\n            {\n                if (data.matrix)\n                {\n                    data.matrix.applyInverse(point, tmpPoint);\n                }\n                else\n                {\n                    tmpPoint.copyFrom(point);\n                }\n\n                if (data.shape.contains(tmpPoint.x, tmpPoint.y))\n                {\n                    let hitHole = false;\n\n                    if (data.holes)\n                    {\n                        for (let i = 0; i < data.holes.length; i++)\n                        {\n                            const hole = data.holes[i];\n\n                            if (hole.shape.contains(tmpPoint.x, tmpPoint.y))\n                            {\n                                hitHole = true;\n                                break;\n                            }\n                        }\n                    }\n\n                    if (!hitHole)\n                    {\n                        return true;\n                    }\n                }\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Generates intermediate batch data. Either gets converted to drawCalls\n     * or used to convert to batch objects directly by the Graphics object.\n     */\n    updateBatches(): void\n    {\n        if (!this.graphicsData.length)\n        {\n            this.batchable = true;\n\n            return;\n        }\n\n        if (!this.validateBatching())\n        {\n            return;\n        }\n\n        this.cacheDirty = this.dirty;\n\n        const uvs = this.uvs;\n        const graphicsData = this.graphicsData;\n\n        let batchPart: BatchPart = null;\n\n        let currentStyle = null;\n\n        if (this.batches.length > 0)\n        {\n            batchPart = this.batches[this.batches.length - 1];\n            currentStyle = batchPart.style;\n        }\n\n        for (let i = this.shapeIndex; i < graphicsData.length; i++)\n        {\n            this.shapeIndex++;\n\n            const data = graphicsData[i];\n            const fillStyle = data.fillStyle;\n            const lineStyle = data.lineStyle;\n            const command = FILL_COMMANDS[data.type];\n\n            // build out the shapes points..\n            command.build(data);\n\n            if (data.matrix)\n            {\n                this.transformPoints(data.points, data.matrix);\n            }\n\n            if (fillStyle.visible || lineStyle.visible)\n            {\n                this.processHoles(data.holes);\n            }\n\n            for (let j = 0; j < 2; j++)\n            {\n                const style = (j === 0) ? fillStyle : lineStyle;\n\n                if (!style.visible) continue;\n\n                const nextTexture = style.texture.baseTexture;\n                const index = this.indices.length;\n                const attribIndex = this.points.length / 2;\n\n                nextTexture.wrapMode = WRAP_MODES.REPEAT;\n\n                if (j === 0)\n                {\n                    this.processFill(data);\n                }\n                else\n                {\n                    this.processLine(data);\n                }\n\n                const size = (this.points.length / 2) - attribIndex;\n\n                if (size === 0) continue;\n                // close batch if style is different\n                if (batchPart && !this._compareStyles(currentStyle, style))\n                {\n                    batchPart.end(index, attribIndex);\n                    batchPart = null;\n                }\n                // spawn new batch if its first batch or previous was closed\n                if (!batchPart)\n                {\n                    batchPart = BATCH_POOL.pop() || new BatchPart();\n                    batchPart.begin(style, index, attribIndex);\n                    this.batches.push(batchPart);\n                    currentStyle = style;\n                }\n\n                this.addUvs(this.points, uvs, style.texture, attribIndex, size, style.matrix);\n            }\n        }\n\n        const index = this.indices.length;\n        const attrib = this.points.length / 2;\n\n        if (batchPart)\n        {\n            batchPart.end(index, attrib);\n        }\n\n        if (this.batches.length === 0)\n        {\n            // there are no visible styles in GraphicsData\n            // its possible that someone wants Graphics just for the bounds\n            this.batchable = true;\n\n            return;\n        }\n\n        const need32 = attrib > 0xffff;\n\n        // prevent allocation when length is same as buffer\n        if (this.indicesUint16 && this.indices.length === this.indicesUint16.length\n            && need32 === (this.indicesUint16.BYTES_PER_ELEMENT > 2))\n        {\n            this.indicesUint16.set(this.indices);\n        }\n        else\n        {\n            this.indicesUint16 = need32 ? new Uint32Array(this.indices) : new Uint16Array(this.indices);\n        }\n\n        // TODO make this a const..\n        this.batchable = this.isBatchable();\n\n        if (this.batchable)\n        {\n            this.packBatches();\n        }\n        else\n        {\n            this.buildDrawCalls();\n        }\n    }\n\n    /**\n     * Affinity check\n     * @param styleA\n     * @param styleB\n     */\n    protected _compareStyles(styleA: FillStyle | LineStyle, styleB: FillStyle | LineStyle): boolean\n    {\n        if (!styleA || !styleB)\n        {\n            return false;\n        }\n\n        if (styleA.texture.baseTexture !== styleB.texture.baseTexture)\n        {\n            return false;\n        }\n\n        if (styleA.color + styleA.alpha !== styleB.color + styleB.alpha)\n        {\n            return false;\n        }\n\n        if (!!(styleA as LineStyle).native !== !!(styleB as LineStyle).native)\n        {\n            return false;\n        }\n\n        return true;\n    }\n\n    /** Test geometry for batching process. */\n    protected validateBatching(): boolean\n    {\n        if (this.dirty === this.cacheDirty || !this.graphicsData.length)\n        {\n            return false;\n        }\n\n        for (let i = 0, l = this.graphicsData.length; i < l; i++)\n        {\n            const data = this.graphicsData[i];\n            const fill = data.fillStyle;\n            const line = data.lineStyle;\n\n            if (fill && !fill.texture.baseTexture.valid) return false;\n            if (line && !line.texture.baseTexture.valid) return false;\n        }\n\n        return true;\n    }\n\n    /** Offset the indices so that it works with the batcher. */\n    protected packBatches(): void\n    {\n        this.batchDirty++;\n        this.uvsFloat32 = new Float32Array(this.uvs);\n\n        const batches = this.batches;\n\n        for (let i = 0, l = batches.length; i < l; i++)\n        {\n            const batch = batches[i];\n\n            for (let j = 0; j < batch.size; j++)\n            {\n                const index = batch.start + j;\n\n                this.indicesUint16[index] = this.indicesUint16[index] - batch.attribStart;\n            }\n        }\n    }\n\n    /**\n     * Checks to see if this graphics geometry can be batched.\n     * Currently it needs to be small enough and not contain any native lines.\n     */\n    protected isBatchable(): boolean\n    {\n        // prevent heavy mesh batching\n        if (this.points.length > 0xffff * 2)\n        {\n            return false;\n        }\n\n        const batches = this.batches;\n\n        for (let i = 0; i < batches.length; i++)\n        {\n            if ((batches[i].style as LineStyle).native)\n            {\n                return false;\n            }\n        }\n\n        return (this.points.length < GraphicsGeometry.BATCHABLE_SIZE * 2);\n    }\n\n    /** Converts intermediate batches data to drawCalls. */\n    protected buildDrawCalls(): void\n    {\n        let TICK = ++BaseTexture._globalBatch;\n\n        for (let i = 0; i < this.drawCalls.length; i++)\n        {\n            this.drawCalls[i].texArray.clear();\n            DRAW_CALL_POOL.push(this.drawCalls[i]);\n        }\n\n        this.drawCalls.length = 0;\n\n        const colors = this.colors;\n        const textureIds = this.textureIds;\n\n        let currentGroup: BatchDrawCall = DRAW_CALL_POOL.pop();\n\n        if (!currentGroup)\n        {\n            currentGroup = new BatchDrawCall();\n            currentGroup.texArray = new BatchTextureArray();\n        }\n        currentGroup.texArray.count = 0;\n        currentGroup.start = 0;\n        currentGroup.size = 0;\n        currentGroup.type = DRAW_MODES.TRIANGLES;\n\n        let textureCount = 0;\n        let currentTexture = null;\n        let textureId = 0;\n        let native = false;\n        let drawMode = DRAW_MODES.TRIANGLES;\n\n        let index = 0;\n\n        this.drawCalls.push(currentGroup);\n\n        // TODO - this can be simplified\n        for (let i = 0; i < this.batches.length; i++)\n        {\n            const data = this.batches[i];\n\n            // TODO add some full on MAX_TEXTURE CODE..\n            const maxTextures = 8;\n\n            // Forced cast for checking `native` without errors\n            const style = data.style as LineStyle;\n\n            const nextTexture = style.texture.baseTexture;\n\n            if (native !== !!style.native)\n            {\n                native = !!style.native;\n                drawMode = native ? DRAW_MODES.LINES : DRAW_MODES.TRIANGLES;\n\n                // force the batch to break!\n                currentTexture = null;\n                textureCount = maxTextures;\n                TICK++;\n            }\n\n            if (currentTexture !== nextTexture)\n            {\n                currentTexture = nextTexture;\n\n                if (nextTexture._batchEnabled !== TICK)\n                {\n                    if (textureCount === maxTextures)\n                    {\n                        TICK++;\n\n                        textureCount = 0;\n\n                        if (currentGroup.size > 0)\n                        {\n                            currentGroup = DRAW_CALL_POOL.pop();\n                            if (!currentGroup)\n                            {\n                                currentGroup = new BatchDrawCall();\n                                currentGroup.texArray = new BatchTextureArray();\n                            }\n                            this.drawCalls.push(currentGroup);\n                        }\n\n                        currentGroup.start = index;\n                        currentGroup.size = 0;\n                        currentGroup.texArray.count = 0;\n                        currentGroup.type = drawMode;\n                    }\n\n                    // TODO add this to the render part..\n                    // Hack! Because texture has protected `touched`\n                    nextTexture.touched = 1;// touch;\n\n                    nextTexture._batchEnabled = TICK;\n                    nextTexture._batchLocation = textureCount;\n                    nextTexture.wrapMode = WRAP_MODES.REPEAT;\n\n                    currentGroup.texArray.elements[currentGroup.texArray.count++] = nextTexture;\n                    textureCount++;\n                }\n            }\n\n            currentGroup.size += data.size;\n            index += data.size;\n\n            textureId = nextTexture._batchLocation;\n\n            this.addColors(colors, style.color, style.alpha, data.attribSize, data.attribStart);\n            this.addTextureIds(textureIds, textureId, data.attribSize, data.attribStart);\n        }\n\n        BaseTexture._globalBatch = TICK;\n\n        // upload..\n        // merge for now!\n        this.packAttributes();\n    }\n\n    /** Packs attributes to single buffer. */\n    protected packAttributes(): void\n    {\n        const verts = this.points;\n        const uvs = this.uvs;\n        const colors = this.colors;\n        const textureIds = this.textureIds;\n\n        // verts are 2 positions.. so we * by 3 as there are 6 properties.. then 4 cos its bytes\n        const glPoints = new ArrayBuffer(verts.length * 3 * 4);\n        const f32 = new Float32Array(glPoints);\n        const u32 = new Uint32Array(glPoints);\n\n        let p = 0;\n\n        for (let i = 0; i < verts.length / 2; i++)\n        {\n            f32[p++] = verts[i * 2];\n            f32[p++] = verts[(i * 2) + 1];\n\n            f32[p++] = uvs[i * 2];\n            f32[p++] = uvs[(i * 2) + 1];\n\n            u32[p++] = colors[i];\n\n            f32[p++] = textureIds[i];\n        }\n\n        this._buffer.update(glPoints);\n        this._indexBuffer.update(this.indicesUint16);\n    }\n\n    /**\n     * Process fill part of Graphics.\n     * @param data\n     */\n    protected processFill(data: GraphicsData): void\n    {\n        if (data.holes.length)\n        {\n            buildPoly.triangulate(data, this);\n        }\n        else\n        {\n            const command = FILL_COMMANDS[data.type];\n\n            command.triangulate(data, this);\n        }\n    }\n\n    /**\n     * Process line part of Graphics.\n     * @param data\n     */\n    protected processLine(data: GraphicsData): void\n    {\n        buildLine(data, this);\n\n        for (let i = 0; i < data.holes.length; i++)\n        {\n            buildLine(data.holes[i], this);\n        }\n    }\n\n    /**\n     * Process the holes data.\n     * @param holes\n     */\n    protected processHoles(holes: Array<GraphicsData>): void\n    {\n        for (let i = 0; i < holes.length; i++)\n        {\n            const hole = holes[i];\n            const command = FILL_COMMANDS[hole.type];\n\n            command.build(hole);\n\n            if (hole.matrix)\n            {\n                this.transformPoints(hole.points, hole.matrix);\n            }\n        }\n    }\n\n    /** Update the local bounds of the object. Expensive to use performance-wise. */\n    protected calculateBounds(): void\n    {\n        const bounds = this._bounds;\n\n        bounds.clear();\n        bounds.addVertexData((this.points as any), 0, this.points.length);\n        bounds.pad(this.boundsPadding, this.boundsPadding);\n    }\n\n    /**\n     * Transform points using matrix.\n     * @param points - Points to transform\n     * @param matrix - Transform matrix\n     */\n    protected transformPoints(points: Array<number>, matrix: Matrix): void\n    {\n        for (let i = 0; i < points.length / 2; i++)\n        {\n            const x = points[(i * 2)];\n            const y = points[(i * 2) + 1];\n\n            points[(i * 2)] = (matrix.a * x) + (matrix.c * y) + matrix.tx;\n            points[(i * 2) + 1] = (matrix.b * x) + (matrix.d * y) + matrix.ty;\n        }\n    }\n\n    /**\n     * Add colors.\n     * @param colors - List of colors to add to\n     * @param color - Color to add\n     * @param alpha - Alpha to use\n     * @param size - Number of colors to add\n     * @param offset\n     */\n    protected addColors(\n        colors: Array<number>,\n        color: number,\n        alpha: number,\n        size: number,\n        offset = 0): void\n    {\n        const bgr = Color.shared\n            .setValue(color)\n            .toLittleEndianNumber();\n\n        const result = Color.shared\n            .setValue(bgr)\n            .toPremultiplied(alpha);\n\n        colors.length = Math.max(colors.length, offset + size);\n\n        for (let i = 0; i < size; i++)\n        {\n            colors[offset + i] = result;\n        }\n    }\n\n    /**\n     * Add texture id that the shader/fragment wants to use.\n     * @param textureIds\n     * @param id\n     * @param size\n     * @param offset\n     */\n    protected addTextureIds(\n        textureIds: Array<number>,\n        id: number,\n        size: number,\n        offset = 0): void\n    {\n        textureIds.length = Math.max(textureIds.length, offset + size);\n\n        for (let i = 0; i < size; i++)\n        {\n            textureIds[offset + i] = id;\n        }\n    }\n\n    /**\n     * Generates the UVs for a shape.\n     * @param verts - Vertices\n     * @param uvs - UVs\n     * @param texture - Reference to Texture\n     * @param start - Index buffer start index.\n     * @param size - The size/length for index buffer.\n     * @param matrix - Optional transform for all points.\n     */\n    protected addUvs(\n        verts: Array<number>,\n        uvs: Array<number>,\n        texture: Texture,\n        start: number,\n        size: number,\n        matrix: Matrix = null): void\n    {\n        let index = 0;\n        const uvsStart = uvs.length;\n        const frame = texture.frame;\n\n        while (index < size)\n        {\n            let x = verts[(start + index) * 2];\n            let y = verts[((start + index) * 2) + 1];\n\n            if (matrix)\n            {\n                const nx = (matrix.a * x) + (matrix.c * y) + matrix.tx;\n\n                y = (matrix.b * x) + (matrix.d * y) + matrix.ty;\n                x = nx;\n            }\n\n            index++;\n\n            uvs.push(x / frame.width, y / frame.height);\n        }\n\n        const baseTexture = texture.baseTexture;\n\n        if (frame.width < baseTexture.width\n            || frame.height < baseTexture.height)\n        {\n            this.adjustUvs(uvs, texture, uvsStart, size);\n        }\n    }\n\n    /**\n     * Modify uvs array according to position of texture region\n     * Does not work with rotated or trimmed textures\n     * @param uvs - array\n     * @param texture - region\n     * @param start - starting index for uvs\n     * @param size - how many points to adjust\n     */\n    protected adjustUvs(uvs: Array<number>, texture: Texture, start: number, size: number): void\n    {\n        const baseTexture = texture.baseTexture;\n        const eps = 1e-6;\n        const finish = start + (size * 2);\n        const frame = texture.frame;\n        const scaleX = frame.width / baseTexture.width;\n        const scaleY = frame.height / baseTexture.height;\n        let offsetX = frame.x / frame.width;\n        let offsetY = frame.y / frame.height;\n        let minX = Math.floor(uvs[start] + eps);\n        let minY = Math.floor(uvs[start + 1] + eps);\n\n        for (let i = start + 2; i < finish; i += 2)\n        {\n            minX = Math.min(minX, Math.floor(uvs[i] + eps));\n            minY = Math.min(minY, Math.floor(uvs[i + 1] + eps));\n        }\n        offsetX -= minX;\n        offsetY -= minY;\n        for (let i = start; i < finish; i += 2)\n        {\n            uvs[i] = (uvs[i] + offsetX) * scaleX;\n            uvs[i + 1] = (uvs[i + 1] + offsetY) * scaleY;\n        }\n    }\n}\n"], "mappings": ";;;;;;;AAuBA,MAAMA,QAAA,GAAW,IAAIC,KAAA;EAURC,iBAAA,GAAN,MAAMC,kBAAA,SAAyBC,aAAA,CACtC;IAAA;IAsEIC,YAAA,EACA;MACU,SAnEV,KAAOC,aAAA,GAAgB,MAGvB,KAAOC,aAAA,GAAgB,GAEI,KAAAC,UAAA,SACgB,KAAAC,aAAA,SAC/B,KAAAC,SAAA,OAGZ,KAAAC,MAAA,GAAmB,IAGnB,KAAAC,MAAA,GAAmB,IAGnB,KAAAC,GAAA,GAAgB,IAGhB,KAAAC,OAAA,GAAoB,IAGpB,KAAAC,UAAA,GAAuB,IAMvB,KAAAC,YAAA,GAAoC,IAMpC,KAAAC,SAAA,GAAkC,IAGrB,KAAAC,UAAA,OAOb,KAAAC,OAAA,GAA4B,IAG5B,KAAUC,KAAA,GAAQ,GAGlB,KAAUC,UAAA,GAAa,IAGvB,KAAUC,UAAA,GAAa,GAGvB,KAAUC,UAAA,GAAa,GAGb,KAAAC,OAAA,GAAkB,IAAIC,MAAA,IAGhC,KAAUC,WAAA,GAAc;IAMxB;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAWA,IAAWC,OAAA,EACX;MACI,YAAKC,aAAA,CAAc,GAEf,KAAKF,WAAA,KAAgB,KAAKN,KAAA,KAE1B,KAAKM,WAAA,GAAc,KAAKN,KAAA,EACxB,KAAKS,eAAA,KAGF,KAAKL,OAAA;IAChB;IAAA;IAGUM,WAAA,EACV;MACS,KAAAJ,WAAA,GAAc,IACnB,KAAKN,KAAA,IACL,KAAKF,UAAA,IACL,KAAKK,UAAA,GAAa,GAElB,KAAKZ,MAAA,CAAOoB,MAAA,GAAS,GACrB,KAAKnB,MAAA,CAAOmB,MAAA,GAAS,GACrB,KAAKlB,GAAA,CAAIkB,MAAA,GAAS,GAClB,KAAKjB,OAAA,CAAQiB,MAAA,GAAS,GACtB,KAAKhB,UAAA,CAAWgB,MAAA,GAAS;MAEzB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKf,SAAA,CAAUc,MAAA,EAAQC,CAAA,IAElC,KAAAf,SAAA,CAAUe,CAAC,EAAEC,QAAA,CAASC,KAAA,IAC3BC,cAAA,CAAeC,IAAA,CAAK,KAAKnB,SAAA,CAAUe,CAAC,CAAC;MAGzC,KAAKf,SAAA,CAAUc,MAAA,GAAS;MAExB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKb,OAAA,CAAQY,MAAA,EAAQC,CAAA,IACzC;QACU,MAAAK,SAAA,GAAY,KAAKlB,OAAA,CAAQa,CAAC;QAEhCK,SAAA,CAAUC,KAAA,CAAM,GAChBC,UAAA,CAAWH,IAAA,CAAKC,SAAS;MAC7B;MAEA,KAAKlB,OAAA,CAAQY,MAAA,GAAS;IAC1B;IAAA;AAAA;AAAA;AAAA;IAMOG,MAAA,EACP;MACI,OAAI,KAAKlB,YAAA,CAAae,MAAA,GAAS,MAE3B,KAAKD,UAAA,CACL,QAAKR,UAAA,IACL,KAAKN,YAAA,CAAae,MAAA,GAAS,IAGxB;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAUOS,UACHC,KAAA,EACAC,SAAA,GAAuB,MACvBC,SAAA,GAAuB,MACvBC,MAAA,GAAiB,MACrB;MACI,MAAMC,IAAA,GAAO,IAAIC,YAAA,CAAaL,KAAA,EAAOC,SAAA,EAAWC,SAAA,EAAWC,MAAM;MAEjE,YAAK5B,YAAA,CAAaoB,IAAA,CAAKS,IAAI,GAC3B,KAAKzB,KAAA,IAEE;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAQO2B,SAASN,KAAA,EAAeG,MAAA,GAAiB,MAChD;MACQ,KAAC,KAAK5B,YAAA,CAAae,MAAA,EAEZ;MAGX,MAAMc,IAAA,GAAO,IAAIC,YAAA,CAAaL,KAAA,EAAO,MAAM,MAAMG,MAAM;QAEjDI,SAAA,GAAY,KAAKhC,YAAA,CAAa,KAAKA,YAAA,CAAae,MAAA,GAAS,CAAC;MAE3D,OAAAc,IAAA,CAAAF,SAAA,GAAYK,SAAA,CAAUL,SAAA,EAE3BK,SAAA,CAAUC,KAAA,CAAMb,IAAA,CAAKS,IAAI,GAEzB,KAAKzB,KAAA,IAEE;IACX;IAAA;IAGO8B,QAAA,EACP;MACI,MAAMA,OAAA,CAAQ;MAGd,SAASlB,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKhB,YAAA,CAAae,MAAA,EAAQ,EAAEC,CAAA,EAEvC,KAAAhB,YAAA,CAAagB,CAAC,EAAEkB,OAAA,CAAQ;MAG5B,KAAAvC,MAAA,CAAOoB,MAAA,GAAS,GACrB,KAAKpB,MAAA,GAAS,MACd,KAAKC,MAAA,CAAOmB,MAAA,GAAS,GACrB,KAAKnB,MAAA,GAAS,MACd,KAAKC,GAAA,CAAIkB,MAAA,GAAS,GAClB,KAAKlB,GAAA,GAAM,MACX,KAAKC,OAAA,CAAQiB,MAAA,GAAS,GACtB,KAAKjB,OAAA,GAAU,MACf,KAAKqC,WAAA,CAAYD,OAAA,CAAQ,GACzB,KAAKC,WAAA,GAAc,MACnB,KAAKnC,YAAA,CAAae,MAAA,GAAS,GAC3B,KAAKf,YAAA,GAAe,MACpB,KAAKC,SAAA,CAAUc,MAAA,GAAS,GACxB,KAAKd,SAAA,GAAY,MACjB,KAAKE,OAAA,CAAQY,MAAA,GAAS,GACtB,KAAKZ,OAAA,GAAU,MACf,KAAKK,OAAA,GAAU;IACnB;IAAA;AAAA;AAAA;AAAA;AAAA;IAOO4B,cAAcC,KAAA,EACrB;MACI,MAAMrC,YAAA,GAAe,KAAKA,YAAA;MAE1B,SAASgB,CAAA,GAAI,GAAGA,CAAA,GAAIhB,YAAA,CAAae,MAAA,EAAQ,EAAEC,CAAA,EAC3C;QACU,MAAAa,IAAA,GAAO7B,YAAA,CAAagB,CAAC;QAEtB,IAAAa,IAAA,CAAKH,SAAA,CAAUY,OAAA,IAMhBT,IAAA,CAAKJ,KAAA,KAEDI,IAAA,CAAKD,MAAA,GAELC,IAAA,CAAKD,MAAA,CAAOW,YAAA,CAAaF,KAAA,EAAOrD,QAAQ,IAIxCA,QAAA,CAASwD,QAAA,CAASH,KAAK,GAGvBR,IAAA,CAAKJ,KAAA,CAAMgB,QAAA,CAASzD,QAAA,CAAS0D,CAAA,EAAG1D,QAAA,CAAS2D,CAAC,IAC9C;UACI,IAAIC,OAAA,GAAU;UAEd,IAAIf,IAAA,CAAKI,KAAA;YAEL,SAASY,EAAA,GAAI,GAAGA,EAAA,GAAIhB,IAAA,CAAKI,KAAA,CAAMlB,MAAA,EAAQ8B,EAAA,IAEtB,IAAAhB,IAAA,CAAKI,KAAA,CAAMY,EAAC,EAEhBpB,KAAA,CAAMgB,QAAA,CAASzD,QAAA,CAAS0D,CAAA,EAAG1D,QAAA,CAAS2D,CAAC,GAC9C;cACcC,OAAA;cACV;YACJ;UAAA;UAIR,IAAI,CAACA,OAAA,EAEM;QAEf;MAER;MAEO;IACX;IAAA;AAAA;AAAA;AAAA;IAMAhC,cAAA,EACA;MACQ,KAAC,KAAKZ,YAAA,CAAae,MAAA,EACvB;QACI,KAAKrB,SAAA,GAAY;QAEjB;MACJ;MAEI,KAAC,KAAKoD,gBAAA,CAAiB,GAEvB;MAGJ,KAAKzC,UAAA,GAAa,KAAKD,KAAA;MAEvB,MAAMP,GAAA,GAAM,KAAKA,GAAA;QACXG,YAAA,GAAe,KAAKA,YAAA;MAEtB,IAAAqB,SAAA,GAAuB;QAEvB0B,YAAA,GAAe;MAEf,KAAK5C,OAAA,CAAQY,MAAA,GAAS,MAEtBM,SAAA,GAAY,KAAKlB,OAAA,CAAQ,KAAKA,OAAA,CAAQY,MAAA,GAAS,CAAC,GAChDgC,YAAA,GAAe1B,SAAA,CAAU2B,KAAA;MAG7B,SAAShC,CAAA,GAAI,KAAKT,UAAA,EAAYS,CAAA,GAAIhB,YAAA,CAAae,MAAA,EAAQC,CAAA,IACvD;QACS,KAAAT,UAAA;QAEC,MAAAsB,IAAA,GAAO7B,YAAA,CAAagB,CAAC;UACrBU,SAAA,GAAYG,IAAA,CAAKH,SAAA;UACjBC,SAAA,GAAYE,IAAA,CAAKF,SAAA;QACPsB,aAAA,CAAcpB,IAAA,CAAKqB,IAAI,EAG/BC,KAAA,CAAMtB,IAAI,GAEdA,IAAA,CAAKD,MAAA,IAEL,KAAKwB,eAAA,CAAgBvB,IAAA,CAAKlC,MAAA,EAAQkC,IAAA,CAAKD,MAAM,IAG7CF,SAAA,CAAUY,OAAA,IAAWX,SAAA,CAAUW,OAAA,KAE/B,KAAKe,YAAA,CAAaxB,IAAA,CAAKI,KAAK;QAGhC,SAASqB,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IACvB;UACU,MAAAN,KAAA,GAASM,CAAA,KAAM,IAAK5B,SAAA,GAAYC,SAAA;UAEtC,IAAI,CAACqB,KAAA,CAAMV,OAAA,EAAS;UAEd,MAAAiB,WAAA,GAAcP,KAAA,CAAMQ,OAAA,CAAQC,WAAA;YAC5BC,MAAA,GAAQ,KAAK5D,OAAA,CAAQiB,MAAA;YACrB4C,WAAA,GAAc,KAAKhE,MAAA,CAAOoB,MAAA,GAAS;UAE7BwC,WAAA,CAAAK,QAAA,GAAWC,UAAA,CAAWC,MAAA,EAE9BR,CAAA,KAAM,IAEN,KAAKS,WAAA,CAAYlC,IAAI,IAIrB,KAAKmC,WAAA,CAAYnC,IAAI;UAGzB,MAAMoC,IAAA,GAAQ,KAAKtE,MAAA,CAAOoB,MAAA,GAAS,IAAK4C,WAAA;UAEpCM,IAAA,KAAS,MAET5C,SAAA,IAAa,CAAC,KAAK6C,cAAA,CAAenB,YAAA,EAAcC,KAAK,MAErD3B,SAAA,CAAU8C,GAAA,CAAIT,MAAA,EAAOC,WAAW,GAChCtC,SAAA,GAAY,OAGXA,SAAA,KAEDA,SAAA,GAAYE,UAAA,CAAW6C,GAAA,CAAI,KAAK,IAAIC,SAAA,IACpChD,SAAA,CAAUiD,KAAA,CAAMtB,KAAA,EAAOU,MAAA,EAAOC,WAAW,GACzC,KAAKxD,OAAA,CAAQiB,IAAA,CAAKC,SAAS,GAC3B0B,YAAA,GAAeC,KAAA,GAGnB,KAAKuB,MAAA,CAAO,KAAK5E,MAAA,EAAQE,GAAA,EAAKmD,KAAA,CAAMQ,OAAA,EAASG,WAAA,EAAaM,IAAA,EAAMjB,KAAA,CAAMpB,MAAM;QAChF;MACJ;MAEA,MAAM4C,KAAA,GAAQ,KAAK1E,OAAA,CAAQiB,MAAA;QACrB0D,MAAA,GAAS,KAAK9E,MAAA,CAAOoB,MAAA,GAAS;MAEhC,IAAAM,SAAA,IAEAA,SAAA,CAAU8C,GAAA,CAAIK,KAAA,EAAOC,MAAM,GAG3B,KAAKtE,OAAA,CAAQY,MAAA,KAAW,GAC5B;QAGI,KAAKrB,SAAA,GAAY;QAEjB;MACJ;MAEA,MAAMgF,MAAA,GAASD,MAAA,GAAS;MAGpB,KAAKhF,aAAA,IAAiB,KAAKK,OAAA,CAAQiB,MAAA,KAAW,KAAKtB,aAAA,CAAcsB,MAAA,IAC9D2D,MAAA,KAAY,KAAKjF,aAAA,CAAckF,iBAAA,GAAoB,IAEtD,KAAKlF,aAAA,CAAcmF,GAAA,CAAI,KAAK9E,OAAO,IAInC,KAAKL,aAAA,GAAgBiF,MAAA,GAAS,IAAIG,WAAA,CAAY,KAAK/E,OAAO,IAAI,IAAIgF,WAAA,CAAY,KAAKhF,OAAO,GAI9F,KAAKJ,SAAA,GAAY,KAAKqF,WAAA,IAElB,KAAKrF,SAAA,GAEL,KAAKsF,WAAA,CAAY,IAIjB,KAAKC,cAAA;IAEb;IAAA;AAAA;AAAA;AAAA;AAAA;IAOUf,eAAegB,MAAA,EAA+BC,MAAA,EACxD;MACQ,UAACD,MAAA,IAAU,CAACC,MAAA,IAKZD,MAAA,CAAO1B,OAAA,CAAQC,WAAA,KAAgB0B,MAAA,CAAO3B,OAAA,CAAQC,WAAA,IAK9CyB,MAAA,CAAOE,KAAA,GAAQF,MAAA,CAAOG,KAAA,KAAUF,MAAA,CAAOC,KAAA,GAAQD,MAAA,CAAOE,KAAA,IAKtD,CAAC,CAAEH,MAAA,CAAqBI,MAAA,IAAW,CAAC,CAAEH,MAAA,CAAqBG,MAAA;IAMnE;IAAA;IAGUxC,iBAAA,EACV;MACI,IAAI,KAAK1C,KAAA,KAAU,KAAKC,UAAA,IAAc,CAAC,KAAKL,YAAA,CAAae,MAAA,EAE9C;MAGF,SAAAC,CAAA,GAAI,GAAGuE,CAAA,GAAI,KAAKvF,YAAA,CAAae,MAAA,EAAQC,CAAA,GAAIuE,CAAA,EAAGvE,CAAA,IACrD;QACU,MAAAa,IAAA,GAAO,KAAK7B,YAAA,CAAagB,CAAC;UAC1BwE,IAAA,GAAO3D,IAAA,CAAKH,SAAA;UACZ+D,IAAA,GAAO5D,IAAA,CAAKF,SAAA;QAEd,IAAA6D,IAAA,IAAQ,CAACA,IAAA,CAAKhC,OAAA,CAAQC,WAAA,CAAYiC,KAAA,IAClCD,IAAA,IAAQ,CAACA,IAAA,CAAKjC,OAAA,CAAQC,WAAA,CAAYiC,KAAA,EAAc;MACxD;MAEO;IACX;IAAA;IAGUV,YAAA,EACV;MACI,KAAK9E,UAAA,IACL,KAAKV,UAAA,GAAa,IAAImG,YAAA,CAAa,KAAK9F,GAAG;MAE3C,MAAMM,OAAA,GAAU,KAAKA,OAAA;MAErB,SAASa,CAAA,GAAI,GAAGuE,CAAA,GAAIpF,OAAA,CAAQY,MAAA,EAAQC,CAAA,GAAIuE,CAAA,EAAGvE,CAAA,IAC3C;QACU,MAAA4E,KAAA,GAAQzF,OAAA,CAAQa,CAAC;QAEvB,SAASsC,CAAA,GAAI,GAAGA,CAAA,GAAIsC,KAAA,CAAM3B,IAAA,EAAMX,CAAA,IAChC;UACU,MAAAkB,KAAA,GAAQoB,KAAA,CAAMC,KAAA,GAAQvC,CAAA;UAE5B,KAAK7D,aAAA,CAAc+E,KAAK,IAAI,KAAK/E,aAAA,CAAc+E,KAAK,IAAIoB,KAAA,CAAME,WAAA;QAClE;MACJ;IACJ;IAAA;AAAA;AAAA;AAAA;IAMUf,YAAA,EACV;MAEQ,SAAKpF,MAAA,CAAOoB,MAAA,GAAS,QAAS,GAEvB;MAGX,MAAMZ,OAAA,GAAU,KAAKA,OAAA;MAErB,SAASa,CAAA,GAAI,GAAGA,CAAA,GAAIb,OAAA,CAAQY,MAAA,EAAQC,CAAA,IAE3B,IAAAb,OAAA,CAAQa,CAAC,EAAEgC,KAAA,CAAoBsC,MAAA,EAEzB;MAIf,OAAQ,KAAK3F,MAAA,CAAOoB,MAAA,GAAS5B,kBAAA,CAAiB4G,cAAA,GAAiB;IACnE;IAAA;IAGUd,eAAA,EACV;MACQ,IAAAe,IAAA,GAAO,EAAEC,WAAA,CAAYC,YAAA;MAEzB,SAASlF,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKf,SAAA,CAAUc,MAAA,EAAQC,CAAA,IAElC,KAAAf,SAAA,CAAUe,CAAC,EAAEC,QAAA,CAASC,KAAA,IAC3BC,cAAA,CAAeC,IAAA,CAAK,KAAKnB,SAAA,CAAUe,CAAC,CAAC;MAGzC,KAAKf,SAAA,CAAUc,MAAA,GAAS;MAExB,MAAMnB,MAAA,GAAS,KAAKA,MAAA;QACdG,UAAA,GAAa,KAAKA,UAAA;MAEpB,IAAAoG,YAAA,GAA8BhF,cAAA,CAAeiD,GAAA;MAE5C+B,YAAA,KAEDA,YAAA,GAAe,IAAIC,aAAA,IACnBD,YAAA,CAAalF,QAAA,GAAW,IAAIoF,iBAAA,CAEhC,IAAAF,YAAA,CAAalF,QAAA,CAASqF,KAAA,GAAQ,GAC9BH,YAAA,CAAaN,KAAA,GAAQ,GACrBM,YAAA,CAAalC,IAAA,GAAO,GACpBkC,YAAA,CAAajD,IAAA,GAAOqD,UAAA,CAAWC,SAAA;MAE3B,IAAAC,YAAA,GAAe;QACfC,cAAA,GAAiB;QACjBC,SAAA,GAAY;QACZrB,MAAA,GAAS;QACTsB,QAAA,GAAWL,UAAA,CAAWC,SAAA;QAEtBhC,KAAA,GAAQ;MAEP,KAAAvE,SAAA,CAAUmB,IAAA,CAAK+E,YAAY;MAGhC,SAASnF,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKb,OAAA,CAAQY,MAAA,EAAQC,CAAA,IACzC;QACI,MAAMa,IAAA,GAAO,KAAK1B,OAAA,CAAQa,CAAC;UAGrB6F,WAAA,GAAc;UAGd7D,KAAA,GAAQnB,IAAA,CAAKmB,KAAA;UAEbO,WAAA,GAAcP,KAAA,CAAMQ,OAAA,CAAQC,WAAA;QAE9B6B,MAAA,KAAW,CAAC,CAACtC,KAAA,CAAMsC,MAAA,KAEnBA,MAAA,GAAS,CAAC,CAACtC,KAAA,CAAMsC,MAAA,EACjBsB,QAAA,GAAWtB,MAAA,GAASiB,UAAA,CAAWO,KAAA,GAAQP,UAAA,CAAWC,SAAA,EAGlDE,cAAA,GAAiB,MACjBD,YAAA,GAAeI,WAAA,EACfb,IAAA,KAGAU,cAAA,KAAmBnD,WAAA,KAEnBmD,cAAA,GAAiBnD,WAAA,EAEbA,WAAA,CAAYwD,aAAA,KAAkBf,IAAA,KAE1BS,YAAA,KAAiBI,WAAA,KAEjBb,IAAA,IAEAS,YAAA,GAAe,GAEXN,YAAA,CAAalC,IAAA,GAAO,MAEpBkC,YAAA,GAAehF,cAAA,CAAeiD,GAAA,CACzB,GAAA+B,YAAA,KAEDA,YAAA,GAAe,IAAIC,aAAA,CAAc,GACjCD,YAAA,CAAalF,QAAA,GAAW,IAAIoF,iBAAA,KAEhC,KAAKpG,SAAA,CAAUmB,IAAA,CAAK+E,YAAY,IAGpCA,YAAA,CAAaN,KAAA,GAAQrB,KAAA,EACrB2B,YAAA,CAAalC,IAAA,GAAO,GACpBkC,YAAA,CAAalF,QAAA,CAASqF,KAAA,GAAQ,GAC9BH,YAAA,CAAajD,IAAA,GAAO0D,QAAA,GAKxBrD,WAAA,CAAYyD,OAAA,GAAU,GAEtBzD,WAAA,CAAYwD,aAAA,GAAgBf,IAAA,EAC5BzC,WAAA,CAAY0D,cAAA,GAAiBR,YAAA,EAC7BlD,WAAA,CAAYK,QAAA,GAAWC,UAAA,CAAWC,MAAA,EAElCqC,YAAA,CAAalF,QAAA,CAASiG,QAAA,CAASf,YAAA,CAAalF,QAAA,CAASqF,KAAA,EAAO,IAAI/C,WAAA,EAChEkD,YAAA,MAIRN,YAAA,CAAalC,IAAA,IAAQpC,IAAA,CAAKoC,IAAA,EAC1BO,KAAA,IAAS3C,IAAA,CAAKoC,IAAA,EAEd0C,SAAA,GAAYpD,WAAA,CAAY0D,cAAA,EAExB,KAAKE,SAAA,CAAUvH,MAAA,EAAQoD,KAAA,CAAMoC,KAAA,EAAOpC,KAAA,CAAMqC,KAAA,EAAOxD,IAAA,CAAKuF,UAAA,EAAYvF,IAAA,CAAKiE,WAAW,GAClF,KAAKuB,aAAA,CAActH,UAAA,EAAY4G,SAAA,EAAW9E,IAAA,CAAKuF,UAAA,EAAYvF,IAAA,CAAKiE,WAAW;MAC/E;MAEYG,WAAA,CAAAC,YAAA,GAAeF,IAAA,EAI3B,KAAKsB,cAAA,CAAe;IACxB;IAAA;IAGUA,eAAA,EACV;MACI,MAAMC,KAAA,GAAQ,KAAK5H,MAAA;QACbE,GAAA,GAAM,KAAKA,GAAA;QACXD,MAAA,GAAS,KAAKA,MAAA;QACdG,UAAA,GAAa,KAAKA,UAAA;QAGlByH,QAAA,GAAW,IAAIC,WAAA,CAAYF,KAAA,CAAMxG,MAAA,GAAS,IAAI,CAAC;QAC/C2G,GAAA,GAAM,IAAI/B,YAAA,CAAa6B,QAAQ;QAC/BG,GAAA,GAAM,IAAI9C,WAAA,CAAY2C,QAAQ;MAEpC,IAAII,CAAA,GAAI;MAER,SAAS5G,CAAA,GAAI,GAAGA,CAAA,GAAIuG,KAAA,CAAMxG,MAAA,GAAS,GAAGC,CAAA,IAElC0G,GAAA,CAAIE,CAAA,EAAG,IAAIL,KAAA,CAAMvG,CAAA,GAAI,CAAC,GACtB0G,GAAA,CAAIE,CAAA,EAAG,IAAIL,KAAA,CAAOvG,CAAA,GAAI,IAAK,CAAC,GAE5B0G,GAAA,CAAIE,CAAA,EAAG,IAAI/H,GAAA,CAAImB,CAAA,GAAI,CAAC,GACpB0G,GAAA,CAAIE,CAAA,EAAG,IAAI/H,GAAA,CAAKmB,CAAA,GAAI,IAAK,CAAC,GAE1B2G,GAAA,CAAIC,CAAA,EAAG,IAAIhI,MAAA,CAAOoB,CAAC,GAEnB0G,GAAA,CAAIE,CAAA,EAAG,IAAI7H,UAAA,CAAWiB,CAAC;MAGtB,KAAA6G,OAAA,CAAQC,MAAA,CAAON,QAAQ,GAC5B,KAAKO,YAAA,CAAaD,MAAA,CAAO,KAAKrI,aAAa;IAC/C;IAAA;AAAA;AAAA;AAAA;IAMUsE,YAAYlC,IAAA,EACtB;MACQA,IAAA,CAAKI,KAAA,CAAMlB,MAAA,GAEXiH,SAAA,CAAUC,WAAA,CAAYpG,IAAA,EAAM,IAAI,IAIhBoB,aAAA,CAAcpB,IAAA,CAAKqB,IAAI,EAE/B+E,WAAA,CAAYpG,IAAA,EAAM,IAAI;IAEtC;IAAA;AAAA;AAAA;AAAA;IAMUmC,YAAYnC,IAAA,EACtB;MACIqG,SAAA,CAAUrG,IAAA,EAAM,IAAI;MAEpB,SAASb,CAAA,GAAI,GAAGA,CAAA,GAAIa,IAAA,CAAKI,KAAA,CAAMlB,MAAA,EAAQC,CAAA,IAEnCkH,SAAA,CAAUrG,IAAA,CAAKI,KAAA,CAAMjB,CAAC,GAAG,IAAI;IAErC;IAAA;AAAA;AAAA;AAAA;IAMUqC,aAAapB,KAAA,EACvB;MACI,SAASjB,CAAA,GAAI,GAAGA,CAAA,GAAIiB,KAAA,CAAMlB,MAAA,EAAQC,CAAA,IAClC;QACU,MAAAmH,IAAA,GAAOlG,KAAA,CAAMjB,CAAC;QACJiC,aAAA,CAAckF,IAAA,CAAKjF,IAAI,EAE/BC,KAAA,CAAMgF,IAAI,GAEdA,IAAA,CAAKvG,MAAA,IAEL,KAAKwB,eAAA,CAAgB+E,IAAA,CAAKxI,MAAA,EAAQwI,IAAA,CAAKvG,MAAM;MAErD;IACJ;IAAA;IAGUf,gBAAA,EACV;MACI,MAAMF,MAAA,GAAS,KAAKH,OAAA;MAEpBG,MAAA,CAAOO,KAAA,IACPP,MAAA,CAAOyH,aAAA,CAAe,KAAKzI,MAAA,EAAgB,GAAG,KAAKA,MAAA,CAAOoB,MAAM,GAChEJ,MAAA,CAAO0H,GAAA,CAAI,KAAK9I,aAAA,EAAe,KAAKA,aAAa;IACrD;IAAA;AAAA;AAAA;AAAA;AAAA;IAOU6D,gBAAgBzD,MAAA,EAAuBiC,MAAA,EACjD;MACI,SAASZ,CAAA,GAAI,GAAGA,CAAA,GAAIrB,MAAA,CAAOoB,MAAA,GAAS,GAAGC,CAAA,IACvC;QACU,MAAA0B,CAAA,GAAI/C,MAAA,CAAQqB,CAAA,GAAI,CAAE;UAClB2B,CAAA,GAAIhD,MAAA,CAAQqB,CAAA,GAAI,IAAK,CAAC;QAEpBrB,MAAA,CAAAqB,CAAA,GAAI,CAAE,IAAKY,MAAA,CAAO0G,CAAA,GAAI5F,CAAA,GAAMd,MAAA,CAAO2G,CAAA,GAAI5F,CAAA,GAAKf,MAAA,CAAO4G,EAAA,EAC3D7I,MAAA,CAAQqB,CAAA,GAAI,IAAK,CAAC,IAAKY,MAAA,CAAO6G,CAAA,GAAI/F,CAAA,GAAMd,MAAA,CAAO8G,CAAA,GAAI/F,CAAA,GAAKf,MAAA,CAAO+G,EAAA;MACnE;IACJ;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAUUxB,UACNvH,MAAA,EACAwF,KAAA,EACAC,KAAA,EACApB,IAAA,EACA2E,MAAA,GAAS,GACb;MACI,MAAMC,GAAA,GAAMC,KAAA,CAAMC,MAAA,CACbC,QAAA,CAAS5D,KAAK,EACd6D,oBAAA,CAEC;QAAAC,MAAA,GAASJ,KAAA,CAAMC,MAAA,CAChBC,QAAA,CAASH,GAAG,EACZM,eAAA,CAAgB9D,KAAK;MAE1BzF,MAAA,CAAOmB,MAAA,GAASqI,IAAA,CAAKC,GAAA,CAAIzJ,MAAA,CAAOmB,MAAA,EAAQ6H,MAAA,GAAS3E,IAAI;MAE5C,SAAAjD,CAAA,GAAI,GAAGA,CAAA,GAAIiD,IAAA,EAAMjD,CAAA,IAEfpB,MAAA,CAAAgJ,MAAA,GAAS5H,CAAC,IAAIkI,MAAA;IAE7B;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IASU7B,cACNtH,UAAA,EACAuJ,EAAA,EACArF,IAAA,EACA2E,MAAA,GAAS,GACb;MACI7I,UAAA,CAAWgB,MAAA,GAASqI,IAAA,CAAKC,GAAA,CAAItJ,UAAA,CAAWgB,MAAA,EAAQ6H,MAAA,GAAS3E,IAAI;MAEpD,SAAAjD,CAAA,GAAI,GAAGA,CAAA,GAAIiD,IAAA,EAAMjD,CAAA,IAEXjB,UAAA,CAAA6I,MAAA,GAAS5H,CAAC,IAAIsI,EAAA;IAEjC;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAWU/E,OACNgD,KAAA,EACA1H,GAAA,EACA2D,OAAA,EACAqC,KAAA,EACA5B,IAAA,EACArC,MAAA,GAAiB,MACrB;MACI,IAAI4C,KAAA,GAAQ;MACZ,MAAM+E,QAAA,GAAW1J,GAAA,CAAIkB,MAAA;QACfyI,KAAA,GAAQhG,OAAA,CAAQgG,KAAA;MAEtB,OAAOhF,KAAA,GAAQP,IAAA,GACf;QACQ,IAAAvB,CAAA,GAAI6E,KAAA,EAAO1B,KAAA,GAAQrB,KAAA,IAAS,CAAC;UAC7B7B,CAAA,GAAI4E,KAAA,EAAQ1B,KAAA,GAAQrB,KAAA,IAAS,IAAK,CAAC;QAEvC,IAAI5C,MAAA,EACJ;UACI,MAAM6H,EAAA,GAAM7H,MAAA,CAAO0G,CAAA,GAAI5F,CAAA,GAAMd,MAAA,CAAO2G,CAAA,GAAI5F,CAAA,GAAKf,MAAA,CAAO4G,EAAA;UAE/C7F,CAAA,GAAAf,MAAA,CAAO6G,CAAA,GAAI/F,CAAA,GAAMd,MAAA,CAAO8G,CAAA,GAAI/F,CAAA,GAAKf,MAAA,CAAO+G,EAAA,EAC7CjG,CAAA,GAAI+G,EAAA;QACR;QAEAjF,KAAA,IAEA3E,GAAA,CAAIuB,IAAA,CAAKsB,CAAA,GAAI8G,KAAA,CAAME,KAAA,EAAO/G,CAAA,GAAI6G,KAAA,CAAMG,MAAM;MAC9C;MAEA,MAAMlG,WAAA,GAAcD,OAAA,CAAQC,WAAA;MAE5B,CAAI+F,KAAA,CAAME,KAAA,GAAQjG,WAAA,CAAYiG,KAAA,IACvBF,KAAA,CAAMG,MAAA,GAASlG,WAAA,CAAYkG,MAAA,KAE9B,KAAKC,SAAA,CAAU/J,GAAA,EAAK2D,OAAA,EAAS+F,QAAA,EAAUtF,IAAI;IAEnD;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAUU2F,UAAU/J,GAAA,EAAoB2D,OAAA,EAAkBqC,KAAA,EAAe5B,IAAA,EACzE;MACU,MAAAR,WAAA,GAAcD,OAAA,CAAQC,WAAA;QACtBoG,GAAA,GAAM;QACNC,MAAA,GAASjE,KAAA,GAAS5B,IAAA,GAAO;QACzBuF,KAAA,GAAQhG,OAAA,CAAQgG,KAAA;QAChBO,MAAA,GAASP,KAAA,CAAME,KAAA,GAAQjG,WAAA,CAAYiG,KAAA;QACnCM,MAAA,GAASR,KAAA,CAAMG,MAAA,GAASlG,WAAA,CAAYkG,MAAA;MACtC,IAAAM,OAAA,GAAUT,KAAA,CAAM9G,CAAA,GAAI8G,KAAA,CAAME,KAAA;QAC1BQ,OAAA,GAAUV,KAAA,CAAM7G,CAAA,GAAI6G,KAAA,CAAMG,MAAA;QAC1BQ,IAAA,GAAOf,IAAA,CAAKgB,KAAA,CAAMvK,GAAA,CAAIgG,KAAK,IAAIgE,GAAG;QAClCQ,IAAA,GAAOjB,IAAA,CAAKgB,KAAA,CAAMvK,GAAA,CAAIgG,KAAA,GAAQ,CAAC,IAAIgE,GAAG;MAE1C,SAAS7I,CAAA,GAAI6E,KAAA,GAAQ,GAAG7E,CAAA,GAAI8I,MAAA,EAAQ9I,CAAA,IAAK,GAE9BmJ,IAAA,GAAAf,IAAA,CAAKkB,GAAA,CAAIH,IAAA,EAAMf,IAAA,CAAKgB,KAAA,CAAMvK,GAAA,CAAImB,CAAC,IAAI6I,GAAG,CAAC,GAC9CQ,IAAA,GAAOjB,IAAA,CAAKkB,GAAA,CAAID,IAAA,EAAMjB,IAAA,CAAKgB,KAAA,CAAMvK,GAAA,CAAImB,CAAA,GAAI,CAAC,IAAI6I,GAAG,CAAC;MAEtDI,OAAA,IAAWE,IAAA,EACXD,OAAA,IAAWG,IAAA;MACX,SAASrJ,CAAA,GAAI6E,KAAA,EAAO7E,CAAA,GAAI8I,MAAA,EAAQ9I,CAAA,IAAK,GAEjCnB,GAAA,CAAImB,CAAC,KAAKnB,GAAA,CAAImB,CAAC,IAAIiJ,OAAA,IAAWF,MAAA,EAC9BlK,GAAA,CAAImB,CAAA,GAAI,CAAC,KAAKnB,GAAA,CAAImB,CAAA,GAAI,CAAC,IAAIkJ,OAAA,IAAWF,MAAA;IAE9C;EACJ;AAv3Ba9K,iBAAA,CAGK6G,cAAA,GAAiB;AAH5B,IAAMwE,gBAAA,GAANrL,iBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}