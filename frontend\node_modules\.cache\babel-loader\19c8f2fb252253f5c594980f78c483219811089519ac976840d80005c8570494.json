{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Projects\\\\Python\\\\EU4\\\\frontend\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport axios from \"axios\";\nimport WorldMap from './WorldMap';\n\n// Enhanced game constants\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WIN_CONDITIONS = {\n  treasury: 10000,\n  prestige: 100,\n  provinces: 10\n};\nconst LOSE_CONDITIONS = {\n  stability: 10,\n  treasury: -5000,\n  legitimacy: 20\n};\nexport default function App() {\n  _s();\n  // Core game state\n  const [countries, setCountries] = useState([]);\n  const [provinces, setProvinces] = useState([]);\n  const [playerCountry, setPlayerCountry] = useState(null);\n  const [turn, setTurn] = useState(1);\n  const [message, setMessage] = useState(\"\");\n  const [gamePhase, setGamePhase] = useState(\"loading\"); // loading, country_selection, playing, victory, defeat\n\n  // Enhanced game data\n  const [globalResources, setGlobalResources] = useState({});\n  const [technologies, setTechnologies] = useState({});\n  const [selectedProvince, setSelectedProvince] = useState(null);\n  const [activeTab, setActiveTab] = useState(\"overview\"); // overview, economy, military, diplomacy, technology\n\n  // UI state\n  const [actionTaken, setActionTaken] = useState(false);\n  const [event, setEvent] = useState(null);\n  const [notifications, setNotifications] = useState([]);\n\n  // Load initial game data\n  useEffect(() => {\n    const loadGameData = async () => {\n      try {\n        setGamePhase(\"loading\");\n\n        // Load all game data in parallel\n        const [countriesRes, provincesRes, resourcesRes, techRes] = await Promise.all([axios.get(\"http://localhost:8000/countries\"), axios.get(\"http://localhost:8000/provinces\"), axios.get(\"http://localhost:8000/resources\"), axios.get(\"http://localhost:8000/technologies\")]);\n        console.log(\"Game data loaded:\", {\n          countries: countriesRes.data.length,\n          provinces: provincesRes.data.length,\n          resources: Object.keys(resourcesRes.data).length,\n          technologies: Object.keys(techRes.data).length\n        });\n        setCountries(countriesRes.data);\n        setProvinces(provincesRes.data);\n        setGlobalResources(resourcesRes.data);\n        setTechnologies(techRes.data);\n        setGamePhase(\"country_selection\");\n      } catch (error) {\n        console.error(\"Failed to load game data:\", error);\n        setMessage(\"Failed to load game data. Please refresh the page.\");\n      }\n    };\n    loadGameData();\n  }, []);\n\n  // Fetch events when turn changes\n  useEffect(() => {\n    if (!playerCountry || gamePhase !== \"playing\") return;\n    const fetchTurnEvents = async () => {\n      try {\n        const eventRes = await axios.get(`http://localhost:8000/event?country=${encodeURIComponent(playerCountry.name)}`);\n        setEvent(eventRes.data);\n      } catch (error) {\n        console.error(\"Failed to fetch events:\", error);\n      }\n    };\n    fetchTurnEvents();\n  }, [turn, playerCountry, gamePhase]);\n\n  // Game phase handlers\n  const selectCountry = country => {\n    setPlayerCountry(country);\n    setGamePhase(\"playing\");\n    setActiveTab(\"overview\");\n    addNotification(`Welcome, ${country.ruler_name} of ${country.name}!`, \"success\");\n  };\n  const addNotification = (text, type = \"info\") => {\n    const notification = {\n      id: Date.now(),\n      text,\n      type,\n      timestamp: new Date().toLocaleTimeString()\n    };\n    setNotifications(prev => [notification, ...prev.slice(0, 4)]); // Keep only 5 notifications\n  };\n\n  // Enhanced turn advancement\n  const advanceTurn = async () => {\n    try {\n      setMessage(\"Processing turn...\");\n      const response = await axios.post(\"http://localhost:8000/turn\");\n\n      // Update all game state\n      setTurn(response.data.turn);\n      setCountries(response.data.countries);\n      setProvinces(response.data.provinces);\n      setGlobalResources(response.data.global_resources);\n\n      // Update player country reference\n      const updatedPlayerCountry = response.data.countries.find(c => c.name === playerCountry.name);\n      if (updatedPlayerCountry) {\n        setPlayerCountry(updatedPlayerCountry);\n      }\n      setActionTaken(false);\n      setMessage(\"\");\n      addNotification(`Turn ${response.data.turn} begins`, \"info\");\n\n      // Check win/lose conditions\n      checkGameEnd(updatedPlayerCountry);\n    } catch (error) {\n      console.error(\"Failed to advance turn:\", error);\n      setMessage(\"Failed to advance turn. Please try again.\");\n    }\n  };\n  const checkGameEnd = country => {\n    if (!country) return;\n\n    // Check victory conditions\n    if (country.treasury >= WIN_CONDITIONS.treasury || country.prestige >= WIN_CONDITIONS.prestige || country.provinces.length >= WIN_CONDITIONS.provinces) {\n      setGamePhase(\"victory\");\n      return;\n    }\n\n    // Check defeat conditions\n    if (country.stability <= LOSE_CONDITIONS.stability || country.treasury <= LOSE_CONDITIONS.treasury || country.legitimacy <= LOSE_CONDITIONS.legitimacy) {\n      setGamePhase(\"defeat\");\n      return;\n    }\n  };\n\n  // Research technology\n  const startResearch = async (category, techName) => {\n    try {\n      const response = await axios.post(\"http://localhost:8000/research\", {\n        country: playerCountry.name,\n        category,\n        technology: techName\n      });\n      if (response.data.success) {\n        addNotification(response.data.message, \"success\");\n        // Refresh country data\n        const countryRes = await axios.get(`http://localhost:8000/country/${playerCountry.name}`);\n        setPlayerCountry(countryRes.data);\n      } else {\n        addNotification(response.data.error, \"error\");\n      }\n    } catch (error) {\n      console.error(\"Research failed:\", error);\n      addNotification(\"Failed to start research\", \"error\");\n    }\n  };\n\n  // Tab content renderer\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'overview':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83C\\uDFDB\\uFE0F Nation Overview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '20px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#2d2d44',\n                padding: '15px',\n                borderRadius: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\uD83D\\uDCB0 Economy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Monthly Income:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 20\n                }, this), \" \", playerCountry.monthly_income.toFixed(1), \" gold\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Monthly Expenses:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 20\n                }, this), \" \", playerCountry.monthly_expenses.toFixed(1), \" gold\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Net Income:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 20\n                }, this), \" \", (playerCountry.monthly_income - playerCountry.monthly_expenses).toFixed(1), \" gold\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Inflation:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 20\n                }, this), \" \", (playerCountry.inflation * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#2d2d44',\n                padding: '15px',\n                borderRadius: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u2694\\uFE0F Military\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Army Size:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 20\n                }, this), \" \", playerCountry.army_size.toLocaleString(), \" troops\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Navy Size:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 20\n                }, this), \" \", playerCountry.navy_size, \" ships\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Military Tradition:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 20\n                }, this), \" \", playerCountry.military_tradition.toFixed(1)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"War Exhaustion:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 20\n                }, this), \" \", playerCountry.war_exhaustion.toFixed(1)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"\\uD83C\\uDFDB\\uFE0F Provinces (\", playerCountry.provinces.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                gap: '10px'\n              },\n              children: provinces.filter(p => p.owner === playerCountry.name).map(province => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#1a1a2e',\n                  padding: '10px',\n                  borderRadius: '5px',\n                  cursor: 'pointer',\n                  border: (selectedProvince === null || selectedProvince === void 0 ? void 0 : selectedProvince.name) === province.name ? '2px solid #4a9eff' : '1px solid #444'\n                },\n                onClick: () => setSelectedProvince(province),\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: province.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [\"Dev: \", province.development.toFixed(1), \" | Pop: \", province.population_groups.reduce((sum, pop) => sum + pop.size, 0).toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 21\n                }, this)]\n              }, province.name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), event && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDCF0 Current Event\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#1a1a2e',\n                padding: '15px',\n                borderRadius: '5px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: event.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: event.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#4a9eff'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Effect:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 51\n                }, this), \" \", event.effect]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  // Apply event logic here\n                  addNotification(`Applied: ${event.effect}`, \"success\");\n                  setEvent(null);\n                },\n                style: {\n                  background: '#4a9eff',\n                  color: '#fff',\n                  border: 'none',\n                  padding: '8px 15px',\n                  borderRadius: '5px',\n                  cursor: 'pointer'\n                },\n                children: \"Accept\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this);\n      case 'population':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDC65 Population Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDCCA Population Summary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',\n                gap: '15px'\n              },\n              children: provinces.filter(p => p.owner === playerCountry.name).map(province => {\n                const totalPop = province.population_groups.reduce((sum, pop) => sum + pop.size, 0);\n                const avgHappiness = province.population_groups.reduce((sum, pop) => sum + pop.happiness, 0) / province.population_groups.length;\n                const avgMilitancy = province.population_groups.reduce((sum, pop) => sum + pop.militancy, 0) / province.population_groups.length;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: '#1a1a2e',\n                    padding: '12px',\n                    borderRadius: '5px',\n                    cursor: 'pointer',\n                    border: (selectedProvince === null || selectedProvince === void 0 ? void 0 : selectedProvince.name) === province.name ? '2px solid #4a9eff' : '1px solid #444'\n                  },\n                  onClick: () => setSelectedProvince(province),\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    style: {\n                      margin: '0 0 8px 0'\n                    },\n                    children: province.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '2px 0',\n                      fontSize: '0.9rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Population:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 290,\n                      columnNumber: 25\n                    }, this), \" \", totalPop.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '2px 0',\n                      fontSize: '0.9rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Happiness:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 25\n                    }, this), \" \", avgHappiness.toFixed(1), \"/10\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '2px 0',\n                      fontSize: '0.9rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Unrest:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 296,\n                      columnNumber: 25\n                    }, this), \" \", province.unrest.toFixed(1)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: avgHappiness > 6 ? '#51cf66' : avgHappiness > 4 ? '#ffd43b' : '#ff6b6b',\n                      height: '4px',\n                      borderRadius: '2px',\n                      marginTop: '8px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 23\n                  }, this)]\n                }, province.name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), selectedProvince && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"\\uD83C\\uDFD8\\uFE0F \", selectedProvince.name, \" - Population Details\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                gap: '15px'\n              },\n              children: selectedProvince.population_groups.map((popGroup, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#1a1a2e',\n                  padding: '15px',\n                  borderRadius: '5px',\n                  border: '1px solid #444'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 10px 0',\n                    textTransform: 'capitalize'\n                  },\n                  children: popGroup.social_class.replace('_', ' ')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.9rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Size:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 28\n                    }, this), \" \", popGroup.size.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Culture:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 28\n                    }, this), \" \", popGroup.culture]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Religion:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 28\n                    }, this), \" \", popGroup.religion]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Profession:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 28\n                    }, this), \" \", popGroup.profession]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Wealth:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 28\n                    }, this), \" \", popGroup.wealth.toFixed(1)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '10px 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        marginBottom: '5px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Happiness:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 339,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: popGroup.happiness > 6 ? '#51cf66' : popGroup.happiness > 4 ? '#ffd43b' : '#ff6b6b'\n                        },\n                        children: [popGroup.happiness.toFixed(1), \"/10\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 340,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: '#333',\n                        height: '6px',\n                        borderRadius: '3px',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          background: popGroup.happiness > 6 ? '#51cf66' : popGroup.happiness > 4 ? '#ffd43b' : '#ff6b6b',\n                          height: '100%',\n                          width: `${popGroup.happiness / 10 * 100}%`,\n                          transition: 'width 0.3s ease'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 350,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 344,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '10px 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        marginBottom: '5px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Militancy:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 361,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: popGroup.militancy > 6 ? '#ff6b6b' : popGroup.militancy > 3 ? '#ffd43b' : '#51cf66'\n                        },\n                        children: [popGroup.militancy.toFixed(1), \"/10\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 362,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: '#333',\n                        height: '6px',\n                        borderRadius: '3px',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          background: popGroup.militancy > 6 ? '#ff6b6b' : popGroup.militancy > 3 ? '#ffd43b' : '#51cf66',\n                          height: '100%',\n                          width: `${popGroup.militancy / 10 * 100}%`,\n                          transition: 'width 0.3s ease'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 372,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 366,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '10px 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        marginBottom: '5px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Consciousness:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 383,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: '#4a9eff'\n                        },\n                        children: [popGroup.consciousness.toFixed(1), \"/10\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 384,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 382,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: '#333',\n                        height: '6px',\n                        borderRadius: '3px',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          background: '#4a9eff',\n                          height: '100%',\n                          width: `${popGroup.consciousness / 10 * 100}%`,\n                          transition: 'width 0.3s ease'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 394,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 388,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\uD83D\\uDC51 Population Policies\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                  gap: '10px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    if (!actionTaken) {\n                      addNotification(\"Implemented education reforms\", \"success\");\n                      setActionTaken(true);\n                    }\n                  },\n                  disabled: actionTaken,\n                  style: {\n                    background: actionTaken ? '#666' : '#4a9eff',\n                    color: '#fff',\n                    border: 'none',\n                    padding: '12px',\n                    borderRadius: '5px',\n                    cursor: actionTaken ? 'not-allowed' : 'pointer'\n                  },\n                  children: [\"\\uD83D\\uDCDA Education Reform\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 42\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: \"Increase consciousness\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    if (!actionTaken) {\n                      addNotification(\"Promoted cultural integration\", \"success\");\n                      setActionTaken(true);\n                    }\n                  },\n                  disabled: actionTaken,\n                  style: {\n                    background: actionTaken ? '#666' : '#51cf66',\n                    color: '#fff',\n                    border: 'none',\n                    padding: '12px',\n                    borderRadius: '5px',\n                    cursor: actionTaken ? 'not-allowed' : 'pointer'\n                  },\n                  children: [\"\\uD83E\\uDD1D Cultural Integration\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 46\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: \"Reduce cultural tensions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    if (!actionTaken) {\n                      addNotification(\"Implemented welfare programs\", \"success\");\n                      setActionTaken(true);\n                    }\n                  },\n                  disabled: actionTaken,\n                  style: {\n                    background: actionTaken ? '#666' : '#ffd43b',\n                    color: '#000',\n                    border: 'none',\n                    padding: '12px',\n                    borderRadius: '5px',\n                    cursor: actionTaken ? 'not-allowed' : 'pointer'\n                  },\n                  children: [\"\\uD83C\\uDFE5 Welfare Programs\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 42\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: \"Increase happiness\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83C\\uDF0D National Demographics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this), (() => {\n              const allPops = provinces.filter(p => p.owner === playerCountry.name).flatMap(p => p.population_groups);\n              const totalPop = allPops.reduce((sum, pop) => sum + pop.size, 0);\n              const classCounts = {};\n              const cultureCounts = {};\n              const religionCounts = {};\n              allPops.forEach(pop => {\n                classCounts[pop.social_class] = (classCounts[pop.social_class] || 0) + pop.size;\n                cultureCounts[pop.culture] = (cultureCounts[pop.culture] || 0) + pop.size;\n                religionCounts[pop.religion] = (religionCounts[pop.religion] || 0) + pop.size;\n              });\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                  gap: '20px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"\\uD83D\\uDC51 Social Classes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 23\n                  }, this), Object.entries(classCounts).map(([className, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '8px 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          textTransform: 'capitalize'\n                        },\n                        children: className.replace('_', ' ')\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 505,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [(count / totalPop * 100).toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 506,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 504,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: '#333',\n                        height: '6px',\n                        borderRadius: '3px',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          background: '#4a9eff',\n                          height: '100%',\n                          width: `${count / totalPop * 100}%`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 509,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 508,\n                      columnNumber: 27\n                    }, this)]\n                  }, className, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"\\uD83C\\uDFDB\\uFE0F Cultures\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 23\n                  }, this), Object.entries(cultureCounts).map(([culture, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '8px 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          textTransform: 'capitalize'\n                        },\n                        children: culture\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 524,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [(count / totalPop * 100).toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 525,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 523,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: '#333',\n                        height: '6px',\n                        borderRadius: '3px',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          background: '#51cf66',\n                          height: '100%',\n                          width: `${count / totalPop * 100}%`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 528,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 527,\n                      columnNumber: 27\n                    }, this)]\n                  }, culture, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"\\u26EA Religions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 23\n                  }, this), Object.entries(religionCounts).map(([religion, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: '8px 0'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          textTransform: 'capitalize'\n                        },\n                        children: religion\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 543,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [(count / totalPop * 100).toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 544,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 542,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        background: '#333',\n                        height: '6px',\n                        borderRadius: '3px',\n                        overflow: 'hidden'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          background: '#ffd43b',\n                          height: '100%',\n                          width: `${count / totalPop * 100}%`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 547,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 546,\n                      columnNumber: 27\n                    }, this)]\n                  }, religion, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 19\n              }, this);\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this);\n      case 'economy':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDCB0 Economic Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDCCA Financial Summary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Treasury:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 22\n                  }, this), \" \", playerCountry.treasury.toLocaleString(), \" gold\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Monthly Income:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 573,\n                    columnNumber: 22\n                  }, this), \" +\", playerCountry.monthly_income.toFixed(1), \" gold\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Monthly Expenses:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 574,\n                    columnNumber: 22\n                  }, this), \" -\", playerCountry.monthly_expenses.toFixed(1), \" gold\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Net Balance:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 22\n                  }, this), \" \", (playerCountry.monthly_income - playerCountry.monthly_expenses).toFixed(1), \" gold\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Trade Efficiency:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 22\n                  }, this), \" \", (playerCountry.trade_efficiency * 100).toFixed(1), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Inflation Rate:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 579,\n                    columnNumber: 22\n                  }, this), \" \", (playerCountry.inflation * 100).toFixed(1), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83C\\uDF0D Global Resource Prices\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',\n                gap: '10px'\n              },\n              children: Object.entries(globalResources).map(([name, resource]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#1a1a2e',\n                  padding: '10px',\n                  borderRadius: '5px',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: name.replace('_', ' ')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: '#4a9eff'\n                  },\n                  children: [resource.current_price.toFixed(2), \" gold\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  style: {\n                    opacity: 0.7\n                  },\n                  children: resource.current_price > resource.base_value ? '📈' : '📉'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 21\n                }, this)]\n              }, name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83C\\uDFED Economic Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                gap: '15px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  if (!actionTaken) {\n                    addNotification(\"Invested in infrastructure development\", \"success\");\n                    setActionTaken(true);\n                  }\n                },\n                disabled: actionTaken,\n                style: {\n                  background: actionTaken ? '#666' : '#4a9eff',\n                  color: '#fff',\n                  border: 'none',\n                  padding: '15px',\n                  borderRadius: '5px',\n                  cursor: actionTaken ? 'not-allowed' : 'pointer'\n                },\n                children: [\"\\uD83C\\uDFD7\\uFE0F Build Infrastructure\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 43\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"Cost: 200 gold\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  if (!actionTaken) {\n                    addNotification(\"Promoted trade and commerce\", \"success\");\n                    setActionTaken(true);\n                  }\n                },\n                disabled: actionTaken,\n                style: {\n                  background: actionTaken ? '#666' : '#51cf66',\n                  color: '#fff',\n                  border: 'none',\n                  padding: '15px',\n                  borderRadius: '5px',\n                  cursor: actionTaken ? 'not-allowed' : 'pointer'\n                },\n                children: [\"\\uD83C\\uDFEA Promote Trade\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 35\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"Cost: 150 gold\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  if (!actionTaken) {\n                    addNotification(\"Implemented tax reforms\", \"success\");\n                    setActionTaken(true);\n                  }\n                },\n                disabled: actionTaken,\n                style: {\n                  background: actionTaken ? '#666' : '#ffd43b',\n                  color: '#000',\n                  border: 'none',\n                  padding: '15px',\n                  borderRadius: '5px',\n                  cursor: actionTaken ? 'not-allowed' : 'pointer'\n                },\n                children: [\"\\uD83D\\uDCCB Tax Reform\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 663,\n                  columnNumber: 32\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"Cost: 100 gold\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 11\n        }, this);\n      case 'technology':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDD2C Technology & Research\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDCDA Research Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                gap: '15px'\n              },\n              children: Object.entries(playerCountry.research_points).map(([category, points]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#1a1a2e',\n                  padding: '15px',\n                  borderRadius: '5px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    textTransform: 'capitalize',\n                    margin: '0 0 10px 0'\n                  },\n                  children: [category === 'military' && '⚔️', category === 'economic' && '💰', category === 'social' && '👥', category === 'administrative' && '🏛️', ' ' + category]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Points:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 688,\n                    columnNumber: 24\n                  }, this), \" \", points.toFixed(1)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 688,\n                  columnNumber: 21\n                }, this), playerCountry.current_research[category] && /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Researching:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 690,\n                    columnNumber: 26\n                  }, this), \" \", playerCountry.current_research[category]]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 23\n                }, this)]\n              }, category, true, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '15px',\n              borderRadius: '8px',\n              marginBottom: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83E\\uDDEA Available Technologies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                gap: '15px'\n              },\n              children: Object.entries(technologies).map(([name, tech]) => {\n                const isResearched = playerCountry.technologies.includes(name);\n                const canResearch = tech.prerequisites.every(prereq => playerCountry.technologies.includes(prereq));\n                const isCurrentlyResearching = Object.values(playerCountry.current_research).includes(name);\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: isResearched ? '#2d5a2d' : '#1a1a2e',\n                    padding: '15px',\n                    borderRadius: '5px',\n                    border: isCurrentlyResearching ? '2px solid #4a9eff' : '1px solid #444',\n                    opacity: canResearch || isResearched ? 1 : 0.5\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    style: {\n                      margin: '0 0 10px 0'\n                    },\n                    children: name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 716,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      fontSize: '0.9rem',\n                      margin: '5px 0'\n                    },\n                    children: tech.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Category:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 718,\n                      columnNumber: 26\n                    }, this), \" \", tech.category]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 718,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Cost:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 719,\n                      columnNumber: 26\n                    }, this), \" \", tech.cost, \" points\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 719,\n                    columnNumber: 23\n                  }, this), tech.prerequisites.length > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Prerequisites:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 722,\n                      columnNumber: 28\n                    }, this), \" \", tech.prerequisites.join(', ')]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 25\n                  }, this), isResearched && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: '#51cf66',\n                      fontWeight: 'bold'\n                    },\n                    children: \"\\u2705 Researched\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 726,\n                    columnNumber: 25\n                  }, this), !isResearched && canResearch && !isCurrentlyResearching && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => startResearch(tech.category, name),\n                    style: {\n                      background: '#4a9eff',\n                      color: '#fff',\n                      border: 'none',\n                      padding: '8px 15px',\n                      borderRadius: '5px',\n                      cursor: 'pointer',\n                      marginTop: '10px'\n                    },\n                    children: \"Start Research\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 730,\n                    columnNumber: 25\n                  }, this), isCurrentlyResearching && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: '#ffd43b',\n                      fontWeight: 'bold'\n                    },\n                    children: \"\\uD83D\\uDD2C Researching...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 747,\n                    columnNumber: 25\n                  }, this)]\n                }, name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDEA7 Coming Soon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"This feature is under development and will be available in future updates.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 761,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 11\n        }, this);\n    }\n  };\n\n  // Game phase rendering\n  if (gamePhase === \"loading\") {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        background: '#1a1a2e'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          color: '#fff'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83C\\uDFF0 Empires & Revolutions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 772,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading the world of Aeterra...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            margin: '20px 0'\n          },\n          children: \"\\u2694\\uFE0F \\uD83C\\uDFDB\\uFE0F \\uD83D\\uDCB0 \\uD83D\\uDD2C \\uD83C\\uDF0D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 774,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 771,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 770,\n      columnNumber: 7\n    }, this);\n  }\n  if (gamePhase === \"country_selection\") {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#1a1a2e',\n        minHeight: '100vh',\n        color: '#fff'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83C\\uDFF0 Choose Your Destiny\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 784,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Select a nation to lead through the Age of Revolutions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 785,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 783,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(WorldMap, {\n        onSelectCountry: selectCountry,\n        selectedCountry: null,\n        countries: countries\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 788,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '20px',\n          maxWidth: '1200px',\n          margin: '0 auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Available Nations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 795,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n            gap: '20px'\n          },\n          children: countries.slice(0, 5).map(country => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#2d2d44',\n              padding: '20px',\n              borderRadius: '10px',\n              cursor: 'pointer',\n              border: '2px solid transparent',\n              transition: 'all 0.3s ease'\n            },\n            onMouseEnter: e => e.target.style.borderColor = '#4a9eff',\n            onMouseLeave: e => e.target.style.borderColor = 'transparent',\n            onClick: () => selectCountry(country),\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: country.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 812,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ruler:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 20\n              }, this), \" \", country.ruler_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Government:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 814,\n                columnNumber: 20\n              }, this), \" \", country.government_type.replace('_', ' ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 814,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Treasury:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 815,\n                columnNumber: 20\n              }, this), \" \", country.treasury.toLocaleString(), \" gold\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Provinces:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 20\n              }, this), \" \", country.provinces.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 816,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Prestige:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 817,\n                columnNumber: 20\n              }, this), \" \", country.prestige]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 17\n            }, this)]\n          }, country.name, true, {\n            fileName: _jsxFileName,\n            lineNumber: 798,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 796,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 794,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 782,\n      columnNumber: 7\n    }, this);\n  }\n  if (gamePhase === \"victory\") {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        minHeight: '100vh',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        color: '#fff',\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            fontSize: '4rem',\n            marginBottom: '20px'\n          },\n          children: \"\\uD83C\\uDF89 VICTORY! \\uD83C\\uDF89\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 838,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"The \", playerCountry.name, \" Triumphant!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 839,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '1.5rem',\n            margin: '20px 0'\n          },\n          children: [\"Under the wise rule of \", playerCountry.ruler_name, \", your nation has achieved greatness!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 840,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'rgba(0,0,0,0.3)',\n            padding: '20px',\n            borderRadius: '10px',\n            margin: '20px 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Final Treasury:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 844,\n              columnNumber: 16\n            }, this), \" \", playerCountry.treasury.toLocaleString(), \" gold\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 844,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Final Prestige:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 16\n            }, this), \" \", playerCountry.prestige]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 845,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Provinces Controlled:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 16\n            }, this), \" \", playerCountry.provinces.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 846,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Turns Survived:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 16\n            }, this), \" \", turn]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 847,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 843,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          style: {\n            padding: '15px 30px',\n            fontSize: '1.2rem',\n            background: '#4a9eff',\n            color: '#fff',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: 'pointer'\n          },\n          children: \"Play Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 849,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 837,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 828,\n      columnNumber: 7\n    }, this);\n  }\n  if (gamePhase === \"defeat\") {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',\n        minHeight: '100vh',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        color: '#fff',\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            fontSize: '4rem',\n            marginBottom: '20px'\n          },\n          children: \"\\uD83D\\uDC80 DEFEAT \\uD83D\\uDC80\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 880,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"The Fall of \", playerCountry.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 881,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '1.5rem',\n            margin: '20px 0'\n          },\n          children: [\"The reign of \", playerCountry.ruler_name, \" has come to an end...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 882,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'rgba(0,0,0,0.3)',\n            padding: '20px',\n            borderRadius: '10px',\n            margin: '20px 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Final Treasury:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 886,\n              columnNumber: 16\n            }, this), \" \", playerCountry.treasury.toLocaleString(), \" gold\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 886,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Final Stability:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 887,\n              columnNumber: 16\n            }, this), \" \", playerCountry.stability]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 887,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Final Legitimacy:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 16\n            }, this), \" \", playerCountry.legitimacy]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 888,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Turns Survived:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 889,\n              columnNumber: 16\n            }, this), \" \", turn]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 889,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 885,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          style: {\n            padding: '15px 30px',\n            fontSize: '1.2rem',\n            background: '#4a9eff',\n            color: '#fff',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: 'pointer'\n          },\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 891,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 879,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 870,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Main game interface - sophisticated tabbed layout\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: '#1a1a2e',\n      minHeight: '100vh',\n      color: '#fff'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#2d2d44',\n        padding: '15px 20px',\n        borderBottom: '2px solid #4a9eff',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: 0,\n            fontSize: '1.8rem'\n          },\n          children: [\"\\uD83C\\uDFF0 \", playerCountry.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 923,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            opacity: 0.8\n          },\n          children: [playerCountry.ruler_name, \" \\u2022 \", playerCountry.government_type.replace('_', ' ')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 924,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 922,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '20px',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '1.2rem',\n              fontWeight: 'bold'\n            },\n            children: [\"\\uD83D\\uDCB0 \", playerCountry.treasury.toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 929,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.8rem',\n              opacity: 0.7\n            },\n            children: \"Treasury\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 928,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '1.2rem',\n              fontWeight: 'bold'\n            },\n            children: [\"\\u2B50 \", playerCountry.prestige]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 933,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.8rem',\n              opacity: 0.7\n            },\n            children: \"Prestige\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 932,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '1.2rem',\n              fontWeight: 'bold'\n            },\n            children: [\"\\uD83C\\uDFDB\\uFE0F \", playerCountry.stability]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.8rem',\n              opacity: 0.7\n            },\n            children: \"Stability\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 938,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 936,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '1.2rem',\n              fontWeight: 'bold'\n            },\n            children: [\"\\uD83D\\uDC51 \", playerCountry.legitimacy]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 941,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.8rem',\n              opacity: 0.7\n            },\n            children: \"Legitimacy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 942,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 940,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '1.2rem',\n              fontWeight: 'bold'\n            },\n            children: [\"\\uD83D\\uDCC5 \", turn]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 945,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.8rem',\n              opacity: 0.7\n            },\n            children: \"Turn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 946,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 944,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 927,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 914,\n      columnNumber: 7\n    }, this), notifications.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '10px 20px'\n      },\n      children: notifications.map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: notification.type === 'error' ? '#ff6b6b' : notification.type === 'success' ? '#51cf66' : '#4a9eff',\n          padding: '8px 15px',\n          margin: '5px 0',\n          borderRadius: '5px',\n          fontSize: '0.9rem',\n          display: 'flex',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: notification.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 968,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            opacity: 0.7\n          },\n          children: notification.timestamp\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 969,\n          columnNumber: 15\n        }, this)]\n      }, notification.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 955,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 953,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#2d2d44',\n        padding: '0 20px',\n        borderBottom: '1px solid #444'\n      },\n      children: ['overview', 'population', 'economy', 'military', 'diplomacy', 'technology'].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setActiveTab(tab),\n        style: {\n          background: activeTab === tab ? '#4a9eff' : 'transparent',\n          color: '#fff',\n          border: 'none',\n          padding: '15px 25px',\n          margin: '0 5px',\n          cursor: 'pointer',\n          borderRadius: '5px 5px 0 0',\n          textTransform: 'capitalize',\n          fontSize: '1rem'\n        },\n        children: [tab === 'overview' && '🏛️', tab === 'population' && '👥', tab === 'economy' && '💰', tab === 'military' && '⚔️', tab === 'diplomacy' && '🤝', tab === 'technology' && '🔬', ' ' + tab]\n      }, tab, true, {\n        fileName: _jsxFileName,\n        lineNumber: 982,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 976,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        height: 'calc(100vh - 200px)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: '1',\n          background: '#16213e',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(WorldMap, {\n          onSelectCountry: () => {},\n          selectedCountry: playerCountry,\n          countries: countries,\n          onSelectProvince: setSelectedProvince,\n          selectedProvince: selectedProvince\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1012,\n          columnNumber: 11\n        }, this), selectedProvince && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#2d2d44',\n            padding: '15px',\n            margin: '10px 0',\n            borderRadius: '8px',\n            maxHeight: '200px',\n            overflowY: 'auto'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: selectedProvince.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1029,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Owner:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1030,\n              columnNumber: 18\n            }, this), \" \", selectedProvince.owner]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1030,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Development:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1031,\n              columnNumber: 18\n            }, this), \" \", selectedProvince.development.toFixed(1)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1031,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Population:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1032,\n              columnNumber: 18\n            }, this), \" \", selectedProvince.population_groups.reduce((sum, pop) => sum + pop.size, 0).toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1032,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Terrain:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1033,\n              columnNumber: 18\n            }, this), \" \", selectedProvince.terrain]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1033,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Unrest:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1034,\n              columnNumber: 18\n            }, this), \" \", selectedProvince.unrest.toFixed(1)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1034,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1021,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1011,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: '1',\n          background: '#1a1a2e',\n          padding: '20px',\n          overflowY: 'auto'\n        },\n        children: renderTabContent()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1040,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1009,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#2d2d44',\n        padding: '15px 20px',\n        borderTop: '2px solid #4a9eff',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: message && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#ffd43b',\n            fontSize: '1rem'\n          },\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1056,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1054,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: advanceTurn,\n        disabled: actionTaken,\n        style: {\n          background: actionTaken ? '#666' : '#4a9eff',\n          color: '#fff',\n          border: 'none',\n          padding: '12px 30px',\n          fontSize: '1.1rem',\n          borderRadius: '5px',\n          cursor: actionTaken ? 'not-allowed' : 'pointer',\n          fontWeight: 'bold'\n        },\n        children: actionTaken ? '⏳ Processing...' : '▶️ End Turn'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1060,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1046,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 912,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"QLNuEcAL5/sindYHeWEA8FmzQY8=\");\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "WorldMap", "jsxDEV", "_jsxDEV", "WIN_CONDITIONS", "treasury", "prestige", "provinces", "LOSE_CONDITIONS", "stability", "legitimacy", "App", "_s", "countries", "setCountries", "setProvinces", "playerCountry", "setPlayerCountry", "turn", "setTurn", "message", "setMessage", "gamePhase", "setGamePhase", "globalResources", "setGlobalResources", "technologies", "setTechnologies", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedProvince", "activeTab", "setActiveTab", "actionTaken", "setActionTaken", "event", "setEvent", "notifications", "setNotifications", "loadGameData", "countriesRes", "provincesRes", "resourcesRes", "techRes", "Promise", "all", "get", "console", "log", "data", "length", "resources", "Object", "keys", "error", "fetchTurnEvents", "eventRes", "encodeURIComponent", "name", "selectCountry", "country", "addNotification", "ruler_name", "text", "type", "notification", "id", "Date", "now", "timestamp", "toLocaleTimeString", "prev", "slice", "advanceTurn", "response", "post", "global_resources", "updatedPlayerCountry", "find", "c", "checkGameEnd", "startResearch", "category", "techName", "technology", "success", "countryRes", "renderTabContent", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "gridTemplateColumns", "gap", "marginBottom", "background", "padding", "borderRadius", "monthly_income", "toFixed", "monthly_expenses", "inflation", "army_size", "toLocaleString", "navy_size", "military_tradition", "war_exhaustion", "filter", "p", "owner", "map", "province", "cursor", "border", "onClick", "development", "population_groups", "reduce", "sum", "pop", "size", "title", "description", "color", "effect", "totalPop", "avgHappiness", "happiness", "avgMilitancy", "militancy", "margin", "fontSize", "unrest", "height", "marginTop", "popGroup", "index", "textTransform", "social_class", "replace", "culture", "religion", "profession", "wealth", "justifyContent", "overflow", "width", "transition", "consciousness", "disabled", "allPops", "flatMap", "classCounts", "cultureCounts", "religionCounts", "for<PERSON>ach", "entries", "className", "count", "trade_efficiency", "resource", "textAlign", "current_price", "opacity", "base_value", "research_points", "points", "current_research", "tech", "isResearched", "includes", "canResearch", "prerequisites", "every", "prereq", "isCurrentlyResearching", "values", "cost", "join", "fontWeight", "alignItems", "minHeight", "onSelectCountry", "selectedCountry", "max<PERSON><PERSON><PERSON>", "onMouseEnter", "e", "target", "borderColor", "onMouseLeave", "government_type", "window", "location", "reload", "borderBottom", "tab", "flex", "onSelectProvince", "maxHeight", "overflowY", "terrain", "borderTop", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Projects/Python/EU4/frontend/src/App.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport axios from \"axios\";\r\nimport WorldMap from './WorldMap';\r\n\r\n// Enhanced game constants\r\nconst WIN_CONDITIONS = {\r\n  treasury: 10000,\r\n  prestige: 100,\r\n  provinces: 10\r\n};\r\n\r\nconst LOSE_CONDITIONS = {\r\n  stability: 10,\r\n  treasury: -5000,\r\n  legitimacy: 20\r\n};\r\n\r\nexport default function App() {\r\n  // Core game state\r\n  const [countries, setCountries] = useState([]);\r\n  const [provinces, setProvinces] = useState([]);\r\n  const [playerCountry, setPlayerCountry] = useState(null);\r\n  const [turn, setTurn] = useState(1);\r\n  const [message, setMessage] = useState(\"\");\r\n  const [gamePhase, setGamePhase] = useState(\"loading\"); // loading, country_selection, playing, victory, defeat\r\n\r\n  // Enhanced game data\r\n  const [globalResources, setGlobalResources] = useState({});\r\n  const [technologies, setTechnologies] = useState({});\r\n  const [selectedProvince, setSelectedProvince] = useState(null);\r\n  const [activeTab, setActiveTab] = useState(\"overview\"); // overview, economy, military, diplomacy, technology\r\n\r\n  // UI state\r\n  const [actionTaken, setActionTaken] = useState(false);\r\n  const [event, setEvent] = useState(null);\r\n  const [notifications, setNotifications] = useState([]);\r\n\r\n  // Load initial game data\r\n  useEffect(() => {\r\n    const loadGameData = async () => {\r\n      try {\r\n        setGamePhase(\"loading\");\r\n\r\n        // Load all game data in parallel\r\n        const [countriesRes, provincesRes, resourcesRes, techRes] = await Promise.all([\r\n          axios.get(\"http://localhost:8000/countries\"),\r\n          axios.get(\"http://localhost:8000/provinces\"),\r\n          axios.get(\"http://localhost:8000/resources\"),\r\n          axios.get(\"http://localhost:8000/technologies\")\r\n        ]);\r\n\r\n        console.log(\"Game data loaded:\", {\r\n          countries: countriesRes.data.length,\r\n          provinces: provincesRes.data.length,\r\n          resources: Object.keys(resourcesRes.data).length,\r\n          technologies: Object.keys(techRes.data).length\r\n        });\r\n\r\n        setCountries(countriesRes.data);\r\n        setProvinces(provincesRes.data);\r\n        setGlobalResources(resourcesRes.data);\r\n        setTechnologies(techRes.data);\r\n        setGamePhase(\"country_selection\");\r\n\r\n      } catch (error) {\r\n        console.error(\"Failed to load game data:\", error);\r\n        setMessage(\"Failed to load game data. Please refresh the page.\");\r\n      }\r\n    };\r\n\r\n    loadGameData();\r\n  }, []);\r\n\r\n  // Fetch events when turn changes\r\n  useEffect(() => {\r\n    if (!playerCountry || gamePhase !== \"playing\") return;\r\n\r\n    const fetchTurnEvents = async () => {\r\n      try {\r\n        const eventRes = await axios.get(`http://localhost:8000/event?country=${encodeURIComponent(playerCountry.name)}`);\r\n        setEvent(eventRes.data);\r\n      } catch (error) {\r\n        console.error(\"Failed to fetch events:\", error);\r\n      }\r\n    };\r\n\r\n    fetchTurnEvents();\r\n  }, [turn, playerCountry, gamePhase]);\r\n\r\n  // Game phase handlers\r\n  const selectCountry = (country) => {\r\n    setPlayerCountry(country);\r\n    setGamePhase(\"playing\");\r\n    setActiveTab(\"overview\");\r\n    addNotification(`Welcome, ${country.ruler_name} of ${country.name}!`, \"success\");\r\n  };\r\n\r\n  const addNotification = (text, type = \"info\") => {\r\n    const notification = {\r\n      id: Date.now(),\r\n      text,\r\n      type,\r\n      timestamp: new Date().toLocaleTimeString()\r\n    };\r\n    setNotifications(prev => [notification, ...prev.slice(0, 4)]); // Keep only 5 notifications\r\n  };\r\n\r\n  // Enhanced turn advancement\r\n  const advanceTurn = async () => {\r\n    try {\r\n      setMessage(\"Processing turn...\");\r\n      const response = await axios.post(\"http://localhost:8000/turn\");\r\n\r\n      // Update all game state\r\n      setTurn(response.data.turn);\r\n      setCountries(response.data.countries);\r\n      setProvinces(response.data.provinces);\r\n      setGlobalResources(response.data.global_resources);\r\n\r\n      // Update player country reference\r\n      const updatedPlayerCountry = response.data.countries.find(c => c.name === playerCountry.name);\r\n      if (updatedPlayerCountry) {\r\n        setPlayerCountry(updatedPlayerCountry);\r\n      }\r\n\r\n      setActionTaken(false);\r\n      setMessage(\"\");\r\n      addNotification(`Turn ${response.data.turn} begins`, \"info\");\r\n\r\n      // Check win/lose conditions\r\n      checkGameEnd(updatedPlayerCountry);\r\n\r\n    } catch (error) {\r\n      console.error(\"Failed to advance turn:\", error);\r\n      setMessage(\"Failed to advance turn. Please try again.\");\r\n    }\r\n  };\r\n\r\n  const checkGameEnd = (country) => {\r\n    if (!country) return;\r\n\r\n    // Check victory conditions\r\n    if (country.treasury >= WIN_CONDITIONS.treasury ||\r\n        country.prestige >= WIN_CONDITIONS.prestige ||\r\n        country.provinces.length >= WIN_CONDITIONS.provinces) {\r\n      setGamePhase(\"victory\");\r\n      return;\r\n    }\r\n\r\n    // Check defeat conditions\r\n    if (country.stability <= LOSE_CONDITIONS.stability ||\r\n        country.treasury <= LOSE_CONDITIONS.treasury ||\r\n        country.legitimacy <= LOSE_CONDITIONS.legitimacy) {\r\n      setGamePhase(\"defeat\");\r\n      return;\r\n    }\r\n  };\r\n\r\n  // Research technology\r\n  const startResearch = async (category, techName) => {\r\n    try {\r\n      const response = await axios.post(\"http://localhost:8000/research\", {\r\n        country: playerCountry.name,\r\n        category,\r\n        technology: techName\r\n      });\r\n\r\n      if (response.data.success) {\r\n        addNotification(response.data.message, \"success\");\r\n        // Refresh country data\r\n        const countryRes = await axios.get(`http://localhost:8000/country/${playerCountry.name}`);\r\n        setPlayerCountry(countryRes.data);\r\n      } else {\r\n        addNotification(response.data.error, \"error\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Research failed:\", error);\r\n      addNotification(\"Failed to start research\", \"error\");\r\n    }\r\n  };\r\n\r\n  // Tab content renderer\r\n  const renderTabContent = () => {\r\n    switch (activeTab) {\r\n      case 'overview':\r\n        return (\r\n          <div>\r\n            <h2>🏛️ Nation Overview</h2>\r\n\r\n            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>\r\n              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>\r\n                <h3>💰 Economy</h3>\r\n                <p><strong>Monthly Income:</strong> {playerCountry.monthly_income.toFixed(1)} gold</p>\r\n                <p><strong>Monthly Expenses:</strong> {playerCountry.monthly_expenses.toFixed(1)} gold</p>\r\n                <p><strong>Net Income:</strong> {(playerCountry.monthly_income - playerCountry.monthly_expenses).toFixed(1)} gold</p>\r\n                <p><strong>Inflation:</strong> {(playerCountry.inflation * 100).toFixed(1)}%</p>\r\n              </div>\r\n\r\n              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>\r\n                <h3>⚔️ Military</h3>\r\n                <p><strong>Army Size:</strong> {playerCountry.army_size.toLocaleString()} troops</p>\r\n                <p><strong>Navy Size:</strong> {playerCountry.navy_size} ships</p>\r\n                <p><strong>Military Tradition:</strong> {playerCountry.military_tradition.toFixed(1)}</p>\r\n                <p><strong>War Exhaustion:</strong> {playerCountry.war_exhaustion.toFixed(1)}</p>\r\n              </div>\r\n            </div>\r\n\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>\r\n              <h3>🏛️ Provinces ({playerCountry.provinces.length})</h3>\r\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>\r\n                {provinces.filter(p => p.owner === playerCountry.name).map(province => (\r\n                  <div\r\n                    key={province.name}\r\n                    style={{\r\n                      background: '#1a1a2e',\r\n                      padding: '10px',\r\n                      borderRadius: '5px',\r\n                      cursor: 'pointer',\r\n                      border: selectedProvince?.name === province.name ? '2px solid #4a9eff' : '1px solid #444'\r\n                    }}\r\n                    onClick={() => setSelectedProvince(province)}\r\n                  >\r\n                    <strong>{province.name}</strong>\r\n                    <br />\r\n                    <small>Dev: {province.development.toFixed(1)} | Pop: {province.population_groups.reduce((sum, pop) => sum + pop.size, 0).toLocaleString()}</small>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Recent Events */}\r\n            {event && (\r\n              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>\r\n                <h3>📰 Current Event</h3>\r\n                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>\r\n                  <h4>{event.title}</h4>\r\n                  <p>{event.description}</p>\r\n                  <p style={{ color: '#4a9eff' }}><strong>Effect:</strong> {event.effect}</p>\r\n                  <button\r\n                    onClick={() => {\r\n                      // Apply event logic here\r\n                      addNotification(`Applied: ${event.effect}`, \"success\");\r\n                      setEvent(null);\r\n                    }}\r\n                    style={{\r\n                      background: '#4a9eff',\r\n                      color: '#fff',\r\n                      border: 'none',\r\n                      padding: '8px 15px',\r\n                      borderRadius: '5px',\r\n                      cursor: 'pointer'\r\n                    }}\r\n                  >\r\n                    Accept\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        );\r\n\r\n      case 'population':\r\n        return (\r\n          <div>\r\n            <h2>👥 Population Management</h2>\r\n\r\n            {/* Population Overview */}\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>\r\n              <h3>📊 Population Summary</h3>\r\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '15px' }}>\r\n                {provinces.filter(p => p.owner === playerCountry.name).map(province => {\r\n                  const totalPop = province.population_groups.reduce((sum, pop) => sum + pop.size, 0);\r\n                  const avgHappiness = province.population_groups.reduce((sum, pop) => sum + pop.happiness, 0) / province.population_groups.length;\r\n                  const avgMilitancy = province.population_groups.reduce((sum, pop) => sum + pop.militancy, 0) / province.population_groups.length;\r\n\r\n                  return (\r\n                    <div\r\n                      key={province.name}\r\n                      style={{\r\n                        background: '#1a1a2e',\r\n                        padding: '12px',\r\n                        borderRadius: '5px',\r\n                        cursor: 'pointer',\r\n                        border: selectedProvince?.name === province.name ? '2px solid #4a9eff' : '1px solid #444'\r\n                      }}\r\n                      onClick={() => setSelectedProvince(province)}\r\n                    >\r\n                      <h4 style={{ margin: '0 0 8px 0' }}>{province.name}</h4>\r\n                      <p style={{ margin: '2px 0', fontSize: '0.9rem' }}>\r\n                        <strong>Population:</strong> {totalPop.toLocaleString()}\r\n                      </p>\r\n                      <p style={{ margin: '2px 0', fontSize: '0.9rem' }}>\r\n                        <strong>Happiness:</strong> {avgHappiness.toFixed(1)}/10\r\n                      </p>\r\n                      <p style={{ margin: '2px 0', fontSize: '0.9rem' }}>\r\n                        <strong>Unrest:</strong> {province.unrest.toFixed(1)}\r\n                      </p>\r\n                      <div style={{\r\n                        background: avgHappiness > 6 ? '#51cf66' : avgHappiness > 4 ? '#ffd43b' : '#ff6b6b',\r\n                        height: '4px',\r\n                        borderRadius: '2px',\r\n                        marginTop: '8px'\r\n                      }} />\r\n                    </div>\r\n                  );\r\n                })}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Detailed Province Population */}\r\n            {selectedProvince && (\r\n              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>\r\n                <h3>🏘️ {selectedProvince.name} - Population Details</h3>\r\n\r\n                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>\r\n                  {selectedProvince.population_groups.map((popGroup, index) => (\r\n                    <div\r\n                      key={index}\r\n                      style={{\r\n                        background: '#1a1a2e',\r\n                        padding: '15px',\r\n                        borderRadius: '5px',\r\n                        border: '1px solid #444'\r\n                      }}\r\n                    >\r\n                      <h4 style={{ margin: '0 0 10px 0', textTransform: 'capitalize' }}>\r\n                        {popGroup.social_class.replace('_', ' ')}\r\n                      </h4>\r\n\r\n                      <div style={{ fontSize: '0.9rem' }}>\r\n                        <p><strong>Size:</strong> {popGroup.size.toLocaleString()}</p>\r\n                        <p><strong>Culture:</strong> {popGroup.culture}</p>\r\n                        <p><strong>Religion:</strong> {popGroup.religion}</p>\r\n                        <p><strong>Profession:</strong> {popGroup.profession}</p>\r\n                        <p><strong>Wealth:</strong> {popGroup.wealth.toFixed(1)}</p>\r\n\r\n                        <div style={{ margin: '10px 0' }}>\r\n                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\r\n                            <span>Happiness:</span>\r\n                            <span style={{ color: popGroup.happiness > 6 ? '#51cf66' : popGroup.happiness > 4 ? '#ffd43b' : '#ff6b6b' }}>\r\n                              {popGroup.happiness.toFixed(1)}/10\r\n                            </span>\r\n                          </div>\r\n                          <div style={{\r\n                            background: '#333',\r\n                            height: '6px',\r\n                            borderRadius: '3px',\r\n                            overflow: 'hidden'\r\n                          }}>\r\n                            <div style={{\r\n                              background: popGroup.happiness > 6 ? '#51cf66' : popGroup.happiness > 4 ? '#ffd43b' : '#ff6b6b',\r\n                              height: '100%',\r\n                              width: `${(popGroup.happiness / 10) * 100}%`,\r\n                              transition: 'width 0.3s ease'\r\n                            }} />\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div style={{ margin: '10px 0' }}>\r\n                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\r\n                            <span>Militancy:</span>\r\n                            <span style={{ color: popGroup.militancy > 6 ? '#ff6b6b' : popGroup.militancy > 3 ? '#ffd43b' : '#51cf66' }}>\r\n                              {popGroup.militancy.toFixed(1)}/10\r\n                            </span>\r\n                          </div>\r\n                          <div style={{\r\n                            background: '#333',\r\n                            height: '6px',\r\n                            borderRadius: '3px',\r\n                            overflow: 'hidden'\r\n                          }}>\r\n                            <div style={{\r\n                              background: popGroup.militancy > 6 ? '#ff6b6b' : popGroup.militancy > 3 ? '#ffd43b' : '#51cf66',\r\n                              height: '100%',\r\n                              width: `${(popGroup.militancy / 10) * 100}%`,\r\n                              transition: 'width 0.3s ease'\r\n                            }} />\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div style={{ margin: '10px 0' }}>\r\n                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\r\n                            <span>Consciousness:</span>\r\n                            <span style={{ color: '#4a9eff' }}>\r\n                              {popGroup.consciousness.toFixed(1)}/10\r\n                            </span>\r\n                          </div>\r\n                          <div style={{\r\n                            background: '#333',\r\n                            height: '6px',\r\n                            borderRadius: '3px',\r\n                            overflow: 'hidden'\r\n                          }}>\r\n                            <div style={{\r\n                              background: '#4a9eff',\r\n                              height: '100%',\r\n                              width: `${(popGroup.consciousness / 10) * 100}%`,\r\n                              transition: 'width 0.3s ease'\r\n                            }} />\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n\r\n                {/* Population Actions */}\r\n                <div style={{ marginTop: '20px' }}>\r\n                  <h4>👑 Population Policies</h4>\r\n                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>\r\n                    <button\r\n                      onClick={() => {\r\n                        if (!actionTaken) {\r\n                          addNotification(\"Implemented education reforms\", \"success\");\r\n                          setActionTaken(true);\r\n                        }\r\n                      }}\r\n                      disabled={actionTaken}\r\n                      style={{\r\n                        background: actionTaken ? '#666' : '#4a9eff',\r\n                        color: '#fff',\r\n                        border: 'none',\r\n                        padding: '12px',\r\n                        borderRadius: '5px',\r\n                        cursor: actionTaken ? 'not-allowed' : 'pointer'\r\n                      }}\r\n                    >\r\n                      📚 Education Reform<br />\r\n                      <small>Increase consciousness</small>\r\n                    </button>\r\n\r\n                    <button\r\n                      onClick={() => {\r\n                        if (!actionTaken) {\r\n                          addNotification(\"Promoted cultural integration\", \"success\");\r\n                          setActionTaken(true);\r\n                        }\r\n                      }}\r\n                      disabled={actionTaken}\r\n                      style={{\r\n                        background: actionTaken ? '#666' : '#51cf66',\r\n                        color: '#fff',\r\n                        border: 'none',\r\n                        padding: '12px',\r\n                        borderRadius: '5px',\r\n                        cursor: actionTaken ? 'not-allowed' : 'pointer'\r\n                      }}\r\n                    >\r\n                      🤝 Cultural Integration<br />\r\n                      <small>Reduce cultural tensions</small>\r\n                    </button>\r\n\r\n                    <button\r\n                      onClick={() => {\r\n                        if (!actionTaken) {\r\n                          addNotification(\"Implemented welfare programs\", \"success\");\r\n                          setActionTaken(true);\r\n                        }\r\n                      }}\r\n                      disabled={actionTaken}\r\n                      style={{\r\n                        background: actionTaken ? '#666' : '#ffd43b',\r\n                        color: '#000',\r\n                        border: 'none',\r\n                        padding: '12px',\r\n                        borderRadius: '5px',\r\n                        cursor: actionTaken ? 'not-allowed' : 'pointer'\r\n                      }}\r\n                    >\r\n                      🏥 Welfare Programs<br />\r\n                      <small>Increase happiness</small>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* National Demographics */}\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>\r\n              <h3>🌍 National Demographics</h3>\r\n\r\n              {(() => {\r\n                const allPops = provinces\r\n                  .filter(p => p.owner === playerCountry.name)\r\n                  .flatMap(p => p.population_groups);\r\n\r\n                const totalPop = allPops.reduce((sum, pop) => sum + pop.size, 0);\r\n                const classCounts = {};\r\n                const cultureCounts = {};\r\n                const religionCounts = {};\r\n\r\n                allPops.forEach(pop => {\r\n                  classCounts[pop.social_class] = (classCounts[pop.social_class] || 0) + pop.size;\r\n                  cultureCounts[pop.culture] = (cultureCounts[pop.culture] || 0) + pop.size;\r\n                  religionCounts[pop.religion] = (religionCounts[pop.religion] || 0) + pop.size;\r\n                });\r\n\r\n                return (\r\n                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>\r\n                    <div>\r\n                      <h4>👑 Social Classes</h4>\r\n                      {Object.entries(classCounts).map(([className, count]) => (\r\n                        <div key={className} style={{ margin: '8px 0' }}>\r\n                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>\r\n                            <span style={{ textTransform: 'capitalize' }}>{className.replace('_', ' ')}</span>\r\n                            <span>{((count / totalPop) * 100).toFixed(1)}%</span>\r\n                          </div>\r\n                          <div style={{ background: '#333', height: '6px', borderRadius: '3px', overflow: 'hidden' }}>\r\n                            <div style={{\r\n                              background: '#4a9eff',\r\n                              height: '100%',\r\n                              width: `${(count / totalPop) * 100}%`\r\n                            }} />\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n\r\n                    <div>\r\n                      <h4>🏛️ Cultures</h4>\r\n                      {Object.entries(cultureCounts).map(([culture, count]) => (\r\n                        <div key={culture} style={{ margin: '8px 0' }}>\r\n                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>\r\n                            <span style={{ textTransform: 'capitalize' }}>{culture}</span>\r\n                            <span>{((count / totalPop) * 100).toFixed(1)}%</span>\r\n                          </div>\r\n                          <div style={{ background: '#333', height: '6px', borderRadius: '3px', overflow: 'hidden' }}>\r\n                            <div style={{\r\n                              background: '#51cf66',\r\n                              height: '100%',\r\n                              width: `${(count / totalPop) * 100}%`\r\n                            }} />\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n\r\n                    <div>\r\n                      <h4>⛪ Religions</h4>\r\n                      {Object.entries(religionCounts).map(([religion, count]) => (\r\n                        <div key={religion} style={{ margin: '8px 0' }}>\r\n                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>\r\n                            <span style={{ textTransform: 'capitalize' }}>{religion}</span>\r\n                            <span>{((count / totalPop) * 100).toFixed(1)}%</span>\r\n                          </div>\r\n                          <div style={{ background: '#333', height: '6px', borderRadius: '3px', overflow: 'hidden' }}>\r\n                            <div style={{\r\n                              background: '#ffd43b',\r\n                              height: '100%',\r\n                              width: `${(count / totalPop) * 100}%`\r\n                            }} />\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })()}\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      case 'economy':\r\n        return (\r\n          <div>\r\n            <h2>💰 Economic Management</h2>\r\n\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>\r\n              <h3>📊 Financial Summary</h3>\r\n              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>\r\n                <div>\r\n                  <p><strong>Treasury:</strong> {playerCountry.treasury.toLocaleString()} gold</p>\r\n                  <p><strong>Monthly Income:</strong> +{playerCountry.monthly_income.toFixed(1)} gold</p>\r\n                  <p><strong>Monthly Expenses:</strong> -{playerCountry.monthly_expenses.toFixed(1)} gold</p>\r\n                  <p><strong>Net Balance:</strong> {(playerCountry.monthly_income - playerCountry.monthly_expenses).toFixed(1)} gold</p>\r\n                </div>\r\n                <div>\r\n                  <p><strong>Trade Efficiency:</strong> {(playerCountry.trade_efficiency * 100).toFixed(1)}%</p>\r\n                  <p><strong>Inflation Rate:</strong> {(playerCountry.inflation * 100).toFixed(1)}%</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>\r\n              <h3>🌍 Global Resource Prices</h3>\r\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '10px' }}>\r\n                {Object.entries(globalResources).map(([name, resource]) => (\r\n                  <div key={name} style={{ background: '#1a1a2e', padding: '10px', borderRadius: '5px', textAlign: 'center' }}>\r\n                    <strong>{name.replace('_', ' ')}</strong>\r\n                    <br />\r\n                    <span style={{ color: '#4a9eff' }}>{resource.current_price.toFixed(2)} gold</span>\r\n                    <br />\r\n                    <small style={{ opacity: 0.7 }}>\r\n                      {resource.current_price > resource.base_value ? '📈' : '📉'}\r\n                    </small>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>\r\n              <h3>🏭 Economic Actions</h3>\r\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>\r\n                <button\r\n                  onClick={() => {\r\n                    if (!actionTaken) {\r\n                      addNotification(\"Invested in infrastructure development\", \"success\");\r\n                      setActionTaken(true);\r\n                    }\r\n                  }}\r\n                  disabled={actionTaken}\r\n                  style={{\r\n                    background: actionTaken ? '#666' : '#4a9eff',\r\n                    color: '#fff',\r\n                    border: 'none',\r\n                    padding: '15px',\r\n                    borderRadius: '5px',\r\n                    cursor: actionTaken ? 'not-allowed' : 'pointer'\r\n                  }}\r\n                >\r\n                  🏗️ Build Infrastructure<br />\r\n                  <small>Cost: 200 gold</small>\r\n                </button>\r\n\r\n                <button\r\n                  onClick={() => {\r\n                    if (!actionTaken) {\r\n                      addNotification(\"Promoted trade and commerce\", \"success\");\r\n                      setActionTaken(true);\r\n                    }\r\n                  }}\r\n                  disabled={actionTaken}\r\n                  style={{\r\n                    background: actionTaken ? '#666' : '#51cf66',\r\n                    color: '#fff',\r\n                    border: 'none',\r\n                    padding: '15px',\r\n                    borderRadius: '5px',\r\n                    cursor: actionTaken ? 'not-allowed' : 'pointer'\r\n                  }}\r\n                >\r\n                  🏪 Promote Trade<br />\r\n                  <small>Cost: 150 gold</small>\r\n                </button>\r\n\r\n                <button\r\n                  onClick={() => {\r\n                    if (!actionTaken) {\r\n                      addNotification(\"Implemented tax reforms\", \"success\");\r\n                      setActionTaken(true);\r\n                    }\r\n                  }}\r\n                  disabled={actionTaken}\r\n                  style={{\r\n                    background: actionTaken ? '#666' : '#ffd43b',\r\n                    color: '#000',\r\n                    border: 'none',\r\n                    padding: '15px',\r\n                    borderRadius: '5px',\r\n                    cursor: actionTaken ? 'not-allowed' : 'pointer'\r\n                  }}\r\n                >\r\n                  📋 Tax Reform<br />\r\n                  <small>Cost: 100 gold</small>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      case 'technology':\r\n        return (\r\n          <div>\r\n            <h2>🔬 Technology & Research</h2>\r\n\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>\r\n              <h3>📚 Research Progress</h3>\r\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>\r\n                {Object.entries(playerCountry.research_points).map(([category, points]) => (\r\n                  <div key={category} style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>\r\n                    <h4 style={{ textTransform: 'capitalize', margin: '0 0 10px 0' }}>\r\n                      {category === 'military' && '⚔️'}\r\n                      {category === 'economic' && '💰'}\r\n                      {category === 'social' && '👥'}\r\n                      {category === 'administrative' && '🏛️'}\r\n                      {' ' + category}\r\n                    </h4>\r\n                    <p><strong>Points:</strong> {points.toFixed(1)}</p>\r\n                    {playerCountry.current_research[category] && (\r\n                      <p><strong>Researching:</strong> {playerCountry.current_research[category]}</p>\r\n                    )}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>\r\n              <h3>🧪 Available Technologies</h3>\r\n              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '15px' }}>\r\n                {Object.entries(technologies).map(([name, tech]) => {\r\n                  const isResearched = playerCountry.technologies.includes(name);\r\n                  const canResearch = tech.prerequisites.every(prereq => playerCountry.technologies.includes(prereq));\r\n                  const isCurrentlyResearching = Object.values(playerCountry.current_research).includes(name);\r\n\r\n                  return (\r\n                    <div\r\n                      key={name}\r\n                      style={{\r\n                        background: isResearched ? '#2d5a2d' : '#1a1a2e',\r\n                        padding: '15px',\r\n                        borderRadius: '5px',\r\n                        border: isCurrentlyResearching ? '2px solid #4a9eff' : '1px solid #444',\r\n                        opacity: canResearch || isResearched ? 1 : 0.5\r\n                      }}\r\n                    >\r\n                      <h4 style={{ margin: '0 0 10px 0' }}>{name}</h4>\r\n                      <p style={{ fontSize: '0.9rem', margin: '5px 0' }}>{tech.description}</p>\r\n                      <p><strong>Category:</strong> {tech.category}</p>\r\n                      <p><strong>Cost:</strong> {tech.cost} points</p>\r\n\r\n                      {tech.prerequisites.length > 0 && (\r\n                        <p><strong>Prerequisites:</strong> {tech.prerequisites.join(', ')}</p>\r\n                      )}\r\n\r\n                      {isResearched && (\r\n                        <span style={{ color: '#51cf66', fontWeight: 'bold' }}>✅ Researched</span>\r\n                      )}\r\n\r\n                      {!isResearched && canResearch && !isCurrentlyResearching && (\r\n                        <button\r\n                          onClick={() => startResearch(tech.category, name)}\r\n                          style={{\r\n                            background: '#4a9eff',\r\n                            color: '#fff',\r\n                            border: 'none',\r\n                            padding: '8px 15px',\r\n                            borderRadius: '5px',\r\n                            cursor: 'pointer',\r\n                            marginTop: '10px'\r\n                          }}\r\n                        >\r\n                          Start Research\r\n                        </button>\r\n                      )}\r\n\r\n                      {isCurrentlyResearching && (\r\n                        <span style={{ color: '#ffd43b', fontWeight: 'bold' }}>🔬 Researching...</span>\r\n                      )}\r\n                    </div>\r\n                  );\r\n                })}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      default:\r\n        return (\r\n          <div>\r\n            <h2>🚧 Coming Soon</h2>\r\n            <p>This feature is under development and will be available in future updates.</p>\r\n          </div>\r\n        );\r\n    }\r\n  };\r\n\r\n  // Game phase rendering\r\n  if (gamePhase === \"loading\") {\r\n    return (\r\n      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh', background: '#1a1a2e' }}>\r\n        <div style={{ textAlign: 'center', color: '#fff' }}>\r\n          <h1>🏰 Empires & Revolutions</h1>\r\n          <p>Loading the world of Aeterra...</p>\r\n          <div style={{ margin: '20px 0' }}>⚔️ 🏛️ 💰 🔬 🌍</div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (gamePhase === \"country_selection\") {\r\n    return (\r\n      <div style={{ background: '#1a1a2e', minHeight: '100vh', color: '#fff' }}>\r\n        <div style={{ textAlign: 'center', padding: '20px' }}>\r\n          <h1>🏰 Choose Your Destiny</h1>\r\n          <p>Select a nation to lead through the Age of Revolutions</p>\r\n        </div>\r\n\r\n        <WorldMap\r\n          onSelectCountry={selectCountry}\r\n          selectedCountry={null}\r\n          countries={countries}\r\n        />\r\n\r\n        <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>\r\n          <h2>Available Nations</h2>\r\n          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>\r\n            {countries.slice(0, 5).map(country => (\r\n              <div\r\n                key={country.name}\r\n                style={{\r\n                  background: '#2d2d44',\r\n                  padding: '20px',\r\n                  borderRadius: '10px',\r\n                  cursor: 'pointer',\r\n                  border: '2px solid transparent',\r\n                  transition: 'all 0.3s ease'\r\n                }}\r\n                onMouseEnter={(e) => e.target.style.borderColor = '#4a9eff'}\r\n                onMouseLeave={(e) => e.target.style.borderColor = 'transparent'}\r\n                onClick={() => selectCountry(country)}\r\n              >\r\n                <h3>{country.name}</h3>\r\n                <p><strong>Ruler:</strong> {country.ruler_name}</p>\r\n                <p><strong>Government:</strong> {country.government_type.replace('_', ' ')}</p>\r\n                <p><strong>Treasury:</strong> {country.treasury.toLocaleString()} gold</p>\r\n                <p><strong>Provinces:</strong> {country.provinces.length}</p>\r\n                <p><strong>Prestige:</strong> {country.prestige}</p>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (gamePhase === \"victory\") {\r\n    return (\r\n      <div style={{\r\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n        minHeight: '100vh',\r\n        display: 'flex',\r\n        justifyContent: 'center',\r\n        alignItems: 'center',\r\n        color: '#fff',\r\n        textAlign: 'center'\r\n      }}>\r\n        <div>\r\n          <h1 style={{ fontSize: '4rem', marginBottom: '20px' }}>🎉 VICTORY! 🎉</h1>\r\n          <h2>The {playerCountry.name} Triumphant!</h2>\r\n          <p style={{ fontSize: '1.5rem', margin: '20px 0' }}>\r\n            Under the wise rule of {playerCountry.ruler_name}, your nation has achieved greatness!\r\n          </p>\r\n          <div style={{ background: 'rgba(0,0,0,0.3)', padding: '20px', borderRadius: '10px', margin: '20px 0' }}>\r\n            <p><strong>Final Treasury:</strong> {playerCountry.treasury.toLocaleString()} gold</p>\r\n            <p><strong>Final Prestige:</strong> {playerCountry.prestige}</p>\r\n            <p><strong>Provinces Controlled:</strong> {playerCountry.provinces.length}</p>\r\n            <p><strong>Turns Survived:</strong> {turn}</p>\r\n          </div>\r\n          <button\r\n            onClick={() => window.location.reload()}\r\n            style={{\r\n              padding: '15px 30px',\r\n              fontSize: '1.2rem',\r\n              background: '#4a9eff',\r\n              color: '#fff',\r\n              border: 'none',\r\n              borderRadius: '5px',\r\n              cursor: 'pointer'\r\n            }}\r\n          >\r\n            Play Again\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (gamePhase === \"defeat\") {\r\n    return (\r\n      <div style={{\r\n        background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',\r\n        minHeight: '100vh',\r\n        display: 'flex',\r\n        justifyContent: 'center',\r\n        alignItems: 'center',\r\n        color: '#fff',\r\n        textAlign: 'center'\r\n      }}>\r\n        <div>\r\n          <h1 style={{ fontSize: '4rem', marginBottom: '20px' }}>💀 DEFEAT 💀</h1>\r\n          <h2>The Fall of {playerCountry.name}</h2>\r\n          <p style={{ fontSize: '1.5rem', margin: '20px 0' }}>\r\n            The reign of {playerCountry.ruler_name} has come to an end...\r\n          </p>\r\n          <div style={{ background: 'rgba(0,0,0,0.3)', padding: '20px', borderRadius: '10px', margin: '20px 0' }}>\r\n            <p><strong>Final Treasury:</strong> {playerCountry.treasury.toLocaleString()} gold</p>\r\n            <p><strong>Final Stability:</strong> {playerCountry.stability}</p>\r\n            <p><strong>Final Legitimacy:</strong> {playerCountry.legitimacy}</p>\r\n            <p><strong>Turns Survived:</strong> {turn}</p>\r\n          </div>\r\n          <button\r\n            onClick={() => window.location.reload()}\r\n            style={{\r\n              padding: '15px 30px',\r\n              fontSize: '1.2rem',\r\n              background: '#4a9eff',\r\n              color: '#fff',\r\n              border: 'none',\r\n              borderRadius: '5px',\r\n              cursor: 'pointer'\r\n            }}\r\n          >\r\n            Try Again\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Main game interface - sophisticated tabbed layout\r\n  return (\r\n    <div style={{ background: '#1a1a2e', minHeight: '100vh', color: '#fff' }}>\r\n      {/* Header */}\r\n      <div style={{\r\n        background: '#2d2d44',\r\n        padding: '15px 20px',\r\n        borderBottom: '2px solid #4a9eff',\r\n        display: 'flex',\r\n        justifyContent: 'space-between',\r\n        alignItems: 'center'\r\n      }}>\r\n        <div>\r\n          <h1 style={{ margin: 0, fontSize: '1.8rem' }}>🏰 {playerCountry.name}</h1>\r\n          <p style={{ margin: 0, opacity: 0.8 }}>{playerCountry.ruler_name} • {playerCountry.government_type.replace('_', ' ')}</p>\r\n        </div>\r\n\r\n        <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>\r\n          <div style={{ textAlign: 'center' }}>\r\n            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>💰 {playerCountry.treasury.toLocaleString()}</div>\r\n            <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>Treasury</div>\r\n          </div>\r\n          <div style={{ textAlign: 'center' }}>\r\n            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>⭐ {playerCountry.prestige}</div>\r\n            <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>Prestige</div>\r\n          </div>\r\n          <div style={{ textAlign: 'center' }}>\r\n            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>🏛️ {playerCountry.stability}</div>\r\n            <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>Stability</div>\r\n          </div>\r\n          <div style={{ textAlign: 'center' }}>\r\n            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>👑 {playerCountry.legitimacy}</div>\r\n            <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>Legitimacy</div>\r\n          </div>\r\n          <div style={{ textAlign: 'center' }}>\r\n            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>📅 {turn}</div>\r\n            <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>Turn</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Notifications */}\r\n      {notifications.length > 0 && (\r\n        <div style={{ padding: '10px 20px' }}>\r\n          {notifications.map(notification => (\r\n            <div\r\n              key={notification.id}\r\n              style={{\r\n                background: notification.type === 'error' ? '#ff6b6b' :\r\n                           notification.type === 'success' ? '#51cf66' : '#4a9eff',\r\n                padding: '8px 15px',\r\n                margin: '5px 0',\r\n                borderRadius: '5px',\r\n                fontSize: '0.9rem',\r\n                display: 'flex',\r\n                justifyContent: 'space-between'\r\n              }}\r\n            >\r\n              <span>{notification.text}</span>\r\n              <span style={{ opacity: 0.7 }}>{notification.timestamp}</span>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n      {/* Tab Navigation */}\r\n      <div style={{\r\n        background: '#2d2d44',\r\n        padding: '0 20px',\r\n        borderBottom: '1px solid #444'\r\n      }}>\r\n        {['overview', 'population', 'economy', 'military', 'diplomacy', 'technology'].map(tab => (\r\n          <button\r\n            key={tab}\r\n            onClick={() => setActiveTab(tab)}\r\n            style={{\r\n              background: activeTab === tab ? '#4a9eff' : 'transparent',\r\n              color: '#fff',\r\n              border: 'none',\r\n              padding: '15px 25px',\r\n              margin: '0 5px',\r\n              cursor: 'pointer',\r\n              borderRadius: '5px 5px 0 0',\r\n              textTransform: 'capitalize',\r\n              fontSize: '1rem'\r\n            }}\r\n          >\r\n            {tab === 'overview' && '🏛️'}\r\n            {tab === 'population' && '👥'}\r\n            {tab === 'economy' && '💰'}\r\n            {tab === 'military' && '⚔️'}\r\n            {tab === 'diplomacy' && '🤝'}\r\n            {tab === 'technology' && '🔬'}\r\n            {' ' + tab}\r\n          </button>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Main Content Area */}\r\n      <div style={{ display: 'flex', height: 'calc(100vh - 200px)' }}>\r\n        {/* Left Panel - Map */}\r\n        <div style={{ flex: '1', background: '#16213e', padding: '20px' }}>\r\n          <WorldMap\r\n            onSelectCountry={() => {}}\r\n            selectedCountry={playerCountry}\r\n            countries={countries}\r\n            onSelectProvince={setSelectedProvince}\r\n            selectedProvince={selectedProvince}\r\n          />\r\n\r\n          {selectedProvince && (\r\n            <div style={{\r\n              background: '#2d2d44',\r\n              padding: '15px',\r\n              margin: '10px 0',\r\n              borderRadius: '8px',\r\n              maxHeight: '200px',\r\n              overflowY: 'auto'\r\n            }}>\r\n              <h3>{selectedProvince.name}</h3>\r\n              <p><strong>Owner:</strong> {selectedProvince.owner}</p>\r\n              <p><strong>Development:</strong> {selectedProvince.development.toFixed(1)}</p>\r\n              <p><strong>Population:</strong> {selectedProvince.population_groups.reduce((sum, pop) => sum + pop.size, 0).toLocaleString()}</p>\r\n              <p><strong>Terrain:</strong> {selectedProvince.terrain}</p>\r\n              <p><strong>Unrest:</strong> {selectedProvince.unrest.toFixed(1)}</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Right Panel - Tab Content */}\r\n        <div style={{ flex: '1', background: '#1a1a2e', padding: '20px', overflowY: 'auto' }}>\r\n          {renderTabContent()}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Bottom Action Bar */}\r\n      <div style={{\r\n        background: '#2d2d44',\r\n        padding: '15px 20px',\r\n        borderTop: '2px solid #4a9eff',\r\n        display: 'flex',\r\n        justifyContent: 'space-between',\r\n        alignItems: 'center'\r\n      }}>\r\n        <div>\r\n          {message && (\r\n            <span style={{ color: '#ffd43b', fontSize: '1rem' }}>{message}</span>\r\n          )}\r\n        </div>\r\n\r\n        <button\r\n          onClick={advanceTurn}\r\n          disabled={actionTaken}\r\n          style={{\r\n            background: actionTaken ? '#666' : '#4a9eff',\r\n            color: '#fff',\r\n            border: 'none',\r\n            padding: '12px 30px',\r\n            fontSize: '1.1rem',\r\n            borderRadius: '5px',\r\n            cursor: actionTaken ? 'not-allowed' : 'pointer',\r\n            fontWeight: 'bold'\r\n          }}\r\n        >\r\n          {actionTaken ? '⏳ Processing...' : '▶️ End Turn'}\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,YAAY;;AAEjC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAG;EACrBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,GAAG;EACbC,SAAS,EAAE;AACb,CAAC;AAED,MAAMC,eAAe,GAAG;EACtBC,SAAS,EAAE,EAAE;EACbJ,QAAQ,EAAE,CAAC,IAAI;EACfK,UAAU,EAAE;AACd,CAAC;AAED,eAAe,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAC5B;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACQ,SAAS,EAAEQ,YAAY,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmB,IAAI,EAAEC,OAAO,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC6B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;;EAExD;EACA,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACAD,SAAS,CAAC,MAAM;IACd,MAAMwC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFf,YAAY,CAAC,SAAS,CAAC;;QAEvB;QACA,MAAM,CAACgB,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,OAAO,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC5E5C,KAAK,CAAC6C,GAAG,CAAC,iCAAiC,CAAC,EAC5C7C,KAAK,CAAC6C,GAAG,CAAC,iCAAiC,CAAC,EAC5C7C,KAAK,CAAC6C,GAAG,CAAC,iCAAiC,CAAC,EAC5C7C,KAAK,CAAC6C,GAAG,CAAC,oCAAoC,CAAC,CAChD,CAAC;QAEFC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;UAC/BlC,SAAS,EAAE0B,YAAY,CAACS,IAAI,CAACC,MAAM;UACnC1C,SAAS,EAAEiC,YAAY,CAACQ,IAAI,CAACC,MAAM;UACnCC,SAAS,EAAEC,MAAM,CAACC,IAAI,CAACX,YAAY,CAACO,IAAI,CAAC,CAACC,MAAM;UAChDvB,YAAY,EAAEyB,MAAM,CAACC,IAAI,CAACV,OAAO,CAACM,IAAI,CAAC,CAACC;QAC1C,CAAC,CAAC;QAEFnC,YAAY,CAACyB,YAAY,CAACS,IAAI,CAAC;QAC/BjC,YAAY,CAACyB,YAAY,CAACQ,IAAI,CAAC;QAC/BvB,kBAAkB,CAACgB,YAAY,CAACO,IAAI,CAAC;QACrCrB,eAAe,CAACe,OAAO,CAACM,IAAI,CAAC;QAC7BzB,YAAY,CAAC,mBAAmB,CAAC;MAEnC,CAAC,CAAC,OAAO8B,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDhC,UAAU,CAAC,oDAAoD,CAAC;MAClE;IACF,CAAC;IAEDiB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxC,SAAS,CAAC,MAAM;IACd,IAAI,CAACkB,aAAa,IAAIM,SAAS,KAAK,SAAS,EAAE;IAE/C,MAAMgC,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMvD,KAAK,CAAC6C,GAAG,CAAC,uCAAuCW,kBAAkB,CAACxC,aAAa,CAACyC,IAAI,CAAC,EAAE,CAAC;QACjHtB,QAAQ,CAACoB,QAAQ,CAACP,IAAI,CAAC;MACzB,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;IACF,CAAC;IAEDC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACpC,IAAI,EAAEF,aAAa,EAAEM,SAAS,CAAC,CAAC;;EAEpC;EACA,MAAMoC,aAAa,GAAIC,OAAO,IAAK;IACjC1C,gBAAgB,CAAC0C,OAAO,CAAC;IACzBpC,YAAY,CAAC,SAAS,CAAC;IACvBQ,YAAY,CAAC,UAAU,CAAC;IACxB6B,eAAe,CAAC,YAAYD,OAAO,CAACE,UAAU,OAAOF,OAAO,CAACF,IAAI,GAAG,EAAE,SAAS,CAAC;EAClF,CAAC;EAED,MAAMG,eAAe,GAAGA,CAACE,IAAI,EAAEC,IAAI,GAAG,MAAM,KAAK;IAC/C,MAAMC,YAAY,GAAG;MACnBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdL,IAAI;MACJC,IAAI;MACJK,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,kBAAkB,CAAC;IAC3C,CAAC;IACDhC,gBAAgB,CAACiC,IAAI,IAAI,CAACN,YAAY,EAAE,GAAGM,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjE,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFnD,UAAU,CAAC,oBAAoB,CAAC;MAChC,MAAMoD,QAAQ,GAAG,MAAMzE,KAAK,CAAC0E,IAAI,CAAC,4BAA4B,CAAC;;MAE/D;MACAvD,OAAO,CAACsD,QAAQ,CAACzB,IAAI,CAAC9B,IAAI,CAAC;MAC3BJ,YAAY,CAAC2D,QAAQ,CAACzB,IAAI,CAACnC,SAAS,CAAC;MACrCE,YAAY,CAAC0D,QAAQ,CAACzB,IAAI,CAACzC,SAAS,CAAC;MACrCkB,kBAAkB,CAACgD,QAAQ,CAACzB,IAAI,CAAC2B,gBAAgB,CAAC;;MAElD;MACA,MAAMC,oBAAoB,GAAGH,QAAQ,CAACzB,IAAI,CAACnC,SAAS,CAACgE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrB,IAAI,KAAKzC,aAAa,CAACyC,IAAI,CAAC;MAC7F,IAAImB,oBAAoB,EAAE;QACxB3D,gBAAgB,CAAC2D,oBAAoB,CAAC;MACxC;MAEA3C,cAAc,CAAC,KAAK,CAAC;MACrBZ,UAAU,CAAC,EAAE,CAAC;MACduC,eAAe,CAAC,QAAQa,QAAQ,CAACzB,IAAI,CAAC9B,IAAI,SAAS,EAAE,MAAM,CAAC;;MAE5D;MACA6D,YAAY,CAACH,oBAAoB,CAAC;IAEpC,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/ChC,UAAU,CAAC,2CAA2C,CAAC;IACzD;EACF,CAAC;EAED,MAAM0D,YAAY,GAAIpB,OAAO,IAAK;IAChC,IAAI,CAACA,OAAO,EAAE;;IAEd;IACA,IAAIA,OAAO,CAACtD,QAAQ,IAAID,cAAc,CAACC,QAAQ,IAC3CsD,OAAO,CAACrD,QAAQ,IAAIF,cAAc,CAACE,QAAQ,IAC3CqD,OAAO,CAACpD,SAAS,CAAC0C,MAAM,IAAI7C,cAAc,CAACG,SAAS,EAAE;MACxDgB,YAAY,CAAC,SAAS,CAAC;MACvB;IACF;;IAEA;IACA,IAAIoC,OAAO,CAAClD,SAAS,IAAID,eAAe,CAACC,SAAS,IAC9CkD,OAAO,CAACtD,QAAQ,IAAIG,eAAe,CAACH,QAAQ,IAC5CsD,OAAO,CAACjD,UAAU,IAAIF,eAAe,CAACE,UAAU,EAAE;MACpDa,YAAY,CAAC,QAAQ,CAAC;MACtB;IACF;EACF,CAAC;;EAED;EACA,MAAMyD,aAAa,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;IAClD,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMzE,KAAK,CAAC0E,IAAI,CAAC,gCAAgC,EAAE;QAClEf,OAAO,EAAE3C,aAAa,CAACyC,IAAI;QAC3BwB,QAAQ;QACRE,UAAU,EAAED;MACd,CAAC,CAAC;MAEF,IAAIT,QAAQ,CAACzB,IAAI,CAACoC,OAAO,EAAE;QACzBxB,eAAe,CAACa,QAAQ,CAACzB,IAAI,CAAC5B,OAAO,EAAE,SAAS,CAAC;QACjD;QACA,MAAMiE,UAAU,GAAG,MAAMrF,KAAK,CAAC6C,GAAG,CAAC,iCAAiC7B,aAAa,CAACyC,IAAI,EAAE,CAAC;QACzFxC,gBAAgB,CAACoE,UAAU,CAACrC,IAAI,CAAC;MACnC,CAAC,MAAM;QACLY,eAAe,CAACa,QAAQ,CAACzB,IAAI,CAACK,KAAK,EAAE,OAAO,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCO,eAAe,CAAC,0BAA0B,EAAE,OAAO,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAM0B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQxD,SAAS;MACf,KAAK,UAAU;QACb,oBACE3B,OAAA;UAAAoF,QAAA,gBACEpF,OAAA;YAAAoF,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE5BxF,OAAA;YAAKyF,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,mBAAmB,EAAE,SAAS;cAAEC,GAAG,EAAE,MAAM;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBACjGpF,OAAA;cAAKyF,KAAK,EAAE;gBAAEK,UAAU,EAAE,SAAS;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,YAAY,EAAE;cAAM,CAAE;cAAAZ,QAAA,gBAC1EpF,OAAA;gBAAAoF,QAAA,EAAI;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBxF,OAAA;gBAAAoF,QAAA,gBAAGpF,OAAA;kBAAAoF,QAAA,EAAQ;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3E,aAAa,CAACoF,cAAc,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,OAAK;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACtFxF,OAAA;gBAAAoF,QAAA,gBAAGpF,OAAA;kBAAAoF,QAAA,EAAQ;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3E,aAAa,CAACsF,gBAAgB,CAACD,OAAO,CAAC,CAAC,CAAC,EAAC,OAAK;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1FxF,OAAA;gBAAAoF,QAAA,gBAAGpF,OAAA;kBAAAoF,QAAA,EAAQ;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,CAAC3E,aAAa,CAACoF,cAAc,GAAGpF,aAAa,CAACsF,gBAAgB,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,OAAK;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrHxF,OAAA;gBAAAoF,QAAA,gBAAGpF,OAAA;kBAAAoF,QAAA,EAAQ;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,CAAC3E,aAAa,CAACuF,SAAS,GAAG,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eAENxF,OAAA;cAAKyF,KAAK,EAAE;gBAAEK,UAAU,EAAE,SAAS;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,YAAY,EAAE;cAAM,CAAE;cAAAZ,QAAA,gBAC1EpF,OAAA;gBAAAoF,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBxF,OAAA;gBAAAoF,QAAA,gBAAGpF,OAAA;kBAAAoF,QAAA,EAAQ;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3E,aAAa,CAACwF,SAAS,CAACC,cAAc,CAAC,CAAC,EAAC,SAAO;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACpFxF,OAAA;gBAAAoF,QAAA,gBAAGpF,OAAA;kBAAAoF,QAAA,EAAQ;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3E,aAAa,CAAC0F,SAAS,EAAC,QAAM;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClExF,OAAA;gBAAAoF,QAAA,gBAAGpF,OAAA;kBAAAoF,QAAA,EAAQ;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3E,aAAa,CAAC2F,kBAAkB,CAACN,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzFxF,OAAA;gBAAAoF,QAAA,gBAAGpF,OAAA;kBAAAoF,QAAA,EAAQ;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3E,aAAa,CAAC4F,cAAc,CAACP,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxF,OAAA;YAAKyF,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAChGpF,OAAA;cAAAoF,QAAA,GAAI,gCAAe,EAACvE,aAAa,CAACT,SAAS,CAAC0C,MAAM,EAAC,GAAC;YAAA;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzDxF,OAAA;cAAKyF,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,EACvGhF,SAAS,CAACsG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,KAAK/F,aAAa,CAACyC,IAAI,CAAC,CAACuD,GAAG,CAACC,QAAQ,iBACjE9G,OAAA;gBAEEyF,KAAK,EAAE;kBACLK,UAAU,EAAE,SAAS;kBACrBC,OAAO,EAAE,MAAM;kBACfC,YAAY,EAAE,KAAK;kBACnBe,MAAM,EAAE,SAAS;kBACjBC,MAAM,EAAE,CAAAvF,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE6B,IAAI,MAAKwD,QAAQ,CAACxD,IAAI,GAAG,mBAAmB,GAAG;gBAC3E,CAAE;gBACF2D,OAAO,EAAEA,CAAA,KAAMvF,mBAAmB,CAACoF,QAAQ,CAAE;gBAAA1B,QAAA,gBAE7CpF,OAAA;kBAAAoF,QAAA,EAAS0B,QAAQ,CAACxD;gBAAI;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eAChCxF,OAAA;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNxF,OAAA;kBAAAoF,QAAA,GAAO,OAAK,EAAC0B,QAAQ,CAACI,WAAW,CAAChB,OAAO,CAAC,CAAC,CAAC,EAAC,UAAQ,EAACY,QAAQ,CAACK,iBAAiB,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACC,IAAI,EAAE,CAAC,CAAC,CAACjB,cAAc,CAAC,CAAC;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GAZ7IsB,QAAQ,CAACxD,IAAI;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLzD,KAAK,iBACJ/B,OAAA;YAAKyF,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE;YAAM,CAAE;YAAAZ,QAAA,gBAC1EpF,OAAA;cAAAoF,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBxF,OAAA;cAAKyF,KAAK,EAAE;gBAAEK,UAAU,EAAE,SAAS;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,YAAY,EAAE;cAAM,CAAE;cAAAZ,QAAA,gBAC1EpF,OAAA;gBAAAoF,QAAA,EAAKrD,KAAK,CAACyF;cAAK;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtBxF,OAAA;gBAAAoF,QAAA,EAAIrD,KAAK,CAAC0F;cAAW;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1BxF,OAAA;gBAAGyF,KAAK,EAAE;kBAAEiC,KAAK,EAAE;gBAAU,CAAE;gBAAAtC,QAAA,gBAACpF,OAAA;kBAAAoF,QAAA,EAAQ;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACzD,KAAK,CAAC4F,MAAM;cAAA;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3ExF,OAAA;gBACEiH,OAAO,EAAEA,CAAA,KAAM;kBACb;kBACAxD,eAAe,CAAC,YAAY1B,KAAK,CAAC4F,MAAM,EAAE,EAAE,SAAS,CAAC;kBACtD3F,QAAQ,CAAC,IAAI,CAAC;gBAChB,CAAE;gBACFyD,KAAK,EAAE;kBACLK,UAAU,EAAE,SAAS;kBACrB4B,KAAK,EAAE,MAAM;kBACbV,MAAM,EAAE,MAAM;kBACdjB,OAAO,EAAE,UAAU;kBACnBC,YAAY,EAAE,KAAK;kBACnBe,MAAM,EAAE;gBACV,CAAE;gBAAA3B,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV,KAAK,YAAY;QACf,oBACExF,OAAA;UAAAoF,QAAA,gBACEpF,OAAA;YAAAoF,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGjCxF,OAAA;YAAKyF,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAChGpF,OAAA;cAAAoF,QAAA,EAAI;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9BxF,OAAA;cAAKyF,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,EACvGhF,SAAS,CAACsG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,KAAK/F,aAAa,CAACyC,IAAI,CAAC,CAACuD,GAAG,CAACC,QAAQ,IAAI;gBACrE,MAAMc,QAAQ,GAAGd,QAAQ,CAACK,iBAAiB,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACC,IAAI,EAAE,CAAC,CAAC;gBACnF,MAAMM,YAAY,GAAGf,QAAQ,CAACK,iBAAiB,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACQ,SAAS,EAAE,CAAC,CAAC,GAAGhB,QAAQ,CAACK,iBAAiB,CAACrE,MAAM;gBAChI,MAAMiF,YAAY,GAAGjB,QAAQ,CAACK,iBAAiB,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACU,SAAS,EAAE,CAAC,CAAC,GAAGlB,QAAQ,CAACK,iBAAiB,CAACrE,MAAM;gBAEhI,oBACE9C,OAAA;kBAEEyF,KAAK,EAAE;oBACLK,UAAU,EAAE,SAAS;oBACrBC,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,KAAK;oBACnBe,MAAM,EAAE,SAAS;oBACjBC,MAAM,EAAE,CAAAvF,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE6B,IAAI,MAAKwD,QAAQ,CAACxD,IAAI,GAAG,mBAAmB,GAAG;kBAC3E,CAAE;kBACF2D,OAAO,EAAEA,CAAA,KAAMvF,mBAAmB,CAACoF,QAAQ,CAAE;kBAAA1B,QAAA,gBAE7CpF,OAAA;oBAAIyF,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAY,CAAE;oBAAA7C,QAAA,EAAE0B,QAAQ,CAACxD;kBAAI;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxDxF,OAAA;oBAAGyF,KAAK,EAAE;sBAAEwC,MAAM,EAAE,OAAO;sBAAEC,QAAQ,EAAE;oBAAS,CAAE;oBAAA9C,QAAA,gBAChDpF,OAAA;sBAAAoF,QAAA,EAAQ;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACoC,QAAQ,CAACtB,cAAc,CAAC,CAAC;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACJxF,OAAA;oBAAGyF,KAAK,EAAE;sBAAEwC,MAAM,EAAE,OAAO;sBAAEC,QAAQ,EAAE;oBAAS,CAAE;oBAAA9C,QAAA,gBAChDpF,OAAA;sBAAAoF,QAAA,EAAQ;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACqC,YAAY,CAAC3B,OAAO,CAAC,CAAC,CAAC,EAAC,KACvD;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJxF,OAAA;oBAAGyF,KAAK,EAAE;sBAAEwC,MAAM,EAAE,OAAO;sBAAEC,QAAQ,EAAE;oBAAS,CAAE;oBAAA9C,QAAA,gBAChDpF,OAAA;sBAAAoF,QAAA,EAAQ;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACsB,QAAQ,CAACqB,MAAM,CAACjC,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACJxF,OAAA;oBAAKyF,KAAK,EAAE;sBACVK,UAAU,EAAE+B,YAAY,GAAG,CAAC,GAAG,SAAS,GAAGA,YAAY,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;sBACnFO,MAAM,EAAE,KAAK;sBACbpC,YAAY,EAAE,KAAK;sBACnBqC,SAAS,EAAE;oBACb;kBAAE;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA,GAzBAsB,QAAQ,CAACxD,IAAI;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0Bf,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL/D,gBAAgB,iBACfzB,OAAA;YAAKyF,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAChGpF,OAAA;cAAAoF,QAAA,GAAI,qBAAI,EAAC3D,gBAAgB,CAAC6B,IAAI,EAAC,uBAAqB;YAAA;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEzDxF,OAAA;cAAKyF,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,EACvG3D,gBAAgB,CAAC0F,iBAAiB,CAACN,GAAG,CAAC,CAACyB,QAAQ,EAAEC,KAAK,kBACtDvI,OAAA;gBAEEyF,KAAK,EAAE;kBACLK,UAAU,EAAE,SAAS;kBACrBC,OAAO,EAAE,MAAM;kBACfC,YAAY,EAAE,KAAK;kBACnBgB,MAAM,EAAE;gBACV,CAAE;gBAAA5B,QAAA,gBAEFpF,OAAA;kBAAIyF,KAAK,EAAE;oBAAEwC,MAAM,EAAE,YAAY;oBAAEO,aAAa,EAAE;kBAAa,CAAE;kBAAApD,QAAA,EAC9DkD,QAAQ,CAACG,YAAY,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG;gBAAC;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eAELxF,OAAA;kBAAKyF,KAAK,EAAE;oBAAEyC,QAAQ,EAAE;kBAAS,CAAE;kBAAA9C,QAAA,gBACjCpF,OAAA;oBAAAoF,QAAA,gBAAGpF,OAAA;sBAAAoF,QAAA,EAAQ;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC8C,QAAQ,CAACf,IAAI,CAACjB,cAAc,CAAC,CAAC;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9DxF,OAAA;oBAAAoF,QAAA,gBAAGpF,OAAA;sBAAAoF,QAAA,EAAQ;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC8C,QAAQ,CAACK,OAAO;kBAAA;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnDxF,OAAA;oBAAAoF,QAAA,gBAAGpF,OAAA;sBAAAoF,QAAA,EAAQ;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC8C,QAAQ,CAACM,QAAQ;kBAAA;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrDxF,OAAA;oBAAAoF,QAAA,gBAAGpF,OAAA;sBAAAoF,QAAA,EAAQ;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC8C,QAAQ,CAACO,UAAU;kBAAA;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzDxF,OAAA;oBAAAoF,QAAA,gBAAGpF,OAAA;sBAAAoF,QAAA,EAAQ;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC8C,QAAQ,CAACQ,MAAM,CAAC5C,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAE5DxF,OAAA;oBAAKyF,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAS,CAAE;oBAAA7C,QAAA,gBAC/BpF,OAAA;sBAAKyF,KAAK,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEqD,cAAc,EAAE,eAAe;wBAAElD,YAAY,EAAE;sBAAM,CAAE;sBAAAT,QAAA,gBACpFpF,OAAA;wBAAAoF,QAAA,EAAM;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvBxF,OAAA;wBAAMyF,KAAK,EAAE;0BAAEiC,KAAK,EAAEY,QAAQ,CAACR,SAAS,GAAG,CAAC,GAAG,SAAS,GAAGQ,QAAQ,CAACR,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG;wBAAU,CAAE;wBAAA1C,QAAA,GACzGkD,QAAQ,CAACR,SAAS,CAAC5B,OAAO,CAAC,CAAC,CAAC,EAAC,KACjC;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNxF,OAAA;sBAAKyF,KAAK,EAAE;wBACVK,UAAU,EAAE,MAAM;wBAClBsC,MAAM,EAAE,KAAK;wBACbpC,YAAY,EAAE,KAAK;wBACnBgD,QAAQ,EAAE;sBACZ,CAAE;sBAAA5D,QAAA,eACApF,OAAA;wBAAKyF,KAAK,EAAE;0BACVK,UAAU,EAAEwC,QAAQ,CAACR,SAAS,GAAG,CAAC,GAAG,SAAS,GAAGQ,QAAQ,CAACR,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;0BAC/FM,MAAM,EAAE,MAAM;0BACda,KAAK,EAAE,GAAIX,QAAQ,CAACR,SAAS,GAAG,EAAE,GAAI,GAAG,GAAG;0BAC5CoB,UAAU,EAAE;wBACd;sBAAE;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENxF,OAAA;oBAAKyF,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAS,CAAE;oBAAA7C,QAAA,gBAC/BpF,OAAA;sBAAKyF,KAAK,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEqD,cAAc,EAAE,eAAe;wBAAElD,YAAY,EAAE;sBAAM,CAAE;sBAAAT,QAAA,gBACpFpF,OAAA;wBAAAoF,QAAA,EAAM;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvBxF,OAAA;wBAAMyF,KAAK,EAAE;0BAAEiC,KAAK,EAAEY,QAAQ,CAACN,SAAS,GAAG,CAAC,GAAG,SAAS,GAAGM,QAAQ,CAACN,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG;wBAAU,CAAE;wBAAA5C,QAAA,GACzGkD,QAAQ,CAACN,SAAS,CAAC9B,OAAO,CAAC,CAAC,CAAC,EAAC,KACjC;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNxF,OAAA;sBAAKyF,KAAK,EAAE;wBACVK,UAAU,EAAE,MAAM;wBAClBsC,MAAM,EAAE,KAAK;wBACbpC,YAAY,EAAE,KAAK;wBACnBgD,QAAQ,EAAE;sBACZ,CAAE;sBAAA5D,QAAA,eACApF,OAAA;wBAAKyF,KAAK,EAAE;0BACVK,UAAU,EAAEwC,QAAQ,CAACN,SAAS,GAAG,CAAC,GAAG,SAAS,GAAGM,QAAQ,CAACN,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;0BAC/FI,MAAM,EAAE,MAAM;0BACda,KAAK,EAAE,GAAIX,QAAQ,CAACN,SAAS,GAAG,EAAE,GAAI,GAAG,GAAG;0BAC5CkB,UAAU,EAAE;wBACd;sBAAE;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENxF,OAAA;oBAAKyF,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAS,CAAE;oBAAA7C,QAAA,gBAC/BpF,OAAA;sBAAKyF,KAAK,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEqD,cAAc,EAAE,eAAe;wBAAElD,YAAY,EAAE;sBAAM,CAAE;sBAAAT,QAAA,gBACpFpF,OAAA;wBAAAoF,QAAA,EAAM;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC3BxF,OAAA;wBAAMyF,KAAK,EAAE;0BAAEiC,KAAK,EAAE;wBAAU,CAAE;wBAAAtC,QAAA,GAC/BkD,QAAQ,CAACa,aAAa,CAACjD,OAAO,CAAC,CAAC,CAAC,EAAC,KACrC;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNxF,OAAA;sBAAKyF,KAAK,EAAE;wBACVK,UAAU,EAAE,MAAM;wBAClBsC,MAAM,EAAE,KAAK;wBACbpC,YAAY,EAAE,KAAK;wBACnBgD,QAAQ,EAAE;sBACZ,CAAE;sBAAA5D,QAAA,eACApF,OAAA;wBAAKyF,KAAK,EAAE;0BACVK,UAAU,EAAE,SAAS;0BACrBsC,MAAM,EAAE,MAAM;0BACda,KAAK,EAAE,GAAIX,QAAQ,CAACa,aAAa,GAAG,EAAE,GAAI,GAAG,GAAG;0BAChDD,UAAU,EAAE;wBACd;sBAAE;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GApFD+C,KAAK;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqFP,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxF,OAAA;cAAKyF,KAAK,EAAE;gBAAE4C,SAAS,EAAE;cAAO,CAAE;cAAAjD,QAAA,gBAChCpF,OAAA;gBAAAoF,QAAA,EAAI;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BxF,OAAA;gBAAKyF,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,mBAAmB,EAAE,sCAAsC;kBAAEC,GAAG,EAAE;gBAAO,CAAE;gBAAAR,QAAA,gBACxGpF,OAAA;kBACEiH,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,CAACpF,WAAW,EAAE;sBAChB4B,eAAe,CAAC,+BAA+B,EAAE,SAAS,CAAC;sBAC3D3B,cAAc,CAAC,IAAI,CAAC;oBACtB;kBACF,CAAE;kBACFsH,QAAQ,EAAEvH,WAAY;kBACtB4D,KAAK,EAAE;oBACLK,UAAU,EAAEjE,WAAW,GAAG,MAAM,GAAG,SAAS;oBAC5C6F,KAAK,EAAE,MAAM;oBACbV,MAAM,EAAE,MAAM;oBACdjB,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,KAAK;oBACnBe,MAAM,EAAElF,WAAW,GAAG,aAAa,GAAG;kBACxC,CAAE;kBAAAuD,QAAA,GACH,+BACoB,eAAApF,OAAA;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzBxF,OAAA;oBAAAoF,QAAA,EAAO;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eAETxF,OAAA;kBACEiH,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,CAACpF,WAAW,EAAE;sBAChB4B,eAAe,CAAC,+BAA+B,EAAE,SAAS,CAAC;sBAC3D3B,cAAc,CAAC,IAAI,CAAC;oBACtB;kBACF,CAAE;kBACFsH,QAAQ,EAAEvH,WAAY;kBACtB4D,KAAK,EAAE;oBACLK,UAAU,EAAEjE,WAAW,GAAG,MAAM,GAAG,SAAS;oBAC5C6F,KAAK,EAAE,MAAM;oBACbV,MAAM,EAAE,MAAM;oBACdjB,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,KAAK;oBACnBe,MAAM,EAAElF,WAAW,GAAG,aAAa,GAAG;kBACxC,CAAE;kBAAAuD,QAAA,GACH,mCACwB,eAAApF,OAAA;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7BxF,OAAA;oBAAAoF,QAAA,EAAO;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eAETxF,OAAA;kBACEiH,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,CAACpF,WAAW,EAAE;sBAChB4B,eAAe,CAAC,8BAA8B,EAAE,SAAS,CAAC;sBAC1D3B,cAAc,CAAC,IAAI,CAAC;oBACtB;kBACF,CAAE;kBACFsH,QAAQ,EAAEvH,WAAY;kBACtB4D,KAAK,EAAE;oBACLK,UAAU,EAAEjE,WAAW,GAAG,MAAM,GAAG,SAAS;oBAC5C6F,KAAK,EAAE,MAAM;oBACbV,MAAM,EAAE,MAAM;oBACdjB,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,KAAK;oBACnBe,MAAM,EAAElF,WAAW,GAAG,aAAa,GAAG;kBACxC,CAAE;kBAAAuD,QAAA,GACH,+BACoB,eAAApF,OAAA;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzBxF,OAAA;oBAAAoF,QAAA,EAAO;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDxF,OAAA;YAAKyF,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE;YAAM,CAAE;YAAAZ,QAAA,gBAC1EpF,OAAA;cAAAoF,QAAA,EAAI;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEhC,CAAC,MAAM;cACN,MAAM6D,OAAO,GAAGjJ,SAAS,CACtBsG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,KAAK/F,aAAa,CAACyC,IAAI,CAAC,CAC3CgG,OAAO,CAAC3C,CAAC,IAAIA,CAAC,CAACQ,iBAAiB,CAAC;cAEpC,MAAMS,QAAQ,GAAGyB,OAAO,CAACjC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACC,IAAI,EAAE,CAAC,CAAC;cAChE,MAAMgC,WAAW,GAAG,CAAC,CAAC;cACtB,MAAMC,aAAa,GAAG,CAAC,CAAC;cACxB,MAAMC,cAAc,GAAG,CAAC,CAAC;cAEzBJ,OAAO,CAACK,OAAO,CAACpC,GAAG,IAAI;gBACrBiC,WAAW,CAACjC,GAAG,CAACmB,YAAY,CAAC,GAAG,CAACc,WAAW,CAACjC,GAAG,CAACmB,YAAY,CAAC,IAAI,CAAC,IAAInB,GAAG,CAACC,IAAI;gBAC/EiC,aAAa,CAAClC,GAAG,CAACqB,OAAO,CAAC,GAAG,CAACa,aAAa,CAAClC,GAAG,CAACqB,OAAO,CAAC,IAAI,CAAC,IAAIrB,GAAG,CAACC,IAAI;gBACzEkC,cAAc,CAACnC,GAAG,CAACsB,QAAQ,CAAC,GAAG,CAACa,cAAc,CAACnC,GAAG,CAACsB,QAAQ,CAAC,IAAI,CAAC,IAAItB,GAAG,CAACC,IAAI;cAC/E,CAAC,CAAC;cAEF,oBACEvH,OAAA;gBAAKyF,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,mBAAmB,EAAE,sCAAsC;kBAAEC,GAAG,EAAE;gBAAO,CAAE;gBAAAR,QAAA,gBACxGpF,OAAA;kBAAAoF,QAAA,gBACEpF,OAAA;oBAAAoF,QAAA,EAAI;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACzBxC,MAAM,CAAC2G,OAAO,CAACJ,WAAW,CAAC,CAAC1C,GAAG,CAAC,CAAC,CAAC+C,SAAS,EAAEC,KAAK,CAAC,kBAClD7J,OAAA;oBAAqByF,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAQ,CAAE;oBAAA7C,QAAA,gBAC9CpF,OAAA;sBAAKyF,KAAK,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEqD,cAAc,EAAE;sBAAgB,CAAE;sBAAA3D,QAAA,gBAC/DpF,OAAA;wBAAMyF,KAAK,EAAE;0BAAE+C,aAAa,EAAE;wBAAa,CAAE;wBAAApD,QAAA,EAAEwE,SAAS,CAAClB,OAAO,CAAC,GAAG,EAAE,GAAG;sBAAC;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAClFxF,OAAA;wBAAAoF,QAAA,GAAO,CAAEyE,KAAK,GAAGjC,QAAQ,GAAI,GAAG,EAAE1B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACNxF,OAAA;sBAAKyF,KAAK,EAAE;wBAAEK,UAAU,EAAE,MAAM;wBAAEsC,MAAM,EAAE,KAAK;wBAAEpC,YAAY,EAAE,KAAK;wBAAEgD,QAAQ,EAAE;sBAAS,CAAE;sBAAA5D,QAAA,eACzFpF,OAAA;wBAAKyF,KAAK,EAAE;0BACVK,UAAU,EAAE,SAAS;0BACrBsC,MAAM,EAAE,MAAM;0BACda,KAAK,EAAE,GAAIY,KAAK,GAAGjC,QAAQ,GAAI,GAAG;wBACpC;sBAAE;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA,GAXEoE,SAAS;oBAAAvE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAYd,CACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENxF,OAAA;kBAAAoF,QAAA,gBACEpF,OAAA;oBAAAoF,QAAA,EAAI;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACpBxC,MAAM,CAAC2G,OAAO,CAACH,aAAa,CAAC,CAAC3C,GAAG,CAAC,CAAC,CAAC8B,OAAO,EAAEkB,KAAK,CAAC,kBAClD7J,OAAA;oBAAmByF,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAQ,CAAE;oBAAA7C,QAAA,gBAC5CpF,OAAA;sBAAKyF,KAAK,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEqD,cAAc,EAAE;sBAAgB,CAAE;sBAAA3D,QAAA,gBAC/DpF,OAAA;wBAAMyF,KAAK,EAAE;0BAAE+C,aAAa,EAAE;wBAAa,CAAE;wBAAApD,QAAA,EAAEuD;sBAAO;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC9DxF,OAAA;wBAAAoF,QAAA,GAAO,CAAEyE,KAAK,GAAGjC,QAAQ,GAAI,GAAG,EAAE1B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACNxF,OAAA;sBAAKyF,KAAK,EAAE;wBAAEK,UAAU,EAAE,MAAM;wBAAEsC,MAAM,EAAE,KAAK;wBAAEpC,YAAY,EAAE,KAAK;wBAAEgD,QAAQ,EAAE;sBAAS,CAAE;sBAAA5D,QAAA,eACzFpF,OAAA;wBAAKyF,KAAK,EAAE;0BACVK,UAAU,EAAE,SAAS;0BACrBsC,MAAM,EAAE,MAAM;0BACda,KAAK,EAAE,GAAIY,KAAK,GAAGjC,QAAQ,GAAI,GAAG;wBACpC;sBAAE;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA,GAXEmD,OAAO;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAYZ,CACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENxF,OAAA;kBAAAoF,QAAA,gBACEpF,OAAA;oBAAAoF,QAAA,EAAI;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACnBxC,MAAM,CAAC2G,OAAO,CAACF,cAAc,CAAC,CAAC5C,GAAG,CAAC,CAAC,CAAC+B,QAAQ,EAAEiB,KAAK,CAAC,kBACpD7J,OAAA;oBAAoByF,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAQ,CAAE;oBAAA7C,QAAA,gBAC7CpF,OAAA;sBAAKyF,KAAK,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEqD,cAAc,EAAE;sBAAgB,CAAE;sBAAA3D,QAAA,gBAC/DpF,OAAA;wBAAMyF,KAAK,EAAE;0BAAE+C,aAAa,EAAE;wBAAa,CAAE;wBAAApD,QAAA,EAAEwD;sBAAQ;wBAAAvD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC/DxF,OAAA;wBAAAoF,QAAA,GAAO,CAAEyE,KAAK,GAAGjC,QAAQ,GAAI,GAAG,EAAE1B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACNxF,OAAA;sBAAKyF,KAAK,EAAE;wBAAEK,UAAU,EAAE,MAAM;wBAAEsC,MAAM,EAAE,KAAK;wBAAEpC,YAAY,EAAE,KAAK;wBAAEgD,QAAQ,EAAE;sBAAS,CAAE;sBAAA5D,QAAA,eACzFpF,OAAA;wBAAKyF,KAAK,EAAE;0BACVK,UAAU,EAAE,SAAS;0BACrBsC,MAAM,EAAE,MAAM;0BACda,KAAK,EAAE,GAAIY,KAAK,GAAGjC,QAAQ,GAAI,GAAG;wBACpC;sBAAE;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA,GAXEoD,QAAQ;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAYb,CACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAEV,CAAC,EAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,SAAS;QACZ,oBACExF,OAAA;UAAAoF,QAAA,gBACEpF,OAAA;YAAAoF,QAAA,EAAI;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE/BxF,OAAA;YAAKyF,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAChGpF,OAAA;cAAAoF,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7BxF,OAAA;cAAKyF,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,SAAS;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,gBAC3EpF,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAAoF,QAAA,gBAAGpF,OAAA;oBAAAoF,QAAA,EAAQ;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC3E,aAAa,CAACX,QAAQ,CAACoG,cAAc,CAAC,CAAC,EAAC,OAAK;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAChFxF,OAAA;kBAAAoF,QAAA,gBAAGpF,OAAA;oBAAAoF,QAAA,EAAQ;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,MAAE,EAAC3E,aAAa,CAACoF,cAAc,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,OAAK;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACvFxF,OAAA;kBAAAoF,QAAA,gBAAGpF,OAAA;oBAAAoF,QAAA,EAAQ;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,MAAE,EAAC3E,aAAa,CAACsF,gBAAgB,CAACD,OAAO,CAAC,CAAC,CAAC,EAAC,OAAK;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC3FxF,OAAA;kBAAAoF,QAAA,gBAAGpF,OAAA;oBAAAoF,QAAA,EAAQ;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC,CAAC3E,aAAa,CAACoF,cAAc,GAAGpF,aAAa,CAACsF,gBAAgB,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,OAAK;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnH,CAAC,eACNxF,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAAoF,QAAA,gBAAGpF,OAAA;oBAAAoF,QAAA,EAAQ;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC,CAAC3E,aAAa,CAACiJ,gBAAgB,GAAG,GAAG,EAAE5D,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC9FxF,OAAA;kBAAAoF,QAAA,gBAAGpF,OAAA;oBAAAoF,QAAA,EAAQ;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC,CAAC3E,aAAa,CAACuF,SAAS,GAAG,GAAG,EAAEF,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxF,OAAA;YAAKyF,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAChGpF,OAAA;cAAAoF,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCxF,OAAA;cAAKyF,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,EACvGpC,MAAM,CAAC2G,OAAO,CAACtI,eAAe,CAAC,CAACwF,GAAG,CAAC,CAAC,CAACvD,IAAI,EAAEyG,QAAQ,CAAC,kBACpD/J,OAAA;gBAAgByF,KAAK,EAAE;kBAAEK,UAAU,EAAE,SAAS;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,YAAY,EAAE,KAAK;kBAAEgE,SAAS,EAAE;gBAAS,CAAE;gBAAA5E,QAAA,gBAC1GpF,OAAA;kBAAAoF,QAAA,EAAS9B,IAAI,CAACoF,OAAO,CAAC,GAAG,EAAE,GAAG;gBAAC;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eACzCxF,OAAA;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNxF,OAAA;kBAAMyF,KAAK,EAAE;oBAAEiC,KAAK,EAAE;kBAAU,CAAE;kBAAAtC,QAAA,GAAE2E,QAAQ,CAACE,aAAa,CAAC/D,OAAO,CAAC,CAAC,CAAC,EAAC,OAAK;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClFxF,OAAA;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNxF,OAAA;kBAAOyF,KAAK,EAAE;oBAAEyE,OAAO,EAAE;kBAAI,CAAE;kBAAA9E,QAAA,EAC5B2E,QAAQ,CAACE,aAAa,GAAGF,QAAQ,CAACI,UAAU,GAAG,IAAI,GAAG;gBAAI;kBAAA9E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA,GAPAlC,IAAI;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQT,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxF,OAAA;YAAKyF,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE;YAAM,CAAE;YAAAZ,QAAA,gBAC1EpF,OAAA;cAAAoF,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BxF,OAAA;cAAKyF,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,gBACxGpF,OAAA;gBACEiH,OAAO,EAAEA,CAAA,KAAM;kBACb,IAAI,CAACpF,WAAW,EAAE;oBAChB4B,eAAe,CAAC,wCAAwC,EAAE,SAAS,CAAC;oBACpE3B,cAAc,CAAC,IAAI,CAAC;kBACtB;gBACF,CAAE;gBACFsH,QAAQ,EAAEvH,WAAY;gBACtB4D,KAAK,EAAE;kBACLK,UAAU,EAAEjE,WAAW,GAAG,MAAM,GAAG,SAAS;kBAC5C6F,KAAK,EAAE,MAAM;kBACbV,MAAM,EAAE,MAAM;kBACdjB,OAAO,EAAE,MAAM;kBACfC,YAAY,EAAE,KAAK;kBACnBe,MAAM,EAAElF,WAAW,GAAG,aAAa,GAAG;gBACxC,CAAE;gBAAAuD,QAAA,GACH,yCACyB,eAAApF,OAAA;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9BxF,OAAA;kBAAAoF,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eAETxF,OAAA;gBACEiH,OAAO,EAAEA,CAAA,KAAM;kBACb,IAAI,CAACpF,WAAW,EAAE;oBAChB4B,eAAe,CAAC,6BAA6B,EAAE,SAAS,CAAC;oBACzD3B,cAAc,CAAC,IAAI,CAAC;kBACtB;gBACF,CAAE;gBACFsH,QAAQ,EAAEvH,WAAY;gBACtB4D,KAAK,EAAE;kBACLK,UAAU,EAAEjE,WAAW,GAAG,MAAM,GAAG,SAAS;kBAC5C6F,KAAK,EAAE,MAAM;kBACbV,MAAM,EAAE,MAAM;kBACdjB,OAAO,EAAE,MAAM;kBACfC,YAAY,EAAE,KAAK;kBACnBe,MAAM,EAAElF,WAAW,GAAG,aAAa,GAAG;gBACxC,CAAE;gBAAAuD,QAAA,GACH,4BACiB,eAAApF,OAAA;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtBxF,OAAA;kBAAAoF,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eAETxF,OAAA;gBACEiH,OAAO,EAAEA,CAAA,KAAM;kBACb,IAAI,CAACpF,WAAW,EAAE;oBAChB4B,eAAe,CAAC,yBAAyB,EAAE,SAAS,CAAC;oBACrD3B,cAAc,CAAC,IAAI,CAAC;kBACtB;gBACF,CAAE;gBACFsH,QAAQ,EAAEvH,WAAY;gBACtB4D,KAAK,EAAE;kBACLK,UAAU,EAAEjE,WAAW,GAAG,MAAM,GAAG,SAAS;kBAC5C6F,KAAK,EAAE,MAAM;kBACbV,MAAM,EAAE,MAAM;kBACdjB,OAAO,EAAE,MAAM;kBACfC,YAAY,EAAE,KAAK;kBACnBe,MAAM,EAAElF,WAAW,GAAG,aAAa,GAAG;gBACxC,CAAE;gBAAAuD,QAAA,GACH,yBACc,eAAApF,OAAA;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnBxF,OAAA;kBAAAoF,QAAA,EAAO;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,YAAY;QACf,oBACExF,OAAA;UAAAoF,QAAA,gBACEpF,OAAA;YAAAoF,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEjCxF,OAAA;YAAKyF,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAChGpF,OAAA;cAAAoF,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7BxF,OAAA;cAAKyF,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,EACvGpC,MAAM,CAAC2G,OAAO,CAAC9I,aAAa,CAACuJ,eAAe,CAAC,CAACvD,GAAG,CAAC,CAAC,CAAC/B,QAAQ,EAAEuF,MAAM,CAAC,kBACpErK,OAAA;gBAAoByF,KAAK,EAAE;kBAAEK,UAAU,EAAE,SAAS;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,YAAY,EAAE;gBAAM,CAAE;gBAAAZ,QAAA,gBACzFpF,OAAA;kBAAIyF,KAAK,EAAE;oBAAE+C,aAAa,EAAE,YAAY;oBAAEP,MAAM,EAAE;kBAAa,CAAE;kBAAA7C,QAAA,GAC9DN,QAAQ,KAAK,UAAU,IAAI,IAAI,EAC/BA,QAAQ,KAAK,UAAU,IAAI,IAAI,EAC/BA,QAAQ,KAAK,QAAQ,IAAI,IAAI,EAC7BA,QAAQ,KAAK,gBAAgB,IAAI,KAAK,EACtC,GAAG,GAAGA,QAAQ;gBAAA;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACLxF,OAAA;kBAAAoF,QAAA,gBAAGpF,OAAA;oBAAAoF,QAAA,EAAQ;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC6E,MAAM,CAACnE,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAClD3E,aAAa,CAACyJ,gBAAgB,CAACxF,QAAQ,CAAC,iBACvC9E,OAAA;kBAAAoF,QAAA,gBAAGpF,OAAA;oBAAAoF,QAAA,EAAQ;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC3E,aAAa,CAACyJ,gBAAgB,CAACxF,QAAQ,CAAC;gBAAA;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC/E;cAAA,GAXOV,QAAQ;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYb,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxF,OAAA;YAAKyF,KAAK,EAAE;cAAEK,UAAU,EAAE,SAAS;cAAEC,OAAO,EAAE,MAAM;cAAEC,YAAY,EAAE,KAAK;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAChGpF,OAAA;cAAAoF,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCxF,OAAA;cAAKyF,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,mBAAmB,EAAE,sCAAsC;gBAAEC,GAAG,EAAE;cAAO,CAAE;cAAAR,QAAA,EACvGpC,MAAM,CAAC2G,OAAO,CAACpI,YAAY,CAAC,CAACsF,GAAG,CAAC,CAAC,CAACvD,IAAI,EAAEiH,IAAI,CAAC,KAAK;gBAClD,MAAMC,YAAY,GAAG3J,aAAa,CAACU,YAAY,CAACkJ,QAAQ,CAACnH,IAAI,CAAC;gBAC9D,MAAMoH,WAAW,GAAGH,IAAI,CAACI,aAAa,CAACC,KAAK,CAACC,MAAM,IAAIhK,aAAa,CAACU,YAAY,CAACkJ,QAAQ,CAACI,MAAM,CAAC,CAAC;gBACnG,MAAMC,sBAAsB,GAAG9H,MAAM,CAAC+H,MAAM,CAAClK,aAAa,CAACyJ,gBAAgB,CAAC,CAACG,QAAQ,CAACnH,IAAI,CAAC;gBAE3F,oBACEtD,OAAA;kBAEEyF,KAAK,EAAE;oBACLK,UAAU,EAAE0E,YAAY,GAAG,SAAS,GAAG,SAAS;oBAChDzE,OAAO,EAAE,MAAM;oBACfC,YAAY,EAAE,KAAK;oBACnBgB,MAAM,EAAE8D,sBAAsB,GAAG,mBAAmB,GAAG,gBAAgB;oBACvEZ,OAAO,EAAEQ,WAAW,IAAIF,YAAY,GAAG,CAAC,GAAG;kBAC7C,CAAE;kBAAApF,QAAA,gBAEFpF,OAAA;oBAAIyF,KAAK,EAAE;sBAAEwC,MAAM,EAAE;oBAAa,CAAE;oBAAA7C,QAAA,EAAE9B;kBAAI;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChDxF,OAAA;oBAAGyF,KAAK,EAAE;sBAAEyC,QAAQ,EAAE,QAAQ;sBAAED,MAAM,EAAE;oBAAQ,CAAE;oBAAA7C,QAAA,EAAEmF,IAAI,CAAC9C;kBAAW;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzExF,OAAA;oBAAAoF,QAAA,gBAAGpF,OAAA;sBAAAoF,QAAA,EAAQ;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC+E,IAAI,CAACzF,QAAQ;kBAAA;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjDxF,OAAA;oBAAAoF,QAAA,gBAAGpF,OAAA;sBAAAoF,QAAA,EAAQ;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC+E,IAAI,CAACS,IAAI,EAAC,SAAO;kBAAA;oBAAA3F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,EAE/C+E,IAAI,CAACI,aAAa,CAAC7H,MAAM,GAAG,CAAC,iBAC5B9C,OAAA;oBAAAoF,QAAA,gBAAGpF,OAAA;sBAAAoF,QAAA,EAAQ;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC+E,IAAI,CAACI,aAAa,CAACM,IAAI,CAAC,IAAI,CAAC;kBAAA;oBAAA5F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CACtE,EAEAgF,YAAY,iBACXxK,OAAA;oBAAMyF,KAAK,EAAE;sBAAEiC,KAAK,EAAE,SAAS;sBAAEwD,UAAU,EAAE;oBAAO,CAAE;oBAAA9F,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAC1E,EAEA,CAACgF,YAAY,IAAIE,WAAW,IAAI,CAACI,sBAAsB,iBACtD9K,OAAA;oBACEiH,OAAO,EAAEA,CAAA,KAAMpC,aAAa,CAAC0F,IAAI,CAACzF,QAAQ,EAAExB,IAAI,CAAE;oBAClDmC,KAAK,EAAE;sBACLK,UAAU,EAAE,SAAS;sBACrB4B,KAAK,EAAE,MAAM;sBACbV,MAAM,EAAE,MAAM;sBACdjB,OAAO,EAAE,UAAU;sBACnBC,YAAY,EAAE,KAAK;sBACnBe,MAAM,EAAE,SAAS;sBACjBsB,SAAS,EAAE;oBACb,CAAE;oBAAAjD,QAAA,EACH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,EAEAsF,sBAAsB,iBACrB9K,OAAA;oBAAMyF,KAAK,EAAE;sBAAEiC,KAAK,EAAE,SAAS;sBAAEwD,UAAU,EAAE;oBAAO,CAAE;oBAAA9F,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAC/E;gBAAA,GAzCIlC,IAAI;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0CN,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV;QACE,oBACExF,OAAA;UAAAoF,QAAA,gBACEpF,OAAA;YAAAoF,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBxF,OAAA;YAAAoF,QAAA,EAAG;UAA0E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC;IAEZ;EACF,CAAC;;EAED;EACA,IAAIrE,SAAS,KAAK,SAAS,EAAE;IAC3B,oBACEnB,OAAA;MAAKyF,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEqD,cAAc,EAAE,QAAQ;QAAEoC,UAAU,EAAE,QAAQ;QAAE/C,MAAM,EAAE,OAAO;QAAEtC,UAAU,EAAE;MAAU,CAAE;MAAAV,QAAA,eACtHpF,OAAA;QAAKyF,KAAK,EAAE;UAAEuE,SAAS,EAAE,QAAQ;UAAEtC,KAAK,EAAE;QAAO,CAAE;QAAAtC,QAAA,gBACjDpF,OAAA;UAAAoF,QAAA,EAAI;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjCxF,OAAA;UAAAoF,QAAA,EAAG;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACtCxF,OAAA;UAAKyF,KAAK,EAAE;YAAEwC,MAAM,EAAE;UAAS,CAAE;UAAA7C,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIrE,SAAS,KAAK,mBAAmB,EAAE;IACrC,oBACEnB,OAAA;MAAKyF,KAAK,EAAE;QAAEK,UAAU,EAAE,SAAS;QAAEsF,SAAS,EAAE,OAAO;QAAE1D,KAAK,EAAE;MAAO,CAAE;MAAAtC,QAAA,gBACvEpF,OAAA;QAAKyF,KAAK,EAAE;UAAEuE,SAAS,EAAE,QAAQ;UAAEjE,OAAO,EAAE;QAAO,CAAE;QAAAX,QAAA,gBACnDpF,OAAA;UAAAoF,QAAA,EAAI;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/BxF,OAAA;UAAAoF,QAAA,EAAG;QAAsD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eAENxF,OAAA,CAACF,QAAQ;QACPuL,eAAe,EAAE9H,aAAc;QAC/B+H,eAAe,EAAE,IAAK;QACtB5K,SAAS,EAAEA;MAAU;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eAEFxF,OAAA;QAAKyF,KAAK,EAAE;UAAEM,OAAO,EAAE,MAAM;UAAEwF,QAAQ,EAAE,QAAQ;UAAEtD,MAAM,EAAE;QAAS,CAAE;QAAA7C,QAAA,gBACpEpF,OAAA;UAAAoF,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BxF,OAAA;UAAKyF,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,mBAAmB,EAAE,sCAAsC;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAR,QAAA,EACvG1E,SAAS,CAAC0D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACyC,GAAG,CAACrD,OAAO,iBAChCxD,OAAA;YAEEyF,KAAK,EAAE;cACLK,UAAU,EAAE,SAAS;cACrBC,OAAO,EAAE,MAAM;cACfC,YAAY,EAAE,MAAM;cACpBe,MAAM,EAAE,SAAS;cACjBC,MAAM,EAAE,uBAAuB;cAC/BkC,UAAU,EAAE;YACd,CAAE;YACFsC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACjG,KAAK,CAACkG,WAAW,GAAG,SAAU;YAC5DC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACjG,KAAK,CAACkG,WAAW,GAAG,aAAc;YAChE1E,OAAO,EAAEA,CAAA,KAAM1D,aAAa,CAACC,OAAO,CAAE;YAAA4B,QAAA,gBAEtCpF,OAAA;cAAAoF,QAAA,EAAK5B,OAAO,CAACF;YAAI;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvBxF,OAAA;cAAAoF,QAAA,gBAAGpF,OAAA;gBAAAoF,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChC,OAAO,CAACE,UAAU;YAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnDxF,OAAA;cAAAoF,QAAA,gBAAGpF,OAAA;gBAAAoF,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChC,OAAO,CAACqI,eAAe,CAACnD,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;YAAA;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/ExF,OAAA;cAAAoF,QAAA,gBAAGpF,OAAA;gBAAAoF,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChC,OAAO,CAACtD,QAAQ,CAACoG,cAAc,CAAC,CAAC,EAAC,OAAK;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1ExF,OAAA;cAAAoF,QAAA,gBAAGpF,OAAA;gBAAAoF,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChC,OAAO,CAACpD,SAAS,CAAC0C,MAAM;YAAA;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DxF,OAAA;cAAAoF,QAAA,gBAAGpF,OAAA;gBAAAoF,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChC,OAAO,CAACrD,QAAQ;YAAA;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAlB/ChC,OAAO,CAACF,IAAI;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBd,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIrE,SAAS,KAAK,SAAS,EAAE;IAC3B,oBACEnB,OAAA;MAAKyF,KAAK,EAAE;QACVK,UAAU,EAAE,mDAAmD;QAC/DsF,SAAS,EAAE,OAAO;QAClB1F,OAAO,EAAE,MAAM;QACfqD,cAAc,EAAE,QAAQ;QACxBoC,UAAU,EAAE,QAAQ;QACpBzD,KAAK,EAAE,MAAM;QACbsC,SAAS,EAAE;MACb,CAAE;MAAA5E,QAAA,eACApF,OAAA;QAAAoF,QAAA,gBACEpF,OAAA;UAAIyF,KAAK,EAAE;YAAEyC,QAAQ,EAAE,MAAM;YAAErC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1ExF,OAAA;UAAAoF,QAAA,GAAI,MAAI,EAACvE,aAAa,CAACyC,IAAI,EAAC,cAAY;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7CxF,OAAA;UAAGyF,KAAK,EAAE;YAAEyC,QAAQ,EAAE,QAAQ;YAAED,MAAM,EAAE;UAAS,CAAE;UAAA7C,QAAA,GAAC,yBAC3B,EAACvE,aAAa,CAAC6C,UAAU,EAAC,uCACnD;QAAA;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJxF,OAAA;UAAKyF,KAAK,EAAE;YAAEK,UAAU,EAAE,iBAAiB;YAAEC,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE,MAAM;YAAEiC,MAAM,EAAE;UAAS,CAAE;UAAA7C,QAAA,gBACrGpF,OAAA;YAAAoF,QAAA,gBAAGpF,OAAA;cAAAoF,QAAA,EAAQ;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3E,aAAa,CAACX,QAAQ,CAACoG,cAAc,CAAC,CAAC,EAAC,OAAK;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtFxF,OAAA;YAAAoF,QAAA,gBAAGpF,OAAA;cAAAoF,QAAA,EAAQ;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3E,aAAa,CAACV,QAAQ;UAAA;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChExF,OAAA;YAAAoF,QAAA,gBAAGpF,OAAA;cAAAoF,QAAA,EAAQ;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3E,aAAa,CAACT,SAAS,CAAC0C,MAAM;UAAA;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9ExF,OAAA;YAAAoF,QAAA,gBAAGpF,OAAA;cAAAoF,QAAA,EAAQ;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACzE,IAAI;UAAA;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNxF,OAAA;UACEiH,OAAO,EAAEA,CAAA,KAAM6E,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCvG,KAAK,EAAE;YACLM,OAAO,EAAE,WAAW;YACpBmC,QAAQ,EAAE,QAAQ;YAClBpC,UAAU,EAAE,SAAS;YACrB4B,KAAK,EAAE,MAAM;YACbV,MAAM,EAAE,MAAM;YACdhB,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAE;UACV,CAAE;UAAA3B,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIrE,SAAS,KAAK,QAAQ,EAAE;IAC1B,oBACEnB,OAAA;MAAKyF,KAAK,EAAE;QACVK,UAAU,EAAE,mDAAmD;QAC/DsF,SAAS,EAAE,OAAO;QAClB1F,OAAO,EAAE,MAAM;QACfqD,cAAc,EAAE,QAAQ;QACxBoC,UAAU,EAAE,QAAQ;QACpBzD,KAAK,EAAE,MAAM;QACbsC,SAAS,EAAE;MACb,CAAE;MAAA5E,QAAA,eACApF,OAAA;QAAAoF,QAAA,gBACEpF,OAAA;UAAIyF,KAAK,EAAE;YAAEyC,QAAQ,EAAE,MAAM;YAAErC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxExF,OAAA;UAAAoF,QAAA,GAAI,cAAY,EAACvE,aAAa,CAACyC,IAAI;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzCxF,OAAA;UAAGyF,KAAK,EAAE;YAAEyC,QAAQ,EAAE,QAAQ;YAAED,MAAM,EAAE;UAAS,CAAE;UAAA7C,QAAA,GAAC,eACrC,EAACvE,aAAa,CAAC6C,UAAU,EAAC,wBACzC;QAAA;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJxF,OAAA;UAAKyF,KAAK,EAAE;YAAEK,UAAU,EAAE,iBAAiB;YAAEC,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE,MAAM;YAAEiC,MAAM,EAAE;UAAS,CAAE;UAAA7C,QAAA,gBACrGpF,OAAA;YAAAoF,QAAA,gBAAGpF,OAAA;cAAAoF,QAAA,EAAQ;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3E,aAAa,CAACX,QAAQ,CAACoG,cAAc,CAAC,CAAC,EAAC,OAAK;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtFxF,OAAA;YAAAoF,QAAA,gBAAGpF,OAAA;cAAAoF,QAAA,EAAQ;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3E,aAAa,CAACP,SAAS;UAAA;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClExF,OAAA;YAAAoF,QAAA,gBAAGpF,OAAA;cAAAoF,QAAA,EAAQ;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3E,aAAa,CAACN,UAAU;UAAA;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpExF,OAAA;YAAAoF,QAAA,gBAAGpF,OAAA;cAAAoF,QAAA,EAAQ;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACzE,IAAI;UAAA;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNxF,OAAA;UACEiH,OAAO,EAAEA,CAAA,KAAM6E,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCvG,KAAK,EAAE;YACLM,OAAO,EAAE,WAAW;YACpBmC,QAAQ,EAAE,QAAQ;YAClBpC,UAAU,EAAE,SAAS;YACrB4B,KAAK,EAAE,MAAM;YACbV,MAAM,EAAE,MAAM;YACdhB,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAE;UACV,CAAE;UAAA3B,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,oBACExF,OAAA;IAAKyF,KAAK,EAAE;MAAEK,UAAU,EAAE,SAAS;MAAEsF,SAAS,EAAE,OAAO;MAAE1D,KAAK,EAAE;IAAO,CAAE;IAAAtC,QAAA,gBAEvEpF,OAAA;MAAKyF,KAAK,EAAE;QACVK,UAAU,EAAE,SAAS;QACrBC,OAAO,EAAE,WAAW;QACpBkG,YAAY,EAAE,mBAAmB;QACjCvG,OAAO,EAAE,MAAM;QACfqD,cAAc,EAAE,eAAe;QAC/BoC,UAAU,EAAE;MACd,CAAE;MAAA/F,QAAA,gBACApF,OAAA;QAAAoF,QAAA,gBACEpF,OAAA;UAAIyF,KAAK,EAAE;YAAEwC,MAAM,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAA9C,QAAA,GAAC,eAAG,EAACvE,aAAa,CAACyC,IAAI;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1ExF,OAAA;UAAGyF,KAAK,EAAE;YAAEwC,MAAM,EAAE,CAAC;YAAEiC,OAAO,EAAE;UAAI,CAAE;UAAA9E,QAAA,GAAEvE,aAAa,CAAC6C,UAAU,EAAC,UAAG,EAAC7C,aAAa,CAACgL,eAAe,CAACnD,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;QAAA;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtH,CAAC,eAENxF,OAAA;QAAKyF,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE,MAAM;UAAEuF,UAAU,EAAE;QAAS,CAAE;QAAA/F,QAAA,gBACjEpF,OAAA;UAAKyF,KAAK,EAAE;YAAEuE,SAAS,EAAE;UAAS,CAAE;UAAA5E,QAAA,gBAClCpF,OAAA;YAAKyF,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEgD,UAAU,EAAE;YAAO,CAAE;YAAA9F,QAAA,GAAC,eAAG,EAACvE,aAAa,CAACX,QAAQ,CAACoG,cAAc,CAAC,CAAC;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1GxF,OAAA;YAAKyF,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEgC,OAAO,EAAE;YAAI,CAAE;YAAA9E,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACNxF,OAAA;UAAKyF,KAAK,EAAE;YAAEuE,SAAS,EAAE;UAAS,CAAE;UAAA5E,QAAA,gBAClCpF,OAAA;YAAKyF,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEgD,UAAU,EAAE;YAAO,CAAE;YAAA9F,QAAA,GAAC,SAAE,EAACvE,aAAa,CAACV,QAAQ;UAAA;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxFxF,OAAA;YAAKyF,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEgC,OAAO,EAAE;YAAI,CAAE;YAAA9E,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACNxF,OAAA;UAAKyF,KAAK,EAAE;YAAEuE,SAAS,EAAE;UAAS,CAAE;UAAA5E,QAAA,gBAClCpF,OAAA;YAAKyF,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEgD,UAAU,EAAE;YAAO,CAAE;YAAA9F,QAAA,GAAC,qBAAI,EAACvE,aAAa,CAACP,SAAS;UAAA;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3FxF,OAAA;YAAKyF,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEgC,OAAO,EAAE;YAAI,CAAE;YAAA9E,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACNxF,OAAA;UAAKyF,KAAK,EAAE;YAAEuE,SAAS,EAAE;UAAS,CAAE;UAAA5E,QAAA,gBAClCpF,OAAA;YAAKyF,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEgD,UAAU,EAAE;YAAO,CAAE;YAAA9F,QAAA,GAAC,eAAG,EAACvE,aAAa,CAACN,UAAU;UAAA;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3FxF,OAAA;YAAKyF,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEgC,OAAO,EAAE;YAAI,CAAE;YAAA9E,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACNxF,OAAA;UAAKyF,KAAK,EAAE;YAAEuE,SAAS,EAAE;UAAS,CAAE;UAAA5E,QAAA,gBAClCpF,OAAA;YAAKyF,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEgD,UAAU,EAAE;YAAO,CAAE;YAAA9F,QAAA,GAAC,eAAG,EAACrE,IAAI;UAAA;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvExF,OAAA;YAAKyF,KAAK,EAAE;cAAEyC,QAAQ,EAAE,QAAQ;cAAEgC,OAAO,EAAE;YAAI,CAAE;YAAA9E,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLvD,aAAa,CAACa,MAAM,GAAG,CAAC,iBACvB9C,OAAA;MAAKyF,KAAK,EAAE;QAAEM,OAAO,EAAE;MAAY,CAAE;MAAAX,QAAA,EAClCnD,aAAa,CAAC4E,GAAG,CAAChD,YAAY,iBAC7B7D,OAAA;QAEEyF,KAAK,EAAE;UACLK,UAAU,EAAEjC,YAAY,CAACD,IAAI,KAAK,OAAO,GAAG,SAAS,GAC1CC,YAAY,CAACD,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;UAClEmC,OAAO,EAAE,UAAU;UACnBkC,MAAM,EAAE,OAAO;UACfjC,YAAY,EAAE,KAAK;UACnBkC,QAAQ,EAAE,QAAQ;UAClBxC,OAAO,EAAE,MAAM;UACfqD,cAAc,EAAE;QAClB,CAAE;QAAA3D,QAAA,gBAEFpF,OAAA;UAAAoF,QAAA,EAAOvB,YAAY,CAACF;QAAI;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChCxF,OAAA;UAAMyF,KAAK,EAAE;YAAEyE,OAAO,EAAE;UAAI,CAAE;UAAA9E,QAAA,EAAEvB,YAAY,CAACI;QAAS;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,GAbzD3B,YAAY,CAACC,EAAE;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAcjB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAGDxF,OAAA;MAAKyF,KAAK,EAAE;QACVK,UAAU,EAAE,SAAS;QACrBC,OAAO,EAAE,QAAQ;QACjBkG,YAAY,EAAE;MAChB,CAAE;MAAA7G,QAAA,EACC,CAAC,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,CAAC,CAACyB,GAAG,CAACqF,GAAG,iBACnFlM,OAAA;QAEEiH,OAAO,EAAEA,CAAA,KAAMrF,YAAY,CAACsK,GAAG,CAAE;QACjCzG,KAAK,EAAE;UACLK,UAAU,EAAEnE,SAAS,KAAKuK,GAAG,GAAG,SAAS,GAAG,aAAa;UACzDxE,KAAK,EAAE,MAAM;UACbV,MAAM,EAAE,MAAM;UACdjB,OAAO,EAAE,WAAW;UACpBkC,MAAM,EAAE,OAAO;UACflB,MAAM,EAAE,SAAS;UACjBf,YAAY,EAAE,aAAa;UAC3BwC,aAAa,EAAE,YAAY;UAC3BN,QAAQ,EAAE;QACZ,CAAE;QAAA9C,QAAA,GAED8G,GAAG,KAAK,UAAU,IAAI,KAAK,EAC3BA,GAAG,KAAK,YAAY,IAAI,IAAI,EAC5BA,GAAG,KAAK,SAAS,IAAI,IAAI,EACzBA,GAAG,KAAK,UAAU,IAAI,IAAI,EAC1BA,GAAG,KAAK,WAAW,IAAI,IAAI,EAC3BA,GAAG,KAAK,YAAY,IAAI,IAAI,EAC5B,GAAG,GAAGA,GAAG;MAAA,GApBLA,GAAG;QAAA7G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqBF,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNxF,OAAA;MAAKyF,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAE0C,MAAM,EAAE;MAAsB,CAAE;MAAAhD,QAAA,gBAE7DpF,OAAA;QAAKyF,KAAK,EAAE;UAAE0G,IAAI,EAAE,GAAG;UAAErG,UAAU,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAX,QAAA,gBAChEpF,OAAA,CAACF,QAAQ;UACPuL,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAE;UAC1BC,eAAe,EAAEzK,aAAc;UAC/BH,SAAS,EAAEA,SAAU;UACrB0L,gBAAgB,EAAE1K,mBAAoB;UACtCD,gBAAgB,EAAEA;QAAiB;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EAED/D,gBAAgB,iBACfzB,OAAA;UAAKyF,KAAK,EAAE;YACVK,UAAU,EAAE,SAAS;YACrBC,OAAO,EAAE,MAAM;YACfkC,MAAM,EAAE,QAAQ;YAChBjC,YAAY,EAAE,KAAK;YACnBqG,SAAS,EAAE,OAAO;YAClBC,SAAS,EAAE;UACb,CAAE;UAAAlH,QAAA,gBACApF,OAAA;YAAAoF,QAAA,EAAK3D,gBAAgB,CAAC6B;UAAI;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChCxF,OAAA;YAAAoF,QAAA,gBAAGpF,OAAA;cAAAoF,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC/D,gBAAgB,CAACmF,KAAK;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvDxF,OAAA;YAAAoF,QAAA,gBAAGpF,OAAA;cAAAoF,QAAA,EAAQ;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC/D,gBAAgB,CAACyF,WAAW,CAAChB,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9ExF,OAAA;YAAAoF,QAAA,gBAAGpF,OAAA;cAAAoF,QAAA,EAAQ;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC/D,gBAAgB,CAAC0F,iBAAiB,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACC,IAAI,EAAE,CAAC,CAAC,CAACjB,cAAc,CAAC,CAAC;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjIxF,OAAA;YAAAoF,QAAA,gBAAGpF,OAAA;cAAAoF,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC/D,gBAAgB,CAAC8K,OAAO;UAAA;YAAAlH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3DxF,OAAA;YAAAoF,QAAA,gBAAGpF,OAAA;cAAAoF,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC/D,gBAAgB,CAAC0G,MAAM,CAACjC,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNxF,OAAA;QAAKyF,KAAK,EAAE;UAAE0G,IAAI,EAAE,GAAG;UAAErG,UAAU,EAAE,SAAS;UAAEC,OAAO,EAAE,MAAM;UAAEuG,SAAS,EAAE;QAAO,CAAE;QAAAlH,QAAA,EAClFD,gBAAgB,CAAC;MAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxF,OAAA;MAAKyF,KAAK,EAAE;QACVK,UAAU,EAAE,SAAS;QACrBC,OAAO,EAAE,WAAW;QACpByG,SAAS,EAAE,mBAAmB;QAC9B9G,OAAO,EAAE,MAAM;QACfqD,cAAc,EAAE,eAAe;QAC/BoC,UAAU,EAAE;MACd,CAAE;MAAA/F,QAAA,gBACApF,OAAA;QAAAoF,QAAA,EACGnE,OAAO,iBACNjB,OAAA;UAAMyF,KAAK,EAAE;YAAEiC,KAAK,EAAE,SAAS;YAAEQ,QAAQ,EAAE;UAAO,CAAE;UAAA9C,QAAA,EAAEnE;QAAO;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MACrE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENxF,OAAA;QACEiH,OAAO,EAAE5C,WAAY;QACrB+E,QAAQ,EAAEvH,WAAY;QACtB4D,KAAK,EAAE;UACLK,UAAU,EAAEjE,WAAW,GAAG,MAAM,GAAG,SAAS;UAC5C6F,KAAK,EAAE,MAAM;UACbV,MAAM,EAAE,MAAM;UACdjB,OAAO,EAAE,WAAW;UACpBmC,QAAQ,EAAE,QAAQ;UAClBlC,YAAY,EAAE,KAAK;UACnBe,MAAM,EAAElF,WAAW,GAAG,aAAa,GAAG,SAAS;UAC/CqJ,UAAU,EAAE;QACd,CAAE;QAAA9F,QAAA,EAEDvD,WAAW,GAAG,iBAAiB,GAAG;MAAa;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC/E,EAAA,CAriCuBD,GAAG;AAAAiM,EAAA,GAAHjM,GAAG;AAAA,IAAAiM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}