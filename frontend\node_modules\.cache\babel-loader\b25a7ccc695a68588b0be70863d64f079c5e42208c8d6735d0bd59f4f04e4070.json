{"ast": null, "code": "import { curves } from \"../const.mjs\";\nclass QuadraticUtils {\n  /**\n   * Calculate length of quadratic curve\n   * @see {@link http://www.malczak.linuxpl.com/blog/quadratic-bezier-curve-length/}\n   * for the detailed explanation of math behind this.\n   * @private\n   * @param fromX - x-coordinate of curve start point\n   * @param fromY - y-coordinate of curve start point\n   * @param cpX - x-coordinate of curve control point\n   * @param cpY - y-coordinate of curve control point\n   * @param toX - x-coordinate of curve end point\n   * @param toY - y-coordinate of curve end point\n   * @returns - Length of quadratic curve\n   */\n  static curveLength(fromX, fromY, cpX, cpY, toX, toY) {\n    const ax = fromX - 2 * cpX + toX,\n      ay = fromY - 2 * cpY + toY,\n      bx = 2 * cpX - 2 * fromX,\n      by = 2 * cpY - 2 * fromY,\n      a = 4 * (ax * ax + ay * ay),\n      b = 4 * (ax * bx + ay * by),\n      c = bx * bx + by * by,\n      s = 2 * Math.sqrt(a + b + c),\n      a2 = Math.sqrt(a),\n      a32 = 2 * a * a2,\n      c2 = 2 * Math.sqrt(c),\n      ba = b / a2;\n    return (a32 * s + a2 * b * (s - c2) + (4 * c * a - b * b) * Math.log((2 * a2 + ba + s) / (ba + c2))) / (4 * a32);\n  }\n  /**\n   * Calculate the points for a quadratic bezier curve and then draws it.\n   * Based on: https://stackoverflow.com/questions/785097/how-do-i-implement-a-bezier-curve-in-c\n   * @private\n   * @param cpX - Control point x\n   * @param cpY - Control point y\n   * @param toX - Destination point x\n   * @param toY - Destination point y\n   * @param points - Points to add segments to.\n   */\n  static curveTo(cpX, cpY, toX, toY, points) {\n    const fromX = points[points.length - 2],\n      fromY = points[points.length - 1],\n      n = curves._segmentsCount(QuadraticUtils.curveLength(fromX, fromY, cpX, cpY, toX, toY));\n    let xa = 0,\n      ya = 0;\n    for (let i = 1; i <= n; ++i) {\n      const j = i / n;\n      xa = fromX + (cpX - fromX) * j, ya = fromY + (cpY - fromY) * j, points.push(xa + (cpX + (toX - cpX) * j - xa) * j, ya + (cpY + (toY - cpY) * j - ya) * j);\n    }\n  }\n}\nexport { QuadraticUtils };", "map": {"version": 3, "names": ["QuadraticUtils", "curveLength", "fromX", "fromY", "cpX", "cpY", "toX", "toY", "ax", "ay", "bx", "by", "a", "b", "c", "s", "Math", "sqrt", "a2", "a32", "c2", "ba", "log", "curveTo", "points", "length", "n", "curves", "_segmentsCount", "xa", "ya", "i", "j", "push"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\graphics\\src\\utils\\QuadraticUtils.ts"], "sourcesContent": ["import { curves } from '../const';\n\n/**\n * Utilities for quadratic curves.\n * @private\n */\nexport class QuadraticUtils\n{\n    /**\n     * Calculate length of quadratic curve\n     * @see {@link http://www.malczak.linuxpl.com/blog/quadratic-bezier-curve-length/}\n     * for the detailed explanation of math behind this.\n     * @private\n     * @param fromX - x-coordinate of curve start point\n     * @param fromY - y-coordinate of curve start point\n     * @param cpX - x-coordinate of curve control point\n     * @param cpY - y-coordinate of curve control point\n     * @param toX - x-coordinate of curve end point\n     * @param toY - y-coordinate of curve end point\n     * @returns - Length of quadratic curve\n     */\n    static curveLength(\n        fromX: number, fromY: number,\n        cpX: number, cpY: number,\n        toX: number, toY: number): number\n    {\n        const ax = fromX - (2.0 * cpX) + toX;\n        const ay = fromY - (2.0 * cpY) + toY;\n        const bx = (2.0 * cpX) - (2.0 * fromX);\n        const by = (2.0 * cpY) - (2.0 * fromY);\n        const a = 4.0 * ((ax * ax) + (ay * ay));\n        const b = 4.0 * ((ax * bx) + (ay * by));\n        const c = (bx * bx) + (by * by);\n\n        const s = 2.0 * Math.sqrt(a + b + c);\n        const a2 = Math.sqrt(a);\n        const a32 = 2.0 * a * a2;\n        const c2 = 2.0 * Math.sqrt(c);\n        const ba = b / a2;\n\n        return (\n            (a32 * s)\n                + (a2 * b * (s - c2))\n                + (\n                    ((4.0 * c * a) - (b * b))\n                   * Math.log(((2.0 * a2) + ba + s) / (ba + c2))\n                )\n        ) / (4.0 * a32);\n    }\n\n    /**\n     * Calculate the points for a quadratic bezier curve and then draws it.\n     * Based on: https://stackoverflow.com/questions/785097/how-do-i-implement-a-bezier-curve-in-c\n     * @private\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @param points - Points to add segments to.\n     */\n    static curveTo(cpX: number, cpY: number, toX: number, toY: number, points: Array<number>): void\n    {\n        const fromX = points[points.length - 2];\n        const fromY = points[points.length - 1];\n\n        const n = curves._segmentsCount(\n            QuadraticUtils.curveLength(fromX, fromY, cpX, cpY, toX, toY)\n        );\n\n        let xa = 0;\n        let ya = 0;\n\n        for (let i = 1; i <= n; ++i)\n        {\n            const j = i / n;\n\n            xa = fromX + ((cpX - fromX) * j);\n            ya = fromY + ((cpY - fromY) * j);\n\n            points.push(xa + (((cpX + ((toX - cpX) * j)) - xa) * j),\n                ya + (((cpY + ((toY - cpY) * j)) - ya) * j));\n        }\n    }\n}\n"], "mappings": ";AAMO,MAAMA,cAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAcI,OAAOC,YACHC,KAAA,EAAeC,KAAA,EACfC,GAAA,EAAaC,GAAA,EACbC,GAAA,EAAaC,GAAA,EACjB;IACI,MAAMC,EAAA,GAAKN,KAAA,GAAS,IAAME,GAAA,GAAOE,GAAA;MAC3BG,EAAA,GAAKN,KAAA,GAAS,IAAME,GAAA,GAAOE,GAAA;MAC3BG,EAAA,GAAM,IAAMN,GAAA,GAAQ,IAAMF,KAAA;MAC1BS,EAAA,GAAM,IAAMN,GAAA,GAAQ,IAAMF,KAAA;MAC1BS,CAAA,GAAI,KAAQJ,EAAA,GAAKA,EAAA,GAAOC,EAAA,GAAKA,EAAA;MAC7BI,CAAA,GAAI,KAAQL,EAAA,GAAKE,EAAA,GAAOD,EAAA,GAAKE,EAAA;MAC7BG,CAAA,GAAKJ,EAAA,GAAKA,EAAA,GAAOC,EAAA,GAAKA,EAAA;MAEtBI,CAAA,GAAI,IAAMC,IAAA,CAAKC,IAAA,CAAKL,CAAA,GAAIC,CAAA,GAAIC,CAAC;MAC7BI,EAAA,GAAKF,IAAA,CAAKC,IAAA,CAAKL,CAAC;MAChBO,GAAA,GAAM,IAAMP,CAAA,GAAIM,EAAA;MAChBE,EAAA,GAAK,IAAMJ,IAAA,CAAKC,IAAA,CAAKH,CAAC;MACtBO,EAAA,GAAKR,CAAA,GAAIK,EAAA;IAGV,QAAAC,GAAA,GAAMJ,CAAA,GACAG,EAAA,GAAKL,CAAA,IAAKE,CAAA,GAAIK,EAAA,KAEX,IAAMN,CAAA,GAAIF,CAAA,GAAMC,CAAA,GAAIA,CAAA,IACrBG,IAAA,CAAKM,GAAA,EAAM,IAAMJ,EAAA,GAAMG,EAAA,GAAKN,CAAA,KAAMM,EAAA,GAAKD,EAAA,CAAG,MAElD,IAAMD,GAAA;EACf;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYA,OAAOI,QAAQnB,GAAA,EAAaC,GAAA,EAAaC,GAAA,EAAaC,GAAA,EAAaiB,MAAA,EACnE;IACI,MAAMtB,KAAA,GAAQsB,MAAA,CAAOA,MAAA,CAAOC,MAAA,GAAS,CAAC;MAChCtB,KAAA,GAAQqB,MAAA,CAAOA,MAAA,CAAOC,MAAA,GAAS,CAAC;MAEhCC,CAAA,GAAIC,MAAA,CAAOC,cAAA,CACb5B,cAAA,CAAeC,WAAA,CAAYC,KAAA,EAAOC,KAAA,EAAOC,GAAA,EAAKC,GAAA,EAAKC,GAAA,EAAKC,GAAG;IAG3D,IAAAsB,EAAA,GAAK;MACLC,EAAA,GAAK;IAET,SAASC,CAAA,GAAI,GAAGA,CAAA,IAAKL,CAAA,EAAG,EAAEK,CAAA,EAC1B;MACI,MAAMC,CAAA,GAAID,CAAA,GAAIL,CAAA;MAETG,EAAA,GAAA3B,KAAA,IAAUE,GAAA,GAAMF,KAAA,IAAS8B,CAAA,EAC9BF,EAAA,GAAK3B,KAAA,IAAUE,GAAA,GAAMF,KAAA,IAAS6B,CAAA,EAE9BR,MAAA,CAAOS,IAAA,CAAKJ,EAAA,IAAQzB,GAAA,IAAQE,GAAA,GAAMF,GAAA,IAAO4B,CAAA,GAAMH,EAAA,IAAMG,CAAA,EACjDF,EAAA,IAAQzB,GAAA,IAAQE,GAAA,GAAMF,GAAA,IAAO2B,CAAA,GAAMF,EAAA,IAAME,CAAA;IACjD;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}