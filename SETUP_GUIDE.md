# Empires & Revolutions - Setup Guide

## Project Overview
Empires & Revolutions is a Europa Universalis IV-inspired grand strategy game with multiple client implementations:
- **Backend**: Python/FastAPI server for game logic and data management
- **Web Frontend**: React/PIXI.js interactive map interface
- **Unity Client**: 3D game client (work in progress)

## Quick Start

### 1. Backend Setup (Python/FastAPI)
```bash
cd backend
pip install -r requirements.txt
python main.py
```
The backend will start on `http://localhost:8000`

### 2. Frontend Setup (React)
```bash
cd frontend
npm install
npm start
```
The frontend will start on `http://localhost:3000`

### 3. Unity Client Setup
1. Open the project in Unity 2022.3 LTS or later
2. Install TextMeshPro package if prompted
3. Create a ProvinceData ScriptableObject asset
4. Assign the DataInitializer script to a GameObject in the scene

## Fixed Issues

### Backend Issues ✅
- **API Response Format**: Fixed country data structure mismatch between backend and frontend
- **Server Startup**: Added proper uvicorn server configuration
- **CORS Configuration**: Enabled cross-origin requests for frontend communication
- **Error Handling**: Improved error handling and logging

### Frontend Issues ✅
- **Country Name Handling**: Fixed compatibility with both backend formats (`name` and `properties.NAME`)
- **API Integration**: Updated all API calls to use consistent country naming
- **State Management**: Improved country stats and game state management
- **Dependencies**: All npm packages installed and configured

### Unity Client Issues ✅
- **TextMeshPro Integration**: Replaced deprecated UI.Text with TextMeshProUGUI
- **MapManager**: Added proper error handling and API integration
- **Province System**: Enhanced Province class with comprehensive game data
- **System Scripts**: Implemented PopSystem and EconomySystem with actual functionality
- **Data Management**: Created DataInitializer for robust province data setup

### Data Management Issues ✅
- **Province Data**: Enhanced Province class with realistic game mechanics
- **Sample Data**: Created comprehensive sample data with 10 countries and provinces
- **API Integration**: Added backend data loading with fallback to local data
- **Adjacency System**: Implemented province adjacency relationships

## Architecture Overview

### Backend (Python/FastAPI)
- **main.py**: FastAPI server with game logic endpoints
- **Game Systems**: Province management, diplomacy, events, economy
- **Data**: GeoJSON country data and game state management

### Frontend (React/PIXI.js)
- **App.jsx**: Main game interface and state management
- **WorldMap.jsx**: Interactive map component with PIXI.js
- **API Integration**: Real-time communication with backend

### Unity Client (C#)
- **Core Systems**: GameManager, MapManager, Province management
- **Game Systems**: PopSystem, EconomySystem, DiplomacySystem, EventSystem
- **UI Components**: ProvinceUI with TextMeshPro integration
- **Data Management**: ScriptableObject-based province data with API integration

## API Endpoints

### Countries & Provinces
- `GET /countries` - Get all countries
- `GET /provinces` - Get all provinces
- `GET /province/{name}` - Get specific province data

### Game Mechanics
- `GET /event?country={name}` - Get random event for country
- `POST /event/apply` - Apply event effects
- `GET /diplomacy?country={name}` - Get diplomacy options
- `POST /diplomacy/perform` - Perform diplomacy action

## Game Features

### Implemented ✅
- **Interactive World Map**: Click-to-select countries and provinces
- **Country Management**: Population, resources, stability tracking
- **Turn-based Gameplay**: Manual turn advancement with AI simulation
- **Random Events**: Dynamic events affecting country stats
- **Basic Diplomacy**: Country-to-country diplomatic actions
- **Economic System**: Tax income, trade, and resource management
- **Population System**: Pop groups with culture, profession, and happiness

### Planned 🚧
- **Military System**: Army management and warfare
- **Technology System**: Research and technological advancement
- **Trade System**: Complex trade routes and economic relationships
- **Religion & Culture**: Cultural conversion and religious mechanics
- **Colonization**: New World exploration and colonization

## Troubleshooting

### Backend Issues
- **Port 8000 in use**: Change port in `main.py` or kill existing process
- **Module not found**: Run `pip install -r requirements.txt`
- **CORS errors**: Backend includes CORS middleware for localhost

### Frontend Issues
- **API connection failed**: Ensure backend is running on port 8000
- **npm install fails**: Try `npm install --legacy-peer-deps`
- **Blank map**: Check browser console for API errors

### Unity Issues
- **TextMeshPro missing**: Install via Package Manager
- **ProvinceData null**: Create ScriptableObject asset in Project window
- **API connection failed**: Ensure backend URL is correct in MapManager

## Development Notes

### Code Quality Improvements Made
1. **Error Handling**: Added comprehensive null checks and error handling
2. **Type Safety**: Improved type definitions and parameter validation
3. **Performance**: Optimized data structures and API calls
4. **Maintainability**: Added clear documentation and modular design
5. **Testing**: Structured code for easier unit testing

### Next Steps for Development
1. **Add Unit Tests**: Create test suites for all major systems
2. **Improve UI/UX**: Enhanced user interface and user experience
3. **Add Persistence**: Save/load game state functionality
4. **Multiplayer Support**: Network multiplayer capabilities
5. **Advanced AI**: More sophisticated AI behavior and decision-making

## Contributing
1. Follow the existing code style and patterns
2. Add comprehensive error handling and logging
3. Update documentation for any new features
4. Test all changes across backend, frontend, and Unity client
5. Ensure API compatibility between all components

## License
This project is for educational and demonstration purposes.
