{"ast": null, "code": "import { BrowserAdapter } from \"./adapter.mjs\";\nconst settings = {\n  /**\n   * This adapter is used to call methods that are platform dependent.\n   * For example `document.createElement` only runs on the web but fails in node environments.\n   * This allows us to support more platforms by abstracting away specific implementations per platform.\n   *\n   * By default the adapter is set to work in the browser. However you can create your own\n   * by implementing the `IAdapter` interface. See `IAdapter` for more information.\n   * @name ADAPTER\n   * @memberof PIXI.settings\n   * @type {PIXI.IAdapter}\n   * @default PIXI.BrowserAdapter\n   */\n  ADAPTER: BrowserAdapter,\n  /**\n   * Default resolution / device pixel ratio of the renderer.\n   * @static\n   * @name RESOLUTION\n   * @memberof PIXI.settings\n   * @type {number}\n   * @default 1\n   */\n  RESOLUTION: 1,\n  /**\n   * Enables bitmap creation before image load. This feature is experimental.\n   * @static\n   * @name CREATE_IMAGE_BITMAP\n   * @memberof PIXI.settings\n   * @type {boolean}\n   * @default false\n   */\n  CREATE_IMAGE_BITMAP: !1,\n  /**\n   * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.\n   * Advantages can include sharper image quality (like text) and faster rendering on canvas.\n   * The main disadvantage is movement of objects may appear less smooth.\n   * @static\n   * @memberof PIXI.settings\n   * @type {boolean}\n   * @default false\n   */\n  ROUND_PIXELS: !1\n};\nexport { settings };", "map": {"version": 3, "names": ["settings", "ADAPTER", "BrowserAdapter", "RESOLUTION", "CREATE_IMAGE_BITMAP", "ROUND_PIXELS"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\settings\\src\\settings.ts"], "sourcesContent": ["import { <PERSON>rowserAdapter } from './adapter';\n\nimport type { IAdapter } from './adapter';\n\ninterface ISettings\n{\n    ADAPTER: IAdapter;\n    RESOLUTION: number;\n    CREATE_IMAGE_BITMAP: boolean;\n    ROUND_PIXELS: boolean;\n}\n\n/**\n * User's customizable globals for overriding the default PIXI settings, such\n * as a renderer's default resolution, framerate, float precision, etc.\n * @example\n * import { settings, ENV } from 'pixi.js';\n *\n * // Use the native window resolution as the default resolution\n * // will support high-density displays when rendering\n * settings.RESOLUTION = window.devicePixelRatio;\n *\n * // Used for older v1 WebGL devices for backwards compatibility\n * settings.PREFER_ENV = ENV.WEBGL_LEGACY;\n * @namespace PIXI.settings\n */\nexport const settings: ISettings & Partial<GlobalMixins.Settings> = {\n    /**\n     * This adapter is used to call methods that are platform dependent.\n     * For example `document.createElement` only runs on the web but fails in node environments.\n     * This allows us to support more platforms by abstracting away specific implementations per platform.\n     *\n     * By default the adapter is set to work in the browser. However you can create your own\n     * by implementing the `IAdapter` interface. See `IAdapter` for more information.\n     * @name ADAPTER\n     * @memberof PIXI.settings\n     * @type {PIXI.IAdapter}\n     * @default PIXI.BrowserAdapter\n     */\n    ADAPTER: BrowserAdapter,\n\n    /**\n     * Default resolution / device pixel ratio of the renderer.\n     * @static\n     * @name RESOLUTION\n     * @memberof PIXI.settings\n     * @type {number}\n     * @default 1\n     */\n    RESOLUTION: 1,\n\n    /**\n     * Enables bitmap creation before image load. This feature is experimental.\n     * @static\n     * @name CREATE_IMAGE_BITMAP\n     * @memberof PIXI.settings\n     * @type {boolean}\n     * @default false\n     */\n    CREATE_IMAGE_BITMAP: false,\n\n    /**\n     * If true PixiJS will Math.floor() x/y values when rendering, stopping pixel interpolation.\n     * Advantages can include sharper image quality (like text) and faster rendering on canvas.\n     * The main disadvantage is movement of objects may appear less smooth.\n     * @static\n     * @memberof PIXI.settings\n     * @type {boolean}\n     * @default false\n     */\n    ROUND_PIXELS: false,\n};\n"], "mappings": ";AA0BO,MAAMA,QAAA,GAAuD;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAahEC,OAAA,EAASC,cAAA;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUTC,UAAA,EAAY;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUZC,mBAAA,EAAqB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWrBC,YAAA,EAAc;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}