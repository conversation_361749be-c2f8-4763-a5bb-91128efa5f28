{"ast": null, "code": "import { Matrix, utils, MASK_TYPES } from \"@pixi/core\";\nimport { DisplayObject } from \"./DisplayObject.mjs\";\nconst tempMatrix = new Matrix();\nfunction sortChildren(a, b) {\n  return a.zIndex === b.zIndex ? a._lastSortedIndex - b._lastSortedIndex : a.zIndex - b.zIndex;\n}\nconst _Container = class _Container2 extends DisplayObject {\n  constructor() {\n    super(), this.children = [], this.sortableChildren = _Container2.defaultSortableChildren, this.sortDirty = !1;\n  }\n  /**\n   * Overridable method that can be used by Container subclasses whenever the children array is modified.\n   * @param _length\n   */\n  onChildrenChange(_length) {}\n  /**\n   * Adds one or more children to the container.\n   *\n   * Multiple items can be added like so: `myContainer.addChild(thingOne, thingTwo, thingThree)`\n   * @param {...PIXI.DisplayObject} children - The DisplayObject(s) to add to the container\n   * @returns {PIXI.DisplayObject} - The first child that was added.\n   */\n  addChild(...children) {\n    if (children.length > 1) for (let i = 0; i < children.length; i++) this.addChild(children[i]);else {\n      const child = children[0];\n      child.parent && child.parent.removeChild(child), child.parent = this, this.sortDirty = !0, child.transform._parentID = -1, this.children.push(child), this._boundsID++, this.onChildrenChange(this.children.length - 1), this.emit(\"childAdded\", child, this, this.children.length - 1), child.emit(\"added\", this);\n    }\n    return children[0];\n  }\n  /**\n   * Adds a child to the container at a specified index. If the index is out of bounds an error will be thrown.\n   * If the child is already in this container, it will be moved to the specified index.\n   * @param {PIXI.DisplayObject} child - The child to add.\n   * @param {number} index - The absolute index where the child will be positioned at the end of the operation.\n   * @returns {PIXI.DisplayObject} The child that was added.\n   */\n  addChildAt(child, index) {\n    if (index < 0 || index > this.children.length) throw new Error(`${child}addChildAt: The index ${index} supplied is out of bounds ${this.children.length}`);\n    return child.parent && child.parent.removeChild(child), child.parent = this, this.sortDirty = !0, child.transform._parentID = -1, this.children.splice(index, 0, child), this._boundsID++, this.onChildrenChange(index), child.emit(\"added\", this), this.emit(\"childAdded\", child, this, index), child;\n  }\n  /**\n   * Swaps the position of 2 Display Objects within this container.\n   * @param child - First display object to swap\n   * @param child2 - Second display object to swap\n   */\n  swapChildren(child, child2) {\n    if (child === child2) return;\n    const index1 = this.getChildIndex(child),\n      index2 = this.getChildIndex(child2);\n    this.children[index1] = child2, this.children[index2] = child, this.onChildrenChange(index1 < index2 ? index1 : index2);\n  }\n  /**\n   * Returns the index position of a child DisplayObject instance\n   * @param child - The DisplayObject instance to identify\n   * @returns - The index position of the child display object to identify\n   */\n  getChildIndex(child) {\n    const index = this.children.indexOf(child);\n    if (index === -1) throw new Error(\"The supplied DisplayObject must be a child of the caller\");\n    return index;\n  }\n  /**\n   * Changes the position of an existing child in the display object container\n   * @param child - The child DisplayObject instance for which you want to change the index number\n   * @param index - The resulting index number for the child display object\n   */\n  setChildIndex(child, index) {\n    if (index < 0 || index >= this.children.length) throw new Error(`The index ${index} supplied is out of bounds ${this.children.length}`);\n    const currentIndex = this.getChildIndex(child);\n    utils.removeItems(this.children, currentIndex, 1), this.children.splice(index, 0, child), this.onChildrenChange(index);\n  }\n  /**\n   * Returns the child at the specified index\n   * @param index - The index to get the child at\n   * @returns - The child at the given index, if any.\n   */\n  getChildAt(index) {\n    if (index < 0 || index >= this.children.length) throw new Error(`getChildAt: Index (${index}) does not exist.`);\n    return this.children[index];\n  }\n  /**\n   * Removes one or more children from the container.\n   * @param {...PIXI.DisplayObject} children - The DisplayObject(s) to remove\n   * @returns {PIXI.DisplayObject} The first child that was removed.\n   */\n  removeChild(...children) {\n    if (children.length > 1) for (let i = 0; i < children.length; i++) this.removeChild(children[i]);else {\n      const child = children[0],\n        index = this.children.indexOf(child);\n      if (index === -1) return null;\n      child.parent = null, child.transform._parentID = -1, utils.removeItems(this.children, index, 1), this._boundsID++, this.onChildrenChange(index), child.emit(\"removed\", this), this.emit(\"childRemoved\", child, this, index);\n    }\n    return children[0];\n  }\n  /**\n   * Removes a child from the specified index position.\n   * @param index - The index to get the child from\n   * @returns The child that was removed.\n   */\n  removeChildAt(index) {\n    const child = this.getChildAt(index);\n    return child.parent = null, child.transform._parentID = -1, utils.removeItems(this.children, index, 1), this._boundsID++, this.onChildrenChange(index), child.emit(\"removed\", this), this.emit(\"childRemoved\", child, this, index), child;\n  }\n  /**\n   * Removes all children from this container that are within the begin and end indexes.\n   * @param beginIndex - The beginning position.\n   * @param endIndex - The ending position. Default value is size of the container.\n   * @returns - List of removed children\n   */\n  removeChildren(beginIndex = 0, endIndex = this.children.length) {\n    const begin = beginIndex,\n      end = endIndex,\n      range = end - begin;\n    let removed;\n    if (range > 0 && range <= end) {\n      removed = this.children.splice(begin, range);\n      for (let i = 0; i < removed.length; ++i) removed[i].parent = null, removed[i].transform && (removed[i].transform._parentID = -1);\n      this._boundsID++, this.onChildrenChange(beginIndex);\n      for (let i = 0; i < removed.length; ++i) removed[i].emit(\"removed\", this), this.emit(\"childRemoved\", removed[i], this, i);\n      return removed;\n    } else if (range === 0 && this.children.length === 0) return [];\n    throw new RangeError(\"removeChildren: numeric values are outside the acceptable range.\");\n  }\n  /** Sorts children by zIndex. Previous order is maintained for 2 children with the same zIndex. */\n  sortChildren() {\n    let sortRequired = !1;\n    for (let i = 0, j = this.children.length; i < j; ++i) {\n      const child = this.children[i];\n      child._lastSortedIndex = i, !sortRequired && child.zIndex !== 0 && (sortRequired = !0);\n    }\n    sortRequired && this.children.length > 1 && this.children.sort(sortChildren), this.sortDirty = !1;\n  }\n  /** Updates the transform on all children of this container for rendering. */\n  updateTransform() {\n    this.sortableChildren && this.sortDirty && this.sortChildren(), this._boundsID++, this.transform.updateTransform(this.parent.transform), this.worldAlpha = this.alpha * this.parent.worldAlpha;\n    for (let i = 0, j = this.children.length; i < j; ++i) {\n      const child = this.children[i];\n      child.visible && child.updateTransform();\n    }\n  }\n  /**\n   * Recalculates the bounds of the container.\n   *\n   * This implementation will automatically fit the children's bounds into the calculation. Each child's bounds\n   * is limited to its mask's bounds or filterArea, if any is applied.\n   */\n  calculateBounds() {\n    this._bounds.clear(), this._calculateBounds();\n    for (let i = 0; i < this.children.length; i++) {\n      const child = this.children[i];\n      if (!(!child.visible || !child.renderable)) if (child.calculateBounds(), child._mask) {\n        const maskObject = child._mask.isMaskData ? child._mask.maskObject : child._mask;\n        maskObject ? (maskObject.calculateBounds(), this._bounds.addBoundsMask(child._bounds, maskObject._bounds)) : this._bounds.addBounds(child._bounds);\n      } else child.filterArea ? this._bounds.addBoundsArea(child._bounds, child.filterArea) : this._bounds.addBounds(child._bounds);\n    }\n    this._bounds.updateID = this._boundsID;\n  }\n  /**\n   * Retrieves the local bounds of the displayObject as a rectangle object.\n   *\n   * Calling `getLocalBounds` may invalidate the `_bounds` of the whole subtree below. If using it inside a render()\n   * call, it is advised to call `getBounds()` immediately after to recalculate the world bounds of the subtree.\n   * @param rect - Optional rectangle to store the result of the bounds calculation.\n   * @param skipChildrenUpdate - Setting to `true` will stop re-calculation of children transforms,\n   *  it was default behaviour of pixi 4.0-5.2 and caused many problems to users.\n   * @returns - The rectangular bounding area.\n   */\n  getLocalBounds(rect, skipChildrenUpdate = !1) {\n    const result = super.getLocalBounds(rect);\n    if (!skipChildrenUpdate) for (let i = 0, j = this.children.length; i < j; ++i) {\n      const child = this.children[i];\n      child.visible && child.updateTransform();\n    }\n    return result;\n  }\n  /**\n   * Recalculates the content bounds of this object. This should be overriden to\n   * calculate the bounds of this specific object (not including children).\n   * @protected\n   */\n  _calculateBounds() {}\n  /**\n   * Renders this object and its children with culling.\n   * @protected\n   * @param {PIXI.Renderer} renderer - The renderer\n   */\n  _renderWithCulling(renderer) {\n    const sourceFrame = renderer.renderTexture.sourceFrame;\n    if (!(sourceFrame.width > 0 && sourceFrame.height > 0)) return;\n    let bounds, transform;\n    this.cullArea ? (bounds = this.cullArea, transform = this.worldTransform) : this._render !== _Container2.prototype._render && (bounds = this.getBounds(!0));\n    const projectionTransform = renderer.projection.transform;\n    if (projectionTransform && (transform ? (transform = tempMatrix.copyFrom(transform), transform.prepend(projectionTransform)) : transform = projectionTransform), bounds && sourceFrame.intersects(bounds, transform)) this._render(renderer);else if (this.cullArea) return;\n    for (let i = 0, j = this.children.length; i < j; ++i) {\n      const child = this.children[i],\n        childCullable = child.cullable;\n      child.cullable = childCullable || !this.cullArea, child.render(renderer), child.cullable = childCullable;\n    }\n  }\n  /**\n   * Renders the object using the WebGL renderer.\n   *\n   * The [_render]{@link PIXI.Container#_render} method is be overriden for rendering the contents of the\n   * container itself. This `render` method will invoke it, and also invoke the `render` methods of all\n   * children afterward.\n   *\n   * If `renderable` or `visible` is false or if `worldAlpha` is not positive or if `cullable` is true and\n   * the bounds of this object are out of frame, this implementation will entirely skip rendering.\n   * See {@link PIXI.DisplayObject} for choosing between `renderable` or `visible`. Generally,\n   * setting alpha to zero is not recommended for purely skipping rendering.\n   *\n   * When your scene becomes large (especially when it is larger than can be viewed in a single screen), it is\n   * advised to employ **culling** to automatically skip rendering objects outside of the current screen.\n   * See [cullable]{@link PIXI.DisplayObject#cullable} and [cullArea]{@link PIXI.DisplayObject#cullArea}.\n   * Other culling methods might be better suited for a large number static objects; see\n   * [@pixi-essentials/cull]{@link https://www.npmjs.com/package/@pixi-essentials/cull} and\n   * [pixi-cull]{@link https://www.npmjs.com/package/pixi-cull}.\n   *\n   * The [renderAdvanced]{@link PIXI.Container#renderAdvanced} method is internally used when when masking or\n   * filtering is applied on a container. This does, however, break batching and can affect performance when\n   * masking and filtering is applied extensively throughout the scene graph.\n   * @param renderer - The renderer\n   */\n  render(renderer) {\n    if (!(!this.visible || this.worldAlpha <= 0 || !this.renderable)) if (this._mask || this.filters?.length) this.renderAdvanced(renderer);else if (this.cullable) this._renderWithCulling(renderer);else {\n      this._render(renderer);\n      for (let i = 0, j = this.children.length; i < j; ++i) this.children[i].render(renderer);\n    }\n  }\n  /**\n   * Render the object using the WebGL renderer and advanced features.\n   * @param renderer - The renderer\n   */\n  renderAdvanced(renderer) {\n    const filters = this.filters,\n      mask = this._mask;\n    if (filters) {\n      this._enabledFilters || (this._enabledFilters = []), this._enabledFilters.length = 0;\n      for (let i = 0; i < filters.length; i++) filters[i].enabled && this._enabledFilters.push(filters[i]);\n    }\n    const flush = filters && this._enabledFilters?.length || mask && (!mask.isMaskData || mask.enabled && (mask.autoDetect || mask.type !== MASK_TYPES.NONE));\n    if (flush && renderer.batch.flush(), filters && this._enabledFilters?.length && renderer.filter.push(this, this._enabledFilters), mask && renderer.mask.push(this, this._mask), this.cullable) this._renderWithCulling(renderer);else {\n      this._render(renderer);\n      for (let i = 0, j = this.children.length; i < j; ++i) this.children[i].render(renderer);\n    }\n    flush && renderer.batch.flush(), mask && renderer.mask.pop(this), filters && this._enabledFilters?.length && renderer.filter.pop();\n  }\n  /**\n   * To be overridden by the subclasses.\n   * @param _renderer - The renderer\n   */\n  _render(_renderer) {}\n  /**\n   * Removes all internal references and listeners as well as removes children from the display list.\n   * Do not use a Container after calling `destroy`.\n   * @param options - Options parameter. A boolean will act as if all options\n   *  have been set to that value\n   * @param {boolean} [options.children=false] - if set to true, all the children will have their destroy\n   *  method called as well. 'options' will be passed on to those calls.\n   * @param {boolean} [options.texture=false] - Only used for child Sprites if options.children is set to true\n   *  Should it destroy the texture of the child sprite\n   * @param {boolean} [options.baseTexture=false] - Only used for child Sprites if options.children is set to true\n   *  Should it destroy the base texture of the child sprite\n   */\n  destroy(options) {\n    super.destroy(), this.sortDirty = !1;\n    const destroyChildren = typeof options == \"boolean\" ? options : options?.children,\n      oldChildren = this.removeChildren(0, this.children.length);\n    if (destroyChildren) for (let i = 0; i < oldChildren.length; ++i) oldChildren[i].destroy(options);\n  }\n  /** The width of the Container, setting this will actually modify the scale to achieve the value set. */\n  get width() {\n    return this.scale.x * this.getLocalBounds().width;\n  }\n  set width(value) {\n    const width = this.getLocalBounds().width;\n    width !== 0 ? this.scale.x = value / width : this.scale.x = 1, this._width = value;\n  }\n  /** The height of the Container, setting this will actually modify the scale to achieve the value set. */\n  get height() {\n    return this.scale.y * this.getLocalBounds().height;\n  }\n  set height(value) {\n    const height = this.getLocalBounds().height;\n    height !== 0 ? this.scale.y = value / height : this.scale.y = 1, this._height = value;\n  }\n};\n_Container.defaultSortableChildren = !1;\nlet Container = _Container;\nContainer.prototype.containerUpdateTransform = Container.prototype.updateTransform;\nexport { Container };", "map": {"version": 3, "names": ["tempMatrix", "Matrix", "sort<PERSON><PERSON><PERSON><PERSON>", "a", "b", "zIndex", "_lastSortedIndex", "_Container", "_Container2", "DisplayObject", "constructor", "children", "sortable<PERSON><PERSON><PERSON>n", "defaultSortableChildren", "sortDirty", "onChildrenChange", "_length", "<PERSON><PERSON><PERSON><PERSON>", "length", "i", "child", "parent", "<PERSON><PERSON><PERSON><PERSON>", "transform", "_parentID", "push", "_boundsID", "emit", "addChildAt", "index", "Error", "splice", "swapChildren", "child2", "index1", "getChildIndex", "index2", "indexOf", "setChildIndex", "currentIndex", "utils", "removeItems", "getChildAt", "removeChildAt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beginIndex", "endIndex", "begin", "end", "range", "removed", "RangeError", "sortRequired", "j", "sort", "updateTransform", "worldAlpha", "alpha", "visible", "calculateBounds", "_bounds", "clear", "_calculateBounds", "renderable", "_mask", "maskObject", "isMaskData", "addBoundsMask", "addBounds", "filterArea", "addBoundsArea", "updateID", "getLocalBounds", "rect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "_renderWithCulling", "renderer", "sourceFrame", "renderTexture", "width", "height", "bounds", "cull<PERSON><PERSON>", "worldTransform", "_render", "prototype", "getBounds", "projectionTransform", "projection", "copyFrom", "prepend", "intersects", "childCullable", "cullable", "render", "filters", "renderAdvanced", "mask", "_enabledFilters", "enabled", "flush", "autoDetect", "type", "MASK_TYPES", "NONE", "batch", "filter", "pop", "_renderer", "destroy", "options", "destroy<PERSON><PERSON><PERSON>n", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scale", "x", "value", "_width", "y", "_height", "Container", "containerUpdateTransform"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\display\\src\\Container.ts"], "sourcesContent": ["import { MASK_TYPES, Matrix, utils } from '@pixi/core';\nimport { DisplayObject } from './DisplayObject';\n\nimport type { <PERSON>D<PERSON>, Rectangle, Renderer } from '@pixi/core';\nimport type { IDestroyOptions } from './DisplayObject';\n\nconst tempMatrix = new Matrix();\n\nfunction sortChildren(a: DisplayObject, b: DisplayObject): number\n{\n    if (a.zIndex === b.zIndex)\n    {\n        return a._lastSortedIndex - b._lastSortedIndex;\n    }\n\n    return a.zIndex - b.zIndex;\n}\n\nexport interface Container extends GlobalMixins.Container, DisplayObject {}\n\n/**\n * Container is a general-purpose display object that holds children. It also adds built-in support for advanced\n * rendering features like masking and filtering.\n *\n * It is the base class of all display objects that act as a container for other objects, including Graphics\n * and Sprite.\n * @example\n * import { BlurFilter, Container, Graphics, Sprite } from 'pixi.js';\n *\n * const container = new Container();\n * const sprite = Sprite.from('https://s3-us-west-2.amazonaws.com/s.cdpn.io/693612/IaUrttj.png');\n *\n * sprite.width = 512;\n * sprite.height = 512;\n *\n * // Adds a sprite as a child to this container. As a result, the sprite will be rendered whenever the container\n * // is rendered.\n * container.addChild(sprite);\n *\n * // Blurs whatever is rendered by the container\n * container.filters = [new BlurFilter()];\n *\n * // Only the contents within a circle at the center should be rendered onto the screen.\n * container.mask = new Graphics()\n *     .beginFill(0xffffff)\n *     .drawCircle(sprite.width / 2, sprite.height / 2, Math.min(sprite.width, sprite.height) / 2)\n *     .endFill();\n * @memberof PIXI\n */\nexport class Container<T extends DisplayObject = DisplayObject> extends DisplayObject\n{\n    /**\n     * Sets the default value for the container property `sortableChildren`.\n     * If set to true, the container will sort its children by zIndex value\n     * when `updateTransform()` is called, or manually if `sortChildren()` is called.\n     *\n     * This actually changes the order of elements in the array, so should be treated\n     * as a basic solution that is not performant compared to other solutions,\n     * such as {@link https://github.com/pixijs/layers PixiJS Layers}.\n     *\n     * Also be aware of that this may not work nicely with the `addChildAt()` function,\n     * as the `zIndex` sorting may cause the child to automatically sorted to another position.\n     * @static\n     */\n    public static defaultSortableChildren = false;\n\n    /**\n     * The array of children of this container.\n     * @readonly\n     */\n    public readonly children: T[];\n\n    /**\n     * If set to true, the container will sort its children by `zIndex` value\n     * when `updateTransform()` is called, or manually if `sortChildren()` is called.\n     *\n     * This actually changes the order of elements in the array, so should be treated\n     * as a basic solution that is not performant compared to other solutions,\n     * such as {@link https://github.com/pixijs/layers PixiJS Layers}\n     *\n     * Also be aware of that this may not work nicely with the `addChildAt()` function,\n     * as the `zIndex` sorting may cause the child to automatically sorted to another position.\n     * @see PIXI.Container.defaultSortableChildren\n     */\n    public sortableChildren: boolean;\n\n    /**\n     * Should children be sorted by zIndex at the next updateTransform call.\n     *\n     * Will get automatically set to true if a new child is added, or if a child's zIndex changes.\n     */\n    public sortDirty: boolean;\n    public parent: Container;\n    public containerUpdateTransform: () => void;\n\n    protected _width: number;\n    protected _height: number;\n\n    constructor()\n    {\n        super();\n\n        this.children = [];\n        this.sortableChildren = Container.defaultSortableChildren;\n        this.sortDirty = false;\n\n        /**\n         * Fired when a DisplayObject is added to this Container.\n         * @event PIXI.Container#childAdded\n         * @param {PIXI.DisplayObject} child - The child added to the Container.\n         * @param {PIXI.Container} container - The container that added the child.\n         * @param {number} index - The children's index of the added child.\n         */\n\n        /**\n         * Fired when a DisplayObject is removed from this Container.\n         * @event PIXI.Container#childRemoved\n         * @param {PIXI.DisplayObject} child - The child removed from the Container.\n         * @param {PIXI.Container} container - The container that removed the child.\n         * @param {number} index - The former children's index of the removed child.\n         */\n    }\n\n    /**\n     * Overridable method that can be used by Container subclasses whenever the children array is modified.\n     * @param _length\n     */\n    protected onChildrenChange(_length?: number): void\n    {\n        /* empty */\n    }\n\n    /**\n     * Adds one or more children to the container.\n     *\n     * Multiple items can be added like so: `myContainer.addChild(thingOne, thingTwo, thingThree)`\n     * @param {...PIXI.DisplayObject} children - The DisplayObject(s) to add to the container\n     * @returns {PIXI.DisplayObject} - The first child that was added.\n     */\n    addChild<U extends T[]>(...children: U): U[0]\n    {\n        // if there is only one argument we can bypass looping through the them\n        if (children.length > 1)\n        {\n            // loop through the array and add all children\n            for (let i = 0; i < children.length; i++)\n            {\n                // eslint-disable-next-line prefer-rest-params\n                this.addChild(children[i]);\n            }\n        }\n        else\n        {\n            const child = children[0];\n            // if the child has a parent then lets remove it as PixiJS objects can only exist in one place\n\n            if (child.parent)\n            {\n                child.parent.removeChild(child);\n            }\n\n            child.parent = this;\n            this.sortDirty = true;\n\n            // ensure child transform will be recalculated\n            child.transform._parentID = -1;\n\n            this.children.push(child);\n\n            // ensure bounds will be recalculated\n            this._boundsID++;\n\n            // TODO - lets either do all callbacks or all events.. not both!\n            this.onChildrenChange(this.children.length - 1);\n            this.emit('childAdded', child, this, this.children.length - 1);\n            child.emit('added', this);\n        }\n\n        return children[0];\n    }\n\n    /**\n     * Adds a child to the container at a specified index. If the index is out of bounds an error will be thrown.\n     * If the child is already in this container, it will be moved to the specified index.\n     * @param {PIXI.DisplayObject} child - The child to add.\n     * @param {number} index - The absolute index where the child will be positioned at the end of the operation.\n     * @returns {PIXI.DisplayObject} The child that was added.\n     */\n    addChildAt<U extends T>(child: U, index: number): U\n    {\n        if (index < 0 || index > this.children.length)\n        {\n            throw new Error(`${child}addChildAt: The index ${index} supplied is out of bounds ${this.children.length}`);\n        }\n\n        if (child.parent)\n        {\n            child.parent.removeChild(child);\n        }\n\n        child.parent = this;\n        this.sortDirty = true;\n\n        // ensure child transform will be recalculated\n        child.transform._parentID = -1;\n\n        this.children.splice(index, 0, child);\n\n        // ensure bounds will be recalculated\n        this._boundsID++;\n\n        // TODO - lets either do all callbacks or all events.. not both!\n        this.onChildrenChange(index);\n        child.emit('added', this);\n        this.emit('childAdded', child, this, index);\n\n        return child;\n    }\n\n    /**\n     * Swaps the position of 2 Display Objects within this container.\n     * @param child - First display object to swap\n     * @param child2 - Second display object to swap\n     */\n    swapChildren(child: T, child2: T): void\n    {\n        if (child === child2)\n        {\n            return;\n        }\n\n        const index1 = this.getChildIndex(child);\n        const index2 = this.getChildIndex(child2);\n\n        this.children[index1] = child2;\n        this.children[index2] = child;\n        this.onChildrenChange(index1 < index2 ? index1 : index2);\n    }\n\n    /**\n     * Returns the index position of a child DisplayObject instance\n     * @param child - The DisplayObject instance to identify\n     * @returns - The index position of the child display object to identify\n     */\n    getChildIndex(child: T): number\n    {\n        const index = this.children.indexOf(child);\n\n        if (index === -1)\n        {\n            throw new Error('The supplied DisplayObject must be a child of the caller');\n        }\n\n        return index;\n    }\n\n    /**\n     * Changes the position of an existing child in the display object container\n     * @param child - The child DisplayObject instance for which you want to change the index number\n     * @param index - The resulting index number for the child display object\n     */\n    setChildIndex(child: T, index: number): void\n    {\n        if (index < 0 || index >= this.children.length)\n        {\n            throw new Error(`The index ${index} supplied is out of bounds ${this.children.length}`);\n        }\n\n        const currentIndex = this.getChildIndex(child);\n\n        utils.removeItems(this.children, currentIndex, 1); // remove from old position\n        this.children.splice(index, 0, child); // add at new position\n\n        this.onChildrenChange(index);\n    }\n\n    /**\n     * Returns the child at the specified index\n     * @param index - The index to get the child at\n     * @returns - The child at the given index, if any.\n     */\n    getChildAt(index: number): T\n    {\n        if (index < 0 || index >= this.children.length)\n        {\n            throw new Error(`getChildAt: Index (${index}) does not exist.`);\n        }\n\n        return this.children[index];\n    }\n\n    /**\n     * Removes one or more children from the container.\n     * @param {...PIXI.DisplayObject} children - The DisplayObject(s) to remove\n     * @returns {PIXI.DisplayObject} The first child that was removed.\n     */\n    removeChild<U extends T[]>(...children: U): U[0]\n    {\n        // if there is only one argument we can bypass looping through the them\n        if (children.length > 1)\n        {\n            // loop through the arguments property and remove all children\n            for (let i = 0; i < children.length; i++)\n            {\n                this.removeChild(children[i]);\n            }\n        }\n        else\n        {\n            const child = children[0];\n            const index = this.children.indexOf(child);\n\n            if (index === -1) return null;\n\n            child.parent = null;\n            // ensure child transform will be recalculated\n            child.transform._parentID = -1;\n            utils.removeItems(this.children, index, 1);\n\n            // ensure bounds will be recalculated\n            this._boundsID++;\n\n            // TODO - lets either do all callbacks or all events.. not both!\n            this.onChildrenChange(index);\n            child.emit('removed', this);\n            this.emit('childRemoved', child, this, index);\n        }\n\n        return children[0];\n    }\n\n    /**\n     * Removes a child from the specified index position.\n     * @param index - The index to get the child from\n     * @returns The child that was removed.\n     */\n    removeChildAt(index: number): T\n    {\n        const child = this.getChildAt(index);\n\n        // ensure child transform will be recalculated..\n        child.parent = null;\n        child.transform._parentID = -1;\n        utils.removeItems(this.children, index, 1);\n\n        // ensure bounds will be recalculated\n        this._boundsID++;\n\n        // TODO - lets either do all callbacks or all events.. not both!\n        this.onChildrenChange(index);\n        child.emit('removed', this);\n        this.emit('childRemoved', child, this, index);\n\n        return child;\n    }\n\n    /**\n     * Removes all children from this container that are within the begin and end indexes.\n     * @param beginIndex - The beginning position.\n     * @param endIndex - The ending position. Default value is size of the container.\n     * @returns - List of removed children\n     */\n    removeChildren(beginIndex = 0, endIndex = this.children.length): T[]\n    {\n        const begin = beginIndex;\n        const end = endIndex;\n        const range = end - begin;\n        let removed;\n\n        if (range > 0 && range <= end)\n        {\n            removed = this.children.splice(begin, range);\n\n            for (let i = 0; i < removed.length; ++i)\n            {\n                removed[i].parent = null;\n                if (removed[i].transform)\n                {\n                    removed[i].transform._parentID = -1;\n                }\n            }\n\n            this._boundsID++;\n\n            this.onChildrenChange(beginIndex);\n\n            for (let i = 0; i < removed.length; ++i)\n            {\n                removed[i].emit('removed', this);\n                this.emit('childRemoved', removed[i], this, i);\n            }\n\n            return removed;\n        }\n        else if (range === 0 && this.children.length === 0)\n        {\n            return [];\n        }\n\n        throw new RangeError('removeChildren: numeric values are outside the acceptable range.');\n    }\n\n    /** Sorts children by zIndex. Previous order is maintained for 2 children with the same zIndex. */\n    sortChildren(): void\n    {\n        let sortRequired = false;\n\n        for (let i = 0, j = this.children.length; i < j; ++i)\n        {\n            const child = this.children[i];\n\n            child._lastSortedIndex = i;\n\n            if (!sortRequired && child.zIndex !== 0)\n            {\n                sortRequired = true;\n            }\n        }\n\n        if (sortRequired && this.children.length > 1)\n        {\n            this.children.sort(sortChildren);\n        }\n\n        this.sortDirty = false;\n    }\n\n    /** Updates the transform on all children of this container for rendering. */\n    updateTransform(): void\n    {\n        if (this.sortableChildren && this.sortDirty)\n        {\n            this.sortChildren();\n        }\n\n        this._boundsID++;\n\n        this.transform.updateTransform(this.parent.transform);\n\n        // TODO: check render flags, how to process stuff here\n        this.worldAlpha = this.alpha * this.parent.worldAlpha;\n\n        for (let i = 0, j = this.children.length; i < j; ++i)\n        {\n            const child = this.children[i];\n\n            if (child.visible)\n            {\n                child.updateTransform();\n            }\n        }\n    }\n\n    /**\n     * Recalculates the bounds of the container.\n     *\n     * This implementation will automatically fit the children's bounds into the calculation. Each child's bounds\n     * is limited to its mask's bounds or filterArea, if any is applied.\n     */\n    calculateBounds(): void\n    {\n        this._bounds.clear();\n\n        this._calculateBounds();\n\n        for (let i = 0; i < this.children.length; i++)\n        {\n            const child = this.children[i];\n\n            if (!child.visible || !child.renderable)\n            {\n                continue;\n            }\n\n            child.calculateBounds();\n\n            // TODO: filter+mask, need to mask both somehow\n            if (child._mask)\n            {\n                const maskObject = ((child._mask as MaskData).isMaskData\n                    ? (child._mask as MaskData).maskObject : child._mask) as Container;\n\n                if (maskObject)\n                {\n                    maskObject.calculateBounds();\n                    this._bounds.addBoundsMask(child._bounds, maskObject._bounds);\n                }\n                else\n                {\n                    this._bounds.addBounds(child._bounds);\n                }\n            }\n            else if (child.filterArea)\n            {\n                this._bounds.addBoundsArea(child._bounds, child.filterArea);\n            }\n            else\n            {\n                this._bounds.addBounds(child._bounds);\n            }\n        }\n\n        this._bounds.updateID = this._boundsID;\n    }\n\n    /**\n     * Retrieves the local bounds of the displayObject as a rectangle object.\n     *\n     * Calling `getLocalBounds` may invalidate the `_bounds` of the whole subtree below. If using it inside a render()\n     * call, it is advised to call `getBounds()` immediately after to recalculate the world bounds of the subtree.\n     * @param rect - Optional rectangle to store the result of the bounds calculation.\n     * @param skipChildrenUpdate - Setting to `true` will stop re-calculation of children transforms,\n     *  it was default behaviour of pixi 4.0-5.2 and caused many problems to users.\n     * @returns - The rectangular bounding area.\n     */\n    public getLocalBounds(rect?: Rectangle, skipChildrenUpdate = false): Rectangle\n    {\n        const result = super.getLocalBounds(rect);\n\n        if (!skipChildrenUpdate)\n        {\n            for (let i = 0, j = this.children.length; i < j; ++i)\n            {\n                const child = this.children[i];\n\n                if (child.visible)\n                {\n                    child.updateTransform();\n                }\n            }\n        }\n\n        return result;\n    }\n\n    /**\n     * Recalculates the content bounds of this object. This should be overriden to\n     * calculate the bounds of this specific object (not including children).\n     * @protected\n     */\n    protected _calculateBounds(): void\n    {\n        // FILL IN//\n    }\n\n    /**\n     * Renders this object and its children with culling.\n     * @protected\n     * @param {PIXI.Renderer} renderer - The renderer\n     */\n    protected _renderWithCulling(renderer: Renderer): void\n    {\n        const sourceFrame = renderer.renderTexture.sourceFrame;\n\n        // If the source frame is empty, stop rendering.\n        if (!(sourceFrame.width > 0 && sourceFrame.height > 0))\n        {\n            return;\n        }\n\n        // Render the content of the container only if its bounds intersect with the source frame.\n        // All filters are on the stack at this point, and the filter source frame is bound:\n        // therefore, even if the bounds to non intersect the filter frame, the filter\n        // is still applied and any filter padding that is in the frame is rendered correctly.\n\n        let bounds: Rectangle;\n        let transform: Matrix;\n\n        // If cullArea is set, we use this rectangle instead of the bounds of the object. The cullArea\n        // rectangle must completely contain the container and its children including filter padding.\n        if (this.cullArea)\n        {\n            bounds = this.cullArea;\n            transform = this.worldTransform;\n        }\n        // If the container doesn't override _render, we can skip the bounds calculation and intersection test.\n        else if (this._render !== Container.prototype._render)\n        {\n            bounds = this.getBounds(true);\n        }\n\n        // Prepend the transform that is appended to the projection matrix.\n        const projectionTransform = renderer.projection.transform;\n\n        if (projectionTransform)\n        {\n            if (transform)\n            {\n                transform = tempMatrix.copyFrom(transform);\n                transform.prepend(projectionTransform);\n            }\n            else\n            {\n                transform = projectionTransform;\n            }\n        }\n\n        // Render the container if the source frame intersects the bounds.\n        if (bounds && sourceFrame.intersects(bounds, transform))\n        {\n            this._render(renderer);\n        }\n        // If the bounds are defined by cullArea and do not intersect with the source frame, stop rendering.\n        else if (this.cullArea)\n        {\n            return;\n        }\n\n        // Unless cullArea is set, we cannot skip the children if the bounds of the container do not intersect\n        // the source frame, because the children might have filters with nonzero padding, which may intersect\n        // with the source frame while the bounds do not: filter padding is not included in the bounds.\n\n        // If cullArea is not set, render the children with culling temporarily enabled so that they are not rendered\n        // if they are out of frame; otherwise, render the children normally.\n        for (let i = 0, j = this.children.length; i < j; ++i)\n        {\n            const child = this.children[i];\n            const childCullable = child.cullable;\n\n            child.cullable = childCullable || !this.cullArea;\n            child.render(renderer);\n            child.cullable = childCullable;\n        }\n    }\n\n    /**\n     * Renders the object using the WebGL renderer.\n     *\n     * The [_render]{@link PIXI.Container#_render} method is be overriden for rendering the contents of the\n     * container itself. This `render` method will invoke it, and also invoke the `render` methods of all\n     * children afterward.\n     *\n     * If `renderable` or `visible` is false or if `worldAlpha` is not positive or if `cullable` is true and\n     * the bounds of this object are out of frame, this implementation will entirely skip rendering.\n     * See {@link PIXI.DisplayObject} for choosing between `renderable` or `visible`. Generally,\n     * setting alpha to zero is not recommended for purely skipping rendering.\n     *\n     * When your scene becomes large (especially when it is larger than can be viewed in a single screen), it is\n     * advised to employ **culling** to automatically skip rendering objects outside of the current screen.\n     * See [cullable]{@link PIXI.DisplayObject#cullable} and [cullArea]{@link PIXI.DisplayObject#cullArea}.\n     * Other culling methods might be better suited for a large number static objects; see\n     * [@pixi-essentials/cull]{@link https://www.npmjs.com/package/@pixi-essentials/cull} and\n     * [pixi-cull]{@link https://www.npmjs.com/package/pixi-cull}.\n     *\n     * The [renderAdvanced]{@link PIXI.Container#renderAdvanced} method is internally used when when masking or\n     * filtering is applied on a container. This does, however, break batching and can affect performance when\n     * masking and filtering is applied extensively throughout the scene graph.\n     * @param renderer - The renderer\n     */\n    render(renderer: Renderer): void\n    {\n        // if the object is not visible or the alpha is 0 then no need to render this element\n        if (!this.visible || this.worldAlpha <= 0 || !this.renderable)\n        {\n            return;\n        }\n\n        // do a quick check to see if this element has a mask or a filter.\n        if (this._mask || this.filters?.length)\n        {\n            this.renderAdvanced(renderer);\n        }\n        else if (this.cullable)\n        {\n            this._renderWithCulling(renderer);\n        }\n        else\n        {\n            this._render(renderer);\n\n            for (let i = 0, j = this.children.length; i < j; ++i)\n            {\n                this.children[i].render(renderer);\n            }\n        }\n    }\n\n    /**\n     * Render the object using the WebGL renderer and advanced features.\n     * @param renderer - The renderer\n     */\n    protected renderAdvanced(renderer: Renderer): void\n    {\n        const filters = this.filters;\n        const mask = this._mask as MaskData;\n\n        // push filter first as we need to ensure the stencil buffer is correct for any masking\n        if (filters)\n        {\n            if (!this._enabledFilters)\n            {\n                this._enabledFilters = [];\n            }\n\n            this._enabledFilters.length = 0;\n\n            for (let i = 0; i < filters.length; i++)\n            {\n                if (filters[i].enabled)\n                {\n                    this._enabledFilters.push(filters[i]);\n                }\n            }\n        }\n\n        const flush = (filters && this._enabledFilters?.length)\n            || (mask && (!mask.isMaskData\n                || (mask.enabled && (mask.autoDetect || mask.type !== MASK_TYPES.NONE))));\n\n        if (flush)\n        {\n            renderer.batch.flush();\n        }\n\n        if (filters && this._enabledFilters?.length)\n        {\n            renderer.filter.push(this, this._enabledFilters);\n        }\n\n        if (mask)\n        {\n            renderer.mask.push(this, this._mask);\n        }\n\n        if (this.cullable)\n        {\n            this._renderWithCulling(renderer);\n        }\n        else\n        {\n            this._render(renderer);\n\n            for (let i = 0, j = this.children.length; i < j; ++i)\n            {\n                this.children[i].render(renderer);\n            }\n        }\n\n        if (flush)\n        {\n            renderer.batch.flush();\n        }\n\n        if (mask)\n        {\n            renderer.mask.pop(this);\n        }\n\n        if (filters && this._enabledFilters?.length)\n        {\n            renderer.filter.pop();\n        }\n    }\n\n    /**\n     * To be overridden by the subclasses.\n     * @param _renderer - The renderer\n     */\n    protected _render(_renderer: Renderer): void // eslint-disable-line no-unused-vars\n    {\n        // this is where content itself gets rendered...\n    }\n\n    /**\n     * Removes all internal references and listeners as well as removes children from the display list.\n     * Do not use a Container after calling `destroy`.\n     * @param options - Options parameter. A boolean will act as if all options\n     *  have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have their destroy\n     *  method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the texture of the child sprite\n     * @param {boolean} [options.baseTexture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the base texture of the child sprite\n     */\n    destroy(options?: IDestroyOptions | boolean): void\n    {\n        super.destroy();\n\n        this.sortDirty = false;\n\n        const destroyChildren = typeof options === 'boolean' ? options : options?.children;\n\n        const oldChildren = this.removeChildren(0, this.children.length);\n\n        if (destroyChildren)\n        {\n            for (let i = 0; i < oldChildren.length; ++i)\n            {\n                oldChildren[i].destroy(options);\n            }\n        }\n    }\n\n    /** The width of the Container, setting this will actually modify the scale to achieve the value set. */\n    get width(): number\n    {\n        return this.scale.x * this.getLocalBounds().width;\n    }\n\n    set width(value: number)\n    {\n        const width = this.getLocalBounds().width;\n\n        if (width !== 0)\n        {\n            this.scale.x = value / width;\n        }\n        else\n        {\n            this.scale.x = 1;\n        }\n\n        this._width = value;\n    }\n\n    /** The height of the Container, setting this will actually modify the scale to achieve the value set. */\n    get height(): number\n    {\n        return this.scale.y * this.getLocalBounds().height;\n    }\n\n    set height(value: number)\n    {\n        const height = this.getLocalBounds().height;\n\n        if (height !== 0)\n        {\n            this.scale.y = value / height;\n        }\n        else\n        {\n            this.scale.y = 1;\n        }\n\n        this._height = value;\n    }\n}\n\n/**\n * Container default updateTransform, does update children of container.\n * Will crash if there's no parent element.\n * @memberof PIXI.Container#\n * @method containerUpdateTransform\n */\nContainer.prototype.containerUpdateTransform = Container.prototype.updateTransform;\n"], "mappings": ";;AAMA,MAAMA,UAAA,GAAa,IAAIC,MAAA;AAEvB,SAASC,aAAaC,CAAA,EAAkBC,CAAA,EACxC;EACQ,OAAAD,CAAA,CAAEE,MAAA,KAAWD,CAAA,CAAEC,MAAA,GAERF,CAAA,CAAEG,gBAAA,GAAmBF,CAAA,CAAEE,gBAAA,GAG3BH,CAAA,CAAEE,MAAA,GAASD,CAAA,CAAEC,MAAA;AACxB;AAiCO,MAAME,UAAA,GAAN,MAAMC,WAAA,SAA2DC,aAAA,CACxE;EAgDIC,YAAA,EACA;IACU,SAED,KAAAC,QAAA,GAAW,IAChB,KAAKC,gBAAA,GAAmBJ,WAAA,CAAUK,uBAAA,EAClC,KAAKC,SAAA,GAAY;EAiBrB;EAAA;AAAA;AAAA;AAAA;EAMUC,iBAAiBC,OAAA,EAC3B,CAEA;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAC,SAAA,GAA2BN,QAAA,EAC3B;IAEI,IAAIA,QAAA,CAASO,MAAA,GAAS,GAGlB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIR,QAAA,CAASO,MAAA,EAAQC,CAAA,IAG5B,KAAAF,QAAA,CAASN,QAAA,CAASQ,CAAC,CAAC,OAIjC;MACU,MAAAC,KAAA,GAAQT,QAAA,CAAS,CAAC;MAGpBS,KAAA,CAAMC,MAAA,IAEND,KAAA,CAAMC,MAAA,CAAOC,WAAA,CAAYF,KAAK,GAGlCA,KAAA,CAAMC,MAAA,GAAS,MACf,KAAKP,SAAA,GAAY,IAGjBM,KAAA,CAAMG,SAAA,CAAUC,SAAA,GAAY,IAE5B,KAAKb,QAAA,CAASc,IAAA,CAAKL,KAAK,GAGxB,KAAKM,SAAA,IAGL,KAAKX,gBAAA,CAAiB,KAAKJ,QAAA,CAASO,MAAA,GAAS,CAAC,GAC9C,KAAKS,IAAA,CAAK,cAAcP,KAAA,EAAO,MAAM,KAAKT,QAAA,CAASO,MAAA,GAAS,CAAC,GAC7DE,KAAA,CAAMO,IAAA,CAAK,SAAS,IAAI;IAC5B;IAEA,OAAOhB,QAAA,CAAS,CAAC;EACrB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAiB,WAAwBR,KAAA,EAAUS,KAAA,EAClC;IACI,IAAIA,KAAA,GAAQ,KAAKA,KAAA,GAAQ,KAAKlB,QAAA,CAASO,MAAA,EAE7B,UAAIY,KAAA,CAAM,GAAGV,KAAK,yBAAyBS,KAAK,8BAA8B,KAAKlB,QAAA,CAASO,MAAM,EAAE;IAG9G,OAAIE,KAAA,CAAMC,MAAA,IAEND,KAAA,CAAMC,MAAA,CAAOC,WAAA,CAAYF,KAAK,GAGlCA,KAAA,CAAMC,MAAA,GAAS,MACf,KAAKP,SAAA,GAAY,IAGjBM,KAAA,CAAMG,SAAA,CAAUC,SAAA,GAAY,IAE5B,KAAKb,QAAA,CAASoB,MAAA,CAAOF,KAAA,EAAO,GAAGT,KAAK,GAGpC,KAAKM,SAAA,IAGL,KAAKX,gBAAA,CAAiBc,KAAK,GAC3BT,KAAA,CAAMO,IAAA,CAAK,SAAS,IAAI,GACxB,KAAKA,IAAA,CAAK,cAAcP,KAAA,EAAO,MAAMS,KAAK,GAEnCT,KAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAY,aAAaZ,KAAA,EAAUa,MAAA,EACvB;IACI,IAAIb,KAAA,KAAUa,MAAA,EAEV;IAGE,MAAAC,MAAA,GAAS,KAAKC,aAAA,CAAcf,KAAK;MACjCgB,MAAA,GAAS,KAAKD,aAAA,CAAcF,MAAM;IAExC,KAAKtB,QAAA,CAASuB,MAAM,IAAID,MAAA,EACxB,KAAKtB,QAAA,CAASyB,MAAM,IAAIhB,KAAA,EACxB,KAAKL,gBAAA,CAAiBmB,MAAA,GAASE,MAAA,GAASF,MAAA,GAASE,MAAM;EAC3D;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAD,cAAcf,KAAA,EACd;IACI,MAAMS,KAAA,GAAQ,KAAKlB,QAAA,CAAS0B,OAAA,CAAQjB,KAAK;IAEzC,IAAIS,KAAA,KAAU,IAEJ,UAAIC,KAAA,CAAM,0DAA0D;IAGvE,OAAAD,KAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAS,cAAclB,KAAA,EAAUS,KAAA,EACxB;IACI,IAAIA,KAAA,GAAQ,KAAKA,KAAA,IAAS,KAAKlB,QAAA,CAASO,MAAA,EAE9B,UAAIY,KAAA,CAAM,aAAaD,KAAK,8BAA8B,KAAKlB,QAAA,CAASO,MAAM,EAAE;IAGpF,MAAAqB,YAAA,GAAe,KAAKJ,aAAA,CAAcf,KAAK;IAE7CoB,KAAA,CAAMC,WAAA,CAAY,KAAK9B,QAAA,EAAU4B,YAAA,EAAc,CAAC,GAChD,KAAK5B,QAAA,CAASoB,MAAA,CAAOF,KAAA,EAAO,GAAGT,KAAK,GAEpC,KAAKL,gBAAA,CAAiBc,KAAK;EAC/B;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAa,WAAWb,KAAA,EACX;IACI,IAAIA,KAAA,GAAQ,KAAKA,KAAA,IAAS,KAAKlB,QAAA,CAASO,MAAA,EAEpC,MAAM,IAAIY,KAAA,CAAM,sBAAsBD,KAAK,mBAAmB;IAG3D,YAAKlB,QAAA,CAASkB,KAAK;EAC9B;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAP,YAAA,GAA8BX,QAAA,EAC9B;IAEI,IAAIA,QAAA,CAASO,MAAA,GAAS,GAGlB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIR,QAAA,CAASO,MAAA,EAAQC,CAAA,IAE5B,KAAAG,WAAA,CAAYX,QAAA,CAASQ,CAAC,CAAC,OAIpC;MACU,MAAAC,KAAA,GAAQT,QAAA,CAAS,CAAC;QAClBkB,KAAA,GAAQ,KAAKlB,QAAA,CAAS0B,OAAA,CAAQjB,KAAK;MAEzC,IAAIS,KAAA,KAAU,IAAW;MAEzBT,KAAA,CAAMC,MAAA,GAAS,MAEfD,KAAA,CAAMG,SAAA,CAAUC,SAAA,GAAY,IAC5BgB,KAAA,CAAMC,WAAA,CAAY,KAAK9B,QAAA,EAAUkB,KAAA,EAAO,CAAC,GAGzC,KAAKH,SAAA,IAGL,KAAKX,gBAAA,CAAiBc,KAAK,GAC3BT,KAAA,CAAMO,IAAA,CAAK,WAAW,IAAI,GAC1B,KAAKA,IAAA,CAAK,gBAAgBP,KAAA,EAAO,MAAMS,KAAK;IAChD;IAEA,OAAOlB,QAAA,CAAS,CAAC;EACrB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAgC,cAAcd,KAAA,EACd;IACU,MAAAT,KAAA,GAAQ,KAAKsB,UAAA,CAAWb,KAAK;IAGnC,OAAAT,KAAA,CAAMC,MAAA,GAAS,MACfD,KAAA,CAAMG,SAAA,CAAUC,SAAA,GAAY,IAC5BgB,KAAA,CAAMC,WAAA,CAAY,KAAK9B,QAAA,EAAUkB,KAAA,EAAO,CAAC,GAGzC,KAAKH,SAAA,IAGL,KAAKX,gBAAA,CAAiBc,KAAK,GAC3BT,KAAA,CAAMO,IAAA,CAAK,WAAW,IAAI,GAC1B,KAAKA,IAAA,CAAK,gBAAgBP,KAAA,EAAO,MAAMS,KAAK,GAErCT,KAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAwB,eAAeC,UAAA,GAAa,GAAGC,QAAA,GAAW,KAAKnC,QAAA,CAASO,MAAA,EACxD;IACI,MAAM6B,KAAA,GAAQF,UAAA;MACRG,GAAA,GAAMF,QAAA;MACNG,KAAA,GAAQD,GAAA,GAAMD,KAAA;IAChB,IAAAG,OAAA;IAEA,IAAAD,KAAA,GAAQ,KAAKA,KAAA,IAASD,GAAA,EAC1B;MACIE,OAAA,GAAU,KAAKvC,QAAA,CAASoB,MAAA,CAAOgB,KAAA,EAAOE,KAAK;MAE3C,SAAS9B,CAAA,GAAI,GAAGA,CAAA,GAAI+B,OAAA,CAAQhC,MAAA,EAAQ,EAAEC,CAAA,EAElC+B,OAAA,CAAQ/B,CAAC,EAAEE,MAAA,GAAS,MAChB6B,OAAA,CAAQ/B,CAAC,EAAEI,SAAA,KAEX2B,OAAA,CAAQ/B,CAAC,EAAEI,SAAA,CAAUC,SAAA,GAAY;MAIpC,KAAAE,SAAA,IAEL,KAAKX,gBAAA,CAAiB8B,UAAU;MAEhC,SAAS1B,CAAA,GAAI,GAAGA,CAAA,GAAI+B,OAAA,CAAQhC,MAAA,EAAQ,EAAEC,CAAA,EAElC+B,OAAA,CAAQ/B,CAAC,EAAEQ,IAAA,CAAK,WAAW,IAAI,GAC/B,KAAKA,IAAA,CAAK,gBAAgBuB,OAAA,CAAQ/B,CAAC,GAAG,MAAMA,CAAC;MAG1C,OAAA+B,OAAA;IAAA,WAEFD,KAAA,KAAU,KAAK,KAAKtC,QAAA,CAASO,MAAA,KAAW,GAE7C,OAAO;IAGL,UAAIiC,UAAA,CAAW,kEAAkE;EAC3F;EAAA;EAGAjD,aAAA,EACA;IACI,IAAIkD,YAAA,GAAe;IAEV,SAAAjC,CAAA,GAAI,GAAGkC,CAAA,GAAI,KAAK1C,QAAA,CAASO,MAAA,EAAQC,CAAA,GAAIkC,CAAA,EAAG,EAAElC,CAAA,EACnD;MACU,MAAAC,KAAA,GAAQ,KAAKT,QAAA,CAASQ,CAAC;MAE7BC,KAAA,CAAMd,gBAAA,GAAmBa,CAAA,EAErB,CAACiC,YAAA,IAAgBhC,KAAA,CAAMf,MAAA,KAAW,MAElC+C,YAAA,GAAe;IAEvB;IAEIA,YAAA,IAAgB,KAAKzC,QAAA,CAASO,MAAA,GAAS,KAEvC,KAAKP,QAAA,CAAS2C,IAAA,CAAKpD,YAAY,GAGnC,KAAKY,SAAA,GAAY;EACrB;EAAA;EAGAyC,gBAAA,EACA;IACQ,KAAK3C,gBAAA,IAAoB,KAAKE,SAAA,IAE9B,KAAKZ,YAAA,CAAa,GAGtB,KAAKwB,SAAA,IAEL,KAAKH,SAAA,CAAUgC,eAAA,CAAgB,KAAKlC,MAAA,CAAOE,SAAS,GAGpD,KAAKiC,UAAA,GAAa,KAAKC,KAAA,GAAQ,KAAKpC,MAAA,CAAOmC,UAAA;IAElC,SAAArC,CAAA,GAAI,GAAGkC,CAAA,GAAI,KAAK1C,QAAA,CAASO,MAAA,EAAQC,CAAA,GAAIkC,CAAA,EAAG,EAAElC,CAAA,EACnD;MACU,MAAAC,KAAA,GAAQ,KAAKT,QAAA,CAASQ,CAAC;MAEzBC,KAAA,CAAMsC,OAAA,IAENtC,KAAA,CAAMmC,eAAA;IAEd;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAI,gBAAA,EACA;IACI,KAAKC,OAAA,CAAQC,KAAA,CAEb,QAAKC,gBAAA,CAAiB;IAEtB,SAAS3C,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKR,QAAA,CAASO,MAAA,EAAQC,CAAA,IAC1C;MACU,MAAAC,KAAA,GAAQ,KAAKT,QAAA,CAASQ,CAAC;MAE7B,IAAI,EAAC,CAAAC,KAAA,CAAMsC,OAAA,IAAW,CAACtC,KAAA,CAAM2C,UAAA,GAQ7B,IAHA3C,KAAA,CAAMuC,eAAA,IAGFvC,KAAA,CAAM4C,KAAA,EACV;QACI,MAAMC,UAAA,GAAe7C,KAAA,CAAM4C,KAAA,CAAmBE,UAAA,GACvC9C,KAAA,CAAM4C,KAAA,CAAmBC,UAAA,GAAa7C,KAAA,CAAM4C,KAAA;QAE/CC,UAAA,IAEAA,UAAA,CAAWN,eAAA,CACX,QAAKC,OAAA,CAAQO,aAAA,CAAc/C,KAAA,CAAMwC,OAAA,EAASK,UAAA,CAAWL,OAAO,KAI5D,KAAKA,OAAA,CAAQQ,SAAA,CAAUhD,KAAA,CAAMwC,OAAO;MAE5C,OACSxC,KAAA,CAAMiD,UAAA,GAEX,KAAKT,OAAA,CAAQU,aAAA,CAAclD,KAAA,CAAMwC,OAAA,EAASxC,KAAA,CAAMiD,UAAU,IAI1D,KAAKT,OAAA,CAAQQ,SAAA,CAAUhD,KAAA,CAAMwC,OAAO;IAE5C;IAEK,KAAAA,OAAA,CAAQW,QAAA,GAAW,KAAK7C,SAAA;EACjC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYO8C,eAAeC,IAAA,EAAkBC,kBAAA,GAAqB,IAC7D;IACU,MAAAC,MAAA,GAAS,MAAMH,cAAA,CAAeC,IAAI;IAExC,IAAI,CAACC,kBAAA,EAEQ,SAAAvD,CAAA,GAAI,GAAGkC,CAAA,GAAI,KAAK1C,QAAA,CAASO,MAAA,EAAQC,CAAA,GAAIkC,CAAA,EAAG,EAAElC,CAAA,EACnD;MACU,MAAAC,KAAA,GAAQ,KAAKT,QAAA,CAASQ,CAAC;MAEzBC,KAAA,CAAMsC,OAAA,IAENtC,KAAA,CAAMmC,eAAA;IAEd;IAGG,OAAAoB,MAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOUb,iBAAA,EACV,CAEA;EAAA;AAAA;AAAA;AAAA;AAAA;EAOUc,mBAAmBC,QAAA,EAC7B;IACU,MAAAC,WAAA,GAAcD,QAAA,CAASE,aAAA,CAAcD,WAAA;IAG3C,IAAI,EAAEA,WAAA,CAAYE,KAAA,GAAQ,KAAKF,WAAA,CAAYG,MAAA,GAAS,IAEhD;IAQJ,IAAIC,MAAA,EACA3D,SAAA;IAIA,KAAK4D,QAAA,IAELD,MAAA,GAAS,KAAKC,QAAA,EACd5D,SAAA,GAAY,KAAK6D,cAAA,IAGZ,KAAKC,OAAA,KAAY7E,WAAA,CAAU8E,SAAA,CAAUD,OAAA,KAE1CH,MAAA,GAAS,KAAKK,SAAA,CAAU,EAAI;IAI1B,MAAAC,mBAAA,GAAsBX,QAAA,CAASY,UAAA,CAAWlE,SAAA;IAgBhD,IAdIiE,mBAAA,KAEIjE,SAAA,IAEAA,SAAA,GAAYvB,UAAA,CAAW0F,QAAA,CAASnE,SAAS,GACzCA,SAAA,CAAUoE,OAAA,CAAQH,mBAAmB,KAIrCjE,SAAA,GAAYiE,mBAAA,GAKhBN,MAAA,IAAUJ,WAAA,CAAYc,UAAA,CAAWV,MAAA,EAAQ3D,SAAS,GAElD,KAAK8D,OAAA,CAAQR,QAAQ,WAGhB,KAAKM,QAAA,EAEV;IASK,SAAAhE,CAAA,GAAI,GAAGkC,CAAA,GAAI,KAAK1C,QAAA,CAASO,MAAA,EAAQC,CAAA,GAAIkC,CAAA,EAAG,EAAElC,CAAA,EACnD;MACI,MAAMC,KAAA,GAAQ,KAAKT,QAAA,CAASQ,CAAC;QACvB0E,aAAA,GAAgBzE,KAAA,CAAM0E,QAAA;MAEtB1E,KAAA,CAAA0E,QAAA,GAAWD,aAAA,IAAiB,CAAC,KAAKV,QAAA,EACxC/D,KAAA,CAAM2E,MAAA,CAAOlB,QAAQ,GACrBzD,KAAA,CAAM0E,QAAA,GAAWD,aAAA;IACrB;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EA0BAE,OAAOlB,QAAA,EACP;IAEI,IAAI,GAAC,KAAKnB,OAAA,IAAW,KAAKF,UAAA,IAAc,KAAK,CAAC,KAAKO,UAAA,GAM/C,SAAKC,KAAA,IAAS,KAAKgC,OAAA,EAAS9E,MAAA,EAE5B,KAAK+E,cAAA,CAAepB,QAAQ,WAEvB,KAAKiB,QAAA,EAEV,KAAKlB,kBAAA,CAAmBC,QAAQ,OAGpC;MACI,KAAKQ,OAAA,CAAQR,QAAQ;MAEZ,SAAA1D,CAAA,GAAI,GAAGkC,CAAA,GAAI,KAAK1C,QAAA,CAASO,MAAA,EAAQC,CAAA,GAAIkC,CAAA,EAAG,EAAElC,CAAA,EAE/C,KAAKR,QAAA,CAASQ,CAAC,EAAE4E,MAAA,CAAOlB,QAAQ;IAExC;EACJ;EAAA;AAAA;AAAA;AAAA;EAMUoB,eAAepB,QAAA,EACzB;IACI,MAAMmB,OAAA,GAAU,KAAKA,OAAA;MACfE,IAAA,GAAO,KAAKlC,KAAA;IAGlB,IAAIgC,OAAA,EACJ;MACS,KAAKG,eAAA,KAEN,KAAKA,eAAA,GAAkB,KAG3B,KAAKA,eAAA,CAAgBjF,MAAA,GAAS;MAE9B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI6E,OAAA,CAAQ9E,MAAA,EAAQC,CAAA,IAE5B6E,OAAA,CAAQ7E,CAAC,EAAEiF,OAAA,IAEX,KAAKD,eAAA,CAAgB1E,IAAA,CAAKuE,OAAA,CAAQ7E,CAAC,CAAC;IAGhD;IAEA,MAAMkF,KAAA,GAASL,OAAA,IAAW,KAAKG,eAAA,EAAiBjF,MAAA,IACxCgF,IAAA,KAAS,CAACA,IAAA,CAAKhC,UAAA,IACXgC,IAAA,CAAKE,OAAA,KAAYF,IAAA,CAAKI,UAAA,IAAcJ,IAAA,CAAKK,IAAA,KAASC,UAAA,CAAWC,IAAA;IAErE,IAAAJ,KAAA,IAEAxB,QAAA,CAAS6B,KAAA,CAAML,KAAA,CAAM,GAGrBL,OAAA,IAAW,KAAKG,eAAA,EAAiBjF,MAAA,IAEjC2D,QAAA,CAAS8B,MAAA,CAAOlF,IAAA,CAAK,MAAM,KAAK0E,eAAe,GAG/CD,IAAA,IAEArB,QAAA,CAASqB,IAAA,CAAKzE,IAAA,CAAK,MAAM,KAAKuC,KAAK,GAGnC,KAAK8B,QAAA,EAEL,KAAKlB,kBAAA,CAAmBC,QAAQ,OAGpC;MACI,KAAKQ,OAAA,CAAQR,QAAQ;MAEZ,SAAA1D,CAAA,GAAI,GAAGkC,CAAA,GAAI,KAAK1C,QAAA,CAASO,MAAA,EAAQC,CAAA,GAAIkC,CAAA,EAAG,EAAElC,CAAA,EAE/C,KAAKR,QAAA,CAASQ,CAAC,EAAE4E,MAAA,CAAOlB,QAAQ;IAExC;IAEIwB,KAAA,IAEAxB,QAAA,CAAS6B,KAAA,CAAML,KAAA,CAAM,GAGrBH,IAAA,IAEArB,QAAA,CAASqB,IAAA,CAAKU,GAAA,CAAI,IAAI,GAGtBZ,OAAA,IAAW,KAAKG,eAAA,EAAiBjF,MAAA,IAEjC2D,QAAA,CAAS8B,MAAA,CAAOC,GAAA;EAExB;EAAA;AAAA;AAAA;AAAA;EAMUvB,QAAQwB,SAAA,EAClB,CAEA;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAcAC,QAAQC,OAAA,EACR;IACU,MAAAD,OAAA,CAEN,QAAKhG,SAAA,GAAY;IAEjB,MAAMkG,eAAA,GAAkB,OAAOD,OAAA,IAAY,YAAYA,OAAA,GAAUA,OAAA,EAASpG,QAAA;MAEpEsG,WAAA,GAAc,KAAKrE,cAAA,CAAe,GAAG,KAAKjC,QAAA,CAASO,MAAM;IAE3D,IAAA8F,eAAA,EAEA,SAAS7F,CAAA,GAAI,GAAGA,CAAA,GAAI8F,WAAA,CAAY/F,MAAA,EAAQ,EAAEC,CAAA,EAE1B8F,WAAA,CAAA9F,CAAC,EAAE2F,OAAA,CAAQC,OAAO;EAG1C;EAAA;EAGA,IAAI/B,MAAA,EACJ;IACI,OAAO,KAAKkC,KAAA,CAAMC,CAAA,GAAI,KAAK3C,cAAA,CAAiB,EAAAQ,KAAA;EAChD;EAEA,IAAIA,MAAMoC,KAAA,EACV;IACU,MAAApC,KAAA,GAAQ,KAAKR,cAAA,GAAiBQ,KAAA;IAEhCA,KAAA,KAAU,IAEV,KAAKkC,KAAA,CAAMC,CAAA,GAAIC,KAAA,GAAQpC,KAAA,GAIvB,KAAKkC,KAAA,CAAMC,CAAA,GAAI,GAGnB,KAAKE,MAAA,GAASD,KAAA;EAClB;EAAA;EAGA,IAAInC,OAAA,EACJ;IACI,OAAO,KAAKiC,KAAA,CAAMI,CAAA,GAAI,KAAK9C,cAAA,CAAiB,EAAAS,MAAA;EAChD;EAEA,IAAIA,OAAOmC,KAAA,EACX;IACU,MAAAnC,MAAA,GAAS,KAAKT,cAAA,GAAiBS,MAAA;IAEjCA,MAAA,KAAW,IAEX,KAAKiC,KAAA,CAAMI,CAAA,GAAIF,KAAA,GAAQnC,MAAA,GAIvB,KAAKiC,KAAA,CAAMI,CAAA,GAAI,GAGnB,KAAKC,OAAA,GAAUH,KAAA;EACnB;AACJ;AApxBa7G,UAAA,CAeKM,uBAAA,GAA0B;AAfrC,IAAM2G,SAAA,GAANjH,UAAA;AA4xBPiH,SAAA,CAAUlC,SAAA,CAAUmC,wBAAA,GAA2BD,SAAA,CAAUlC,SAAA,CAAU/B,eAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}