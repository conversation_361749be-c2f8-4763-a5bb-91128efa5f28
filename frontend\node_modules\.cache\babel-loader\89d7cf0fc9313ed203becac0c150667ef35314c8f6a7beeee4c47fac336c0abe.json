{"ast": null, "code": "import { ExtensionType, extensions } from \"@pixi/core\";\nimport { testImageFormat } from \"../utils/testImageFormat.mjs\";\nconst detectWebp = {\n  extension: {\n    type: ExtensionType.DetectionParser,\n    priority: 0\n  },\n  test: async () => testImageFormat(\"data:image/webp;base64,UklGRh4AAABXRUJQVlA4TBEAAAAvAAAAAAfQ//73v/+BiOh/AAA=\"),\n  add: async formats => [...formats, \"webp\"],\n  remove: async formats => formats.filter(f => f !== \"webp\")\n};\nextensions.add(detectWebp);\nexport { detectWebp };", "map": {"version": 3, "names": ["detectWebp", "extension", "type", "ExtensionType", "DetectionParser", "priority", "test", "testImageFormat", "add", "formats", "remove", "filter", "f", "extensions"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\assets\\src\\detections\\parsers\\detectWebp.ts"], "sourcesContent": ["import { extensions, ExtensionType } from '@pixi/core';\nimport { testImageFormat } from '../utils/testImageFormat';\n\nimport type { FormatDetectionParser } from '..';\n\nexport const detectWebp = {\n    extension: {\n        type: ExtensionType.DetectionParser,\n        priority: 0,\n    },\n    test: async (): Promise<boolean> => testImageFormat(\n        'data:image/webp;base64,UklGRh4AAABXRUJQVlA4TBEAAAAvAAAAAAfQ//73v/+BiOh/AAA='\n    ),\n    add: async (formats) => [...formats, 'webp'],\n    remove: async (formats) => formats.filter((f) => f !== 'webp'),\n} as FormatDetectionParser;\n\nextensions.add(detectWebp);\n"], "mappings": ";;AAKO,MAAMA,UAAA,GAAa;EACtBC,SAAA,EAAW;IACPC,IAAA,EAAMC,aAAA,CAAcC,eAAA;IACpBC,QAAA,EAAU;EACd;EACAC,IAAA,EAAM,MAAAA,CAAA,KAA8BC,eAAA,CAChC,6EACJ;EACAC,GAAA,EAAK,MAAOC,OAAA,IAAY,CAAC,GAAGA,OAAA,EAAS,MAAM;EAC3CC,MAAA,EAAQ,MAAOD,OAAA,IAAYA,OAAA,CAAQE,MAAA,CAAQC,CAAA,IAAMA,CAAA,KAAM,MAAM;AACjE;AAEAC,UAAA,CAAWL,GAAA,CAAIR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}