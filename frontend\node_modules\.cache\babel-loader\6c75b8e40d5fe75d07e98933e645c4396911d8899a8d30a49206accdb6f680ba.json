{"ast": null, "code": "import { TYPES, FORMATS } from \"@pixi/constants\";\nclass GLTexture {\n  constructor(texture) {\n    this.texture = texture, this.width = -1, this.height = -1, this.dirtyId = -1, this.dirtyStyleId = -1, this.mipmap = !1, this.wrapMode = 33071, this.type = TYPES.UNSIGNED_BYTE, this.internalFormat = FORMATS.RGBA, this.samplerType = 0;\n  }\n}\nexport { GLTexture };", "map": {"version": 3, "names": ["GLTexture", "constructor", "texture", "width", "height", "dirtyId", "dirtyStyleId", "mipmap", "wrapMode", "type", "TYPES", "UNSIGNED_BYTE", "internalFormat", "FORMATS", "RGBA", "samplerType"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\textures\\GLTexture.ts"], "sourcesContent": ["import { FORMATS, TYPES } from '@pixi/constants';\n\n/**\n * Internal texture for WebGL context.\n * @memberof PIXI\n */\nexport class GLTexture\n{\n    /** The WebGL texture. */\n    public texture: WebGLTexture;\n\n    /** Width of texture that was used in texImage2D. */\n    public width: number;\n\n    /** Height of texture that was used in texImage2D. */\n    public height: number;\n\n    /** Whether mip levels has to be generated. */\n    public mipmap: boolean;\n\n    /** WrapMode copied from baseTexture. */\n    public wrapMode: number;\n\n    /** Type copied from baseTexture. */\n    public type: number;\n\n    /** Type copied from baseTexture. */\n    public internalFormat: number;\n\n    /** Type of sampler corresponding to this texture. See {@link PIXI.SAMPLER_TYPES} */\n    public samplerType: number;\n\n    /** Texture contents dirty flag. */\n    dirtyId: number;\n\n    /** Texture style dirty flag. */\n    dirtyStyleId: number;\n\n    constructor(texture: WebGLTexture)\n    {\n        this.texture = texture;\n        this.width = -1;\n        this.height = -1;\n        this.dirtyId = -1;\n        this.dirtyStyleId = -1;\n        this.mipmap = false;\n        this.wrapMode = 33071;\n        this.type = TYPES.UNSIGNED_BYTE;\n        this.internalFormat = FORMATS.RGBA;\n\n        this.samplerType = 0;\n    }\n}\n"], "mappings": ";AAMO,MAAMA,SAAA,CACb;EA+BIC,YAAYC,OAAA,EACZ;IACI,KAAKA,OAAA,GAAUA,OAAA,EACf,KAAKC,KAAA,GAAQ,IACb,KAAKC,MAAA,GAAS,IACd,KAAKC,OAAA,GAAU,IACf,KAAKC,YAAA,GAAe,IACpB,KAAKC,MAAA,GAAS,IACd,KAAKC,QAAA,GAAW,OAChB,KAAKC,IAAA,GAAOC,KAAA,CAAMC,aAAA,EAClB,KAAKC,cAAA,GAAiBC,OAAA,CAAQC,IAAA,EAE9B,KAAKC,WAAA,GAAc;EACvB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}