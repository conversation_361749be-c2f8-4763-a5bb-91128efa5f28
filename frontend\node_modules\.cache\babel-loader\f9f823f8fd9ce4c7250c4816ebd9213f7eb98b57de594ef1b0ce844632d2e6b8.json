{"ast": null, "code": "import { <PERSON>uff<PERSON> } from \"../geometry/Buffer.mjs\";\nimport { Geometry } from \"../geometry/Geometry.mjs\";\nclass QuadUv extends Geometry {\n  constructor() {\n    super(), this.vertices = new Float32Array([-1, -1, 1, -1, 1, 1, -1, 1]), this.uvs = new Float32Array([0, 0, 1, 0, 1, 1, 0, 1]), this.vertexBuffer = new Buffer(this.vertices), this.uvBuffer = new Buffer(this.uvs), this.addAttribute(\"aVertexPosition\", this.vertexBuffer).addAttribute(\"aTextureCoord\", this.uvBuffer).addIndex([0, 1, 2, 0, 2, 3]);\n  }\n  /**\n   * Maps two Rectangle to the quad.\n   * @param targetTextureFrame - The first rectangle\n   * @param destinationFrame - The second rectangle\n   * @returns - Returns itself.\n   */\n  map(targetTextureFrame, destinationFrame) {\n    let x = 0,\n      y = 0;\n    return this.uvs[0] = x, this.uvs[1] = y, this.uvs[2] = x + destinationFrame.width / targetTextureFrame.width, this.uvs[3] = y, this.uvs[4] = x + destinationFrame.width / targetTextureFrame.width, this.uvs[5] = y + destinationFrame.height / targetTextureFrame.height, this.uvs[6] = x, this.uvs[7] = y + destinationFrame.height / targetTextureFrame.height, x = destinationFrame.x, y = destinationFrame.y, this.vertices[0] = x, this.vertices[1] = y, this.vertices[2] = x + destinationFrame.width, this.vertices[3] = y, this.vertices[4] = x + destinationFrame.width, this.vertices[5] = y + destinationFrame.height, this.vertices[6] = x, this.vertices[7] = y + destinationFrame.height, this.invalidate(), this;\n  }\n  /**\n   * Legacy upload method, just marks buffers dirty.\n   * @returns - Returns itself.\n   */\n  invalidate() {\n    return this.vertexBuffer._updateID++, this.uvBuffer._updateID++, this;\n  }\n}\nexport { QuadUv };", "map": {"version": 3, "names": ["QuadUv", "Geometry", "constructor", "vertices", "Float32Array", "uvs", "vertexBuffer", "<PERSON><PERSON><PERSON>", "uv<PERSON><PERSON><PERSON>", "addAttribute", "addIndex", "map", "targetTextureFrame", "destinationFrame", "x", "y", "width", "height", "invalidate", "_updateID"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\utils\\QuadUv.ts"], "sourcesContent": ["import { <PERSON>uff<PERSON> } from '../geometry/Buffer';\nimport { Geometry } from '../geometry/Geometry';\n\nimport type { Rectangle } from '@pixi/math';\n\n/**\n * Helper class to create a quad with uvs like in v4\n * @memberof PIXI\n */\nexport class QuadUv extends Geometry\n{\n    vertexBuffer: Buffer;\n    uvBuffer: Buffer;\n\n    /** An array of vertices. */\n    vertices: Float32Array;\n\n    /** The Uvs of the quad. */\n    uvs: Float32Array;\n\n    constructor()\n    {\n        super();\n\n        this.vertices = new Float32Array([\n            -1, -1,\n            1, -1,\n            1, 1,\n            -1, 1,\n        ]);\n\n        this.uvs = new Float32Array([\n            0, 0,\n            1, 0,\n            1, 1,\n            0, 1,\n        ]);\n\n        this.vertexBuffer = new Buffer(this.vertices);\n        this.uvBuffer = new Buffer(this.uvs);\n\n        this.addAttribute('aVertexPosition', this.vertexBuffer)\n            .addAttribute('aTextureCoord', this.uvBuffer)\n            .addIndex([0, 1, 2, 0, 2, 3]);\n    }\n\n    /**\n     * Maps two Rectangle to the quad.\n     * @param targetTextureFrame - The first rectangle\n     * @param destinationFrame - The second rectangle\n     * @returns - Returns itself.\n     */\n    map(targetTextureFrame: Rectangle, destinationFrame: Rectangle): this\n    {\n        let x = 0; // destinationFrame.x / targetTextureFrame.width;\n        let y = 0; // destinationFrame.y / targetTextureFrame.height;\n\n        this.uvs[0] = x;\n        this.uvs[1] = y;\n\n        this.uvs[2] = x + (destinationFrame.width / targetTextureFrame.width);\n        this.uvs[3] = y;\n\n        this.uvs[4] = x + (destinationFrame.width / targetTextureFrame.width);\n        this.uvs[5] = y + (destinationFrame.height / targetTextureFrame.height);\n\n        this.uvs[6] = x;\n        this.uvs[7] = y + (destinationFrame.height / targetTextureFrame.height);\n\n        x = destinationFrame.x;\n        y = destinationFrame.y;\n\n        this.vertices[0] = x;\n        this.vertices[1] = y;\n\n        this.vertices[2] = x + destinationFrame.width;\n        this.vertices[3] = y;\n\n        this.vertices[4] = x + destinationFrame.width;\n        this.vertices[5] = y + destinationFrame.height;\n\n        this.vertices[6] = x;\n        this.vertices[7] = y + destinationFrame.height;\n\n        this.invalidate();\n\n        return this;\n    }\n\n    /**\n     * Legacy upload method, just marks buffers dirty.\n     * @returns - Returns itself.\n     */\n    invalidate(): this\n    {\n        this.vertexBuffer._updateID++;\n        this.uvBuffer._updateID++;\n\n        return this;\n    }\n}\n"], "mappings": ";;AASO,MAAMA,MAAA,SAAeC,QAAA,CAC5B;EAUIC,YAAA,EACA;IACU,SAED,KAAAC,QAAA,GAAW,IAAIC,YAAA,CAAa,CAC7B,IAAI,IACJ,GAAG,IACH,GAAG,GACH,IAAI,EACP,GAED,KAAKC,GAAA,GAAM,IAAID,YAAA,CAAa,CACxB,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,EACN,GAED,KAAKE,YAAA,GAAe,IAAIC,MAAA,CAAO,KAAKJ,QAAQ,GAC5C,KAAKK,QAAA,GAAW,IAAID,MAAA,CAAO,KAAKF,GAAG,GAEnC,KAAKI,YAAA,CAAa,mBAAmB,KAAKH,YAAY,EACjDG,YAAA,CAAa,iBAAiB,KAAKD,QAAQ,EAC3CE,QAAA,CAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;EACpC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAC,IAAIC,kBAAA,EAA+BC,gBAAA,EACnC;IACQ,IAAAC,CAAA,GAAI;MACJC,CAAA,GAAI;IAEH,YAAAV,GAAA,CAAI,CAAC,IAAIS,CAAA,EACd,KAAKT,GAAA,CAAI,CAAC,IAAIU,CAAA,EAEd,KAAKV,GAAA,CAAI,CAAC,IAAIS,CAAA,GAAKD,gBAAA,CAAiBG,KAAA,GAAQJ,kBAAA,CAAmBI,KAAA,EAC/D,KAAKX,GAAA,CAAI,CAAC,IAAIU,CAAA,EAEd,KAAKV,GAAA,CAAI,CAAC,IAAIS,CAAA,GAAKD,gBAAA,CAAiBG,KAAA,GAAQJ,kBAAA,CAAmBI,KAAA,EAC/D,KAAKX,GAAA,CAAI,CAAC,IAAIU,CAAA,GAAKF,gBAAA,CAAiBI,MAAA,GAASL,kBAAA,CAAmBK,MAAA,EAEhE,KAAKZ,GAAA,CAAI,CAAC,IAAIS,CAAA,EACd,KAAKT,GAAA,CAAI,CAAC,IAAIU,CAAA,GAAKF,gBAAA,CAAiBI,MAAA,GAASL,kBAAA,CAAmBK,MAAA,EAEhEH,CAAA,GAAID,gBAAA,CAAiBC,CAAA,EACrBC,CAAA,GAAIF,gBAAA,CAAiBE,CAAA,EAErB,KAAKZ,QAAA,CAAS,CAAC,IAAIW,CAAA,EACnB,KAAKX,QAAA,CAAS,CAAC,IAAIY,CAAA,EAEnB,KAAKZ,QAAA,CAAS,CAAC,IAAIW,CAAA,GAAID,gBAAA,CAAiBG,KAAA,EACxC,KAAKb,QAAA,CAAS,CAAC,IAAIY,CAAA,EAEnB,KAAKZ,QAAA,CAAS,CAAC,IAAIW,CAAA,GAAID,gBAAA,CAAiBG,KAAA,EACxC,KAAKb,QAAA,CAAS,CAAC,IAAIY,CAAA,GAAIF,gBAAA,CAAiBI,MAAA,EAExC,KAAKd,QAAA,CAAS,CAAC,IAAIW,CAAA,EACnB,KAAKX,QAAA,CAAS,CAAC,IAAIY,CAAA,GAAIF,gBAAA,CAAiBI,MAAA,EAExC,KAAKC,UAAA,IAEE;EACX;EAAA;AAAA;AAAA;AAAA;EAMAA,WAAA,EACA;IACI,YAAKZ,YAAA,CAAaa,SAAA,IAClB,KAAKX,QAAA,CAASW,SAAA,IAEP;EACX;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}