using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using UnityEngine.Networking;

public class DataInitializer : MonoBehaviour
{
    [Header("Data Sources")]
    public ProvinceData provinceData;
    public string backendUrl = "http://localhost:8000";
    
    [Header("Default Values")]
    public int defaultPopulation = 10000;
    public int defaultResources = 50;
    public float defaultStability = 60f;
    
    [Header("Sample Countries")]
    public string[] sampleCountries = {
        "France", "England", "Spain", "Austria", "Ottoman Empire",
        "Russia", "Poland", "Brandenburg", "Venice", "Portugal"
    };

    void Start()
    {
        if (provinceData != null && (provinceData.Provinces == null || provinceData.Provinces.Count == 0))
        {
            StartCoroutine(InitializeProvinceData());
        }
    }

    IEnumerator InitializeProvinceData()
    {
        Debug.Log("Initializing province data...");
        
        // Try to load from backend first
        yield return StartCoroutine(LoadFromBackend());
        
        // If backend failed or no data, create sample data
        if (provinceData.Provinces == null || provinceData.Provinces.Count == 0)
        {
            CreateSampleProvinces();
        }
        
        Debug.Log($"Province data initialized with {provinceData.Provinces.Count} provinces.");
    }

    IEnumerator LoadFromBackend()
    {
        using (UnityWebRequest request = UnityWebRequest.Get($"{backendUrl}/countries"))
        {
            yield return request.SendWebRequest();

            if (request.result == UnityWebRequest.Result.Success)
            {
                try
                {
                    string jsonResponse = request.downloadHandler.text;
                    Debug.Log("Successfully loaded countries from backend.");
                    
                    // Parse the JSON and convert to provinces
                    // This is a simplified version - in a real project you'd use proper JSON parsing
                    ParseBackendData(jsonResponse);
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to parse backend data: {e.Message}");
                }
            }
            else
            {
                Debug.LogWarning($"Failed to load from backend: {request.error}");
            }
        }
    }

    void ParseBackendData(string jsonData)
    {
        // Simplified parsing - in reality you'd use JsonUtility or Newtonsoft.Json
        // For now, just create sample data based on the sample countries
        CreateSampleProvinces();
    }

    void CreateSampleProvinces()
    {
        if (provinceData.Provinces == null)
        {
            provinceData.Provinces = new List<Province>();
        }
        
        provinceData.Provinces.Clear();
        
        for (int i = 0; i < sampleCountries.Length; i++)
        {
            string countryName = sampleCountries[i];
            
            // Create capital province
            var capital = CreateProvince(
                $"{countryName} Capital",
                countryName,
                defaultPopulation + Random.Range(-2000, 5000),
                defaultResources + Random.Range(-10, 20),
                true
            );
            
            provinceData.Provinces.Add(capital);
            
            // Create 2-4 additional provinces per country
            int additionalProvinces = Random.Range(2, 5);
            for (int j = 0; j < additionalProvinces; j++)
            {
                var province = CreateProvince(
                    $"{countryName} Province {j + 1}",
                    countryName,
                    defaultPopulation / 2 + Random.Range(-3000, 2000),
                    defaultResources / 2 + Random.Range(-15, 10),
                    false
                );
                
                provinceData.Provinces.Add(province);
            }
        }
        
        // Add some uncolonized provinces
        for (int i = 0; i < 10; i++)
        {
            var province = CreateProvince(
                $"Uncolonized Land {i + 1}",
                "Uncolonized",
                Random.Range(500, 2000),
                Random.Range(5, 25),
                false
            );
            
            provinceData.Provinces.Add(province);
        }
        
        // Set up adjacencies (simplified)
        SetupProvinceAdjacencies();
        
        Debug.Log($"Created {provinceData.Provinces.Count} sample provinces.");
    }

    Province CreateProvince(string name, string owner, int population, int resources, bool isCapital)
    {
        var province = new Province(name, owner, population, resources);
        
        province.IsCapital = isCapital;
        province.Stability = defaultStability + Random.Range(-20f, 20f);
        province.Development = isCapital ? Random.Range(2f, 4f) : Random.Range(0.5f, 2f);
        province.Culture = GetCultureForCountry(owner);
        province.Religion = GetReligionForCountry(owner);
        province.TerrainType = GetRandomTerrain();
        province.Climate = GetRandomClimate();
        
        // Set random position for map layout
        province.Position = new Vector2(
            Random.Range(-50f, 50f),
            Random.Range(-30f, 30f)
        );
        
        return province;
    }

    void SetupProvinceAdjacencies()
    {
        // Simple adjacency setup - each province is adjacent to 1-3 random other provinces
        foreach (var province in provinceData.Provinces)
        {
            int adjacentCount = Random.Range(1, 4);
            var potentialAdjacent = new List<Province>(provinceData.Provinces);
            potentialAdjacent.Remove(province);
            
            for (int i = 0; i < adjacentCount && potentialAdjacent.Count > 0; i++)
            {
                int randomIndex = Random.Range(0, potentialAdjacent.Count);
                var adjacent = potentialAdjacent[randomIndex];
                
                province.AddAdjacentProvince(adjacent.Name);
                adjacent.AddAdjacentProvince(province.Name);
                
                potentialAdjacent.RemoveAt(randomIndex);
            }
        }
    }

    string GetCultureForCountry(string country)
    {
        switch (country)
        {
            case "France": return "French";
            case "England": return "English";
            case "Spain": return "Spanish";
            case "Austria": return "Austrian";
            case "Ottoman Empire": return "Turkish";
            case "Russia": return "Russian";
            case "Poland": return "Polish";
            case "Brandenburg": return "German";
            case "Venice": return "Italian";
            case "Portugal": return "Portuguese";
            default: return "Local";
        }
    }

    string GetReligionForCountry(string country)
    {
        switch (country)
        {
            case "Ottoman Empire": return "Sunni Islam";
            case "Russia": return "Orthodox";
            case "Austria":
            case "France":
            case "Spain":
            case "Portugal":
            case "Venice":
            case "Poland": return "Catholic";
            case "England":
            case "Brandenburg": return "Protestant";
            default: return "Animism";
        }
    }

    string GetRandomTerrain()
    {
        string[] terrains = { "Plains", "Hills", "Mountains", "Forest", "Desert", "Coastal", "Marsh" };
        return terrains[Random.Range(0, terrains.Length)];
    }

    string GetRandomClimate()
    {
        string[] climates = { "Temperate", "Continental", "Mediterranean", "Arctic", "Tropical", "Arid" };
        return climates[Random.Range(0, climates.Length)];
    }

    [ContextMenu("Regenerate Province Data")]
    public void RegenerateData()
    {
        if (provinceData != null)
        {
            CreateSampleProvinces();
            Debug.Log("Province data regenerated!");
        }
    }
}
