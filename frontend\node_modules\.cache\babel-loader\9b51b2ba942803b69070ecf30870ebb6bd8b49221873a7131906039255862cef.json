{"ast": null, "code": "import { Texture } from \"@pixi/core\";\nimport { Mesh, MeshMaterial } from \"@pixi/mesh\";\nimport { PlaneGeometry } from \"./geometry/PlaneGeometry.mjs\";\nclass SimplePlane extends Mesh {\n  /**\n   * @param texture - The texture to use on the SimplePlane.\n   * @param verticesX - The number of vertices in the x-axis\n   * @param verticesY - The number of vertices in the y-axis\n   */\n  constructor(texture, verticesX, verticesY) {\n    const planeGeometry = new PlaneGeometry(texture.width, texture.height, verticesX, verticesY),\n      meshMaterial = new MeshMaterial(Texture.WHITE);\n    super(planeGeometry, meshMaterial), this.texture = texture, this.autoResize = !0;\n  }\n  /**\n   * Method used for overrides, to do something in case texture frame was changed.\n   * Meshes based on plane can override it and change more details based on texture.\n   */\n  textureUpdated() {\n    this._textureID = this.shader.texture._updateID;\n    const geometry = this.geometry,\n      {\n        width,\n        height\n      } = this.shader.texture;\n    this.autoResize && (geometry.width !== width || geometry.height !== height) && (geometry.width = this.shader.texture.width, geometry.height = this.shader.texture.height, geometry.build());\n  }\n  set texture(value) {\n    this.shader.texture !== value && (this.shader.texture = value, this._textureID = -1, value.baseTexture.valid ? this.textureUpdated() : value.once(\"update\", this.textureUpdated, this));\n  }\n  get texture() {\n    return this.shader.texture;\n  }\n  _render(renderer) {\n    this._textureID !== this.shader.texture._updateID && this.textureUpdated(), super._render(renderer);\n  }\n  destroy(options) {\n    this.shader.texture.off(\"update\", this.textureUpdated, this), super.destroy(options);\n  }\n}\nexport { SimplePlane };", "map": {"version": 3, "names": ["SimplePlane", "<PERSON><PERSON>", "constructor", "texture", "verticesX", "verticesY", "planeGeometry", "PlaneGeometry", "width", "height", "meshMaterial", "MeshMaterial", "Texture", "WHITE", "autoResize", "textureUpdated", "_textureID", "shader", "_updateID", "geometry", "build", "value", "baseTexture", "valid", "once", "_render", "renderer", "destroy", "options", "off"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\mesh-extras\\src\\SimplePlane.ts"], "sourcesContent": ["import { Texture } from '@pixi/core';\nimport { Mesh, MeshMaterial } from '@pixi/mesh';\nimport { PlaneGeometry } from './geometry/PlaneGeometry';\n\nimport type{ Renderer } from '@pixi/core';\nimport type { IDestroyOptions } from '@pixi/display';\n\n/**\n * The SimplePlane allows you to draw a texture across several points and then manipulate these points\n * @example\n * import { Point, SimplePlane, Texture } from 'pixi.js';\n *\n * for (let i = 0; i < 20; i++) {\n *     points.push(new Point(i * 50, 0));\n * }\n * const SimplePlane = new SimplePlane(Texture.from('snake.png'), points);\n * @memberof PIXI\n */\nexport class SimplePlane extends Mesh\n{\n    /** The geometry is automatically updated when the texture size changes. */\n    public autoResize: boolean;\n\n    protected _textureID: number;\n\n    /**\n     * @param texture - The texture to use on the SimplePlane.\n     * @param verticesX - The number of vertices in the x-axis\n     * @param verticesY - The number of vertices in the y-axis\n     */\n    constructor(texture: Texture, verticesX?: number, verticesY?: number)\n    {\n        const planeGeometry = new PlaneGeometry(texture.width, texture.height, verticesX, verticesY);\n        const meshMaterial = new MeshMaterial(Texture.WHITE);\n\n        super(planeGeometry, meshMaterial);\n\n        // lets call the setter to ensure all necessary updates are performed\n        this.texture = texture;\n        this.autoResize = true;\n    }\n\n    /**\n     * Method used for overrides, to do something in case texture frame was changed.\n     * Meshes based on plane can override it and change more details based on texture.\n     */\n    public textureUpdated(): void\n    {\n        this._textureID = this.shader.texture._updateID;\n\n        const geometry: PlaneGeometry = this.geometry as any;\n        const { width, height } = this.shader.texture;\n\n        if (this.autoResize && (geometry.width !== width || geometry.height !== height))\n        {\n            geometry.width = this.shader.texture.width;\n            geometry.height = this.shader.texture.height;\n            geometry.build();\n        }\n    }\n\n    set texture(value: Texture)\n    {\n        // Track texture same way sprite does.\n        // For generated meshes like NineSlicePlane it can change the geometry.\n        // Unfortunately, this method might not work if you directly change texture in material.\n\n        if (this.shader.texture === value)\n        {\n            return;\n        }\n\n        this.shader.texture = value;\n        this._textureID = -1;\n\n        if (value.baseTexture.valid)\n        {\n            this.textureUpdated();\n        }\n        else\n        {\n            value.once('update', this.textureUpdated, this);\n        }\n    }\n\n    get texture(): Texture\n    {\n        return this.shader.texture;\n    }\n\n    _render(renderer: Renderer): void\n    {\n        if (this._textureID !== this.shader.texture._updateID)\n        {\n            this.textureUpdated();\n        }\n\n        super._render(renderer);\n    }\n\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        this.shader.texture.off('update', this.textureUpdated, this);\n        super.destroy(options);\n    }\n}\n"], "mappings": ";;;AAkBO,MAAMA,WAAA,SAAoBC,IAAA,CACjC;EAAA;AAAA;AAAA;AAAA;AAAA;EAWIC,YAAYC,OAAA,EAAkBC,SAAA,EAAoBC,SAAA,EAClD;IACI,MAAMC,aAAA,GAAgB,IAAIC,aAAA,CAAcJ,OAAA,CAAQK,KAAA,EAAOL,OAAA,CAAQM,MAAA,EAAQL,SAAA,EAAWC,SAAS;MACrFK,YAAA,GAAe,IAAIC,YAAA,CAAaC,OAAA,CAAQC,KAAK;IAEnD,MAAMP,aAAA,EAAeI,YAAY,GAG5B,KAAAP,OAAA,GAAUA,OAAA,EACf,KAAKW,UAAA,GAAa;EACtB;EAAA;AAAA;AAAA;AAAA;EAMOC,eAAA,EACP;IACS,KAAAC,UAAA,GAAa,KAAKC,MAAA,CAAOd,OAAA,CAAQe,SAAA;IAEhC,MAAAC,QAAA,GAA0B,KAAKA,QAAA;MAC/B;QAAEX,KAAA;QAAOC;MAAA,IAAW,KAAKQ,MAAA,CAAOd,OAAA;IAElC,KAAKW,UAAA,KAAeK,QAAA,CAASX,KAAA,KAAUA,KAAA,IAASW,QAAA,CAASV,MAAA,KAAWA,MAAA,MAEpEU,QAAA,CAASX,KAAA,GAAQ,KAAKS,MAAA,CAAOd,OAAA,CAAQK,KAAA,EACrCW,QAAA,CAASV,MAAA,GAAS,KAAKQ,MAAA,CAAOd,OAAA,CAAQM,MAAA,EACtCU,QAAA,CAASC,KAAA,CAAM;EAEvB;EAEA,IAAIjB,QAAQkB,KAAA,EACZ;IAKQ,KAAKJ,MAAA,CAAOd,OAAA,KAAYkB,KAAA,KAK5B,KAAKJ,MAAA,CAAOd,OAAA,GAAUkB,KAAA,EACtB,KAAKL,UAAA,GAAa,IAEdK,KAAA,CAAMC,WAAA,CAAYC,KAAA,GAElB,KAAKR,cAAA,CAAe,IAIpBM,KAAA,CAAMG,IAAA,CAAK,UAAU,KAAKT,cAAA,EAAgB,IAAI;EAEtD;EAEA,IAAIZ,QAAA,EACJ;IACI,OAAO,KAAKc,MAAA,CAAOd,OAAA;EACvB;EAEAsB,QAAQC,QAAA,EACR;IACQ,KAAKV,UAAA,KAAe,KAAKC,MAAA,CAAOd,OAAA,CAAQe,SAAA,IAExC,KAAKH,cAAA,CAAe,GAGxB,MAAMU,OAAA,CAAQC,QAAQ;EAC1B;EAEOC,QAAQC,OAAA,EACf;IACS,KAAAX,MAAA,CAAOd,OAAA,CAAQ0B,GAAA,CAAI,UAAU,KAAKd,cAAA,EAAgB,IAAI,GAC3D,MAAMY,OAAA,CAAQC,OAAO;EACzB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}