{"ast": null, "code": "var defaultFragment = `varying vec2 vTextureCoord;\n\nuniform sampler2D uSampler;\n\nvoid main(void){\n   gl_FragColor = texture2D(uSampler, vTextureCoord);\n}\n`;\nexport { defaultFragment as default };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["var defaultFragment = `varying vec2 vTextureCoord;\n\nuniform sampler2D uSampler;\n\nvoid main(void){\n   gl_FragColor = texture2D(uSampler, vTextureCoord);\n}\n`;\nexport {\n  defaultFragment as default\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}