{"ast": null, "code": "import { ExtensionType, extensions } from \"@pixi/extensions\";\nimport { Rectangle } from \"@pixi/math\";\nimport { settings } from \"@pixi/settings\";\nclass ViewSystem {\n  constructor(renderer) {\n    this.renderer = renderer;\n  }\n  /**\n   * initiates the view system\n   * @param {PIXI.ViewOptions} options - the options for the view\n   */\n  init(options) {\n    this.screen = new Rectangle(0, 0, options.width, options.height), this.element = options.view || settings.ADAPTER.createCanvas(), this.resolution = options.resolution || settings.RESOLUTION, this.autoDensity = !!options.autoDensity;\n  }\n  /**\n   * Resizes the screen and canvas to the specified dimensions.\n   * @param desiredScreenWidth - The new width of the screen.\n   * @param desiredScreenHeight - The new height of the screen.\n   */\n  resizeView(desiredScreenWidth, desiredScreenHeight) {\n    this.element.width = Math.round(desiredScreenWidth * this.resolution), this.element.height = Math.round(desiredScreenHeight * this.resolution);\n    const screenWidth = this.element.width / this.resolution,\n      screenHeight = this.element.height / this.resolution;\n    this.screen.width = screenWidth, this.screen.height = screenHeight, this.autoDensity && (this.element.style.width = `${screenWidth}px`, this.element.style.height = `${screenHeight}px`), this.renderer.emit(\"resize\", screenWidth, screenHeight), this.renderer.runners.resize.emit(this.screen.width, this.screen.height);\n  }\n  /**\n   * Destroys this System and optionally removes the canvas from the dom.\n   * @param {boolean} [removeView=false] - Whether to remove the canvas from the DOM.\n   */\n  destroy(removeView) {\n    removeView && this.element.parentNode?.removeChild(this.element), this.renderer = null, this.element = null, this.screen = null;\n  }\n}\nViewSystem.defaultOptions = {\n  /**\n   * {@link PIXI.IRendererOptions.width}\n   * @default 800\n   * @memberof PIXI.settings.RENDER_OPTIONS\n   */\n  width: 800,\n  /**\n   * {@link PIXI.IRendererOptions.height}\n   * @default 600\n   * @memberof PIXI.settings.RENDER_OPTIONS\n   */\n  height: 600,\n  /**\n   * {@link PIXI.IRendererOptions.resolution}\n   * @type {number}\n   * @default PIXI.settings.RESOLUTION\n   * @memberof PIXI.settings.RENDER_OPTIONS\n   */\n  resolution: void 0,\n  /**\n   * {@link PIXI.IRendererOptions.autoDensity}\n   * @default false\n   * @memberof PIXI.settings.RENDER_OPTIONS\n   */\n  autoDensity: !1\n}, /** @ignore */\nViewSystem.extension = {\n  type: [ExtensionType.RendererSystem, ExtensionType.CanvasRendererSystem],\n  name: \"_view\"\n};\nextensions.add(ViewSystem);\nexport { ViewSystem };", "map": {"version": 3, "names": ["ViewSystem", "constructor", "renderer", "init", "options", "screen", "Rectangle", "width", "height", "element", "view", "settings", "ADAPTER", "createCanvas", "resolution", "RESOLUTION", "autoDensity", "resizeView", "desiredScreenWidth", "desiredScreenHeight", "Math", "round", "screenWidth", "screenHeight", "style", "emit", "runners", "resize", "destroy", "<PERSON><PERSON><PERSON><PERSON>", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "defaultOptions", "extension", "type", "ExtensionType", "RendererSystem", "CanvasRendererSystem", "name", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\view\\ViewSystem.ts"], "sourcesContent": ["import { extensions, ExtensionType } from '@pixi/extensions';\nimport { Rectangle } from '@pixi/math';\nimport { settings } from '@pixi/settings';\n\nimport type { ExtensionMetadata } from '@pixi/extensions';\nimport type { ICanvas } from '@pixi/settings';\nimport type { <PERSON><PERSON><PERSON><PERSON> } from '../IRenderer';\nimport type { ISystem } from '../system/ISystem';\n\n/**\n * Options for the view system.\n * @memberof PIXI\n */\nexport interface ViewSystemOptions\n{\n    /**\n     * The canvas to use as the view. If omitted, a new canvas will be created.\n     * @memberof PIXI.IRendererOptions\n     */\n    view?: ICanvas;\n    /**\n     * The width of the renderer's view.\n     * @memberof PIXI.IRendererOptions\n     */\n    width?: number;\n    /**\n     * The height of the renderer's view.\n     * @memberof PIXI.IRendererOptions\n     */\n    height?: number;\n    /**\n     * The resolution / device pixel ratio of the renderer.\n     * @memberof PIXI.IRendererOptions\n     */\n    resolution?: number;\n    /**\n     * Whether the CSS dimensions of the renderer's view should be resized automatically.\n     * @memberof PIXI.IRendererOptions\n     */\n    autoDensity?: boolean;\n}\n\n/**\n * The view system manages the main canvas that is attached to the DOM.\n * This main role is to deal with how the holding the view reference and dealing with how it is resized.\n * @memberof PIXI\n */\nexport class ViewSystem implements ISystem<ViewSystemOptions, boolean>\n{\n    /** @ignore */\n    static defaultOptions: ViewSystemOptions = {\n        /**\n         * {@link PIXI.IRendererOptions.width}\n         * @default 800\n         * @memberof PIXI.settings.RENDER_OPTIONS\n         */\n        width: 800,\n        /**\n         * {@link PIXI.IRendererOptions.height}\n         * @default 600\n         * @memberof PIXI.settings.RENDER_OPTIONS\n         */\n        height: 600,\n        /**\n         * {@link PIXI.IRendererOptions.resolution}\n         * @type {number}\n         * @default PIXI.settings.RESOLUTION\n         * @memberof PIXI.settings.RENDER_OPTIONS\n         */\n        resolution: undefined,\n        /**\n         * {@link PIXI.IRendererOptions.autoDensity}\n         * @default false\n         * @memberof PIXI.settings.RENDER_OPTIONS\n         */\n        autoDensity: false,\n    };\n\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        type: [\n            ExtensionType.RendererSystem,\n            ExtensionType.CanvasRendererSystem\n        ],\n        name: '_view',\n    };\n\n    private renderer: IRenderer;\n\n    /**\n     * The resolution / device pixel ratio of the renderer.\n     * @member {number}\n     * @default PIXI.settings.RESOLUTION\n     */\n    public resolution: number;\n\n    /**\n     * Measurements of the screen. (0, 0, screenWidth, screenHeight).\n     *\n     * Its safe to use as filterArea or hitArea for the whole stage.\n     * @member {PIXI.Rectangle}\n     */\n    public screen: Rectangle;\n\n    /**\n     * The canvas element that everything is drawn to.\n     * @member {PIXI.ICanvas}\n     */\n    public element: ICanvas;\n\n    /**\n     * Whether CSS dimensions of canvas view should be resized to screen dimensions automatically.\n     * @member {boolean}\n     */\n    public autoDensity: boolean;\n\n    constructor(renderer: IRenderer)\n    {\n        this.renderer = renderer;\n    }\n\n    /**\n     * initiates the view system\n     * @param {PIXI.ViewOptions} options - the options for the view\n     */\n    init(options: ViewSystemOptions): void\n    {\n        this.screen = new Rectangle(0, 0, options.width, options.height);\n\n        this.element = options.view || settings.ADAPTER.createCanvas() as ICanvas;\n\n        this.resolution = options.resolution || settings.RESOLUTION;\n\n        this.autoDensity = !!options.autoDensity;\n    }\n\n    /**\n     * Resizes the screen and canvas to the specified dimensions.\n     * @param desiredScreenWidth - The new width of the screen.\n     * @param desiredScreenHeight - The new height of the screen.\n     */\n    resizeView(desiredScreenWidth: number, desiredScreenHeight: number): void\n    {\n        this.element.width = Math.round(desiredScreenWidth * this.resolution);\n        this.element.height = Math.round(desiredScreenHeight * this.resolution);\n\n        const screenWidth = this.element.width / this.resolution;\n        const screenHeight = this.element.height / this.resolution;\n\n        this.screen.width = screenWidth;\n        this.screen.height = screenHeight;\n\n        if (this.autoDensity)\n        {\n            this.element.style.width = `${screenWidth}px`;\n            this.element.style.height = `${screenHeight}px`;\n        }\n\n        /**\n         * Fired after view has been resized.\n         * @event PIXI.Renderer#resize\n         * @param {number} screenWidth - The new width of the screen.\n         * @param {number} screenHeight - The new height of the screen.\n         */\n        this.renderer.emit('resize', screenWidth, screenHeight);\n        this.renderer.runners.resize.emit(this.screen.width, this.screen.height);\n    }\n\n    /**\n     * Destroys this System and optionally removes the canvas from the dom.\n     * @param {boolean} [removeView=false] - Whether to remove the canvas from the DOM.\n     */\n    destroy(removeView: boolean): void\n    {\n        // ka boom!\n        if (removeView)\n        {\n            this.element.parentNode?.removeChild(this.element);\n        }\n\n        this.renderer = null;\n        this.element = null;\n        this.screen = null;\n    }\n}\n\nextensions.add(ViewSystem);\n"], "mappings": ";;;AA+CO,MAAMA,UAAA,CACb;EAoEIC,YAAYC,QAAA,EACZ;IACI,KAAKA,QAAA,GAAWA,QAAA;EACpB;EAAA;AAAA;AAAA;AAAA;EAMAC,KAAKC,OAAA,EACL;IACI,KAAKC,MAAA,GAAS,IAAIC,SAAA,CAAU,GAAG,GAAGF,OAAA,CAAQG,KAAA,EAAOH,OAAA,CAAQI,MAAM,GAE/D,KAAKC,OAAA,GAAUL,OAAA,CAAQM,IAAA,IAAQC,QAAA,CAASC,OAAA,CAAQC,YAAA,CAAa,GAE7D,KAAKC,UAAA,GAAaV,OAAA,CAAQU,UAAA,IAAcH,QAAA,CAASI,UAAA,EAEjD,KAAKC,WAAA,GAAc,CAAC,CAACZ,OAAA,CAAQY,WAAA;EACjC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAC,WAAWC,kBAAA,EAA4BC,mBAAA,EACvC;IACI,KAAKV,OAAA,CAAQF,KAAA,GAAQa,IAAA,CAAKC,KAAA,CAAMH,kBAAA,GAAqB,KAAKJ,UAAU,GACpE,KAAKL,OAAA,CAAQD,MAAA,GAASY,IAAA,CAAKC,KAAA,CAAMF,mBAAA,GAAsB,KAAKL,UAAU;IAEhE,MAAAQ,WAAA,GAAc,KAAKb,OAAA,CAAQF,KAAA,GAAQ,KAAKO,UAAA;MACxCS,YAAA,GAAe,KAAKd,OAAA,CAAQD,MAAA,GAAS,KAAKM,UAAA;IAEhD,KAAKT,MAAA,CAAOE,KAAA,GAAQe,WAAA,EACpB,KAAKjB,MAAA,CAAOG,MAAA,GAASe,YAAA,EAEjB,KAAKP,WAAA,KAEL,KAAKP,OAAA,CAAQe,KAAA,CAAMjB,KAAA,GAAQ,GAAGe,WAAW,MACzC,KAAKb,OAAA,CAAQe,KAAA,CAAMhB,MAAA,GAAS,GAAGe,YAAY,OAS/C,KAAKrB,QAAA,CAASuB,IAAA,CAAK,UAAUH,WAAA,EAAaC,YAAY,GACtD,KAAKrB,QAAA,CAASwB,OAAA,CAAQC,MAAA,CAAOF,IAAA,CAAK,KAAKpB,MAAA,CAAOE,KAAA,EAAO,KAAKF,MAAA,CAAOG,MAAM;EAC3E;EAAA;AAAA;AAAA;AAAA;EAMAoB,QAAQC,UAAA,EACR;IAEQA,UAAA,IAEA,KAAKpB,OAAA,CAAQqB,UAAA,EAAYC,WAAA,CAAY,KAAKtB,OAAO,GAGrD,KAAKP,QAAA,GAAW,MAChB,KAAKO,OAAA,GAAU,MACf,KAAKJ,MAAA,GAAS;EAClB;AACJ;AAzIaL,UAAA,CAGFgC,cAAA,GAAoC;EAAA;AAAA;AAAA;AAAA;AAAA;EAMvCzB,KAAA,EAAO;EAAA;AAAA;AAAA;AAAA;AAAA;EAMPC,MAAA,EAAQ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAORM,UAAA,EAAY;EAAA;AAAA;AAAA;AAAA;AAAA;EAMZE,WAAA,EAAa;AACjB;AA7BShB,UAAA,CAgCFiC,SAAA,GAA+B;EAClCC,IAAA,EAAM,CACFC,aAAA,CAAcC,cAAA,EACdD,aAAA,CAAcE,oBAAA,CAClB;EACAC,IAAA,EAAM;AACV;AAqGJC,UAAA,CAAWC,GAAA,CAAIxC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}