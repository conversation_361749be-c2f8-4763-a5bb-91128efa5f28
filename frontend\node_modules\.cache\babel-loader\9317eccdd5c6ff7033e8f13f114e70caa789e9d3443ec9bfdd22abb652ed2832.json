{"ast": null, "code": "const _BoundingBox = class {\n  /**\n   * @param left - The left coordinate value of the bounding box.\n   * @param top - The top coordinate value of the bounding box.\n   * @param right - The right coordinate value of the bounding box.\n   * @param bottom - The bottom coordinate value of the bounding box.\n   */\n  constructor(left, top, right, bottom) {\n    this.left = left, this.top = top, this.right = right, this.bottom = bottom;\n  }\n  /** The width of the bounding box. */\n  get width() {\n    return this.right - this.left;\n  }\n  /** The height of the bounding box. */\n  get height() {\n    return this.bottom - this.top;\n  }\n  /** Determines whether the BoundingBox is empty. */\n  isEmpty() {\n    return this.left === this.right || this.top === this.bottom;\n  }\n};\n_BoundingBox.EMPTY = new _BoundingBox(0, 0, 0, 0);\nlet BoundingBox = _BoundingBox;\nexport { BoundingBox };", "map": {"version": 3, "names": ["_BoundingBox", "constructor", "left", "top", "right", "bottom", "width", "height", "isEmpty", "EMPTY", "BoundingBox"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\utils\\src\\media\\BoundingBox.ts"], "sourcesContent": ["/**\n * A rectangular bounding box describing the boundary of an area.\n * @memberof PIXI.utils\n * @since 7.1.0\n */\nexport class BoundingBox\n{\n    /** The left coordinate value of the bounding box. */\n    left: number;\n    /** The top coordinate value of the bounding box. */\n    top: number;\n    /** The right coordinate value of the bounding box. */\n    right: number;\n    /** The bottom coordinate value of the bounding box. */\n    bottom: number;\n\n    /**\n     * @param left - The left coordinate value of the bounding box.\n     * @param top - The top coordinate value of the bounding box.\n     * @param right - The right coordinate value of the bounding box.\n     * @param bottom - The bottom coordinate value of the bounding box.\n     */\n    constructor(left: number, top: number, right: number, bottom: number)\n    {\n        this.left = left;\n        this.top = top;\n        this.right = right;\n        this.bottom = bottom;\n    }\n\n    /** The width of the bounding box. */\n    get width(): number { return this.right - this.left; }\n    /** The height of the bounding box. */\n    get height(): number { return this.bottom - this.top; }\n\n    /** Determines whether the BoundingBox is empty. */\n    isEmpty(): boolean\n    {\n        return this.left === this.right || this.top === this.bottom;\n    }\n\n    /**\n     * An empty BoundingBox.\n     * @type {PIXI.utils.BoundingBox}\n     */\n    public static readonly EMPTY = new BoundingBox(0, 0, 0, 0);\n}\n"], "mappings": "AAKO,MAAMA,YAAA,GAAN,MACP;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAgBIC,YAAYC,IAAA,EAAcC,GAAA,EAAaC,KAAA,EAAeC,MAAA,EACtD;IACS,KAAAH,IAAA,GAAOA,IAAA,EACZ,KAAKC,GAAA,GAAMA,GAAA,EACX,KAAKC,KAAA,GAAQA,KAAA,EACb,KAAKC,MAAA,GAASA,MAAA;EAClB;EAAA;EAGA,IAAIC,MAAA,EAAgB;IAAS,YAAKF,KAAA,GAAQ,KAAKF,IAAA;EAAM;EAAA;EAErD,IAAIK,OAAA,EAAiB;IAAS,YAAKF,MAAA,GAAS,KAAKF,GAAA;EAAK;EAAA;EAGtDK,QAAA,EACA;IACI,OAAO,KAAKN,IAAA,KAAS,KAAKE,KAAA,IAAS,KAAKD,GAAA,KAAQ,KAAKE,MAAA;EACzD;AAOJ;AAzCaL,YAAA,CAwCcS,KAAA,GAAQ,IAAIT,YAAA,CAAY,GAAG,GAAG,GAAG,CAAC;AAxCtD,IAAMU,WAAA,GAANV,YAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}