{"ast": null, "code": "function compileShader(gl, type, src) {\n  const shader = gl.createShader(type);\n  return gl.shaderSource(shader, src), gl.compileShader(shader), shader;\n}\nexport { compileShader };", "map": {"version": 3, "names": ["compileShader", "gl", "type", "src", "shader", "createShader", "shaderSource"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\shader\\utils\\compileShader.ts"], "sourcesContent": ["/**\n * @private\n * @param {WebGLRenderingContext} gl - The current WebGL context {WebGLProgram}\n * @param {number} type - the type, can be either VERTEX_SHADER or FRAGMENT_SHADER\n * @param {string} src - The vertex shader source as an array of strings.\n * @returns {WebGLShader} the shader\n */\nexport function compileShader(gl: WebGLRenderingContextBase, type: number, src: string): WebGLShader\n{\n    const shader = gl.createShader(type);\n\n    gl.shaderSource(shader, src);\n    gl.compileShader(shader);\n\n    return shader;\n}\n"], "mappings": "AAOgB,SAAAA,cAAcC,EAAA,EAA+BC,IAAA,EAAcC,GAAA,EAC3E;EACU,MAAAC,MAAA,GAASH,EAAA,CAAGI,YAAA,CAAaH,IAAI;EAEnC,OAAAD,EAAA,CAAGK,YAAA,CAAaF,MAAA,EAAQD,GAAG,GAC3BF,EAAA,CAAGD,aAAA,CAAcI,MAAM,GAEhBA,MAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}