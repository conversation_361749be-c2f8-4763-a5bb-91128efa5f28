{"ast": null, "code": "import { settings, isMobile } from \"@pixi/settings\";\nfunction maxRecommendedTextures(max) {\n  let allowMax = !0;\n  const navigator = settings.ADAPTER.getNavigator();\n  if (isMobile.tablet || isMobile.phone) {\n    if (isMobile.apple.device) {\n      const match = navigator.userAgent.match(/OS (\\d+)_(\\d+)?/);\n      match && parseInt(match[1], 10) < 11 && (allowMax = !1);\n    }\n    if (isMobile.android.device) {\n      const match = navigator.userAgent.match(/Android\\s([0-9.]*)/);\n      match && parseInt(match[1], 10) < 7 && (allowMax = !1);\n    }\n  }\n  return allowMax ? max : 4;\n}\nexport { maxRecommendedTextures };", "map": {"version": 3, "names": ["maxRecommendedTextures", "max", "allowMax", "navigator", "settings", "ADAPTER", "getNavigator", "isMobile", "tablet", "phone", "apple", "device", "match", "userAgent", "parseInt", "android"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\batch\\maxRecommendedTextures.ts"], "sourcesContent": ["import { isMobile, settings } from '@pixi/settings';\n\n/**\n * The maximum recommended texture units to use.\n * In theory the bigger the better, and for desktop we'll use as many as we can.\n * But some mobile devices slow down if there is to many branches in the shader.\n * So in practice there seems to be a sweet spot size that varies depending on the device.\n *\n * In v4, all mobile devices were limited to 4 texture units because for this.\n * In v5, we allow all texture units to be used on modern Apple or Android devices.\n * @private\n * @param {number} max\n * @returns {number} The maximum recommended texture units to use.\n */\nexport function maxRecommendedTextures(max: number): number\n{\n    let allowMax = true;\n    const navigator = settings.ADAPTER.getNavigator();\n\n    if (isMobile.tablet || isMobile.phone)\n    {\n        if (isMobile.apple.device)\n        {\n            const match = (navigator.userAgent).match(/OS (\\d+)_(\\d+)?/);\n\n            if (match)\n            {\n                const majorVersion = parseInt(match[1], 10);\n\n                // Limit texture units on devices below iOS 11, which will be older hardware\n                if (majorVersion < 11)\n                {\n                    allowMax = false;\n                }\n            }\n        }\n        if (isMobile.android.device)\n        {\n            const match = (navigator.userAgent).match(/Android\\s([0-9.]*)/);\n\n            if (match)\n            {\n                const majorVersion = parseInt(match[1], 10);\n\n                // Limit texture units on devices below Android 7 (Nougat), which will be older hardware\n                if (majorVersion < 7)\n                {\n                    allowMax = false;\n                }\n            }\n        }\n    }\n\n    return allowMax ? max : 4;\n}\n"], "mappings": ";AAcO,SAASA,uBAAuBC,GAAA,EACvC;EACI,IAAIC,QAAA,GAAW;EACT,MAAAC,SAAA,GAAYC,QAAA,CAASC,OAAA,CAAQC,YAAA,CAAa;EAE5C,IAAAC,QAAA,CAASC,MAAA,IAAUD,QAAA,CAASE,KAAA,EAChC;IACQ,IAAAF,QAAA,CAASG,KAAA,CAAMC,MAAA,EACnB;MACI,MAAMC,KAAA,GAAST,SAAA,CAAUU,SAAA,CAAWD,KAAA,CAAM,iBAAiB;MAEvDA,KAAA,IAEqBE,QAAA,CAASF,KAAA,CAAM,CAAC,GAAG,EAAE,IAGvB,OAEfV,QAAA,GAAW;IAGvB;IACI,IAAAK,QAAA,CAASQ,OAAA,CAAQJ,MAAA,EACrB;MACI,MAAMC,KAAA,GAAST,SAAA,CAAUU,SAAA,CAAWD,KAAA,CAAM,oBAAoB;MAE1DA,KAAA,IAEqBE,QAAA,CAASF,KAAA,CAAM,CAAC,GAAG,EAAE,IAGvB,MAEfV,QAAA,GAAW;IAGvB;EACJ;EAEA,OAAOA,QAAA,GAAWD,GAAA,GAAM;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}