{"ast": null, "code": "import { PI_2 } from \"./const.mjs\";\nimport { Point } from \"./Point.mjs\";\nclass Matrix {\n  /**\n   * @param a - x scale\n   * @param b - y skew\n   * @param c - x skew\n   * @param d - y scale\n   * @param tx - x translation\n   * @param ty - y translation\n   */\n  constructor(a = 1, b = 0, c = 0, d = 1, tx = 0, ty = 0) {\n    this.array = null, this.a = a, this.b = b, this.c = c, this.d = d, this.tx = tx, this.ty = ty;\n  }\n  /**\n   * Creates a Matrix object based on the given array. The Element to Matrix mapping order is as follows:\n   *\n   * a = array[0]\n   * b = array[1]\n   * c = array[3]\n   * d = array[4]\n   * tx = array[2]\n   * ty = array[5]\n   * @param array - The array that the matrix will be populated from.\n   */\n  fromArray(array) {\n    this.a = array[0], this.b = array[1], this.c = array[3], this.d = array[4], this.tx = array[2], this.ty = array[5];\n  }\n  /**\n   * Sets the matrix properties.\n   * @param a - Matrix component\n   * @param b - Matrix component\n   * @param c - Matrix component\n   * @param d - Matrix component\n   * @param tx - Matrix component\n   * @param ty - Matrix component\n   * @returns This matrix. Good for chaining method calls.\n   */\n  set(a, b, c, d, tx, ty) {\n    return this.a = a, this.b = b, this.c = c, this.d = d, this.tx = tx, this.ty = ty, this;\n  }\n  /**\n   * Creates an array from the current Matrix object.\n   * @param transpose - Whether we need to transpose the matrix or not\n   * @param [out=new Float32Array(9)] - If provided the array will be assigned to out\n   * @returns The newly created array which contains the matrix\n   */\n  toArray(transpose, out) {\n    this.array || (this.array = new Float32Array(9));\n    const array = out || this.array;\n    return transpose ? (array[0] = this.a, array[1] = this.b, array[2] = 0, array[3] = this.c, array[4] = this.d, array[5] = 0, array[6] = this.tx, array[7] = this.ty, array[8] = 1) : (array[0] = this.a, array[1] = this.c, array[2] = this.tx, array[3] = this.b, array[4] = this.d, array[5] = this.ty, array[6] = 0, array[7] = 0, array[8] = 1), array;\n  }\n  /**\n   * Get a new position with the current transformation applied.\n   * Can be used to go from a child's coordinate space to the world coordinate space. (e.g. rendering)\n   * @param pos - The origin\n   * @param {PIXI.Point} [newPos] - The point that the new position is assigned to (allowed to be same as input)\n   * @returns {PIXI.Point} The new point, transformed through this matrix\n   */\n  apply(pos, newPos) {\n    newPos = newPos || new Point();\n    const x = pos.x,\n      y = pos.y;\n    return newPos.x = this.a * x + this.c * y + this.tx, newPos.y = this.b * x + this.d * y + this.ty, newPos;\n  }\n  /**\n   * Get a new position with the inverse of the current transformation applied.\n   * Can be used to go from the world coordinate space to a child's coordinate space. (e.g. input)\n   * @param pos - The origin\n   * @param {PIXI.Point} [newPos] - The point that the new position is assigned to (allowed to be same as input)\n   * @returns {PIXI.Point} The new point, inverse-transformed through this matrix\n   */\n  applyInverse(pos, newPos) {\n    newPos = newPos || new Point();\n    const id = 1 / (this.a * this.d + this.c * -this.b),\n      x = pos.x,\n      y = pos.y;\n    return newPos.x = this.d * id * x + -this.c * id * y + (this.ty * this.c - this.tx * this.d) * id, newPos.y = this.a * id * y + -this.b * id * x + (-this.ty * this.a + this.tx * this.b) * id, newPos;\n  }\n  /**\n   * Translates the matrix on the x and y.\n   * @param x - How much to translate x by\n   * @param y - How much to translate y by\n   * @returns This matrix. Good for chaining method calls.\n   */\n  translate(x, y) {\n    return this.tx += x, this.ty += y, this;\n  }\n  /**\n   * Applies a scale transformation to the matrix.\n   * @param x - The amount to scale horizontally\n   * @param y - The amount to scale vertically\n   * @returns This matrix. Good for chaining method calls.\n   */\n  scale(x, y) {\n    return this.a *= x, this.d *= y, this.c *= x, this.b *= y, this.tx *= x, this.ty *= y, this;\n  }\n  /**\n   * Applies a rotation transformation to the matrix.\n   * @param angle - The angle in radians.\n   * @returns This matrix. Good for chaining method calls.\n   */\n  rotate(angle) {\n    const cos = Math.cos(angle),\n      sin = Math.sin(angle),\n      a1 = this.a,\n      c1 = this.c,\n      tx1 = this.tx;\n    return this.a = a1 * cos - this.b * sin, this.b = a1 * sin + this.b * cos, this.c = c1 * cos - this.d * sin, this.d = c1 * sin + this.d * cos, this.tx = tx1 * cos - this.ty * sin, this.ty = tx1 * sin + this.ty * cos, this;\n  }\n  /**\n   * Appends the given Matrix to this Matrix.\n   * @param matrix - The matrix to append.\n   * @returns This matrix. Good for chaining method calls.\n   */\n  append(matrix) {\n    const a1 = this.a,\n      b1 = this.b,\n      c1 = this.c,\n      d1 = this.d;\n    return this.a = matrix.a * a1 + matrix.b * c1, this.b = matrix.a * b1 + matrix.b * d1, this.c = matrix.c * a1 + matrix.d * c1, this.d = matrix.c * b1 + matrix.d * d1, this.tx = matrix.tx * a1 + matrix.ty * c1 + this.tx, this.ty = matrix.tx * b1 + matrix.ty * d1 + this.ty, this;\n  }\n  /**\n   * Sets the matrix based on all the available properties\n   * @param x - Position on the x axis\n   * @param y - Position on the y axis\n   * @param pivotX - Pivot on the x axis\n   * @param pivotY - Pivot on the y axis\n   * @param scaleX - Scale on the x axis\n   * @param scaleY - Scale on the y axis\n   * @param rotation - Rotation in radians\n   * @param skewX - Skew on the x axis\n   * @param skewY - Skew on the y axis\n   * @returns This matrix. Good for chaining method calls.\n   */\n  setTransform(x, y, pivotX, pivotY, scaleX, scaleY, rotation, skewX, skewY) {\n    return this.a = Math.cos(rotation + skewY) * scaleX, this.b = Math.sin(rotation + skewY) * scaleX, this.c = -Math.sin(rotation - skewX) * scaleY, this.d = Math.cos(rotation - skewX) * scaleY, this.tx = x - (pivotX * this.a + pivotY * this.c), this.ty = y - (pivotX * this.b + pivotY * this.d), this;\n  }\n  /**\n   * Prepends the given Matrix to this Matrix.\n   * @param matrix - The matrix to prepend\n   * @returns This matrix. Good for chaining method calls.\n   */\n  prepend(matrix) {\n    const tx1 = this.tx;\n    if (matrix.a !== 1 || matrix.b !== 0 || matrix.c !== 0 || matrix.d !== 1) {\n      const a1 = this.a,\n        c1 = this.c;\n      this.a = a1 * matrix.a + this.b * matrix.c, this.b = a1 * matrix.b + this.b * matrix.d, this.c = c1 * matrix.a + this.d * matrix.c, this.d = c1 * matrix.b + this.d * matrix.d;\n    }\n    return this.tx = tx1 * matrix.a + this.ty * matrix.c + matrix.tx, this.ty = tx1 * matrix.b + this.ty * matrix.d + matrix.ty, this;\n  }\n  /**\n   * Decomposes the matrix (x, y, scaleX, scaleY, and rotation) and sets the properties on to a transform.\n   * @param transform - The transform to apply the properties to.\n   * @returns The transform with the newly applied properties\n   */\n  decompose(transform) {\n    const a = this.a,\n      b = this.b,\n      c = this.c,\n      d = this.d,\n      pivot = transform.pivot,\n      skewX = -Math.atan2(-c, d),\n      skewY = Math.atan2(b, a),\n      delta = Math.abs(skewX + skewY);\n    return delta < 1e-5 || Math.abs(PI_2 - delta) < 1e-5 ? (transform.rotation = skewY, transform.skew.x = transform.skew.y = 0) : (transform.rotation = 0, transform.skew.x = skewX, transform.skew.y = skewY), transform.scale.x = Math.sqrt(a * a + b * b), transform.scale.y = Math.sqrt(c * c + d * d), transform.position.x = this.tx + (pivot.x * a + pivot.y * c), transform.position.y = this.ty + (pivot.x * b + pivot.y * d), transform;\n  }\n  /**\n   * Inverts this matrix\n   * @returns This matrix. Good for chaining method calls.\n   */\n  invert() {\n    const a1 = this.a,\n      b1 = this.b,\n      c1 = this.c,\n      d1 = this.d,\n      tx1 = this.tx,\n      n = a1 * d1 - b1 * c1;\n    return this.a = d1 / n, this.b = -b1 / n, this.c = -c1 / n, this.d = a1 / n, this.tx = (c1 * this.ty - d1 * tx1) / n, this.ty = -(a1 * this.ty - b1 * tx1) / n, this;\n  }\n  /**\n   * Resets this Matrix to an identity (default) matrix.\n   * @returns This matrix. Good for chaining method calls.\n   */\n  identity() {\n    return this.a = 1, this.b = 0, this.c = 0, this.d = 1, this.tx = 0, this.ty = 0, this;\n  }\n  /**\n   * Creates a new Matrix object with the same values as this one.\n   * @returns A copy of this matrix. Good for chaining method calls.\n   */\n  clone() {\n    const matrix = new Matrix();\n    return matrix.a = this.a, matrix.b = this.b, matrix.c = this.c, matrix.d = this.d, matrix.tx = this.tx, matrix.ty = this.ty, matrix;\n  }\n  /**\n   * Changes the values of the given matrix to be the same as the ones in this matrix\n   * @param matrix - The matrix to copy to.\n   * @returns The matrix given in parameter with its values updated.\n   */\n  copyTo(matrix) {\n    return matrix.a = this.a, matrix.b = this.b, matrix.c = this.c, matrix.d = this.d, matrix.tx = this.tx, matrix.ty = this.ty, matrix;\n  }\n  /**\n   * Changes the values of the matrix to be the same as the ones in given matrix\n   * @param {PIXI.Matrix} matrix - The matrix to copy from.\n   * @returns {PIXI.Matrix} this\n   */\n  copyFrom(matrix) {\n    return this.a = matrix.a, this.b = matrix.b, this.c = matrix.c, this.d = matrix.d, this.tx = matrix.tx, this.ty = matrix.ty, this;\n  }\n  /**\n   * A default (identity) matrix\n   * @readonly\n   */\n  static get IDENTITY() {\n    return new Matrix();\n  }\n  /**\n   * A temp matrix\n   * @readonly\n   */\n  static get TEMP_MATRIX() {\n    return new Matrix();\n  }\n}\nMatrix.prototype.toString = function () {\n  return `[@pixi/math:Matrix a=${this.a} b=${this.b} c=${this.c} d=${this.d} tx=${this.tx} ty=${this.ty}]`;\n};\nexport { Matrix };", "map": {"version": 3, "names": ["Matrix", "constructor", "a", "b", "c", "d", "tx", "ty", "array", "fromArray", "set", "toArray", "transpose", "out", "Float32Array", "apply", "pos", "newPos", "Point", "x", "y", "applyInverse", "id", "translate", "scale", "rotate", "angle", "cos", "Math", "sin", "a1", "c1", "tx1", "append", "matrix", "b1", "d1", "setTransform", "pivotX", "pivotY", "scaleX", "scaleY", "rotation", "skewX", "skewY", "prepend", "decompose", "transform", "pivot", "atan2", "delta", "abs", "PI_2", "skew", "sqrt", "position", "invert", "n", "identity", "clone", "copyTo", "copyFrom", "IDENTITY", "TEMP_MATRIX", "prototype", "toString"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\math\\src\\Matrix.ts"], "sourcesContent": ["import { PI_2 } from './const';\nimport { Point } from './Point';\n\nimport type { IPointData } from './IPointData';\nimport type { Transform } from './Transform';\n\n/**\n * The PixiJS Matrix as a class makes it a lot faster.\n *\n * Here is a representation of it:\n * ```\n * | a | c | tx|\n * | b | d | ty|\n * | 0 | 0 | 1 |\n * ```\n * @memberof PIXI\n */\nexport class Matrix\n{\n    /** @default 1 */\n    public a: number;\n\n    /** @default 0 */\n    public b: number;\n\n    /** @default 0 */\n    public c: number;\n\n    /** @default 1 */\n    public d: number;\n\n    /** @default 0 */\n    public tx: number;\n\n    /** @default 0 */\n    public ty: number;\n\n    public array: Float32Array | null = null;\n\n    /**\n     * @param a - x scale\n     * @param b - y skew\n     * @param c - x skew\n     * @param d - y scale\n     * @param tx - x translation\n     * @param ty - y translation\n     */\n    constructor(a = 1, b = 0, c = 0, d = 1, tx = 0, ty = 0)\n    {\n        this.a = a;\n        this.b = b;\n        this.c = c;\n        this.d = d;\n        this.tx = tx;\n        this.ty = ty;\n    }\n\n    /**\n     * Creates a Matrix object based on the given array. The Element to Matrix mapping order is as follows:\n     *\n     * a = array[0]\n     * b = array[1]\n     * c = array[3]\n     * d = array[4]\n     * tx = array[2]\n     * ty = array[5]\n     * @param array - The array that the matrix will be populated from.\n     */\n    fromArray(array: number[]): void\n    {\n        this.a = array[0];\n        this.b = array[1];\n        this.c = array[3];\n        this.d = array[4];\n        this.tx = array[2];\n        this.ty = array[5];\n    }\n\n    /**\n     * Sets the matrix properties.\n     * @param a - Matrix component\n     * @param b - Matrix component\n     * @param c - Matrix component\n     * @param d - Matrix component\n     * @param tx - Matrix component\n     * @param ty - Matrix component\n     * @returns This matrix. Good for chaining method calls.\n     */\n    set(a: number, b: number, c: number, d: number, tx: number, ty: number): this\n    {\n        this.a = a;\n        this.b = b;\n        this.c = c;\n        this.d = d;\n        this.tx = tx;\n        this.ty = ty;\n\n        return this;\n    }\n\n    /**\n     * Creates an array from the current Matrix object.\n     * @param transpose - Whether we need to transpose the matrix or not\n     * @param [out=new Float32Array(9)] - If provided the array will be assigned to out\n     * @returns The newly created array which contains the matrix\n     */\n    toArray(transpose: boolean, out?: Float32Array): Float32Array\n    {\n        if (!this.array)\n        {\n            this.array = new Float32Array(9);\n        }\n\n        const array = out || this.array;\n\n        if (transpose)\n        {\n            array[0] = this.a;\n            array[1] = this.b;\n            array[2] = 0;\n            array[3] = this.c;\n            array[4] = this.d;\n            array[5] = 0;\n            array[6] = this.tx;\n            array[7] = this.ty;\n            array[8] = 1;\n        }\n        else\n        {\n            array[0] = this.a;\n            array[1] = this.c;\n            array[2] = this.tx;\n            array[3] = this.b;\n            array[4] = this.d;\n            array[5] = this.ty;\n            array[6] = 0;\n            array[7] = 0;\n            array[8] = 1;\n        }\n\n        return array;\n    }\n\n    /**\n     * Get a new position with the current transformation applied.\n     * Can be used to go from a child's coordinate space to the world coordinate space. (e.g. rendering)\n     * @param pos - The origin\n     * @param {PIXI.Point} [newPos] - The point that the new position is assigned to (allowed to be same as input)\n     * @returns {PIXI.Point} The new point, transformed through this matrix\n     */\n    apply<P extends IPointData = Point>(pos: IPointData, newPos?: P): P\n    {\n        newPos = (newPos || new Point()) as P;\n\n        const x = pos.x;\n        const y = pos.y;\n\n        newPos.x = (this.a * x) + (this.c * y) + this.tx;\n        newPos.y = (this.b * x) + (this.d * y) + this.ty;\n\n        return newPos;\n    }\n\n    /**\n     * Get a new position with the inverse of the current transformation applied.\n     * Can be used to go from the world coordinate space to a child's coordinate space. (e.g. input)\n     * @param pos - The origin\n     * @param {PIXI.Point} [newPos] - The point that the new position is assigned to (allowed to be same as input)\n     * @returns {PIXI.Point} The new point, inverse-transformed through this matrix\n     */\n    applyInverse<P extends IPointData = Point>(pos: IPointData, newPos?: P): P\n    {\n        newPos = (newPos || new Point()) as P;\n\n        const id = 1 / ((this.a * this.d) + (this.c * -this.b));\n\n        const x = pos.x;\n        const y = pos.y;\n\n        newPos.x = (this.d * id * x) + (-this.c * id * y) + (((this.ty * this.c) - (this.tx * this.d)) * id);\n        newPos.y = (this.a * id * y) + (-this.b * id * x) + (((-this.ty * this.a) + (this.tx * this.b)) * id);\n\n        return newPos;\n    }\n\n    /**\n     * Translates the matrix on the x and y.\n     * @param x - How much to translate x by\n     * @param y - How much to translate y by\n     * @returns This matrix. Good for chaining method calls.\n     */\n    translate(x: number, y: number): this\n    {\n        this.tx += x;\n        this.ty += y;\n\n        return this;\n    }\n\n    /**\n     * Applies a scale transformation to the matrix.\n     * @param x - The amount to scale horizontally\n     * @param y - The amount to scale vertically\n     * @returns This matrix. Good for chaining method calls.\n     */\n    scale(x: number, y: number): this\n    {\n        this.a *= x;\n        this.d *= y;\n        this.c *= x;\n        this.b *= y;\n        this.tx *= x;\n        this.ty *= y;\n\n        return this;\n    }\n\n    /**\n     * Applies a rotation transformation to the matrix.\n     * @param angle - The angle in radians.\n     * @returns This matrix. Good for chaining method calls.\n     */\n    rotate(angle: number): this\n    {\n        const cos = Math.cos(angle);\n        const sin = Math.sin(angle);\n\n        const a1 = this.a;\n        const c1 = this.c;\n        const tx1 = this.tx;\n\n        this.a = (a1 * cos) - (this.b * sin);\n        this.b = (a1 * sin) + (this.b * cos);\n        this.c = (c1 * cos) - (this.d * sin);\n        this.d = (c1 * sin) + (this.d * cos);\n        this.tx = (tx1 * cos) - (this.ty * sin);\n        this.ty = (tx1 * sin) + (this.ty * cos);\n\n        return this;\n    }\n\n    /**\n     * Appends the given Matrix to this Matrix.\n     * @param matrix - The matrix to append.\n     * @returns This matrix. Good for chaining method calls.\n     */\n    append(matrix: Matrix): this\n    {\n        const a1 = this.a;\n        const b1 = this.b;\n        const c1 = this.c;\n        const d1 = this.d;\n\n        this.a = (matrix.a * a1) + (matrix.b * c1);\n        this.b = (matrix.a * b1) + (matrix.b * d1);\n        this.c = (matrix.c * a1) + (matrix.d * c1);\n        this.d = (matrix.c * b1) + (matrix.d * d1);\n\n        this.tx = (matrix.tx * a1) + (matrix.ty * c1) + this.tx;\n        this.ty = (matrix.tx * b1) + (matrix.ty * d1) + this.ty;\n\n        return this;\n    }\n\n    /**\n     * Sets the matrix based on all the available properties\n     * @param x - Position on the x axis\n     * @param y - Position on the y axis\n     * @param pivotX - Pivot on the x axis\n     * @param pivotY - Pivot on the y axis\n     * @param scaleX - Scale on the x axis\n     * @param scaleY - Scale on the y axis\n     * @param rotation - Rotation in radians\n     * @param skewX - Skew on the x axis\n     * @param skewY - Skew on the y axis\n     * @returns This matrix. Good for chaining method calls.\n     */\n    setTransform(x: number, y: number, pivotX: number, pivotY: number, scaleX: number,\n        scaleY: number, rotation: number, skewX: number, skewY: number): this\n    {\n        this.a = Math.cos(rotation + skewY) * scaleX;\n        this.b = Math.sin(rotation + skewY) * scaleX;\n        this.c = -Math.sin(rotation - skewX) * scaleY;\n        this.d = Math.cos(rotation - skewX) * scaleY;\n\n        this.tx = x - ((pivotX * this.a) + (pivotY * this.c));\n        this.ty = y - ((pivotX * this.b) + (pivotY * this.d));\n\n        return this;\n    }\n\n    /**\n     * Prepends the given Matrix to this Matrix.\n     * @param matrix - The matrix to prepend\n     * @returns This matrix. Good for chaining method calls.\n     */\n    prepend(matrix: Matrix): this\n    {\n        const tx1 = this.tx;\n\n        if (matrix.a !== 1 || matrix.b !== 0 || matrix.c !== 0 || matrix.d !== 1)\n        {\n            const a1 = this.a;\n            const c1 = this.c;\n\n            this.a = (a1 * matrix.a) + (this.b * matrix.c);\n            this.b = (a1 * matrix.b) + (this.b * matrix.d);\n            this.c = (c1 * matrix.a) + (this.d * matrix.c);\n            this.d = (c1 * matrix.b) + (this.d * matrix.d);\n        }\n\n        this.tx = (tx1 * matrix.a) + (this.ty * matrix.c) + matrix.tx;\n        this.ty = (tx1 * matrix.b) + (this.ty * matrix.d) + matrix.ty;\n\n        return this;\n    }\n\n    /**\n     * Decomposes the matrix (x, y, scaleX, scaleY, and rotation) and sets the properties on to a transform.\n     * @param transform - The transform to apply the properties to.\n     * @returns The transform with the newly applied properties\n     */\n    decompose(transform: Transform): Transform\n    {\n        // sort out rotation / skew..\n        const a = this.a;\n        const b = this.b;\n        const c = this.c;\n        const d = this.d;\n        const pivot = transform.pivot;\n\n        const skewX = -Math.atan2(-c, d);\n        const skewY = Math.atan2(b, a);\n\n        const delta = Math.abs(skewX + skewY);\n\n        if (delta < 0.00001 || Math.abs(PI_2 - delta) < 0.00001)\n        {\n            transform.rotation = skewY;\n            transform.skew.x = transform.skew.y = 0;\n        }\n        else\n        {\n            transform.rotation = 0;\n            transform.skew.x = skewX;\n            transform.skew.y = skewY;\n        }\n\n        // next set scale\n        transform.scale.x = Math.sqrt((a * a) + (b * b));\n        transform.scale.y = Math.sqrt((c * c) + (d * d));\n\n        // next set position\n        transform.position.x = this.tx + ((pivot.x * a) + (pivot.y * c));\n        transform.position.y = this.ty + ((pivot.x * b) + (pivot.y * d));\n\n        return transform;\n    }\n\n    /**\n     * Inverts this matrix\n     * @returns This matrix. Good for chaining method calls.\n     */\n    invert(): this\n    {\n        const a1 = this.a;\n        const b1 = this.b;\n        const c1 = this.c;\n        const d1 = this.d;\n        const tx1 = this.tx;\n        const n = (a1 * d1) - (b1 * c1);\n\n        this.a = d1 / n;\n        this.b = -b1 / n;\n        this.c = -c1 / n;\n        this.d = a1 / n;\n        this.tx = ((c1 * this.ty) - (d1 * tx1)) / n;\n        this.ty = -((a1 * this.ty) - (b1 * tx1)) / n;\n\n        return this;\n    }\n\n    /**\n     * Resets this Matrix to an identity (default) matrix.\n     * @returns This matrix. Good for chaining method calls.\n     */\n    identity(): this\n    {\n        this.a = 1;\n        this.b = 0;\n        this.c = 0;\n        this.d = 1;\n        this.tx = 0;\n        this.ty = 0;\n\n        return this;\n    }\n\n    /**\n     * Creates a new Matrix object with the same values as this one.\n     * @returns A copy of this matrix. Good for chaining method calls.\n     */\n    clone(): Matrix\n    {\n        const matrix = new Matrix();\n\n        matrix.a = this.a;\n        matrix.b = this.b;\n        matrix.c = this.c;\n        matrix.d = this.d;\n        matrix.tx = this.tx;\n        matrix.ty = this.ty;\n\n        return matrix;\n    }\n\n    /**\n     * Changes the values of the given matrix to be the same as the ones in this matrix\n     * @param matrix - The matrix to copy to.\n     * @returns The matrix given in parameter with its values updated.\n     */\n    copyTo(matrix: Matrix): Matrix\n    {\n        matrix.a = this.a;\n        matrix.b = this.b;\n        matrix.c = this.c;\n        matrix.d = this.d;\n        matrix.tx = this.tx;\n        matrix.ty = this.ty;\n\n        return matrix;\n    }\n\n    /**\n     * Changes the values of the matrix to be the same as the ones in given matrix\n     * @param {PIXI.Matrix} matrix - The matrix to copy from.\n     * @returns {PIXI.Matrix} this\n     */\n    copyFrom(matrix: Matrix): this\n    {\n        this.a = matrix.a;\n        this.b = matrix.b;\n        this.c = matrix.c;\n        this.d = matrix.d;\n        this.tx = matrix.tx;\n        this.ty = matrix.ty;\n\n        return this;\n    }\n\n    /**\n     * A default (identity) matrix\n     * @readonly\n     */\n    static get IDENTITY(): Matrix\n    {\n        return new Matrix();\n    }\n\n    /**\n     * A temp matrix\n     * @readonly\n     */\n    static get TEMP_MATRIX(): Matrix\n    {\n        return new Matrix();\n    }\n}\n\nif (process.env.DEBUG)\n{\n    Matrix.prototype.toString = function toString(): string\n    {\n        return `[@pixi/math:Matrix a=${this.a} b=${this.b} c=${this.c} d=${this.d} tx=${this.tx} ty=${this.ty}]`;\n    };\n}\n"], "mappings": ";;AAiBO,MAAMA,MAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EA6BIC,YAAYC,CAAA,GAAI,GAAGC,CAAA,GAAI,GAAGC,CAAA,GAAI,GAAGC,CAAA,GAAI,GAAGC,EAAA,GAAK,GAAGC,EAAA,GAAK,GACrD;IAXA,KAAOC,KAAA,GAA6B,MAYhC,KAAKN,CAAA,GAAIA,CAAA,EACT,KAAKC,CAAA,GAAIA,CAAA,EACT,KAAKC,CAAA,GAAIA,CAAA,EACT,KAAKC,CAAA,GAAIA,CAAA,EACT,KAAKC,EAAA,GAAKA,EAAA,EACV,KAAKC,EAAA,GAAKA,EAAA;EACd;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAaAE,UAAUD,KAAA,EACV;IACS,KAAAN,CAAA,GAAIM,KAAA,CAAM,CAAC,GAChB,KAAKL,CAAA,GAAIK,KAAA,CAAM,CAAC,GAChB,KAAKJ,CAAA,GAAII,KAAA,CAAM,CAAC,GAChB,KAAKH,CAAA,GAAIG,KAAA,CAAM,CAAC,GAChB,KAAKF,EAAA,GAAKE,KAAA,CAAM,CAAC,GACjB,KAAKD,EAAA,GAAKC,KAAA,CAAM,CAAC;EACrB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYAE,IAAIR,CAAA,EAAWC,CAAA,EAAWC,CAAA,EAAWC,CAAA,EAAWC,EAAA,EAAYC,EAAA,EAC5D;IACI,YAAKL,CAAA,GAAIA,CAAA,EACT,KAAKC,CAAA,GAAIA,CAAA,EACT,KAAKC,CAAA,GAAIA,CAAA,EACT,KAAKC,CAAA,GAAIA,CAAA,EACT,KAAKC,EAAA,GAAKA,EAAA,EACV,KAAKC,EAAA,GAAKA,EAAA,EAEH;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAI,QAAQC,SAAA,EAAoBC,GAAA,EAC5B;IACS,KAAKL,KAAA,KAEN,KAAKA,KAAA,GAAQ,IAAIM,YAAA,CAAa,CAAC;IAG7B,MAAAN,KAAA,GAAQK,GAAA,IAAO,KAAKL,KAAA;IAE1B,OAAII,SAAA,IAEAJ,KAAA,CAAM,CAAC,IAAI,KAAKN,CAAA,EAChBM,KAAA,CAAM,CAAC,IAAI,KAAKL,CAAA,EAChBK,KAAA,CAAM,CAAC,IAAI,GACXA,KAAA,CAAM,CAAC,IAAI,KAAKJ,CAAA,EAChBI,KAAA,CAAM,CAAC,IAAI,KAAKH,CAAA,EAChBG,KAAA,CAAM,CAAC,IAAI,GACXA,KAAA,CAAM,CAAC,IAAI,KAAKF,EAAA,EAChBE,KAAA,CAAM,CAAC,IAAI,KAAKD,EAAA,EAChBC,KAAA,CAAM,CAAC,IAAI,MAIXA,KAAA,CAAM,CAAC,IAAI,KAAKN,CAAA,EAChBM,KAAA,CAAM,CAAC,IAAI,KAAKJ,CAAA,EAChBI,KAAA,CAAM,CAAC,IAAI,KAAKF,EAAA,EAChBE,KAAA,CAAM,CAAC,IAAI,KAAKL,CAAA,EAChBK,KAAA,CAAM,CAAC,IAAI,KAAKH,CAAA,EAChBG,KAAA,CAAM,CAAC,IAAI,KAAKD,EAAA,EAChBC,KAAA,CAAM,CAAC,IAAI,GACXA,KAAA,CAAM,CAAC,IAAI,GACXA,KAAA,CAAM,CAAC,IAAI,IAGRA,KAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAO,MAAoCC,GAAA,EAAiBC,MAAA,EACrD;IACcA,MAAA,GAAAA,MAAA,IAAU,IAAIC,KAAA;IAExB,MAAMC,CAAA,GAAIH,GAAA,CAAIG,CAAA;MACRC,CAAA,GAAIJ,GAAA,CAAII,CAAA;IAEd,OAAAH,MAAA,CAAOE,CAAA,GAAK,KAAKjB,CAAA,GAAIiB,CAAA,GAAM,KAAKf,CAAA,GAAIgB,CAAA,GAAK,KAAKd,EAAA,EAC9CW,MAAA,CAAOG,CAAA,GAAK,KAAKjB,CAAA,GAAIgB,CAAA,GAAM,KAAKd,CAAA,GAAIe,CAAA,GAAK,KAAKb,EAAA,EAEvCU,MAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAI,aAA2CL,GAAA,EAAiBC,MAAA,EAC5D;IACcA,MAAA,GAAAA,MAAA,IAAU,IAAIC,KAAA;IAExB,MAAMI,EAAA,GAAK,KAAM,KAAKpB,CAAA,GAAI,KAAKG,CAAA,GAAM,KAAKD,CAAA,GAAI,CAAC,KAAKD,CAAA;MAE9CgB,CAAA,GAAIH,GAAA,CAAIG,CAAA;MACRC,CAAA,GAAIJ,GAAA,CAAII,CAAA;IAEd,OAAAH,MAAA,CAAOE,CAAA,GAAK,KAAKd,CAAA,GAAIiB,EAAA,GAAKH,CAAA,GAAM,CAAC,KAAKf,CAAA,GAAIkB,EAAA,GAAKF,CAAA,IAAQ,KAAKb,EAAA,GAAK,KAAKH,CAAA,GAAM,KAAKE,EAAA,GAAK,KAAKD,CAAA,IAAMiB,EAAA,EACjGL,MAAA,CAAOG,CAAA,GAAK,KAAKlB,CAAA,GAAIoB,EAAA,GAAKF,CAAA,GAAM,CAAC,KAAKjB,CAAA,GAAImB,EAAA,GAAKH,CAAA,IAAQ,CAAC,KAAKZ,EAAA,GAAK,KAAKL,CAAA,GAAM,KAAKI,EAAA,GAAK,KAAKH,CAAA,IAAMmB,EAAA,EAE3FL,MAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAM,UAAUJ,CAAA,EAAWC,CAAA,EACrB;IACI,YAAKd,EAAA,IAAMa,CAAA,EACX,KAAKZ,EAAA,IAAMa,CAAA,EAEJ;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAI,MAAML,CAAA,EAAWC,CAAA,EACjB;IACI,YAAKlB,CAAA,IAAKiB,CAAA,EACV,KAAKd,CAAA,IAAKe,CAAA,EACV,KAAKhB,CAAA,IAAKe,CAAA,EACV,KAAKhB,CAAA,IAAKiB,CAAA,EACV,KAAKd,EAAA,IAAMa,CAAA,EACX,KAAKZ,EAAA,IAAMa,CAAA,EAEJ;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAK,OAAOC,KAAA,EACP;IACI,MAAMC,GAAA,GAAMC,IAAA,CAAKD,GAAA,CAAID,KAAK;MACpBG,GAAA,GAAMD,IAAA,CAAKC,GAAA,CAAIH,KAAK;MAEpBI,EAAA,GAAK,KAAK5B,CAAA;MACV6B,EAAA,GAAK,KAAK3B,CAAA;MACV4B,GAAA,GAAM,KAAK1B,EAAA;IAEjB,YAAKJ,CAAA,GAAK4B,EAAA,GAAKH,GAAA,GAAQ,KAAKxB,CAAA,GAAI0B,GAAA,EAChC,KAAK1B,CAAA,GAAK2B,EAAA,GAAKD,GAAA,GAAQ,KAAK1B,CAAA,GAAIwB,GAAA,EAChC,KAAKvB,CAAA,GAAK2B,EAAA,GAAKJ,GAAA,GAAQ,KAAKtB,CAAA,GAAIwB,GAAA,EAChC,KAAKxB,CAAA,GAAK0B,EAAA,GAAKF,GAAA,GAAQ,KAAKxB,CAAA,GAAIsB,GAAA,EAChC,KAAKrB,EAAA,GAAM0B,GAAA,GAAML,GAAA,GAAQ,KAAKpB,EAAA,GAAKsB,GAAA,EACnC,KAAKtB,EAAA,GAAMyB,GAAA,GAAMH,GAAA,GAAQ,KAAKtB,EAAA,GAAKoB,GAAA,EAE5B;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAM,OAAOC,MAAA,EACP;IACU,MAAAJ,EAAA,GAAK,KAAK5B,CAAA;MACViC,EAAA,GAAK,KAAKhC,CAAA;MACV4B,EAAA,GAAK,KAAK3B,CAAA;MACVgC,EAAA,GAAK,KAAK/B,CAAA;IAEX,YAAAH,CAAA,GAAKgC,MAAA,CAAOhC,CAAA,GAAI4B,EAAA,GAAOI,MAAA,CAAO/B,CAAA,GAAI4B,EAAA,EACvC,KAAK5B,CAAA,GAAK+B,MAAA,CAAOhC,CAAA,GAAIiC,EAAA,GAAOD,MAAA,CAAO/B,CAAA,GAAIiC,EAAA,EACvC,KAAKhC,CAAA,GAAK8B,MAAA,CAAO9B,CAAA,GAAI0B,EAAA,GAAOI,MAAA,CAAO7B,CAAA,GAAI0B,EAAA,EACvC,KAAK1B,CAAA,GAAK6B,MAAA,CAAO9B,CAAA,GAAI+B,EAAA,GAAOD,MAAA,CAAO7B,CAAA,GAAI+B,EAAA,EAEvC,KAAK9B,EAAA,GAAM4B,MAAA,CAAO5B,EAAA,GAAKwB,EAAA,GAAOI,MAAA,CAAO3B,EAAA,GAAKwB,EAAA,GAAM,KAAKzB,EAAA,EACrD,KAAKC,EAAA,GAAM2B,MAAA,CAAO5B,EAAA,GAAK6B,EAAA,GAAOD,MAAA,CAAO3B,EAAA,GAAK6B,EAAA,GAAM,KAAK7B,EAAA,EAE9C;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAeA8B,aAAalB,CAAA,EAAWC,CAAA,EAAWkB,MAAA,EAAgBC,MAAA,EAAgBC,MAAA,EAC/DC,MAAA,EAAgBC,QAAA,EAAkBC,KAAA,EAAeC,KAAA,EACrD;IACS,YAAA1C,CAAA,GAAI0B,IAAA,CAAKD,GAAA,CAAIe,QAAA,GAAWE,KAAK,IAAIJ,MAAA,EACtC,KAAKrC,CAAA,GAAIyB,IAAA,CAAKC,GAAA,CAAIa,QAAA,GAAWE,KAAK,IAAIJ,MAAA,EACtC,KAAKpC,CAAA,GAAI,CAACwB,IAAA,CAAKC,GAAA,CAAIa,QAAA,GAAWC,KAAK,IAAIF,MAAA,EACvC,KAAKpC,CAAA,GAAIuB,IAAA,CAAKD,GAAA,CAAIe,QAAA,GAAWC,KAAK,IAAIF,MAAA,EAEtC,KAAKnC,EAAA,GAAKa,CAAA,IAAMmB,MAAA,GAAS,KAAKpC,CAAA,GAAMqC,MAAA,GAAS,KAAKnC,CAAA,GAClD,KAAKG,EAAA,GAAKa,CAAA,IAAMkB,MAAA,GAAS,KAAKnC,CAAA,GAAMoC,MAAA,GAAS,KAAKlC,CAAA,GAE3C;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAwC,QAAQX,MAAA,EACR;IACI,MAAMF,GAAA,GAAM,KAAK1B,EAAA;IAEb,IAAA4B,MAAA,CAAOhC,CAAA,KAAM,KAAKgC,MAAA,CAAO/B,CAAA,KAAM,KAAK+B,MAAA,CAAO9B,CAAA,KAAM,KAAK8B,MAAA,CAAO7B,CAAA,KAAM,GACvE;MACI,MAAMyB,EAAA,GAAK,KAAK5B,CAAA;QACV6B,EAAA,GAAK,KAAK3B,CAAA;MAEhB,KAAKF,CAAA,GAAK4B,EAAA,GAAKI,MAAA,CAAOhC,CAAA,GAAM,KAAKC,CAAA,GAAI+B,MAAA,CAAO9B,CAAA,EAC5C,KAAKD,CAAA,GAAK2B,EAAA,GAAKI,MAAA,CAAO/B,CAAA,GAAM,KAAKA,CAAA,GAAI+B,MAAA,CAAO7B,CAAA,EAC5C,KAAKD,CAAA,GAAK2B,EAAA,GAAKG,MAAA,CAAOhC,CAAA,GAAM,KAAKG,CAAA,GAAI6B,MAAA,CAAO9B,CAAA,EAC5C,KAAKC,CAAA,GAAK0B,EAAA,GAAKG,MAAA,CAAO/B,CAAA,GAAM,KAAKE,CAAA,GAAI6B,MAAA,CAAO7B,CAAA;IAChD;IAEK,YAAAC,EAAA,GAAM0B,GAAA,GAAME,MAAA,CAAOhC,CAAA,GAAM,KAAKK,EAAA,GAAK2B,MAAA,CAAO9B,CAAA,GAAK8B,MAAA,CAAO5B,EAAA,EAC3D,KAAKC,EAAA,GAAMyB,GAAA,GAAME,MAAA,CAAO/B,CAAA,GAAM,KAAKI,EAAA,GAAK2B,MAAA,CAAO7B,CAAA,GAAK6B,MAAA,CAAO3B,EAAA,EAEpD;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAuC,UAAUC,SAAA,EACV;IAEI,MAAM7C,CAAA,GAAI,KAAKA,CAAA;MACTC,CAAA,GAAI,KAAKA,CAAA;MACTC,CAAA,GAAI,KAAKA,CAAA;MACTC,CAAA,GAAI,KAAKA,CAAA;MACT2C,KAAA,GAAQD,SAAA,CAAUC,KAAA;MAElBL,KAAA,GAAQ,CAACf,IAAA,CAAKqB,KAAA,CAAM,CAAC7C,CAAA,EAAGC,CAAC;MACzBuC,KAAA,GAAQhB,IAAA,CAAKqB,KAAA,CAAM9C,CAAA,EAAGD,CAAC;MAEvBgD,KAAA,GAAQtB,IAAA,CAAKuB,GAAA,CAAIR,KAAA,GAAQC,KAAK;IAEpC,OAAIM,KAAA,GAAQ,QAAWtB,IAAA,CAAKuB,GAAA,CAAIC,IAAA,GAAOF,KAAK,IAAI,QAE5CH,SAAA,CAAUL,QAAA,GAAWE,KAAA,EACrBG,SAAA,CAAUM,IAAA,CAAKlC,CAAA,GAAI4B,SAAA,CAAUM,IAAA,CAAKjC,CAAA,GAAI,MAItC2B,SAAA,CAAUL,QAAA,GAAW,GACrBK,SAAA,CAAUM,IAAA,CAAKlC,CAAA,GAAIwB,KAAA,EACnBI,SAAA,CAAUM,IAAA,CAAKjC,CAAA,GAAIwB,KAAA,GAIvBG,SAAA,CAAUvB,KAAA,CAAML,CAAA,GAAIS,IAAA,CAAK0B,IAAA,CAAMpD,CAAA,GAAIA,CAAA,GAAMC,CAAA,GAAIA,CAAE,GAC/C4C,SAAA,CAAUvB,KAAA,CAAMJ,CAAA,GAAIQ,IAAA,CAAK0B,IAAA,CAAMlD,CAAA,GAAIA,CAAA,GAAMC,CAAA,GAAIA,CAAE,GAG/C0C,SAAA,CAAUQ,QAAA,CAASpC,CAAA,GAAI,KAAKb,EAAA,IAAO0C,KAAA,CAAM7B,CAAA,GAAIjB,CAAA,GAAM8C,KAAA,CAAM5B,CAAA,GAAIhB,CAAA,GAC7D2C,SAAA,CAAUQ,QAAA,CAASnC,CAAA,GAAI,KAAKb,EAAA,IAAOyC,KAAA,CAAM7B,CAAA,GAAIhB,CAAA,GAAM6C,KAAA,CAAM5B,CAAA,GAAIf,CAAA,GAEtD0C,SAAA;EACX;EAAA;AAAA;AAAA;AAAA;EAMAS,OAAA,EACA;IACI,MAAM1B,EAAA,GAAK,KAAK5B,CAAA;MACViC,EAAA,GAAK,KAAKhC,CAAA;MACV4B,EAAA,GAAK,KAAK3B,CAAA;MACVgC,EAAA,GAAK,KAAK/B,CAAA;MACV2B,GAAA,GAAM,KAAK1B,EAAA;MACXmD,CAAA,GAAK3B,EAAA,GAAKM,EAAA,GAAOD,EAAA,GAAKJ,EAAA;IAE5B,YAAK7B,CAAA,GAAIkC,EAAA,GAAKqB,CAAA,EACd,KAAKtD,CAAA,GAAI,CAACgC,EAAA,GAAKsB,CAAA,EACf,KAAKrD,CAAA,GAAI,CAAC2B,EAAA,GAAK0B,CAAA,EACf,KAAKpD,CAAA,GAAIyB,EAAA,GAAK2B,CAAA,EACd,KAAKnD,EAAA,IAAOyB,EAAA,GAAK,KAAKxB,EAAA,GAAO6B,EAAA,GAAKJ,GAAA,IAAQyB,CAAA,EAC1C,KAAKlD,EAAA,GAAK,EAAGuB,EAAA,GAAK,KAAKvB,EAAA,GAAO4B,EAAA,GAAKH,GAAA,IAAQyB,CAAA,EAEpC;EACX;EAAA;AAAA;AAAA;AAAA;EAMAC,SAAA,EACA;IACI,YAAKxD,CAAA,GAAI,GACT,KAAKC,CAAA,GAAI,GACT,KAAKC,CAAA,GAAI,GACT,KAAKC,CAAA,GAAI,GACT,KAAKC,EAAA,GAAK,GACV,KAAKC,EAAA,GAAK,GAEH;EACX;EAAA;AAAA;AAAA;AAAA;EAMAoD,MAAA,EACA;IACU,MAAAzB,MAAA,GAAS,IAAIlC,MAAA;IAEZ,OAAAkC,MAAA,CAAAhC,CAAA,GAAI,KAAKA,CAAA,EAChBgC,MAAA,CAAO/B,CAAA,GAAI,KAAKA,CAAA,EAChB+B,MAAA,CAAO9B,CAAA,GAAI,KAAKA,CAAA,EAChB8B,MAAA,CAAO7B,CAAA,GAAI,KAAKA,CAAA,EAChB6B,MAAA,CAAO5B,EAAA,GAAK,KAAKA,EAAA,EACjB4B,MAAA,CAAO3B,EAAA,GAAK,KAAKA,EAAA,EAEV2B,MAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA0B,OAAO1B,MAAA,EACP;IACW,OAAAA,MAAA,CAAAhC,CAAA,GAAI,KAAKA,CAAA,EAChBgC,MAAA,CAAO/B,CAAA,GAAI,KAAKA,CAAA,EAChB+B,MAAA,CAAO9B,CAAA,GAAI,KAAKA,CAAA,EAChB8B,MAAA,CAAO7B,CAAA,GAAI,KAAKA,CAAA,EAChB6B,MAAA,CAAO5B,EAAA,GAAK,KAAKA,EAAA,EACjB4B,MAAA,CAAO3B,EAAA,GAAK,KAAKA,EAAA,EAEV2B,MAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA2B,SAAS3B,MAAA,EACT;IACS,YAAAhC,CAAA,GAAIgC,MAAA,CAAOhC,CAAA,EAChB,KAAKC,CAAA,GAAI+B,MAAA,CAAO/B,CAAA,EAChB,KAAKC,CAAA,GAAI8B,MAAA,CAAO9B,CAAA,EAChB,KAAKC,CAAA,GAAI6B,MAAA,CAAO7B,CAAA,EAChB,KAAKC,EAAA,GAAK4B,MAAA,CAAO5B,EAAA,EACjB,KAAKC,EAAA,GAAK2B,MAAA,CAAO3B,EAAA,EAEV;EACX;EAAA;AAAA;AAAA;AAAA;EAMA,WAAWuD,SAAA,EACX;IACI,OAAO,IAAI9D,MAAA,CAAO;EACtB;EAAA;AAAA;AAAA;AAAA;EAMA,WAAW+D,YAAA,EACX;IACI,OAAO,IAAI/D,MAAA,CAAO;EACtB;AACJ;AAIIA,MAAA,CAAOgE,SAAA,CAAUC,QAAA,GAAW,YAC5B;EACI,OAAO,wBAAwB,KAAK/D,CAAC,MAAM,KAAKC,CAAC,MAAM,KAAKC,CAAC,MAAM,KAAKC,CAAC,OAAO,KAAKC,EAAE,OAAO,KAAKC,EAAE;AACzG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}