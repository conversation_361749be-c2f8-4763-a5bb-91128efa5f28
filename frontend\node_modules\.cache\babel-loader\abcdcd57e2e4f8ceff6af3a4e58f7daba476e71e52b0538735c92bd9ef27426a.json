{"ast": null, "code": "import { extend, colord } from \"@pixi/colord\";\nimport namesPlugin from \"@pixi/colord/plugins/names\";\nextend([namesPlugin]);\nconst _Color = class _Color2 {\n  /**\n   * @param {PIXI.ColorSource} value - Optional value to use, if not provided, white is used.\n   */\n  constructor(value = 16777215) {\n    this._value = null, this._components = new Float32Array(4), this._components.fill(1), this._int = 16777215, this.value = value;\n  }\n  /** Get red component (0 - 1) */\n  get red() {\n    return this._components[0];\n  }\n  /** Get green component (0 - 1) */\n  get green() {\n    return this._components[1];\n  }\n  /** Get blue component (0 - 1) */\n  get blue() {\n    return this._components[2];\n  }\n  /** Get alpha component (0 - 1) */\n  get alpha() {\n    return this._components[3];\n  }\n  /**\n   * Set the value, suitable for chaining\n   * @param value\n   * @see PIXI.Color.value\n   */\n  setValue(value) {\n    return this.value = value, this;\n  }\n  /**\n   * The current color source.\n   *\n   * When setting:\n   * - Setting to an instance of `Color` will copy its color source and components.\n   * - Otherwise, `Color` will try to normalize the color source and set the components.\n   *   If the color source is invalid, an `Error` will be thrown and the `Color` will left unchanged.\n   *\n   * Note: The `null` in the setter's parameter type is added to match the TypeScript rule: return type of getter\n   * must be assignable to its setter's parameter type. Setting `value` to `null` will throw an `Error`.\n   *\n   * When getting:\n   * - A return value of `null` means the previous value was overridden (e.g., {@link PIXI.Color.multiply multiply},\n   *   {@link PIXI.Color.premultiply premultiply} or {@link PIXI.Color.round round}).\n   * - Otherwise, the color source used when setting is returned.\n   * @type {PIXI.ColorSource}\n   */\n  set value(value) {\n    if (value instanceof _Color2) this._value = this.cloneSource(value._value), this._int = value._int, this._components.set(value._components);else {\n      if (value === null) throw new Error(\"Cannot set PIXI.Color#value to null\");\n      (this._value === null || !this.isSourceEqual(this._value, value)) && (this.normalize(value), this._value = this.cloneSource(value));\n    }\n  }\n  get value() {\n    return this._value;\n  }\n  /**\n   * Copy a color source internally.\n   * @param value - Color source\n   */\n  cloneSource(value) {\n    return typeof value == \"string\" || typeof value == \"number\" || value instanceof Number || value === null ? value : Array.isArray(value) || ArrayBuffer.isView(value) ? value.slice(0) : typeof value == \"object\" && value !== null ? {\n      ...value\n    } : value;\n  }\n  /**\n   * Equality check for color sources.\n   * @param value1 - First color source\n   * @param value2 - Second color source\n   * @returns `true` if the color sources are equal, `false` otherwise.\n   */\n  isSourceEqual(value1, value2) {\n    const type1 = typeof value1;\n    if (type1 !== typeof value2) return !1;\n    if (type1 === \"number\" || type1 === \"string\" || value1 instanceof Number) return value1 === value2;\n    if (Array.isArray(value1) && Array.isArray(value2) || ArrayBuffer.isView(value1) && ArrayBuffer.isView(value2)) return value1.length !== value2.length ? !1 : value1.every((v, i) => v === value2[i]);\n    if (value1 !== null && value2 !== null) {\n      const keys1 = Object.keys(value1),\n        keys2 = Object.keys(value2);\n      return keys1.length !== keys2.length ? !1 : keys1.every(key => value1[key] === value2[key]);\n    }\n    return value1 === value2;\n  }\n  /**\n   * Convert to a RGBA color object.\n   * @example\n   * import { Color } from 'pixi.js';\n   * new Color('white').toRgb(); // returns { r: 1, g: 1, b: 1, a: 1 }\n   */\n  toRgba() {\n    const [r, g, b, a] = this._components;\n    return {\n      r,\n      g,\n      b,\n      a\n    };\n  }\n  /**\n   * Convert to a RGB color object.\n   * @example\n   * import { Color } from 'pixi.js';\n   * new Color('white').toRgb(); // returns { r: 1, g: 1, b: 1 }\n   */\n  toRgb() {\n    const [r, g, b] = this._components;\n    return {\n      r,\n      g,\n      b\n    };\n  }\n  /** Convert to a CSS-style rgba string: `rgba(255,255,255,1.0)`. */\n  toRgbaString() {\n    const [r, g, b] = this.toUint8RgbArray();\n    return `rgba(${r},${g},${b},${this.alpha})`;\n  }\n  toUint8RgbArray(out) {\n    const [r, g, b] = this._components;\n    return out = out ?? [], out[0] = Math.round(r * 255), out[1] = Math.round(g * 255), out[2] = Math.round(b * 255), out;\n  }\n  toRgbArray(out) {\n    out = out ?? [];\n    const [r, g, b] = this._components;\n    return out[0] = r, out[1] = g, out[2] = b, out;\n  }\n  /**\n   * Convert to a hexadecimal number.\n   * @example\n   * import { Color } from 'pixi.js';\n   * new Color('white').toNumber(); // returns 16777215\n   */\n  toNumber() {\n    return this._int;\n  }\n  /**\n   * Convert to a hexadecimal number in little endian format (e.g., BBGGRR).\n   * @example\n   * import { Color } from 'pixi.js';\n   * new Color(0xffcc99).toLittleEndianNumber(); // returns 0x99ccff\n   * @returns {number} - The color as a number in little endian format.\n   */\n  toLittleEndianNumber() {\n    const value = this._int;\n    return (value >> 16) + (value & 65280) + ((value & 255) << 16);\n  }\n  /**\n   * Multiply with another color. This action is destructive, and will\n   * override the previous `value` property to be `null`.\n   * @param {PIXI.ColorSource} value - The color to multiply by.\n   */\n  multiply(value) {\n    const [r, g, b, a] = _Color2.temp.setValue(value)._components;\n    return this._components[0] *= r, this._components[1] *= g, this._components[2] *= b, this._components[3] *= a, this.refreshInt(), this._value = null, this;\n  }\n  /**\n   * Converts color to a premultiplied alpha format. This action is destructive, and will\n   * override the previous `value` property to be `null`.\n   * @param alpha - The alpha to multiply by.\n   * @param {boolean} [applyToRGB=true] - Whether to premultiply RGB channels.\n   * @returns {PIXI.Color} - Itself.\n   */\n  premultiply(alpha, applyToRGB = !0) {\n    return applyToRGB && (this._components[0] *= alpha, this._components[1] *= alpha, this._components[2] *= alpha), this._components[3] = alpha, this.refreshInt(), this._value = null, this;\n  }\n  /**\n   * Premultiplies alpha with current color.\n   * @param {number} alpha - The alpha to multiply by.\n   * @param {boolean} [applyToRGB=true] - Whether to premultiply RGB channels.\n   * @returns {number} tint multiplied by alpha\n   */\n  toPremultiplied(alpha, applyToRGB = !0) {\n    if (alpha === 1) return (255 << 24) + this._int;\n    if (alpha === 0) return applyToRGB ? 0 : this._int;\n    let r = this._int >> 16 & 255,\n      g = this._int >> 8 & 255,\n      b = this._int & 255;\n    return applyToRGB && (r = r * alpha + 0.5 | 0, g = g * alpha + 0.5 | 0, b = b * alpha + 0.5 | 0), (alpha * 255 << 24) + (r << 16) + (g << 8) + b;\n  }\n  /**\n   * Convert to a hexidecimal string.\n   * @example\n   * import { Color } from 'pixi.js';\n   * new Color('white').toHex(); // returns \"#ffffff\"\n   */\n  toHex() {\n    const hexString = this._int.toString(16);\n    return `#${\"000000\".substring(0, 6 - hexString.length) + hexString}`;\n  }\n  /**\n   * Convert to a hexidecimal string with alpha.\n   * @example\n   * import { Color } from 'pixi.js';\n   * new Color('white').toHexa(); // returns \"#ffffffff\"\n   */\n  toHexa() {\n    const alphaString = Math.round(this._components[3] * 255).toString(16);\n    return this.toHex() + \"00\".substring(0, 2 - alphaString.length) + alphaString;\n  }\n  /**\n   * Set alpha, suitable for chaining.\n   * @param alpha\n   */\n  setAlpha(alpha) {\n    return this._components[3] = this._clamp(alpha), this;\n  }\n  /**\n   * Rounds the specified color according to the step. This action is destructive, and will\n   * override the previous `value` property to be `null`. The alpha component is not rounded.\n   * @param steps - Number of steps which will be used as a cap when rounding colors\n   * @deprecated since 7.3.0\n   */\n  round(steps) {\n    const [r, g, b] = this._components;\n    return this._components[0] = Math.round(r * steps) / steps, this._components[1] = Math.round(g * steps) / steps, this._components[2] = Math.round(b * steps) / steps, this.refreshInt(), this._value = null, this;\n  }\n  toArray(out) {\n    out = out ?? [];\n    const [r, g, b, a] = this._components;\n    return out[0] = r, out[1] = g, out[2] = b, out[3] = a, out;\n  }\n  /**\n   * Normalize the input value into rgba\n   * @param value - Input value\n   */\n  normalize(value) {\n    let r, g, b, a;\n    if ((typeof value == \"number\" || value instanceof Number) && value >= 0 && value <= 16777215) {\n      const int = value;\n      r = (int >> 16 & 255) / 255, g = (int >> 8 & 255) / 255, b = (int & 255) / 255, a = 1;\n    } else if ((Array.isArray(value) || value instanceof Float32Array) && value.length >= 3 && value.length <= 4) value = this._clamp(value), [r, g, b, a = 1] = value;else if ((value instanceof Uint8Array || value instanceof Uint8ClampedArray) && value.length >= 3 && value.length <= 4) value = this._clamp(value, 0, 255), [r, g, b, a = 255] = value, r /= 255, g /= 255, b /= 255, a /= 255;else if (typeof value == \"string\" || typeof value == \"object\") {\n      if (typeof value == \"string\") {\n        const match = _Color2.HEX_PATTERN.exec(value);\n        match && (value = `#${match[2]}`);\n      }\n      const color = colord(value);\n      color.isValid() && ({\n        r,\n        g,\n        b,\n        a\n      } = color.rgba, r /= 255, g /= 255, b /= 255);\n    }\n    if (r !== void 0) this._components[0] = r, this._components[1] = g, this._components[2] = b, this._components[3] = a, this.refreshInt();else throw new Error(`Unable to convert color ${value}`);\n  }\n  /** Refresh the internal color rgb number */\n  refreshInt() {\n    this._clamp(this._components);\n    const [r, g, b] = this._components;\n    this._int = (r * 255 << 16) + (g * 255 << 8) + (b * 255 | 0);\n  }\n  /**\n   * Clamps values to a range. Will override original values\n   * @param value - Value(s) to clamp\n   * @param min - Minimum value\n   * @param max - Maximum value\n   */\n  _clamp(value, min = 0, max = 1) {\n    return typeof value == \"number\" ? Math.min(Math.max(value, min), max) : (value.forEach((v, i) => {\n      value[i] = Math.min(Math.max(v, min), max);\n    }), value);\n  }\n};\n_Color.shared = new _Color(),\n/**\n* Temporary Color object for static uses internally.\n* As to not conflict with Color.shared.\n* @ignore\n*/\n_Color.temp = new _Color(), /** Pattern for hex strings */\n_Color.HEX_PATTERN = /^(#|0x)?(([a-f0-9]{3}){1,2}([a-f0-9]{2})?)$/i;\nlet Color = _Color;\nexport { Color };", "map": {"version": 3, "names": ["extend", "namesPlugin", "_Color", "_Color2", "constructor", "value", "_value", "_components", "Float32Array", "fill", "_int", "red", "green", "blue", "alpha", "setValue", "cloneSource", "set", "Error", "isSourceEqual", "normalize", "Number", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "slice", "value1", "value2", "type1", "length", "every", "v", "i", "keys1", "Object", "keys", "keys2", "key", "toRgba", "r", "g", "b", "a", "toRgb", "toRgbaString", "toUint8RgbArray", "out", "Math", "round", "toRgbArray", "toNumber", "toLittleEndianNumber", "multiply", "temp", "refreshInt", "premultiply", "applyToRGB", "toPremultiplied", "toHex", "hexString", "toString", "substring", "toHexa", "alphaString", "<PERSON><PERSON><PERSON><PERSON>", "_clamp", "steps", "toArray", "int", "Uint8Array", "Uint8ClampedArray", "match", "HEX_PATTERN", "exec", "color", "colord", "<PERSON><PERSON><PERSON><PERSON>", "rgba", "min", "max", "for<PERSON>ach", "shared", "Color"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\color\\src\\Color.ts"], "sourcesContent": ["import { colord, extend } from '@pixi/colord';\nimport namesPlugin from '@pixi/colord/plugins/names';\n\nimport type {\n    AnyColor,\n    HslaColor,\n    HslColor,\n    HsvaColor,\n    HsvColor,\n    RgbaColor,\n    RgbColor\n} from '@pixi/colord';\n\nextend([namesPlugin]);\n\n/**\n * Value types for the constructor of {@link PIXI.Color}.\n * These types are extended from [colord](https://www.npmjs.com/package/colord) with some PixiJS-specific extensions.\n *\n * Possible value types are:\n * - [Color names](https://www.w3.org/TR/css-color-4/#named-colors):\n *   `'red'`, `'green'`, `'blue'`, `'white'`, etc.\n * - RGB hex integers (`0xRRGGBB`):\n *   `0xff0000`, `0x00ff00`, `0x0000ff`, etc.\n * - [RGB(A) hex strings](https://www.w3.org/TR/css-color-4/#hex-notation):\n *   - 6 digits (`RRGGBB`): `'ff0000'`, `'#00ff00'`, `'0x0000ff'`, etc.\n *   - 3 digits (`RGB`): `'f00'`, `'#0f0'`, `'0x00f'`, etc.\n *   - 8 digits (`RRGGBBAA`): `'ff000080'`, `'#00ff0080'`, `'0x0000ff80'`, etc.\n *   - 4 digits (`RGBA`): `'f008'`, `'#0f08'`, `'0x00f8'`, etc.\n * - RGB(A) objects:\n *   `{ r: 255, g: 0, b: 0 }`, `{ r: 255, g: 0, b: 0, a: 0.5 }`, etc.\n * - [RGB(A) strings](https://www.w3.org/TR/css-color-4/#rgb-functions):\n *   `'rgb(255, 0, 0)'`, `'rgb(100% 0% 0%)'`, `'rgba(255, 0, 0, 0.5)'`, `'rgba(100% 0% 0% / 50%)'`, etc.\n * - RGB(A) arrays:\n *   `[1, 0, 0]`, `[1, 0, 0, 0.5]`, etc.\n * - RGB(A) Float32Array:\n *   `new Float32Array([1, 0, 0])`, `new Float32Array([1, 0, 0, 0.5])`, etc.\n * - RGB(A) Uint8Array:\n *   `new Uint8Array([255, 0, 0])`, `new Uint8Array([255, 0, 0, 128])`, etc.\n * - RGB(A) Uint8ClampedArray:\n *   `new Uint8ClampedArray([255, 0, 0])`, `new Uint8ClampedArray([255, 0, 0, 128])`, etc.\n * - HSL(A) objects:\n *   `{ h: 0, s: 100, l: 50 }`, `{ h: 0, s: 100, l: 50, a: 0.5 }`, etc.\n * - [HSL(A) strings](https://www.w3.org/TR/css-color-4/#the-hsl-notation):\n *   `'hsl(0, 100%, 50%)'`, `'hsl(0deg 100% 50%)'`, `'hsla(0, 100%, 50%, 0.5)'`, `'hsla(0deg 100% 50% / 50%)'`, etc.\n * - HSV(A) objects:\n *   `{ h: 0, s: 100, v: 100 }`, `{ h: 0, s: 100, v: 100, a: 0.5 }`, etc.\n * - {@link PIXI.Color} objects.\n * @memberof PIXI\n * @since 7.2.0\n */\nexport type ColorSource = string | number | number[] | Float32Array | Uint8Array | Uint8ClampedArray\n| HslColor | HslaColor | HsvColor | HsvaColor | RgbColor | RgbaColor | Color |\n// eslint-disable-next-line @typescript-eslint/ban-types\nNumber;\n\ntype ColorSourceTypedArray = Float32Array | Uint8Array | Uint8ClampedArray;\n\n/**\n * Color utility class.\n * @example\n * import { Color } from 'pixi.js';\n * new Color('red').toArray(); // [1, 0, 0, 1]\n * new Color(0xff0000).toArray(); // [1, 0, 0, 1]\n * new Color('ff0000').toArray(); // [1, 0, 0, 1]\n * new Color('#f00').toArray(); // [1, 0, 0, 1]\n * new Color('0xff0000ff').toArray(); // [1, 0, 0, 1]\n * new Color('#f00f').toArray(); // [1, 0, 0, 1]\n * new Color({ r: 255, g: 0, b: 0, a: 0.5 }).toArray(); // [1, 0, 0, 0.5]\n * new Color('rgb(255, 0, 0, 0.5)').toArray(); // [1, 0, 0, 0.5]\n * new Color([1, 1, 1]).toArray(); // [1, 1, 1, 1]\n * new Color([1, 0, 0, 0.5]).toArray(); // [1, 0, 0, 0.5]\n * new Color(new Float32Array([1, 0, 0, 0.5])).toArray(); // [1, 0, 0, 0.5]\n * new Color(new Uint8Array([255, 0, 0, 255])).toArray(); // [1, 0, 0, 1]\n * new Color(new Uint8ClampedArray([255, 0, 0, 255])).toArray(); // [1, 0, 0, 1]\n * new Color({ h: 0, s: 100, l: 50, a: 0.5 }).toArray(); // [1, 0, 0, 0.5]\n * new Color('hsl(0, 100%, 50%, 50%)').toArray(); // [1, 0, 0, 0.5]\n * new Color({ h: 0, s: 100, v: 100, a: 0.5 }).toArray(); // [1, 0, 0, 0.5]\n * @memberof PIXI\n * @since 7.2.0\n */\nexport class Color\n{\n    /**\n     * Default Color object for static uses\n     * @example\n     * import { Color } from 'pixi.js';\n     * Color.shared.setValue(0xffffff).toHex(); // '#ffffff'\n     */\n    static readonly shared = new Color();\n\n    /**\n     * Temporary Color object for static uses internally.\n     * As to not conflict with Color.shared.\n     * @ignore\n     */\n    private static readonly temp = new Color();\n\n    /** Pattern for hex strings */\n    private static readonly HEX_PATTERN = /^(#|0x)?(([a-f0-9]{3}){1,2}([a-f0-9]{2})?)$/i;\n\n    /** Internal color source, from constructor or set value */\n    private _value: Exclude<ColorSource, Color> | null;\n\n    /** Normalized rgba component, floats from 0-1 */\n    private _components: Float32Array;\n\n    /** Cache color as number */\n    private _int: number;\n\n    /**\n     * @param {PIXI.ColorSource} value - Optional value to use, if not provided, white is used.\n     */\n    constructor(value: ColorSource = 0xffffff)\n    {\n        this._value = null;\n        this._components = new Float32Array(4);\n        this._components.fill(1);\n        this._int = 0xffffff;\n        this.value = value;\n    }\n\n    /** Get red component (0 - 1) */\n    get red(): number\n    {\n        return this._components[0];\n    }\n\n    /** Get green component (0 - 1) */\n    get green(): number\n    {\n        return this._components[1];\n    }\n\n    /** Get blue component (0 - 1) */\n    get blue(): number\n    {\n        return this._components[2];\n    }\n\n    /** Get alpha component (0 - 1) */\n    get alpha(): number\n    {\n        return this._components[3];\n    }\n\n    /**\n     * Set the value, suitable for chaining\n     * @param value\n     * @see PIXI.Color.value\n     */\n    setValue(value: ColorSource): this\n    {\n        this.value = value;\n\n        return this;\n    }\n\n    /**\n     * The current color source.\n     *\n     * When setting:\n     * - Setting to an instance of `Color` will copy its color source and components.\n     * - Otherwise, `Color` will try to normalize the color source and set the components.\n     *   If the color source is invalid, an `Error` will be thrown and the `Color` will left unchanged.\n     *\n     * Note: The `null` in the setter's parameter type is added to match the TypeScript rule: return type of getter\n     * must be assignable to its setter's parameter type. Setting `value` to `null` will throw an `Error`.\n     *\n     * When getting:\n     * - A return value of `null` means the previous value was overridden (e.g., {@link PIXI.Color.multiply multiply},\n     *   {@link PIXI.Color.premultiply premultiply} or {@link PIXI.Color.round round}).\n     * - Otherwise, the color source used when setting is returned.\n     * @type {PIXI.ColorSource}\n     */\n    set value(value: ColorSource | null)\n    {\n        // Support copying from other Color objects\n        if (value instanceof Color)\n        {\n            this._value = this.cloneSource(value._value);\n            this._int = value._int;\n            this._components.set(value._components);\n        }\n        else if (value === null)\n        {\n            throw new Error('Cannot set PIXI.Color#value to null');\n        }\n        else if (this._value === null || !this.isSourceEqual(this._value, value))\n        {\n            this.normalize(value);\n            this._value = this.cloneSource(value);\n        }\n    }\n    get value(): Exclude<ColorSource, Color> | null\n    {\n        return this._value;\n    }\n\n    /**\n     * Copy a color source internally.\n     * @param value - Color source\n     */\n    private cloneSource(value: Exclude<ColorSource, Color> | null): Exclude<ColorSource, Color> | null\n    {\n        if (typeof value === 'string' || typeof value === 'number' || value instanceof Number || value === null)\n        {\n            return value;\n        }\n        else if (Array.isArray(value) || ArrayBuffer.isView(value))\n        {\n            return value.slice(0);\n        }\n        else if (typeof value === 'object' && value !== null)\n        {\n            return { ...value };\n        }\n\n        return value;\n    }\n\n    /**\n     * Equality check for color sources.\n     * @param value1 - First color source\n     * @param value2 - Second color source\n     * @returns `true` if the color sources are equal, `false` otherwise.\n     */\n    private isSourceEqual(value1: Exclude<ColorSource, Color>, value2: Exclude<ColorSource, Color>): boolean\n    {\n        const type1 = typeof value1;\n        const type2 = typeof value2;\n\n        // Mismatched types\n        if (type1 !== type2)\n        {\n            return false;\n        }\n        // Handle numbers/strings and things that extend Number\n        // important to do the instanceof Number first, as this is \"object\" type\n        else if (type1 === 'number' || type1 === 'string' || value1 instanceof Number)\n        {\n            return value1 === value2;\n        }\n        // Handle Arrays and TypedArrays\n        else if ((Array.isArray(value1) && Array.isArray(value2))\n            || (ArrayBuffer.isView(value1) && ArrayBuffer.isView(value2)))\n        {\n            if (value1.length !== value2.length)\n            {\n                return false;\n            }\n\n            return value1.every((v, i) => v === value2[i]);\n        }\n        // Handle Objects\n        else if (value1 !== null && value2 !== null)\n        {\n            const keys1 = Object.keys(value1) as (keyof typeof value1)[];\n            const keys2 = Object.keys(value2) as (keyof typeof value2)[];\n\n            if (keys1.length !== keys2.length)\n            {\n                return false;\n            }\n\n            return keys1.every((key) => value1[key] === value2[key]);\n        }\n\n        return value1 === value2;\n    }\n\n    /**\n     * Convert to a RGBA color object.\n     * @example\n     * import { Color } from 'pixi.js';\n     * new Color('white').toRgb(); // returns { r: 1, g: 1, b: 1, a: 1 }\n     */\n    toRgba(): RgbaColor\n    {\n        const [r, g, b, a] = this._components;\n\n        return { r, g, b, a };\n    }\n\n    /**\n     * Convert to a RGB color object.\n     * @example\n     * import { Color } from 'pixi.js';\n     * new Color('white').toRgb(); // returns { r: 1, g: 1, b: 1 }\n     */\n    toRgb(): RgbColor\n    {\n        const [r, g, b] = this._components;\n\n        return { r, g, b };\n    }\n\n    /** Convert to a CSS-style rgba string: `rgba(255,255,255,1.0)`. */\n    toRgbaString(): string\n    {\n        const [r, g, b] = this.toUint8RgbArray();\n\n        return `rgba(${r},${g},${b},${this.alpha})`;\n    }\n\n    /**\n     * Convert to an [R, G, B] array of clamped uint8 values (0 to 255).\n     * @example\n     * import { Color } from 'pixi.js';\n     * new Color('white').toUint8RgbArray(); // returns [255, 255, 255]\n     * @param {number[]|Uint8Array|Uint8ClampedArray} [out] - Output array\n     */\n    toUint8RgbArray(): number[];\n    toUint8RgbArray<T extends (number[] | Uint8Array | Uint8ClampedArray)>(out: T): T;\n    toUint8RgbArray<T extends (number[] | Uint8Array | Uint8ClampedArray)>(out?: T): T\n    {\n        const [r, g, b] = this._components;\n\n        out = out ?? [] as number[] as T;\n\n        out[0] = Math.round(r * 255);\n        out[1] = Math.round(g * 255);\n        out[2] = Math.round(b * 255);\n\n        return out;\n    }\n\n    /**\n     * Convert to an [R, G, B] array of normalized floats (numbers from 0.0 to 1.0).\n     * @example\n     * import { Color } from 'pixi.js';\n     * new Color('white').toRgbArray(); // returns [1, 1, 1]\n     * @param {number[]|Float32Array} [out] - Output array\n     */\n    toRgbArray(): number[];\n    toRgbArray<T extends (number[] | Float32Array)>(out: T): T;\n    toRgbArray<T extends (number[] | Float32Array)>(out?: T): T\n    {\n        out = out ?? [] as number[] as T;\n        const [r, g, b] = this._components;\n\n        out[0] = r;\n        out[1] = g;\n        out[2] = b;\n\n        return out;\n    }\n\n    /**\n     * Convert to a hexadecimal number.\n     * @example\n     * import { Color } from 'pixi.js';\n     * new Color('white').toNumber(); // returns 16777215\n     */\n    toNumber(): number\n    {\n        return this._int;\n    }\n\n    /**\n     * Convert to a hexadecimal number in little endian format (e.g., BBGGRR).\n     * @example\n     * import { Color } from 'pixi.js';\n     * new Color(0xffcc99).toLittleEndianNumber(); // returns 0x99ccff\n     * @returns {number} - The color as a number in little endian format.\n     */\n    toLittleEndianNumber(): number\n    {\n        const value = this._int;\n\n        return (value >> 16) + (value & 0xff00) + ((value & 0xff) << 16);\n    }\n\n    /**\n     * Multiply with another color. This action is destructive, and will\n     * override the previous `value` property to be `null`.\n     * @param {PIXI.ColorSource} value - The color to multiply by.\n     */\n    multiply(value: ColorSource): this\n    {\n        const [r, g, b, a] = Color.temp.setValue(value)._components;\n\n        this._components[0] *= r;\n        this._components[1] *= g;\n        this._components[2] *= b;\n        this._components[3] *= a;\n\n        this.refreshInt();\n        this._value = null;\n\n        return this;\n    }\n\n    /**\n     * Converts color to a premultiplied alpha format. This action is destructive, and will\n     * override the previous `value` property to be `null`.\n     * @param alpha - The alpha to multiply by.\n     * @param {boolean} [applyToRGB=true] - Whether to premultiply RGB channels.\n     * @returns {PIXI.Color} - Itself.\n     */\n    premultiply(alpha: number, applyToRGB = true): this\n    {\n        if (applyToRGB)\n        {\n            this._components[0] *= alpha;\n            this._components[1] *= alpha;\n            this._components[2] *= alpha;\n        }\n        this._components[3] = alpha;\n\n        this.refreshInt();\n        this._value = null;\n\n        return this;\n    }\n\n    /**\n     * Premultiplies alpha with current color.\n     * @param {number} alpha - The alpha to multiply by.\n     * @param {boolean} [applyToRGB=true] - Whether to premultiply RGB channels.\n     * @returns {number} tint multiplied by alpha\n     */\n    toPremultiplied(alpha: number, applyToRGB = true): number\n    {\n        if (alpha === 1.0)\n        {\n            return (0xFF << 24) + this._int;\n        }\n        if (alpha === 0.0)\n        {\n            return applyToRGB ? 0 : this._int;\n        }\n        let r = ((this._int >> 16) & 0xFF);\n        let g = ((this._int >> 8) & 0xFF);\n        let b = (this._int & 0xFF);\n\n        if (applyToRGB)\n        {\n            r = ((r * alpha) + 0.5) | 0;\n            g = ((g * alpha) + 0.5) | 0;\n            b = ((b * alpha) + 0.5) | 0;\n        }\n\n        return (alpha * 255 << 24) + (r << 16) + (g << 8) + b;\n    }\n\n    /**\n     * Convert to a hexidecimal string.\n     * @example\n     * import { Color } from 'pixi.js';\n     * new Color('white').toHex(); // returns \"#ffffff\"\n     */\n    toHex(): string\n    {\n        const hexString = this._int.toString(16);\n\n        return `#${'000000'.substring(0, 6 - hexString.length) + hexString}`;\n    }\n\n    /**\n     * Convert to a hexidecimal string with alpha.\n     * @example\n     * import { Color } from 'pixi.js';\n     * new Color('white').toHexa(); // returns \"#ffffffff\"\n     */\n    toHexa(): string\n    {\n        const alphaValue = Math.round(this._components[3] * 255);\n        const alphaString = alphaValue.toString(16);\n\n        return this.toHex() + '00'.substring(0, 2 - alphaString.length) + alphaString;\n    }\n\n    /**\n     * Set alpha, suitable for chaining.\n     * @param alpha\n     */\n    setAlpha(alpha: number): this\n    {\n        this._components[3] = this._clamp(alpha);\n\n        return this;\n    }\n\n    /**\n     * Rounds the specified color according to the step. This action is destructive, and will\n     * override the previous `value` property to be `null`. The alpha component is not rounded.\n     * @param steps - Number of steps which will be used as a cap when rounding colors\n     * @deprecated since 7.3.0\n     */\n    round(steps: number): this\n    {\n        const [r, g, b] = this._components;\n\n        this._components[0] = Math.round(r * steps) / steps;\n        this._components[1] = Math.round(g * steps) / steps;\n        this._components[2] = Math.round(b * steps) / steps;\n        this.refreshInt();\n        this._value = null;\n\n        return this;\n    }\n\n    /**\n     * Convert to an [R, G, B, A] array of normalized floats (numbers from 0.0 to 1.0).\n     * @example\n     * import { Color } from 'pixi.js';\n     * new Color('white').toArray(); // returns [1, 1, 1, 1]\n     * @param {number[]|Float32Array} [out] - Output array\n     */\n    toArray(): number[];\n    toArray<T extends (number[] | Float32Array)>(out: T): T;\n    toArray<T extends (number[] | Float32Array)>(out?: T): T\n    {\n        out = out ?? [] as number[] as T;\n        const [r, g, b, a] = this._components;\n\n        out[0] = r;\n        out[1] = g;\n        out[2] = b;\n        out[3] = a;\n\n        return out;\n    }\n\n    /**\n     * Normalize the input value into rgba\n     * @param value - Input value\n     */\n    private normalize(value: Exclude<ColorSource, Color>): void\n    {\n        let r: number | undefined;\n        let g: number | undefined;\n        let b: number | undefined;\n        let a: number | undefined;\n\n        // Number is a primative so typeof works fine, but in the case\n        // that someone creates a class that extends Number, we also\n        // need to check for instanceof Number\n        if ((typeof value === 'number' || value instanceof Number)\n            && (value as number) >= 0\n            && (value as number) <= 0xffffff)\n        {\n            const int = value as number; // cast required because instanceof Number is ambiguous for TS\n\n            r = ((int >> 16) & 0xFF) / 255;\n            g = ((int >> 8) & 0xFF) / 255;\n            b = (int & 0xFF) / 255;\n            a = 1.0;\n        }\n        else if ((Array.isArray(value) || value instanceof Float32Array)\n            // Can be rgb or rgba\n            && value.length >= 3 && value.length <= 4)\n        {\n            // make sure all values are 0 - 1\n            value = this._clamp(value);\n            [r, g, b, a = 1.0] = value;\n        }\n        else if ((value instanceof Uint8Array || value instanceof Uint8ClampedArray)\n            // Can be rgb or rgba\n            && value.length >= 3 && value.length <= 4)\n        {\n            // make sure all values are 0 - 255\n            value = this._clamp(value, 0, 255);\n            [r, g, b, a = 255] = value;\n            r /= 255;\n            g /= 255;\n            b /= 255;\n            a /= 255;\n        }\n        else if (typeof value === 'string' || typeof value === 'object')\n        {\n            if (typeof value === 'string')\n            {\n                const match = Color.HEX_PATTERN.exec(value);\n\n                if (match)\n                {\n                    // Normalize hex string, remove 0x or # prefix\n                    value = `#${match[2]}`;\n                }\n            }\n\n            const color = colord(value as AnyColor);\n\n            if (color.isValid())\n            {\n                ({ r, g, b, a } = color.rgba);\n                r /= 255;\n                g /= 255;\n                b /= 255;\n            }\n        }\n\n        // Cache normalized values for rgba and hex integer\n        if (r !== undefined)\n        {\n            this._components[0] = r as number;\n            this._components[1] = g as number;\n            this._components[2] = b as number;\n            this._components[3] = a as number;\n            this.refreshInt();\n        }\n        else\n        {\n            throw new Error(`Unable to convert color ${value}`);\n        }\n    }\n\n    /** Refresh the internal color rgb number */\n    private refreshInt(): void\n    {\n        // Clamp values to 0 - 1\n        this._clamp(this._components);\n\n        const [r, g, b] = this._components;\n\n        this._int = (((r * 255) << 16) + ((g * 255) << 8) + (b * 255 | 0));\n    }\n\n    /**\n     * Clamps values to a range. Will override original values\n     * @param value - Value(s) to clamp\n     * @param min - Minimum value\n     * @param max - Maximum value\n     */\n    private _clamp<T extends number | number[] | ColorSourceTypedArray>(value: T, min = 0, max = 1): T\n    {\n        if (typeof value === 'number')\n        {\n            return Math.min(Math.max(value, min), max) as T;\n        }\n\n        value.forEach((v, i) =>\n        {\n            value[i] = Math.min(Math.max(v, min), max);\n        });\n\n        return value;\n    }\n}\n"], "mappings": ";;AAaAA,MAAA,CAAO,CAACC,WAAW,CAAC;AAoEb,MAAMC,MAAA,GAAN,MAAMC,OAAA,CACb;EAAA;AAAA;AAAA;EA+BIC,YAAYC,KAAA,GAAqB,UACjC;IACI,KAAKC,MAAA,GAAS,MACd,KAAKC,WAAA,GAAc,IAAIC,YAAA,CAAa,CAAC,GACrC,KAAKD,WAAA,CAAYE,IAAA,CAAK,CAAC,GACvB,KAAKC,IAAA,GAAO,UACZ,KAAKL,KAAA,GAAQA,KAAA;EACjB;EAAA;EAGA,IAAIM,IAAA,EACJ;IACW,YAAKJ,WAAA,CAAY,CAAC;EAC7B;EAAA;EAGA,IAAIK,MAAA,EACJ;IACW,YAAKL,WAAA,CAAY,CAAC;EAC7B;EAAA;EAGA,IAAIM,KAAA,EACJ;IACW,YAAKN,WAAA,CAAY,CAAC;EAC7B;EAAA;EAGA,IAAIO,MAAA,EACJ;IACW,YAAKP,WAAA,CAAY,CAAC;EAC7B;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAQ,SAASV,KAAA,EACT;IACI,YAAKA,KAAA,GAAQA,KAAA,EAEN;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAmBA,IAAIA,MAAMA,KAAA,EACV;IAEI,IAAIA,KAAA,YAAiBF,OAAA,EAEjB,KAAKG,MAAA,GAAS,KAAKU,WAAA,CAAYX,KAAA,CAAMC,MAAM,GAC3C,KAAKI,IAAA,GAAOL,KAAA,CAAMK,IAAA,EAClB,KAAKH,WAAA,CAAYU,GAAA,CAAIZ,KAAA,CAAME,WAAW,OAErC;MAAA,IAAIF,KAAA,KAAU,MAET,UAAIa,KAAA,CAAM,qCAAqC;MAEpD,CAAI,KAAKZ,MAAA,KAAW,QAAQ,CAAC,KAAKa,aAAA,CAAc,KAAKb,MAAA,EAAQD,KAAK,OAEnE,KAAKe,SAAA,CAAUf,KAAK,GACpB,KAAKC,MAAA,GAAS,KAAKU,WAAA,CAAYX,KAAK;IAAA;EAE5C;EACA,IAAIA,MAAA,EACJ;IACI,OAAO,KAAKC,MAAA;EAChB;EAAA;AAAA;AAAA;AAAA;EAMQU,YAAYX,KAAA,EACpB;IACI,OAAI,OAAOA,KAAA,IAAU,YAAY,OAAOA,KAAA,IAAU,YAAYA,KAAA,YAAiBgB,MAAA,IAAUhB,KAAA,KAAU,OAExFA,KAAA,GAEFiB,KAAA,CAAMC,OAAA,CAAQlB,KAAK,KAAKmB,WAAA,CAAYC,MAAA,CAAOpB,KAAK,IAE9CA,KAAA,CAAMqB,KAAA,CAAM,CAAC,IAEf,OAAOrB,KAAA,IAAU,YAAYA,KAAA,KAAU,OAErC;MAAE,GAAGA;IAAA,IAGTA,KAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQQc,cAAcQ,MAAA,EAAqCC,MAAA,EAC3D;IACI,MAAMC,KAAA,GAAQ,OAAOF,MAAA;IAIrB,IAAIE,KAAA,KAHU,OAAOD,MAAA,EAKV;IAIN,IAAIC,KAAA,KAAU,YAAYA,KAAA,KAAU,YAAYF,MAAA,YAAkBN,MAAA,EAEnE,OAAOM,MAAA,KAAWC,MAAA;IAGjB,IAAKN,KAAA,CAAMC,OAAA,CAAQI,MAAM,KAAKL,KAAA,CAAMC,OAAA,CAAQK,MAAM,KAC/CJ,WAAA,CAAYC,MAAA,CAAOE,MAAM,KAAKH,WAAA,CAAYC,MAAA,CAAOG,MAAM,GAE3D,OAAID,MAAA,CAAOG,MAAA,KAAWF,MAAA,CAAOE,MAAA,GAElB,KAGJH,MAAA,CAAOI,KAAA,CAAM,CAACC,CAAA,EAAGC,CAAA,KAAMD,CAAA,KAAMJ,MAAA,CAAOK,CAAC,CAAC;IAGxC,IAAAN,MAAA,KAAW,QAAQC,MAAA,KAAW,MACvC;MACU,MAAAM,KAAA,GAAQC,MAAA,CAAOC,IAAA,CAAKT,MAAM;QAC1BU,KAAA,GAAQF,MAAA,CAAOC,IAAA,CAAKR,MAAM;MAEhC,OAAIM,KAAA,CAAMJ,MAAA,KAAWO,KAAA,CAAMP,MAAA,GAEhB,KAGJI,KAAA,CAAMH,KAAA,CAAOO,GAAA,IAAQX,MAAA,CAAOW,GAAG,MAAMV,MAAA,CAAOU,GAAG,CAAC;IAC3D;IAEA,OAAOX,MAAA,KAAWC,MAAA;EACtB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAW,OAAA,EACA;IACI,MAAM,CAACC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGC,CAAC,IAAI,KAAKpC,WAAA;IAE1B,OAAO;MAAEiC,CAAA;MAAGC,CAAA;MAAGC,CAAA;MAAGC;IAAE;EACxB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAC,MAAA,EACA;IACI,MAAM,CAACJ,CAAA,EAAGC,CAAA,EAAGC,CAAC,IAAI,KAAKnC,WAAA;IAEhB;MAAEiC,CAAA;MAAGC,CAAA;MAAGC;IAAA;EACnB;EAAA;EAGAG,aAAA,EACA;IACI,MAAM,CAACL,CAAA,EAAGC,CAAA,EAAGC,CAAC,IAAI,KAAKI,eAAA;IAEhB,eAAQN,CAAC,IAAIC,CAAC,IAAIC,CAAC,IAAI,KAAK5B,KAAK;EAC5C;EAWAgC,gBAAuEC,GAAA,EACvE;IACI,MAAM,CAACP,CAAA,EAAGC,CAAA,EAAGC,CAAC,IAAI,KAAKnC,WAAA;IAEjB,OAAAwC,GAAA,GAAAA,GAAA,IAAO,EAAC,EAEdA,GAAA,CAAI,CAAC,IAAIC,IAAA,CAAKC,KAAA,CAAMT,CAAA,GAAI,GAAG,GAC3BO,GAAA,CAAI,CAAC,IAAIC,IAAA,CAAKC,KAAA,CAAMR,CAAA,GAAI,GAAG,GAC3BM,GAAA,CAAI,CAAC,IAAIC,IAAA,CAAKC,KAAA,CAAMP,CAAA,GAAI,GAAG,GAEpBK,GAAA;EACX;EAWAG,WAAgDH,GAAA,EAChD;IACIA,GAAA,GAAMA,GAAA,IAAO;IACb,MAAM,CAACP,CAAA,EAAGC,CAAA,EAAGC,CAAC,IAAI,KAAKnC,WAAA;IAEnB,OAAAwC,GAAA,EAAC,IAAIP,CAAA,EACTO,GAAA,CAAI,CAAC,IAAIN,CAAA,EACTM,GAAA,CAAI,CAAC,IAAIL,CAAA,EAEFK,GAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAI,SAAA,EACA;IACI,OAAO,KAAKzC,IAAA;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASA0C,qBAAA,EACA;IACI,MAAM/C,KAAA,GAAQ,KAAKK,IAAA;IAEnB,QAAQL,KAAA,IAAS,OAAOA,KAAA,GAAQ,WAAYA,KAAA,GAAQ,QAAS;EACjE;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAgD,SAAShD,KAAA,EACT;IACU,OAACmC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGC,CAAC,IAAIxC,OAAA,CAAMmD,IAAA,CAAKvC,QAAA,CAASV,KAAK,EAAEE,WAAA;IAE3C,YAAAA,WAAA,CAAY,CAAC,KAAKiC,CAAA,EACvB,KAAKjC,WAAA,CAAY,CAAC,KAAKkC,CAAA,EACvB,KAAKlC,WAAA,CAAY,CAAC,KAAKmC,CAAA,EACvB,KAAKnC,WAAA,CAAY,CAAC,KAAKoC,CAAA,EAEvB,KAAKY,UAAA,CAAW,GAChB,KAAKjD,MAAA,GAAS,MAEP;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAkD,YAAY1C,KAAA,EAAe2C,UAAA,GAAa,IACxC;IACQ,OAAAA,UAAA,KAEA,KAAKlD,WAAA,CAAY,CAAC,KAAKO,KAAA,EACvB,KAAKP,WAAA,CAAY,CAAC,KAAKO,KAAA,EACvB,KAAKP,WAAA,CAAY,CAAC,KAAKO,KAAA,GAE3B,KAAKP,WAAA,CAAY,CAAC,IAAIO,KAAA,EAEtB,KAAKyC,UAAA,CAAW,GAChB,KAAKjD,MAAA,GAAS,MAEP;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAoD,gBAAgB5C,KAAA,EAAe2C,UAAA,GAAa,IAC5C;IACI,IAAI3C,KAAA,KAAU,GAEF,eAAQ,MAAM,KAAKJ,IAAA;IAE/B,IAAII,KAAA,KAAU,GAEH,OAAA2C,UAAA,GAAa,IAAI,KAAK/C,IAAA;IAEjC,IAAI8B,CAAA,GAAM,KAAK9B,IAAA,IAAQ,KAAM;MACzB+B,CAAA,GAAM,KAAK/B,IAAA,IAAQ,IAAK;MACxBgC,CAAA,GAAK,KAAKhC,IAAA,GAAO;IAEjB,OAAA+C,UAAA,KAEAjB,CAAA,GAAMA,CAAA,GAAI1B,KAAA,GAAS,MAAO,GAC1B2B,CAAA,GAAMA,CAAA,GAAI3B,KAAA,GAAS,MAAO,GAC1B4B,CAAA,GAAMA,CAAA,GAAI5B,KAAA,GAAS,MAAO,KAGtBA,KAAA,GAAQ,OAAO,OAAO0B,CAAA,IAAK,OAAOC,CAAA,IAAK,KAAKC,CAAA;EACxD;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAiB,MAAA,EACA;IACI,MAAMC,SAAA,GAAY,KAAKlD,IAAA,CAAKmD,QAAA,CAAS,EAAE;IAEhC,WAAI,SAASC,SAAA,CAAU,GAAG,IAAIF,SAAA,CAAU9B,MAAM,IAAI8B,SAAS;EACtE;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAG,OAAA,EACA;IAEU,MAAAC,WAAA,GADahB,IAAA,CAAKC,KAAA,CAAM,KAAK1C,WAAA,CAAY,CAAC,IAAI,GAAG,EACxBsD,QAAA,CAAS,EAAE;IAEnC,YAAKF,KAAA,KAAU,KAAKG,SAAA,CAAU,GAAG,IAAIE,WAAA,CAAYlC,MAAM,IAAIkC,WAAA;EACtE;EAAA;AAAA;AAAA;AAAA;EAMAC,SAASnD,KAAA,EACT;IACI,YAAKP,WAAA,CAAY,CAAC,IAAI,KAAK2D,MAAA,CAAOpD,KAAK,GAEhC;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAmC,MAAMkB,KAAA,EACN;IACI,MAAM,CAAC3B,CAAA,EAAGC,CAAA,EAAGC,CAAC,IAAI,KAAKnC,WAAA;IAEvB,YAAKA,WAAA,CAAY,CAAC,IAAIyC,IAAA,CAAKC,KAAA,CAAMT,CAAA,GAAI2B,KAAK,IAAIA,KAAA,EAC9C,KAAK5D,WAAA,CAAY,CAAC,IAAIyC,IAAA,CAAKC,KAAA,CAAMR,CAAA,GAAI0B,KAAK,IAAIA,KAAA,EAC9C,KAAK5D,WAAA,CAAY,CAAC,IAAIyC,IAAA,CAAKC,KAAA,CAAMP,CAAA,GAAIyB,KAAK,IAAIA,KAAA,EAC9C,KAAKZ,UAAA,CACL,QAAKjD,MAAA,GAAS,MAEP;EACX;EAWA8D,QAA6CrB,GAAA,EAC7C;IACIA,GAAA,GAAMA,GAAA,IAAO;IACb,MAAM,CAACP,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGC,CAAC,IAAI,KAAKpC,WAAA;IAE1B,OAAAwC,GAAA,CAAI,CAAC,IAAIP,CAAA,EACTO,GAAA,CAAI,CAAC,IAAIN,CAAA,EACTM,GAAA,CAAI,CAAC,IAAIL,CAAA,EACTK,GAAA,CAAI,CAAC,IAAIJ,CAAA,EAEFI,GAAA;EACX;EAAA;AAAA;AAAA;AAAA;EAMQ3B,UAAUf,KAAA,EAClB;IACQ,IAAAmC,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA;IAKC,YAAOtC,KAAA,IAAU,YAAYA,KAAA,YAAiBgB,MAAA,KAC3ChB,KAAA,IAAoB,KACpBA,KAAA,IAAoB,UAC5B;MACI,MAAMgE,GAAA,GAAMhE,KAAA;MAEZmC,CAAA,IAAM6B,GAAA,IAAO,KAAM,OAAQ,KAC3B5B,CAAA,IAAM4B,GAAA,IAAO,IAAK,OAAQ,KAC1B3B,CAAA,IAAK2B,GAAA,GAAM,OAAQ,KACnB1B,CAAA,GAAI;IACR,YACUrB,KAAA,CAAMC,OAAA,CAAQlB,KAAK,KAAKA,KAAA,YAAiBG,YAAA,KAE5CH,KAAA,CAAMyB,MAAA,IAAU,KAAKzB,KAAA,CAAMyB,MAAA,IAAU,GAGhCzB,KAAA,QAAK6D,MAAA,CAAO7D,KAAK,GACzB,CAACmC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGC,CAAA,GAAI,CAAG,IAAItC,KAAA,WAEfA,KAAA,YAAiBiE,UAAA,IAAcjE,KAAA,YAAiBkE,iBAAA,KAEnDlE,KAAA,CAAMyB,MAAA,IAAU,KAAKzB,KAAA,CAAMyB,MAAA,IAAU,GAGhCzB,KAAA,QAAK6D,MAAA,CAAO7D,KAAA,EAAO,GAAG,GAAG,GACjC,CAACmC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGC,CAAA,GAAI,GAAG,IAAItC,KAAA,EACrBmC,CAAA,IAAK,KACLC,CAAA,IAAK,KACLC,CAAA,IAAK,KACLC,CAAA,IAAK,aAEA,OAAOtC,KAAA,IAAU,YAAY,OAAOA,KAAA,IAAU,UACvD;MACQ,WAAOA,KAAA,IAAU,UACrB;QACI,MAAMmE,KAAA,GAAQrE,OAAA,CAAMsE,WAAA,CAAYC,IAAA,CAAKrE,KAAK;QAEtCmE,KAAA,KAGAnE,KAAA,GAAQ,IAAImE,KAAA,CAAM,CAAC,CAAC;MAE5B;MAEM,MAAAG,KAAA,GAAQC,MAAA,CAAOvE,KAAiB;MAElCsE,KAAA,CAAME,OAAA,OAEL;QAAErC,CAAA;QAAGC,CAAA;QAAGC,CAAA;QAAGC;MAAE,IAAIgC,KAAA,CAAMG,IAAA,EACxBtC,CAAA,IAAK,KACLC,CAAA,IAAK,KACLC,CAAA,IAAK;IAEb;IAGA,IAAIF,CAAA,KAAM,QAED,KAAAjC,WAAA,CAAY,CAAC,IAAIiC,CAAA,EACtB,KAAKjC,WAAA,CAAY,CAAC,IAAIkC,CAAA,EACtB,KAAKlC,WAAA,CAAY,CAAC,IAAImC,CAAA,EACtB,KAAKnC,WAAA,CAAY,CAAC,IAAIoC,CAAA,EACtB,KAAKY,UAAA,QAIL,MAAM,IAAIrC,KAAA,CAAM,2BAA2Bb,KAAK,EAAE;EAE1D;EAAA;EAGQkD,WAAA,EACR;IAES,KAAAW,MAAA,CAAO,KAAK3D,WAAW;IAE5B,MAAM,CAACiC,CAAA,EAAGC,CAAA,EAAGC,CAAC,IAAI,KAAKnC,WAAA;IAElB,KAAAG,IAAA,IAAU8B,CAAA,GAAI,OAAQ,OAAQC,CAAA,GAAI,OAAQ,MAAMC,CAAA,GAAI,MAAM;EACnE;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQQwB,OAA4D7D,KAAA,EAAU0E,GAAA,GAAM,GAAGC,GAAA,GAAM,GAC7F;IACI,OAAI,OAAO3E,KAAA,IAAU,WAEV2C,IAAA,CAAK+B,GAAA,CAAI/B,IAAA,CAAKgC,GAAA,CAAI3E,KAAA,EAAO0E,GAAG,GAAGC,GAAG,KAG7C3E,KAAA,CAAM4E,OAAA,CAAQ,CAACjD,CAAA,EAAGC,CAAA,KAClB;MACU5B,KAAA,CAAA4B,CAAC,IAAIe,IAAA,CAAK+B,GAAA,CAAI/B,IAAA,CAAKgC,GAAA,CAAIhD,CAAA,EAAG+C,GAAG,GAAGC,GAAG;IAAA,CAC5C,GAEM3E,KAAA;EACX;AACJ;AA/iBaH,MAAA,CAQOgF,MAAA,GAAS,IAAIhF,MAAA,CAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAR1BA,MAAA,CAeeoD,IAAA,GAAO,IAAIpD,MAAA,CAAM;AAfhCA,MAAA,CAkBeuE,WAAA,GAAc;AAlBnC,IAAMU,KAAA,GAANjF,MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}