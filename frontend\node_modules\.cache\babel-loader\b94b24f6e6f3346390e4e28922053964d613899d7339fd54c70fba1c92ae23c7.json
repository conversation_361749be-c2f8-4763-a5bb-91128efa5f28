{"ast": null, "code": "import { buildCircle } from \"./buildCircle.mjs\";\nconst buildRoundedRectangle = {\n  build(graphicsData) {\n    buildCircle.build(graphicsData);\n  },\n  triangulate(graphicsData, graphicsGeometry) {\n    buildCircle.triangulate(graphicsData, graphicsGeometry);\n  }\n};\nexport { buildRoundedRectangle };", "map": {"version": 3, "names": ["buildRoundedRectangle", "build", "graphicsData", "buildCircle", "triangulate", "graphicsGeometry"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\graphics\\src\\utils\\buildRoundedRectangle.ts"], "sourcesContent": ["// for type only\nimport { buildCircle } from './buildCircle';\n\nimport type { IShapeBuildCommand } from './IShapeBuildCommand';\n\n/**\n * Builds a rounded rectangle to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.WebGLGraphicsData} graphicsData - The graphics object containing all the necessary properties\n * @param {object} webGLData - an object containing all the WebGL-specific information to create this shape\n * @param {object} webGLDataNativeLines - an object containing all the WebGL-specific information to create nativeLines\n */\nexport const buildRoundedRectangle: IShapeBuildCommand = {\n\n    build(graphicsData)\n    {\n        buildCircle.build(graphicsData);\n    },\n\n    triangulate(graphicsData, graphicsGeometry)\n    {\n        buildCircle.triangulate(graphicsData, graphicsGeometry);\n    },\n};\n"], "mappings": ";AAeO,MAAMA,qBAAA,GAA4C;EAErDC,MAAMC,YAAA,EACN;IACIC,WAAA,CAAYF,KAAA,CAAMC,YAAY;EAClC;EAEAE,YAAYF,YAAA,EAAcG,gBAAA,EAC1B;IACgBF,WAAA,CAAAC,WAAA,CAAYF,YAAA,EAAcG,gBAAgB;EAC1D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}