{"ast": null, "code": "import { settings, utils } from \"@pixi/core\";\nimport { TextStyle } from \"@pixi/text\";\nconst _HTMLTextStyle = class _HTMLTextStyle2 extends TextStyle {\n  constructor() {\n    super(...arguments), this._fonts = [], this._overrides = [], this._stylesheet = \"\", this.fontsDirty = !1;\n  }\n  /**\n   * Convert a TextStyle to HTMLTextStyle\n   * @param originalStyle\n   * @example\n   * import {TextStyle } from 'pixi.js';\n   * import {HTMLTextStyle} from '@pixi/text-html';\n   * const style = new TextStyle();\n   * const htmlStyle = HTMLTextStyle.from(style);\n   */\n  static from(originalStyle) {\n    return new _HTMLTextStyle2(Object.keys(_HTMLTextStyle2.defaultOptions).reduce((obj, prop) => ({\n      ...obj,\n      [prop]: originalStyle[prop]\n    }), {}));\n  }\n  /** Clear the current font */\n  cleanFonts() {\n    this._fonts.length > 0 && (this._fonts.forEach(font => {\n      URL.revokeObjectURL(font.src), font.refs--, font.refs === 0 && (font.fontFace && document.fonts.delete(font.fontFace), delete _HTMLTextStyle2.availableFonts[font.originalUrl]);\n    }), this.fontFamily = \"Arial\", this._fonts.length = 0, this.styleID++, this.fontsDirty = !0);\n  }\n  /**\n   * Because of how HTMLText renders, fonts need to be imported\n   * @param url\n   * @param options\n   */\n  loadFont(url, options = {}) {\n    const {\n      availableFonts\n    } = _HTMLTextStyle2;\n    if (availableFonts[url]) {\n      const font = availableFonts[url];\n      return this._fonts.push(font), font.refs++, this.styleID++, this.fontsDirty = !0, Promise.resolve();\n    }\n    return settings.ADAPTER.fetch(url).then(response => response.blob()).then(async blob => new Promise((resolve, reject) => {\n      const src = URL.createObjectURL(blob),\n        reader = new FileReader();\n      reader.onload = () => resolve([src, reader.result]), reader.onerror = reject, reader.readAsDataURL(blob);\n    })).then(async ([src, dataSrc]) => {\n      const font = Object.assign({\n        family: utils.path.basename(url, utils.path.extname(url)),\n        weight: \"normal\",\n        style: \"normal\",\n        display: \"auto\",\n        src,\n        dataSrc,\n        refs: 1,\n        originalUrl: url,\n        fontFace: null\n      }, options);\n      availableFonts[url] = font, this._fonts.push(font), this.styleID++;\n      const fontFace = new FontFace(font.family, `url(${font.src})`, {\n        weight: font.weight,\n        style: font.style,\n        display: font.display\n      });\n      font.fontFace = fontFace, await fontFace.load(), document.fonts.add(fontFace), await document.fonts.ready, this.styleID++, this.fontsDirty = !0;\n    });\n  }\n  /**\n   * Add a style override, this can be any CSS property\n   * it will override any built-in style. This is the\n   * property and the value as a string (e.g., `color: red`).\n   * This will override any other internal style.\n   * @param {string} value - CSS style(s) to add.\n   * @example\n   * style.addOverride('background-color: red');\n   */\n  addOverride(...value) {\n    const toAdd = value.filter(v => !this._overrides.includes(v));\n    toAdd.length > 0 && (this._overrides.push(...toAdd), this.styleID++);\n  }\n  /**\n   * Remove any overrides that match the value.\n   * @param {string} value - CSS style to remove.\n   * @example\n   * style.removeOverride('background-color: red');\n   */\n  removeOverride(...value) {\n    const toRemove = value.filter(v => this._overrides.includes(v));\n    toRemove.length > 0 && (this._overrides = this._overrides.filter(v => !toRemove.includes(v)), this.styleID++);\n  }\n  /**\n   * Internally converts all of the style properties into CSS equivalents.\n   * @param scale\n   * @returns The CSS style string, for setting `style` property of root HTMLElement.\n   */\n  toCSS(scale) {\n    return [`transform: scale(${scale})`, \"transform-origin: top left\", \"display: inline-block\", `color: ${this.normalizeColor(this.fill)}`, `font-size: ${this.fontSize}px`, `font-family: ${this.fontFamily}`, `font-weight: ${this.fontWeight}`, `font-style: ${this.fontStyle}`, `font-variant: ${this.fontVariant}`, `letter-spacing: ${this.letterSpacing}px`, `text-align: ${this.align}`, `padding: ${this.padding}px`, `white-space: ${this.whiteSpace}`, ...(this.lineHeight ? [`line-height: ${this.lineHeight}px`] : []), ...(this.wordWrap ? [`word-wrap: ${this.breakWords ? \"break-all\" : \"break-word\"}`, `max-width: ${this.wordWrapWidth}px`] : []), ...(this.strokeThickness ? [`-webkit-text-stroke-width: ${this.strokeThickness}px`, `-webkit-text-stroke-color: ${this.normalizeColor(this.stroke)}`, `text-stroke-width: ${this.strokeThickness}px`, `text-stroke-color: ${this.normalizeColor(this.stroke)}`, \"paint-order: stroke\"] : []), ...(this.dropShadow ? [this.dropShadowToCSS()] : []), ...this._overrides].join(\";\");\n  }\n  /** Get the font CSS styles from the loaded font, If available. */\n  toGlobalCSS() {\n    return this._fonts.reduce((result, font) => `${result}\n            @font-face {\n                font-family: \"${font.family}\";\n                src: url('${font.dataSrc}');\n                font-weight: ${font.weight};\n                font-style: ${font.style};\n                font-display: ${font.display};\n            }`, this._stylesheet);\n  }\n  /** Internal stylesheet contents, useful for creating rules for rendering */\n  get stylesheet() {\n    return this._stylesheet;\n  }\n  set stylesheet(value) {\n    this._stylesheet !== value && (this._stylesheet = value, this.styleID++);\n  }\n  /**\n   * Convert numerical colors into hex-strings\n   * @param color\n   */\n  normalizeColor(color) {\n    return Array.isArray(color) && (color = utils.rgb2hex(color)), typeof color == \"number\" ? utils.hex2string(color) : color;\n  }\n  /** Convert the internal drop-shadow settings to CSS text-shadow */\n  dropShadowToCSS() {\n    let color = this.normalizeColor(this.dropShadowColor);\n    const alpha = this.dropShadowAlpha,\n      x = Math.round(Math.cos(this.dropShadowAngle) * this.dropShadowDistance),\n      y = Math.round(Math.sin(this.dropShadowAngle) * this.dropShadowDistance);\n    color.startsWith(\"#\") && alpha < 1 && (color += (alpha * 255 | 0).toString(16).padStart(2, \"0\"));\n    const position = `${x}px ${y}px`;\n    return this.dropShadowBlur > 0 ? `text-shadow: ${position} ${this.dropShadowBlur}px ${color}` : `text-shadow: ${position} ${color}`;\n  }\n  /** Resets all properties to the defaults specified in TextStyle.prototype._default */\n  reset() {\n    Object.assign(this, _HTMLTextStyle2.defaultOptions);\n  }\n  /**\n   * Called after the image is loaded but before drawing to the canvas.\n   * Mostly used to handle Safari's font loading bug.\n   * @ignore\n   */\n  onBeforeDraw() {\n    const {\n      fontsDirty: prevFontsDirty\n    } = this;\n    return this.fontsDirty = !1, this.isSafari && this._fonts.length > 0 && prevFontsDirty ? new Promise(resolve => setTimeout(resolve, 100)) : Promise.resolve();\n  }\n  /**\n   * Proving that Safari is the new IE\n   * @ignore\n   */\n  get isSafari() {\n    const {\n      userAgent\n    } = settings.ADAPTER.getNavigator();\n    return /^((?!chrome|android).)*safari/i.test(userAgent);\n  }\n  set fillGradientStops(_value) {\n    console.warn(\"[HTMLTextStyle] fillGradientStops is not supported by HTMLText\");\n  }\n  get fillGradientStops() {\n    return super.fillGradientStops;\n  }\n  set fillGradientType(_value) {\n    console.warn(\"[HTMLTextStyle] fillGradientType is not supported by HTMLText\");\n  }\n  get fillGradientType() {\n    return super.fillGradientType;\n  }\n  set miterLimit(_value) {\n    console.warn(\"[HTMLTextStyle] miterLimit is not supported by HTMLText\");\n  }\n  get miterLimit() {\n    return super.miterLimit;\n  }\n  set trim(_value) {\n    console.warn(\"[HTMLTextStyle] trim is not supported by HTMLText\");\n  }\n  get trim() {\n    return super.trim;\n  }\n  set textBaseline(_value) {\n    console.warn(\"[HTMLTextStyle] textBaseline is not supported by HTMLText\");\n  }\n  get textBaseline() {\n    return super.textBaseline;\n  }\n  set leading(_value) {\n    console.warn(\"[HTMLTextStyle] leading is not supported by HTMLText\");\n  }\n  get leading() {\n    return super.leading;\n  }\n  set lineJoin(_value) {\n    console.warn(\"[HTMLTextStyle] lineJoin is not supported by HTMLText\");\n  }\n  get lineJoin() {\n    return super.lineJoin;\n  }\n};\n_HTMLTextStyle.availableFonts = {},\n/**\n* List of default options, these are largely the same as TextStyle,\n* with the exception of whiteSpace, which is set to 'normal' by default.\n*/\n_HTMLTextStyle.defaultOptions = {\n  /** Align */\n  align: \"left\",\n  /** Break words */\n  breakWords: !1,\n  /** Drop shadow */\n  dropShadow: !1,\n  /** Drop shadow alpha */\n  dropShadowAlpha: 1,\n  /**\n   * Drop shadow angle\n   * @type {number}\n   * @default Math.PI / 6\n   */\n  dropShadowAngle: Math.PI / 6,\n  /** Drop shadow blur */\n  dropShadowBlur: 0,\n  /** Drop shadow color */\n  dropShadowColor: \"black\",\n  /** Drop shadow distance */\n  dropShadowDistance: 5,\n  /** Fill */\n  fill: \"black\",\n  /** Font family */\n  fontFamily: \"Arial\",\n  /** Font size */\n  fontSize: 26,\n  /** Font style */\n  fontStyle: \"normal\",\n  /** Font variant */\n  fontVariant: \"normal\",\n  /** Font weight */\n  fontWeight: \"normal\",\n  /** Letter spacing */\n  letterSpacing: 0,\n  /** Line height */\n  lineHeight: 0,\n  /** Padding */\n  padding: 0,\n  /** Stroke */\n  stroke: \"black\",\n  /** Stroke thickness */\n  strokeThickness: 0,\n  /** White space */\n  whiteSpace: \"normal\",\n  /** Word wrap */\n  wordWrap: !1,\n  /** Word wrap width */\n  wordWrapWidth: 100\n};\nlet HTMLTextStyle = _HTMLTextStyle;\nexport { HTMLTextStyle };", "map": {"version": 3, "names": ["_HTMLTextStyle", "_HTMLTextStyle2", "TextStyle", "constructor", "arguments", "_fonts", "_overrides", "_stylesheet", "fontsDirty", "from", "originalStyle", "Object", "keys", "defaultOptions", "reduce", "obj", "prop", "cleanFonts", "length", "for<PERSON>ach", "font", "URL", "revokeObjectURL", "src", "refs", "fontFace", "document", "fonts", "delete", "availableFonts", "originalUrl", "fontFamily", "styleID", "loadFont", "url", "options", "push", "Promise", "resolve", "settings", "ADAPTER", "fetch", "then", "response", "blob", "reject", "createObjectURL", "reader", "FileReader", "onload", "result", "onerror", "readAsDataURL", "dataSrc", "assign", "family", "utils", "path", "basename", "extname", "weight", "style", "display", "FontFace", "load", "add", "ready", "addOverride", "value", "toAdd", "filter", "v", "includes", "removeOverride", "toRemove", "toCSS", "scale", "normalizeColor", "fill", "fontSize", "fontWeight", "fontStyle", "fontVariant", "letterSpacing", "align", "padding", "whiteSpace", "lineHeight", "wordWrap", "breakWords", "wordWrapWidth", "strokeThickness", "stroke", "dropShadow", "dropShadowToCSS", "join", "toGlobalCSS", "stylesheet", "color", "Array", "isArray", "rgb2hex", "hex2string", "dropShadowColor", "alpha", "dropShadowAlpha", "x", "Math", "round", "cos", "dropShadowAngle", "dropShadowDistance", "y", "sin", "startsWith", "toString", "padStart", "position", "dropShadowBlur", "reset", "onBeforeDraw", "prevFontsDirty", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "userAgent", "getNavigator", "test", "fillGradientStops", "_value", "console", "warn", "fillGradientType", "miterLimit", "trim", "textBaseline", "leading", "lineJoin", "PI", "HTMLTextStyle"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\text-html\\src\\HTMLTextStyle.ts"], "sourcesContent": ["import { settings, utils } from '@pixi/core';\nimport { TextStyle } from '@pixi/text';\n\nimport type {\n    ITextStyle,\n    TextStyleFontStyle,\n    TextStyleFontWeight,\n    TextStyleLineJoin,\n    TextStyleTextBaseline\n} from '@pixi/text';\n\n/**\n * HTMLText support more white-space options.\n * @memberof PIXI\n * @since 7.2.0\n * @see PIXI.IHTMLTextStyle\n */\nexport type HTMLTextStyleWhiteSpace = 'normal' | 'pre' | 'pre-line' | 'nowrap' | 'pre-wrap';\n\n/**\n * FontFace display options.\n * @memberof PIXI\n * @since 7.3.0\n */\nexport type FontDisplay = 'auto' | 'block' | 'swap' | 'fallback' | 'optional';\n\n// Subset of ITextStyle\ntype ITextStyleIgnore = 'whiteSpace'\n| 'fillGradientStops'\n| 'fillGradientType'\n| 'miterLimit'\n| 'textBaseline'\n| 'trim'\n| 'leading'\n| 'lineJoin';\n\n/**\n * Modifed versions from ITextStyle.\n * @memberof PIXI\n * @extends PIXI.ITextStyle\n * @since 7.2.0\n */\nexport interface IHTMLTextStyle extends Omit<ITextStyle, ITextStyleIgnore>\n{\n    /** White-space with expanded options. */\n    whiteSpace: HTMLTextStyleWhiteSpace;\n}\n\nexport interface IHTMLTextFontOptions extends Pick<IHTMLFont, 'weight' | 'style' | 'family'>\n{\n    /** font-display property */\n    display: FontDisplay;\n}\n\n/**\n * Font information for HTMLText\n * @memberof PIXI\n * @since 7.2.0\n */\nexport interface IHTMLFont\n{\n    /** User-supplied URL request */\n    originalUrl: string;\n    /** Base64 string for font */\n    dataSrc: string;\n    /** FontFace installed in the document */\n    fontFace: FontFace | null;\n    /** Blob-based URL for font */\n    src: string;\n    /** Family name of font */\n    family: string;\n    /** Weight of the font */\n    weight: TextStyleFontWeight;\n    /** Style of the font */\n    style: TextStyleFontStyle;\n    /** Display property of the font */\n    display: FontDisplay;\n    /** Reference counter */\n    refs: number;\n}\n\n/**\n * Used internally to restrict text style usage and convert easily to CSS.\n * @class\n * @memberof PIXI\n * @param {PIXI.ITextStyle|PIXI.IHTMLTextStyle} [style] - Style to copy.\n * @since 7.2.0\n */\nexport class HTMLTextStyle extends TextStyle\n{\n    /** The collection of installed fonts */\n    public static availableFonts: Record<string, IHTMLFont> = {};\n\n    /**\n     * List of default options, these are largely the same as TextStyle,\n     * with the exception of whiteSpace, which is set to 'normal' by default.\n     */\n    public static readonly defaultOptions: IHTMLTextStyle = {\n        /** Align */\n        align: 'left',\n        /** Break words */\n        breakWords: false,\n        /** Drop shadow */\n        dropShadow: false,\n        /** Drop shadow alpha */\n        dropShadowAlpha: 1,\n        /**\n         * Drop shadow angle\n         * @type {number}\n         * @default Math.PI / 6\n         */\n        dropShadowAngle: Math.PI / 6,\n        /** Drop shadow blur */\n        dropShadowBlur: 0,\n        /** Drop shadow color */\n        dropShadowColor: 'black',\n        /** Drop shadow distance */\n        dropShadowDistance: 5,\n        /** Fill */\n        fill: 'black',\n        /** Font family */\n        fontFamily: 'Arial',\n        /** Font size */\n        fontSize: 26,\n        /** Font style */\n        fontStyle: 'normal',\n        /** Font variant */\n        fontVariant: 'normal',\n        /** Font weight */\n        fontWeight: 'normal',\n        /** Letter spacing */\n        letterSpacing: 0,\n        /** Line height */\n        lineHeight: 0,\n        /** Padding */\n        padding: 0,\n        /** Stroke */\n        stroke: 'black',\n        /** Stroke thickness */\n        strokeThickness: 0,\n        /** White space */\n        whiteSpace: 'normal',\n        /** Word wrap */\n        wordWrap: false,\n        /** Word wrap width */\n        wordWrapWidth: 100,\n    };\n\n    /** For using custom fonts */\n    private _fonts: IHTMLFont[] = [];\n\n    /** List of internal style rules */\n    private _overrides: string[] = [];\n\n    /** Global rules or stylesheet, useful for creating rules for rendering */\n    private _stylesheet = '';\n\n    /** Track font changes internally */\n    private fontsDirty = false;\n\n    /**\n     * Convert a TextStyle to HTMLTextStyle\n     * @param originalStyle\n     * @example\n     * import {TextStyle } from 'pixi.js';\n     * import {HTMLTextStyle} from '@pixi/text-html';\n     * const style = new TextStyle();\n     * const htmlStyle = HTMLTextStyle.from(style);\n     */\n    static from(originalStyle: TextStyle | Partial<IHTMLTextStyle>): HTMLTextStyle\n    {\n        return new HTMLTextStyle(Object.keys(HTMLTextStyle.defaultOptions)\n            .reduce((obj, prop) => ({ ...obj, [prop]: originalStyle[prop as keyof IHTMLTextStyle] }), {})\n        );\n    }\n\n    /** Clear the current font */\n    public cleanFonts(): void\n    {\n        if (this._fonts.length > 0)\n        {\n            this._fonts.forEach((font) =>\n            {\n                URL.revokeObjectURL(font.src);\n                font.refs--;\n                if (font.refs === 0)\n                {\n                    if (font.fontFace)\n                    {\n                        document.fonts.delete(font.fontFace);\n                    }\n                    delete HTMLTextStyle.availableFonts[font.originalUrl];\n                }\n            });\n            this.fontFamily = 'Arial';\n            this._fonts.length = 0;\n            this.styleID++;\n            this.fontsDirty = true;\n        }\n    }\n\n    /**\n     * Because of how HTMLText renders, fonts need to be imported\n     * @param url\n     * @param options\n     */\n    public loadFont(url: string, options: Partial<IHTMLTextFontOptions> = {}): Promise<void>\n    {\n        const { availableFonts } = HTMLTextStyle;\n\n        // Font is already installed\n        if (availableFonts[url])\n        {\n            const font = availableFonts[url];\n\n            this._fonts.push(font);\n            font.refs++;\n            this.styleID++;\n            this.fontsDirty = true;\n\n            return Promise.resolve();\n        }\n\n        return settings.ADAPTER.fetch(url)\n            .then((response) => response.blob())\n            .then(async (blob) => new Promise<[string, string]>((resolve, reject) =>\n            {\n                const src = URL.createObjectURL(blob);\n                const reader = new FileReader();\n\n                reader.onload = () => resolve([src, reader.result as string]);\n                reader.onerror = reject;\n                reader.readAsDataURL(blob);\n            }))\n            .then(async ([src, dataSrc]) =>\n            {\n                const font: IHTMLFont = Object.assign({\n                    family: utils.path.basename(url, utils.path.extname(url)),\n                    weight: 'normal',\n                    style: 'normal',\n                    display: 'auto',\n                    src,\n                    dataSrc,\n                    refs: 1,\n                    originalUrl: url,\n                    fontFace: null,\n                }, options);\n\n                availableFonts[url] = font;\n                this._fonts.push(font);\n                this.styleID++;\n\n                // Load it into the current DOM so we can properly measure it!\n                const fontFace = new FontFace(font.family, `url(${font.src})`, {\n                    weight: font.weight,\n                    style: font.style,\n                    display: font.display,\n                });\n\n                // Keep this reference so we can remove it later from document\n                font.fontFace = fontFace;\n\n                await fontFace.load();\n                document.fonts.add(fontFace);\n                await document.fonts.ready;\n\n                this.styleID++;\n                this.fontsDirty = true;\n            });\n    }\n\n    /**\n     * Add a style override, this can be any CSS property\n     * it will override any built-in style. This is the\n     * property and the value as a string (e.g., `color: red`).\n     * This will override any other internal style.\n     * @param {string} value - CSS style(s) to add.\n     * @example\n     * style.addOverride('background-color: red');\n     */\n    public addOverride(...value: string[]): void\n    {\n        const toAdd = value.filter((v) => !this._overrides.includes(v));\n\n        if (toAdd.length > 0)\n        {\n            this._overrides.push(...toAdd);\n            this.styleID++;\n        }\n    }\n\n    /**\n     * Remove any overrides that match the value.\n     * @param {string} value - CSS style to remove.\n     * @example\n     * style.removeOverride('background-color: red');\n     */\n    public removeOverride(...value: string[]): void\n    {\n        const toRemove = value.filter((v) => this._overrides.includes(v));\n\n        if (toRemove.length > 0)\n        {\n            this._overrides = this._overrides.filter((v) => !toRemove.includes(v));\n            this.styleID++;\n        }\n    }\n\n    /**\n     * Internally converts all of the style properties into CSS equivalents.\n     * @param scale\n     * @returns The CSS style string, for setting `style` property of root HTMLElement.\n     */\n    public toCSS(scale: number): string\n    {\n        return [\n            `transform: scale(${scale})`,\n            `transform-origin: top left`,\n            'display: inline-block',\n            `color: ${this.normalizeColor(this.fill)}`,\n            `font-size: ${(this.fontSize as number)}px`,\n            `font-family: ${this.fontFamily}`,\n            `font-weight: ${this.fontWeight}`,\n            `font-style: ${this.fontStyle}`,\n            `font-variant: ${this.fontVariant}`,\n            `letter-spacing: ${this.letterSpacing}px`,\n            `text-align: ${this.align}`,\n            `padding: ${this.padding}px`,\n            `white-space: ${this.whiteSpace}`,\n            ...this.lineHeight ? [`line-height: ${this.lineHeight}px`] : [],\n            ...this.wordWrap ? [\n                `word-wrap: ${this.breakWords ? 'break-all' : 'break-word'}`,\n                `max-width: ${this.wordWrapWidth}px`\n            ] : [],\n            ...this.strokeThickness ? [\n                `-webkit-text-stroke-width: ${this.strokeThickness}px`,\n                `-webkit-text-stroke-color: ${this.normalizeColor(this.stroke)}`,\n                `text-stroke-width: ${this.strokeThickness}px`,\n                `text-stroke-color: ${this.normalizeColor(this.stroke)}`,\n                'paint-order: stroke',\n            ] : [],\n            ...this.dropShadow ? [this.dropShadowToCSS()] : [],\n            ...this._overrides,\n        ].join(';');\n    }\n\n    /** Get the font CSS styles from the loaded font, If available. */\n    public toGlobalCSS(): string\n    {\n        return this._fonts.reduce((result, font) => (\n            `${result}\n            @font-face {\n                font-family: \"${font.family}\";\n                src: url('${font.dataSrc}');\n                font-weight: ${font.weight};\n                font-style: ${font.style};\n                font-display: ${font.display};\n            }`\n        ), this._stylesheet);\n    }\n\n    /** Internal stylesheet contents, useful for creating rules for rendering */\n    public get stylesheet(): string\n    {\n        return this._stylesheet;\n    }\n    public set stylesheet(value: string)\n    {\n        if (this._stylesheet !== value)\n        {\n            this._stylesheet = value;\n            this.styleID++;\n        }\n    }\n\n    /**\n     * Convert numerical colors into hex-strings\n     * @param color\n     */\n    private normalizeColor(color: any): string\n    {\n        if (Array.isArray(color))\n        {\n            color = utils.rgb2hex(color);\n        }\n\n        if (typeof color === 'number')\n        {\n            return utils.hex2string(color);\n        }\n\n        return color;\n    }\n\n    /** Convert the internal drop-shadow settings to CSS text-shadow */\n    private dropShadowToCSS(): string\n    {\n        let color = this.normalizeColor(this.dropShadowColor);\n        const alpha = this.dropShadowAlpha;\n        const x = Math.round(Math.cos(this.dropShadowAngle) * this.dropShadowDistance);\n        const y = Math.round(Math.sin(this.dropShadowAngle) * this.dropShadowDistance);\n\n        // Append alpha to color\n        if (color.startsWith('#') && alpha < 1)\n        {\n            color += (alpha * 255 | 0).toString(16).padStart(2, '0');\n        }\n\n        const position = `${x}px ${y}px`;\n\n        if (this.dropShadowBlur > 0)\n        {\n            return `text-shadow: ${position} ${this.dropShadowBlur}px ${color}`;\n        }\n\n        return `text-shadow: ${position} ${color}`;\n    }\n\n    /** Resets all properties to the defaults specified in TextStyle.prototype._default */\n    public reset(): void\n    {\n        Object.assign(this, HTMLTextStyle.defaultOptions);\n    }\n\n    /**\n     * Called after the image is loaded but before drawing to the canvas.\n     * Mostly used to handle Safari's font loading bug.\n     * @ignore\n     */\n    public onBeforeDraw()\n    {\n        const { fontsDirty: prevFontsDirty } = this;\n\n        this.fontsDirty = false;\n\n        // Safari has a known bug where embedded fonts are not available\n        // immediately after the image loads, to compensate we wait an\n        // arbitrary amount of time\n        // @see https://bugs.webkit.org/show_bug.cgi?id=219770\n        if (this.isSafari && this._fonts.length > 0 && prevFontsDirty)\n        {\n            return new Promise<void>((resolve) => setTimeout(resolve, 100));\n        }\n\n        return Promise.resolve();\n    }\n\n    /**\n     * Proving that Safari is the new IE\n     * @ignore\n     */\n    private get isSafari(): boolean\n    {\n        const { userAgent } = settings.ADAPTER.getNavigator();\n\n        return (/^((?!chrome|android).)*safari/i).test(userAgent);\n    }\n\n    override set fillGradientStops(_value: number[])\n    {\n        console.warn('[HTMLTextStyle] fillGradientStops is not supported by HTMLText');\n    }\n    override get fillGradientStops()\n    {\n        return super.fillGradientStops;\n    }\n\n    override set fillGradientType(_value: number)\n    {\n        console.warn('[HTMLTextStyle] fillGradientType is not supported by HTMLText');\n    }\n    override get fillGradientType()\n    {\n        return super.fillGradientType;\n    }\n\n    override set miterLimit(_value: number)\n    {\n        console.warn('[HTMLTextStyle] miterLimit is not supported by HTMLText');\n    }\n    override get miterLimit()\n    {\n        return super.miterLimit;\n    }\n\n    override set trim(_value: boolean)\n    {\n        console.warn('[HTMLTextStyle] trim is not supported by HTMLText');\n    }\n    override get trim()\n    {\n        return super.trim;\n    }\n\n    override set textBaseline(_value: TextStyleTextBaseline)\n    {\n        console.warn('[HTMLTextStyle] textBaseline is not supported by HTMLText');\n    }\n    override get textBaseline()\n    {\n        return super.textBaseline;\n    }\n\n    override set leading(_value: number)\n    {\n        console.warn('[HTMLTextStyle] leading is not supported by HTMLText');\n    }\n    override get leading()\n    {\n        return super.leading;\n    }\n\n    override set lineJoin(_value: TextStyleLineJoin)\n    {\n        console.warn('[HTMLTextStyle] lineJoin is not supported by HTMLText');\n    }\n    override get lineJoin()\n    {\n        return super.lineJoin;\n    }\n}\n"], "mappings": ";;AAwFO,MAAMA,cAAA,GAAN,MAAMC,eAAA,SAAsBC,SAAA,CACnC;EADOC,YAAA;IAAA,SAAAC,SAAA,GA6DH,KAAQC,MAAA,GAAsB,IAG9B,KAAQC,UAAA,GAAuB,IAG/B,KAAQC,WAAA,GAAc,IAGtB,KAAQC,UAAA,GAAa;EAAA;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWrB,OAAOC,KAAKC,aAAA,EACZ;IACI,OAAO,IAAIT,eAAA,CAAcU,MAAA,CAAOC,IAAA,CAAKX,eAAA,CAAcY,cAAc,EAC5DC,MAAA,CAAO,CAACC,GAAA,EAAKC,IAAA,MAAU;MAAE,GAAGD,GAAA;MAAK,CAACC,IAAI,GAAGN,aAAA,CAAcM,IAA4B;IAAE,IAAI,EAAE;EAEpG;EAAA;EAGOC,WAAA,EACP;IACQ,KAAKZ,MAAA,CAAOa,MAAA,GAAS,MAErB,KAAKb,MAAA,CAAOc,OAAA,CAASC,IAAA,IACrB;MACQC,GAAA,CAAAC,eAAA,CAAgBF,IAAA,CAAKG,GAAG,GAC5BH,IAAA,CAAKI,IAAA,IACDJ,IAAA,CAAKI,IAAA,KAAS,MAEVJ,IAAA,CAAKK,QAAA,IAELC,QAAA,CAASC,KAAA,CAAMC,MAAA,CAAOR,IAAA,CAAKK,QAAQ,GAEvC,OAAOxB,eAAA,CAAc4B,cAAA,CAAeT,IAAA,CAAKU,WAAW;IAAA,CAE3D,GACD,KAAKC,UAAA,GAAa,SAClB,KAAK1B,MAAA,CAAOa,MAAA,GAAS,GACrB,KAAKc,OAAA,IACL,KAAKxB,UAAA,GAAa;EAE1B;EAAA;AAAA;AAAA;AAAA;AAAA;EAOOyB,SAASC,GAAA,EAAaC,OAAA,GAAyC,IACtE;IACU;MAAEN;IAAmB,IAAA5B,eAAA;IAGvB,IAAA4B,cAAA,CAAeK,GAAG,GACtB;MACU,MAAAd,IAAA,GAAOS,cAAA,CAAeK,GAAG;MAE/B,YAAK7B,MAAA,CAAO+B,IAAA,CAAKhB,IAAI,GACrBA,IAAA,CAAKI,IAAA,IACL,KAAKQ,OAAA,IACL,KAAKxB,UAAA,GAAa,IAEX6B,OAAA,CAAQC,OAAA;IACnB;IAEA,OAAOC,QAAA,CAASC,OAAA,CAAQC,KAAA,CAAMP,GAAG,EAC5BQ,IAAA,CAAMC,QAAA,IAAaA,QAAA,CAASC,IAAA,CAAM,GAClCF,IAAA,CAAK,MAAOE,IAAA,IAAS,IAAIP,OAAA,CAA0B,CAACC,OAAA,EAASO,MAAA,KAC9D;MACI,MAAMtB,GAAA,GAAMF,GAAA,CAAIyB,eAAA,CAAgBF,IAAI;QAC9BG,MAAA,GAAS,IAAIC,UAAA;MAEnBD,MAAA,CAAOE,MAAA,GAAS,MAAMX,OAAA,CAAQ,CAACf,GAAA,EAAKwB,MAAA,CAAOG,MAAgB,CAAC,GAC5DH,MAAA,CAAOI,OAAA,GAAUN,MAAA,EACjBE,MAAA,CAAOK,aAAA,CAAcR,IAAI;IAC5B,EAAC,EACDF,IAAA,CAAK,OAAO,CAACnB,GAAA,EAAK8B,OAAO,MAC1B;MACU,MAAAjC,IAAA,GAAkBT,MAAA,CAAO2C,MAAA,CAAO;QAClCC,MAAA,EAAQC,KAAA,CAAMC,IAAA,CAAKC,QAAA,CAASxB,GAAA,EAAKsB,KAAA,CAAMC,IAAA,CAAKE,OAAA,CAAQzB,GAAG,CAAC;QACxD0B,MAAA,EAAQ;QACRC,KAAA,EAAO;QACPC,OAAA,EAAS;QACTvC,GAAA;QACA8B,OAAA;QACA7B,IAAA,EAAM;QACNM,WAAA,EAAaI,GAAA;QACbT,QAAA,EAAU;MAAA,GACXU,OAAO;MAEKN,cAAA,CAAAK,GAAG,IAAId,IAAA,EACtB,KAAKf,MAAA,CAAO+B,IAAA,CAAKhB,IAAI,GACrB,KAAKY,OAAA;MAGC,MAAAP,QAAA,GAAW,IAAIsC,QAAA,CAAS3C,IAAA,CAAKmC,MAAA,EAAQ,OAAOnC,IAAA,CAAKG,GAAG,KAAK;QAC3DqC,MAAA,EAAQxC,IAAA,CAAKwC,MAAA;QACbC,KAAA,EAAOzC,IAAA,CAAKyC,KAAA;QACZC,OAAA,EAAS1C,IAAA,CAAK0C;MAAA,CACjB;MAGD1C,IAAA,CAAKK,QAAA,GAAWA,QAAA,EAEhB,MAAMA,QAAA,CAASuC,IAAA,IACftC,QAAA,CAASC,KAAA,CAAMsC,GAAA,CAAIxC,QAAQ,GAC3B,MAAMC,QAAA,CAASC,KAAA,CAAMuC,KAAA,EAErB,KAAKlC,OAAA,IACL,KAAKxB,UAAA,GAAa;IAAA,CACrB;EACT;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWO2D,YAAA,GAAeC,KAAA,EACtB;IACU,MAAAC,KAAA,GAAQD,KAAA,CAAME,MAAA,CAAQC,CAAA,IAAM,CAAC,KAAKjE,UAAA,CAAWkE,QAAA,CAASD,CAAC,CAAC;IAE1DF,KAAA,CAAMnD,MAAA,GAAS,MAEf,KAAKZ,UAAA,CAAW8B,IAAA,CAAK,GAAGiC,KAAK,GAC7B,KAAKrC,OAAA;EAEb;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQOyC,eAAA,GAAkBL,KAAA,EACzB;IACU,MAAAM,QAAA,GAAWN,KAAA,CAAME,MAAA,CAAQC,CAAA,IAAM,KAAKjE,UAAA,CAAWkE,QAAA,CAASD,CAAC,CAAC;IAE5DG,QAAA,CAASxD,MAAA,GAAS,MAElB,KAAKZ,UAAA,GAAa,KAAKA,UAAA,CAAWgE,MAAA,CAAQC,CAAA,IAAM,CAACG,QAAA,CAASF,QAAA,CAASD,CAAC,CAAC,GACrE,KAAKvC,OAAA;EAEb;EAAA;AAAA;AAAA;AAAA;AAAA;EAOO2C,MAAMC,KAAA,EACb;IACW,QACH,oBAAoBA,KAAK,KACzB,8BACA,yBACA,UAAU,KAAKC,cAAA,CAAe,KAAKC,IAAI,CAAC,IACxC,cAAe,KAAKC,QAAmB,MACvC,gBAAgB,KAAKhD,UAAU,IAC/B,gBAAgB,KAAKiD,UAAU,IAC/B,eAAe,KAAKC,SAAS,IAC7B,iBAAiB,KAAKC,WAAW,IACjC,mBAAmB,KAAKC,aAAa,MACrC,eAAe,KAAKC,KAAK,IACzB,YAAY,KAAKC,OAAO,MACxB,gBAAgB,KAAKC,UAAU,IAC/B,IAAG,KAAKC,UAAA,GAAa,CAAC,gBAAgB,KAAKA,UAAU,IAAI,IAAI,EAAC,GAC9D,IAAG,KAAKC,QAAA,GAAW,CACf,cAAc,KAAKC,UAAA,GAAa,cAAc,YAAY,IAC1D,cAAc,KAAKC,aAAa,QAChC,EAAC,GACL,IAAG,KAAKC,eAAA,GAAkB,CACtB,8BAA8B,KAAKA,eAAe,MAClD,8BAA8B,KAAKd,cAAA,CAAe,KAAKe,MAAM,CAAC,IAC9D,sBAAsB,KAAKD,eAAe,MAC1C,sBAAsB,KAAKd,cAAA,CAAe,KAAKe,MAAM,CAAC,IACtD,yBACA,EAAC,GACL,IAAG,KAAKC,UAAA,GAAa,CAAC,KAAKC,eAAA,CAAgB,CAAC,IAAI,EAAC,GACjD,GAAG,KAAKxF,UAAA,EACVyF,IAAA,CAAK,GAAG;EACd;EAAA;EAGOC,YAAA,EACP;IACI,OAAO,KAAK3F,MAAA,CAAOS,MAAA,CAAO,CAACoC,MAAA,EAAQ9B,IAAA,KAC/B,GAAG8B,MAAM;AAAA;AAAA,gCAEW9B,IAAA,CAAKmC,MAAM;AAAA,4BACfnC,IAAA,CAAKiC,OAAO;AAAA,+BACTjC,IAAA,CAAKwC,MAAM;AAAA,8BACZxC,IAAA,CAAKyC,KAAK;AAAA,gCACRzC,IAAA,CAAK0C,OAAO;AAAA,gBAEjC,KAAKvD,WAAW;EACvB;EAAA;EAGA,IAAW0F,WAAA,EACX;IACI,OAAO,KAAK1F,WAAA;EAChB;EACA,IAAW0F,WAAW7B,KAAA,EACtB;IACQ,KAAK7D,WAAA,KAAgB6D,KAAA,KAErB,KAAK7D,WAAA,GAAc6D,KAAA,EACnB,KAAKpC,OAAA;EAEb;EAAA;AAAA;AAAA;AAAA;EAMQ6C,eAAeqB,KAAA,EACvB;IAMI,OALIC,KAAA,CAAMC,OAAA,CAAQF,KAAK,MAEnBA,KAAA,GAAQ1C,KAAA,CAAM6C,OAAA,CAAQH,KAAK,IAG3B,OAAOA,KAAA,IAAU,WAEV1C,KAAA,CAAM8C,UAAA,CAAWJ,KAAK,IAG1BA,KAAA;EACX;EAAA;EAGQJ,gBAAA,EACR;IACI,IAAII,KAAA,GAAQ,KAAKrB,cAAA,CAAe,KAAK0B,eAAe;IAC9C,MAAAC,KAAA,GAAQ,KAAKC,eAAA;MACbC,CAAA,GAAIC,IAAA,CAAKC,KAAA,CAAMD,IAAA,CAAKE,GAAA,CAAI,KAAKC,eAAe,IAAI,KAAKC,kBAAkB;MACvEC,CAAA,GAAIL,IAAA,CAAKC,KAAA,CAAMD,IAAA,CAAKM,GAAA,CAAI,KAAKH,eAAe,IAAI,KAAKC,kBAAkB;IAGzEb,KAAA,CAAMgB,UAAA,CAAW,GAAG,KAAKV,KAAA,GAAQ,MAEjCN,KAAA,KAAUM,KAAA,GAAQ,MAAM,GAAGW,QAAA,CAAS,EAAE,EAAEC,QAAA,CAAS,GAAG,GAAG;IAG3D,MAAMC,QAAA,GAAW,GAAGX,CAAC,MAAMM,CAAC;IAE5B,OAAI,KAAKM,cAAA,GAAiB,IAEf,gBAAgBD,QAAQ,IAAI,KAAKC,cAAc,MAAMpB,KAAK,KAG9D,gBAAgBmB,QAAQ,IAAInB,KAAK;EAC5C;EAAA;EAGOqB,MAAA,EACP;IACW5G,MAAA,CAAA2C,MAAA,CAAO,MAAMrD,eAAA,CAAcY,cAAc;EACpD;EAAA;AAAA;AAAA;AAAA;AAAA;EAOO2G,aAAA,EACP;IACU;MAAEhH,UAAA,EAAYiH;IAAmB;IAEvC,YAAKjH,UAAA,GAAa,IAMd,KAAKkH,QAAA,IAAY,KAAKrH,MAAA,CAAOa,MAAA,GAAS,KAAKuG,cAAA,GAEpC,IAAIpF,OAAA,CAAeC,OAAA,IAAYqF,UAAA,CAAWrF,OAAA,EAAS,GAAG,CAAC,IAG3DD,OAAA,CAAQC,OAAA;EACnB;EAAA;AAAA;AAAA;AAAA;EAMA,IAAYoF,SAAA,EACZ;IACI,MAAM;MAAEE;IAAc,IAAArF,QAAA,CAASC,OAAA,CAAQqF,YAAA,CAAa;IAE5C,wCAAkCC,IAAA,CAAKF,SAAS;EAC5D;EAEA,IAAaG,kBAAkBC,MAAA,EAC/B;IACIC,OAAA,CAAQC,IAAA,CAAK,gEAAgE;EACjF;EACA,IAAaH,kBAAA,EACb;IACI,OAAO,MAAMA,iBAAA;EACjB;EAEA,IAAaI,iBAAiBH,MAAA,EAC9B;IACIC,OAAA,CAAQC,IAAA,CAAK,+DAA+D;EAChF;EACA,IAAaC,iBAAA,EACb;IACI,OAAO,MAAMA,gBAAA;EACjB;EAEA,IAAaC,WAAWJ,MAAA,EACxB;IACIC,OAAA,CAAQC,IAAA,CAAK,yDAAyD;EAC1E;EACA,IAAaE,WAAA,EACb;IACI,OAAO,MAAMA,UAAA;EACjB;EAEA,IAAaC,KAAKL,MAAA,EAClB;IACIC,OAAA,CAAQC,IAAA,CAAK,mDAAmD;EACpE;EACA,IAAaG,KAAA,EACb;IACI,OAAO,MAAMA,IAAA;EACjB;EAEA,IAAaC,aAAaN,MAAA,EAC1B;IACIC,OAAA,CAAQC,IAAA,CAAK,2DAA2D;EAC5E;EACA,IAAaI,aAAA,EACb;IACI,OAAO,MAAMA,YAAA;EACjB;EAEA,IAAaC,QAAQP,MAAA,EACrB;IACIC,OAAA,CAAQC,IAAA,CAAK,sDAAsD;EACvE;EACA,IAAaK,QAAA,EACb;IACI,OAAO,MAAMA,OAAA;EACjB;EAEA,IAAaC,SAASR,MAAA,EACtB;IACIC,OAAA,CAAQC,IAAA,CAAK,uDAAuD;EACxE;EACA,IAAaM,SAAA,EACb;IACI,OAAO,MAAMA,QAAA;EACjB;AACJ;AAhbaxI,cAAA,CAGK6B,cAAA,GAA4C,CAAC;AAAA;AAAA;AAAA;AAAA;AAHlD7B,cAAA,CASca,cAAA,GAAiC;EAAA;EAEpDuE,KAAA,EAAO;EAAA;EAEPK,UAAA,EAAY;EAAA;EAEZI,UAAA,EAAY;EAAA;EAEZY,eAAA,EAAiB;EAAA;AAAA;AAAA;AAAA;AAAA;EAMjBK,eAAA,EAAiBH,IAAA,CAAK8B,EAAA,GAAK;EAAA;EAE3BnB,cAAA,EAAgB;EAAA;EAEhBf,eAAA,EAAiB;EAAA;EAEjBQ,kBAAA,EAAoB;EAAA;EAEpBjC,IAAA,EAAM;EAAA;EAEN/C,UAAA,EAAY;EAAA;EAEZgD,QAAA,EAAU;EAAA;EAEVE,SAAA,EAAW;EAAA;EAEXC,WAAA,EAAa;EAAA;EAEbF,UAAA,EAAY;EAAA;EAEZG,aAAA,EAAe;EAAA;EAEfI,UAAA,EAAY;EAAA;EAEZF,OAAA,EAAS;EAAA;EAETO,MAAA,EAAQ;EAAA;EAERD,eAAA,EAAiB;EAAA;EAEjBL,UAAA,EAAY;EAAA;EAEZE,QAAA,EAAU;EAAA;EAEVE,aAAA,EAAe;AACnB;AA1DG,IAAMgD,aAAA,GAAN1I,cAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}