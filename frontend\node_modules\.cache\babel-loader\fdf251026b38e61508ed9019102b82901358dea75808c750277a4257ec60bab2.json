{"ast": null, "code": "import { Filter, CLEAR_MODES } from \"@pixi/core\";\nimport { BlurFilterPass } from \"./BlurFilterPass.mjs\";\nclass BlurFilter extends Filter {\n  /**\n   * @param strength - The strength of the blur filter.\n   * @param quality - The quality of the blur filter.\n   * @param {number|null} [resolution=PIXI.Filter.defaultResolution] - The resolution of the blur filter.\n   * @param kernelSize - The kernelSize of the blur filter.Options: 5, 7, 9, 11, 13, 15.\n   */\n  constructor(strength = 8, quality = 4, resolution = Filter.defaultResolution, kernelSize = 5) {\n    super(), this._repeatEdgePixels = !1, this.blurXFilter = new BlurFilterPass(!0, strength, quality, resolution, kernelSize), this.blurYFilter = new BlurFilterPass(!1, strength, quality, resolution, kernelSize), this.resolution = resolution, this.quality = quality, this.blur = strength, this.repeatEdgePixels = !1;\n  }\n  /**\n   * Applies the filter.\n   * @param filterManager - The manager.\n   * @param input - The input target.\n   * @param output - The output target.\n   * @param clearMode - How to clear\n   */\n  apply(filterManager, input, output, clearMode) {\n    const xStrength = Math.abs(this.blurXFilter.strength),\n      yStrength = Math.abs(this.blurYFilter.strength);\n    if (xStrength && yStrength) {\n      const renderTarget = filterManager.getFilterTexture();\n      this.blurXFilter.apply(filterManager, input, renderTarget, CLEAR_MODES.CLEAR), this.blurYFilter.apply(filterManager, renderTarget, output, clearMode), filterManager.returnFilterTexture(renderTarget);\n    } else yStrength ? this.blurYFilter.apply(filterManager, input, output, clearMode) : this.blurXFilter.apply(filterManager, input, output, clearMode);\n  }\n  updatePadding() {\n    this._repeatEdgePixels ? this.padding = 0 : this.padding = Math.max(Math.abs(this.blurXFilter.strength), Math.abs(this.blurYFilter.strength)) * 2;\n  }\n  /**\n   * Sets the strength of both the blurX and blurY properties simultaneously\n   * @default 2\n   */\n  get blur() {\n    return this.blurXFilter.blur;\n  }\n  set blur(value) {\n    this.blurXFilter.blur = this.blurYFilter.blur = value, this.updatePadding();\n  }\n  /**\n   * Sets the number of passes for blur. More passes means higher quality bluring.\n   * @default 1\n   */\n  get quality() {\n    return this.blurXFilter.quality;\n  }\n  set quality(value) {\n    this.blurXFilter.quality = this.blurYFilter.quality = value;\n  }\n  /**\n   * Sets the strength of the blurX property\n   * @default 2\n   */\n  get blurX() {\n    return this.blurXFilter.blur;\n  }\n  set blurX(value) {\n    this.blurXFilter.blur = value, this.updatePadding();\n  }\n  /**\n   * Sets the strength of the blurY property\n   * @default 2\n   */\n  get blurY() {\n    return this.blurYFilter.blur;\n  }\n  set blurY(value) {\n    this.blurYFilter.blur = value, this.updatePadding();\n  }\n  /**\n   * Sets the blendmode of the filter\n   * @default PIXI.BLEND_MODES.NORMAL\n   */\n  get blendMode() {\n    return this.blurYFilter.blendMode;\n  }\n  set blendMode(value) {\n    this.blurYFilter.blendMode = value;\n  }\n  /**\n   * If set to true the edge of the target will be clamped\n   * @default false\n   */\n  get repeatEdgePixels() {\n    return this._repeatEdgePixels;\n  }\n  set repeatEdgePixels(value) {\n    this._repeatEdgePixels = value, this.updatePadding();\n  }\n}\nexport { BlurFilter };", "map": {"version": 3, "names": ["BlurFilter", "Filter", "constructor", "strength", "quality", "resolution", "defaultResolution", "kernelSize", "_repeatEdgePixels", "blurXFilter", "Blur<PERSON>ilterPass", "blurYFilter", "blur", "repeatEdgePixels", "apply", "filterManager", "input", "output", "clearMode", "xStrength", "Math", "abs", "yStrength", "renderTarget", "getFilterTexture", "CLEAR_MODES", "CLEAR", "returnFilterTexture", "updatePadding", "padding", "max", "value", "blurX", "blurY", "blendMode"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\filter-blur\\src\\BlurFilter.ts"], "sourcesContent": ["import { CLEAR_MODES, Filter } from '@pixi/core';\nimport { BlurFilterPass } from './BlurFilterPass';\n\nimport type { BLEND_MODES, FilterSystem, RenderTexture } from '@pixi/core';\n\n/**\n * The BlurFilter applies a Gaussian blur to an object.\n *\n * The strength of the blur can be set for the x-axis and y-axis separately.\n * @memberof PIXI\n */\nexport class BlurFilter extends Filter\n{\n    public blurXFilter: BlurFilterPass;\n    public blurYFilter: BlurFilterPass;\n\n    private _repeatEdgePixels = false;\n\n    /**\n     * @param strength - The strength of the blur filter.\n     * @param quality - The quality of the blur filter.\n     * @param {number|null} [resolution=PIXI.Filter.defaultResolution] - The resolution of the blur filter.\n     * @param kernelSize - The kernelSize of the blur filter.Options: 5, 7, 9, 11, 13, 15.\n     */\n    constructor(strength = 8, quality = 4, resolution = Filter.defaultResolution, kernelSize = 5)\n    {\n        super();\n\n        this.blurXFilter = new BlurFilterPass(true, strength, quality, resolution, kernelSize);\n        this.blurYFilter = new BlurFilterPass(false, strength, quality, resolution, kernelSize);\n\n        this.resolution = resolution;\n        this.quality = quality;\n        this.blur = strength;\n\n        this.repeatEdgePixels = false;\n    }\n\n    /**\n     * Applies the filter.\n     * @param filterManager - The manager.\n     * @param input - The input target.\n     * @param output - The output target.\n     * @param clearMode - How to clear\n     */\n    apply(filterManager: FilterSystem, input: RenderTexture, output: RenderTexture, clearMode: CLEAR_MODES): void\n    {\n        const xStrength = Math.abs(this.blurXFilter.strength);\n        const yStrength = Math.abs(this.blurYFilter.strength);\n\n        if (xStrength && yStrength)\n        {\n            const renderTarget = filterManager.getFilterTexture();\n\n            this.blurXFilter.apply(filterManager, input, renderTarget, CLEAR_MODES.CLEAR);\n            this.blurYFilter.apply(filterManager, renderTarget, output, clearMode);\n\n            filterManager.returnFilterTexture(renderTarget);\n        }\n        else if (yStrength)\n        {\n            this.blurYFilter.apply(filterManager, input, output, clearMode);\n        }\n        else\n        {\n            this.blurXFilter.apply(filterManager, input, output, clearMode);\n        }\n    }\n\n    protected updatePadding(): void\n    {\n        if (this._repeatEdgePixels)\n        {\n            this.padding = 0;\n        }\n        else\n        {\n            this.padding = Math.max(Math.abs(this.blurXFilter.strength), Math.abs(this.blurYFilter.strength)) * 2;\n        }\n    }\n\n    /**\n     * Sets the strength of both the blurX and blurY properties simultaneously\n     * @default 2\n     */\n    get blur(): number\n    {\n        return this.blurXFilter.blur;\n    }\n\n    set blur(value: number)\n    {\n        this.blurXFilter.blur = this.blurYFilter.blur = value;\n        this.updatePadding();\n    }\n\n    /**\n     * Sets the number of passes for blur. More passes means higher quality bluring.\n     * @default 1\n     */\n    get quality(): number\n    {\n        return this.blurXFilter.quality;\n    }\n\n    set quality(value: number)\n    {\n        this.blurXFilter.quality = this.blurYFilter.quality = value;\n    }\n\n    /**\n     * Sets the strength of the blurX property\n     * @default 2\n     */\n    get blurX(): number\n    {\n        return this.blurXFilter.blur;\n    }\n\n    set blurX(value: number)\n    {\n        this.blurXFilter.blur = value;\n        this.updatePadding();\n    }\n\n    /**\n     * Sets the strength of the blurY property\n     * @default 2\n     */\n    get blurY(): number\n    {\n        return this.blurYFilter.blur;\n    }\n\n    set blurY(value: number)\n    {\n        this.blurYFilter.blur = value;\n        this.updatePadding();\n    }\n\n    /**\n     * Sets the blendmode of the filter\n     * @default PIXI.BLEND_MODES.NORMAL\n     */\n    get blendMode(): BLEND_MODES\n    {\n        return this.blurYFilter.blendMode;\n    }\n\n    set blendMode(value: BLEND_MODES)\n    {\n        this.blurYFilter.blendMode = value;\n    }\n\n    /**\n     * If set to true the edge of the target will be clamped\n     * @default false\n     */\n    get repeatEdgePixels(): boolean\n    {\n        return this._repeatEdgePixels;\n    }\n\n    set repeatEdgePixels(value: boolean)\n    {\n        this._repeatEdgePixels = value;\n        this.updatePadding();\n    }\n}\n"], "mappings": ";;AAWO,MAAMA,UAAA,SAAmBC,MAAA,CAChC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYIC,YAAYC,QAAA,GAAW,GAAGC,OAAA,GAAU,GAAGC,UAAA,GAAaJ,MAAA,CAAOK,iBAAA,EAAmBC,UAAA,GAAa,GAC3F;IACU,SAVV,KAAQC,iBAAA,GAAoB,IAYxB,KAAKC,WAAA,GAAc,IAAIC,cAAA,CAAe,IAAMP,QAAA,EAAUC,OAAA,EAASC,UAAA,EAAYE,UAAU,GACrF,KAAKI,WAAA,GAAc,IAAID,cAAA,CAAe,IAAOP,QAAA,EAAUC,OAAA,EAASC,UAAA,EAAYE,UAAU,GAEtF,KAAKF,UAAA,GAAaA,UAAA,EAClB,KAAKD,OAAA,GAAUA,OAAA,EACf,KAAKQ,IAAA,GAAOT,QAAA,EAEZ,KAAKU,gBAAA,GAAmB;EAC5B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAC,MAAMC,aAAA,EAA6BC,KAAA,EAAsBC,MAAA,EAAuBC,SAAA,EAChF;IACI,MAAMC,SAAA,GAAYC,IAAA,CAAKC,GAAA,CAAI,KAAKZ,WAAA,CAAYN,QAAQ;MAC9CmB,SAAA,GAAYF,IAAA,CAAKC,GAAA,CAAI,KAAKV,WAAA,CAAYR,QAAQ;IAEpD,IAAIgB,SAAA,IAAaG,SAAA,EACjB;MACU,MAAAC,YAAA,GAAeR,aAAA,CAAcS,gBAAA;MAEnC,KAAKf,WAAA,CAAYK,KAAA,CAAMC,aAAA,EAAeC,KAAA,EAAOO,YAAA,EAAcE,WAAA,CAAYC,KAAK,GAC5E,KAAKf,WAAA,CAAYG,KAAA,CAAMC,aAAA,EAAeQ,YAAA,EAAcN,MAAA,EAAQC,SAAS,GAErEH,aAAA,CAAcY,mBAAA,CAAoBJ,YAAY;IAClD,OACSD,SAAA,GAEL,KAAKX,WAAA,CAAYG,KAAA,CAAMC,aAAA,EAAeC,KAAA,EAAOC,MAAA,EAAQC,SAAS,IAI9D,KAAKT,WAAA,CAAYK,KAAA,CAAMC,aAAA,EAAeC,KAAA,EAAOC,MAAA,EAAQC,SAAS;EAEtE;EAEUU,cAAA,EACV;IACQ,KAAKpB,iBAAA,GAEL,KAAKqB,OAAA,GAAU,IAIf,KAAKA,OAAA,GAAUT,IAAA,CAAKU,GAAA,CAAIV,IAAA,CAAKC,GAAA,CAAI,KAAKZ,WAAA,CAAYN,QAAQ,GAAGiB,IAAA,CAAKC,GAAA,CAAI,KAAKV,WAAA,CAAYR,QAAQ,CAAC,IAAI;EAE5G;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIS,KAAA,EACJ;IACI,OAAO,KAAKH,WAAA,CAAYG,IAAA;EAC5B;EAEA,IAAIA,KAAKmB,KAAA,EACT;IACI,KAAKtB,WAAA,CAAYG,IAAA,GAAO,KAAKD,WAAA,CAAYC,IAAA,GAAOmB,KAAA,EAChD,KAAKH,aAAA;EACT;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIxB,QAAA,EACJ;IACI,OAAO,KAAKK,WAAA,CAAYL,OAAA;EAC5B;EAEA,IAAIA,QAAQ2B,KAAA,EACZ;IACI,KAAKtB,WAAA,CAAYL,OAAA,GAAU,KAAKO,WAAA,CAAYP,OAAA,GAAU2B,KAAA;EAC1D;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIC,MAAA,EACJ;IACI,OAAO,KAAKvB,WAAA,CAAYG,IAAA;EAC5B;EAEA,IAAIoB,MAAMD,KAAA,EACV;IACI,KAAKtB,WAAA,CAAYG,IAAA,GAAOmB,KAAA,EACxB,KAAKH,aAAA,CAAc;EACvB;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIK,MAAA,EACJ;IACI,OAAO,KAAKtB,WAAA,CAAYC,IAAA;EAC5B;EAEA,IAAIqB,MAAMF,KAAA,EACV;IACI,KAAKpB,WAAA,CAAYC,IAAA,GAAOmB,KAAA,EACxB,KAAKH,aAAA,CAAc;EACvB;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIM,UAAA,EACJ;IACI,OAAO,KAAKvB,WAAA,CAAYuB,SAAA;EAC5B;EAEA,IAAIA,UAAUH,KAAA,EACd;IACI,KAAKpB,WAAA,CAAYuB,SAAA,GAAYH,KAAA;EACjC;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIlB,iBAAA,EACJ;IACI,OAAO,KAAKL,iBAAA;EAChB;EAEA,IAAIK,iBAAiBkB,KAAA,EACrB;IACS,KAAAvB,iBAAA,GAAoBuB,KAAA,EACzB,KAAKH,aAAA,CAAc;EACvB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}