{"ast": null, "code": "import { Runner } from \"@pixi/runner\";\nimport { EventEmitter } from \"@pixi/utils\";\nclass SystemManager extends EventEmitter {\n  constructor() {\n    super(...arguments), this.runners = {}, this._systemsHash = {};\n  }\n  /**\n   * Set up a system with a collection of SystemClasses and runners.\n   * Systems are attached dynamically to this class when added.\n   * @param config - the config for the system manager\n   */\n  setup(config) {\n    this.addRunners(...config.runners);\n    const priority = (config.priority ?? []).filter(key => config.systems[key]),\n      orderByPriority = [...priority, ...Object.keys(config.systems).filter(key => !priority.includes(key))];\n    for (const i of orderByPriority) this.addSystem(config.systems[i], i);\n  }\n  /**\n   * Create a bunch of runners based of a collection of ids\n   * @param runnerIds - the runner ids to add\n   */\n  addRunners(...runnerIds) {\n    runnerIds.forEach(runnerId => {\n      this.runners[runnerId] = new Runner(runnerId);\n    });\n  }\n  /**\n   * Add a new system to the renderer.\n   * @param ClassRef - Class reference\n   * @param name - Property name for system, if not specified\n   *        will use a static `name` property on the class itself. This\n   *        name will be assigned as s property on the Renderer so make\n   *        sure it doesn't collide with properties on Renderer.\n   * @returns Return instance of renderer\n   */\n  addSystem(ClassRef, name) {\n    const system = new ClassRef(this);\n    if (this[name]) throw new Error(`Whoops! The name \"${name}\" is already in use`);\n    this[name] = system, this._systemsHash[name] = system;\n    for (const i in this.runners) this.runners[i].add(system);\n    return this;\n  }\n  /**\n   * A function that will run a runner and call the runners function but pass in different options\n   * to each system based on there name.\n   *\n   * E.g. If you have two systems added called `systemA` and `systemB` you could call do the following:\n   *\n   * ```js\n   * system.emitWithCustomOptions(init, {\n   *     systemA: {...optionsForA},\n   *     systemB: {...optionsForB},\n   * });\n   * ```\n   *\n   * `init` would be called on system A passing `optionsForA` and on system B passing `optionsForB`.\n   * @param runner - the runner to target\n   * @param options - key value options for each system\n   */\n  emitWithCustomOptions(runner, options) {\n    const systemHashKeys = Object.keys(this._systemsHash);\n    runner.items.forEach(system => {\n      const systemName = systemHashKeys.find(systemId => this._systemsHash[systemId] === system);\n      system[runner.name](options[systemName]);\n    });\n  }\n  /** destroy the all runners and systems. Its apps job to */\n  destroy() {\n    Object.values(this.runners).forEach(runner => {\n      runner.destroy();\n    }), this._systemsHash = {};\n  }\n  // TODO implement!\n  // removeSystem(ClassRef: ISystemConstructor, name: string): void\n  // {\n  // }\n}\nexport { SystemManager };", "map": {"version": 3, "names": ["SystemManager", "EventEmitter", "constructor", "arguments", "runners", "_systemsHash", "setup", "config", "addRunners", "priority", "filter", "key", "systems", "orderByPriority", "Object", "keys", "includes", "i", "addSystem", "runnerIds", "for<PERSON>ach", "runnerId", "Runner", "ClassRef", "name", "system", "Error", "add", "emitWithCustomOptions", "runner", "options", "systemHashKeys", "items", "systemName", "find", "systemId", "destroy", "values"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\system\\SystemManager.ts"], "sourcesContent": ["import { Runner } from '@pixi/runner';\nimport { EventEmitter } from '@pixi/utils';\n\nimport type { <PERSON><PERSON><PERSON><PERSON> } from '../IRenderer';\nimport type { ISystem, ISystemConstructor } from './ISystem';\n\ninterface ISystemConfig<R>\n{\n    runners: string[],\n    systems: Record<string, ISystemConstructor<R>>\n    priority: string[];\n}\n\n/**\n * The SystemManager is a class that provides functions for managing a set of systems\n * This is a base class, that is generic (no render code or knowledge at all)\n * @memberof PIXI\n */\nexport class SystemManager<R=IRenderer> extends EventEmitter\n{\n    /** a collection of runners defined by the user */\n    readonly runners: {[key: string]: Runner} = {};\n\n    private _systemsHash: Record<string, ISystem> = {};\n\n    /**\n     * Set up a system with a collection of SystemClasses and runners.\n     * Systems are attached dynamically to this class when added.\n     * @param config - the config for the system manager\n     */\n    setup(config: ISystemConfig<R>): void\n    {\n        this.addRunners(...config.runners);\n\n        // Remove keys that aren't available\n        const priority = (config.priority ?? []).filter((key) => config.systems[key]);\n\n        // Order the systems by priority\n        const orderByPriority = [\n            ...priority,\n            ...Object.keys(config.systems)\n                .filter((key) => !priority.includes(key))\n        ];\n\n        for (const i of orderByPriority)\n        {\n            this.addSystem(config.systems[i], i);\n        }\n    }\n\n    /**\n     * Create a bunch of runners based of a collection of ids\n     * @param runnerIds - the runner ids to add\n     */\n    addRunners(...runnerIds: string[]): void\n    {\n        runnerIds.forEach((runnerId) =>\n        {\n            this.runners[runnerId] = new Runner(runnerId);\n        });\n    }\n\n    /**\n     * Add a new system to the renderer.\n     * @param ClassRef - Class reference\n     * @param name - Property name for system, if not specified\n     *        will use a static `name` property on the class itself. This\n     *        name will be assigned as s property on the Renderer so make\n     *        sure it doesn't collide with properties on Renderer.\n     * @returns Return instance of renderer\n     */\n    addSystem(ClassRef: ISystemConstructor<R>, name: string): this\n    {\n        const system = new ClassRef(this as any as R);\n\n        if ((this as any)[name])\n        {\n            throw new Error(`Whoops! The name \"${name}\" is already in use`);\n        }\n\n        (this as any)[name] = system;\n\n        this._systemsHash[name] = system;\n\n        for (const i in this.runners)\n        {\n            this.runners[i].add(system);\n        }\n\n        /**\n         * Fired after rendering finishes.\n         * @event PIXI.Renderer#postrender\n         */\n\n        /**\n         * Fired before rendering starts.\n         * @event PIXI.Renderer#prerender\n         */\n\n        /**\n         * Fired when the WebGL context is set.\n         * @event PIXI.Renderer#context\n         * @param {WebGLRenderingContext} gl - WebGL context.\n         */\n\n        return this;\n    }\n\n    /**\n     * A function that will run a runner and call the runners function but pass in different options\n     * to each system based on there name.\n     *\n     * E.g. If you have two systems added called `systemA` and `systemB` you could call do the following:\n     *\n     * ```js\n     * system.emitWithCustomOptions(init, {\n     *     systemA: {...optionsForA},\n     *     systemB: {...optionsForB},\n     * });\n     * ```\n     *\n     * `init` would be called on system A passing `optionsForA` and on system B passing `optionsForB`.\n     * @param runner - the runner to target\n     * @param options - key value options for each system\n     */\n    emitWithCustomOptions(runner: Runner, options: Record<string, unknown>): void\n    {\n        const systemHashKeys = Object.keys(this._systemsHash);\n\n        runner.items.forEach((system) =>\n        {\n            // I know this does not need to be a performant function so it.. isn't!\n            // its only used for init and destroy.. we can refactor if required..\n            const systemName = systemHashKeys.find((systemId) => this._systemsHash[systemId] === system);\n\n            system[runner.name](options[systemName]);\n        });\n    }\n\n    /** destroy the all runners and systems. Its apps job to */\n    destroy(): void\n    {\n        Object.values(this.runners).forEach((runner) =>\n        {\n            runner.destroy();\n        });\n\n        this._systemsHash = {};\n    }\n\n    // TODO implement!\n    // removeSystem(ClassRef: ISystemConstructor, name: string): void\n    // {\n\n    // }\n}\n"], "mappings": ";;AAkBO,MAAMA,aAAA,SAAmCC,YAAA,CAChD;EADOC,YAAA;IAAA,SAAAC,SAAA,GAGH,KAASC,OAAA,GAAmC,IAE5C,KAAQC,YAAA,GAAwC;EAAC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOjDC,MAAMC,MAAA,EACN;IACS,KAAAC,UAAA,CAAW,GAAGD,MAAA,CAAOH,OAAO;IAGjC,MAAMK,QAAA,IAAYF,MAAA,CAAOE,QAAA,IAAY,EAAI,EAAAC,MAAA,CAAQC,GAAA,IAAQJ,MAAA,CAAOK,OAAA,CAAQD,GAAG,CAAC;MAGtEE,eAAA,GAAkB,CACpB,GAAGJ,QAAA,EACH,GAAGK,MAAA,CAAOC,IAAA,CAAKR,MAAA,CAAOK,OAAO,EACxBF,MAAA,CAAQC,GAAA,IAAQ,CAACF,QAAA,CAASO,QAAA,CAASL,GAAG,CAAC;IAGhD,WAAWM,CAAA,IAAKJ,eAAA,EAEZ,KAAKK,SAAA,CAAUX,MAAA,CAAOK,OAAA,CAAQK,CAAC,GAAGA,CAAC;EAE3C;EAAA;AAAA;AAAA;AAAA;EAMAT,WAAA,GAAcW,SAAA,EACd;IACcA,SAAA,CAAAC,OAAA,CAASC,QAAA,IACnB;MACI,KAAKjB,OAAA,CAAQiB,QAAQ,IAAI,IAAIC,MAAA,CAAOD,QAAQ;IAAA,CAC/C;EACL;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWAH,UAAUK,QAAA,EAAiCC,IAAA,EAC3C;IACU,MAAAC,MAAA,GAAS,IAAIF,QAAA,CAAS,IAAgB;IAE5C,IAAK,KAAaC,IAAI,GAElB,MAAM,IAAIE,KAAA,CAAM,qBAAqBF,IAAI,qBAAqB;IAGjE,KAAaA,IAAI,IAAIC,MAAA,EAEtB,KAAKpB,YAAA,CAAamB,IAAI,IAAIC,MAAA;IAE1B,WAAWR,CAAA,IAAK,KAAKb,OAAA,EAEjB,KAAKA,OAAA,CAAQa,CAAC,EAAEU,GAAA,CAAIF,MAAM;IAmBvB;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAmBAG,sBAAsBC,MAAA,EAAgBC,OAAA,EACtC;IACI,MAAMC,cAAA,GAAiBjB,MAAA,CAAOC,IAAA,CAAK,KAAKV,YAAY;IAE7CwB,MAAA,CAAAG,KAAA,CAAMZ,OAAA,CAASK,MAAA,IACtB;MAGU,MAAAQ,UAAA,GAAaF,cAAA,CAAeG,IAAA,CAAMC,QAAA,IAAa,KAAK9B,YAAA,CAAa8B,QAAQ,MAAMV,MAAM;MAE3FA,MAAA,CAAOI,MAAA,CAAOL,IAAI,EAAEM,OAAA,CAAQG,UAAU,CAAC;IAAA,CAC1C;EACL;EAAA;EAGAG,QAAA,EACA;IACItB,MAAA,CAAOuB,MAAA,CAAO,KAAKjC,OAAO,EAAEgB,OAAA,CAASS,MAAA,IACrC;MACIA,MAAA,CAAOO,OAAA,CAAQ;IAAA,CAClB,GAED,KAAK/B,YAAA,GAAe;EACxB;EAAA;EAAA;EAAA;EAAA;AAOJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}