{"ast": null, "code": "class BatchTextureArray {\n  constructor() {\n    this.elements = [], this.ids = [], this.count = 0;\n  }\n  clear() {\n    for (let i = 0; i < this.count; i++) this.elements[i] = null;\n    this.count = 0;\n  }\n}\nexport { BatchTextureArray };", "map": {"version": 3, "names": ["BatchTextureArray", "constructor", "elements", "ids", "count", "clear", "i"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\batch\\BatchTextureArray.ts"], "sourcesContent": ["import type { BaseTexture } from '../textures/BaseTexture';\n\n/**\n * Used by the batcher to build texture batches.\n * Holds list of textures and their respective locations.\n * @memberof PIXI\n */\nexport class BatchTextureArray\n{\n    /** Inside textures array. */\n    public elements: BaseTexture[];\n\n    /** Respective locations for textures. */\n    public ids: number[];\n\n    /** Number of filled elements. */\n    public count: number;\n\n    constructor()\n    {\n        this.elements = [];\n        this.ids = [];\n        this.count = 0;\n    }\n\n    clear(): void\n    {\n        for (let i = 0; i < this.count; i++)\n        {\n            this.elements[i] = null;\n        }\n        this.count = 0;\n    }\n}\n"], "mappings": "AAOO,MAAMA,iBAAA,CACb;EAUIC,YAAA,EACA;IACS,KAAAC,QAAA,GAAW,IAChB,KAAKC,GAAA,GAAM,IACX,KAAKC,KAAA,GAAQ;EACjB;EAEAC,MAAA,EACA;IACI,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKF,KAAA,EAAOE,CAAA,IAEvB,KAAAJ,QAAA,CAASI,CAAC,IAAI;IAEvB,KAAKF,KAAA,GAAQ;EACjB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}