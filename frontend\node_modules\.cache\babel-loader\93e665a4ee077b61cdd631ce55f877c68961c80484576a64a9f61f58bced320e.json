{"ast": null, "code": "var LoaderParserPriority = /* @__PURE__ */(LoaderParserPriority2 => (LoaderParserPriority2[LoaderParserPriority2.Low = 0] = \"Low\", LoaderParserPriority2[LoaderParserPriority2.Normal = 1] = \"Normal\", LoaderParserPriority2[LoaderParserPriority2.High = 2] = \"High\", LoaderParserPriority2))(LoaderParserPriority || {});\nexport { LoaderParserPriority };", "map": {"version": 3, "names": ["LoaderParserPriority", "LoaderParserPriority2", "Low", "Normal", "High"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\assets\\src\\loader\\parsers\\LoaderParser.ts"], "sourcesContent": ["import type { ExtensionMetadata } from '@pixi/core';\nimport type { ResolvedAsset } from '../../types';\nimport type { Loader } from '../Loader';\n\n/**\n * The extension priority for loader parsers.\n * Helpful when managing multiple parsers that share the same extension test.\n * The higher priority parsers will be checked first.\n * @memberof PIXI\n * @enum {number}\n */\nexport enum LoaderParserPriority\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    /** Generic parsers: txt, json, webfonts */\n    Low = 0,\n    /** PixiJS assets with generic extensions: spritesheets, bitmapfonts  */\n    Normal = 1,\n    /** Specific texture types: svg, png, ktx, dds, basis */\n    High = 2,\n}\n\n/**\n * All functions are optional here. The flow:\n *\n * for every asset,\n *\n * 1. `parser.test()`: Test the asset url.\n * 2. `parser.load()`: If test passes call the load function with the url\n * 3. `parser.testParse()`: Test to see if the asset should be parsed by the plugin\n * 4. `parse.parse()`: If test is parsed, then run the parse function on the asset.\n *\n * some plugins may only be used for parsing,\n * some only for loading\n * and some for both!\n * @memberof PIXI\n */\nexport interface LoaderParser<ASSET = any, META_DATA = any, CONFIG = Record<string, any>>\n{\n    extension?: ExtensionMetadata;\n\n    /** A config to adjust the parser */\n    config?: CONFIG;\n\n    /** The name of the parser (this can be used when specifying loadParser in a ResolvedAsset) */\n    name?: string;\n\n    /**\n     * each URL to load will be tested here,\n     * if the test is passed the assets are loaded using the load function below.\n     * Good place to test for things like file extensions!\n     * @param url - The URL to test\n     * @param resolvedAsset - Any custom additional information relevant to the asset being loaded\n     * @param loader - The loader instance\n     */\n    test?: (url: string, resolvedAsset?: ResolvedAsset<META_DATA>, loader?: Loader) => boolean;\n\n    /**\n     * This is the promise that loads the URL provided\n     * resolves with a loaded asset if returned by the parser.\n     * @param url - The URL to load\n     * @param resolvedAsset - Any custom additional information relevant to the asset being loaded\n     * @param loader - The loader instance\n     */\n    load?: <T>(url: string, resolvedAsset?: ResolvedAsset<META_DATA>, loader?: Loader) => Promise<T>;\n\n    /**\n     * This function is used to test if the parse function should be run on the asset\n     * If this returns true then parse is called with the asset\n     * @param asset - The loaded asset data\n     * @param resolvedAsset - Any custom additional information relevant to the asset being loaded\n     * @param loader - The loader instance\n     */\n    testParse?: (asset: ASSET, resolvedAsset?: ResolvedAsset<META_DATA>, loader?: Loader) => Promise<boolean>;\n\n    /**\n     * Gets called on the asset it testParse passes. Useful to convert a raw asset into something more useful than\n     * @param asset - The loaded asset data\n     * @param resolvedAsset - Any custom additional information relevant to the asset being loaded\n     * @param loader - The loader instance\n     */\n    parse?: <T>(asset: ASSET, resolvedAsset?: ResolvedAsset<META_DATA>, loader?: Loader) => Promise<T>;\n\n    /**\n     * If an asset is parsed using this parser, the unload function will be called when the user requests an asset\n     * to be unloaded. This is useful for things like sounds or textures that can be unloaded from memory\n     * @param asset - The asset to unload/destroy\n     * @param resolvedAsset - Any custom additional information relevant to the asset being loaded\n     * @param loader - The loader instance\n     */\n    unload?: (asset: ASSET, resolvedAsset?: ResolvedAsset<META_DATA>, loader?: Loader) => void;\n}\n"], "mappings": "AAWO,IAAKA,oBAAA,GAAL,gBAAKC,qBAAA,KAIRA,qBAAA,CAAAA,qBAAA,CAAAC,GAAA,GAAM,CAAN,WAEAD,qBAAA,CAAAA,qBAAA,CAAAE,MAAA,GAAS,KAAT,UAEAF,qBAAA,CAAAA,qBAAA,CAAAG,IAAA,GAAO,KAAP,QARQH,qBAAA,GAAAD,oBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}