{"ast": null, "code": "import { LoaderParserPriority, copySearchParams } from \"@pixi/assets\";\nimport { utils, ExtensionType, settings, extensions } from \"@pixi/core\";\nimport { Spritesheet } from \"./Spritesheet.mjs\";\nconst validImages = [\"jpg\", \"png\", \"jpeg\", \"avif\", \"webp\", \"s3tc\", \"s3tc_sRGB\", \"etc\", \"etc1\", \"pvrtc\", \"atc\", \"astc\", \"bptc\"];\nfunction getCacheableAssets(keys, asset, ignoreMultiPack) {\n  const out = {};\n  if (keys.forEach(key => {\n    out[key] = asset;\n  }), Object.keys(asset.textures).forEach(key => {\n    out[`${asset.cachePrefix}${key}`] = asset.textures[key];\n  }), !ignoreMultiPack) {\n    const basePath = utils.path.dirname(keys[0]);\n    asset.linkedSheets.forEach((item, i) => {\n      Object.assign(out, getCacheableAssets([`${basePath}/${asset.data.meta.related_multi_packs[i]}`], item, !0));\n    });\n  }\n  return out;\n}\nconst spritesheetAsset = {\n  extension: ExtensionType.Asset,\n  /** Handle the caching of the related Spritesheet Textures */\n  cache: {\n    test: asset => asset instanceof Spritesheet,\n    getCacheableAssets: (keys, asset) => getCacheableAssets(keys, asset, !1)\n  },\n  /** Resolve the the resolution of the asset. */\n  resolver: {\n    test: value => {\n      const split = value.split(\"?\")[0].split(\".\"),\n        extension = split.pop(),\n        format = split.pop();\n      return extension === \"json\" && validImages.includes(format);\n    },\n    parse: value => {\n      const split = value.split(\".\");\n      return {\n        resolution: parseFloat(settings.RETINA_PREFIX.exec(value)?.[1] ?? \"1\"),\n        format: split[split.length - 2],\n        src: value\n      };\n    }\n  },\n  /**\n   * Loader plugin that parses sprite sheets!\n   * once the JSON has been loaded this checks to see if the JSON is spritesheet data.\n   * If it is, we load the spritesheets image and parse the data into PIXI.Spritesheet\n   * All textures in the sprite sheet are then added to the cache\n   * @ignore\n   */\n  loader: {\n    name: \"spritesheetLoader\",\n    extension: {\n      type: ExtensionType.LoadParser,\n      priority: LoaderParserPriority.Normal\n    },\n    async testParse(asset, options) {\n      return utils.path.extname(options.src).toLowerCase() === \".json\" && !!asset.frames;\n    },\n    async parse(asset, options, loader) {\n      const {\n        texture: imageTexture,\n        // if user need to use preloaded texture\n        imageFilename,\n        // if user need to use custom filename (not from jsonFile.meta.image)\n        cachePrefix\n        // if user need to use custom cache prefix\n      } = options?.data ?? {};\n      let basePath = utils.path.dirname(options.src);\n      basePath && basePath.lastIndexOf(\"/\") !== basePath.length - 1 && (basePath += \"/\");\n      let texture;\n      if (imageTexture && imageTexture.baseTexture) texture = imageTexture;else {\n        const imagePath = copySearchParams(basePath + (imageFilename ?? asset.meta.image), options.src);\n        texture = (await loader.load([imagePath]))[imagePath];\n      }\n      const spritesheet = new Spritesheet({\n        texture: texture.baseTexture,\n        data: asset,\n        resolutionFilename: options.src,\n        cachePrefix\n      });\n      await spritesheet.parse();\n      const multiPacks = asset?.meta?.related_multi_packs;\n      if (Array.isArray(multiPacks)) {\n        const promises = [];\n        for (const item of multiPacks) {\n          if (typeof item != \"string\") continue;\n          let itemUrl = basePath + item;\n          options.data?.ignoreMultiPack || (itemUrl = copySearchParams(itemUrl, options.src), promises.push(loader.load({\n            src: itemUrl,\n            data: {\n              ignoreMultiPack: !0\n            }\n          })));\n        }\n        const res = await Promise.all(promises);\n        spritesheet.linkedSheets = res, res.forEach(item => {\n          item.linkedSheets = [spritesheet].concat(spritesheet.linkedSheets.filter(sp => sp !== item));\n        });\n      }\n      return spritesheet;\n    },\n    unload(spritesheet) {\n      spritesheet.destroy(!0);\n    }\n  }\n};\nextensions.add(spritesheetAsset);\nexport { spritesheetAsset };", "map": {"version": 3, "names": ["validImages", "getCacheableAssets", "keys", "asset", "ignoreMultiPack", "out", "for<PERSON>ach", "key", "Object", "textures", "cachePrefix", "basePath", "utils", "path", "dirname", "linkedSheets", "item", "i", "assign", "data", "meta", "related_multi_packs", "spritesheetAsset", "extension", "ExtensionType", "<PERSON><PERSON>", "cache", "test", "Spritesheet", "resolver", "value", "split", "pop", "format", "includes", "parse", "resolution", "parseFloat", "settings", "RETINA_PREFIX", "exec", "length", "src", "loader", "name", "type", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "priority", "LoaderParserPriority", "Normal", "test<PERSON><PERSON>e", "options", "extname", "toLowerCase", "frames", "texture", "imageTexture", "imageFilename", "lastIndexOf", "baseTexture", "imagePath", "copySearchParams", "image", "load", "spritesheet", "resolutionFilename", "multiPacks", "Array", "isArray", "promises", "itemUrl", "push", "res", "Promise", "all", "concat", "filter", "sp", "unload", "destroy", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\spritesheet\\src\\spritesheetAsset.ts"], "sourcesContent": ["import { copySearchPara<PERSON>, LoaderParserPriority } from '@pixi/assets';\nimport { extensions, ExtensionType, settings, utils } from '@pixi/core';\nimport { Spritesheet } from './Spritesheet';\n\nimport type { AssetExtension, Loader, ResolvedAsset, UnresolvedAsset } from '@pixi/assets';\nimport type { Texture } from '@pixi/core';\nimport type { ISpritesheetData } from './Spritesheet';\n\nexport interface SpriteSheet<PERSON>son extends ISpritesheetData\n{\n    meta: {\n        image: string;\n        scale: string;\n        // eslint-disable-next-line camelcase\n        related_multi_packs?: string[];\n    };\n}\n\nconst validImages = ['jpg', 'png', 'jpeg', 'avif', 'webp',\n    's3tc', 's3tc_sRGB', 'etc', 'etc1', 'pvrtc', 'atc', 'astc', 'bptc'];\n\nfunction getCacheableAssets(keys: string[], asset: Spritesheet, ignoreMultiPack: boolean)\n{\n    const out: Record<string, Texture | Spritesheet> = {};\n\n    keys.forEach((key: string) =>\n    {\n        out[key] = asset;\n    });\n\n    Object.keys(asset.textures).forEach((key) =>\n    {\n        out[`${asset.cachePrefix}${key}`] = asset.textures[key];\n    });\n\n    if (!ignoreMultiPack)\n    {\n        const basePath = utils.path.dirname(keys[0]);\n\n        asset.linkedSheets.forEach((item: Spritesheet, i) =>\n        {\n            Object.assign(out, getCacheableAssets(\n                [`${basePath}/${asset.data.meta.related_multi_packs[i]}`],\n                item,\n                true\n            ));\n        });\n    }\n\n    return out;\n}\n\n/**\n * Asset extension for loading spritesheets.\n * @memberof PIXI\n * @type {PIXI.AssetExtension}\n */\nexport const spritesheetAsset = {\n    extension: ExtensionType.Asset,\n    /** Handle the caching of the related Spritesheet Textures */\n    cache: {\n        test: (asset: Spritesheet) => asset instanceof Spritesheet,\n        getCacheableAssets: (keys: string[], asset: Spritesheet) => getCacheableAssets(keys, asset, false),\n    },\n    /** Resolve the the resolution of the asset. */\n    resolver: {\n        test: (value: string): boolean =>\n        {\n            const tempURL = value.split('?')[0];\n            const split = tempURL.split('.');\n            const extension = split.pop();\n            const format = split.pop();\n\n            return extension === 'json' && validImages.includes(format);\n        },\n        parse: (value: string): UnresolvedAsset =>\n        {\n            const split = value.split('.');\n\n            return {\n                resolution: parseFloat(settings.RETINA_PREFIX.exec(value)?.[1] ?? '1'),\n                format: split[split.length - 2],\n                src: value,\n            };\n        },\n    },\n    /**\n     * Loader plugin that parses sprite sheets!\n     * once the JSON has been loaded this checks to see if the JSON is spritesheet data.\n     * If it is, we load the spritesheets image and parse the data into PIXI.Spritesheet\n     * All textures in the sprite sheet are then added to the cache\n     * @ignore\n     */\n    loader: {\n        name: 'spritesheetLoader',\n\n        extension: {\n            type: ExtensionType.LoadParser,\n            priority: LoaderParserPriority.Normal,\n        },\n\n        async testParse(asset: SpriteSheetJson, options: ResolvedAsset): Promise<boolean>\n        {\n            return (utils.path.extname(options.src).toLowerCase() === '.json' && !!asset.frames);\n        },\n\n        async parse(asset: SpriteSheetJson, options: ResolvedAsset, loader: Loader): Promise<Spritesheet>\n        {\n            const {\n                texture: imageTexture, // if user need to use preloaded texture\n                imageFilename, // if user need to use custom filename (not from jsonFile.meta.image)\n                cachePrefix, // if user need to use custom cache prefix\n            } = options?.data ?? {};\n\n            let basePath = utils.path.dirname(options.src);\n\n            if (basePath && basePath.lastIndexOf('/') !== (basePath.length - 1))\n            {\n                basePath += '/';\n            }\n\n            let texture: Texture;\n\n            if (imageTexture && imageTexture.baseTexture)\n            {\n                texture = imageTexture;\n            }\n            else\n            {\n                const imagePath = copySearchParams(basePath + (imageFilename ?? asset.meta.image), options.src);\n\n                const assets = await loader.load<Texture>([imagePath]);\n\n                texture = assets[imagePath];\n            }\n\n            const spritesheet = new Spritesheet({\n                texture: texture.baseTexture,\n                data: asset,\n                resolutionFilename: options.src,\n                cachePrefix,\n            });\n\n            await spritesheet.parse();\n\n            // Check and add the multi atlas\n            // Heavily influenced and based on https://github.com/rocket-ua/pixi-tps-loader/blob/master/src/ResourceLoader.js\n            // eslint-disable-next-line camelcase\n            const multiPacks = asset?.meta?.related_multi_packs;\n\n            if (Array.isArray(multiPacks))\n            {\n                const promises: Promise<Spritesheet<SpriteSheetJson>>[] = [];\n\n                for (const item of multiPacks)\n                {\n                    if (typeof item !== 'string')\n                    {\n                        continue;\n                    }\n\n                    let itemUrl = basePath + item;\n\n                    // Check if the file wasn't already added as multipack\n                    if (options.data?.ignoreMultiPack)\n                    {\n                        continue;\n                    }\n\n                    itemUrl = copySearchParams(itemUrl, options.src);\n\n                    promises.push(loader.load<Spritesheet<SpriteSheetJson>>({\n                        src: itemUrl,\n                        data: {\n                            ignoreMultiPack: true,\n                        }\n                    }));\n                }\n\n                const res = await Promise.all(promises);\n\n                spritesheet.linkedSheets = res;\n                res.forEach((item) =>\n                {\n                    item.linkedSheets = [spritesheet].concat(spritesheet.linkedSheets.filter((sp) => (sp !== item)));\n                });\n            }\n\n            return spritesheet;\n        },\n\n        unload(spritesheet: Spritesheet)\n        {\n            spritesheet.destroy(true);\n        },\n    },\n} as AssetExtension<Spritesheet | SpriteSheetJson>;\n\nextensions.add(spritesheetAsset);\n"], "mappings": ";;;AAkBA,MAAMA,WAAA,GAAc,CAAC,OAAO,OAAO,QAAQ,QAAQ,QAC/C,QAAQ,aAAa,OAAO,QAAQ,SAAS,OAAO,QAAQ,OAAM;AAEtE,SAASC,mBAAmBC,IAAA,EAAgBC,KAAA,EAAoBC,eAAA,EAChE;EACI,MAAMC,GAAA,GAA6C;EAEnD,IAAAH,IAAA,CAAKI,OAAA,CAASC,GAAA,IACd;IACIF,GAAA,CAAIE,GAAG,IAAIJ,KAAA;EAAA,CACd,GAEDK,MAAA,CAAON,IAAA,CAAKC,KAAA,CAAMM,QAAQ,EAAEH,OAAA,CAASC,GAAA,IACrC;IACQF,GAAA,IAAGF,KAAA,CAAMO,WAAW,GAAGH,GAAG,EAAE,IAAIJ,KAAA,CAAMM,QAAA,CAASF,GAAG;EAAA,CACzD,GAEG,CAACH,eAAA,EACL;IACI,MAAMO,QAAA,GAAWC,KAAA,CAAMC,IAAA,CAAKC,OAAA,CAAQZ,IAAA,CAAK,CAAC,CAAC;IAE3CC,KAAA,CAAMY,YAAA,CAAaT,OAAA,CAAQ,CAACU,IAAA,EAAmBC,CAAA,KAC/C;MACIT,MAAA,CAAOU,MAAA,CAAOb,GAAA,EAAKJ,kBAAA,CACf,CAAC,GAAGU,QAAQ,IAAIR,KAAA,CAAMgB,IAAA,CAAKC,IAAA,CAAKC,mBAAA,CAAoBJ,CAAC,CAAC,EAAE,GACxDD,IAAA,EACA,GACH;IAAA,CACJ;EACL;EAEO,OAAAX,GAAA;AACX;AAOO,MAAMiB,gBAAA,GAAmB;EAC5BC,SAAA,EAAWC,aAAA,CAAcC,KAAA;EAAA;EAEzBC,KAAA,EAAO;IACHC,IAAA,EAAOxB,KAAA,IAAuBA,KAAA,YAAiByB,WAAA;IAC/C3B,kBAAA,EAAoBA,CAACC,IAAA,EAAgBC,KAAA,KAAuBF,kBAAA,CAAmBC,IAAA,EAAMC,KAAA,EAAO,EAAK;EACrG;EAAA;EAEA0B,QAAA,EAAU;IACNF,IAAA,EAAOG,KAAA,IACP;MAEI,MAAMC,KAAA,GADUD,KAAA,CAAMC,KAAA,CAAM,GAAG,EAAE,CAAC,EACZA,KAAA,CAAM,GAAG;QACzBR,SAAA,GAAYQ,KAAA,CAAMC,GAAA;QAClBC,MAAA,GAASF,KAAA,CAAMC,GAAA;MAErB,OAAOT,SAAA,KAAc,UAAUvB,WAAA,CAAYkC,QAAA,CAASD,MAAM;IAC9D;IACAE,KAAA,EAAQL,KAAA,IACR;MACU,MAAAC,KAAA,GAAQD,KAAA,CAAMC,KAAA,CAAM,GAAG;MAEtB;QACHK,UAAA,EAAYC,UAAA,CAAWC,QAAA,CAASC,aAAA,CAAcC,IAAA,CAAKV,KAAK,IAAI,CAAC,KAAK,GAAG;QACrEG,MAAA,EAAQF,KAAA,CAAMA,KAAA,CAAMU,MAAA,GAAS,CAAC;QAC9BC,GAAA,EAAKZ;MAAA;IAEb;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAa,MAAA,EAAQ;IACJC,IAAA,EAAM;IAENrB,SAAA,EAAW;MACPsB,IAAA,EAAMrB,aAAA,CAAcsB,UAAA;MACpBC,QAAA,EAAUC,oBAAA,CAAqBC;IACnC;IAEA,MAAMC,UAAU/C,KAAA,EAAwBgD,OAAA,EACxC;MACY,OAAAvC,KAAA,CAAMC,IAAA,CAAKuC,OAAA,CAAQD,OAAA,CAAQT,GAAG,EAAEW,WAAA,OAAkB,WAAW,CAAC,CAAClD,KAAA,CAAMmD,MAAA;IACjF;IAEA,MAAMnB,MAAMhC,KAAA,EAAwBgD,OAAA,EAAwBR,MAAA,EAC5D;MACU;QACFY,OAAA,EAASC,YAAA;QAAA;QACTC,aAAA;QAAA;QACA/C;QAAA;MAAA,IACAyC,OAAA,EAAShC,IAAA,IAAQ;MAErB,IAAIR,QAAA,GAAWC,KAAA,CAAMC,IAAA,CAAKC,OAAA,CAAQqC,OAAA,CAAQT,GAAG;MAEzC/B,QAAA,IAAYA,QAAA,CAAS+C,WAAA,CAAY,GAAG,MAAO/C,QAAA,CAAS8B,MAAA,GAAS,MAE7D9B,QAAA,IAAY;MAGZ,IAAA4C,OAAA;MAEJ,IAAIC,YAAA,IAAgBA,YAAA,CAAaG,WAAA,EAEnBJ,OAAA,GAAAC,YAAA,MAGd;QACU,MAAAI,SAAA,GAAYC,gBAAA,CAAiBlD,QAAA,IAAY8C,aAAA,IAAiBtD,KAAA,CAAMiB,IAAA,CAAK0C,KAAA,GAAQX,OAAA,CAAQT,GAAG;QAI9Fa,OAAA,IAFe,MAAMZ,MAAA,CAAOoB,IAAA,CAAc,CAACH,SAAS,CAAC,GAEpCA,SAAS;MAC9B;MAEM,MAAAI,WAAA,GAAc,IAAIpC,WAAA,CAAY;QAChC2B,OAAA,EAASA,OAAA,CAAQI,WAAA;QACjBxC,IAAA,EAAMhB,KAAA;QACN8D,kBAAA,EAAoBd,OAAA,CAAQT,GAAA;QAC5BhC;MAAA,CACH;MAED,MAAMsD,WAAA,CAAY7B,KAAA;MAKZ,MAAA+B,UAAA,GAAa/D,KAAA,EAAOiB,IAAA,EAAMC,mBAAA;MAE5B,IAAA8C,KAAA,CAAMC,OAAA,CAAQF,UAAU,GAC5B;QACI,MAAMG,QAAA,GAAoD;QAE1D,WAAWrD,IAAA,IAAQkD,UAAA,EACnB;UACI,IAAI,OAAOlD,IAAA,IAAS,UAEhB;UAGJ,IAAIsD,OAAA,GAAU3D,QAAA,GAAWK,IAAA;UAGrBmC,OAAA,CAAQhC,IAAA,EAAMf,eAAA,KAKlBkE,OAAA,GAAUT,gBAAA,CAAiBS,OAAA,EAASnB,OAAA,CAAQT,GAAG,GAE/C2B,QAAA,CAASE,IAAA,CAAK5B,MAAA,CAAOoB,IAAA,CAAmC;YACpDrB,GAAA,EAAK4B,OAAA;YACLnD,IAAA,EAAM;cACFf,eAAA,EAAiB;YACrB;UACH,EAAC;QACN;QAEA,MAAMoE,GAAA,GAAM,MAAMC,OAAA,CAAQC,GAAA,CAAIL,QAAQ;QAEtCL,WAAA,CAAYjD,YAAA,GAAeyD,GAAA,EAC3BA,GAAA,CAAIlE,OAAA,CAASU,IAAA,IACb;UACIA,IAAA,CAAKD,YAAA,GAAe,CAACiD,WAAW,EAAEW,MAAA,CAAOX,WAAA,CAAYjD,YAAA,CAAa6D,MAAA,CAAQC,EAAA,IAAQA,EAAA,KAAO7D,IAAK,CAAC;QAAA,CAClG;MACL;MAEO,OAAAgD,WAAA;IACX;IAEAc,OAAOd,WAAA,EACP;MACIA,WAAA,CAAYe,OAAA,CAAQ,EAAI;IAC5B;EACJ;AACJ;AAEAC,UAAA,CAAWC,GAAA,CAAI3D,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}