{"ast": null, "code": "import { MSAA_QUALITY, SCALE_MODES, MIPMAP_MODES, FORMATS, TYPES } from \"@pixi/constants\";\nimport { Runner } from \"@pixi/runner\";\nimport { BaseTexture } from \"../textures/BaseTexture.mjs\";\nclass Framebuffer {\n  /**\n   * @param width - Width of the frame buffer\n   * @param height - Height of the frame buffer\n   */\n  constructor(width, height) {\n    if (this.width = Math.round(width), this.height = Math.round(height), !this.width || !this.height) throw new Error(\"Framebuffer width or height is zero\");\n    this.stencil = !1, this.depth = !1, this.dirtyId = 0, this.dirtyFormat = 0, this.dirtySize = 0, this.depthTexture = null, this.colorTextures = [], this.glFramebuffers = {}, this.disposeRunner = new Runner(\"disposeFramebuffer\"), this.multisample = MSAA_QUALITY.NONE;\n  }\n  /**\n   * Reference to the colorTexture.\n   * @readonly\n   */\n  get colorTexture() {\n    return this.colorTextures[0];\n  }\n  /**\n   * Add texture to the colorTexture array.\n   * @param index - Index of the array to add the texture to\n   * @param texture - Texture to add to the array\n   */\n  addColorTexture(index = 0, texture) {\n    return this.colorTextures[index] = texture || new BaseTexture(null, {\n      scaleMode: SCALE_MODES.NEAREST,\n      resolution: 1,\n      mipmap: MIPMAP_MODES.OFF,\n      width: this.width,\n      height: this.height\n    }), this.dirtyId++, this.dirtyFormat++, this;\n  }\n  /**\n   * Add a depth texture to the frame buffer.\n   * @param texture - Texture to add.\n   */\n  addDepthTexture(texture) {\n    return this.depthTexture = texture || new BaseTexture(null, {\n      scaleMode: SCALE_MODES.NEAREST,\n      resolution: 1,\n      width: this.width,\n      height: this.height,\n      mipmap: MIPMAP_MODES.OFF,\n      format: FORMATS.DEPTH_COMPONENT,\n      type: TYPES.UNSIGNED_SHORT\n    }), this.dirtyId++, this.dirtyFormat++, this;\n  }\n  /** Enable depth on the frame buffer. */\n  enableDepth() {\n    return this.depth = !0, this.dirtyId++, this.dirtyFormat++, this;\n  }\n  /** Enable stencil on the frame buffer. */\n  enableStencil() {\n    return this.stencil = !0, this.dirtyId++, this.dirtyFormat++, this;\n  }\n  /**\n   * Resize the frame buffer\n   * @param width - Width of the frame buffer to resize to\n   * @param height - Height of the frame buffer to resize to\n   */\n  resize(width, height) {\n    if (width = Math.round(width), height = Math.round(height), !width || !height) throw new Error(\"Framebuffer width and height must not be zero\");\n    if (!(width === this.width && height === this.height)) {\n      this.width = width, this.height = height, this.dirtyId++, this.dirtySize++;\n      for (let i = 0; i < this.colorTextures.length; i++) {\n        const texture = this.colorTextures[i],\n          resolution = texture.resolution;\n        texture.setSize(width / resolution, height / resolution);\n      }\n      if (this.depthTexture) {\n        const resolution = this.depthTexture.resolution;\n        this.depthTexture.setSize(width / resolution, height / resolution);\n      }\n    }\n  }\n  /** Disposes WebGL resources that are connected to this geometry. */\n  dispose() {\n    this.disposeRunner.emit(this, !1);\n  }\n  /** Destroys and removes the depth texture added to this framebuffer. */\n  destroyDepthTexture() {\n    this.depthTexture && (this.depthTexture.destroy(), this.depthTexture = null, ++this.dirtyId, ++this.dirtyFormat);\n  }\n}\nexport { Framebuffer };", "map": {"version": 3, "names": ["Framebuffer", "constructor", "width", "height", "Math", "round", "Error", "stencil", "depth", "dirtyId", "dirtyFormat", "dirtySize", "depthTexture", "colorTextures", "glFramebuffers", "dispose<PERSON><PERSON><PERSON>", "Runner", "multisample", "MSAA_QUALITY", "NONE", "colorTexture", "addColorTexture", "index", "texture", "BaseTexture", "scaleMode", "SCALE_MODES", "NEAREST", "resolution", "mipmap", "MIPMAP_MODES", "OFF", "addDepthTexture", "format", "FORMATS", "DEPTH_COMPONENT", "type", "TYPES", "UNSIGNED_SHORT", "enableDepth", "enableStencil", "resize", "i", "length", "setSize", "dispose", "emit", "destroyDepthTexture", "destroy"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\framebuffer\\Framebuffer.ts"], "sourcesContent": ["import { FORMATS, MIPM<PERSON>_MODES, MSAA_QUALITY, SCALE_MODES, TYPES } from '@pixi/constants';\nimport { Runner } from '@pixi/runner';\nimport { BaseTexture } from '../textures/BaseTexture';\n\nimport type { <PERSON><PERSON><PERSON><PERSON>uffer } from './GLFramebuffer';\n\n/**\n * A framebuffer can be used to render contents off of the screen. {@link PIXI.BaseRenderTexture} uses\n * one internally to render into itself. You can attach a depth or stencil buffer to a framebuffer.\n *\n * On WebGL 2 machines, shaders can output to multiple textures simultaneously with GLSL 300 ES.\n * @memberof PIXI\n */\nexport class Framebuffer\n{\n    /** Width of framebuffer in pixels. */\n    public width: number;\n\n    /** Height of framebuffer in pixels. */\n    public height: number;\n\n    /**\n     * Desired number of samples for antialiasing. 0 means AA should not be used.\n     *\n     * Experimental WebGL2 feature, allows to use antialiasing in individual renderTextures.\n     * Antialiasing is the same as for main buffer with renderer `antialias: true` options.\n     * Seriously affects GPU memory consumption and GPU performance.\n     * @example\n     * import { MSAA_QUALITY } from 'pixi.js';\n     *\n     * renderTexture.framebuffer.multisample = MSAA_QUALITY.HIGH;\n     * // ...\n     * renderer.render(myContainer, { renderTexture });\n     * renderer.framebuffer.blit(); // Copies data from MSAA framebuffer to texture\n     * @default PIXI.MSAA_QUALITY.NONE\n     */\n    public multisample: MSAA_QUALITY;\n\n    stencil: boolean;\n    depth: boolean;\n    dirtyId: number;\n    dirtyFormat: number;\n    dirtySize: number;\n    depthTexture: BaseTexture;\n    colorTextures: Array<BaseTexture>;\n    glFramebuffers: {[key: string]: GLFramebuffer};\n    disposeRunner: Runner;\n\n    /**\n     * @param width - Width of the frame buffer\n     * @param height - Height of the frame buffer\n     */\n    constructor(width: number, height: number)\n    {\n        this.width = Math.round(width);\n        this.height = Math.round(height);\n\n        if (!this.width || !this.height)\n        {\n            throw new Error('Framebuffer width or height is zero');\n        }\n\n        this.stencil = false;\n        this.depth = false;\n\n        this.dirtyId = 0;\n        this.dirtyFormat = 0;\n        this.dirtySize = 0;\n\n        this.depthTexture = null;\n        this.colorTextures = [];\n\n        this.glFramebuffers = {};\n\n        this.disposeRunner = new Runner('disposeFramebuffer');\n        this.multisample = MSAA_QUALITY.NONE;\n    }\n\n    /**\n     * Reference to the colorTexture.\n     * @readonly\n     */\n    get colorTexture(): BaseTexture\n    {\n        return this.colorTextures[0];\n    }\n\n    /**\n     * Add texture to the colorTexture array.\n     * @param index - Index of the array to add the texture to\n     * @param texture - Texture to add to the array\n     */\n    addColorTexture(index = 0, texture?: BaseTexture): this\n    {\n        // TODO add some validation to the texture - same width / height etc?\n        this.colorTextures[index] = texture || new BaseTexture(null, {\n            scaleMode: SCALE_MODES.NEAREST,\n            resolution: 1,\n            mipmap: MIPMAP_MODES.OFF,\n            width: this.width,\n            height: this.height,\n        });\n\n        this.dirtyId++;\n        this.dirtyFormat++;\n\n        return this;\n    }\n\n    /**\n     * Add a depth texture to the frame buffer.\n     * @param texture - Texture to add.\n     */\n    addDepthTexture(texture?: BaseTexture): this\n    {\n        this.depthTexture = texture || new BaseTexture(null, {\n            scaleMode: SCALE_MODES.NEAREST,\n            resolution: 1,\n            width: this.width,\n            height: this.height,\n            mipmap: MIPMAP_MODES.OFF,\n            format: FORMATS.DEPTH_COMPONENT,\n            type: TYPES.UNSIGNED_SHORT,\n        });\n\n        this.dirtyId++;\n        this.dirtyFormat++;\n\n        return this;\n    }\n\n    /** Enable depth on the frame buffer. */\n    enableDepth(): this\n    {\n        this.depth = true;\n\n        this.dirtyId++;\n        this.dirtyFormat++;\n\n        return this;\n    }\n\n    /** Enable stencil on the frame buffer. */\n    enableStencil(): this\n    {\n        this.stencil = true;\n\n        this.dirtyId++;\n        this.dirtyFormat++;\n\n        return this;\n    }\n\n    /**\n     * Resize the frame buffer\n     * @param width - Width of the frame buffer to resize to\n     * @param height - Height of the frame buffer to resize to\n     */\n    resize(width: number, height: number): void\n    {\n        width = Math.round(width);\n        height = Math.round(height);\n\n        if (!width || !height)\n        {\n            throw new Error('Framebuffer width and height must not be zero');\n        }\n\n        if (width === this.width && height === this.height) return;\n\n        this.width = width;\n        this.height = height;\n\n        this.dirtyId++;\n        this.dirtySize++;\n\n        for (let i = 0; i < this.colorTextures.length; i++)\n        {\n            const texture = this.colorTextures[i];\n            const resolution = texture.resolution;\n\n            // take into account the fact the texture may have a different resolution..\n            texture.setSize(width / resolution, height / resolution);\n        }\n\n        if (this.depthTexture)\n        {\n            const resolution = this.depthTexture.resolution;\n\n            this.depthTexture.setSize(width / resolution, height / resolution);\n        }\n    }\n\n    /** Disposes WebGL resources that are connected to this geometry. */\n    dispose(): void\n    {\n        this.disposeRunner.emit(this, false);\n    }\n\n    /** Destroys and removes the depth texture added to this framebuffer. */\n    destroyDepthTexture(): void\n    {\n        if (this.depthTexture)\n        {\n            this.depthTexture.destroy();\n            this.depthTexture = null;\n\n            ++this.dirtyId;\n            ++this.dirtyFormat;\n        }\n    }\n}\n"], "mappings": ";;;AAaO,MAAMA,WAAA,CACb;EAAA;AAAA;AAAA;AAAA;EAsCIC,YAAYC,KAAA,EAAeC,MAAA,EAC3B;IAII,IAHA,KAAKD,KAAA,GAAQE,IAAA,CAAKC,KAAA,CAAMH,KAAK,GAC7B,KAAKC,MAAA,GAASC,IAAA,CAAKC,KAAA,CAAMF,MAAM,GAE3B,CAAC,KAAKD,KAAA,IAAS,CAAC,KAAKC,MAAA,EAEf,UAAIG,KAAA,CAAM,qCAAqC;IAGzD,KAAKC,OAAA,GAAU,IACf,KAAKC,KAAA,GAAQ,IAEb,KAAKC,OAAA,GAAU,GACf,KAAKC,WAAA,GAAc,GACnB,KAAKC,SAAA,GAAY,GAEjB,KAAKC,YAAA,GAAe,MACpB,KAAKC,aAAA,GAAgB,EAErB,OAAKC,cAAA,GAAiB,IAEtB,KAAKC,aAAA,GAAgB,IAAIC,MAAA,CAAO,oBAAoB,GACpD,KAAKC,WAAA,GAAcC,YAAA,CAAaC,IAAA;EACpC;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIC,aAAA,EACJ;IACW,YAAKP,aAAA,CAAc,CAAC;EAC/B;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAQ,gBAAgBC,KAAA,GAAQ,GAAGC,OAAA,EAC3B;IAEI,YAAKV,aAAA,CAAcS,KAAK,IAAIC,OAAA,IAAW,IAAIC,WAAA,CAAY,MAAM;MACzDC,SAAA,EAAWC,WAAA,CAAYC,OAAA;MACvBC,UAAA,EAAY;MACZC,MAAA,EAAQC,YAAA,CAAaC,GAAA;MACrB7B,KAAA,EAAO,KAAKA,KAAA;MACZC,MAAA,EAAQ,KAAKA;IAAA,CAChB,GAED,KAAKM,OAAA,IACL,KAAKC,WAAA,IAEE;EACX;EAAA;AAAA;AAAA;AAAA;EAMAsB,gBAAgBT,OAAA,EAChB;IACI,YAAKX,YAAA,GAAeW,OAAA,IAAW,IAAIC,WAAA,CAAY,MAAM;MACjDC,SAAA,EAAWC,WAAA,CAAYC,OAAA;MACvBC,UAAA,EAAY;MACZ1B,KAAA,EAAO,KAAKA,KAAA;MACZC,MAAA,EAAQ,KAAKA,MAAA;MACb0B,MAAA,EAAQC,YAAA,CAAaC,GAAA;MACrBE,MAAA,EAAQC,OAAA,CAAQC,eAAA;MAChBC,IAAA,EAAMC,KAAA,CAAMC;IAAA,CACf,GAED,KAAK7B,OAAA,IACL,KAAKC,WAAA,IAEE;EACX;EAAA;EAGA6B,YAAA,EACA;IACI,YAAK/B,KAAA,GAAQ,IAEb,KAAKC,OAAA,IACL,KAAKC,WAAA,IAEE;EACX;EAAA;EAGA8B,cAAA,EACA;IACI,YAAKjC,OAAA,GAAU,IAEf,KAAKE,OAAA,IACL,KAAKC,WAAA,IAEE;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA+B,OAAOvC,KAAA,EAAeC,MAAA,EACtB;IAII,IAHAD,KAAA,GAAQE,IAAA,CAAKC,KAAA,CAAMH,KAAK,GACxBC,MAAA,GAASC,IAAA,CAAKC,KAAA,CAAMF,MAAM,GAEtB,CAACD,KAAA,IAAS,CAACC,MAAA,EAEL,UAAIG,KAAA,CAAM,+CAA+C;IAGnE,IAAI,EAAUJ,KAAA,UAAKA,KAAA,IAASC,MAAA,KAAW,KAAKA,MAAA,GAE5C;MAAA,KAAKD,KAAA,GAAQA,KAAA,EACb,KAAKC,MAAA,GAASA,MAAA,EAEd,KAAKM,OAAA,IACL,KAAKE,SAAA;MAEL,SAAS+B,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK7B,aAAA,CAAc8B,MAAA,EAAQD,CAAA,IAC/C;QACI,MAAMnB,OAAA,GAAU,KAAKV,aAAA,CAAc6B,CAAC;UAC9Bd,UAAA,GAAaL,OAAA,CAAQK,UAAA;QAG3BL,OAAA,CAAQqB,OAAA,CAAQ1C,KAAA,GAAQ0B,UAAA,EAAYzB,MAAA,GAASyB,UAAU;MAC3D;MAEA,IAAI,KAAKhB,YAAA,EACT;QACU,MAAAgB,UAAA,GAAa,KAAKhB,YAAA,CAAagB,UAAA;QAErC,KAAKhB,YAAA,CAAagC,OAAA,CAAQ1C,KAAA,GAAQ0B,UAAA,EAAYzB,MAAA,GAASyB,UAAU;MACrE;IAAA;EACJ;EAAA;EAGAiB,QAAA,EACA;IACS,KAAA9B,aAAA,CAAc+B,IAAA,CAAK,MAAM,EAAK;EACvC;EAAA;EAGAC,oBAAA,EACA;IACQ,KAAKnC,YAAA,KAEL,KAAKA,YAAA,CAAaoC,OAAA,CAAQ,GAC1B,KAAKpC,YAAA,GAAe,MAEpB,EAAE,KAAKH,OAAA,EACP,EAAE,KAAKC,WAAA;EAEf;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}