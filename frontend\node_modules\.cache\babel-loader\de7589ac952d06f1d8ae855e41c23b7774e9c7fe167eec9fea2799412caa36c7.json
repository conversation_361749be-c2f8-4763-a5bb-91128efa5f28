{"ast": null, "code": "import { utils, ExtensionType, settings, extensions } from \"@pixi/core\";\nimport { checkDataUrl } from \"../../utils/checkDataUrl.mjs\";\nimport { checkExtension } from \"../../utils/checkExtension.mjs\";\nimport { LoaderParserPriority } from \"./LoaderParser.mjs\";\nconst validWeights = [\"normal\", \"bold\", \"100\", \"200\", \"300\", \"400\", \"500\", \"600\", \"700\", \"800\", \"900\"],\n  validFontExtensions = [\".ttf\", \".otf\", \".woff\", \".woff2\"],\n  validFontMIMEs = [\"font/ttf\", \"font/otf\", \"font/woff\", \"font/woff2\"],\n  CSS_IDENT_TOKEN_REGEX = /^(--|-?[A-Z_])[0-9A-Z_-]*$/i;\nfunction getFontFamilyName(url) {\n  const ext = utils.path.extname(url),\n    nameTokens = utils.path.basename(url, ext).replace(/(-|_)/g, \" \").toLowerCase().split(\" \").map(word => word.charAt(0).toUpperCase() + word.slice(1));\n  let valid = nameTokens.length > 0;\n  for (const token of nameTokens) if (!token.match(CSS_IDENT_TOKEN_REGEX)) {\n    valid = !1;\n    break;\n  }\n  let fontFamilyName = nameTokens.join(\" \");\n  return valid || (fontFamilyName = `\"${fontFamilyName.replace(/[\\\\\"]/g, \"\\\\$&\")}\"`), fontFamilyName;\n}\nconst validURICharactersRegex = /^[0-9A-Za-z%:/?#\\[\\]@!\\$&'()\\*\\+,;=\\-._~]*$/;\nfunction encodeURIWhenNeeded(uri) {\n  return validURICharactersRegex.test(uri) ? uri : encodeURI(uri);\n}\nconst loadWebFont = {\n  extension: {\n    type: ExtensionType.LoadParser,\n    priority: LoaderParserPriority.Low\n  },\n  name: \"loadWebFont\",\n  test(url) {\n    return checkDataUrl(url, validFontMIMEs) || checkExtension(url, validFontExtensions);\n  },\n  async load(url, options) {\n    const fonts = settings.ADAPTER.getFontFaceSet();\n    if (fonts) {\n      const fontFaces = [],\n        name = options.data?.family ?? getFontFamilyName(url),\n        weights = options.data?.weights?.filter(weight => validWeights.includes(weight)) ?? [\"normal\"],\n        data = options.data ?? {};\n      for (let i = 0; i < weights.length; i++) {\n        const weight = weights[i],\n          font = new FontFace(name, `url(${encodeURIWhenNeeded(url)})`, {\n            ...data,\n            weight\n          });\n        await font.load(), fonts.add(font), fontFaces.push(font);\n      }\n      return fontFaces.length === 1 ? fontFaces[0] : fontFaces;\n    }\n    return console.warn(\"[loadWebFont] FontFace API is not supported. Skipping loading font\"), null;\n  },\n  unload(font) {\n    (Array.isArray(font) ? font : [font]).forEach(t => settings.ADAPTER.getFontFaceSet().delete(t));\n  }\n};\nextensions.add(loadWebFont);\nexport { getFontFamilyName, loadWebFont };", "map": {"version": 3, "names": ["validWeights", "validFontExtensions", "validFontMIMEs", "CSS_IDENT_TOKEN_REGEX", "getFontFamilyName", "url", "ext", "utils", "path", "extname", "nameTokens", "basename", "replace", "toLowerCase", "split", "map", "word", "char<PERSON>t", "toUpperCase", "slice", "valid", "length", "token", "match", "fontFamilyName", "join", "validURICharactersRegex", "encodeURIWhenNeeded", "uri", "test", "encodeURI", "loadWebFont", "extension", "type", "ExtensionType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "priority", "LoaderParserPriority", "Low", "name", "checkDataUrl", "checkExtension", "load", "options", "fonts", "settings", "ADAPTER", "getFontFaceSet", "fontFaces", "data", "family", "weights", "filter", "weight", "includes", "i", "font", "FontFace", "add", "push", "console", "warn", "unload", "Array", "isArray", "for<PERSON>ach", "t", "delete", "extensions"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\assets\\src\\loader\\parsers\\loadWebFont.ts"], "sourcesContent": ["import { extensions, ExtensionType, settings, utils } from '@pixi/core';\nimport { checkDataUrl } from '../../utils/checkDataUrl';\nimport { checkExtension } from '../../utils/checkExtension';\nimport { LoaderParserPriority } from './LoaderParser';\n\nimport type { ResolvedAsset } from '../../types';\nimport type { LoaderParser } from './LoaderParser';\n\nconst validWeights = [\n    'normal', 'bold',\n    '100', '200', '300', '400', '500', '600', '700', '800', '900',\n];\nconst validFontExtensions = ['.ttf', '.otf', '.woff', '.woff2'];\nconst validFontMIMEs = [\n    'font/ttf',\n    'font/otf',\n    'font/woff',\n    'font/woff2',\n];\n\n/**\n * Loader plugin for handling web fonts\n * @memberof PIXI\n */\nexport type LoadFontData = {\n    family: string;\n    display: string;\n    featureSettings: string;\n    stretch: string;\n    style: string;\n    unicodeRange: string;\n    variant: string;\n    weights: string[];\n};\n\n/**\n * RegExp for matching CSS <ident-token>. It doesn't consider escape and non-ASCII characters, but enough for us.\n * @see {@link https://www.w3.org/TR/css-syntax-3/#ident-token-diagram}\n */\nconst CSS_IDENT_TOKEN_REGEX = /^(--|-?[A-Z_])[0-9A-Z_-]*$/i;\n\n/**\n * Return font face name from a file name\n * Ex.: 'fonts/tital-one.woff' turns into 'Titan One'\n * @param url - File url\n */\nexport function getFontFamilyName(url: string): string\n{\n    const ext = utils.path.extname(url);\n    const name = utils.path.basename(url, ext);\n\n    // Replace dashes by white spaces\n    const nameWithSpaces = name.replace(/(-|_)/g, ' ');\n\n    // Upper case first character of each word\n    const nameTokens = nameWithSpaces.toLowerCase()\n        .split(' ')\n        .map((word) => word.charAt(0).toUpperCase() + word.slice(1));\n\n    let valid = nameTokens.length > 0;\n\n    for (const token of nameTokens)\n    {\n        if (!token.match(CSS_IDENT_TOKEN_REGEX))\n        {\n            valid = false;\n            break;\n        }\n    }\n\n    let fontFamilyName = nameTokens.join(' ');\n\n    if (!valid)\n    {\n        fontFamilyName = `\"${fontFamilyName.replace(/[\\\\\"]/g, '\\\\$&')}\"`;\n    }\n\n    return fontFamilyName;\n}\n\n// See RFC 3986 Chapter 2. Characters\nconst validURICharactersRegex = /^[0-9A-Za-z%:/?#\\[\\]@!\\$&'()\\*\\+,;=\\-._~]*$/;\n\n/**\n * Encode URI only when it contains invalid characters.\n * @param uri - URI to encode.\n */\nfunction encodeURIWhenNeeded(uri: string)\n{\n    if (validURICharactersRegex.test(uri))\n    {\n        return uri;\n    }\n\n    return encodeURI(uri);\n}\n\n/** Web font loader plugin */\nexport const loadWebFont = {\n    extension: {\n        type: ExtensionType.LoadParser,\n        priority: LoaderParserPriority.Low,\n    },\n\n    name: 'loadWebFont',\n\n    test(url: string): boolean\n    {\n        return checkDataUrl(url, validFontMIMEs) || checkExtension(url, validFontExtensions);\n    },\n\n    async load(url: string, options?: ResolvedAsset<LoadFontData>): Promise<FontFace | FontFace[]>\n    {\n        const fonts = settings.ADAPTER.getFontFaceSet();\n\n        if (fonts)\n        {\n            const fontFaces: FontFace[] = [];\n            const name = options.data?.family ?? getFontFamilyName(url);\n            const weights = options.data?.weights?.filter((weight) => validWeights.includes(weight)) ?? ['normal'];\n            const data = options.data ?? {};\n\n            for (let i = 0; i < weights.length; i++)\n            {\n                const weight = weights[i];\n\n                const font = new FontFace(name, `url(${encodeURIWhenNeeded(url)})`, {\n                    ...data,\n                    weight,\n                });\n\n                await font.load();\n\n                fonts.add(font);\n\n                fontFaces.push(font);\n            }\n\n            return fontFaces.length === 1 ? fontFaces[0] : fontFaces;\n        }\n\n        if (process.env.DEBUG)\n        {\n            console.warn('[loadWebFont] FontFace API is not supported. Skipping loading font');\n        }\n\n        return null;\n    },\n\n    unload(font: FontFace | FontFace[]): void\n    {\n        (Array.isArray(font) ? font : [font])\n            .forEach((t) => settings.ADAPTER.getFontFaceSet().delete(t));\n    }\n} as LoaderParser<FontFace | FontFace[]>;\n\nextensions.add(loadWebFont);\n"], "mappings": ";;;;AAQA,MAAMA,YAAA,GAAe,CACjB,UAAU,QACV,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,MAC5D;EACMC,mBAAA,GAAsB,CAAC,QAAQ,QAAQ,SAAS,QAAQ;EACxDC,cAAA,GAAiB,CACnB,YACA,YACA,aACA,aACJ;EAqBMC,qBAAA,GAAwB;AAOvB,SAASC,kBAAkBC,GAAA,EAClC;EACI,MAAMC,GAAA,GAAMC,KAAA,CAAMC,IAAA,CAAKC,OAAA,CAAQJ,GAAG;IAO5BK,UAAA,GANOH,KAAA,CAAMC,IAAA,CAAKG,QAAA,CAASN,GAAA,EAAKC,GAAG,EAGbM,OAAA,CAAQ,UAAU,GAAG,EAGfC,WAAA,CAAY,EACzCC,KAAA,CAAM,GAAG,EACTC,GAAA,CAAKC,IAAA,IAASA,IAAA,CAAKC,MAAA,CAAO,CAAC,EAAEC,WAAA,CAAY,IAAIF,IAAA,CAAKG,KAAA,CAAM,CAAC,CAAC;EAE3D,IAAAC,KAAA,GAAQV,UAAA,CAAWW,MAAA,GAAS;EAEhC,WAAWC,KAAA,IAASZ,UAAA,EAEhB,IAAI,CAACY,KAAA,CAAMC,KAAA,CAAMpB,qBAAqB,GACtC;IACYiB,KAAA;IACR;EACJ;EAGA,IAAAI,cAAA,GAAiBd,UAAA,CAAWe,IAAA,CAAK,GAAG;EAEnC,OAAAL,KAAA,KAEDI,cAAA,GAAiB,IAAIA,cAAA,CAAeZ,OAAA,CAAQ,UAAU,MAAM,CAAC,MAG1DY,cAAA;AACX;AAGA,MAAME,uBAAA,GAA0B;AAMhC,SAASC,oBAAoBC,GAAA,EAC7B;EACI,OAAIF,uBAAA,CAAwBG,IAAA,CAAKD,GAAG,IAEzBA,GAAA,GAGJE,SAAA,CAAUF,GAAG;AACxB;AAGO,MAAMG,WAAA,GAAc;EACvBC,SAAA,EAAW;IACPC,IAAA,EAAMC,aAAA,CAAcC,UAAA;IACpBC,QAAA,EAAUC,oBAAA,CAAqBC;EACnC;EAEAC,IAAA,EAAM;EAENV,KAAKxB,GAAA,EACL;IACI,OAAOmC,YAAA,CAAanC,GAAA,EAAKH,cAAc,KAAKuC,cAAA,CAAepC,GAAA,EAAKJ,mBAAmB;EACvF;EAEA,MAAMyC,KAAKrC,GAAA,EAAasC,OAAA,EACxB;IACU,MAAAC,KAAA,GAAQC,QAAA,CAASC,OAAA,CAAQC,cAAA,CAAe;IAE9C,IAAIH,KAAA,EACJ;MACI,MAAMI,SAAA,GAAwB;QACxBT,IAAA,GAAOI,OAAA,CAAQM,IAAA,EAAMC,MAAA,IAAU9C,iBAAA,CAAkBC,GAAG;QACpD8C,OAAA,GAAUR,OAAA,CAAQM,IAAA,EAAME,OAAA,EAASC,MAAA,CAAQC,MAAA,IAAWrD,YAAA,CAAasD,QAAA,CAASD,MAAM,CAAC,KAAK,CAAC,QAAQ;QAC/FJ,IAAA,GAAON,OAAA,CAAQM,IAAA,IAAQ;MAE7B,SAASM,CAAA,GAAI,GAAGA,CAAA,GAAIJ,OAAA,CAAQ9B,MAAA,EAAQkC,CAAA,IACpC;QACI,MAAMF,MAAA,GAASF,OAAA,CAAQI,CAAC;UAElBC,IAAA,GAAO,IAAIC,QAAA,CAASlB,IAAA,EAAM,OAAOZ,mBAAA,CAAoBtB,GAAG,CAAC,KAAK;YAChE,GAAG4C,IAAA;YACHI;UAAA,CACH;QAEK,MAAAG,IAAA,CAAKd,IAAA,CAEX,GAAAE,KAAA,CAAMc,GAAA,CAAIF,IAAI,GAEdR,SAAA,CAAUW,IAAA,CAAKH,IAAI;MACvB;MAEA,OAAOR,SAAA,CAAU3B,MAAA,KAAW,IAAI2B,SAAA,CAAU,CAAC,IAAIA,SAAA;IACnD;IAIY,OAAAY,OAAA,CAAAC,IAAA,CAAK,oEAAoE,GAG9E;EACX;EAEAC,OAAON,IAAA,EACP;IACI,CAACO,KAAA,CAAMC,OAAA,CAAQR,IAAI,IAAIA,IAAA,GAAO,CAACA,IAAI,GAC9BS,OAAA,CAASC,CAAA,IAAMrB,QAAA,CAASC,OAAA,CAAQC,cAAA,CAAiB,EAAAoB,MAAA,CAAOD,CAAC,CAAC;EACnE;AACJ;AAEAE,UAAA,CAAWV,GAAA,CAAI3B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}