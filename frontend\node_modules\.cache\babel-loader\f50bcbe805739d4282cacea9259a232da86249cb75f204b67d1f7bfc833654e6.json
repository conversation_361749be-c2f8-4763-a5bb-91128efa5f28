{"ast": null, "code": "class TickerListener {\n  /**\n   * Constructor\n   * @private\n   * @param fn - The listener function to be added for one update\n   * @param context - The listener context\n   * @param priority - The priority for emitting\n   * @param once - If the handler should fire once\n   */\n  constructor(fn, context = null, priority = 0, once = !1) {\n    this.next = null, this.previous = null, this._destroyed = !1, this.fn = fn, this.context = context, this.priority = priority, this.once = once;\n  }\n  /**\n   * Simple compare function to figure out if a function and context match.\n   * @private\n   * @param fn - The listener function to be added for one update\n   * @param context - The listener context\n   * @returns `true` if the listener match the arguments\n   */\n  match(fn, context = null) {\n    return this.fn === fn && this.context === context;\n  }\n  /**\n   * Emit by calling the current function.\n   * @private\n   * @param deltaTime - time since the last emit.\n   * @returns Next ticker\n   */\n  emit(deltaTime) {\n    this.fn && (this.context ? this.fn.call(this.context, deltaTime) : this.fn(deltaTime));\n    const redirect = this.next;\n    return this.once && this.destroy(!0), this._destroyed && (this.next = null), redirect;\n  }\n  /**\n   * Connect to the list.\n   * @private\n   * @param previous - Input node, previous listener\n   */\n  connect(previous) {\n    this.previous = previous, previous.next && (previous.next.previous = this), this.next = previous.next, previous.next = this;\n  }\n  /**\n   * Destroy and don't use after this.\n   * @private\n   * @param hard - `true` to remove the `next` reference, this\n   *        is considered a hard destroy. Soft destroy maintains the next reference.\n   * @returns The listener to redirect while emitting or removing.\n   */\n  destroy(hard = !1) {\n    this._destroyed = !0, this.fn = null, this.context = null, this.previous && (this.previous.next = this.next), this.next && (this.next.previous = this.previous);\n    const redirect = this.next;\n    return this.next = hard ? null : redirect, this.previous = null, redirect;\n  }\n}\nexport { TickerListener };", "map": {"version": 3, "names": ["TickerListener", "constructor", "fn", "context", "priority", "once", "next", "previous", "_destroyed", "match", "emit", "deltaTime", "call", "redirect", "destroy", "connect", "hard"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\ticker\\src\\TickerListener.ts"], "sourcesContent": ["import type { TickerCallback } from './Ticker';\n\n/**\n * Internal class for handling the priority sorting of ticker handlers.\n * @private\n * @class\n * @memberof PIXI\n */\nexport class TickerListener<T = any>\n{\n    /** The current priority. */\n    public priority: number;\n    /** The next item in chain. */\n    public next: TickerListener = null;\n    /** The previous item in chain. */\n    public previous: TickerListener = null;\n\n    /** The handler function to execute. */\n    private fn: TickerCallback<T>;\n    /** The calling to execute. */\n    private context: T;\n    /** If this should only execute once. */\n    private once: boolean;\n    /** `true` if this listener has been destroyed already. */\n    private _destroyed = false;\n\n    /**\n     * Constructor\n     * @private\n     * @param fn - The listener function to be added for one update\n     * @param context - The listener context\n     * @param priority - The priority for emitting\n     * @param once - If the handler should fire once\n     */\n    constructor(fn: TickerCallback<T>, context: T = null, priority = 0, once = false)\n    {\n        this.fn = fn;\n        this.context = context;\n        this.priority = priority;\n        this.once = once;\n    }\n\n    /**\n     * Simple compare function to figure out if a function and context match.\n     * @private\n     * @param fn - The listener function to be added for one update\n     * @param context - The listener context\n     * @returns `true` if the listener match the arguments\n     */\n    match(fn: TickerCallback<T>, context: any = null): boolean\n    {\n        return this.fn === fn && this.context === context;\n    }\n\n    /**\n     * Emit by calling the current function.\n     * @private\n     * @param deltaTime - time since the last emit.\n     * @returns Next ticker\n     */\n    emit(deltaTime: number): TickerListener\n    {\n        if (this.fn)\n        {\n            if (this.context)\n            {\n                this.fn.call(this.context, deltaTime);\n            }\n            else\n            {\n                (this as TickerListener<any>).fn(deltaTime);\n            }\n        }\n\n        const redirect = this.next;\n\n        if (this.once)\n        {\n            this.destroy(true);\n        }\n\n        // Soft-destroying should remove\n        // the next reference\n        if (this._destroyed)\n        {\n            this.next = null;\n        }\n\n        return redirect;\n    }\n\n    /**\n     * Connect to the list.\n     * @private\n     * @param previous - Input node, previous listener\n     */\n    connect(previous: TickerListener): void\n    {\n        this.previous = previous;\n        if (previous.next)\n        {\n            previous.next.previous = this;\n        }\n        this.next = previous.next;\n        previous.next = this;\n    }\n\n    /**\n     * Destroy and don't use after this.\n     * @private\n     * @param hard - `true` to remove the `next` reference, this\n     *        is considered a hard destroy. Soft destroy maintains the next reference.\n     * @returns The listener to redirect while emitting or removing.\n     */\n    destroy(hard = false): TickerListener\n    {\n        this._destroyed = true;\n        this.fn = null;\n        this.context = null;\n\n        // Disconnect, hook up next and previous\n        if (this.previous)\n        {\n            this.previous.next = this.next;\n        }\n\n        if (this.next)\n        {\n            this.next.previous = this.previous;\n        }\n\n        // Redirect to the next item\n        const redirect = this.next;\n\n        // Remove references\n        this.next = hard ? null : redirect;\n        this.previous = null;\n\n        return redirect;\n    }\n}\n"], "mappings": "AAQO,MAAMA,cAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAyBIC,YAAYC,EAAA,EAAuBC,OAAA,GAAa,MAAMC,QAAA,GAAW,GAAGC,IAAA,GAAO,IAC3E;IAtBA,KAAOC,IAAA,GAAuB,MAE9B,KAAOC,QAAA,GAA2B,MASlC,KAAQC,UAAA,GAAa,IAYZ,KAAAN,EAAA,GAAKA,EAAA,EACV,KAAKC,OAAA,GAAUA,OAAA,EACf,KAAKC,QAAA,GAAWA,QAAA,EAChB,KAAKC,IAAA,GAAOA,IAAA;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAI,MAAMP,EAAA,EAAuBC,OAAA,GAAe,MAC5C;IACI,OAAO,KAAKD,EAAA,KAAOA,EAAA,IAAM,KAAKC,OAAA,KAAYA,OAAA;EAC9C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAO,KAAKC,SAAA,EACL;IACQ,KAAKT,EAAA,KAED,KAAKC,OAAA,GAEL,KAAKD,EAAA,CAAGU,IAAA,CAAK,KAAKT,OAAA,EAASQ,SAAS,IAInC,KAA6BT,EAAA,CAAGS,SAAS;IAIlD,MAAME,QAAA,GAAW,KAAKP,IAAA;IAElB,YAAKD,IAAA,IAEL,KAAKS,OAAA,CAAQ,EAAI,GAKjB,KAAKN,UAAA,KAEL,KAAKF,IAAA,GAAO,OAGTO,QAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAE,QAAQR,QAAA,EACR;IACI,KAAKA,QAAA,GAAWA,QAAA,EACZA,QAAA,CAASD,IAAA,KAETC,QAAA,CAASD,IAAA,CAAKC,QAAA,GAAW,OAE7B,KAAKD,IAAA,GAAOC,QAAA,CAASD,IAAA,EACrBC,QAAA,CAASD,IAAA,GAAO;EACpB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAQ,QAAQE,IAAA,GAAO,IACf;IACS,KAAAR,UAAA,GAAa,IAClB,KAAKN,EAAA,GAAK,MACV,KAAKC,OAAA,GAAU,MAGX,KAAKI,QAAA,KAEL,KAAKA,QAAA,CAASD,IAAA,GAAO,KAAKA,IAAA,GAG1B,KAAKA,IAAA,KAEL,KAAKA,IAAA,CAAKC,QAAA,GAAW,KAAKA,QAAA;IAI9B,MAAMM,QAAA,GAAW,KAAKP,IAAA;IAGtB,YAAKA,IAAA,GAAOU,IAAA,GAAO,OAAOH,QAAA,EAC1B,KAAKN,QAAA,GAAW,MAETM,QAAA;EACX;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}