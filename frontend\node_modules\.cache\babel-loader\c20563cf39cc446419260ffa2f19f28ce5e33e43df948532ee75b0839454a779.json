{"ast": null, "code": "import { Matrix } from \"./Matrix.mjs\";\nimport { ObservablePoint } from \"./ObservablePoint.mjs\";\nconst _Transform = class {\n  constructor() {\n    this.worldTransform = new Matrix(), this.localTransform = new Matrix(), this.position = new ObservablePoint(this.onChange, this, 0, 0), this.scale = new ObservablePoint(this.onChange, this, 1, 1), this.pivot = new ObservablePoint(this.onChange, this, 0, 0), this.skew = new ObservablePoint(this.updateSkew, this, 0, 0), this._rotation = 0, this._cx = 1, this._sx = 0, this._cy = 0, this._sy = 1, this._localID = 0, this._currentLocalID = 0, this._worldID = 0, this._parentID = 0;\n  }\n  /** Called when a value changes. */\n  onChange() {\n    this._localID++;\n  }\n  /** Called when the skew or the rotation changes. */\n  updateSkew() {\n    this._cx = Math.cos(this._rotation + this.skew.y), this._sx = Math.sin(this._rotation + this.skew.y), this._cy = -Math.sin(this._rotation - this.skew.x), this._sy = Math.cos(this._rotation - this.skew.x), this._localID++;\n  }\n  /** Updates the local transformation matrix. */\n  updateLocalTransform() {\n    const lt = this.localTransform;\n    this._localID !== this._currentLocalID && (lt.a = this._cx * this.scale.x, lt.b = this._sx * this.scale.x, lt.c = this._cy * this.scale.y, lt.d = this._sy * this.scale.y, lt.tx = this.position.x - (this.pivot.x * lt.a + this.pivot.y * lt.c), lt.ty = this.position.y - (this.pivot.x * lt.b + this.pivot.y * lt.d), this._currentLocalID = this._localID, this._parentID = -1);\n  }\n  /**\n   * Updates the local and the world transformation matrices.\n   * @param parentTransform - The parent transform\n   */\n  updateTransform(parentTransform) {\n    const lt = this.localTransform;\n    if (this._localID !== this._currentLocalID && (lt.a = this._cx * this.scale.x, lt.b = this._sx * this.scale.x, lt.c = this._cy * this.scale.y, lt.d = this._sy * this.scale.y, lt.tx = this.position.x - (this.pivot.x * lt.a + this.pivot.y * lt.c), lt.ty = this.position.y - (this.pivot.x * lt.b + this.pivot.y * lt.d), this._currentLocalID = this._localID, this._parentID = -1), this._parentID !== parentTransform._worldID) {\n      const pt = parentTransform.worldTransform,\n        wt = this.worldTransform;\n      wt.a = lt.a * pt.a + lt.b * pt.c, wt.b = lt.a * pt.b + lt.b * pt.d, wt.c = lt.c * pt.a + lt.d * pt.c, wt.d = lt.c * pt.b + lt.d * pt.d, wt.tx = lt.tx * pt.a + lt.ty * pt.c + pt.tx, wt.ty = lt.tx * pt.b + lt.ty * pt.d + pt.ty, this._parentID = parentTransform._worldID, this._worldID++;\n    }\n  }\n  /**\n   * Decomposes a matrix and sets the transforms properties based on it.\n   * @param matrix - The matrix to decompose\n   */\n  setFromMatrix(matrix) {\n    matrix.decompose(this), this._localID++;\n  }\n  /** The rotation of the object in radians. */\n  get rotation() {\n    return this._rotation;\n  }\n  set rotation(value) {\n    this._rotation !== value && (this._rotation = value, this.updateSkew());\n  }\n};\n_Transform.IDENTITY = new _Transform();\nlet Transform = _Transform;\nTransform.prototype.toString = function () {\n  return `[@pixi/math:Transform position=(${this.position.x}, ${this.position.y}) rotation=${this.rotation} scale=(${this.scale.x}, ${this.scale.y}) skew=(${this.skew.x}, ${this.skew.y}) ]`;\n};\nexport { Transform };", "map": {"version": 3, "names": ["_Transform", "constructor", "worldTransform", "Matrix", "localTransform", "position", "ObservablePoint", "onChange", "scale", "pivot", "skew", "updateSkew", "_rotation", "_cx", "_sx", "_cy", "_sy", "_localID", "_currentLocalID", "_worldID", "_parentID", "Math", "cos", "y", "sin", "x", "updateLocalTransform", "lt", "a", "b", "c", "d", "tx", "ty", "updateTransform", "parentTransform", "pt", "wt", "setFromMatrix", "matrix", "decompose", "rotation", "value", "IDENTITY", "Transform", "prototype", "toString"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\math\\src\\Transform.ts"], "sourcesContent": ["import { Matrix } from './Matrix';\nimport { ObservablePoint } from './ObservablePoint';\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface Transform extends GlobalMixins.Transform {}\n\n/**\n * Transform that takes care about its versions.\n * @memberof PIXI\n */\nexport class Transform\n{\n    /**\n     * A default (identity) transform.\n     * @static\n     * @type {PIXI.Transform}\n     */\n    public static readonly IDENTITY = new Transform();\n\n    /** The world transformation matrix. */\n    public worldTransform: Matrix;\n\n    /** The local transformation matrix. */\n    public localTransform: Matrix;\n\n    /** The coordinate of the object relative to the local coordinates of the parent. */\n    public position: ObservablePoint;\n\n    /** The scale factor of the object. */\n    public scale: ObservablePoint;\n\n    /** The pivot point of the displayObject that it rotates around. */\n    public pivot: ObservablePoint;\n\n    /** The skew amount, on the x and y axis. */\n    public skew: ObservablePoint;\n\n    /** The locally unique ID of the parent's world transform used to calculate the current world transformation matrix. */\n    public _parentID: number;\n\n    /** The locally unique ID of the world transform. */\n    _worldID: number;\n\n    /** The rotation amount. */\n    protected _rotation: number;\n\n    /**\n     * The X-coordinate value of the normalized local X axis,\n     * the first column of the local transformation matrix without a scale.\n     */\n    protected _cx: number;\n\n    /**\n     * The Y-coordinate value of the normalized local X axis,\n     * the first column of the local transformation matrix without a scale.\n     */\n    protected _sx: number;\n\n    /**\n     * The X-coordinate value of the normalized local Y axis,\n     * the second column of the local transformation matrix without a scale.\n     */\n    protected _cy: number;\n\n    /**\n     * The Y-coordinate value of the normalized local Y axis,\n     * the second column of the local transformation matrix without a scale.\n     */\n    protected _sy: number;\n\n    /** The locally unique ID of the local transform. */\n    protected _localID: number;\n\n    /** The locally unique ID of the local transform used to calculate the current local transformation matrix. */\n    protected _currentLocalID: number;\n\n    constructor()\n    {\n        this.worldTransform = new Matrix();\n        this.localTransform = new Matrix();\n        this.position = new ObservablePoint(this.onChange, this, 0, 0);\n        this.scale = new ObservablePoint(this.onChange, this, 1, 1);\n        this.pivot = new ObservablePoint(this.onChange, this, 0, 0);\n        this.skew = new ObservablePoint(this.updateSkew, this, 0, 0);\n\n        this._rotation = 0;\n        this._cx = 1;\n        this._sx = 0;\n        this._cy = 0;\n        this._sy = 1;\n        this._localID = 0;\n        this._currentLocalID = 0;\n\n        this._worldID = 0;\n        this._parentID = 0;\n    }\n\n    /** Called when a value changes. */\n    protected onChange(): void\n    {\n        this._localID++;\n    }\n\n    /** Called when the skew or the rotation changes. */\n    protected updateSkew(): void\n    {\n        this._cx = Math.cos(this._rotation + this.skew.y);\n        this._sx = Math.sin(this._rotation + this.skew.y);\n        this._cy = -Math.sin(this._rotation - this.skew.x); // cos, added PI/2\n        this._sy = Math.cos(this._rotation - this.skew.x); // sin, added PI/2\n\n        this._localID++;\n    }\n\n    /** Updates the local transformation matrix. */\n    updateLocalTransform(): void\n    {\n        const lt = this.localTransform;\n\n        if (this._localID !== this._currentLocalID)\n        {\n            // get the matrix values of the displayobject based on its transform properties..\n            lt.a = this._cx * this.scale.x;\n            lt.b = this._sx * this.scale.x;\n            lt.c = this._cy * this.scale.y;\n            lt.d = this._sy * this.scale.y;\n\n            lt.tx = this.position.x - ((this.pivot.x * lt.a) + (this.pivot.y * lt.c));\n            lt.ty = this.position.y - ((this.pivot.x * lt.b) + (this.pivot.y * lt.d));\n            this._currentLocalID = this._localID;\n\n            // force an update..\n            this._parentID = -1;\n        }\n    }\n\n    /**\n     * Updates the local and the world transformation matrices.\n     * @param parentTransform - The parent transform\n     */\n    updateTransform(parentTransform: Transform): void\n    {\n        const lt = this.localTransform;\n\n        if (this._localID !== this._currentLocalID)\n        {\n            // get the matrix values of the displayobject based on its transform properties..\n            lt.a = this._cx * this.scale.x;\n            lt.b = this._sx * this.scale.x;\n            lt.c = this._cy * this.scale.y;\n            lt.d = this._sy * this.scale.y;\n\n            lt.tx = this.position.x - ((this.pivot.x * lt.a) + (this.pivot.y * lt.c));\n            lt.ty = this.position.y - ((this.pivot.x * lt.b) + (this.pivot.y * lt.d));\n            this._currentLocalID = this._localID;\n\n            // force an update..\n            this._parentID = -1;\n        }\n\n        if (this._parentID !== parentTransform._worldID)\n        {\n            // concat the parent matrix with the objects transform.\n            const pt = parentTransform.worldTransform;\n            const wt = this.worldTransform;\n\n            wt.a = (lt.a * pt.a) + (lt.b * pt.c);\n            wt.b = (lt.a * pt.b) + (lt.b * pt.d);\n            wt.c = (lt.c * pt.a) + (lt.d * pt.c);\n            wt.d = (lt.c * pt.b) + (lt.d * pt.d);\n            wt.tx = (lt.tx * pt.a) + (lt.ty * pt.c) + pt.tx;\n            wt.ty = (lt.tx * pt.b) + (lt.ty * pt.d) + pt.ty;\n\n            this._parentID = parentTransform._worldID;\n\n            // update the id of the transform..\n            this._worldID++;\n        }\n    }\n\n    /**\n     * Decomposes a matrix and sets the transforms properties based on it.\n     * @param matrix - The matrix to decompose\n     */\n    setFromMatrix(matrix: Matrix): void\n    {\n        matrix.decompose(this);\n        this._localID++;\n    }\n\n    /** The rotation of the object in radians. */\n    get rotation(): number\n    {\n        return this._rotation;\n    }\n\n    set rotation(value: number)\n    {\n        if (this._rotation !== value)\n        {\n            this._rotation = value;\n            this.updateSkew();\n        }\n    }\n}\n\nif (process.env.DEBUG)\n{\n    Transform.prototype.toString = function toString(): string\n    {\n        return `[@pixi/math:Transform `\n            + `position=(${this.position.x}, ${this.position.y}) `\n            + `rotation=${this.rotation} `\n            + `scale=(${this.scale.x}, ${this.scale.y}) `\n            + `skew=(${this.skew.x}, ${this.skew.y}) `\n            + `]`;\n    };\n}\n"], "mappings": ";;AAUO,MAAMA,UAAA,GAAN,MACP;EAiEIC,YAAA,EACA;IACI,KAAKC,cAAA,GAAiB,IAAIC,MAAA,CAAO,GACjC,KAAKC,cAAA,GAAiB,IAAID,MAAA,CAAO,GACjC,KAAKE,QAAA,GAAW,IAAIC,eAAA,CAAgB,KAAKC,QAAA,EAAU,MAAM,GAAG,CAAC,GAC7D,KAAKC,KAAA,GAAQ,IAAIF,eAAA,CAAgB,KAAKC,QAAA,EAAU,MAAM,GAAG,CAAC,GAC1D,KAAKE,KAAA,GAAQ,IAAIH,eAAA,CAAgB,KAAKC,QAAA,EAAU,MAAM,GAAG,CAAC,GAC1D,KAAKG,IAAA,GAAO,IAAIJ,eAAA,CAAgB,KAAKK,UAAA,EAAY,MAAM,GAAG,CAAC,GAE3D,KAAKC,SAAA,GAAY,GACjB,KAAKC,GAAA,GAAM,GACX,KAAKC,GAAA,GAAM,GACX,KAAKC,GAAA,GAAM,GACX,KAAKC,GAAA,GAAM,GACX,KAAKC,QAAA,GAAW,GAChB,KAAKC,eAAA,GAAkB,GAEvB,KAAKC,QAAA,GAAW,GAChB,KAAKC,SAAA,GAAY;EACrB;EAAA;EAGUb,SAAA,EACV;IACS,KAAAU,QAAA;EACT;EAAA;EAGUN,WAAA,EACV;IACI,KAAKE,GAAA,GAAMQ,IAAA,CAAKC,GAAA,CAAI,KAAKV,SAAA,GAAY,KAAKF,IAAA,CAAKa,CAAC,GAChD,KAAKT,GAAA,GAAMO,IAAA,CAAKG,GAAA,CAAI,KAAKZ,SAAA,GAAY,KAAKF,IAAA,CAAKa,CAAC,GAChD,KAAKR,GAAA,GAAM,CAACM,IAAA,CAAKG,GAAA,CAAI,KAAKZ,SAAA,GAAY,KAAKF,IAAA,CAAKe,CAAC,GACjD,KAAKT,GAAA,GAAMK,IAAA,CAAKC,GAAA,CAAI,KAAKV,SAAA,GAAY,KAAKF,IAAA,CAAKe,CAAC,GAEhD,KAAKR,QAAA;EACT;EAAA;EAGAS,qBAAA,EACA;IACI,MAAMC,EAAA,GAAK,KAAKvB,cAAA;IAEZ,KAAKa,QAAA,KAAa,KAAKC,eAAA,KAGvBS,EAAA,CAAGC,CAAA,GAAI,KAAKf,GAAA,GAAM,KAAKL,KAAA,CAAMiB,CAAA,EAC7BE,EAAA,CAAGE,CAAA,GAAI,KAAKf,GAAA,GAAM,KAAKN,KAAA,CAAMiB,CAAA,EAC7BE,EAAA,CAAGG,CAAA,GAAI,KAAKf,GAAA,GAAM,KAAKP,KAAA,CAAMe,CAAA,EAC7BI,EAAA,CAAGI,CAAA,GAAI,KAAKf,GAAA,GAAM,KAAKR,KAAA,CAAMe,CAAA,EAE7BI,EAAA,CAAGK,EAAA,GAAK,KAAK3B,QAAA,CAASoB,CAAA,IAAM,KAAKhB,KAAA,CAAMgB,CAAA,GAAIE,EAAA,CAAGC,CAAA,GAAM,KAAKnB,KAAA,CAAMc,CAAA,GAAII,EAAA,CAAGG,CAAA,GACtEH,EAAA,CAAGM,EAAA,GAAK,KAAK5B,QAAA,CAASkB,CAAA,IAAM,KAAKd,KAAA,CAAMgB,CAAA,GAAIE,EAAA,CAAGE,CAAA,GAAM,KAAKpB,KAAA,CAAMc,CAAA,GAAII,EAAA,CAAGI,CAAA,GACtE,KAAKb,eAAA,GAAkB,KAAKD,QAAA,EAG5B,KAAKG,SAAA,GAAY;EAEzB;EAAA;AAAA;AAAA;AAAA;EAMAc,gBAAgBC,eAAA,EAChB;IACI,MAAMR,EAAA,GAAK,KAAKvB,cAAA;IAkBhB,IAhBI,KAAKa,QAAA,KAAa,KAAKC,eAAA,KAGvBS,EAAA,CAAGC,CAAA,GAAI,KAAKf,GAAA,GAAM,KAAKL,KAAA,CAAMiB,CAAA,EAC7BE,EAAA,CAAGE,CAAA,GAAI,KAAKf,GAAA,GAAM,KAAKN,KAAA,CAAMiB,CAAA,EAC7BE,EAAA,CAAGG,CAAA,GAAI,KAAKf,GAAA,GAAM,KAAKP,KAAA,CAAMe,CAAA,EAC7BI,EAAA,CAAGI,CAAA,GAAI,KAAKf,GAAA,GAAM,KAAKR,KAAA,CAAMe,CAAA,EAE7BI,EAAA,CAAGK,EAAA,GAAK,KAAK3B,QAAA,CAASoB,CAAA,IAAM,KAAKhB,KAAA,CAAMgB,CAAA,GAAIE,EAAA,CAAGC,CAAA,GAAM,KAAKnB,KAAA,CAAMc,CAAA,GAAII,EAAA,CAAGG,CAAA,GACtEH,EAAA,CAAGM,EAAA,GAAK,KAAK5B,QAAA,CAASkB,CAAA,IAAM,KAAKd,KAAA,CAAMgB,CAAA,GAAIE,EAAA,CAAGE,CAAA,GAAM,KAAKpB,KAAA,CAAMc,CAAA,GAAII,EAAA,CAAGI,CAAA,GACtE,KAAKb,eAAA,GAAkB,KAAKD,QAAA,EAG5B,KAAKG,SAAA,GAAY,KAGjB,KAAKA,SAAA,KAAce,eAAA,CAAgBhB,QAAA,EACvC;MAEI,MAAMiB,EAAA,GAAKD,eAAA,CAAgBjC,cAAA;QACrBmC,EAAA,GAAK,KAAKnC,cAAA;MAEhBmC,EAAA,CAAGT,CAAA,GAAKD,EAAA,CAAGC,CAAA,GAAIQ,EAAA,CAAGR,CAAA,GAAMD,EAAA,CAAGE,CAAA,GAAIO,EAAA,CAAGN,CAAA,EAClCO,EAAA,CAAGR,CAAA,GAAKF,EAAA,CAAGC,CAAA,GAAIQ,EAAA,CAAGP,CAAA,GAAMF,EAAA,CAAGE,CAAA,GAAIO,EAAA,CAAGL,CAAA,EAClCM,EAAA,CAAGP,CAAA,GAAKH,EAAA,CAAGG,CAAA,GAAIM,EAAA,CAAGR,CAAA,GAAMD,EAAA,CAAGI,CAAA,GAAIK,EAAA,CAAGN,CAAA,EAClCO,EAAA,CAAGN,CAAA,GAAKJ,EAAA,CAAGG,CAAA,GAAIM,EAAA,CAAGP,CAAA,GAAMF,EAAA,CAAGI,CAAA,GAAIK,EAAA,CAAGL,CAAA,EAClCM,EAAA,CAAGL,EAAA,GAAML,EAAA,CAAGK,EAAA,GAAKI,EAAA,CAAGR,CAAA,GAAMD,EAAA,CAAGM,EAAA,GAAKG,EAAA,CAAGN,CAAA,GAAKM,EAAA,CAAGJ,EAAA,EAC7CK,EAAA,CAAGJ,EAAA,GAAMN,EAAA,CAAGK,EAAA,GAAKI,EAAA,CAAGP,CAAA,GAAMF,EAAA,CAAGM,EAAA,GAAKG,EAAA,CAAGL,CAAA,GAAKK,EAAA,CAAGH,EAAA,EAE7C,KAAKb,SAAA,GAAYe,eAAA,CAAgBhB,QAAA,EAGjC,KAAKA,QAAA;IACT;EACJ;EAAA;AAAA;AAAA;AAAA;EAMAmB,cAAcC,MAAA,EACd;IACWA,MAAA,CAAAC,SAAA,CAAU,IAAI,GACrB,KAAKvB,QAAA;EACT;EAAA;EAGA,IAAIwB,SAAA,EACJ;IACI,OAAO,KAAK7B,SAAA;EAChB;EAEA,IAAI6B,SAASC,KAAA,EACb;IACQ,KAAK9B,SAAA,KAAc8B,KAAA,KAEnB,KAAK9B,SAAA,GAAY8B,KAAA,EACjB,KAAK/B,UAAA,CAAW;EAExB;AACJ;AAlMaX,UAAA,CAOc2C,QAAA,GAAW,IAAI3C,UAAA;AAPnC,IAAM4C,SAAA,GAAN5C,UAAA;AAsMH4C,SAAA,CAAUC,SAAA,CAAUC,QAAA,GAAW,YAC/B;EACW,0CACY,KAAKzC,QAAA,CAASoB,CAAC,KAAK,KAAKpB,QAAA,CAASkB,CAAC,cACpC,KAAKkB,QAAQ,WACf,KAAKjC,KAAA,CAAMiB,CAAC,KAAK,KAAKjB,KAAA,CAAMe,CAAC,WAC9B,KAAKb,IAAA,CAAKe,CAAC,KAAK,KAAKf,IAAA,CAAKa,CAAC;AAE9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}