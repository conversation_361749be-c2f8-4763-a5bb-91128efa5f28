{"ast": null, "code": "import { Rectangle, Point } from \"@pixi/math\";\nimport { settings } from \"@pixi/settings\";\nimport { EventEmitter, TextureCache, uid, getResolutionOfUrl } from \"@pixi/utils\";\nimport { BaseTexture } from \"./BaseTexture.mjs\";\nimport { ImageResource } from \"./resources/ImageResource.mjs\";\nimport { TextureUvs } from \"./TextureUvs.mjs\";\nconst DEFAULT_UVS = new TextureUvs();\nfunction removeAllHandlers(tex) {\n  tex.destroy = function () {}, tex.on = function () {}, tex.once = function () {}, tex.emit = function () {};\n}\nclass Texture extends EventEmitter {\n  /**\n   * @param baseTexture - The base texture source to create the texture from\n   * @param frame - The rectangle frame of the texture to show\n   * @param orig - The area of original texture\n   * @param trim - Trimmed rectangle of original texture\n   * @param rotate - indicates how the texture was rotated by texture packer. See {@link PIXI.groupD8}\n   * @param anchor - Default anchor point used for sprite placement / rotation\n   * @param borders - Default borders used for 9-slice scaling. See {@link PIXI.NineSlicePlane}\n   */\n  constructor(baseTexture, frame, orig, trim, rotate, anchor, borders) {\n    if (super(), this.noFrame = !1, frame || (this.noFrame = !0, frame = new Rectangle(0, 0, 1, 1)), baseTexture instanceof Texture && (baseTexture = baseTexture.baseTexture), this.baseTexture = baseTexture, this._frame = frame, this.trim = trim, this.valid = !1, this.destroyed = !1, this._uvs = DEFAULT_UVS, this.uvMatrix = null, this.orig = orig || frame, this._rotate = Number(rotate || 0), rotate === !0) this._rotate = 2;else if (this._rotate % 2 !== 0) throw new Error(\"attempt to use diamond-shaped UVs. If you are sure, set rotation manually\");\n    this.defaultAnchor = anchor ? new Point(anchor.x, anchor.y) : new Point(0, 0), this.defaultBorders = borders, this._updateID = 0, this.textureCacheIds = [], baseTexture.valid ? this.noFrame ? baseTexture.valid && this.onBaseTextureUpdated(baseTexture) : this.frame = frame : baseTexture.once(\"loaded\", this.onBaseTextureUpdated, this), this.noFrame && baseTexture.on(\"update\", this.onBaseTextureUpdated, this);\n  }\n  /**\n   * Updates this texture on the gpu.\n   *\n   * Calls the TextureResource update.\n   *\n   * If you adjusted `frame` manually, please call `updateUvs()` instead.\n   */\n  update() {\n    this.baseTexture.resource && this.baseTexture.resource.update();\n  }\n  /**\n   * Called when the base texture is updated\n   * @protected\n   * @param baseTexture - The base texture.\n   */\n  onBaseTextureUpdated(baseTexture) {\n    if (this.noFrame) {\n      if (!this.baseTexture.valid) return;\n      this._frame.width = baseTexture.width, this._frame.height = baseTexture.height, this.valid = !0, this.updateUvs();\n    } else this.frame = this._frame;\n    this.emit(\"update\", this);\n  }\n  /**\n   * Destroys this texture\n   * @param [destroyBase=false] - Whether to destroy the base texture as well\n   * @fires PIXI.Texture#destroyed\n   */\n  destroy(destroyBase) {\n    if (this.baseTexture) {\n      if (destroyBase) {\n        const {\n          resource\n        } = this.baseTexture;\n        resource?.url && TextureCache[resource.url] && Texture.removeFromCache(resource.url), this.baseTexture.destroy();\n      }\n      this.baseTexture.off(\"loaded\", this.onBaseTextureUpdated, this), this.baseTexture.off(\"update\", this.onBaseTextureUpdated, this), this.baseTexture = null;\n    }\n    this._frame = null, this._uvs = null, this.trim = null, this.orig = null, this.valid = !1, Texture.removeFromCache(this), this.textureCacheIds = null, this.destroyed = !0, this.emit(\"destroyed\", this), this.removeAllListeners();\n  }\n  /**\n   * Creates a new texture object that acts the same as this one.\n   * @returns - The new texture\n   */\n  clone() {\n    const clonedFrame = this._frame.clone(),\n      clonedOrig = this._frame === this.orig ? clonedFrame : this.orig.clone(),\n      clonedTexture = new Texture(this.baseTexture, !this.noFrame && clonedFrame, clonedOrig, this.trim?.clone(), this.rotate, this.defaultAnchor, this.defaultBorders);\n    return this.noFrame && (clonedTexture._frame = clonedFrame), clonedTexture;\n  }\n  /**\n   * Updates the internal WebGL UV cache. Use it after you change `frame` or `trim` of the texture.\n   * Call it after changing the frame\n   */\n  updateUvs() {\n    this._uvs === DEFAULT_UVS && (this._uvs = new TextureUvs()), this._uvs.set(this._frame, this.baseTexture, this.rotate), this._updateID++;\n  }\n  /**\n   * Helper function that creates a new Texture based on the source you provide.\n   * The source can be - frame id, image url, video url, canvas element, video element, base texture\n   * @param {string|PIXI.BaseTexture|HTMLImageElement|HTMLVideoElement|ImageBitmap|PIXI.ICanvas} source -\n   *        Source or array of sources to create texture from\n   * @param options - See {@link PIXI.BaseTexture}'s constructor for options.\n   * @param {string} [options.pixiIdPrefix=pixiid] - If a source has no id, this is the prefix of the generated id\n   * @param {boolean} [strict] - Enforce strict-mode, see {@link PIXI.settings.STRICT_TEXTURE_CACHE}.\n   * @returns {PIXI.Texture} The newly created texture\n   */\n  static from(source, options = {}, strict = settings.STRICT_TEXTURE_CACHE) {\n    const isFrame = typeof source == \"string\";\n    let cacheId = null;\n    if (isFrame) cacheId = source;else if (source instanceof BaseTexture) {\n      if (!source.cacheId) {\n        const prefix = options?.pixiIdPrefix || \"pixiid\";\n        source.cacheId = `${prefix}-${uid()}`, BaseTexture.addToCache(source, source.cacheId);\n      }\n      cacheId = source.cacheId;\n    } else {\n      if (!source._pixiId) {\n        const prefix = options?.pixiIdPrefix || \"pixiid\";\n        source._pixiId = `${prefix}_${uid()}`;\n      }\n      cacheId = source._pixiId;\n    }\n    let texture = TextureCache[cacheId];\n    if (isFrame && strict && !texture) throw new Error(`The cacheId \"${cacheId}\" does not exist in TextureCache.`);\n    return !texture && !(source instanceof BaseTexture) ? (options.resolution || (options.resolution = getResolutionOfUrl(source)), texture = new Texture(new BaseTexture(source, options)), texture.baseTexture.cacheId = cacheId, BaseTexture.addToCache(texture.baseTexture, cacheId), Texture.addToCache(texture, cacheId)) : !texture && source instanceof BaseTexture && (texture = new Texture(source), Texture.addToCache(texture, cacheId)), texture;\n  }\n  /**\n   * Useful for loading textures via URLs. Use instead of `Texture.from` because\n   * it does a better job of handling failed URLs more effectively. This also ignores\n   * `PIXI.settings.STRICT_TEXTURE_CACHE`. Works for Videos, SVGs, Images.\n   * @param url - The remote URL or array of URLs to load.\n   * @param options - Optional options to include\n   * @returns - A Promise that resolves to a Texture.\n   */\n  static fromURL(url, options) {\n    const resourceOptions = Object.assign({\n        autoLoad: !1\n      }, options?.resourceOptions),\n      texture = Texture.from(url, Object.assign({\n        resourceOptions\n      }, options), !1),\n      resource = texture.baseTexture.resource;\n    return texture.baseTexture.valid ? Promise.resolve(texture) : resource.load().then(() => Promise.resolve(texture));\n  }\n  /**\n   * Create a new Texture with a BufferResource from a typed array.\n   * @param buffer - The optional array to use. If no data is provided, a new Float32Array is created.\n   * @param width - Width of the resource\n   * @param height - Height of the resource\n   * @param options - See {@link PIXI.BaseTexture}'s constructor for options.\n   *        Default properties are different from the constructor's defaults.\n   * @param {PIXI.FORMATS} [options.format] - The format is not given, the type is inferred from the\n   *        type of the buffer: `RGBA` if Float32Array, Int8Array, Uint8Array, or Uint8ClampedArray,\n   *        otherwise `RGBA_INTEGER`.\n   * @param {PIXI.TYPES} [options.type] - The type is not given, the type is inferred from the\n   *        type of the buffer. Maps Float32Array to `FLOAT`, Int32Array to `INT`, Uint32Array to\n   *        `UNSIGNED_INT`, Int16Array to `SHORT`, Uint16Array to `UNSIGNED_SHORT`, Int8Array to `BYTE`,\n   *        Uint8Array/Uint8ClampedArray to `UNSIGNED_BYTE`.\n   * @param {PIXI.ALPHA_MODES} [options.alphaMode=PIXI.ALPHA_MODES.NPM]\n   * @param {PIXI.SCALE_MODES} [options.scaleMode=PIXI.SCALE_MODES.NEAREST]\n   * @returns - The resulting new BaseTexture\n   */\n  static fromBuffer(buffer, width, height, options) {\n    return new Texture(BaseTexture.fromBuffer(buffer, width, height, options));\n  }\n  /**\n   * Create a texture from a source and add to the cache.\n   * @param {HTMLImageElement|HTMLVideoElement|ImageBitmap|PIXI.ICanvas|string} source - The input source.\n   * @param imageUrl - File name of texture, for cache and resolving resolution.\n   * @param name - Human readable name for the texture cache. If no name is\n   *        specified, only `imageUrl` will be used as the cache ID.\n   * @param options\n   * @returns - Output texture\n   */\n  static fromLoader(source, imageUrl, name, options) {\n    const baseTexture = new BaseTexture(source, Object.assign({\n        scaleMode: BaseTexture.defaultOptions.scaleMode,\n        resolution: getResolutionOfUrl(imageUrl)\n      }, options)),\n      {\n        resource\n      } = baseTexture;\n    resource instanceof ImageResource && (resource.url = imageUrl);\n    const texture = new Texture(baseTexture);\n    return name || (name = imageUrl), BaseTexture.addToCache(texture.baseTexture, name), Texture.addToCache(texture, name), name !== imageUrl && (BaseTexture.addToCache(texture.baseTexture, imageUrl), Texture.addToCache(texture, imageUrl)), texture.baseTexture.valid ? Promise.resolve(texture) : new Promise(resolve => {\n      texture.baseTexture.once(\"loaded\", () => resolve(texture));\n    });\n  }\n  /**\n   * Adds a Texture to the global TextureCache. This cache is shared across the whole PIXI object.\n   * @param texture - The Texture to add to the cache.\n   * @param id - The id that the Texture will be stored against.\n   */\n  static addToCache(texture, id) {\n    id && (texture.textureCacheIds.includes(id) || texture.textureCacheIds.push(id), TextureCache[id] && TextureCache[id] !== texture && console.warn(`Texture added to the cache with an id [${id}] that already had an entry`), TextureCache[id] = texture);\n  }\n  /**\n   * Remove a Texture from the global TextureCache.\n   * @param texture - id of a Texture to be removed, or a Texture instance itself\n   * @returns - The Texture that was removed\n   */\n  static removeFromCache(texture) {\n    if (typeof texture == \"string\") {\n      const textureFromCache = TextureCache[texture];\n      if (textureFromCache) {\n        const index = textureFromCache.textureCacheIds.indexOf(texture);\n        return index > -1 && textureFromCache.textureCacheIds.splice(index, 1), delete TextureCache[texture], textureFromCache;\n      }\n    } else if (texture?.textureCacheIds) {\n      for (let i = 0; i < texture.textureCacheIds.length; ++i) TextureCache[texture.textureCacheIds[i]] === texture && delete TextureCache[texture.textureCacheIds[i]];\n      return texture.textureCacheIds.length = 0, texture;\n    }\n    return null;\n  }\n  /**\n   * Returns resolution of baseTexture\n   * @readonly\n   */\n  get resolution() {\n    return this.baseTexture.resolution;\n  }\n  /**\n   * The frame specifies the region of the base texture that this texture uses.\n   * Please call `updateUvs()` after you change coordinates of `frame` manually.\n   */\n  get frame() {\n    return this._frame;\n  }\n  set frame(frame) {\n    this._frame = frame, this.noFrame = !1;\n    const {\n        x,\n        y,\n        width,\n        height\n      } = frame,\n      xNotFit = x + width > this.baseTexture.width,\n      yNotFit = y + height > this.baseTexture.height;\n    if (xNotFit || yNotFit) {\n      const relationship = xNotFit && yNotFit ? \"and\" : \"or\",\n        errorX = `X: ${x} + ${width} = ${x + width} > ${this.baseTexture.width}`,\n        errorY = `Y: ${y} + ${height} = ${y + height} > ${this.baseTexture.height}`;\n      throw new Error(`Texture Error: frame does not fit inside the base Texture dimensions: ${errorX} ${relationship} ${errorY}`);\n    }\n    this.valid = width && height && this.baseTexture.valid, !this.trim && !this.rotate && (this.orig = frame), this.valid && this.updateUvs();\n  }\n  /**\n   * Indicates whether the texture is rotated inside the atlas\n   * set to 2 to compensate for texture packer rotation\n   * set to 6 to compensate for spine packer rotation\n   * can be used to rotate or mirror sprites\n   * See {@link PIXI.groupD8} for explanation\n   */\n  get rotate() {\n    return this._rotate;\n  }\n  set rotate(rotate) {\n    this._rotate = rotate, this.valid && this.updateUvs();\n  }\n  /** The width of the Texture in pixels. */\n  get width() {\n    return this.orig.width;\n  }\n  /** The height of the Texture in pixels. */\n  get height() {\n    return this.orig.height;\n  }\n  /** Utility function for BaseTexture|Texture cast. */\n  castToBaseTexture() {\n    return this.baseTexture;\n  }\n  /** An empty texture, used often to not have to create multiple empty textures. Can not be destroyed. */\n  static get EMPTY() {\n    return Texture._EMPTY || (Texture._EMPTY = new Texture(new BaseTexture()), removeAllHandlers(Texture._EMPTY), removeAllHandlers(Texture._EMPTY.baseTexture)), Texture._EMPTY;\n  }\n  /** A white texture of 16x16 size, used for graphics and other things Can not be destroyed. */\n  static get WHITE() {\n    if (!Texture._WHITE) {\n      const canvas = settings.ADAPTER.createCanvas(16, 16),\n        context = canvas.getContext(\"2d\");\n      canvas.width = 16, canvas.height = 16, context.fillStyle = \"white\", context.fillRect(0, 0, 16, 16), Texture._WHITE = new Texture(BaseTexture.from(canvas)), removeAllHandlers(Texture._WHITE), removeAllHandlers(Texture._WHITE.baseTexture);\n    }\n    return Texture._WHITE;\n  }\n}\nexport { Texture };", "map": {"version": 3, "names": ["DEFAULT_UVS", "TextureUvs", "removeAllHandlers", "tex", "destroy", "on", "once", "emit", "Texture", "EventEmitter", "constructor", "baseTexture", "frame", "orig", "trim", "rotate", "anchor", "borders", "noFrame", "Rectangle", "_frame", "valid", "destroyed", "_uvs", "uvMatrix", "_rotate", "Number", "Error", "defaultAnchor", "Point", "x", "y", "defaultBorders", "_updateID", "textureCacheIds", "onBaseTextureUpdated", "update", "resource", "width", "height", "updateUvs", "destroyBase", "url", "TextureCache", "removeFromCache", "off", "removeAllListeners", "clone", "clonedFrame", "cloned<PERSON><PERSON>", "clonedTexture", "set", "from", "source", "options", "strict", "settings", "STRICT_TEXTURE_CACHE", "isFrame", "cacheId", "BaseTexture", "prefix", "pixiIdPrefix", "uid", "addToCache", "_pixiId", "texture", "resolution", "getResolutionOfUrl", "fromURL", "resourceOptions", "Object", "assign", "autoLoad", "Promise", "resolve", "load", "then", "fromBuffer", "buffer", "fromLoader", "imageUrl", "name", "scaleMode", "defaultOptions", "ImageResource", "id", "includes", "push", "console", "warn", "textureFromCache", "index", "indexOf", "splice", "i", "length", "xNotFit", "yNotFit", "relationship", "errorX", "errorY", "castToBaseTexture", "EMPTY", "_EMPTY", "WHITE", "_WHITE", "canvas", "ADAPTER", "createCanvas", "context", "getContext", "fillStyle", "fillRect"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\textures\\Texture.ts"], "sourcesContent": ["import { Point, Rectangle } from '@pixi/math';\nimport { settings } from '@pixi/settings';\nimport { EventEmitter, getResolutionOfUrl, TextureCache, uid } from '@pixi/utils';\nimport { BaseTexture } from './BaseTexture';\nimport { ImageResource } from './resources/ImageResource';\nimport { TextureUvs } from './TextureUvs';\n\nimport type { IPointData } from '@pixi/math';\nimport type { IBaseTextureOptions, ImageSource } from './BaseTexture';\nimport type { BufferResource, BufferType, IBufferResourceOptions } from './resources/BufferResource';\nimport type { CanvasResource } from './resources/CanvasResource';\nimport type { Resource } from './resources/Resource';\nimport type { TextureMatrix } from './TextureMatrix';\n\nconst DEFAULT_UVS = new TextureUvs();\n\nexport type TextureSource = string | BaseTexture | ImageSource;\n\n/**\n * Stores the width of the non-scalable borders, for example when used with {@link PIXI.NineSlicePlane} texture.\n * @memberof PIXI\n * @since 7.2.0\n */\nexport interface ITextureBorders\n{\n    /** left border in pixels */\n    left: number;\n    /** top border in pixels */\n    top: number;\n    /** right border in pixels */\n    right: number;\n    /** bottom border in pixels */\n    bottom: number;\n}\n\nexport interface Texture extends GlobalMixins.Texture, EventEmitter {}\n\n/**\n * Used to remove listeners from WHITE and EMPTY Textures\n * @ignore\n */\nfunction removeAllHandlers(tex: any): void\n{\n    tex.destroy = function _emptyDestroy(): void { /* empty */ };\n    tex.on = function _emptyOn(): void { /* empty */ };\n    tex.once = function _emptyOnce(): void { /* empty */ };\n    tex.emit = function _emptyEmit(): void { /* empty */ };\n}\n\n/**\n * A texture stores the information that represents an image or part of an image.\n *\n * It cannot be added to the display list directly; instead use it as the texture for a Sprite.\n * If no frame is provided for a texture, then the whole image is used.\n *\n * You can directly create a texture from an image and then reuse it multiple times like this :\n *\n * ```js\n * import { Sprite, Texture } from 'pixi.js';\n *\n * const texture = Texture.from('assets/image.png');\n * const sprite1 = new Sprite(texture);\n * const sprite2 = new Sprite(texture);\n * ```\n *\n * If you didnt pass the texture frame to constructor, it enables `noFrame` mode:\n * it subscribes on baseTexture events, it automatically resizes at the same time as baseTexture.\n *\n * Textures made from SVGs, loaded or not, cannot be used before the file finishes processing.\n * You can check for this by checking the sprite's _textureID property.\n *\n * ```js\n * import { Sprite, Texture } from 'pixi.js';\n *\n * const texture = Texture.from('assets/image.svg');\n * const sprite1 = new Sprite(texture);\n * // sprite1._textureID should not be undefined if the texture has finished processing the SVG file\n * ```\n *\n * You can use a ticker or rAF to ensure your sprites load the finished textures after processing.\n * See issue [#3085]{@link https://github.com/pixijs/pixijs/issues/3085}.\n * @memberof PIXI\n * @typeParam R - The BaseTexture's Resource type.\n */\nexport class Texture<R extends Resource = Resource> extends EventEmitter\n{\n    /** The base texture that this texture uses. */\n    public baseTexture: BaseTexture<R>;\n\n    /** This is the area of original texture, before it was put in atlas. */\n    public orig: Rectangle;\n\n    /**\n     * This is the trimmed area of original texture, before it was put in atlas\n     * Please call `updateUvs()` after you change coordinates of `trim` manually.\n     */\n    public trim: Rectangle;\n\n    /** This will let the renderer know if the texture is valid. If it's not then it cannot be rendered. */\n    public valid: boolean;\n\n    /**\n     * Has the texture been destroyed?\n     * @readonly\n     */\n    public destroyed: boolean;\n\n    /**\n     * Does this Texture have any frame data assigned to it?\n     *\n     * This mode is enabled automatically if no frame was passed inside constructor.\n     *\n     * In this mode texture is subscribed to baseTexture events, and fires `update` on any change.\n     *\n     * Beware, after loading or resize of baseTexture event can fired two times!\n     * If you want more control, subscribe on baseTexture itself.\n     *\n     * Any assignment of `frame` switches off `noFrame` mode.\n     * @example\n     * texture.on('update', () => {});\n     */\n    public noFrame: boolean;\n\n    /**\n     * Anchor point that is used as default if sprite is created with this texture.\n     * Changing the `defaultAnchor` at a later point of time will not update Sprite's anchor point.\n     * @default {0,0}\n     */\n    public defaultAnchor: Point;\n\n    /**\n     * Default width of the non-scalable border that is used if 9-slice plane is created with this texture.\n     * @since 7.2.0\n     * @see PIXI.NineSlicePlane\n     */\n    public defaultBorders?: ITextureBorders;\n\n    /** Default TextureMatrix instance for this texture. By default, that object is not created because its heavy. */\n    public uvMatrix: TextureMatrix;\n    protected _rotate: number;\n\n    /**\n     * Update ID is observed by sprites and TextureMatrix instances.\n     * Call updateUvs() to increment it.\n     * @protected\n     */\n    _updateID: number;\n\n    /**\n     * This is the area of the BaseTexture image to actually copy to the Canvas / WebGL when rendering,\n     * irrespective of the actual frame size or placement (which can be influenced by trimmed texture atlases)\n     */\n    _frame: Rectangle;\n\n    /**\n     * The WebGL UV data cache. Can be used as quad UV.\n     * @protected\n     */\n    _uvs: TextureUvs;\n\n    /**\n     * The ids under which this Texture has been added to the texture cache. This is\n     * automatically set as long as Texture.addToCache is used, but may not be set if a\n     * Texture is added directly to the TextureCache array.\n     */\n    textureCacheIds: Array<string>;\n\n    /**\n     * @param baseTexture - The base texture source to create the texture from\n     * @param frame - The rectangle frame of the texture to show\n     * @param orig - The area of original texture\n     * @param trim - Trimmed rectangle of original texture\n     * @param rotate - indicates how the texture was rotated by texture packer. See {@link PIXI.groupD8}\n     * @param anchor - Default anchor point used for sprite placement / rotation\n     * @param borders - Default borders used for 9-slice scaling. See {@link PIXI.NineSlicePlane}\n     */\n    constructor(baseTexture: BaseTexture<R>, frame?: Rectangle,\n        orig?: Rectangle, trim?: Rectangle, rotate?: number, anchor?: IPointData, borders?: ITextureBorders)\n    {\n        super();\n\n        this.noFrame = false;\n\n        if (!frame)\n        {\n            this.noFrame = true;\n            frame = new Rectangle(0, 0, 1, 1);\n        }\n\n        if (baseTexture instanceof Texture)\n        {\n            baseTexture = baseTexture.baseTexture;\n        }\n\n        this.baseTexture = baseTexture;\n        this._frame = frame;\n        this.trim = trim;\n        this.valid = false;\n        this.destroyed = false;\n        this._uvs = DEFAULT_UVS;\n        this.uvMatrix = null;\n        this.orig = orig || frame;// new Rectangle(0, 0, 1, 1);\n\n        this._rotate = Number(rotate || 0);\n\n        if (rotate as any === true)\n        {\n            // this is old texturepacker legacy, some games/libraries are passing \"true\" for rotated textures\n            this._rotate = 2;\n        }\n        else if (this._rotate % 2 !== 0)\n        {\n            throw new Error('attempt to use diamond-shaped UVs. If you are sure, set rotation manually');\n        }\n\n        this.defaultAnchor = anchor ? new Point(anchor.x, anchor.y) : new Point(0, 0);\n        this.defaultBorders = borders;\n\n        this._updateID = 0;\n\n        this.textureCacheIds = [];\n\n        if (!baseTexture.valid)\n        {\n            baseTexture.once('loaded', this.onBaseTextureUpdated, this);\n        }\n        else if (this.noFrame)\n        {\n            // if there is no frame we should monitor for any base texture changes..\n            if (baseTexture.valid)\n            {\n                this.onBaseTextureUpdated(baseTexture);\n            }\n        }\n        else\n        {\n            this.frame = frame;\n        }\n\n        if (this.noFrame)\n        {\n            baseTexture.on('update', this.onBaseTextureUpdated, this);\n        }\n    }\n\n    /**\n     * Updates this texture on the gpu.\n     *\n     * Calls the TextureResource update.\n     *\n     * If you adjusted `frame` manually, please call `updateUvs()` instead.\n     */\n    update(): void\n    {\n        if (this.baseTexture.resource)\n        {\n            this.baseTexture.resource.update();\n        }\n    }\n\n    /**\n     * Called when the base texture is updated\n     * @protected\n     * @param baseTexture - The base texture.\n     */\n    onBaseTextureUpdated(baseTexture: BaseTexture): void\n    {\n        if (this.noFrame)\n        {\n            if (!this.baseTexture.valid)\n            {\n                return;\n            }\n\n            this._frame.width = baseTexture.width;\n            this._frame.height = baseTexture.height;\n            this.valid = true;\n            this.updateUvs();\n        }\n        else\n        {\n            // TODO this code looks confusing.. boo to abusing getters and setters!\n            // if user gave us frame that has bigger size than resized texture it can be a problem\n            this.frame = this._frame;\n        }\n\n        this.emit('update', this);\n    }\n\n    /**\n     * Destroys this texture\n     * @param [destroyBase=false] - Whether to destroy the base texture as well\n     * @fires PIXI.Texture#destroyed\n     */\n    destroy(destroyBase?: boolean): void\n    {\n        if (this.baseTexture)\n        {\n            if (destroyBase)\n            {\n                const { resource } = this.baseTexture as unknown as BaseTexture<ImageResource>;\n\n                // delete the texture if it exists in the texture cache..\n                // this only needs to be removed if the base texture is actually destroyed too..\n                if (resource?.url && TextureCache[resource.url])\n                {\n                    Texture.removeFromCache(resource.url);\n                }\n\n                this.baseTexture.destroy();\n            }\n\n            this.baseTexture.off('loaded', this.onBaseTextureUpdated, this);\n            this.baseTexture.off('update', this.onBaseTextureUpdated, this);\n\n            this.baseTexture = null;\n        }\n\n        this._frame = null;\n        this._uvs = null;\n        this.trim = null;\n        this.orig = null;\n\n        this.valid = false;\n\n        Texture.removeFromCache(this);\n        this.textureCacheIds = null;\n\n        this.destroyed = true;\n        this.emit('destroyed', this);\n        this.removeAllListeners();\n    }\n\n    /**\n     * Creates a new texture object that acts the same as this one.\n     * @returns - The new texture\n     */\n    clone(): Texture\n    {\n        const clonedFrame = this._frame.clone();\n        const clonedOrig = this._frame === this.orig ? clonedFrame : this.orig.clone();\n        const clonedTexture = new Texture(this.baseTexture,\n            !this.noFrame && clonedFrame,\n            clonedOrig,\n            this.trim?.clone(),\n            this.rotate,\n            this.defaultAnchor,\n            this.defaultBorders\n        );\n\n        if (this.noFrame)\n        {\n            clonedTexture._frame = clonedFrame;\n        }\n\n        return clonedTexture;\n    }\n\n    /**\n     * Updates the internal WebGL UV cache. Use it after you change `frame` or `trim` of the texture.\n     * Call it after changing the frame\n     */\n    updateUvs(): void\n    {\n        if (this._uvs === DEFAULT_UVS)\n        {\n            this._uvs = new TextureUvs();\n        }\n\n        this._uvs.set(this._frame, this.baseTexture, this.rotate);\n\n        this._updateID++;\n    }\n\n    /**\n     * Helper function that creates a new Texture based on the source you provide.\n     * The source can be - frame id, image url, video url, canvas element, video element, base texture\n     * @param {string|PIXI.BaseTexture|HTMLImageElement|HTMLVideoElement|ImageBitmap|PIXI.ICanvas} source -\n     *        Source or array of sources to create texture from\n     * @param options - See {@link PIXI.BaseTexture}'s constructor for options.\n     * @param {string} [options.pixiIdPrefix=pixiid] - If a source has no id, this is the prefix of the generated id\n     * @param {boolean} [strict] - Enforce strict-mode, see {@link PIXI.settings.STRICT_TEXTURE_CACHE}.\n     * @returns {PIXI.Texture} The newly created texture\n     */\n    static from<R extends Resource = Resource, RO = any>(source: TextureSource | TextureSource[],\n        options: IBaseTextureOptions<RO> = {},\n        strict = settings.STRICT_TEXTURE_CACHE): Texture<R>\n    {\n        const isFrame = typeof source === 'string';\n        let cacheId = null;\n\n        if (isFrame)\n        {\n            cacheId = source;\n        }\n        else if (source instanceof BaseTexture)\n        {\n            if (!source.cacheId)\n            {\n                const prefix = options?.pixiIdPrefix || 'pixiid';\n\n                source.cacheId = `${prefix}-${uid()}`;\n                BaseTexture.addToCache(source, source.cacheId);\n            }\n\n            cacheId = source.cacheId;\n        }\n        else\n        {\n            if (!(source as any)._pixiId)\n            {\n                const prefix = options?.pixiIdPrefix || 'pixiid';\n\n                (source as any)._pixiId = `${prefix}_${uid()}`;\n            }\n\n            cacheId = (source as any)._pixiId;\n        }\n\n        let texture = TextureCache[cacheId] as Texture<R>;\n\n        // Strict-mode rejects invalid cacheIds\n        if (isFrame && strict && !texture)\n        {\n            throw new Error(`The cacheId \"${cacheId}\" does not exist in TextureCache.`);\n        }\n\n        if (!texture && !(source instanceof BaseTexture))\n        {\n            if (!options.resolution)\n            {\n                options.resolution = getResolutionOfUrl(source as string);\n            }\n\n            texture = new Texture<R>(new BaseTexture<R>(source, options));\n            texture.baseTexture.cacheId = cacheId;\n\n            BaseTexture.addToCache(texture.baseTexture, cacheId);\n            Texture.addToCache(texture, cacheId);\n        }\n        else if (!texture && (source instanceof BaseTexture))\n        {\n            texture = new Texture<R>(source as BaseTexture<R>);\n\n            Texture.addToCache(texture, cacheId);\n        }\n\n        // lets assume its a base texture!\n        return texture;\n    }\n\n    /**\n     * Useful for loading textures via URLs. Use instead of `Texture.from` because\n     * it does a better job of handling failed URLs more effectively. This also ignores\n     * `PIXI.settings.STRICT_TEXTURE_CACHE`. Works for Videos, SVGs, Images.\n     * @param url - The remote URL or array of URLs to load.\n     * @param options - Optional options to include\n     * @returns - A Promise that resolves to a Texture.\n     */\n    static fromURL<R extends Resource = Resource, RO = any>(\n        url: string | string[], options?: IBaseTextureOptions<RO>): Promise<Texture<R>>\n    {\n        const resourceOptions = Object.assign({ autoLoad: false }, options?.resourceOptions);\n        const texture = Texture.from<R>(url, Object.assign({ resourceOptions }, options), false);\n        const resource = texture.baseTexture.resource;\n\n        // The texture was already loaded\n        if (texture.baseTexture.valid)\n        {\n            return Promise.resolve(texture);\n        }\n\n        // Manually load the texture, this should allow users to handle load errors\n        return resource.load().then(() => Promise.resolve(texture));\n    }\n\n    /**\n     * Create a new Texture with a BufferResource from a typed array.\n     * @param buffer - The optional array to use. If no data is provided, a new Float32Array is created.\n     * @param width - Width of the resource\n     * @param height - Height of the resource\n     * @param options - See {@link PIXI.BaseTexture}'s constructor for options.\n     *        Default properties are different from the constructor's defaults.\n     * @param {PIXI.FORMATS} [options.format] - The format is not given, the type is inferred from the\n     *        type of the buffer: `RGBA` if Float32Array, Int8Array, Uint8Array, or Uint8ClampedArray,\n     *        otherwise `RGBA_INTEGER`.\n     * @param {PIXI.TYPES} [options.type] - The type is not given, the type is inferred from the\n     *        type of the buffer. Maps Float32Array to `FLOAT`, Int32Array to `INT`, Uint32Array to\n     *        `UNSIGNED_INT`, Int16Array to `SHORT`, Uint16Array to `UNSIGNED_SHORT`, Int8Array to `BYTE`,\n     *        Uint8Array/Uint8ClampedArray to `UNSIGNED_BYTE`.\n     * @param {PIXI.ALPHA_MODES} [options.alphaMode=PIXI.ALPHA_MODES.NPM]\n     * @param {PIXI.SCALE_MODES} [options.scaleMode=PIXI.SCALE_MODES.NEAREST]\n     * @returns - The resulting new BaseTexture\n     */\n    static fromBuffer(buffer: BufferType, width: number, height: number,\n        options?: IBaseTextureOptions<IBufferResourceOptions>): Texture<BufferResource>\n    {\n        return new Texture(BaseTexture.fromBuffer(buffer, width, height, options));\n    }\n\n    /**\n     * Create a texture from a source and add to the cache.\n     * @param {HTMLImageElement|HTMLVideoElement|ImageBitmap|PIXI.ICanvas|string} source - The input source.\n     * @param imageUrl - File name of texture, for cache and resolving resolution.\n     * @param name - Human readable name for the texture cache. If no name is\n     *        specified, only `imageUrl` will be used as the cache ID.\n     * @param options\n     * @returns - Output texture\n     */\n    static fromLoader<R extends Resource = Resource>(source: ImageSource | string,\n        imageUrl: string, name?: string, options?: IBaseTextureOptions): Promise<Texture<R>>\n    {\n        const baseTexture = new BaseTexture<R>(source, Object.assign({\n            scaleMode: BaseTexture.defaultOptions.scaleMode,\n            resolution: getResolutionOfUrl(imageUrl),\n        }, options));\n\n        const { resource } = baseTexture;\n\n        if (resource instanceof ImageResource)\n        {\n            resource.url = imageUrl;\n        }\n\n        const texture = new Texture<R>(baseTexture);\n\n        // No name, use imageUrl instead\n        if (!name)\n        {\n            name = imageUrl;\n        }\n\n        // lets also add the frame to pixi's global cache for 'fromLoader' function\n        BaseTexture.addToCache(texture.baseTexture, name);\n        Texture.addToCache(texture, name);\n\n        // also add references by url if they are different.\n        if (name !== imageUrl)\n        {\n            BaseTexture.addToCache(texture.baseTexture, imageUrl);\n            Texture.addToCache(texture, imageUrl);\n        }\n\n        // Generally images are valid right away\n        if (texture.baseTexture.valid)\n        {\n            return Promise.resolve(texture);\n        }\n\n        // SVG assets need to be parsed async, let's wait\n        return new Promise((resolve) =>\n        {\n            texture.baseTexture.once('loaded', () => resolve(texture));\n        });\n    }\n\n    /**\n     * Adds a Texture to the global TextureCache. This cache is shared across the whole PIXI object.\n     * @param texture - The Texture to add to the cache.\n     * @param id - The id that the Texture will be stored against.\n     */\n    static addToCache(texture: Texture, id: string): void\n    {\n        if (id)\n        {\n            if (!texture.textureCacheIds.includes(id))\n            {\n                texture.textureCacheIds.push(id);\n            }\n\n            // only throw a warning if there is a different texture mapped to this id.\n            if (TextureCache[id] && TextureCache[id] !== texture)\n            {\n                // eslint-disable-next-line no-console\n                console.warn(`Texture added to the cache with an id [${id}] that already had an entry`);\n            }\n\n            TextureCache[id] = texture;\n        }\n    }\n\n    /**\n     * Remove a Texture from the global TextureCache.\n     * @param texture - id of a Texture to be removed, or a Texture instance itself\n     * @returns - The Texture that was removed\n     */\n    static removeFromCache(texture: string | Texture): Texture | null\n    {\n        if (typeof texture === 'string')\n        {\n            const textureFromCache = TextureCache[texture];\n\n            if (textureFromCache)\n            {\n                const index = textureFromCache.textureCacheIds.indexOf(texture);\n\n                if (index > -1)\n                {\n                    textureFromCache.textureCacheIds.splice(index, 1);\n                }\n\n                delete TextureCache[texture];\n\n                return textureFromCache;\n            }\n        }\n        else if (texture?.textureCacheIds)\n        {\n            for (let i = 0; i < texture.textureCacheIds.length; ++i)\n            {\n                // Check that texture matches the one being passed in before deleting it from the cache.\n                if (TextureCache[texture.textureCacheIds[i]] === texture)\n                {\n                    delete TextureCache[texture.textureCacheIds[i]];\n                }\n            }\n\n            texture.textureCacheIds.length = 0;\n\n            return texture;\n        }\n\n        return null;\n    }\n\n    /**\n     * Returns resolution of baseTexture\n     * @readonly\n     */\n    get resolution(): number\n    {\n        return this.baseTexture.resolution;\n    }\n\n    /**\n     * The frame specifies the region of the base texture that this texture uses.\n     * Please call `updateUvs()` after you change coordinates of `frame` manually.\n     */\n    get frame(): Rectangle\n    {\n        return this._frame;\n    }\n\n    set frame(frame: Rectangle)\n    {\n        this._frame = frame;\n\n        this.noFrame = false;\n\n        const { x, y, width, height } = frame;\n        const xNotFit = x + width > this.baseTexture.width;\n        const yNotFit = y + height > this.baseTexture.height;\n\n        if (xNotFit || yNotFit)\n        {\n            const relationship = xNotFit && yNotFit ? 'and' : 'or';\n            const errorX = `X: ${x} + ${width} = ${x + width} > ${this.baseTexture.width}`;\n            const errorY = `Y: ${y} + ${height} = ${y + height} > ${this.baseTexture.height}`;\n\n            throw new Error('Texture Error: frame does not fit inside the base Texture dimensions: '\n                + `${errorX} ${relationship} ${errorY}`);\n        }\n\n        this.valid = width && height && this.baseTexture.valid;\n\n        if (!this.trim && !this.rotate)\n        {\n            this.orig = frame;\n        }\n\n        if (this.valid)\n        {\n            this.updateUvs();\n        }\n    }\n\n    /**\n     * Indicates whether the texture is rotated inside the atlas\n     * set to 2 to compensate for texture packer rotation\n     * set to 6 to compensate for spine packer rotation\n     * can be used to rotate or mirror sprites\n     * See {@link PIXI.groupD8} for explanation\n     */\n    get rotate(): number\n    {\n        return this._rotate;\n    }\n\n    set rotate(rotate: number)\n    {\n        this._rotate = rotate;\n        if (this.valid)\n        {\n            this.updateUvs();\n        }\n    }\n\n    /** The width of the Texture in pixels. */\n    get width(): number\n    {\n        return this.orig.width;\n    }\n\n    /** The height of the Texture in pixels. */\n    get height(): number\n    {\n        return this.orig.height;\n    }\n\n    /** Utility function for BaseTexture|Texture cast. */\n    castToBaseTexture(): BaseTexture\n    {\n        return this.baseTexture;\n    }\n\n    private static _EMPTY: Texture<Resource>;\n    private static _WHITE: Texture<CanvasResource>;\n\n    /** An empty texture, used often to not have to create multiple empty textures. Can not be destroyed. */\n    public static get EMPTY(): Texture<Resource>\n    {\n        if (!Texture._EMPTY)\n        {\n            Texture._EMPTY = new Texture(new BaseTexture());\n            removeAllHandlers(Texture._EMPTY);\n            removeAllHandlers(Texture._EMPTY.baseTexture);\n        }\n\n        return Texture._EMPTY;\n    }\n\n    /** A white texture of 16x16 size, used for graphics and other things Can not be destroyed. */\n    public static get WHITE(): Texture<CanvasResource>\n    {\n        if (!Texture._WHITE)\n        {\n            const canvas = settings.ADAPTER.createCanvas(16, 16);\n            const context = canvas.getContext('2d');\n\n            canvas.width = 16;\n            canvas.height = 16;\n            context.fillStyle = 'white';\n            context.fillRect(0, 0, 16, 16);\n\n            Texture._WHITE = new Texture(BaseTexture.from(canvas));\n            removeAllHandlers(Texture._WHITE);\n            removeAllHandlers(Texture._WHITE.baseTexture);\n        }\n\n        return Texture._WHITE;\n    }\n}\n\n"], "mappings": ";;;;;;AAcA,MAAMA,WAAA,GAAc,IAAIC,UAAA;AA2BxB,SAASC,kBAAkBC,GAAA,EAC3B;EACIA,GAAA,CAAIC,OAAA,GAAU,YAA+B,IAC7CD,GAAA,CAAIE,EAAA,GAAK,YAA0B,IACnCF,GAAA,CAAIG,IAAA,GAAO,YAA4B,IACvCH,GAAA,CAAII,IAAA,GAAO,YAA4B;AAC3C;AAqCO,MAAMC,OAAA,SAA+CC,YAAA,CAC5D;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EA2FIC,YAAYC,WAAA,EAA6BC,KAAA,EACrCC,IAAA,EAAkBC,IAAA,EAAkBC,MAAA,EAAiBC,MAAA,EAAqBC,OAAA,EAC9E;IAGS,IAFC,SAED,KAAAC,OAAA,GAAU,IAEVN,KAAA,KAED,KAAKM,OAAA,GAAU,IACfN,KAAA,GAAQ,IAAIO,SAAA,CAAU,GAAG,GAAG,GAAG,CAAC,IAGhCR,WAAA,YAAuBH,OAAA,KAEvBG,WAAA,GAAcA,WAAA,CAAYA,WAAA,GAG9B,KAAKA,WAAA,GAAcA,WAAA,EACnB,KAAKS,MAAA,GAASR,KAAA,EACd,KAAKE,IAAA,GAAOA,IAAA,EACZ,KAAKO,KAAA,GAAQ,IACb,KAAKC,SAAA,GAAY,IACjB,KAAKC,IAAA,GAAOvB,WAAA,EACZ,KAAKwB,QAAA,GAAW,MAChB,KAAKX,IAAA,GAAOA,IAAA,IAAQD,KAAA,EAEpB,KAAKa,OAAA,GAAUC,MAAA,CAAOX,MAAA,IAAU,CAAC,GAE7BA,MAAA,KAAkB,IAGlB,KAAKU,OAAA,GAAU,WAEV,KAAKA,OAAA,GAAU,MAAM,GAEpB,UAAIE,KAAA,CAAM,2EAA2E;IAG1F,KAAAC,aAAA,GAAgBZ,MAAA,GAAS,IAAIa,KAAA,CAAMb,MAAA,CAAOc,CAAA,EAAGd,MAAA,CAAOe,CAAC,IAAI,IAAIF,KAAA,CAAM,GAAG,CAAC,GAC5E,KAAKG,cAAA,GAAiBf,OAAA,EAEtB,KAAKgB,SAAA,GAAY,GAEjB,KAAKC,eAAA,GAAkB,IAElBvB,WAAA,CAAYU,KAAA,GAIR,KAAKH,OAAA,GAGNP,WAAA,CAAYU,KAAA,IAEZ,KAAKc,oBAAA,CAAqBxB,WAAW,IAKzC,KAAKC,KAAA,GAAQA,KAAA,GAZbD,WAAA,CAAYL,IAAA,CAAK,UAAU,KAAK6B,oBAAA,EAAsB,IAAI,GAe1D,KAAKjB,OAAA,IAELP,WAAA,CAAYN,EAAA,CAAG,UAAU,KAAK8B,oBAAA,EAAsB,IAAI;EAEhE;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAC,OAAA,EACA;IACQ,KAAKzB,WAAA,CAAY0B,QAAA,IAEjB,KAAK1B,WAAA,CAAY0B,QAAA,CAASD,MAAA;EAElC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAD,qBAAqBxB,WAAA,EACrB;IACI,IAAI,KAAKO,OAAA,EACT;MACQ,KAAC,KAAKP,WAAA,CAAYU,KAAA,EAElB;MAGJ,KAAKD,MAAA,CAAOkB,KAAA,GAAQ3B,WAAA,CAAY2B,KAAA,EAChC,KAAKlB,MAAA,CAAOmB,MAAA,GAAS5B,WAAA,CAAY4B,MAAA,EACjC,KAAKlB,KAAA,GAAQ,IACb,KAAKmB,SAAA;IACT,OAKI,KAAK5B,KAAA,GAAQ,KAAKQ,MAAA;IAGjB,KAAAb,IAAA,CAAK,UAAU,IAAI;EAC5B;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAH,QAAQqC,WAAA,EACR;IACI,IAAI,KAAK9B,WAAA,EACT;MACI,IAAI8B,WAAA,EACJ;QACU;UAAEJ;QAAS,IAAI,KAAK1B,WAAA;QAItB0B,QAAA,EAAUK,GAAA,IAAOC,YAAA,CAAaN,QAAA,CAASK,GAAG,KAE1ClC,OAAA,CAAQoC,eAAA,CAAgBP,QAAA,CAASK,GAAG,GAGxC,KAAK/B,WAAA,CAAYP,OAAA,CAAQ;MAC7B;MAEA,KAAKO,WAAA,CAAYkC,GAAA,CAAI,UAAU,KAAKV,oBAAA,EAAsB,IAAI,GAC9D,KAAKxB,WAAA,CAAYkC,GAAA,CAAI,UAAU,KAAKV,oBAAA,EAAsB,IAAI,GAE9D,KAAKxB,WAAA,GAAc;IACvB;IAEA,KAAKS,MAAA,GAAS,MACd,KAAKG,IAAA,GAAO,MACZ,KAAKT,IAAA,GAAO,MACZ,KAAKD,IAAA,GAAO,MAEZ,KAAKQ,KAAA,GAAQ,IAEbb,OAAA,CAAQoC,eAAA,CAAgB,IAAI,GAC5B,KAAKV,eAAA,GAAkB,MAEvB,KAAKZ,SAAA,GAAY,IACjB,KAAKf,IAAA,CAAK,aAAa,IAAI,GAC3B,KAAKuC,kBAAA;EACT;EAAA;AAAA;AAAA;AAAA;EAMAC,MAAA,EACA;IACI,MAAMC,WAAA,GAAc,KAAK5B,MAAA,CAAO2B,KAAA,CAAM;MAChCE,UAAA,GAAa,KAAK7B,MAAA,KAAW,KAAKP,IAAA,GAAOmC,WAAA,GAAc,KAAKnC,IAAA,CAAKkC,KAAA,CAAM;MACvEG,aAAA,GAAgB,IAAI1C,OAAA,CAAQ,KAAKG,WAAA,EACnC,CAAC,KAAKO,OAAA,IAAW8B,WAAA,EACjBC,UAAA,EACA,KAAKnC,IAAA,EAAMiC,KAAA,CAAM,GACjB,KAAKhC,MAAA,EACL,KAAKa,aAAA,EACL,KAAKI,cAAA;IAGT,OAAI,KAAKd,OAAA,KAELgC,aAAA,CAAc9B,MAAA,GAAS4B,WAAA,GAGpBE,aAAA;EACX;EAAA;AAAA;AAAA;AAAA;EAMAV,UAAA,EACA;IACQ,KAAKjB,IAAA,KAASvB,WAAA,KAEd,KAAKuB,IAAA,GAAO,IAAItB,UAAA,KAGpB,KAAKsB,IAAA,CAAK4B,GAAA,CAAI,KAAK/B,MAAA,EAAQ,KAAKT,WAAA,EAAa,KAAKI,MAAM,GAExD,KAAKkB,SAAA;EACT;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYA,OAAOmB,KAA8CC,MAAA,EACjDC,OAAA,GAAmC,CACnC,GAAAC,MAAA,GAASC,QAAA,CAASC,oBAAA,EACtB;IACU,MAAAC,OAAA,GAAU,OAAOL,MAAA,IAAW;IAClC,IAAIM,OAAA,GAAU;IAEV,IAAAD,OAAA,EAEUC,OAAA,GAAAN,MAAA,UAELA,MAAA,YAAkBO,WAAA,EAC3B;MACQ,KAACP,MAAA,CAAOM,OAAA,EACZ;QACU,MAAAE,MAAA,GAASP,OAAA,EAASQ,YAAA,IAAgB;QAEjCT,MAAA,CAAAM,OAAA,GAAU,GAAGE,MAAM,IAAIE,GAAA,EAAK,IACnCH,WAAA,CAAYI,UAAA,CAAWX,MAAA,EAAQA,MAAA,CAAOM,OAAO;MACjD;MAEAA,OAAA,GAAUN,MAAA,CAAOM,OAAA;IAAA,OAGrB;MACQ,KAAEN,MAAA,CAAeY,OAAA,EACrB;QACU,MAAAJ,MAAA,GAASP,OAAA,EAASQ,YAAA,IAAgB;QAEvCT,MAAA,CAAeY,OAAA,GAAU,GAAGJ,MAAM,IAAIE,GAAA,CAAK;MAChD;MAEAJ,OAAA,GAAWN,MAAA,CAAeY,OAAA;IAC9B;IAEI,IAAAC,OAAA,GAAUvB,YAAA,CAAagB,OAAO;IAG9B,IAAAD,OAAA,IAAWH,MAAA,IAAU,CAACW,OAAA,EAEtB,MAAM,IAAIvC,KAAA,CAAM,gBAAgBgC,OAAO,mCAAmC;IAG1E,QAACO,OAAA,IAAW,EAAEb,MAAA,YAAkBO,WAAA,KAE3BN,OAAA,CAAQa,UAAA,KAETb,OAAA,CAAQa,UAAA,GAAaC,kBAAA,CAAmBf,MAAgB,IAG5Da,OAAA,GAAU,IAAI1D,OAAA,CAAW,IAAIoD,WAAA,CAAeP,MAAA,EAAQC,OAAO,CAAC,GAC5DY,OAAA,CAAQvD,WAAA,CAAYgD,OAAA,GAAUA,OAAA,EAE9BC,WAAA,CAAYI,UAAA,CAAWE,OAAA,CAAQvD,WAAA,EAAagD,OAAO,GACnDnD,OAAA,CAAQwD,UAAA,CAAWE,OAAA,EAASP,OAAO,KAE9B,CAACO,OAAA,IAAYb,MAAA,YAAkBO,WAAA,KAEpCM,OAAA,GAAU,IAAI1D,OAAA,CAAW6C,MAAwB,GAEjD7C,OAAA,CAAQwD,UAAA,CAAWE,OAAA,EAASP,OAAO,IAIhCO,OAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUA,OAAOG,QACH3B,GAAA,EAAwBY,OAAA,EAC5B;IACU,MAAAgB,eAAA,GAAkBC,MAAA,CAAOC,MAAA,CAAO;QAAEC,QAAA,EAAU;MAAS,GAAAnB,OAAA,EAASgB,eAAe;MAC7EJ,OAAA,GAAU1D,OAAA,CAAQ4C,IAAA,CAAQV,GAAA,EAAK6B,MAAA,CAAOC,MAAA,CAAO;QAAEF;MAAgB,GAAGhB,OAAO,GAAG,EAAK;MACjFjB,QAAA,GAAW6B,OAAA,CAAQvD,WAAA,CAAY0B,QAAA;IAGrC,OAAI6B,OAAA,CAAQvD,WAAA,CAAYU,KAAA,GAEbqD,OAAA,CAAQC,OAAA,CAAQT,OAAO,IAI3B7B,QAAA,CAASuC,IAAA,GAAOC,IAAA,CAAK,MAAMH,OAAA,CAAQC,OAAA,CAAQT,OAAO,CAAC;EAC9D;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAoBA,OAAOY,WAAWC,MAAA,EAAoBzC,KAAA,EAAeC,MAAA,EACjDe,OAAA,EACJ;IACW,WAAI9C,OAAA,CAAQoD,WAAA,CAAYkB,UAAA,CAAWC,MAAA,EAAQzC,KAAA,EAAOC,MAAA,EAAQe,OAAO,CAAC;EAC7E;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWA,OAAO0B,WAA0C3B,MAAA,EAC7C4B,QAAA,EAAkBC,IAAA,EAAe5B,OAAA,EACrC;IACI,MAAM3C,WAAA,GAAc,IAAIiD,WAAA,CAAeP,MAAA,EAAQkB,MAAA,CAAOC,MAAA,CAAO;QACzDW,SAAA,EAAWvB,WAAA,CAAYwB,cAAA,CAAeD,SAAA;QACtChB,UAAA,EAAYC,kBAAA,CAAmBa,QAAQ;MAAA,GACxC3B,OAAO,CAAC;MAEL;QAAEjB;MAAA,IAAa1B,WAAA;IAEjB0B,QAAA,YAAoBgD,aAAA,KAEpBhD,QAAA,CAASK,GAAA,GAAMuC,QAAA;IAGb,MAAAf,OAAA,GAAU,IAAI1D,OAAA,CAAWG,WAAW;IAoB1C,OAjBKuE,IAAA,KAEDA,IAAA,GAAOD,QAAA,GAIXrB,WAAA,CAAYI,UAAA,CAAWE,OAAA,CAAQvD,WAAA,EAAauE,IAAI,GAChD1E,OAAA,CAAQwD,UAAA,CAAWE,OAAA,EAASgB,IAAI,GAG5BA,IAAA,KAASD,QAAA,KAETrB,WAAA,CAAYI,UAAA,CAAWE,OAAA,CAAQvD,WAAA,EAAasE,QAAQ,GACpDzE,OAAA,CAAQwD,UAAA,CAAWE,OAAA,EAASe,QAAQ,IAIpCf,OAAA,CAAQvD,WAAA,CAAYU,KAAA,GAEbqD,OAAA,CAAQC,OAAA,CAAQT,OAAO,IAI3B,IAAIQ,OAAA,CAASC,OAAA,IACpB;MACIT,OAAA,CAAQvD,WAAA,CAAYL,IAAA,CAAK,UAAU,MAAMqE,OAAA,CAAQT,OAAO,CAAC;IAAA,CAC5D;EACL;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,OAAOF,WAAWE,OAAA,EAAkBoB,EAAA,EACpC;IACQA,EAAA,KAEKpB,OAAA,CAAQhC,eAAA,CAAgBqD,QAAA,CAASD,EAAE,KAEpCpB,OAAA,CAAQhC,eAAA,CAAgBsD,IAAA,CAAKF,EAAE,GAI/B3C,YAAA,CAAa2C,EAAE,KAAK3C,YAAA,CAAa2C,EAAE,MAAMpB,OAAA,IAGzCuB,OAAA,CAAQC,IAAA,CAAK,0CAA0CJ,EAAE,6BAA6B,GAG1F3C,YAAA,CAAa2C,EAAE,IAAIpB,OAAA;EAE3B;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,OAAOtB,gBAAgBsB,OAAA,EACvB;IACQ,WAAOA,OAAA,IAAY,UACvB;MACU,MAAAyB,gBAAA,GAAmBhD,YAAA,CAAauB,OAAO;MAE7C,IAAIyB,gBAAA,EACJ;QACI,MAAMC,KAAA,GAAQD,gBAAA,CAAiBzD,eAAA,CAAgB2D,OAAA,CAAQ3B,OAAO;QAE1D,OAAA0B,KAAA,GAAQ,MAERD,gBAAA,CAAiBzD,eAAA,CAAgB4D,MAAA,CAAOF,KAAA,EAAO,CAAC,GAGpD,OAAOjD,YAAA,CAAauB,OAAO,GAEpByB,gBAAA;MACX;IAAA,WAEKzB,OAAA,EAAShC,eAAA,EAClB;MACI,SAAS6D,CAAA,GAAI,GAAGA,CAAA,GAAI7B,OAAA,CAAQhC,eAAA,CAAgB8D,MAAA,EAAQ,EAAED,CAAA,EAG9CpD,YAAA,CAAauB,OAAA,CAAQhC,eAAA,CAAgB6D,CAAC,CAAC,MAAM7B,OAAA,IAE7C,OAAOvB,YAAA,CAAauB,OAAA,CAAQhC,eAAA,CAAgB6D,CAAC,CAAC;MAI9C,OAAA7B,OAAA,CAAAhC,eAAA,CAAgB8D,MAAA,GAAS,GAE1B9B,OAAA;IACX;IAEO;EACX;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIC,WAAA,EACJ;IACI,OAAO,KAAKxD,WAAA,CAAYwD,UAAA;EAC5B;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIvD,MAAA,EACJ;IACI,OAAO,KAAKQ,MAAA;EAChB;EAEA,IAAIR,MAAMA,KAAA,EACV;IACS,KAAAQ,MAAA,GAASR,KAAA,EAEd,KAAKM,OAAA,GAAU;IAEf,MAAM;QAAEY,CAAA;QAAGC,CAAA;QAAGO,KAAA;QAAOC;MAAA,IAAW3B,KAAA;MAC1BqF,OAAA,GAAUnE,CAAA,GAAIQ,KAAA,GAAQ,KAAK3B,WAAA,CAAY2B,KAAA;MACvC4D,OAAA,GAAUnE,CAAA,GAAIQ,MAAA,GAAS,KAAK5B,WAAA,CAAY4B,MAAA;IAE9C,IAAI0D,OAAA,IAAWC,OAAA,EACf;MACI,MAAMC,YAAA,GAAeF,OAAA,IAAWC,OAAA,GAAU,QAAQ;QAC5CE,MAAA,GAAS,MAAMtE,CAAC,MAAMQ,KAAK,MAAMR,CAAA,GAAIQ,KAAK,MAAM,KAAK3B,WAAA,CAAY2B,KAAK;QACtE+D,MAAA,GAAS,MAAMtE,CAAC,MAAMQ,MAAM,MAAMR,CAAA,GAAIQ,MAAM,MAAM,KAAK5B,WAAA,CAAY4B,MAAM;MAEzE,UAAIZ,KAAA,CAAM,yEACPyE,MAAM,IAAID,YAAY,IAAIE,MAAM,EAAE;IAC/C;IAEA,KAAKhF,KAAA,GAAQiB,KAAA,IAASC,MAAA,IAAU,KAAK5B,WAAA,CAAYU,KAAA,EAE7C,CAAC,KAAKP,IAAA,IAAQ,CAAC,KAAKC,MAAA,KAEpB,KAAKF,IAAA,GAAOD,KAAA,GAGZ,KAAKS,KAAA,IAEL,KAAKmB,SAAA;EAEb;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASA,IAAIzB,OAAA,EACJ;IACI,OAAO,KAAKU,OAAA;EAChB;EAEA,IAAIV,OAAOA,MAAA,EACX;IACI,KAAKU,OAAA,GAAUV,MAAA,EACX,KAAKM,KAAA,IAEL,KAAKmB,SAAA;EAEb;EAAA;EAGA,IAAIF,MAAA,EACJ;IACI,OAAO,KAAKzB,IAAA,CAAKyB,KAAA;EACrB;EAAA;EAGA,IAAIC,OAAA,EACJ;IACI,OAAO,KAAK1B,IAAA,CAAK0B,MAAA;EACrB;EAAA;EAGA+D,kBAAA,EACA;IACI,OAAO,KAAK3F,WAAA;EAChB;EAAA;EAMA,WAAkB4F,MAAA,EAClB;IACI,OAAK/F,OAAA,CAAQgG,MAAA,KAEThG,OAAA,CAAQgG,MAAA,GAAS,IAAIhG,OAAA,CAAQ,IAAIoD,WAAA,CAAY,CAAC,GAC9C1D,iBAAA,CAAkBM,OAAA,CAAQgG,MAAM,GAChCtG,iBAAA,CAAkBM,OAAA,CAAQgG,MAAA,CAAO7F,WAAW,IAGzCH,OAAA,CAAQgG,MAAA;EACnB;EAAA;EAGA,WAAkBC,MAAA,EAClB;IACQ,KAACjG,OAAA,CAAQkG,MAAA,EACb;MACU,MAAAC,MAAA,GAASnD,QAAA,CAASoD,OAAA,CAAQC,YAAA,CAAa,IAAI,EAAE;QAC7CC,OAAA,GAAUH,MAAA,CAAOI,UAAA,CAAW,IAAI;MAEtCJ,MAAA,CAAOrE,KAAA,GAAQ,IACfqE,MAAA,CAAOpE,MAAA,GAAS,IAChBuE,OAAA,CAAQE,SAAA,GAAY,SACpBF,OAAA,CAAQG,QAAA,CAAS,GAAG,GAAG,IAAI,EAAE,GAE7BzG,OAAA,CAAQkG,MAAA,GAAS,IAAIlG,OAAA,CAAQoD,WAAA,CAAYR,IAAA,CAAKuD,MAAM,CAAC,GACrDzG,iBAAA,CAAkBM,OAAA,CAAQkG,MAAM,GAChCxG,iBAAA,CAAkBM,OAAA,CAAQkG,MAAA,CAAO/F,WAAW;IAChD;IAEA,OAAOH,OAAA,CAAQkG,MAAA;EACnB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}