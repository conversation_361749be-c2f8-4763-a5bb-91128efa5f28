{"ast": null, "code": "import { detectAvif } from \"./detectAvif.mjs\";\nimport { detectWebp } from \"./detectWebp.mjs\";\nimport { detectDefaults } from \"./detectDefaults.mjs\";\nimport { detectWebm } from \"./detectWebm.mjs\";\nimport { detectMp4 } from \"./detectMp4.mjs\";\nimport { detectOgv } from \"./detectOgv.mjs\";\nexport { detectAvif, detectDefaults, detectMp4, detectOgv, detectWebm, detectWebp };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import { detectAvif } from \"./detectAvif.mjs\";\nimport { detectWebp } from \"./detectWebp.mjs\";\nimport { detectDefaults } from \"./detectDefaults.mjs\";\nimport { detectWebm } from \"./detectWebm.mjs\";\nimport { detectMp4 } from \"./detectMp4.mjs\";\nimport { detectOgv } from \"./detectOgv.mjs\";\nexport {\n  detectAvif,\n  detectDefaults,\n  detectMp4,\n  detectOgv,\n  detectWebm,\n  detectWebp\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}