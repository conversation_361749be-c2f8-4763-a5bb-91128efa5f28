{"ast": null, "code": "import { DRAW_MODES } from \"@pixi/constants\";\nclass BatchDrawCall {\n  constructor() {\n    this.texArray = null, this.blend = 0, this.type = DRAW_MODES.TRIANGLES, this.start = 0, this.size = 0, this.data = null;\n  }\n}\nexport { BatchDrawCall };", "map": {"version": 3, "names": ["BatchDrawCall", "constructor", "texArray", "blend", "type", "DRAW_MODES", "TRIANGLES", "start", "size", "data"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\batch\\BatchDrawCall.ts"], "sourcesContent": ["import { DRAW_MODES } from '@pixi/constants';\n\nimport type { B<PERSON><PERSON>_MODES } from '@pixi/constants';\nimport type { BatchTextureArray } from './BatchTextureArray';\n\n/**\n * Used by the batcher to draw batches.\n * Each one of these contains all information required to draw a bound geometry.\n * @memberof PIXI\n */\nexport class BatchDrawCall\n{\n    texArray: BatchTextureArray;\n    type: DRAW_MODES;\n    blend: BLEND_MODES;\n    start: number;\n    size: number;\n\n    /** Data for uniforms or custom webgl state. */\n    data: any;\n\n    constructor()\n    {\n        this.texArray = null;\n        this.blend = 0;\n        this.type = DRAW_MODES.TRIANGLES;\n\n        this.start = 0;\n        this.size = 0;\n\n        this.data = null;\n    }\n}\n"], "mappings": ";AAUO,MAAMA,aAAA,CACb;EAUIC,YAAA,EACA;IACI,KAAKC,QAAA,GAAW,MAChB,KAAKC,KAAA,GAAQ,GACb,KAAKC,IAAA,GAAOC,UAAA,CAAWC,SAAA,EAEvB,KAAKC,KAAA,GAAQ,GACb,KAAKC,IAAA,GAAO,GAEZ,KAAKC,IAAA,GAAO;EAChB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}