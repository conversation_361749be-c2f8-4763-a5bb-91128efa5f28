{"ast": null, "code": "function sign(n) {\n  return n === 0 ? 0 : n < 0 ? -1 : 1;\n}\nexport { sign };", "map": {"version": 3, "names": ["sign", "n"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\utils\\src\\data\\sign.ts"], "sourcesContent": ["/**\n * Returns sign of number\n * @memberof PIXI.utils\n * @function sign\n * @param {number} n - the number to check the sign of\n * @returns {number} 0 if `n` is 0, -1 if `n` is negative, 1 if `n` is positive\n */\nexport function sign(n: number): -1 | 0 | 1\n{\n    if (n === 0) return 0;\n\n    return n < 0 ? -1 : 1;\n}\n"], "mappings": "AAOO,SAASA,KAAKC,CAAA,EACrB;EACI,OAAIA,CAAA,KAAM,IAAU,IAEbA,CAAA,GAAI,IAAI,KAAK;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}