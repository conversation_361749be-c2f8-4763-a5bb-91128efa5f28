{"ast": null, "code": "import { Color } from \"@pixi/color\";\nimport { deprecation } from \"../logging/deprecation.mjs\";\nfunction hex2rgb(hex, out = []) {\n  return deprecation(\"7.2.0\", \"utils.hex2rgb is deprecated, use Color#toRgbArray instead\"), Color.shared.setValue(hex).toRgbArray(out);\n}\nfunction hex2string(hex) {\n  return deprecation(\"7.2.0\", \"utils.hex2string is deprecated, use Color#toHex instead\"), Color.shared.setValue(hex).toHex();\n}\nfunction string2hex(string) {\n  return deprecation(\"7.2.0\", \"utils.string2hex is deprecated, use Color#toNumber instead\"), Color.shared.setValue(string).toNumber();\n}\nfunction rgb2hex(rgb) {\n  return deprecation(\"7.2.0\", \"utils.rgb2hex is deprecated, use Color#toNumber instead\"), Color.shared.setValue(rgb).toNumber();\n}\nexport { hex2rgb, hex2string, rgb2hex, string2hex };", "map": {"version": 3, "names": ["hex2rgb", "hex", "out", "deprecation", "Color", "shared", "setValue", "toRgbArray", "hex2string", "toHex", "string2hex", "string", "toNumber", "rgb2hex", "rgb"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\utils\\src\\color\\hex.ts"], "sourcesContent": ["import { Color } from '@pixi/color';\nimport { deprecation } from '../logging/deprecation';\n\n/**\n * Converts a hexadecimal color number to an [R, G, B] array of normalized floats (numbers from 0.0 to 1.0).\n * @memberof PIXI.utils\n * @function hex2rgb\n * @see PIXI.Color.toRgbArray\n * @deprecated since 7.2.0\n * @param {number} hex - The hexadecimal number to convert\n * @param  {number[]} [out=[]] - If supplied, this array will be used rather than returning a new one\n * @returns {number[]} An array representing the [R, G, B] of the color where all values are floats.\n */\nexport function hex2rgb(hex: number, out: Array<number> | Float32Array = []): Array<number> | Float32Array\n{\n    if (process.env.DEBUG)\n    {\n        deprecation('7.2.0', 'utils.hex2rgb is deprecated, use Color#toRgbArray instead');\n    }\n\n    return Color.shared.setValue(hex).toRgbArray(out);\n}\n\n/**\n * Converts a hexadecimal color number to a string.\n * @see PIXI.Color.toHex\n * @deprecated since 7.2.0\n * @memberof PIXI.utils\n * @function hex2string\n * @param {number} hex - Number in hex (e.g., `0xffffff`)\n * @returns {string} The string color (e.g., `\"#ffffff\"`).\n */\nexport function hex2string(hex: number): string\n{\n    if (process.env.DEBUG)\n    {\n        deprecation('7.2.0', 'utils.hex2string is deprecated, use Color#toHex instead');\n    }\n\n    return Color.shared.setValue(hex).toHex();\n}\n\n/**\n * Converts a string to a hexadecimal color number.\n * @deprecated since 7.2.0\n * @see PIXI.Color.toNumber\n * @memberof PIXI.utils\n * @function string2hex\n * @param {string} string - The string color (e.g., `\"#ffffff\"`)\n * @returns {number} Number in hexadecimal.\n */\nexport function string2hex(string: string): number\n{\n    if (process.env.DEBUG)\n    {\n        deprecation('7.2.0', 'utils.string2hex is deprecated, use Color#toNumber instead');\n    }\n\n    return Color.shared.setValue(string).toNumber();\n}\n\n/**\n * Converts a color as an [R, G, B] array of normalized floats to a hexadecimal number.\n * @deprecated since 7.2.0\n * @see PIXI.Color.toNumber\n * @memberof PIXI.utils\n * @function rgb2hex\n * @param {number[]} rgb - Array of numbers where all values are normalized floats from 0.0 to 1.0.\n * @returns {number} Number in hexadecimal.\n */\nexport function rgb2hex(rgb: number[] | Float32Array): number\n{\n    if (process.env.DEBUG)\n    {\n        deprecation('7.2.0', 'utils.rgb2hex is deprecated, use Color#toNumber instead');\n    }\n\n    return Color.shared.setValue(rgb).toNumber();\n}\n"], "mappings": ";;AAaO,SAASA,QAAQC,GAAA,EAAaC,GAAA,GAAoC,IACzE;EAGoB,OAAAC,WAAA,UAAS,2DAA2D,GAG7EC,KAAA,CAAMC,MAAA,CAAOC,QAAA,CAASL,GAAG,EAAEM,UAAA,CAAWL,GAAG;AACpD;AAWO,SAASM,WAAWP,GAAA,EAC3B;EAGoB,OAAAE,WAAA,UAAS,yDAAyD,GAG3EC,KAAA,CAAMC,MAAA,CAAOC,QAAA,CAASL,GAAG,EAAEQ,KAAA;AACtC;AAWO,SAASC,WAAWC,MAAA,EAC3B;EAGoB,OAAAR,WAAA,UAAS,4DAA4D,GAG9EC,KAAA,CAAMC,MAAA,CAAOC,QAAA,CAASK,MAAM,EAAEC,QAAA;AACzC;AAWO,SAASC,QAAQC,GAAA,EACxB;EAGoB,OAAAX,WAAA,UAAS,yDAAyD,GAG3EC,KAAA,CAAMC,MAAA,CAAOC,QAAA,CAASQ,GAAG,EAAEF,QAAA;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}