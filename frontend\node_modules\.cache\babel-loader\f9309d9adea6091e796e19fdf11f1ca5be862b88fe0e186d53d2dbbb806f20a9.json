{"ast": null, "code": "import { ObjectRenderer, Matrix, TYPES, Shader, State, utils, Color, ExtensionType, extensions } from \"@pixi/core\";\nimport { ParticleBuffer } from \"./ParticleBuffer.mjs\";\nimport fragment from \"./particles.frag.mjs\";\nimport vertex from \"./particles.vert.mjs\";\nclass ParticleRenderer extends ObjectRenderer {\n  /**\n   * @param renderer - The renderer this sprite batch works for.\n   */\n  constructor(renderer) {\n    super(renderer), this.shader = null, this.properties = null, this.tempMatrix = new Matrix(), this.properties = [\n    // verticesData\n    {\n      attributeName: \"aVertexPosition\",\n      size: 2,\n      uploadFunction: this.uploadVertices,\n      offset: 0\n    },\n    // positionData\n    {\n      attributeName: \"aPositionCoord\",\n      size: 2,\n      uploadFunction: this.uploadPosition,\n      offset: 0\n    },\n    // rotationData\n    {\n      attributeName: \"aRotation\",\n      size: 1,\n      uploadFunction: this.uploadRotation,\n      offset: 0\n    },\n    // uvsData\n    {\n      attributeName: \"aTextureCoord\",\n      size: 2,\n      uploadFunction: this.uploadUvs,\n      offset: 0\n    },\n    // tintData\n    {\n      attributeName: \"aColor\",\n      size: 1,\n      type: TYPES.UNSIGNED_BYTE,\n      uploadFunction: this.uploadTint,\n      offset: 0\n    }], this.shader = Shader.from(vertex, fragment, {}), this.state = State.for2d();\n  }\n  /**\n   * Renders the particle container object.\n   * @param container - The container to render using this ParticleRenderer.\n   */\n  render(container) {\n    const children = container.children,\n      maxSize = container._maxSize,\n      batchSize = container._batchSize,\n      renderer = this.renderer;\n    let totalChildren = children.length;\n    if (totalChildren === 0) return;\n    totalChildren > maxSize && !container.autoResize && (totalChildren = maxSize);\n    let buffers = container._buffers;\n    buffers || (buffers = container._buffers = this.generateBuffers(container));\n    const baseTexture = children[0]._texture.baseTexture,\n      premultiplied = baseTexture.alphaMode > 0;\n    this.state.blendMode = utils.correctBlendMode(container.blendMode, premultiplied), renderer.state.set(this.state);\n    const gl = renderer.gl,\n      m = container.worldTransform.copyTo(this.tempMatrix);\n    m.prepend(renderer.globalUniforms.uniforms.projectionMatrix), this.shader.uniforms.translationMatrix = m.toArray(!0), this.shader.uniforms.uColor = Color.shared.setValue(container.tintRgb).premultiply(container.worldAlpha, premultiplied).toArray(this.shader.uniforms.uColor), this.shader.uniforms.uSampler = baseTexture, this.renderer.shader.bind(this.shader);\n    let updateStatic = !1;\n    for (let i = 0, j = 0; i < totalChildren; i += batchSize, j += 1) {\n      let amount = totalChildren - i;\n      amount > batchSize && (amount = batchSize), j >= buffers.length && buffers.push(this._generateOneMoreBuffer(container));\n      const buffer = buffers[j];\n      buffer.uploadDynamic(children, i, amount);\n      const bid = container._bufferUpdateIDs[j] || 0;\n      updateStatic = updateStatic || buffer._updateID < bid, updateStatic && (buffer._updateID = container._updateID, buffer.uploadStatic(children, i, amount)), renderer.geometry.bind(buffer.geometry), gl.drawElements(gl.TRIANGLES, amount * 6, gl.UNSIGNED_SHORT, 0);\n    }\n  }\n  /**\n   * Creates one particle buffer for each child in the container we want to render and updates internal properties.\n   * @param container - The container to render using this ParticleRenderer\n   * @returns - The buffers\n   */\n  generateBuffers(container) {\n    const buffers = [],\n      size = container._maxSize,\n      batchSize = container._batchSize,\n      dynamicPropertyFlags = container._properties;\n    for (let i = 0; i < size; i += batchSize) buffers.push(new ParticleBuffer(this.properties, dynamicPropertyFlags, batchSize));\n    return buffers;\n  }\n  /**\n   * Creates one more particle buffer, because container has autoResize feature.\n   * @param container - The container to render using this ParticleRenderer\n   * @returns - The generated buffer\n   */\n  _generateOneMoreBuffer(container) {\n    const batchSize = container._batchSize,\n      dynamicPropertyFlags = container._properties;\n    return new ParticleBuffer(this.properties, dynamicPropertyFlags, batchSize);\n  }\n  /**\n   * Uploads the vertices.\n   * @param children - the array of sprites to render\n   * @param startIndex - the index to start from in the children array\n   * @param amount - the amount of children that will have their vertices uploaded\n   * @param array - The vertices to upload.\n   * @param stride - Stride to use for iteration.\n   * @param offset - Offset to start at.\n   */\n  uploadVertices(children, startIndex, amount, array, stride, offset) {\n    let w0 = 0,\n      w1 = 0,\n      h0 = 0,\n      h1 = 0;\n    for (let i = 0; i < amount; ++i) {\n      const sprite = children[startIndex + i],\n        texture = sprite._texture,\n        sx = sprite.scale.x,\n        sy = sprite.scale.y,\n        trim = texture.trim,\n        orig = texture.orig;\n      trim ? (w1 = trim.x - sprite.anchor.x * orig.width, w0 = w1 + trim.width, h1 = trim.y - sprite.anchor.y * orig.height, h0 = h1 + trim.height) : (w0 = orig.width * (1 - sprite.anchor.x), w1 = orig.width * -sprite.anchor.x, h0 = orig.height * (1 - sprite.anchor.y), h1 = orig.height * -sprite.anchor.y), array[offset] = w1 * sx, array[offset + 1] = h1 * sy, array[offset + stride] = w0 * sx, array[offset + stride + 1] = h1 * sy, array[offset + stride * 2] = w0 * sx, array[offset + stride * 2 + 1] = h0 * sy, array[offset + stride * 3] = w1 * sx, array[offset + stride * 3 + 1] = h0 * sy, offset += stride * 4;\n    }\n  }\n  /**\n   * Uploads the position.\n   * @param children - the array of sprites to render\n   * @param startIndex - the index to start from in the children array\n   * @param amount - the amount of children that will have their positions uploaded\n   * @param array - The vertices to upload.\n   * @param stride - Stride to use for iteration.\n   * @param offset - Offset to start at.\n   */\n  uploadPosition(children, startIndex, amount, array, stride, offset) {\n    for (let i = 0; i < amount; i++) {\n      const spritePosition = children[startIndex + i].position;\n      array[offset] = spritePosition.x, array[offset + 1] = spritePosition.y, array[offset + stride] = spritePosition.x, array[offset + stride + 1] = spritePosition.y, array[offset + stride * 2] = spritePosition.x, array[offset + stride * 2 + 1] = spritePosition.y, array[offset + stride * 3] = spritePosition.x, array[offset + stride * 3 + 1] = spritePosition.y, offset += stride * 4;\n    }\n  }\n  /**\n   * Uploads the rotation.\n   * @param children - the array of sprites to render\n   * @param startIndex - the index to start from in the children array\n   * @param amount - the amount of children that will have their rotation uploaded\n   * @param array - The vertices to upload.\n   * @param stride - Stride to use for iteration.\n   * @param offset - Offset to start at.\n   */\n  uploadRotation(children, startIndex, amount, array, stride, offset) {\n    for (let i = 0; i < amount; i++) {\n      const spriteRotation = children[startIndex + i].rotation;\n      array[offset] = spriteRotation, array[offset + stride] = spriteRotation, array[offset + stride * 2] = spriteRotation, array[offset + stride * 3] = spriteRotation, offset += stride * 4;\n    }\n  }\n  /**\n   * Uploads the UVs.\n   * @param children - the array of sprites to render\n   * @param startIndex - the index to start from in the children array\n   * @param amount - the amount of children that will have their rotation uploaded\n   * @param array - The vertices to upload.\n   * @param stride - Stride to use for iteration.\n   * @param offset - Offset to start at.\n   */\n  uploadUvs(children, startIndex, amount, array, stride, offset) {\n    for (let i = 0; i < amount; ++i) {\n      const textureUvs = children[startIndex + i]._texture._uvs;\n      textureUvs ? (array[offset] = textureUvs.x0, array[offset + 1] = textureUvs.y0, array[offset + stride] = textureUvs.x1, array[offset + stride + 1] = textureUvs.y1, array[offset + stride * 2] = textureUvs.x2, array[offset + stride * 2 + 1] = textureUvs.y2, array[offset + stride * 3] = textureUvs.x3, array[offset + stride * 3 + 1] = textureUvs.y3, offset += stride * 4) : (array[offset] = 0, array[offset + 1] = 0, array[offset + stride] = 0, array[offset + stride + 1] = 0, array[offset + stride * 2] = 0, array[offset + stride * 2 + 1] = 0, array[offset + stride * 3] = 0, array[offset + stride * 3 + 1] = 0, offset += stride * 4);\n    }\n  }\n  /**\n   * Uploads the tint.\n   * @param children - the array of sprites to render\n   * @param startIndex - the index to start from in the children array\n   * @param amount - the amount of children that will have their rotation uploaded\n   * @param array - The vertices to upload.\n   * @param stride - Stride to use for iteration.\n   * @param offset - Offset to start at.\n   */\n  uploadTint(children, startIndex, amount, array, stride, offset) {\n    for (let i = 0; i < amount; ++i) {\n      const sprite = children[startIndex + i],\n        result = Color.shared.setValue(sprite._tintRGB).toPremultiplied(sprite.alpha, sprite.texture.baseTexture.alphaMode > 0);\n      array[offset] = result, array[offset + stride] = result, array[offset + stride * 2] = result, array[offset + stride * 3] = result, offset += stride * 4;\n    }\n  }\n  /** Destroys the ParticleRenderer. */\n  destroy() {\n    super.destroy(), this.shader && (this.shader.destroy(), this.shader = null), this.tempMatrix = null;\n  }\n}\nParticleRenderer.extension = {\n  name: \"particle\",\n  type: ExtensionType.RendererPlugin\n};\nextensions.add(ParticleRenderer);\nexport { ParticleRenderer };", "map": {"version": 3, "names": ["Particle<PERSON><PERSON><PERSON>", "O<PERSON><PERSON><PERSON><PERSON>", "constructor", "renderer", "shader", "properties", "tempMatrix", "Matrix", "attributeName", "size", "uploadFunction", "uploadVertices", "offset", "uploadPosition", "uploadRotation", "uploadUvs", "type", "TYPES", "UNSIGNED_BYTE", "uploadTint", "Shader", "from", "vertex", "fragment", "state", "State", "for2d", "render", "container", "children", "maxSize", "_maxSize", "batchSize", "_batchSize", "totalChildren", "length", "autoResize", "buffers", "_buffers", "generateBuffers", "baseTexture", "_texture", "premultiplied", "alphaMode", "blendMode", "utils", "correctBlendMode", "set", "gl", "m", "worldTransform", "copyTo", "prepend", "globalUniforms", "uniforms", "projectionMatrix", "translationMatrix", "toArray", "uColor", "Color", "shared", "setValue", "tintRgb", "premultiply", "worldAlpha", "uSampler", "bind", "updateStatic", "i", "j", "amount", "push", "_generateOneMoreBuffer", "buffer", "uploadDynamic", "bid", "_bufferUpdateIDs", "_updateID", "uploadStatic", "geometry", "drawElements", "TRIANGLES", "UNSIGNED_SHORT", "dynamicPropertyFlags", "_properties", "ParticleBuffer", "startIndex", "array", "stride", "w0", "w1", "h0", "h1", "sprite", "texture", "sx", "scale", "x", "sy", "y", "trim", "orig", "anchor", "width", "height", "spritePosition", "position", "spriteRotation", "rotation", "textureUvs", "_uvs", "x0", "y0", "x1", "y1", "x2", "y2", "x3", "y3", "result", "_tintRGB", "toPremultiplied", "alpha", "destroy", "extension", "name", "ExtensionType", "RendererPlugin", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\particle-container\\src\\ParticleRenderer.ts"], "sourcesContent": ["import { Color, extensions, ExtensionType, Matrix, ObjectRenderer, Shader, State, TYPES, utils } from '@pixi/core';\nimport { ParticleBuffer } from './ParticleBuffer';\nimport fragment from './particles.frag';\nimport vertex from './particles.vert';\n\nimport type { ExtensionMetadata, Renderer } from '@pixi/core';\nimport type { Sprite } from '@pixi/sprite';\nimport type { ParticleContainer } from './ParticleContainer';\n\nexport interface IParticleRendererProperty\n{\n    attributeName: string;\n    size: number;\n    type?: TYPES;\n    uploadFunction: (...params: any[]) => any;\n    offset: number;\n}\n\n/*\n * <AUTHOR>\n *\n * Big thanks to the very clever <PERSON> <mattdesl> https://github.com/mattdesl/\n * for creating the original PixiJS version!\n * Also a thanks to https://github.com/bchevalier for tweaking the tint and alpha so that they now\n * share 4 bytes on the vertex buffer\n *\n * Heavily inspired by LibGDX's ParticleRenderer:\n * https://github.com/libgdx/libgdx/blob/master/gdx/src/com/badlogic/gdx/graphics/g2d/ParticleRenderer.java\n */\n\n/**\n * Renderer for Particles that is designer for speed over feature set.\n * @memberof PIXI\n */\nexport class ParticleRenderer extends ObjectRenderer\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'particle',\n        type: ExtensionType.RendererPlugin,\n    };\n\n    /** The WebGL state in which this renderer will work. */\n    public readonly state: State;\n\n    /** The default shader that is used if a sprite doesn't have a more specific one. */\n    public shader: Shader;\n    public tempMatrix: Matrix;\n    public properties: IParticleRendererProperty[];\n\n    /**\n     * @param renderer - The renderer this sprite batch works for.\n     */\n    constructor(renderer: Renderer)\n    {\n        super(renderer);\n\n        // 65535 is max vertex index in the index buffer (see ParticleRenderer)\n        // so max number of particles is 65536 / 4 = 16384\n        // and max number of element in the index buffer is 16384 * 6 = 98304\n        // Creating a full index buffer, overhead is 98304 * 2 = 196Ko\n        // let numIndices = 98304;\n\n        this.shader = null;\n\n        this.properties = null;\n\n        this.tempMatrix = new Matrix();\n\n        this.properties = [\n            // verticesData\n            {\n                attributeName: 'aVertexPosition',\n                size: 2,\n                uploadFunction: this.uploadVertices,\n                offset: 0,\n            },\n            // positionData\n            {\n                attributeName: 'aPositionCoord',\n                size: 2,\n                uploadFunction: this.uploadPosition,\n                offset: 0,\n            },\n            // rotationData\n            {\n                attributeName: 'aRotation',\n                size: 1,\n                uploadFunction: this.uploadRotation,\n                offset: 0,\n            },\n            // uvsData\n            {\n                attributeName: 'aTextureCoord',\n                size: 2,\n                uploadFunction: this.uploadUvs,\n                offset: 0,\n            },\n            // tintData\n            {\n                attributeName: 'aColor',\n                size: 1,\n                type: TYPES.UNSIGNED_BYTE,\n                uploadFunction: this.uploadTint,\n                offset: 0,\n            },\n        ];\n\n        this.shader = Shader.from(vertex, fragment, {});\n        this.state = State.for2d();\n    }\n\n    /**\n     * Renders the particle container object.\n     * @param container - The container to render using this ParticleRenderer.\n     */\n    public render(container: ParticleContainer): void\n    {\n        const children = container.children;\n        const maxSize = container._maxSize;\n        const batchSize = container._batchSize;\n        const renderer = this.renderer;\n        let totalChildren = children.length;\n\n        if (totalChildren === 0)\n        {\n            return;\n        }\n        else if (totalChildren > maxSize && !container.autoResize)\n        {\n            totalChildren = maxSize;\n        }\n\n        let buffers = container._buffers;\n\n        if (!buffers)\n        {\n            buffers = container._buffers = this.generateBuffers(container);\n        }\n\n        const baseTexture = children[0]._texture.baseTexture;\n        const premultiplied = baseTexture.alphaMode > 0;\n\n        // if the uvs have not updated then no point rendering just yet!\n        this.state.blendMode = utils.correctBlendMode(container.blendMode, premultiplied);\n        renderer.state.set(this.state);\n\n        const gl = renderer.gl;\n\n        const m = container.worldTransform.copyTo(this.tempMatrix);\n\n        m.prepend(renderer.globalUniforms.uniforms.projectionMatrix);\n\n        this.shader.uniforms.translationMatrix = m.toArray(true);\n\n        this.shader.uniforms.uColor = Color.shared\n            .setValue(container.tintRgb)\n            .premultiply(container.worldAlpha, premultiplied)\n            .toArray(this.shader.uniforms.uColor);\n\n        this.shader.uniforms.uSampler = baseTexture;\n\n        this.renderer.shader.bind(this.shader);\n\n        let updateStatic = false;\n\n        // now lets upload and render the buffers..\n        for (let i = 0, j = 0; i < totalChildren; i += batchSize, j += 1)\n        {\n            let amount = (totalChildren - i);\n\n            if (amount > batchSize)\n            {\n                amount = batchSize;\n            }\n\n            if (j >= buffers.length)\n            {\n                buffers.push(this._generateOneMoreBuffer(container));\n            }\n\n            const buffer = buffers[j];\n\n            // we always upload the dynamic\n            buffer.uploadDynamic(children, i, amount);\n\n            const bid = container._bufferUpdateIDs[j] || 0;\n\n            updateStatic = updateStatic || (buffer._updateID < bid);\n            // we only upload the static content when we have to!\n            if (updateStatic)\n            {\n                buffer._updateID = container._updateID;\n                buffer.uploadStatic(children, i, amount);\n            }\n\n            // bind the buffer\n            renderer.geometry.bind(buffer.geometry);\n            gl.drawElements(gl.TRIANGLES, amount * 6, gl.UNSIGNED_SHORT, 0);\n        }\n    }\n\n    /**\n     * Creates one particle buffer for each child in the container we want to render and updates internal properties.\n     * @param container - The container to render using this ParticleRenderer\n     * @returns - The buffers\n     */\n    private generateBuffers(container: ParticleContainer): ParticleBuffer[]\n    {\n        const buffers = [];\n        const size = container._maxSize;\n        const batchSize = container._batchSize;\n        const dynamicPropertyFlags = container._properties;\n\n        for (let i = 0; i < size; i += batchSize)\n        {\n            buffers.push(new ParticleBuffer(this.properties, dynamicPropertyFlags, batchSize));\n        }\n\n        return buffers;\n    }\n\n    /**\n     * Creates one more particle buffer, because container has autoResize feature.\n     * @param container - The container to render using this ParticleRenderer\n     * @returns - The generated buffer\n     */\n    private _generateOneMoreBuffer(container: ParticleContainer): ParticleBuffer\n    {\n        const batchSize = container._batchSize;\n        const dynamicPropertyFlags = container._properties;\n\n        return new ParticleBuffer(this.properties, dynamicPropertyFlags, batchSize);\n    }\n\n    /**\n     * Uploads the vertices.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their vertices uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadVertices(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        let w0 = 0;\n        let w1 = 0;\n        let h0 = 0;\n        let h1 = 0;\n\n        for (let i = 0; i < amount; ++i)\n        {\n            const sprite = children[startIndex + i];\n            const texture = sprite._texture;\n            const sx = sprite.scale.x;\n            const sy = sprite.scale.y;\n            const trim = texture.trim;\n            const orig = texture.orig;\n\n            if (trim)\n            {\n                // if the sprite is trimmed and is not a tilingsprite then we need to add the\n                // extra space before transforming the sprite coords..\n                w1 = trim.x - (sprite.anchor.x * orig.width);\n                w0 = w1 + trim.width;\n\n                h1 = trim.y - (sprite.anchor.y * orig.height);\n                h0 = h1 + trim.height;\n            }\n            else\n            {\n                w0 = (orig.width) * (1 - sprite.anchor.x);\n                w1 = (orig.width) * -sprite.anchor.x;\n\n                h0 = orig.height * (1 - sprite.anchor.y);\n                h1 = orig.height * -sprite.anchor.y;\n            }\n\n            array[offset] = w1 * sx;\n            array[offset + 1] = h1 * sy;\n\n            array[offset + stride] = w0 * sx;\n            array[offset + stride + 1] = h1 * sy;\n\n            array[offset + (stride * 2)] = w0 * sx;\n            array[offset + (stride * 2) + 1] = h0 * sy;\n\n            array[offset + (stride * 3)] = w1 * sx;\n            array[offset + (stride * 3) + 1] = h0 * sy;\n\n            offset += stride * 4;\n        }\n    }\n\n    /**\n     * Uploads the position.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their positions uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadPosition(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; i++)\n        {\n            const spritePosition = children[startIndex + i].position;\n\n            array[offset] = spritePosition.x;\n            array[offset + 1] = spritePosition.y;\n\n            array[offset + stride] = spritePosition.x;\n            array[offset + stride + 1] = spritePosition.y;\n\n            array[offset + (stride * 2)] = spritePosition.x;\n            array[offset + (stride * 2) + 1] = spritePosition.y;\n\n            array[offset + (stride * 3)] = spritePosition.x;\n            array[offset + (stride * 3) + 1] = spritePosition.y;\n\n            offset += stride * 4;\n        }\n    }\n\n    /**\n     * Uploads the rotation.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their rotation uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadRotation(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; i++)\n        {\n            const spriteRotation = children[startIndex + i].rotation;\n\n            array[offset] = spriteRotation;\n            array[offset + stride] = spriteRotation;\n            array[offset + (stride * 2)] = spriteRotation;\n            array[offset + (stride * 3)] = spriteRotation;\n\n            offset += stride * 4;\n        }\n    }\n\n    /**\n     * Uploads the UVs.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their rotation uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadUvs(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; ++i)\n        {\n            const textureUvs = children[startIndex + i]._texture._uvs;\n\n            if (textureUvs)\n            {\n                array[offset] = textureUvs.x0;\n                array[offset + 1] = textureUvs.y0;\n\n                array[offset + stride] = textureUvs.x1;\n                array[offset + stride + 1] = textureUvs.y1;\n\n                array[offset + (stride * 2)] = textureUvs.x2;\n                array[offset + (stride * 2) + 1] = textureUvs.y2;\n\n                array[offset + (stride * 3)] = textureUvs.x3;\n                array[offset + (stride * 3) + 1] = textureUvs.y3;\n\n                offset += stride * 4;\n            }\n            else\n            {\n                // TODO you know this can be easier!\n                array[offset] = 0;\n                array[offset + 1] = 0;\n\n                array[offset + stride] = 0;\n                array[offset + stride + 1] = 0;\n\n                array[offset + (stride * 2)] = 0;\n                array[offset + (stride * 2) + 1] = 0;\n\n                array[offset + (stride * 3)] = 0;\n                array[offset + (stride * 3) + 1] = 0;\n\n                offset += stride * 4;\n            }\n        }\n    }\n\n    /**\n     * Uploads the tint.\n     * @param children - the array of sprites to render\n     * @param startIndex - the index to start from in the children array\n     * @param amount - the amount of children that will have their rotation uploaded\n     * @param array - The vertices to upload.\n     * @param stride - Stride to use for iteration.\n     * @param offset - Offset to start at.\n     */\n    public uploadTint(\n        children: Sprite[], startIndex: number, amount: number,\n        array: number[], stride: number, offset: number\n    ): void\n    {\n        for (let i = 0; i < amount; ++i)\n        {\n            const sprite = children[startIndex + i];\n            const result = Color.shared\n                .setValue(sprite._tintRGB)\n                .toPremultiplied(sprite.alpha, sprite.texture.baseTexture.alphaMode > 0);\n\n            array[offset] = result;\n            array[offset + stride] = result;\n            array[offset + (stride * 2)] = result;\n            array[offset + (stride * 3)] = result;\n\n            offset += stride * 4;\n        }\n    }\n\n    /** Destroys the ParticleRenderer. */\n    public destroy(): void\n    {\n        super.destroy();\n\n        if (this.shader)\n        {\n            this.shader.destroy();\n            this.shader = null;\n        }\n\n        this.tempMatrix = null;\n    }\n}\n\nextensions.add(ParticleRenderer);\n"], "mappings": ";;;;AAkCO,MAAMA,gBAAA,SAAyBC,cAAA,CACtC;EAAA;AAAA;AAAA;EAkBIC,YAAYC,QAAA,EACZ;IACI,MAAMA,QAAQ,GAQT,KAAAC,MAAA,GAAS,MAEd,KAAKC,UAAA,GAAa,MAElB,KAAKC,UAAA,GAAa,IAAIC,MAAA,IAEtB,KAAKF,UAAA,GAAa;IAAA;IAEd;MACIG,aAAA,EAAe;MACfC,IAAA,EAAM;MACNC,cAAA,EAAgB,KAAKC,cAAA;MACrBC,MAAA,EAAQ;IACZ;IAAA;IAEA;MACIJ,aAAA,EAAe;MACfC,IAAA,EAAM;MACNC,cAAA,EAAgB,KAAKG,cAAA;MACrBD,MAAA,EAAQ;IACZ;IAAA;IAEA;MACIJ,aAAA,EAAe;MACfC,IAAA,EAAM;MACNC,cAAA,EAAgB,KAAKI,cAAA;MACrBF,MAAA,EAAQ;IACZ;IAAA;IAEA;MACIJ,aAAA,EAAe;MACfC,IAAA,EAAM;MACNC,cAAA,EAAgB,KAAKK,SAAA;MACrBH,MAAA,EAAQ;IACZ;IAAA;IAEA;MACIJ,aAAA,EAAe;MACfC,IAAA,EAAM;MACNO,IAAA,EAAMC,KAAA,CAAMC,aAAA;MACZR,cAAA,EAAgB,KAAKS,UAAA;MACrBP,MAAA,EAAQ;IACZ,EAGJ,OAAKR,MAAA,GAASgB,MAAA,CAAOC,IAAA,CAAKC,MAAA,EAAQC,QAAA,EAAU,CAAE,IAC9C,KAAKC,KAAA,GAAQC,KAAA,CAAMC,KAAA,CAAM;EAC7B;EAAA;AAAA;AAAA;AAAA;EAMOC,OAAOC,SAAA,EACd;IACU,MAAAC,QAAA,GAAWD,SAAA,CAAUC,QAAA;MACrBC,OAAA,GAAUF,SAAA,CAAUG,QAAA;MACpBC,SAAA,GAAYJ,SAAA,CAAUK,UAAA;MACtB9B,QAAA,GAAW,KAAKA,QAAA;IACtB,IAAI+B,aAAA,GAAgBL,QAAA,CAASM,MAAA;IAE7B,IAAID,aAAA,KAAkB,GAElB;IAEKA,aAAA,GAAgBJ,OAAA,IAAW,CAACF,SAAA,CAAUQ,UAAA,KAE3CF,aAAA,GAAgBJ,OAAA;IAGpB,IAAIO,OAAA,GAAUT,SAAA,CAAUU,QAAA;IAEnBD,OAAA,KAEDA,OAAA,GAAUT,SAAA,CAAUU,QAAA,GAAW,KAAKC,eAAA,CAAgBX,SAAS;IAG3D,MAAAY,WAAA,GAAcX,QAAA,CAAS,CAAC,EAAEY,QAAA,CAASD,WAAA;MACnCE,aAAA,GAAgBF,WAAA,CAAYG,SAAA,GAAY;IAG9C,KAAKnB,KAAA,CAAMoB,SAAA,GAAYC,KAAA,CAAMC,gBAAA,CAAiBlB,SAAA,CAAUgB,SAAA,EAAWF,aAAa,GAChFvC,QAAA,CAASqB,KAAA,CAAMuB,GAAA,CAAI,KAAKvB,KAAK;IAEvB,MAAAwB,EAAA,GAAK7C,QAAA,CAAS6C,EAAA;MAEdC,CAAA,GAAIrB,SAAA,CAAUsB,cAAA,CAAeC,MAAA,CAAO,KAAK7C,UAAU;IAEvD2C,CAAA,CAAAG,OAAA,CAAQjD,QAAA,CAASkD,cAAA,CAAeC,QAAA,CAASC,gBAAgB,GAE3D,KAAKnD,MAAA,CAAOkD,QAAA,CAASE,iBAAA,GAAoBP,CAAA,CAAEQ,OAAA,CAAQ,EAAI,GAEvD,KAAKrD,MAAA,CAAOkD,QAAA,CAASI,MAAA,GAASC,KAAA,CAAMC,MAAA,CAC/BC,QAAA,CAASjC,SAAA,CAAUkC,OAAO,EAC1BC,WAAA,CAAYnC,SAAA,CAAUoC,UAAA,EAAYtB,aAAa,EAC/Ce,OAAA,CAAQ,KAAKrD,MAAA,CAAOkD,QAAA,CAASI,MAAM,GAExC,KAAKtD,MAAA,CAAOkD,QAAA,CAASW,QAAA,GAAWzB,WAAA,EAEhC,KAAKrC,QAAA,CAASC,MAAA,CAAO8D,IAAA,CAAK,KAAK9D,MAAM;IAErC,IAAI+D,YAAA,GAAe;IAGV,SAAAC,CAAA,GAAI,GAAGC,CAAA,GAAI,GAAGD,CAAA,GAAIlC,aAAA,EAAekC,CAAA,IAAKpC,SAAA,EAAWqC,CAAA,IAAK,GAC/D;MACI,IAAIC,MAAA,GAAUpC,aAAA,GAAgBkC,CAAA;MAE1BE,MAAA,GAAStC,SAAA,KAETsC,MAAA,GAAStC,SAAA,GAGTqC,CAAA,IAAKhC,OAAA,CAAQF,MAAA,IAEbE,OAAA,CAAQkC,IAAA,CAAK,KAAKC,sBAAA,CAAuB5C,SAAS,CAAC;MAGjD,MAAA6C,MAAA,GAASpC,OAAA,CAAQgC,CAAC;MAGjBI,MAAA,CAAAC,aAAA,CAAc7C,QAAA,EAAUuC,CAAA,EAAGE,MAAM;MAExC,MAAMK,GAAA,GAAM/C,SAAA,CAAUgD,gBAAA,CAAiBP,CAAC,KAAK;MAE7CF,YAAA,GAAeA,YAAA,IAAiBM,MAAA,CAAOI,SAAA,GAAYF,GAAA,EAE/CR,YAAA,KAEAM,MAAA,CAAOI,SAAA,GAAYjD,SAAA,CAAUiD,SAAA,EAC7BJ,MAAA,CAAOK,YAAA,CAAajD,QAAA,EAAUuC,CAAA,EAAGE,MAAM,IAI3CnE,QAAA,CAAS4E,QAAA,CAASb,IAAA,CAAKO,MAAA,CAAOM,QAAQ,GACtC/B,EAAA,CAAGgC,YAAA,CAAahC,EAAA,CAAGiC,SAAA,EAAWX,MAAA,GAAS,GAAGtB,EAAA,CAAGkC,cAAA,EAAgB,CAAC;IAClE;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;EAOQ3C,gBAAgBX,SAAA,EACxB;IACU,MAAAS,OAAA,GAAU;MACV5B,IAAA,GAAOmB,SAAA,CAAUG,QAAA;MACjBC,SAAA,GAAYJ,SAAA,CAAUK,UAAA;MACtBkD,oBAAA,GAAuBvD,SAAA,CAAUwD,WAAA;IAEvC,SAAShB,CAAA,GAAI,GAAGA,CAAA,GAAI3D,IAAA,EAAM2D,CAAA,IAAKpC,SAAA,EAE3BK,OAAA,CAAQkC,IAAA,CAAK,IAAIc,cAAA,CAAe,KAAKhF,UAAA,EAAY8E,oBAAA,EAAsBnD,SAAS,CAAC;IAG9E,OAAAK,OAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOQmC,uBAAuB5C,SAAA,EAC/B;IACI,MAAMI,SAAA,GAAYJ,SAAA,CAAUK,UAAA;MACtBkD,oBAAA,GAAuBvD,SAAA,CAAUwD,WAAA;IAEvC,OAAO,IAAIC,cAAA,CAAe,KAAKhF,UAAA,EAAY8E,oBAAA,EAAsBnD,SAAS;EAC9E;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWOrB,eACHkB,QAAA,EAAoByD,UAAA,EAAoBhB,MAAA,EACxCiB,KAAA,EAAiBC,MAAA,EAAgB5E,MAAA,EAErC;IACI,IAAI6E,EAAA,GAAK;MACLC,EAAA,GAAK;MACLC,EAAA,GAAK;MACLC,EAAA,GAAK;IAET,SAASxB,CAAA,GAAI,GAAGA,CAAA,GAAIE,MAAA,EAAQ,EAAEF,CAAA,EAC9B;MACU,MAAAyB,MAAA,GAAShE,QAAA,CAASyD,UAAA,GAAalB,CAAC;QAChC0B,OAAA,GAAUD,MAAA,CAAOpD,QAAA;QACjBsD,EAAA,GAAKF,MAAA,CAAOG,KAAA,CAAMC,CAAA;QAClBC,EAAA,GAAKL,MAAA,CAAOG,KAAA,CAAMG,CAAA;QAClBC,IAAA,GAAON,OAAA,CAAQM,IAAA;QACfC,IAAA,GAAOP,OAAA,CAAQO,IAAA;MAEjBD,IAAA,IAIAV,EAAA,GAAKU,IAAA,CAAKH,CAAA,GAAKJ,MAAA,CAAOS,MAAA,CAAOL,CAAA,GAAII,IAAA,CAAKE,KAAA,EACtCd,EAAA,GAAKC,EAAA,GAAKU,IAAA,CAAKG,KAAA,EAEfX,EAAA,GAAKQ,IAAA,CAAKD,CAAA,GAAKN,MAAA,CAAOS,MAAA,CAAOH,CAAA,GAAIE,IAAA,CAAKG,MAAA,EACtCb,EAAA,GAAKC,EAAA,GAAKQ,IAAA,CAAKI,MAAA,KAIff,EAAA,GAAMY,IAAA,CAAKE,KAAA,IAAU,IAAIV,MAAA,CAAOS,MAAA,CAAOL,CAAA,GACvCP,EAAA,GAAMW,IAAA,CAAKE,KAAA,GAAS,CAACV,MAAA,CAAOS,MAAA,CAAOL,CAAA,EAEnCN,EAAA,GAAKU,IAAA,CAAKG,MAAA,IAAU,IAAIX,MAAA,CAAOS,MAAA,CAAOH,CAAA,GACtCP,EAAA,GAAKS,IAAA,CAAKG,MAAA,GAAS,CAACX,MAAA,CAAOS,MAAA,CAAOH,CAAA,GAGtCZ,KAAA,CAAM3E,MAAM,IAAI8E,EAAA,GAAKK,EAAA,EACrBR,KAAA,CAAM3E,MAAA,GAAS,CAAC,IAAIgF,EAAA,GAAKM,EAAA,EAEzBX,KAAA,CAAM3E,MAAA,GAAS4E,MAAM,IAAIC,EAAA,GAAKM,EAAA,EAC9BR,KAAA,CAAM3E,MAAA,GAAS4E,MAAA,GAAS,CAAC,IAAII,EAAA,GAAKM,EAAA,EAElCX,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,CAAE,IAAIC,EAAA,GAAKM,EAAA,EACpCR,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,IAAK,CAAC,IAAIG,EAAA,GAAKO,EAAA,EAExCX,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,CAAE,IAAIE,EAAA,GAAKK,EAAA,EACpCR,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,IAAK,CAAC,IAAIG,EAAA,GAAKO,EAAA,EAExCtF,MAAA,IAAU4E,MAAA,GAAS;IACvB;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWO3E,eACHgB,QAAA,EAAoByD,UAAA,EAAoBhB,MAAA,EACxCiB,KAAA,EAAiBC,MAAA,EAAgB5E,MAAA,EAErC;IACI,SAASwD,CAAA,GAAI,GAAGA,CAAA,GAAIE,MAAA,EAAQF,CAAA,IAC5B;MACI,MAAMqC,cAAA,GAAiB5E,QAAA,CAASyD,UAAA,GAAalB,CAAC,EAAEsC,QAAA;MAE1CnB,KAAA,CAAA3E,MAAM,IAAI6F,cAAA,CAAeR,CAAA,EAC/BV,KAAA,CAAM3E,MAAA,GAAS,CAAC,IAAI6F,cAAA,CAAeN,CAAA,EAEnCZ,KAAA,CAAM3E,MAAA,GAAS4E,MAAM,IAAIiB,cAAA,CAAeR,CAAA,EACxCV,KAAA,CAAM3E,MAAA,GAAS4E,MAAA,GAAS,CAAC,IAAIiB,cAAA,CAAeN,CAAA,EAE5CZ,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,CAAE,IAAIiB,cAAA,CAAeR,CAAA,EAC9CV,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,IAAK,CAAC,IAAIiB,cAAA,CAAeN,CAAA,EAElDZ,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,CAAE,IAAIiB,cAAA,CAAeR,CAAA,EAC9CV,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,IAAK,CAAC,IAAIiB,cAAA,CAAeN,CAAA,EAElDvF,MAAA,IAAU4E,MAAA,GAAS;IACvB;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWO1E,eACHe,QAAA,EAAoByD,UAAA,EAAoBhB,MAAA,EACxCiB,KAAA,EAAiBC,MAAA,EAAgB5E,MAAA,EAErC;IACI,SAASwD,CAAA,GAAI,GAAGA,CAAA,GAAIE,MAAA,EAAQF,CAAA,IAC5B;MACI,MAAMuC,cAAA,GAAiB9E,QAAA,CAASyD,UAAA,GAAalB,CAAC,EAAEwC,QAAA;MAE1CrB,KAAA,CAAA3E,MAAM,IAAI+F,cAAA,EAChBpB,KAAA,CAAM3E,MAAA,GAAS4E,MAAM,IAAImB,cAAA,EACzBpB,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,CAAE,IAAImB,cAAA,EAC/BpB,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,CAAE,IAAImB,cAAA,EAE/B/F,MAAA,IAAU4E,MAAA,GAAS;IACvB;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWOzE,UACHc,QAAA,EAAoByD,UAAA,EAAoBhB,MAAA,EACxCiB,KAAA,EAAiBC,MAAA,EAAgB5E,MAAA,EAErC;IACI,SAASwD,CAAA,GAAI,GAAGA,CAAA,GAAIE,MAAA,EAAQ,EAAEF,CAAA,EAC9B;MACI,MAAMyC,UAAA,GAAahF,QAAA,CAASyD,UAAA,GAAalB,CAAC,EAAE3B,QAAA,CAASqE,IAAA;MAEjDD,UAAA,IAEAtB,KAAA,CAAM3E,MAAM,IAAIiG,UAAA,CAAWE,EAAA,EAC3BxB,KAAA,CAAM3E,MAAA,GAAS,CAAC,IAAIiG,UAAA,CAAWG,EAAA,EAE/BzB,KAAA,CAAM3E,MAAA,GAAS4E,MAAM,IAAIqB,UAAA,CAAWI,EAAA,EACpC1B,KAAA,CAAM3E,MAAA,GAAS4E,MAAA,GAAS,CAAC,IAAIqB,UAAA,CAAWK,EAAA,EAExC3B,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,CAAE,IAAIqB,UAAA,CAAWM,EAAA,EAC1C5B,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,IAAK,CAAC,IAAIqB,UAAA,CAAWO,EAAA,EAE9C7B,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,CAAE,IAAIqB,UAAA,CAAWQ,EAAA,EAC1C9B,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,IAAK,CAAC,IAAIqB,UAAA,CAAWS,EAAA,EAE9C1G,MAAA,IAAU4E,MAAA,GAAS,MAKnBD,KAAA,CAAM3E,MAAM,IAAI,GAChB2E,KAAA,CAAM3E,MAAA,GAAS,CAAC,IAAI,GAEpB2E,KAAA,CAAM3E,MAAA,GAAS4E,MAAM,IAAI,GACzBD,KAAA,CAAM3E,MAAA,GAAS4E,MAAA,GAAS,CAAC,IAAI,GAE7BD,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,CAAE,IAAI,GAC/BD,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,IAAK,CAAC,IAAI,GAEnCD,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,CAAE,IAAI,GAC/BD,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,IAAK,CAAC,IAAI,GAEnC5E,MAAA,IAAU4E,MAAA,GAAS;IAE3B;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWOrE,WACHU,QAAA,EAAoByD,UAAA,EAAoBhB,MAAA,EACxCiB,KAAA,EAAiBC,MAAA,EAAgB5E,MAAA,EAErC;IACI,SAASwD,CAAA,GAAI,GAAGA,CAAA,GAAIE,MAAA,EAAQ,EAAEF,CAAA,EAC9B;MACI,MAAMyB,MAAA,GAAShE,QAAA,CAASyD,UAAA,GAAalB,CAAC;QAChCmD,MAAA,GAAS5D,KAAA,CAAMC,MAAA,CAChBC,QAAA,CAASgC,MAAA,CAAO2B,QAAQ,EACxBC,eAAA,CAAgB5B,MAAA,CAAO6B,KAAA,EAAO7B,MAAA,CAAOC,OAAA,CAAQtD,WAAA,CAAYG,SAAA,GAAY,CAAC;MAErE4C,KAAA,CAAA3E,MAAM,IAAI2G,MAAA,EAChBhC,KAAA,CAAM3E,MAAA,GAAS4E,MAAM,IAAI+B,MAAA,EACzBhC,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,CAAE,IAAI+B,MAAA,EAC/BhC,KAAA,CAAM3E,MAAA,GAAU4E,MAAA,GAAS,CAAE,IAAI+B,MAAA,EAE/B3G,MAAA,IAAU4E,MAAA,GAAS;IACvB;EACJ;EAAA;EAGOmC,QAAA,EACP;IACI,MAAMA,OAAA,CAAQ,GAEV,KAAKvH,MAAA,KAEL,KAAKA,MAAA,CAAOuH,OAAA,CAAQ,GACpB,KAAKvH,MAAA,GAAS,OAGlB,KAAKE,UAAA,GAAa;EACtB;AACJ;AAtaaN,gBAAA,CAGF4H,SAAA,GAA+B;EAClCC,IAAA,EAAM;EACN7G,IAAA,EAAM8G,aAAA,CAAcC;AACxB;AAkaJC,UAAA,CAAWC,GAAA,CAAIjI,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}