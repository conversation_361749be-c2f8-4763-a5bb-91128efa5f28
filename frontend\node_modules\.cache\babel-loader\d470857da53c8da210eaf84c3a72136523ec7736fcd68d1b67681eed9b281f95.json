{"ast": null, "code": "import { Shader, Matrix, Program, TextureMatrix, Color } from \"@pixi/core\";\nimport fragment from \"./shader/mesh.frag.mjs\";\nimport vertex from \"./shader/mesh.vert.mjs\";\nclass MeshMaterial extends Shader {\n  /**\n   * @param uSampler - Texture that material uses to render.\n   * @param options - Additional options\n   * @param {number} [options.alpha=1] - Default alpha.\n   * @param {PIXI.ColorSource} [options.tint=0xFFFFFF] - Default tint.\n   * @param {string} [options.pluginName='batch'] - Renderer plugin for batching.\n   * @param {PIXI.Program} [options.program=0xFFFFFF] - Custom program.\n   * @param {object} [options.uniforms] - Custom uniforms.\n   */\n  constructor(uSampler, options) {\n    const uniforms = {\n      uSampler,\n      alpha: 1,\n      uTextureMatrix: Matrix.IDENTITY,\n      uColor: new Float32Array([1, 1, 1, 1])\n    };\n    options = Object.assign({\n      tint: 16777215,\n      alpha: 1,\n      pluginName: \"batch\"\n    }, options), options.uniforms && Object.assign(uniforms, options.uniforms), super(options.program || Program.from(vertex, fragment), uniforms), this._colorDirty = !1, this.uvMatrix = new TextureMatrix(uSampler), this.batchable = options.program === void 0, this.pluginName = options.pluginName, this._tintColor = new Color(options.tint), this._tintRGB = this._tintColor.toLittleEndianNumber(), this._colorDirty = !0, this.alpha = options.alpha;\n  }\n  /** Reference to the texture being rendered. */\n  get texture() {\n    return this.uniforms.uSampler;\n  }\n  set texture(value) {\n    this.uniforms.uSampler !== value && (!this.uniforms.uSampler.baseTexture.alphaMode != !value.baseTexture.alphaMode && (this._colorDirty = !0), this.uniforms.uSampler = value, this.uvMatrix.texture = value);\n  }\n  /**\n   * This gets automatically set by the object using this.\n   * @default 1\n   */\n  set alpha(value) {\n    value !== this._alpha && (this._alpha = value, this._colorDirty = !0);\n  }\n  get alpha() {\n    return this._alpha;\n  }\n  /**\n   * Multiply tint for the material.\n   * @default 0xFFFFFF\n   */\n  set tint(value) {\n    value !== this.tint && (this._tintColor.setValue(value), this._tintRGB = this._tintColor.toLittleEndianNumber(), this._colorDirty = !0);\n  }\n  get tint() {\n    return this._tintColor.value;\n  }\n  /**\n   * Get the internal number from tint color\n   * @ignore\n   */\n  get tintValue() {\n    return this._tintColor.toNumber();\n  }\n  /** Gets called automatically by the Mesh. Intended to be overridden for custom {@link PIXI.MeshMaterial} objects. */\n  update() {\n    if (this._colorDirty) {\n      this._colorDirty = !1;\n      const applyToChannels = this.texture.baseTexture.alphaMode;\n      Color.shared.setValue(this._tintColor).premultiply(this._alpha, applyToChannels).toArray(this.uniforms.uColor);\n    }\n    this.uvMatrix.update() && (this.uniforms.uTextureMatrix = this.uvMatrix.mapCoord);\n  }\n}\nexport { MeshMaterial };", "map": {"version": 3, "names": ["MeshMaterial", "Shader", "constructor", "uSampler", "options", "uniforms", "alpha", "uTextureMatrix", "Matrix", "IDENTITY", "uColor", "Float32Array", "Object", "assign", "tint", "pluginName", "program", "Program", "from", "vertex", "fragment", "_colorDirty", "uvMatrix", "TextureMatrix", "batchable", "_tintColor", "Color", "_tintRGB", "toLittleEndianNumber", "texture", "value", "baseTexture", "alphaMode", "_alpha", "setValue", "tintValue", "toNumber", "update", "applyToChannels", "shared", "premultiply", "toArray", "mapCoord"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\mesh\\src\\MeshMaterial.ts"], "sourcesContent": ["import { Color, Matrix, Program, Shader, TextureMatrix } from '@pixi/core';\nimport fragment from './shader/mesh.frag';\nimport vertex from './shader/mesh.vert';\n\nimport type { ColorSource, Texture, utils } from '@pixi/core';\n\nexport interface IMeshMaterialOptions\n{\n    alpha?: number;\n    tint?: ColorSource;\n    pluginName?: string;\n    program?: Program;\n    uniforms?: utils.Dict<unknown>;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface MeshMaterial extends GlobalMixins.MeshMaterial {}\n\n/**\n * Slightly opinionated default shader for PixiJS 2D objects.\n * @memberof PIXI\n */\nexport class MeshMaterial extends Shader\n{\n    /**\n     * TextureMatrix instance for this Mesh, used to track Texture changes.\n     * @readonly\n     */\n    public readonly uvMatrix: TextureMatrix;\n\n    /**\n     * `true` if shader can be batch with the renderer's batch system.\n     * @default true\n     */\n    public batchable: boolean;\n\n    /**\n     * Renderer plugin for batching.\n     * @default 'batch'\n     */\n    public pluginName: string;\n\n    // Internal-only properties\n    _tintRGB: number;\n\n    /**\n     * Only do update if tint or alpha changes.\n     * @private\n     * @default false\n     */\n    private _colorDirty: boolean;\n    private _alpha: number;\n    private _tintColor: Color;\n\n    /**\n     * @param uSampler - Texture that material uses to render.\n     * @param options - Additional options\n     * @param {number} [options.alpha=1] - Default alpha.\n     * @param {PIXI.ColorSource} [options.tint=0xFFFFFF] - Default tint.\n     * @param {string} [options.pluginName='batch'] - Renderer plugin for batching.\n     * @param {PIXI.Program} [options.program=0xFFFFFF] - Custom program.\n     * @param {object} [options.uniforms] - Custom uniforms.\n     */\n    constructor(uSampler: Texture, options?: IMeshMaterialOptions)\n    {\n        const uniforms = {\n            uSampler,\n            alpha: 1,\n            uTextureMatrix: Matrix.IDENTITY,\n            uColor: new Float32Array([1, 1, 1, 1]),\n        };\n\n        // Set defaults\n        options = Object.assign({\n            tint: 0xFFFFFF,\n            alpha: 1,\n            pluginName: 'batch',\n        }, options);\n\n        if (options.uniforms)\n        {\n            Object.assign(uniforms, options.uniforms);\n        }\n\n        super(options.program || Program.from(vertex, fragment), uniforms);\n\n        this._colorDirty = false;\n\n        this.uvMatrix = new TextureMatrix(uSampler);\n        this.batchable = options.program === undefined;\n        this.pluginName = options.pluginName;\n\n        this._tintColor = new Color(options.tint);\n        this._tintRGB = this._tintColor.toLittleEndianNumber();\n        this._colorDirty = true;\n        this.alpha = options.alpha;\n    }\n\n    /** Reference to the texture being rendered. */\n    get texture(): Texture\n    {\n        return this.uniforms.uSampler;\n    }\n    set texture(value: Texture)\n    {\n        if (this.uniforms.uSampler !== value)\n        {\n            if (!this.uniforms.uSampler.baseTexture.alphaMode !== !value.baseTexture.alphaMode)\n            {\n                this._colorDirty = true;\n            }\n\n            this.uniforms.uSampler = value;\n            this.uvMatrix.texture = value;\n        }\n    }\n\n    /**\n     * This gets automatically set by the object using this.\n     * @default 1\n     */\n    set alpha(value: number)\n    {\n        if (value === this._alpha) return;\n\n        this._alpha = value;\n        this._colorDirty = true;\n    }\n    get alpha(): number\n    {\n        return this._alpha;\n    }\n\n    /**\n     * Multiply tint for the material.\n     * @default 0xFFFFFF\n     */\n    set tint(value: ColorSource)\n    {\n        if (value === this.tint) return;\n\n        this._tintColor.setValue(value);\n        this._tintRGB = this._tintColor.toLittleEndianNumber();\n        this._colorDirty = true;\n    }\n    get tint(): ColorSource\n    {\n        return this._tintColor.value;\n    }\n\n    /**\n     * Get the internal number from tint color\n     * @ignore\n     */\n    get tintValue(): number\n    {\n        return this._tintColor.toNumber();\n    }\n\n    /** Gets called automatically by the Mesh. Intended to be overridden for custom {@link PIXI.MeshMaterial} objects. */\n    public update(): void\n    {\n        if (this._colorDirty)\n        {\n            this._colorDirty = false;\n            const baseTexture = this.texture.baseTexture;\n            const applyToChannels = (baseTexture.alphaMode as unknown as boolean);\n\n            Color.shared\n                .setValue(this._tintColor)\n                .premultiply(this._alpha, applyToChannels)\n                .toArray(this.uniforms.uColor);\n        }\n        if (this.uvMatrix.update())\n        {\n            this.uniforms.uTextureMatrix = this.uvMatrix.mapCoord;\n        }\n    }\n}\n"], "mappings": ";;;AAsBO,MAAMA,YAAA,SAAqBC,MAAA,CAClC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAwCIC,YAAYC,QAAA,EAAmBC,OAAA,EAC/B;IACI,MAAMC,QAAA,GAAW;MACbF,QAAA;MACAG,KAAA,EAAO;MACPC,cAAA,EAAgBC,MAAA,CAAOC,QAAA;MACvBC,MAAA,EAAQ,IAAIC,YAAA,CAAa,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;IAAA;IAIzCP,OAAA,GAAUQ,MAAA,CAAOC,MAAA,CAAO;MACpBC,IAAA,EAAM;MACNR,KAAA,EAAO;MACPS,UAAA,EAAY;IAAA,GACbX,OAAO,GAENA,OAAA,CAAQC,QAAA,IAERO,MAAA,CAAOC,MAAA,CAAOR,QAAA,EAAUD,OAAA,CAAQC,QAAQ,GAG5C,MAAMD,OAAA,CAAQY,OAAA,IAAWC,OAAA,CAAQC,IAAA,CAAKC,MAAA,EAAQC,QAAQ,GAAGf,QAAQ,GAEjE,KAAKgB,WAAA,GAAc,IAEnB,KAAKC,QAAA,GAAW,IAAIC,aAAA,CAAcpB,QAAQ,GAC1C,KAAKqB,SAAA,GAAYpB,OAAA,CAAQY,OAAA,KAAY,QACrC,KAAKD,UAAA,GAAaX,OAAA,CAAQW,UAAA,EAE1B,KAAKU,UAAA,GAAa,IAAIC,KAAA,CAAMtB,OAAA,CAAQU,IAAI,GACxC,KAAKa,QAAA,GAAW,KAAKF,UAAA,CAAWG,oBAAA,IAChC,KAAKP,WAAA,GAAc,IACnB,KAAKf,KAAA,GAAQF,OAAA,CAAQE,KAAA;EACzB;EAAA;EAGA,IAAIuB,QAAA,EACJ;IACI,OAAO,KAAKxB,QAAA,CAASF,QAAA;EACzB;EACA,IAAI0B,QAAQC,KAAA,EACZ;IACQ,KAAKzB,QAAA,CAASF,QAAA,KAAa2B,KAAA,KAEvB,CAAC,KAAKzB,QAAA,CAASF,QAAA,CAAS4B,WAAA,CAAYC,SAAA,IAAc,CAACF,KAAA,CAAMC,WAAA,CAAYC,SAAA,KAErE,KAAKX,WAAA,GAAc,KAGvB,KAAKhB,QAAA,CAASF,QAAA,GAAW2B,KAAA,EACzB,KAAKR,QAAA,CAASO,OAAA,GAAUC,KAAA;EAEhC;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIxB,MAAMwB,KAAA,EACV;IACQA,KAAA,KAAU,KAAKG,MAAA,KAEnB,KAAKA,MAAA,GAASH,KAAA,EACd,KAAKT,WAAA,GAAc;EACvB;EACA,IAAIf,MAAA,EACJ;IACI,OAAO,KAAK2B,MAAA;EAChB;EAAA;AAAA;AAAA;AAAA;EAMA,IAAInB,KAAKgB,KAAA,EACT;IACQA,KAAA,KAAU,KAAKhB,IAAA,KAEnB,KAAKW,UAAA,CAAWS,QAAA,CAASJ,KAAK,GAC9B,KAAKH,QAAA,GAAW,KAAKF,UAAA,CAAWG,oBAAA,CAAqB,GACrD,KAAKP,WAAA,GAAc;EACvB;EACA,IAAIP,KAAA,EACJ;IACI,OAAO,KAAKW,UAAA,CAAWK,KAAA;EAC3B;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIK,UAAA,EACJ;IACW,YAAKV,UAAA,CAAWW,QAAA;EAC3B;EAAA;EAGOC,OAAA,EACP;IACI,IAAI,KAAKhB,WAAA,EACT;MACI,KAAKA,WAAA,GAAc;MAEb,MAAAiB,eAAA,GADc,KAAKT,OAAA,CAAQE,WAAA,CACIC,SAAA;MAErCN,KAAA,CAAMa,MAAA,CACDL,QAAA,CAAS,KAAKT,UAAU,EACxBe,WAAA,CAAY,KAAKP,MAAA,EAAQK,eAAe,EACxCG,OAAA,CAAQ,KAAKpC,QAAA,CAASK,MAAM;IACrC;IACI,KAAKY,QAAA,CAASe,MAAA,CAAO,MAErB,KAAKhC,QAAA,CAASE,cAAA,GAAiB,KAAKe,QAAA,CAASoB,QAAA;EAErD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}