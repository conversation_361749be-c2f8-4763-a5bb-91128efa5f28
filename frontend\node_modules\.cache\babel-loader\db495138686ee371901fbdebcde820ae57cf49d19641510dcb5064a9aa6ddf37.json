{"ast": null, "code": "import \"./AssetExtension.mjs\";\nimport { Assets, AssetsClass } from \"./Assets.mjs\";\nimport \"./cache/index.mjs\";\nimport \"./detections/index.mjs\";\nimport \"./loader/index.mjs\";\nimport \"./resolver/index.mjs\";\nimport \"./types.mjs\";\nimport \"./utils/index.mjs\";\nimport { Cache } from \"./cache/Cache.mjs\";\nimport { cacheTextureArray } from \"./cache/parsers/cacheTextureArray.mjs\";\nimport { detectAvif } from \"./detections/parsers/detectAvif.mjs\";\nimport { detectWebp } from \"./detections/parsers/detectWebp.mjs\";\nimport { detectDefaults } from \"./detections/parsers/detectDefaults.mjs\";\nimport { detectWebm } from \"./detections/parsers/detectWebm.mjs\";\nimport { detectMp4 } from \"./detections/parsers/detectMp4.mjs\";\nimport { detectOgv } from \"./detections/parsers/detectOgv.mjs\";\nimport { LoaderParserPriority } from \"./loader/parsers/LoaderParser.mjs\";\nimport { loadJson } from \"./loader/parsers/loadJson.mjs\";\nimport { loadTxt } from \"./loader/parsers/loadTxt.mjs\";\nimport { getFontFamilyName, loadWebFont } from \"./loader/parsers/loadWebFont.mjs\";\nimport { loadSVG } from \"./loader/parsers/textures/loadSVG.mjs\";\nimport { loadImageBitmap, loadTextures } from \"./loader/parsers/textures/loadTextures.mjs\";\nimport { loadVideo } from \"./loader/parsers/textures/loadVideo.mjs\";\nimport { createTexture } from \"./loader/parsers/textures/utils/createTexture.mjs\";\nimport { resolveTextureUrl } from \"./resolver/parsers/resolveTextureUrl.mjs\";\nimport { checkDataUrl } from \"./utils/checkDataUrl.mjs\";\nimport { checkExtension } from \"./utils/checkExtension.mjs\";\nimport { convertToList } from \"./utils/convertToList.mjs\";\nimport { copySearchParams } from \"./utils/copySearchParams.mjs\";\nimport { createStringVariations } from \"./utils/createStringVariations.mjs\";\nimport { isSingleItem } from \"./utils/isSingleItem.mjs\";\nexport { Assets, AssetsClass, Cache, LoaderParserPriority, cacheTextureArray, checkDataUrl, checkExtension, convertToList, copySearchParams, createStringVariations, createTexture, detectAvif, detectDefaults, detectMp4, detectOgv, detectWebm, detectWebp, getFontFamilyName, isSingleItem, loadImageBitmap, loadJson, loadSVG, loadTextures, loadTxt, loadVideo, loadWebFont, resolveTextureUrl };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import \"./AssetExtension.mjs\";\nimport { Assets, AssetsClass } from \"./Assets.mjs\";\nimport \"./cache/index.mjs\";\nimport \"./detections/index.mjs\";\nimport \"./loader/index.mjs\";\nimport \"./resolver/index.mjs\";\nimport \"./types.mjs\";\nimport \"./utils/index.mjs\";\nimport { Cache } from \"./cache/Cache.mjs\";\nimport { cacheTextureArray } from \"./cache/parsers/cacheTextureArray.mjs\";\nimport { detectAvif } from \"./detections/parsers/detectAvif.mjs\";\nimport { detectWebp } from \"./detections/parsers/detectWebp.mjs\";\nimport { detectDefaults } from \"./detections/parsers/detectDefaults.mjs\";\nimport { detectWebm } from \"./detections/parsers/detectWebm.mjs\";\nimport { detectMp4 } from \"./detections/parsers/detectMp4.mjs\";\nimport { detectOgv } from \"./detections/parsers/detectOgv.mjs\";\nimport { LoaderParserPriority } from \"./loader/parsers/LoaderParser.mjs\";\nimport { loadJson } from \"./loader/parsers/loadJson.mjs\";\nimport { loadTxt } from \"./loader/parsers/loadTxt.mjs\";\nimport { getFontFamilyName, loadWebFont } from \"./loader/parsers/loadWebFont.mjs\";\nimport { loadSVG } from \"./loader/parsers/textures/loadSVG.mjs\";\nimport { loadImageBitmap, loadTextures } from \"./loader/parsers/textures/loadTextures.mjs\";\nimport { loadVideo } from \"./loader/parsers/textures/loadVideo.mjs\";\nimport { createTexture } from \"./loader/parsers/textures/utils/createTexture.mjs\";\nimport { resolveTextureUrl } from \"./resolver/parsers/resolveTextureUrl.mjs\";\nimport { checkDataUrl } from \"./utils/checkDataUrl.mjs\";\nimport { checkExtension } from \"./utils/checkExtension.mjs\";\nimport { convertToList } from \"./utils/convertToList.mjs\";\nimport { copySearchParams } from \"./utils/copySearchParams.mjs\";\nimport { createStringVariations } from \"./utils/createStringVariations.mjs\";\nimport { isSingleItem } from \"./utils/isSingleItem.mjs\";\nexport {\n  Assets,\n  AssetsClass,\n  Cache,\n  LoaderParserPriority,\n  cacheTextureArray,\n  checkDataUrl,\n  checkExtension,\n  convertToList,\n  copySearchParams,\n  createStringVariations,\n  createTexture,\n  detectAvif,\n  detectDefaults,\n  detectMp4,\n  detectOgv,\n  detectWebm,\n  detectWebp,\n  getFontFamilyName,\n  isSingleItem,\n  loadImageBitmap,\n  loadJson,\n  loadSVG,\n  loadTextures,\n  loadTxt,\n  loadVideo,\n  loadWebFont,\n  resolveTextureUrl\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}