{"ast": null, "code": "import { settings } from \"@pixi/settings\";\nfunction assertPath(path2) {\n  if (typeof path2 != \"string\") throw new TypeError(`Path must be a string. Received ${JSON.stringify(path2)}`);\n}\nfunction removeUrlParams(url) {\n  return url.split(\"?\")[0].split(\"#\")[0];\n}\nfunction escapeRegExp(string) {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction replaceAll(str, find, replace) {\n  return str.replace(new RegExp(escapeRegExp(find), \"g\"), replace);\n}\nfunction normalizeStringPosix(path2, allowAboveRoot) {\n  let res = \"\",\n    lastSegmentLength = 0,\n    lastSlash = -1,\n    dots = 0,\n    code = -1;\n  for (let i = 0; i <= path2.length; ++i) {\n    if (i < path2.length) code = path2.charCodeAt(i);else {\n      if (code === 47) break;\n      code = 47;\n    }\n    if (code === 47) {\n      if (!(lastSlash === i - 1 || dots === 1)) if (lastSlash !== i - 1 && dots === 2) {\n        if (res.length < 2 || lastSegmentLength !== 2 || res.charCodeAt(res.length - 1) !== 46 || res.charCodeAt(res.length - 2) !== 46) {\n          if (res.length > 2) {\n            const lastSlashIndex = res.lastIndexOf(\"/\");\n            if (lastSlashIndex !== res.length - 1) {\n              lastSlashIndex === -1 ? (res = \"\", lastSegmentLength = 0) : (res = res.slice(0, lastSlashIndex), lastSegmentLength = res.length - 1 - res.lastIndexOf(\"/\")), lastSlash = i, dots = 0;\n              continue;\n            }\n          } else if (res.length === 2 || res.length === 1) {\n            res = \"\", lastSegmentLength = 0, lastSlash = i, dots = 0;\n            continue;\n          }\n        }\n        allowAboveRoot && (res.length > 0 ? res += \"/..\" : res = \"..\", lastSegmentLength = 2);\n      } else res.length > 0 ? res += `/${path2.slice(lastSlash + 1, i)}` : res = path2.slice(lastSlash + 1, i), lastSegmentLength = i - lastSlash - 1;\n      lastSlash = i, dots = 0;\n    } else code === 46 && dots !== -1 ? ++dots : dots = -1;\n  }\n  return res;\n}\nconst path = {\n  /**\n   * Converts a path to posix format.\n   * @param path - The path to convert to posix\n   */\n  toPosix(path2) {\n    return replaceAll(path2, \"\\\\\", \"/\");\n  },\n  /**\n   * Checks if the path is a URL e.g. http://, https://\n   * @param path - The path to check\n   */\n  isUrl(path2) {\n    return /^https?:/.test(this.toPosix(path2));\n  },\n  /**\n   * Checks if the path is a data URL\n   * @param path - The path to check\n   */\n  isDataUrl(path2) {\n    return /^data:([a-z]+\\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s<>]*?)$/i.test(path2);\n  },\n  /**\n   * Checks if the path is a blob URL\n   * @param path - The path to check\n   */\n  isBlobUrl(path2) {\n    return path2.startsWith(\"blob:\");\n  },\n  /**\n   * Checks if the path has a protocol e.g. http://, https://, file:///, data:, blob:, C:/\n   * This will return true for windows file paths\n   * @param path - The path to check\n   */\n  hasProtocol(path2) {\n    return /^[^/:]+:/.test(this.toPosix(path2));\n  },\n  /**\n   * Returns the protocol of the path e.g. http://, https://, file:///, data:, blob:, C:/\n   * @param path - The path to get the protocol from\n   */\n  getProtocol(path2) {\n    assertPath(path2), path2 = this.toPosix(path2);\n    const matchFile = /^file:\\/\\/\\//.exec(path2);\n    if (matchFile) return matchFile[0];\n    const matchProtocol = /^[^/:]+:\\/{0,2}/.exec(path2);\n    return matchProtocol ? matchProtocol[0] : \"\";\n  },\n  /**\n   * Converts URL to an absolute path.\n   * When loading from a Web Worker, we must use absolute paths.\n   * If the URL is already absolute we return it as is\n   * If it's not, we convert it\n   * @param url - The URL to test\n   * @param customBaseUrl - The base URL to use\n   * @param customRootUrl - The root URL to use\n   */\n  toAbsolute(url, customBaseUrl, customRootUrl) {\n    if (assertPath(url), this.isDataUrl(url) || this.isBlobUrl(url)) return url;\n    const baseUrl = removeUrlParams(this.toPosix(customBaseUrl ?? settings.ADAPTER.getBaseUrl())),\n      rootUrl = removeUrlParams(this.toPosix(customRootUrl ?? this.rootname(baseUrl)));\n    return url = this.toPosix(url), url.startsWith(\"/\") ? path.join(rootUrl, url.slice(1)) : this.isAbsolute(url) ? url : this.join(baseUrl, url);\n  },\n  /**\n   * Normalizes the given path, resolving '..' and '.' segments\n   * @param path - The path to normalize\n   */\n  normalize(path2) {\n    if (assertPath(path2), path2.length === 0) return \".\";\n    if (this.isDataUrl(path2) || this.isBlobUrl(path2)) return path2;\n    path2 = this.toPosix(path2);\n    let protocol = \"\";\n    const isAbsolute = path2.startsWith(\"/\");\n    this.hasProtocol(path2) && (protocol = this.rootname(path2), path2 = path2.slice(protocol.length));\n    const trailingSeparator = path2.endsWith(\"/\");\n    return path2 = normalizeStringPosix(path2, !1), path2.length > 0 && trailingSeparator && (path2 += \"/\"), isAbsolute ? `/${path2}` : protocol + path2;\n  },\n  /**\n   * Determines if path is an absolute path.\n   * Absolute paths can be urls, data urls, or paths on disk\n   * @param path - The path to test\n   */\n  isAbsolute(path2) {\n    return assertPath(path2), path2 = this.toPosix(path2), this.hasProtocol(path2) ? !0 : path2.startsWith(\"/\");\n  },\n  /**\n   * Joins all given path segments together using the platform-specific separator as a delimiter,\n   * then normalizes the resulting path\n   * @param segments - The segments of the path to join\n   */\n  join(...segments) {\n    if (segments.length === 0) return \".\";\n    let joined;\n    for (let i = 0; i < segments.length; ++i) {\n      const arg = segments[i];\n      if (assertPath(arg), arg.length > 0) if (joined === void 0) joined = arg;else {\n        const prevArg = segments[i - 1] ?? \"\";\n        this.joinExtensions.includes(this.extname(prevArg).toLowerCase()) ? joined += `/../${arg}` : joined += `/${arg}`;\n      }\n    }\n    return joined === void 0 ? \".\" : this.normalize(joined);\n  },\n  /**\n   * Returns the directory name of a path\n   * @param path - The path to parse\n   */\n  dirname(path2) {\n    if (assertPath(path2), path2.length === 0) return \".\";\n    path2 = this.toPosix(path2);\n    let code = path2.charCodeAt(0);\n    const hasRoot = code === 47;\n    let end = -1,\n      matchedSlash = !0;\n    const proto = this.getProtocol(path2),\n      origpath = path2;\n    path2 = path2.slice(proto.length);\n    for (let i = path2.length - 1; i >= 1; --i) if (code = path2.charCodeAt(i), code === 47) {\n      if (!matchedSlash) {\n        end = i;\n        break;\n      }\n    } else matchedSlash = !1;\n    return end === -1 ? hasRoot ? \"/\" : this.isUrl(origpath) ? proto + path2 : proto : hasRoot && end === 1 ? \"//\" : proto + path2.slice(0, end);\n  },\n  /**\n   * Returns the root of the path e.g. /, C:/, file:///, http://domain.com/\n   * @param path - The path to parse\n   */\n  rootname(path2) {\n    assertPath(path2), path2 = this.toPosix(path2);\n    let root = \"\";\n    if (path2.startsWith(\"/\") ? root = \"/\" : root = this.getProtocol(path2), this.isUrl(path2)) {\n      const index = path2.indexOf(\"/\", root.length);\n      index !== -1 ? root = path2.slice(0, index) : root = path2, root.endsWith(\"/\") || (root += \"/\");\n    }\n    return root;\n  },\n  /**\n   * Returns the last portion of a path\n   * @param path - The path to test\n   * @param ext - Optional extension to remove\n   */\n  basename(path2, ext) {\n    assertPath(path2), ext && assertPath(ext), path2 = removeUrlParams(this.toPosix(path2));\n    let start = 0,\n      end = -1,\n      matchedSlash = !0,\n      i;\n    if (ext !== void 0 && ext.length > 0 && ext.length <= path2.length) {\n      if (ext.length === path2.length && ext === path2) return \"\";\n      let extIdx = ext.length - 1,\n        firstNonSlashEnd = -1;\n      for (i = path2.length - 1; i >= 0; --i) {\n        const code = path2.charCodeAt(i);\n        if (code === 47) {\n          if (!matchedSlash) {\n            start = i + 1;\n            break;\n          }\n        } else firstNonSlashEnd === -1 && (matchedSlash = !1, firstNonSlashEnd = i + 1), extIdx >= 0 && (code === ext.charCodeAt(extIdx) ? --extIdx === -1 && (end = i) : (extIdx = -1, end = firstNonSlashEnd));\n      }\n      return start === end ? end = firstNonSlashEnd : end === -1 && (end = path2.length), path2.slice(start, end);\n    }\n    for (i = path2.length - 1; i >= 0; --i) if (path2.charCodeAt(i) === 47) {\n      if (!matchedSlash) {\n        start = i + 1;\n        break;\n      }\n    } else end === -1 && (matchedSlash = !1, end = i + 1);\n    return end === -1 ? \"\" : path2.slice(start, end);\n  },\n  /**\n   * Returns the extension of the path, from the last occurrence of the . (period) character to end of string in the last\n   * portion of the path. If there is no . in the last portion of the path, or if there are no . characters other than\n   * the first character of the basename of path, an empty string is returned.\n   * @param path - The path to parse\n   */\n  extname(path2) {\n    assertPath(path2), path2 = removeUrlParams(this.toPosix(path2));\n    let startDot = -1,\n      startPart = 0,\n      end = -1,\n      matchedSlash = !0,\n      preDotState = 0;\n    for (let i = path2.length - 1; i >= 0; --i) {\n      const code = path2.charCodeAt(i);\n      if (code === 47) {\n        if (!matchedSlash) {\n          startPart = i + 1;\n          break;\n        }\n        continue;\n      }\n      end === -1 && (matchedSlash = !1, end = i + 1), code === 46 ? startDot === -1 ? startDot = i : preDotState !== 1 && (preDotState = 1) : startDot !== -1 && (preDotState = -1);\n    }\n    return startDot === -1 || end === -1 || preDotState === 0 || preDotState === 1 && startDot === end - 1 && startDot === startPart + 1 ? \"\" : path2.slice(startDot, end);\n  },\n  /**\n   * Parses a path into an object containing the 'root', `dir`, `base`, `ext`, and `name` properties.\n   * @param path - The path to parse\n   */\n  parse(path2) {\n    assertPath(path2);\n    const ret = {\n      root: \"\",\n      dir: \"\",\n      base: \"\",\n      ext: \"\",\n      name: \"\"\n    };\n    if (path2.length === 0) return ret;\n    path2 = removeUrlParams(this.toPosix(path2));\n    let code = path2.charCodeAt(0);\n    const isAbsolute = this.isAbsolute(path2);\n    let start;\n    const protocol = \"\";\n    ret.root = this.rootname(path2), isAbsolute || this.hasProtocol(path2) ? start = 1 : start = 0;\n    let startDot = -1,\n      startPart = 0,\n      end = -1,\n      matchedSlash = !0,\n      i = path2.length - 1,\n      preDotState = 0;\n    for (; i >= start; --i) {\n      if (code = path2.charCodeAt(i), code === 47) {\n        if (!matchedSlash) {\n          startPart = i + 1;\n          break;\n        }\n        continue;\n      }\n      end === -1 && (matchedSlash = !1, end = i + 1), code === 46 ? startDot === -1 ? startDot = i : preDotState !== 1 && (preDotState = 1) : startDot !== -1 && (preDotState = -1);\n    }\n    return startDot === -1 || end === -1 || preDotState === 0 || preDotState === 1 && startDot === end - 1 && startDot === startPart + 1 ? end !== -1 && (startPart === 0 && isAbsolute ? ret.base = ret.name = path2.slice(1, end) : ret.base = ret.name = path2.slice(startPart, end)) : (startPart === 0 && isAbsolute ? (ret.name = path2.slice(1, startDot), ret.base = path2.slice(1, end)) : (ret.name = path2.slice(startPart, startDot), ret.base = path2.slice(startPart, end)), ret.ext = path2.slice(startDot, end)), ret.dir = this.dirname(path2), protocol && (ret.dir = protocol + ret.dir), ret;\n  },\n  sep: \"/\",\n  delimiter: \":\",\n  joinExtensions: [\".html\"]\n};\nexport { path };", "map": {"version": 3, "names": ["assertPath", "path2", "TypeError", "JSON", "stringify", "removeUrlParams", "url", "split", "escapeRegExp", "string", "replace", "replaceAll", "str", "find", "RegExp", "normalizeStringPosix", "allowAboveRoot", "res", "lastSegmentLength", "lastSlash", "dots", "code", "i", "length", "charCodeAt", "lastSlashIndex", "lastIndexOf", "slice", "path", "toPosix", "isUrl", "test", "isDataUrl", "isBlobUrl", "startsWith", "hasProtocol", "getProtocol", "matchFile", "exec", "matchProtocol", "toAbsolute", "customBaseUrl", "customRootUrl", "baseUrl", "settings", "ADAPTER", "getBaseUrl", "rootUrl", "rootname", "join", "isAbsolute", "normalize", "protocol", "trailingSeparator", "endsWith", "segments", "joined", "arg", "prevArg", "joinExtensions", "includes", "extname", "toLowerCase", "dirname", "hasRoot", "end", "matchedSlash", "proto", "origpath", "root", "index", "indexOf", "basename", "ext", "start", "extIdx", "firstNonSlashEnd", "startDot", "startPart", "preDotState", "parse", "ret", "dir", "base", "name", "sep", "delimiter"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\utils\\src\\path.ts"], "sourcesContent": ["import { settings } from '@pixi/settings';\n\nfunction assertPath(path: string)\n{\n    if (typeof path !== 'string')\n    {\n        throw new TypeError(`Path must be a string. Received ${JSON.stringify(path)}`);\n    }\n}\n\nfunction removeUrlParams(url: string): string\n{\n    const re = url.split('?')[0];\n\n    return re.split('#')[0];\n}\n\nfunction escapeRegExp(string: string)\n{\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'); // $& means the whole matched string\n}\n\nfunction replaceAll(str: string, find: string, replace: string)\n{\n    return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);\n}\n\n// Resolves . and .. elements in a path with directory names\nfunction normalizeStringPosix(path: string, allowAboveRoot: boolean)\n{\n    let res = '';\n    let lastSegmentLength = 0;\n    let lastSlash = -1;\n    let dots = 0;\n    let code = -1;\n\n    for (let i = 0; i <= path.length; ++i)\n    {\n        if (i < path.length)\n        {\n            code = path.charCodeAt(i);\n        }\n        else if (code === 47)\n        {\n            break;\n        }\n        else\n        {\n            code = 47;\n        }\n        if (code === 47)\n        {\n            if (lastSlash === i - 1 || dots === 1)\n            {\n                // NOOP\n            }\n            else if (lastSlash !== i - 1 && dots === 2)\n            {\n                if (\n                    res.length < 2\n                    || lastSegmentLength !== 2\n                    || res.charCodeAt(res.length - 1) !== 46\n                    || res.charCodeAt(res.length - 2) !== 46\n                )\n                {\n                    if (res.length > 2)\n                    {\n                        const lastSlashIndex = res.lastIndexOf('/');\n\n                        if (lastSlashIndex !== res.length - 1)\n                        {\n                            if (lastSlashIndex === -1)\n                            {\n                                res = '';\n                                lastSegmentLength = 0;\n                            }\n                            else\n                            {\n                                res = res.slice(0, lastSlashIndex);\n                                lastSegmentLength = res.length - 1 - res.lastIndexOf('/');\n                            }\n                            lastSlash = i;\n                            dots = 0;\n                            continue;\n                        }\n                    }\n                    else if (res.length === 2 || res.length === 1)\n                    {\n                        res = '';\n                        lastSegmentLength = 0;\n                        lastSlash = i;\n                        dots = 0;\n                        continue;\n                    }\n                }\n                if (allowAboveRoot)\n                {\n                    if (res.length > 0)\n                    { res += '/..'; }\n                    else\n                    { res = '..'; }\n                    lastSegmentLength = 2;\n                }\n            }\n            else\n            {\n                if (res.length > 0)\n                {\n                    res += `/${path.slice(lastSlash + 1, i)}`;\n                }\n                else\n                {\n                    res = path.slice(lastSlash + 1, i);\n                }\n                lastSegmentLength = i - lastSlash - 1;\n            }\n            lastSlash = i;\n            dots = 0;\n        }\n        else if (code === 46 && dots !== -1)\n        {\n            ++dots;\n        }\n        else\n        {\n            dots = -1;\n        }\n    }\n\n    return res;\n}\n\nexport interface Path\n{\n    toPosix: (path: string) => string;\n    toAbsolute: (url: string, baseUrl?: string, rootUrl?: string) => string;\n    isUrl: (path: string) => boolean;\n    isDataUrl: (path: string) => boolean;\n    isBlobUrl: (path: string) => boolean;\n    hasProtocol: (path: string) => boolean;\n    getProtocol: (path: string) => string;\n    normalize: (path: string) => string;\n    join: (...paths: string[]) => string;\n    isAbsolute: (path: string) => boolean;\n    dirname: (path: string) => string;\n    rootname: (path: string) => string;\n    basename: (path: string, ext?: string) => string;\n    extname: (path: string) => string;\n    parse: (path: string) => { root?: string, dir?: string, base?: string, ext?: string, name?: string };\n    sep: string,\n    delimiter: string,\n    joinExtensions: string[],\n}\n\nexport const path: Path = {\n    /**\n     * Converts a path to posix format.\n     * @param path - The path to convert to posix\n     */\n    toPosix(path: string) { return replaceAll(path, '\\\\', '/'); },\n    /**\n     * Checks if the path is a URL e.g. http://, https://\n     * @param path - The path to check\n     */\n    isUrl(path: string) { return (/^https?:/).test(this.toPosix(path)); },\n    /**\n     * Checks if the path is a data URL\n     * @param path - The path to check\n     */\n    isDataUrl(path: string)\n    {\n        // eslint-disable-next-line max-len\n        return (/^data:([a-z]+\\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\\-._~:@\\/?%\\s<>]*?)$/i)\n            .test(path);\n    },\n    /**\n     * Checks if the path is a blob URL\n     * @param path - The path to check\n     */\n    isBlobUrl(path: string)\n    {\n        // Not necessary to have an exact regex to match the blob URLs\n        return path.startsWith('blob:');\n    },\n    /**\n     * Checks if the path has a protocol e.g. http://, https://, file:///, data:, blob:, C:/\n     * This will return true for windows file paths\n     * @param path - The path to check\n     */\n    hasProtocol(path: string) { return (/^[^/:]+:/).test(this.toPosix(path)); },\n    /**\n     * Returns the protocol of the path e.g. http://, https://, file:///, data:, blob:, C:/\n     * @param path - The path to get the protocol from\n     */\n    getProtocol(path: string)\n    {\n        assertPath(path);\n        path = this.toPosix(path);\n\n        const matchFile = (/^file:\\/\\/\\//).exec(path);\n\n        if (matchFile)\n        {\n            return matchFile[0];\n        }\n\n        const matchProtocol = (/^[^/:]+:\\/{0,2}/).exec(path);\n\n        if (matchProtocol)\n        {\n            return matchProtocol[0];\n        }\n\n        return '';\n    },\n\n    /**\n     * Converts URL to an absolute path.\n     * When loading from a Web Worker, we must use absolute paths.\n     * If the URL is already absolute we return it as is\n     * If it's not, we convert it\n     * @param url - The URL to test\n     * @param customBaseUrl - The base URL to use\n     * @param customRootUrl - The root URL to use\n     */\n    toAbsolute(url: string, customBaseUrl?: string, customRootUrl?: string)\n    {\n        assertPath(url);\n\n        if (this.isDataUrl(url) || this.isBlobUrl(url)) return url;\n\n        const baseUrl = removeUrlParams(this.toPosix(customBaseUrl ?? settings.ADAPTER.getBaseUrl()));\n        const rootUrl = removeUrlParams(this.toPosix(customRootUrl ?? this.rootname(baseUrl)));\n\n        url = this.toPosix(url);\n\n        // root relative url\n        if (url.startsWith('/'))\n        {\n            return path.join(rootUrl, url.slice(1));\n        }\n\n        const absolutePath = this.isAbsolute(url) ? url : this.join(baseUrl, url);\n\n        return absolutePath;\n    },\n\n    /**\n     * Normalizes the given path, resolving '..' and '.' segments\n     * @param path - The path to normalize\n     */\n    normalize(path: string)\n    {\n        assertPath(path);\n\n        if (path.length === 0) return '.';\n        if (this.isDataUrl(path) || this.isBlobUrl(path)) return path;\n\n        path = this.toPosix(path);\n\n        let protocol = '';\n        const isAbsolute = path.startsWith('/');\n\n        if (this.hasProtocol(path))\n        {\n            protocol = this.rootname(path);\n            path = path.slice(protocol.length);\n        }\n\n        const trailingSeparator = path.endsWith('/');\n\n        // Normalize the path\n        path = normalizeStringPosix(path, false);\n\n        if (path.length > 0 && trailingSeparator) path += '/';\n        if (isAbsolute) return `/${path}`;\n\n        return protocol + path;\n    },\n\n    /**\n     * Determines if path is an absolute path.\n     * Absolute paths can be urls, data urls, or paths on disk\n     * @param path - The path to test\n     */\n    isAbsolute(path: string)\n    {\n        assertPath(path);\n        path = this.toPosix(path);\n\n        if (this.hasProtocol(path)) return true;\n\n        return path.startsWith('/');\n    },\n\n    /**\n     * Joins all given path segments together using the platform-specific separator as a delimiter,\n     * then normalizes the resulting path\n     * @param segments - The segments of the path to join\n     */\n    join(...segments: string[])\n    {\n        if (segments.length === 0)\n        { return '.'; }\n        let joined;\n\n        for (let i = 0; i < segments.length; ++i)\n        {\n            const arg = segments[i];\n\n            assertPath(arg);\n            if (arg.length > 0)\n            {\n                if (joined === undefined) joined = arg;\n                else\n                {\n                    const prevArg = segments[i - 1] ?? '';\n\n                    if (this.joinExtensions.includes(this.extname(prevArg).toLowerCase()))\n                    {\n                        joined += `/../${arg}`;\n                    }\n                    else\n                    {\n                        joined += `/${arg}`;\n                    }\n                }\n            }\n        }\n        if (joined === undefined) { return '.'; }\n\n        return this.normalize(joined);\n    },\n\n    /**\n     * Returns the directory name of a path\n     * @param path - The path to parse\n     */\n    dirname(path: string)\n    {\n        assertPath(path);\n        if (path.length === 0) return '.';\n        path = this.toPosix(path);\n        let code = path.charCodeAt(0);\n        const hasRoot = code === 47;\n        let end = -1;\n        let matchedSlash = true;\n\n        const proto = this.getProtocol(path);\n        const origpath = path;\n\n        path = path.slice(proto.length);\n\n        for (let i = path.length - 1; i >= 1; --i)\n        {\n            code = path.charCodeAt(i);\n            if (code === 47)\n            {\n                if (!matchedSlash)\n                {\n                    end = i;\n                    break;\n                }\n            }\n            else\n            {\n                // We saw the first non-path separator\n                matchedSlash = false;\n            }\n        }\n\n        // if end is -1 and its a url then we need to add the path back\n        // eslint-disable-next-line no-nested-ternary\n        if (end === -1) return hasRoot ? '/' : this.isUrl(origpath) ? proto + path : proto;\n        if (hasRoot && end === 1) return '//';\n\n        return proto + path.slice(0, end);\n    },\n\n    /**\n     * Returns the root of the path e.g. /, C:/, file:///, http://domain.com/\n     * @param path - The path to parse\n     */\n    rootname(path: string)\n    {\n        assertPath(path);\n        path = this.toPosix(path);\n\n        let root = '';\n\n        if (path.startsWith('/')) root = '/';\n        else\n        {\n            root = this.getProtocol(path);\n        }\n\n        if (this.isUrl(path))\n        {\n            // need to find the first path separator\n            const index = path.indexOf('/', root.length);\n\n            if (index !== -1)\n            {\n                root = path.slice(0, index);\n            }\n            else root = path;\n\n            if (!root.endsWith('/')) root += '/';\n        }\n\n        return root;\n    },\n\n    /**\n     * Returns the last portion of a path\n     * @param path - The path to test\n     * @param ext - Optional extension to remove\n     */\n    basename(path: string, ext?: string)\n    {\n        assertPath(path);\n        if (ext) assertPath(ext);\n\n        path = removeUrlParams(this.toPosix(path));\n\n        let start = 0;\n        let end = -1;\n        let matchedSlash = true;\n        let i: number;\n\n        if (ext !== undefined && ext.length > 0 && ext.length <= path.length)\n        {\n            if (ext.length === path.length && ext === path) return '';\n            let extIdx = ext.length - 1;\n            let firstNonSlashEnd = -1;\n\n            for (i = path.length - 1; i >= 0; --i)\n            {\n                const code = path.charCodeAt(i);\n\n                if (code === 47)\n                {\n                    // If we reached a path separator that was not part of a set of path\n                    // separators at the end of the string, stop now\n                    if (!matchedSlash)\n                    {\n                        start = i + 1;\n                        break;\n                    }\n                }\n                else\n                {\n                    if (firstNonSlashEnd === -1)\n                    {\n                        // We saw the first non-path separator, remember this index in case\n                        // we need it if the extension ends up not matching\n                        matchedSlash = false;\n                        firstNonSlashEnd = i + 1;\n                    }\n                    if (extIdx >= 0)\n                    {\n                        // Try to match the explicit extension\n                        if (code === ext.charCodeAt(extIdx))\n                        {\n                            if (--extIdx === -1)\n                            {\n                                // We matched the extension, so mark this as the end of our path\n                                // component\n                                end = i;\n                            }\n                        }\n                        else\n                        {\n                            // Extension does not match, so our result is the entire path\n                            // component\n                            extIdx = -1;\n                            end = firstNonSlashEnd;\n                        }\n                    }\n                }\n            }\n\n            if (start === end) end = firstNonSlashEnd; else if (end === -1) end = path.length;\n\n            return path.slice(start, end);\n        }\n        for (i = path.length - 1; i >= 0; --i)\n        {\n            if (path.charCodeAt(i) === 47)\n            {\n                // If we reached a path separator that was not part of a set of path\n                // separators at the end of the string, stop now\n                if (!matchedSlash)\n                {\n                    start = i + 1;\n                    break;\n                }\n            }\n            else if (end === -1)\n            {\n                // We saw the first non-path separator, mark this as the end of our\n                // path component\n                matchedSlash = false;\n                end = i + 1;\n            }\n        }\n\n        if (end === -1) return '';\n\n        return path.slice(start, end);\n    },\n\n    /**\n     * Returns the extension of the path, from the last occurrence of the . (period) character to end of string in the last\n     * portion of the path. If there is no . in the last portion of the path, or if there are no . characters other than\n     * the first character of the basename of path, an empty string is returned.\n     * @param path - The path to parse\n     */\n    extname(path: string)\n    {\n        assertPath(path);\n        path = removeUrlParams(this.toPosix(path));\n\n        let startDot = -1;\n        let startPart = 0;\n        let end = -1;\n        let matchedSlash = true;\n        // Track the state of characters (if any) we see before our first dot and\n        // after any path separator we find\n        let preDotState = 0;\n\n        for (let i = path.length - 1; i >= 0; --i)\n        {\n            const code = path.charCodeAt(i);\n\n            if (code === 47)\n            {\n                // If we reached a path separator that was not part of a set of path\n                // separators at the end of the string, stop now\n                if (!matchedSlash)\n                {\n                    startPart = i + 1;\n                    break;\n                }\n                continue;\n            }\n            if (end === -1)\n            {\n                // We saw the first non-path separator, mark this as the end of our\n                // extension\n                matchedSlash = false;\n                end = i + 1;\n            }\n            if (code === 46)\n            {\n                // If this is our first dot, mark it as the start of our extension\n                if (startDot === -1) startDot = i;\n                else if (preDotState !== 1) preDotState = 1;\n            }\n            else if (startDot !== -1)\n            {\n                // We saw a non-dot and non-path separator before our dot, so we should\n                // have a good chance at having a non-empty extension\n                preDotState = -1;\n            }\n        }\n\n        if (\n            startDot === -1 || end === -1\n            // We saw a non-dot character immediately before the dot\n            || preDotState === 0\n            // The (right-most) trimmed path component is exactly '..'\n            // eslint-disable-next-line no-mixed-operators\n            || preDotState === 1 && startDot === end - 1 && startDot === startPart + 1\n        )\n        {\n            return '';\n        }\n\n        return path.slice(startDot, end);\n    },\n\n    /**\n     * Parses a path into an object containing the 'root', `dir`, `base`, `ext`, and `name` properties.\n     * @param path - The path to parse\n     */\n    parse(path: string)\n    {\n        assertPath(path);\n\n        const ret = { root: '', dir: '', base: '', ext: '', name: '' };\n\n        if (path.length === 0) return ret;\n        path = removeUrlParams(this.toPosix(path));\n\n        let code = path.charCodeAt(0);\n        const isAbsolute = this.isAbsolute(path);\n        let start: number;\n        const protocol = '';\n\n        ret.root = this.rootname(path);\n\n        if (isAbsolute || this.hasProtocol(path))\n        {\n            start = 1;\n        }\n        else\n        {\n            start = 0;\n        }\n        let startDot = -1;\n        let startPart = 0;\n        let end = -1;\n        let matchedSlash = true;\n        let i = path.length - 1;\n\n        // Track the state of characters (if any) we see before our first dot and\n        // after any path separator we find\n        let preDotState = 0;\n\n        // Get non-dir info\n        for (; i >= start; --i)\n        {\n            code = path.charCodeAt(i);\n            if (code === 47)\n            {\n                // If we reached a path separator that was not part of a set of path\n                // separators at the end of the string, stop now\n                if (!matchedSlash)\n                {\n                    startPart = i + 1;\n                    break;\n                }\n                continue;\n            }\n            if (end === -1)\n            {\n                // We saw the first non-path separator, mark this as the end of our\n                // extension\n                matchedSlash = false;\n                end = i + 1;\n            }\n            if (code === 46)\n            {\n                // If this is our first dot, mark it as the start of our extension\n                if (startDot === -1) startDot = i;\n                else if (preDotState !== 1) preDotState = 1;\n            }\n            else if (startDot !== -1)\n            {\n                // We saw a non-dot and non-path separator before our dot, so we should\n                // have a good chance at having a non-empty extension\n                preDotState = -1;\n            }\n        }\n\n        if (\n            startDot === -1 || end === -1\n            // We saw a non-dot character immediately before the dot\n            || preDotState === 0\n            // The (right-most) trimmed path component is exactly '..'\n            // eslint-disable-next-line no-mixed-operators\n            || preDotState === 1 && startDot === end - 1 && startDot === startPart + 1\n        )\n        {\n            if (end !== -1)\n            {\n                if (startPart === 0 && isAbsolute) ret.base = ret.name = path.slice(1, end);\n                else ret.base = ret.name = path.slice(startPart, end);\n            }\n        }\n        else\n        {\n            if (startPart === 0 && isAbsolute)\n            {\n                ret.name = path.slice(1, startDot);\n                ret.base = path.slice(1, end);\n            }\n            else\n            {\n                ret.name = path.slice(startPart, startDot);\n                ret.base = path.slice(startPart, end);\n            }\n            ret.ext = path.slice(startDot, end);\n        }\n\n        ret.dir = this.dirname(path);\n        if (protocol) ret.dir = protocol + ret.dir;\n\n        return ret;\n    },\n\n    sep: '/',\n    delimiter: ':',\n    joinExtensions: ['.html'],\n} as Path;\n"], "mappings": ";AAEA,SAASA,WAAWC,KAAA,EACpB;EACI,IAAI,OAAOA,KAAA,IAAS,UAEhB,MAAM,IAAIC,SAAA,CAAU,mCAAmCC,IAAA,CAAKC,SAAA,CAAUH,KAAI,CAAC,EAAE;AAErF;AAEA,SAASI,gBAAgBC,GAAA,EACzB;EACe,OAAAA,GAAA,CAAIC,KAAA,CAAM,GAAG,EAAE,CAAC,EAEjBA,KAAA,CAAM,GAAG,EAAE,CAAC;AAC1B;AAEA,SAASC,aAAaC,MAAA,EACtB;EACW,OAAAA,MAAA,CAAOC,OAAA,CAAQ,uBAAuB,MAAM;AACvD;AAEA,SAASC,WAAWC,GAAA,EAAaC,IAAA,EAAcH,OAAA,EAC/C;EACW,OAAAE,GAAA,CAAIF,OAAA,CAAQ,IAAII,MAAA,CAAON,YAAA,CAAaK,IAAI,GAAG,GAAG,GAAGH,OAAO;AACnE;AAGA,SAASK,qBAAqBd,KAAA,EAAce,cAAA,EAC5C;EACQ,IAAAC,GAAA,GAAM;IACNC,iBAAA,GAAoB;IACpBC,SAAA,GAAY;IACZC,IAAA,GAAO;IACPC,IAAA,GAAO;EAEX,SAASC,CAAA,GAAI,GAAGA,CAAA,IAAKrB,KAAA,CAAKsB,MAAA,EAAQ,EAAED,CAAA,EACpC;IACI,IAAIA,CAAA,GAAIrB,KAAA,CAAKsB,MAAA,EAEFF,IAAA,GAAApB,KAAA,CAAKuB,UAAA,CAAWF,CAAC,OAEvB;MAAA,IAAID,IAAA,KAAS,IAEd;MAIOA,IAAA;IAAA;IAEX,IAAIA,IAAA,KAAS,IACb;MACQ,MAAAF,SAAA,KAAcG,CAAA,GAAI,KAAKF,IAAA,KAAS,IAI/B,IAAID,SAAA,KAAcG,CAAA,GAAI,KAAKF,IAAA,KAAS,GACzC;QACI,IACIH,GAAA,CAAIM,MAAA,GAAS,KACVL,iBAAA,KAAsB,KACtBD,GAAA,CAAIO,UAAA,CAAWP,GAAA,CAAIM,MAAA,GAAS,CAAC,MAAM,MACnCN,GAAA,CAAIO,UAAA,CAAWP,GAAA,CAAIM,MAAA,GAAS,CAAC,MAAM;UAGlC,IAAAN,GAAA,CAAIM,MAAA,GAAS,GACjB;YACU,MAAAE,cAAA,GAAiBR,GAAA,CAAIS,WAAA,CAAY,GAAG;YAEtC,IAAAD,cAAA,KAAmBR,GAAA,CAAIM,MAAA,GAAS,GACpC;cACQE,cAAA,KAAmB,MAEnBR,GAAA,GAAM,IACNC,iBAAA,GAAoB,MAIpBD,GAAA,GAAMA,GAAA,CAAIU,KAAA,CAAM,GAAGF,cAAc,GACjCP,iBAAA,GAAoBD,GAAA,CAAIM,MAAA,GAAS,IAAIN,GAAA,CAAIS,WAAA,CAAY,GAAG,IAE5DP,SAAA,GAAYG,CAAA,EACZF,IAAA,GAAO;cACP;YACJ;UAAA,WAEKH,GAAA,CAAIM,MAAA,KAAW,KAAKN,GAAA,CAAIM,MAAA,KAAW,GAC5C;YACIN,GAAA,GAAM,IACNC,iBAAA,GAAoB,GACpBC,SAAA,GAAYG,CAAA,EACZF,IAAA,GAAO;YACP;UACJ;QAAA;QAEAJ,cAAA,KAEIC,GAAA,CAAIM,MAAA,GAAS,IACfN,GAAA,IAAO,QAEPA,GAAA,GAAM,MACRC,iBAAA,GAAoB;MAE5B,OAGQD,GAAA,CAAIM,MAAA,GAAS,IAEbN,GAAA,IAAO,IAAIhB,KAAA,CAAK0B,KAAA,CAAMR,SAAA,GAAY,GAAGG,CAAC,CAAC,KAIvCL,GAAA,GAAMhB,KAAA,CAAK0B,KAAA,CAAMR,SAAA,GAAY,GAAGG,CAAC,GAErCJ,iBAAA,GAAoBI,CAAA,GAAIH,SAAA,GAAY;MAExCA,SAAA,GAAYG,CAAA,EACZF,IAAA,GAAO;IACX,OACSC,IAAA,KAAS,MAAMD,IAAA,KAAS,KAE7B,EAAEA,IAAA,GAIFA,IAAA,GAAO;EAEf;EAEO,OAAAH,GAAA;AACX;AAwBO,MAAMW,IAAA,GAAa;EAAA;AAAA;AAAA;AAAA;EAKtBC,QAAQ5B,KAAA,EAAc;IAAS,OAAAU,UAAA,CAAWV,KAAA,EAAM,MAAM,GAAG;EAAG;EAAA;AAAA;AAAA;AAAA;EAK5D6B,MAAM7B,KAAA,EAAc;IAAE,OAAQ,WAAY8B,IAAA,CAAK,KAAKF,OAAA,CAAQ5B,KAAI,CAAC;EAAG;EAAA;AAAA;AAAA;AAAA;EAKpE+B,UAAU/B,KAAA,EACV;IAEY,gJACH8B,IAAA,CAAK9B,KAAI;EAClB;EAAA;AAAA;AAAA;AAAA;EAKAgC,UAAUhC,KAAA,EACV;IAEW,OAAAA,KAAA,CAAKiC,UAAA,CAAW,OAAO;EAClC;EAAA;AAAA;AAAA;AAAA;AAAA;EAMAC,YAAYlC,KAAA,EAAc;IAAE,OAAQ,WAAY8B,IAAA,CAAK,KAAKF,OAAA,CAAQ5B,KAAI,CAAC;EAAG;EAAA;AAAA;AAAA;AAAA;EAK1EmC,YAAYnC,KAAA,EACZ;IACID,UAAA,CAAWC,KAAI,GACfA,KAAA,GAAO,KAAK4B,OAAA,CAAQ5B,KAAI;IAElB,MAAAoC,SAAA,GAAa,eAAgBC,IAAA,CAAKrC,KAAI;IAExC,IAAAoC,SAAA,EAEA,OAAOA,SAAA,CAAU,CAAC;IAGhB,MAAAE,aAAA,GAAiB,kBAAmBD,IAAA,CAAKrC,KAAI;IAE/C,OAAAsC,aAAA,GAEOA,aAAA,CAAc,CAAC,IAGnB;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWAC,WAAWlC,GAAA,EAAamC,aAAA,EAAwBC,aAAA,EAChD;IACI,IAAA1C,UAAA,CAAWM,GAAG,GAEV,KAAK0B,SAAA,CAAU1B,GAAG,KAAK,KAAK2B,SAAA,CAAU3B,GAAG,GAAU,OAAAA,GAAA;IAEjD,MAAAqC,OAAA,GAAUtC,eAAA,CAAgB,KAAKwB,OAAA,CAAQY,aAAA,IAAiBG,QAAA,CAASC,OAAA,CAAQC,UAAA,EAAY,CAAC;MACtFC,OAAA,GAAU1C,eAAA,CAAgB,KAAKwB,OAAA,CAAQa,aAAA,IAAiB,KAAKM,QAAA,CAASL,OAAO,CAAC,CAAC;IAErF,OAAArC,GAAA,GAAM,KAAKuB,OAAA,CAAQvB,GAAG,GAGlBA,GAAA,CAAI4B,UAAA,CAAW,GAAG,IAEXN,IAAA,CAAKqB,IAAA,CAAKF,OAAA,EAASzC,GAAA,CAAIqB,KAAA,CAAM,CAAC,CAAC,IAGrB,KAAKuB,UAAA,CAAW5C,GAAG,IAAIA,GAAA,GAAM,KAAK2C,IAAA,CAAKN,OAAA,EAASrC,GAAG;EAG5E;EAAA;AAAA;AAAA;AAAA;EAMA6C,UAAUlD,KAAA,EACV;IAGI,IAFAD,UAAA,CAAWC,KAAI,GAEXA,KAAA,CAAKsB,MAAA,KAAW,GAAU;IAC9B,IAAI,KAAKS,SAAA,CAAU/B,KAAI,KAAK,KAAKgC,SAAA,CAAUhC,KAAI,GAAU,OAAAA,KAAA;IAEzDA,KAAA,GAAO,KAAK4B,OAAA,CAAQ5B,KAAI;IAExB,IAAImD,QAAA,GAAW;IACT,MAAAF,UAAA,GAAajD,KAAA,CAAKiC,UAAA,CAAW,GAAG;IAElC,KAAKC,WAAA,CAAYlC,KAAI,MAErBmD,QAAA,GAAW,KAAKJ,QAAA,CAAS/C,KAAI,GAC7BA,KAAA,GAAOA,KAAA,CAAK0B,KAAA,CAAMyB,QAAA,CAAS7B,MAAM;IAG/B,MAAA8B,iBAAA,GAAoBpD,KAAA,CAAKqD,QAAA,CAAS,GAAG;IAM3C,OAHArD,KAAA,GAAOc,oBAAA,CAAqBd,KAAA,EAAM,EAAK,GAEnCA,KAAA,CAAKsB,MAAA,GAAS,KAAK8B,iBAAA,KAAmBpD,KAAA,IAAQ,MAC9CiD,UAAA,GAAmB,IAAIjD,KAAI,KAExBmD,QAAA,GAAWnD,KAAA;EACtB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAiD,WAAWjD,KAAA,EACX;IAII,OAHAD,UAAA,CAAWC,KAAI,GACfA,KAAA,GAAO,KAAK4B,OAAA,CAAQ5B,KAAI,GAEpB,KAAKkC,WAAA,CAAYlC,KAAI,IAAU,KAE5BA,KAAA,CAAKiC,UAAA,CAAW,GAAG;EAC9B;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAe,KAAA,GAAQM,QAAA,EACR;IACI,IAAIA,QAAA,CAAShC,MAAA,KAAW,GACf;IACL,IAAAiC,MAAA;IAEJ,SAASlC,CAAA,GAAI,GAAGA,CAAA,GAAIiC,QAAA,CAAShC,MAAA,EAAQ,EAAED,CAAA,EACvC;MACU,MAAAmC,GAAA,GAAMF,QAAA,CAASjC,CAAC;MAGtB,IADAtB,UAAA,CAAWyD,GAAG,GACVA,GAAA,CAAIlC,MAAA,GAAS,GAEb,IAAIiC,MAAA,KAAW,QAAoBA,MAAA,GAAAC,GAAA,MAEnC;QACI,MAAMC,OAAA,GAAUH,QAAA,CAASjC,CAAA,GAAI,CAAC,KAAK;QAE/B,KAAKqC,cAAA,CAAeC,QAAA,CAAS,KAAKC,OAAA,CAAQH,OAAO,EAAEI,WAAA,EAAa,IAEhEN,MAAA,IAAU,OAAOC,GAAG,KAIpBD,MAAA,IAAU,IAAIC,GAAG;MAEzB;IAER;IACA,OAAID,MAAA,KAAW,SAAoB,MAE5B,KAAKL,SAAA,CAAUK,MAAM;EAChC;EAAA;AAAA;AAAA;AAAA;EAMAO,QAAQ9D,KAAA,EACR;IAEI,IADAD,UAAA,CAAWC,KAAI,GACXA,KAAA,CAAKsB,MAAA,KAAW,GAAU;IAC9BtB,KAAA,GAAO,KAAK4B,OAAA,CAAQ5B,KAAI;IACpB,IAAAoB,IAAA,GAAOpB,KAAA,CAAKuB,UAAA,CAAW,CAAC;IAC5B,MAAMwC,OAAA,GAAU3C,IAAA,KAAS;IACrB,IAAA4C,GAAA,GAAM;MACNC,YAAA,GAAe;IAEnB,MAAMC,KAAA,GAAQ,KAAK/B,WAAA,CAAYnC,KAAI;MAC7BmE,QAAA,GAAWnE,KAAA;IAEjBA,KAAA,GAAOA,KAAA,CAAK0B,KAAA,CAAMwC,KAAA,CAAM5C,MAAM;IAE9B,SAASD,CAAA,GAAIrB,KAAA,CAAKsB,MAAA,GAAS,GAAGD,CAAA,IAAK,GAAG,EAAEA,CAAA,EAGpC,IADAD,IAAA,GAAOpB,KAAA,CAAKuB,UAAA,CAAWF,CAAC,GACpBD,IAAA,KAAS;MAET,IAAI,CAAC6C,YAAA,EACL;QACUD,GAAA,GAAA3C,CAAA;QACN;MACJ;IAAA,OAKe4C,YAAA;IAMvB,OAAID,GAAA,KAAQ,KAAWD,OAAA,GAAU,MAAM,KAAKlC,KAAA,CAAMsC,QAAQ,IAAID,KAAA,GAAQlE,KAAA,GAAOkE,KAAA,GACzEH,OAAA,IAAWC,GAAA,KAAQ,IAAU,OAE1BE,KAAA,GAAQlE,KAAA,CAAK0B,KAAA,CAAM,GAAGsC,GAAG;EACpC;EAAA;AAAA;AAAA;AAAA;EAMAjB,SAAS/C,KAAA,EACT;IACID,UAAA,CAAWC,KAAI,GACfA,KAAA,GAAO,KAAK4B,OAAA,CAAQ5B,KAAI;IAExB,IAAIoE,IAAA,GAAO;IAQX,IANIpE,KAAA,CAAKiC,UAAA,CAAW,GAAG,IAAGmC,IAAA,GAAO,MAG7BA,IAAA,GAAO,KAAKjC,WAAA,CAAYnC,KAAI,GAG5B,KAAK6B,KAAA,CAAM7B,KAAI,GACnB;MAEI,MAAMqE,KAAA,GAAQrE,KAAA,CAAKsE,OAAA,CAAQ,KAAKF,IAAA,CAAK9C,MAAM;MAEvC+C,KAAA,KAAU,KAEVD,IAAA,GAAOpE,KAAA,CAAK0B,KAAA,CAAM,GAAG2C,KAAK,IAEzBD,IAAA,GAAOpE,KAAA,EAEPoE,IAAA,CAAKf,QAAA,CAAS,GAAG,MAAGe,IAAA,IAAQ;IACrC;IAEO,OAAAA,IAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAG,SAASvE,KAAA,EAAcwE,GAAA,EACvB;IACezE,UAAA,CAAAC,KAAI,GACXwE,GAAA,IAAKzE,UAAA,CAAWyE,GAAG,GAEvBxE,KAAA,GAAOI,eAAA,CAAgB,KAAKwB,OAAA,CAAQ5B,KAAI,CAAC;IAEzC,IAAIyE,KAAA,GAAQ;MACRT,GAAA,GAAM;MACNC,YAAA,GAAe;MACf5C,CAAA;IAEA,IAAAmD,GAAA,KAAQ,UAAaA,GAAA,CAAIlD,MAAA,GAAS,KAAKkD,GAAA,CAAIlD,MAAA,IAAUtB,KAAA,CAAKsB,MAAA,EAC9D;MACI,IAAIkD,GAAA,CAAIlD,MAAA,KAAWtB,KAAA,CAAKsB,MAAA,IAAUkD,GAAA,KAAQxE,KAAA,EAAa;MACvD,IAAI0E,MAAA,GAASF,GAAA,CAAIlD,MAAA,GAAS;QACtBqD,gBAAA,GAAmB;MAEvB,KAAKtD,CAAA,GAAIrB,KAAA,CAAKsB,MAAA,GAAS,GAAGD,CAAA,IAAK,GAAG,EAAEA,CAAA,EACpC;QACU,MAAAD,IAAA,GAAOpB,KAAA,CAAKuB,UAAA,CAAWF,CAAC;QAE9B,IAAID,IAAA,KAAS;UAIT,IAAI,CAAC6C,YAAA,EACL;YACIQ,KAAA,GAAQpD,CAAA,GAAI;YACZ;UACJ;QAAA,OAIIsD,gBAAA,KAAqB,OAIrBV,YAAA,GAAe,IACfU,gBAAA,GAAmBtD,CAAA,GAAI,IAEvBqD,MAAA,IAAU,MAGNtD,IAAA,KAASoD,GAAA,CAAIjD,UAAA,CAAWmD,MAAM,IAE1B,EAAEA,MAAA,KAAW,OAIbV,GAAA,GAAM3C,CAAA,KAOVqD,MAAA,GAAS,IACTV,GAAA,GAAMW,gBAAA;MAItB;MAEA,OAAIF,KAAA,KAAUT,GAAA,GAAKA,GAAA,GAAMW,gBAAA,GAA2BX,GAAA,KAAQ,OAAIA,GAAA,GAAMhE,KAAA,CAAKsB,MAAA,GAEpEtB,KAAA,CAAK0B,KAAA,CAAM+C,KAAA,EAAOT,GAAG;IAChC;IACA,KAAK3C,CAAA,GAAIrB,KAAA,CAAKsB,MAAA,GAAS,GAAGD,CAAA,IAAK,GAAG,EAAEA,CAAA,EAE5B,IAAArB,KAAA,CAAKuB,UAAA,CAAWF,CAAC,MAAM;MAIvB,IAAI,CAAC4C,YAAA,EACL;QACIQ,KAAA,GAAQpD,CAAA,GAAI;QACZ;MACJ;IAAA,OAEK2C,GAAA,KAAQ,OAIbC,YAAA,GAAe,IACfD,GAAA,GAAM3C,CAAA,GAAI;IAIlB,OAAI2C,GAAA,KAAQ,KAAW,KAEhBhE,KAAA,CAAK0B,KAAA,CAAM+C,KAAA,EAAOT,GAAG;EAChC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAJ,QAAQ5D,KAAA,EACR;IACID,UAAA,CAAWC,KAAI,GACfA,KAAA,GAAOI,eAAA,CAAgB,KAAKwB,OAAA,CAAQ5B,KAAI,CAAC;IAErC,IAAA4E,QAAA,GAAW;MACXC,SAAA,GAAY;MACZb,GAAA,GAAM;MACNC,YAAA,GAAe;MAGfa,WAAA,GAAc;IAElB,SAASzD,CAAA,GAAIrB,KAAA,CAAKsB,MAAA,GAAS,GAAGD,CAAA,IAAK,GAAG,EAAEA,CAAA,EACxC;MACU,MAAAD,IAAA,GAAOpB,KAAA,CAAKuB,UAAA,CAAWF,CAAC;MAE9B,IAAID,IAAA,KAAS,IACb;QAGI,IAAI,CAAC6C,YAAA,EACL;UACIY,SAAA,GAAYxD,CAAA,GAAI;UAChB;QACJ;QACA;MACJ;MACI2C,GAAA,KAAQ,OAIRC,YAAA,GAAe,IACfD,GAAA,GAAM3C,CAAA,GAAI,IAEVD,IAAA,KAAS,KAGLwD,QAAA,KAAa,KAAIA,QAAA,GAAWvD,CAAA,GACvByD,WAAA,KAAgB,MAAGA,WAAA,GAAc,KAErCF,QAAA,KAAa,OAIlBE,WAAA,GAAc;IAEtB;IAEA,OACIF,QAAA,KAAa,MAAMZ,GAAA,KAAQ,MAExBc,WAAA,KAAgB,KAGhBA,WAAA,KAAgB,KAAKF,QAAA,KAAaZ,GAAA,GAAM,KAAKY,QAAA,KAAaC,SAAA,GAAY,IAGlE,KAGJ7E,KAAA,CAAK0B,KAAA,CAAMkD,QAAA,EAAUZ,GAAG;EACnC;EAAA;AAAA;AAAA;AAAA;EAMAe,MAAM/E,KAAA,EACN;IACID,UAAA,CAAWC,KAAI;IAET,MAAAgF,GAAA,GAAM;MAAEZ,IAAA,EAAM;MAAIa,GAAA,EAAK;MAAIC,IAAA,EAAM;MAAIV,GAAA,EAAK;MAAIW,IAAA,EAAM;IAAG;IAE7D,IAAInF,KAAA,CAAKsB,MAAA,KAAW,GAAU,OAAA0D,GAAA;IAC9BhF,KAAA,GAAOI,eAAA,CAAgB,KAAKwB,OAAA,CAAQ5B,KAAI,CAAC;IAErC,IAAAoB,IAAA,GAAOpB,KAAA,CAAKuB,UAAA,CAAW,CAAC;IACtB,MAAA0B,UAAA,GAAa,KAAKA,UAAA,CAAWjD,KAAI;IACnC,IAAAyE,KAAA;IACJ,MAAMtB,QAAA,GAAW;IAEjB6B,GAAA,CAAIZ,IAAA,GAAO,KAAKrB,QAAA,CAAS/C,KAAI,GAEzBiD,UAAA,IAAc,KAAKf,WAAA,CAAYlC,KAAI,IAEnCyE,KAAA,GAAQ,IAIRA,KAAA,GAAQ;IAEZ,IAAIG,QAAA,GAAW;MACXC,SAAA,GAAY;MACZb,GAAA,GAAM;MACNC,YAAA,GAAe;MACf5C,CAAA,GAAIrB,KAAA,CAAKsB,MAAA,GAAS;MAIlBwD,WAAA,GAAc;IAGX,OAAAzD,CAAA,IAAKoD,KAAA,EAAO,EAAEpD,CAAA,EACrB;MAEI,IADAD,IAAA,GAAOpB,KAAA,CAAKuB,UAAA,CAAWF,CAAC,GACpBD,IAAA,KAAS,IACb;QAGI,IAAI,CAAC6C,YAAA,EACL;UACIY,SAAA,GAAYxD,CAAA,GAAI;UAChB;QACJ;QACA;MACJ;MACI2C,GAAA,KAAQ,OAIRC,YAAA,GAAe,IACfD,GAAA,GAAM3C,CAAA,GAAI,IAEVD,IAAA,KAAS,KAGLwD,QAAA,KAAa,KAAIA,QAAA,GAAWvD,CAAA,GACvByD,WAAA,KAAgB,MAAGA,WAAA,GAAc,KAErCF,QAAA,KAAa,OAIlBE,WAAA,GAAc;IAEtB;IAEA,OACIF,QAAA,KAAa,MAAMZ,GAAA,KAAQ,MAExBc,WAAA,KAAgB,KAGhBA,WAAA,KAAgB,KAAKF,QAAA,KAAaZ,GAAA,GAAM,KAAKY,QAAA,KAAaC,SAAA,GAAY,IAGrEb,GAAA,KAAQ,OAEJa,SAAA,KAAc,KAAK5B,UAAA,GAAY+B,GAAA,CAAIE,IAAA,GAAOF,GAAA,CAAIG,IAAA,GAAOnF,KAAA,CAAK0B,KAAA,CAAM,GAAGsC,GAAG,IACrEgB,GAAA,CAAIE,IAAA,GAAOF,GAAA,CAAIG,IAAA,GAAOnF,KAAA,CAAK0B,KAAA,CAAMmD,SAAA,EAAWb,GAAG,MAKpDa,SAAA,KAAc,KAAK5B,UAAA,IAEnB+B,GAAA,CAAIG,IAAA,GAAOnF,KAAA,CAAK0B,KAAA,CAAM,GAAGkD,QAAQ,GACjCI,GAAA,CAAIE,IAAA,GAAOlF,KAAA,CAAK0B,KAAA,CAAM,GAAGsC,GAAG,MAI5BgB,GAAA,CAAIG,IAAA,GAAOnF,KAAA,CAAK0B,KAAA,CAAMmD,SAAA,EAAWD,QAAQ,GACzCI,GAAA,CAAIE,IAAA,GAAOlF,KAAA,CAAK0B,KAAA,CAAMmD,SAAA,EAAWb,GAAG,IAExCgB,GAAA,CAAIR,GAAA,GAAMxE,KAAA,CAAK0B,KAAA,CAAMkD,QAAA,EAAUZ,GAAG,IAGtCgB,GAAA,CAAIC,GAAA,GAAM,KAAKnB,OAAA,CAAQ9D,KAAI,GACvBmD,QAAA,KAAU6B,GAAA,CAAIC,GAAA,GAAM9B,QAAA,GAAW6B,GAAA,CAAIC,GAAA,GAEhCD,GAAA;EACX;EAEAI,GAAA,EAAK;EACLC,SAAA,EAAW;EACX3B,cAAA,EAAgB,CAAC,OAAO;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}