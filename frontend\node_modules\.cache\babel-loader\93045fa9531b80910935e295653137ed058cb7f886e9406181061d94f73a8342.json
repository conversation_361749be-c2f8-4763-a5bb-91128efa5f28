{"ast": null, "code": "const PI_2 = Math.PI * 2,\n  RAD_TO_DEG = 180 / Math.PI,\n  DEG_TO_RAD = Math.PI / 180;\nvar SHAPES = /* @__PURE__ */(SHAPES2 => (SHAPES2[SHAPES2.POLY = 0] = \"POLY\", SHAPES2[SHAPES2.RECT = 1] = \"RECT\", SHAPES2[SHAPES2.CIRC = 2] = \"CIRC\", SHAPES2[SHAPES2.ELIP = 3] = \"ELIP\", SHAPES2[SHAPES2.RREC = 4] = \"RREC\", SHAPES2))(SHAPES || {});\nexport { DEG_TO_RAD, PI_2, RAD_TO_DEG, SHAPES };", "map": {"version": 3, "names": ["PI_2", "Math", "PI", "RAD_TO_DEG", "DEG_TO_RAD", "SHAPES", "SHAPES2", "POLY", "RECT", "CIRC", "ELIP", "RREC"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\math\\src\\const.ts"], "sourcesContent": ["/**\n * Two Pi.\n * @static\n * @member {number}\n * @memberof PIXI\n */\nexport const PI_2 = Math.PI * 2;\n\n/**\n * Conversion factor for converting radians to degrees.\n * @static\n * @member {number} RAD_TO_DEG\n * @memberof PIXI\n */\nexport const RAD_TO_DEG = 180 / Math.PI;\n\n/**\n * Conversion factor for converting degrees to radians.\n * @static\n * @member {number}\n * @memberof PIXI\n */\nexport const DEG_TO_RAD = Math.PI / 180;\n\n/**\n * Constants that identify shapes, mainly to prevent `instanceof` calls.\n * @static\n * @memberof PIXI\n * @enum {number}\n */\nexport enum SHAPES\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    /**\n     * @property {number} RECT Rectangle\n     * @default 0\n     */\n    POLY = 0,\n    /**\n     * @property {number} POLY Polygon\n     * @default 1\n     */\n    RECT = 1,\n    /**\n     * @property {number} CIRC Circle\n     * @default 2\n     */\n    CIRC = 2,\n    /**\n     * @property {number} ELIP Ellipse\n     * @default 3\n     */\n    ELIP = 3,\n    /**\n     * @property {number} RREC Rounded Rectangle\n     * @default 4\n     */\n    RREC = 4,\n}\n"], "mappings": "AAMa,MAAAA,IAAA,GAAOC,IAAA,CAAKC,EAAA,GAAK;EAQjBC,UAAA,GAAa,MAAMF,IAAA,CAAKC,EAAA;EAQxBE,UAAA,GAAaH,IAAA,CAAKC,EAAA,GAAK;AAQxB,IAAAG,MAAA,mBAAAC,OAAA,KAORA,OAAA,CAAAA,OAAA,CAAAC,IAAA,GAAO,CAAP,YAKAD,OAAA,CAAAA,OAAA,CAAAE,IAAA,GAAO,CAAP,YAKAF,OAAA,CAAAA,OAAA,CAAAG,IAAA,GAAO,KAAP,QAKAH,OAAA,CAAAA,OAAA,CAAAI,IAAA,GAAO,KAAP,QAKAJ,OAAA,CAAAA,OAAA,CAAAK,IAAA,GAAO,CAAP,YA3BQL,OAAA,GAAAD,MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}