{"ast": null, "code": "import { LoaderParserPriority, checkExtension, createTexture } from \"@pixi/assets\";\nimport { ExtensionType, settings, MIPMAP_MODES, ALPHA_MODES, utils, BaseTexture, extensions } from \"@pixi/core\";\nimport \"../parsers/index.mjs\";\nimport { parseKTX } from \"../parsers/parseKTX.mjs\";\nconst loadKTX = {\n  extension: {\n    type: ExtensionType.LoadParser,\n    priority: LoaderParserPriority.High\n  },\n  name: \"loadKTX\",\n  test(url) {\n    return checkExtension(url, \".ktx\");\n  },\n  async load(url, asset, loader) {\n    const arrayBuffer = await (await settings.ADAPTER.fetch(url)).arrayBuffer(),\n      {\n        compressed,\n        uncompressed,\n        kvData\n      } = parseKTX(url, arrayBuffer),\n      resources = compressed ?? uncompressed,\n      options = {\n        mipmap: MIPMAP_MODES.OFF,\n        alphaMode: ALPHA_MODES.NO_PREMULTIPLIED_ALPHA,\n        resolution: utils.getResolutionOfUrl(url),\n        ...asset.data\n      },\n      textures = resources.map(resource => {\n        resources === uncompressed && Object.assign(options, {\n          type: resource.type,\n          format: resource.format\n        });\n        const res = resource.resource ?? resource,\n          base = new BaseTexture(res, options);\n        return base.ktxKeyValueData = kvData, createTexture(base, loader, url);\n      });\n    return textures.length === 1 ? textures[0] : textures;\n  },\n  unload(texture) {\n    Array.isArray(texture) ? texture.forEach(t => t.destroy(!0)) : texture.destroy(!0);\n  }\n};\nextensions.add(loadKTX);\nexport { loadKTX };", "map": {"version": 3, "names": ["loadKTX", "extension", "type", "ExtensionType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "priority", "LoaderParserPriority", "High", "name", "test", "url", "checkExtension", "load", "asset", "loader", "arrayBuffer", "settings", "ADAPTER", "fetch", "compressed", "uncompressed", "kvData", "parseKTX", "resources", "options", "mipmap", "MIPMAP_MODES", "OFF", "alphaMode", "ALPHA_MODES", "NO_PREMULTIPLIED_ALPHA", "resolution", "utils", "getResolutionOfUrl", "data", "textures", "map", "resource", "Object", "assign", "format", "res", "base", "BaseTexture", "ktxKeyValueData", "createTexture", "length", "unload", "texture", "Array", "isArray", "for<PERSON>ach", "t", "destroy", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\compressed-textures\\src\\loaders\\loadKTX.ts"], "sourcesContent": ["import { checkExtension, createTexture, LoaderParserPriority } from '@pixi/assets';\nimport { ALPHA_MODES, BaseTexture, extensions, ExtensionType, MIPMAP_MODES, settings, utils } from '@pixi/core';\nimport { parseKTX } from '../parsers';\n\nimport type { Loader, LoaderParser, ResolvedAsset } from '@pixi/assets';\nimport type { IBaseTextureOptions, Texture } from '@pixi/core';\n\n/** Loads KTX textures! */\nexport const loadKTX = {\n    extension: {\n        type: ExtensionType.LoadParser,\n        priority: LoaderParserPriority.High,\n    },\n\n    name: 'loadKTX',\n\n    test(url: string): boolean\n    {\n        return checkExtension(url, '.ktx');\n    },\n\n    async load(url: string, asset: ResolvedAsset, loader: Loader): Promise<Texture | Texture[]>\n    {\n        // get an array buffer...\n        const response = await settings.ADAPTER.fetch(url);\n\n        const arrayBuffer = await response.arrayBuffer();\n\n        const { compressed, uncompressed, kvData } = parseKTX(url, arrayBuffer);\n\n        const resources = compressed ?? uncompressed;\n\n        const options = {\n            mipmap: MIPMAP_MODES.OFF,\n            alphaMode: ALPHA_MODES.NO_PREMULTIPLIED_ALPHA,\n            resolution: utils.getResolutionOfUrl(url),\n            ...asset.data,\n        };\n\n        const textures = resources.map((resource) =>\n        {\n            if (resources === uncompressed)\n            {\n                Object.assign(options, {\n                    type: (resource as typeof uncompressed[0]).type,\n                    format: (resource as typeof uncompressed[0]).format,\n                });\n            }\n\n            const res = (resource as typeof uncompressed[0]).resource ?? (resource as typeof compressed[0]);\n\n            const base = new BaseTexture(res, options);\n\n            base.ktxKeyValueData = kvData;\n\n            return createTexture(base, loader, url);\n        });\n\n        return textures.length === 1 ? textures[0] : textures;\n    },\n\n    unload(texture: Texture | Texture[]): void\n    {\n        if (Array.isArray(texture))\n        {\n            texture.forEach((t) => t.destroy(true));\n        }\n        else\n        {\n            texture.destroy(true);\n        }\n    }\n\n} as LoaderParser<Texture | Texture[], IBaseTextureOptions>;\n\nextensions.add(loadKTX);\n"], "mappings": ";;;;AAQO,MAAMA,OAAA,GAAU;EACnBC,SAAA,EAAW;IACPC,IAAA,EAAMC,aAAA,CAAcC,UAAA;IACpBC,QAAA,EAAUC,oBAAA,CAAqBC;EACnC;EAEAC,IAAA,EAAM;EAENC,KAAKC,GAAA,EACL;IACW,OAAAC,cAAA,CAAeD,GAAA,EAAK,MAAM;EACrC;EAEA,MAAME,KAAKF,GAAA,EAAaG,KAAA,EAAsBC,MAAA,EAC9C;IAIU,MAAAC,WAAA,GAAc,OAFH,MAAMC,QAAA,CAASC,OAAA,CAAQC,KAAA,CAAMR,GAAG,GAEdK,WAAA;MAE7B;QAAEI,UAAA;QAAYC,YAAA;QAAcC;MAAA,IAAWC,QAAA,CAASZ,GAAA,EAAKK,WAAW;MAEhEQ,SAAA,GAAYJ,UAAA,IAAcC,YAAA;MAE1BI,OAAA,GAAU;QACZC,MAAA,EAAQC,YAAA,CAAaC,GAAA;QACrBC,SAAA,EAAWC,WAAA,CAAYC,sBAAA;QACvBC,UAAA,EAAYC,KAAA,CAAMC,kBAAA,CAAmBvB,GAAG;QACxC,GAAGG,KAAA,CAAMqB;MAGP;MAAAC,QAAA,GAAWZ,SAAA,CAAUa,GAAA,CAAKC,QAAA,IAChC;QACQd,SAAA,KAAcH,YAAA,IAEdkB,MAAA,CAAOC,MAAA,CAAOf,OAAA,EAAS;UACnBtB,IAAA,EAAOmC,QAAA,CAAoCnC,IAAA;UAC3CsC,MAAA,EAASH,QAAA,CAAoCG;QAAA,CAChD;QAGC,MAAAC,GAAA,GAAOJ,QAAA,CAAoCA,QAAA,IAAaA,QAAA;UAExDK,IAAA,GAAO,IAAIC,WAAA,CAAYF,GAAA,EAAKjB,OAAO;QAEzC,OAAAkB,IAAA,CAAKE,eAAA,GAAkBvB,MAAA,EAEhBwB,aAAA,CAAcH,IAAA,EAAM5B,MAAA,EAAQJ,GAAG;MAAA,CACzC;IAED,OAAOyB,QAAA,CAASW,MAAA,KAAW,IAAIX,QAAA,CAAS,CAAC,IAAIA,QAAA;EACjD;EAEAY,OAAOC,OAAA,EACP;IACQC,KAAA,CAAMC,OAAA,CAAQF,OAAO,IAErBA,OAAA,CAAQG,OAAA,CAASC,CAAA,IAAMA,CAAA,CAAEC,OAAA,CAAQ,EAAI,CAAC,IAItCL,OAAA,CAAQK,OAAA,CAAQ,EAAI;EAE5B;AAEJ;AAEAC,UAAA,CAAWC,GAAA,CAAIvD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}