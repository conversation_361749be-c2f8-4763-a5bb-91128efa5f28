{"ast": null, "code": "import { groupD8 } from \"@pixi/math\";\nclass TextureUvs {\n  constructor() {\n    this.x0 = 0, this.y0 = 0, this.x1 = 1, this.y1 = 0, this.x2 = 1, this.y2 = 1, this.x3 = 0, this.y3 = 1, this.uvsFloat32 = new Float32Array(8);\n  }\n  /**\n   * Sets the texture Uvs based on the given frame information.\n   * @protected\n   * @param frame - The frame of the texture\n   * @param baseFrame - The base frame of the texture\n   * @param rotate - Rotation of frame, see {@link PIXI.groupD8}\n   */\n  set(frame, baseFrame, rotate) {\n    const tw = baseFrame.width,\n      th = baseFrame.height;\n    if (rotate) {\n      const w2 = frame.width / 2 / tw,\n        h2 = frame.height / 2 / th,\n        cX = frame.x / tw + w2,\n        cY = frame.y / th + h2;\n      rotate = groupD8.add(rotate, groupD8.NW), this.x0 = cX + w2 * groupD8.uX(rotate), this.y0 = cY + h2 * groupD8.uY(rotate), rotate = groupD8.add(rotate, 2), this.x1 = cX + w2 * groupD8.uX(rotate), this.y1 = cY + h2 * groupD8.uY(rotate), rotate = groupD8.add(rotate, 2), this.x2 = cX + w2 * groupD8.uX(rotate), this.y2 = cY + h2 * groupD8.uY(rotate), rotate = groupD8.add(rotate, 2), this.x3 = cX + w2 * groupD8.uX(rotate), this.y3 = cY + h2 * groupD8.uY(rotate);\n    } else this.x0 = frame.x / tw, this.y0 = frame.y / th, this.x1 = (frame.x + frame.width) / tw, this.y1 = frame.y / th, this.x2 = (frame.x + frame.width) / tw, this.y2 = (frame.y + frame.height) / th, this.x3 = frame.x / tw, this.y3 = (frame.y + frame.height) / th;\n    this.uvsFloat32[0] = this.x0, this.uvsFloat32[1] = this.y0, this.uvsFloat32[2] = this.x1, this.uvsFloat32[3] = this.y1, this.uvsFloat32[4] = this.x2, this.uvsFloat32[5] = this.y2, this.uvsFloat32[6] = this.x3, this.uvsFloat32[7] = this.y3;\n  }\n}\nTextureUvs.prototype.toString = function () {\n  return `[@pixi/core:TextureUvs x0=${this.x0} y0=${this.y0} x1=${this.x1} y1=${this.y1} x2=${this.x2} y2=${this.y2} x3=${this.x3} y3=${this.y3}]`;\n};\nexport { TextureUvs };", "map": {"version": 3, "names": ["TextureUvs", "constructor", "x0", "y0", "x1", "y1", "x2", "y2", "x3", "y3", "uvsFloat32", "Float32Array", "set", "frame", "baseFrame", "rotate", "tw", "width", "th", "height", "w2", "h2", "cX", "x", "cY", "y", "groupD8", "add", "NW", "uX", "uY", "prototype", "toString"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\textures\\TextureUvs.ts"], "sourcesContent": ["import { groupD8 } from '@pixi/math';\n\nimport type { ISize, Rectangle } from '@pixi/math';\n\n/**\n * Stores a texture's frame in UV coordinates, in\n * which everything lies in the rectangle `[(0,0), (1,0),\n * (1,1), (0,1)]`.\n *\n * | Corner       | Coordinates |\n * |--------------|-------------|\n * | Top-Left     | `(x0,y0)`   |\n * | Top-Right    | `(x1,y1)`   |\n * | Bottom-Right | `(x2,y2)`   |\n * | Bottom-Left  | `(x3,y3)`   |\n * @protected\n * @memberof PIXI\n */\nexport class TextureUvs\n{\n    /** X-component of top-left corner `(x0,y0)`. */\n    public x0: number;\n\n    /** Y-component of top-left corner `(x0,y0)`. */\n    public y0: number;\n\n    /** X-component of top-right corner `(x1,y1)`. */\n    public x1: number;\n\n    /** Y-component of top-right corner `(x1,y1)`. */\n    public y1: number;\n\n    /** X-component of bottom-right corner `(x2,y2)`. */\n    public x2: number;\n\n    /** Y-component of bottom-right corner `(x2,y2)`. */\n    public y2: number;\n\n    /** X-component of bottom-left corner `(x3,y3)`. */\n    public x3: number;\n\n    /** Y-component of bottom-right corner `(x3,y3)`. */\n    public y3: number;\n    public uvsFloat32: Float32Array;\n\n    constructor()\n    {\n        this.x0 = 0;\n        this.y0 = 0;\n        this.x1 = 1;\n        this.y1 = 0;\n        this.x2 = 1;\n        this.y2 = 1;\n        this.x3 = 0;\n        this.y3 = 1;\n\n        this.uvsFloat32 = new Float32Array(8);\n    }\n\n    /**\n     * Sets the texture Uvs based on the given frame information.\n     * @protected\n     * @param frame - The frame of the texture\n     * @param baseFrame - The base frame of the texture\n     * @param rotate - Rotation of frame, see {@link PIXI.groupD8}\n     */\n    set(frame: Rectangle, baseFrame: ISize, rotate: number): void\n    {\n        const tw = baseFrame.width;\n        const th = baseFrame.height;\n\n        if (rotate)\n        {\n            // width and height div 2 div baseFrame size\n            const w2 = frame.width / 2 / tw;\n            const h2 = frame.height / 2 / th;\n\n            // coordinates of center\n            const cX = (frame.x / tw) + w2;\n            const cY = (frame.y / th) + h2;\n\n            rotate = groupD8.add(rotate, groupD8.NW); // NW is top-left corner\n            this.x0 = cX + (w2 * groupD8.uX(rotate));\n            this.y0 = cY + (h2 * groupD8.uY(rotate));\n\n            rotate = groupD8.add(rotate, 2); // rotate 90 degrees clockwise\n            this.x1 = cX + (w2 * groupD8.uX(rotate));\n            this.y1 = cY + (h2 * groupD8.uY(rotate));\n\n            rotate = groupD8.add(rotate, 2);\n            this.x2 = cX + (w2 * groupD8.uX(rotate));\n            this.y2 = cY + (h2 * groupD8.uY(rotate));\n\n            rotate = groupD8.add(rotate, 2);\n            this.x3 = cX + (w2 * groupD8.uX(rotate));\n            this.y3 = cY + (h2 * groupD8.uY(rotate));\n        }\n        else\n        {\n            this.x0 = frame.x / tw;\n            this.y0 = frame.y / th;\n\n            this.x1 = (frame.x + frame.width) / tw;\n            this.y1 = frame.y / th;\n\n            this.x2 = (frame.x + frame.width) / tw;\n            this.y2 = (frame.y + frame.height) / th;\n\n            this.x3 = frame.x / tw;\n            this.y3 = (frame.y + frame.height) / th;\n        }\n\n        this.uvsFloat32[0] = this.x0;\n        this.uvsFloat32[1] = this.y0;\n        this.uvsFloat32[2] = this.x1;\n        this.uvsFloat32[3] = this.y1;\n        this.uvsFloat32[4] = this.x2;\n        this.uvsFloat32[5] = this.y2;\n        this.uvsFloat32[6] = this.x3;\n        this.uvsFloat32[7] = this.y3;\n    }\n}\n\nif (process.env.DEBUG)\n{\n    TextureUvs.prototype.toString = function toString(): string\n    {\n        return `[@pixi/core:TextureUvs `\n            + `x0=${this.x0} y0=${this.y0} `\n            + `x1=${this.x1} y1=${this.y1} x2=${this.x2} `\n            + `y2=${this.y2} x3=${this.x3} y3=${this.y3}`\n            + `]`;\n    };\n}\n"], "mappings": ";AAkBO,MAAMA,UAAA,CACb;EA0BIC,YAAA,EACA;IACS,KAAAC,EAAA,GAAK,GACV,KAAKC,EAAA,GAAK,GACV,KAAKC,EAAA,GAAK,GACV,KAAKC,EAAA,GAAK,GACV,KAAKC,EAAA,GAAK,GACV,KAAKC,EAAA,GAAK,GACV,KAAKC,EAAA,GAAK,GACV,KAAKC,EAAA,GAAK,GAEV,KAAKC,UAAA,GAAa,IAAIC,YAAA,CAAa,CAAC;EACxC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAC,IAAIC,KAAA,EAAkBC,SAAA,EAAkBC,MAAA,EACxC;IACI,MAAMC,EAAA,GAAKF,SAAA,CAAUG,KAAA;MACfC,EAAA,GAAKJ,SAAA,CAAUK,MAAA;IAErB,IAAIJ,MAAA,EACJ;MAEI,MAAMK,EAAA,GAAKP,KAAA,CAAMI,KAAA,GAAQ,IAAID,EAAA;QACvBK,EAAA,GAAKR,KAAA,CAAMM,MAAA,GAAS,IAAID,EAAA;QAGxBI,EAAA,GAAMT,KAAA,CAAMU,CAAA,GAAIP,EAAA,GAAMI,EAAA;QACtBI,EAAA,GAAMX,KAAA,CAAMY,CAAA,GAAIP,EAAA,GAAMG,EAAA;MAE5BN,MAAA,GAASW,OAAA,CAAQC,GAAA,CAAIZ,MAAA,EAAQW,OAAA,CAAQE,EAAE,GACvC,KAAK1B,EAAA,GAAKoB,EAAA,GAAMF,EAAA,GAAKM,OAAA,CAAQG,EAAA,CAAGd,MAAM,GACtC,KAAKZ,EAAA,GAAKqB,EAAA,GAAMH,EAAA,GAAKK,OAAA,CAAQI,EAAA,CAAGf,MAAM,GAEtCA,MAAA,GAASW,OAAA,CAAQC,GAAA,CAAIZ,MAAA,EAAQ,CAAC,GAC9B,KAAKX,EAAA,GAAKkB,EAAA,GAAMF,EAAA,GAAKM,OAAA,CAAQG,EAAA,CAAGd,MAAM,GACtC,KAAKV,EAAA,GAAKmB,EAAA,GAAMH,EAAA,GAAKK,OAAA,CAAQI,EAAA,CAAGf,MAAM,GAEtCA,MAAA,GAASW,OAAA,CAAQC,GAAA,CAAIZ,MAAA,EAAQ,CAAC,GAC9B,KAAKT,EAAA,GAAKgB,EAAA,GAAMF,EAAA,GAAKM,OAAA,CAAQG,EAAA,CAAGd,MAAM,GACtC,KAAKR,EAAA,GAAKiB,EAAA,GAAMH,EAAA,GAAKK,OAAA,CAAQI,EAAA,CAAGf,MAAM,GAEtCA,MAAA,GAASW,OAAA,CAAQC,GAAA,CAAIZ,MAAA,EAAQ,CAAC,GAC9B,KAAKP,EAAA,GAAKc,EAAA,GAAMF,EAAA,GAAKM,OAAA,CAAQG,EAAA,CAAGd,MAAM,GACtC,KAAKN,EAAA,GAAKe,EAAA,GAAMH,EAAA,GAAKK,OAAA,CAAQI,EAAA,CAAGf,MAAM;IAC1C,OAGS,KAAAb,EAAA,GAAKW,KAAA,CAAMU,CAAA,GAAIP,EAAA,EACpB,KAAKb,EAAA,GAAKU,KAAA,CAAMY,CAAA,GAAIP,EAAA,EAEpB,KAAKd,EAAA,IAAMS,KAAA,CAAMU,CAAA,GAAIV,KAAA,CAAMI,KAAA,IAASD,EAAA,EACpC,KAAKX,EAAA,GAAKQ,KAAA,CAAMY,CAAA,GAAIP,EAAA,EAEpB,KAAKZ,EAAA,IAAMO,KAAA,CAAMU,CAAA,GAAIV,KAAA,CAAMI,KAAA,IAASD,EAAA,EACpC,KAAKT,EAAA,IAAMM,KAAA,CAAMY,CAAA,GAAIZ,KAAA,CAAMM,MAAA,IAAUD,EAAA,EAErC,KAAKV,EAAA,GAAKK,KAAA,CAAMU,CAAA,GAAIP,EAAA,EACpB,KAAKP,EAAA,IAAMI,KAAA,CAAMY,CAAA,GAAIZ,KAAA,CAAMM,MAAA,IAAUD,EAAA;IAGpC,KAAAR,UAAA,CAAW,CAAC,IAAI,KAAKR,EAAA,EAC1B,KAAKQ,UAAA,CAAW,CAAC,IAAI,KAAKP,EAAA,EAC1B,KAAKO,UAAA,CAAW,CAAC,IAAI,KAAKN,EAAA,EAC1B,KAAKM,UAAA,CAAW,CAAC,IAAI,KAAKL,EAAA,EAC1B,KAAKK,UAAA,CAAW,CAAC,IAAI,KAAKJ,EAAA,EAC1B,KAAKI,UAAA,CAAW,CAAC,IAAI,KAAKH,EAAA,EAC1B,KAAKG,UAAA,CAAW,CAAC,IAAI,KAAKF,EAAA,EAC1B,KAAKE,UAAA,CAAW,CAAC,IAAI,KAAKD,EAAA;EAC9B;AACJ;AAIIT,UAAA,CAAW+B,SAAA,CAAUC,QAAA,GAAW,YAChC;EACW,oCACK,KAAK9B,EAAE,OAAO,KAAKC,EAAE,OACrB,KAAKC,EAAE,OAAO,KAAKC,EAAE,OAAO,KAAKC,EAAE,OACnC,KAAKC,EAAE,OAAO,KAAKC,EAAE,OAAO,KAAKC,EAAE;AAEnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}