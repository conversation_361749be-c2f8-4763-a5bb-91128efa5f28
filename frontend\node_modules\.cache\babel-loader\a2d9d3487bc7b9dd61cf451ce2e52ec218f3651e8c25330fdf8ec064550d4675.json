{"ast": null, "code": "'use strict';\n\nvar getSideChannel = require('side-channel');\nvar utils = require('./utils');\nvar formats = require('./formats');\nvar has = Object.prototype.hasOwnProperty;\nvar arrayPrefixGenerators = {\n  brackets: function brackets(prefix) {\n    return prefix + '[]';\n  },\n  comma: 'comma',\n  indices: function indices(prefix, key) {\n    return prefix + '[' + key + ']';\n  },\n  repeat: function repeat(prefix) {\n    return prefix;\n  }\n};\nvar isArray = Array.isArray;\nvar push = Array.prototype.push;\nvar pushToArray = function (arr, valueOrArray) {\n  push.apply(arr, isArray(valueOrArray) ? valueOrArray : [valueOrArray]);\n};\nvar toISO = Date.prototype.toISOString;\nvar defaultFormat = formats['default'];\nvar defaults = {\n  addQueryPrefix: false,\n  allowDots: false,\n  allowEmptyArrays: false,\n  arrayFormat: 'indices',\n  charset: 'utf-8',\n  charsetSentinel: false,\n  commaRoundTrip: false,\n  delimiter: '&',\n  encode: true,\n  encodeDotInKeys: false,\n  encoder: utils.encode,\n  encodeValuesOnly: false,\n  filter: void undefined,\n  format: defaultFormat,\n  formatter: formats.formatters[defaultFormat],\n  // deprecated\n  indices: false,\n  serializeDate: function serializeDate(date) {\n    return toISO.call(date);\n  },\n  skipNulls: false,\n  strictNullHandling: false\n};\nvar isNonNullishPrimitive = function isNonNullishPrimitive(v) {\n  return typeof v === 'string' || typeof v === 'number' || typeof v === 'boolean' || typeof v === 'symbol' || typeof v === 'bigint';\n};\nvar sentinel = {};\nvar stringify = function stringify(object, prefix, generateArrayPrefix, commaRoundTrip, allowEmptyArrays, strictNullHandling, skipNulls, encodeDotInKeys, encoder, filter, sort, allowDots, serializeDate, format, formatter, encodeValuesOnly, charset, sideChannel) {\n  var obj = object;\n  var tmpSc = sideChannel;\n  var step = 0;\n  var findFlag = false;\n  while ((tmpSc = tmpSc.get(sentinel)) !== void undefined && !findFlag) {\n    // Where object last appeared in the ref tree\n    var pos = tmpSc.get(object);\n    step += 1;\n    if (typeof pos !== 'undefined') {\n      if (pos === step) {\n        throw new RangeError('Cyclic object value');\n      } else {\n        findFlag = true; // Break while\n      }\n    }\n    if (typeof tmpSc.get(sentinel) === 'undefined') {\n      step = 0;\n    }\n  }\n  if (typeof filter === 'function') {\n    obj = filter(prefix, obj);\n  } else if (obj instanceof Date) {\n    obj = serializeDate(obj);\n  } else if (generateArrayPrefix === 'comma' && isArray(obj)) {\n    obj = utils.maybeMap(obj, function (value) {\n      if (value instanceof Date) {\n        return serializeDate(value);\n      }\n      return value;\n    });\n  }\n  if (obj === null) {\n    if (strictNullHandling) {\n      return encoder && !encodeValuesOnly ? encoder(prefix, defaults.encoder, charset, 'key', format) : prefix;\n    }\n    obj = '';\n  }\n  if (isNonNullishPrimitive(obj) || utils.isBuffer(obj)) {\n    if (encoder) {\n      var keyValue = encodeValuesOnly ? prefix : encoder(prefix, defaults.encoder, charset, 'key', format);\n      return [formatter(keyValue) + '=' + formatter(encoder(obj, defaults.encoder, charset, 'value', format))];\n    }\n    return [formatter(prefix) + '=' + formatter(String(obj))];\n  }\n  var values = [];\n  if (typeof obj === 'undefined') {\n    return values;\n  }\n  var objKeys;\n  if (generateArrayPrefix === 'comma' && isArray(obj)) {\n    // we need to join elements in\n    if (encodeValuesOnly && encoder) {\n      obj = utils.maybeMap(obj, encoder);\n    }\n    objKeys = [{\n      value: obj.length > 0 ? obj.join(',') || null : void undefined\n    }];\n  } else if (isArray(filter)) {\n    objKeys = filter;\n  } else {\n    var keys = Object.keys(obj);\n    objKeys = sort ? keys.sort(sort) : keys;\n  }\n  var encodedPrefix = encodeDotInKeys ? String(prefix).replace(/\\./g, '%2E') : String(prefix);\n  var adjustedPrefix = commaRoundTrip && isArray(obj) && obj.length === 1 ? encodedPrefix + '[]' : encodedPrefix;\n  if (allowEmptyArrays && isArray(obj) && obj.length === 0) {\n    return adjustedPrefix + '[]';\n  }\n  for (var j = 0; j < objKeys.length; ++j) {\n    var key = objKeys[j];\n    var value = typeof key === 'object' && key && typeof key.value !== 'undefined' ? key.value : obj[key];\n    if (skipNulls && value === null) {\n      continue;\n    }\n    var encodedKey = allowDots && encodeDotInKeys ? String(key).replace(/\\./g, '%2E') : String(key);\n    var keyPrefix = isArray(obj) ? typeof generateArrayPrefix === 'function' ? generateArrayPrefix(adjustedPrefix, encodedKey) : adjustedPrefix : adjustedPrefix + (allowDots ? '.' + encodedKey : '[' + encodedKey + ']');\n    sideChannel.set(object, step);\n    var valueSideChannel = getSideChannel();\n    valueSideChannel.set(sentinel, sideChannel);\n    pushToArray(values, stringify(value, keyPrefix, generateArrayPrefix, commaRoundTrip, allowEmptyArrays, strictNullHandling, skipNulls, encodeDotInKeys, generateArrayPrefix === 'comma' && encodeValuesOnly && isArray(obj) ? null : encoder, filter, sort, allowDots, serializeDate, format, formatter, encodeValuesOnly, charset, valueSideChannel));\n  }\n  return values;\n};\nvar normalizeStringifyOptions = function normalizeStringifyOptions(opts) {\n  if (!opts) {\n    return defaults;\n  }\n  if (typeof opts.allowEmptyArrays !== 'undefined' && typeof opts.allowEmptyArrays !== 'boolean') {\n    throw new TypeError('`allowEmptyArrays` option can only be `true` or `false`, when provided');\n  }\n  if (typeof opts.encodeDotInKeys !== 'undefined' && typeof opts.encodeDotInKeys !== 'boolean') {\n    throw new TypeError('`encodeDotInKeys` option can only be `true` or `false`, when provided');\n  }\n  if (opts.encoder !== null && typeof opts.encoder !== 'undefined' && typeof opts.encoder !== 'function') {\n    throw new TypeError('Encoder has to be a function.');\n  }\n  var charset = opts.charset || defaults.charset;\n  if (typeof opts.charset !== 'undefined' && opts.charset !== 'utf-8' && opts.charset !== 'iso-8859-1') {\n    throw new TypeError('The charset option must be either utf-8, iso-8859-1, or undefined');\n  }\n  var format = formats['default'];\n  if (typeof opts.format !== 'undefined') {\n    if (!has.call(formats.formatters, opts.format)) {\n      throw new TypeError('Unknown format option provided.');\n    }\n    format = opts.format;\n  }\n  var formatter = formats.formatters[format];\n  var filter = defaults.filter;\n  if (typeof opts.filter === 'function' || isArray(opts.filter)) {\n    filter = opts.filter;\n  }\n  var arrayFormat;\n  if (opts.arrayFormat in arrayPrefixGenerators) {\n    arrayFormat = opts.arrayFormat;\n  } else if ('indices' in opts) {\n    arrayFormat = opts.indices ? 'indices' : 'repeat';\n  } else {\n    arrayFormat = defaults.arrayFormat;\n  }\n  if ('commaRoundTrip' in opts && typeof opts.commaRoundTrip !== 'boolean') {\n    throw new TypeError('`commaRoundTrip` must be a boolean, or absent');\n  }\n  var allowDots = typeof opts.allowDots === 'undefined' ? opts.encodeDotInKeys === true ? true : defaults.allowDots : !!opts.allowDots;\n  return {\n    addQueryPrefix: typeof opts.addQueryPrefix === 'boolean' ? opts.addQueryPrefix : defaults.addQueryPrefix,\n    allowDots: allowDots,\n    allowEmptyArrays: typeof opts.allowEmptyArrays === 'boolean' ? !!opts.allowEmptyArrays : defaults.allowEmptyArrays,\n    arrayFormat: arrayFormat,\n    charset: charset,\n    charsetSentinel: typeof opts.charsetSentinel === 'boolean' ? opts.charsetSentinel : defaults.charsetSentinel,\n    commaRoundTrip: !!opts.commaRoundTrip,\n    delimiter: typeof opts.delimiter === 'undefined' ? defaults.delimiter : opts.delimiter,\n    encode: typeof opts.encode === 'boolean' ? opts.encode : defaults.encode,\n    encodeDotInKeys: typeof opts.encodeDotInKeys === 'boolean' ? opts.encodeDotInKeys : defaults.encodeDotInKeys,\n    encoder: typeof opts.encoder === 'function' ? opts.encoder : defaults.encoder,\n    encodeValuesOnly: typeof opts.encodeValuesOnly === 'boolean' ? opts.encodeValuesOnly : defaults.encodeValuesOnly,\n    filter: filter,\n    format: format,\n    formatter: formatter,\n    serializeDate: typeof opts.serializeDate === 'function' ? opts.serializeDate : defaults.serializeDate,\n    skipNulls: typeof opts.skipNulls === 'boolean' ? opts.skipNulls : defaults.skipNulls,\n    sort: typeof opts.sort === 'function' ? opts.sort : null,\n    strictNullHandling: typeof opts.strictNullHandling === 'boolean' ? opts.strictNullHandling : defaults.strictNullHandling\n  };\n};\nmodule.exports = function (object, opts) {\n  var obj = object;\n  var options = normalizeStringifyOptions(opts);\n  var objKeys;\n  var filter;\n  if (typeof options.filter === 'function') {\n    filter = options.filter;\n    obj = filter('', obj);\n  } else if (isArray(options.filter)) {\n    filter = options.filter;\n    objKeys = filter;\n  }\n  var keys = [];\n  if (typeof obj !== 'object' || obj === null) {\n    return '';\n  }\n  var generateArrayPrefix = arrayPrefixGenerators[options.arrayFormat];\n  var commaRoundTrip = generateArrayPrefix === 'comma' && options.commaRoundTrip;\n  if (!objKeys) {\n    objKeys = Object.keys(obj);\n  }\n  if (options.sort) {\n    objKeys.sort(options.sort);\n  }\n  var sideChannel = getSideChannel();\n  for (var i = 0; i < objKeys.length; ++i) {\n    var key = objKeys[i];\n    var value = obj[key];\n    if (options.skipNulls && value === null) {\n      continue;\n    }\n    pushToArray(keys, stringify(value, key, generateArrayPrefix, commaRoundTrip, options.allowEmptyArrays, options.strictNullHandling, options.skipNulls, options.encodeDotInKeys, options.encode ? options.encoder : null, options.filter, options.sort, options.allowDots, options.serializeDate, options.format, options.formatter, options.encodeValuesOnly, options.charset, sideChannel));\n  }\n  var joined = keys.join(options.delimiter);\n  var prefix = options.addQueryPrefix === true ? '?' : '';\n  if (options.charsetSentinel) {\n    if (options.charset === 'iso-8859-1') {\n      // encodeURIComponent('&#10003;'), the \"numeric entity\" representation of a checkmark\n      prefix += 'utf8=%26%2310003%3B&';\n    } else {\n      // encodeURIComponent('✓')\n      prefix += 'utf8=%E2%9C%93&';\n    }\n  }\n  return joined.length > 0 ? prefix + joined : '';\n};", "map": {"version": 3, "names": ["getSideChannel", "require", "utils", "formats", "has", "Object", "prototype", "hasOwnProperty", "arrayPrefixGenerators", "brackets", "prefix", "comma", "indices", "key", "repeat", "isArray", "Array", "push", "pushToArray", "arr", "valueOrArray", "apply", "toISO", "Date", "toISOString", "defaultFormat", "defaults", "addQueryPrefix", "allowDots", "allowEmptyArrays", "arrayFormat", "charset", "charset<PERSON><PERSON><PERSON>l", "commaRoundTrip", "delimiter", "encode", "encodeDotInKeys", "encoder", "encodeValuesOnly", "filter", "undefined", "format", "formatter", "formatters", "serializeDate", "date", "call", "skipNulls", "strict<PERSON>ull<PERSON>andling", "isNonNullishPrimitive", "v", "sentinel", "stringify", "object", "generateArrayPrefix", "sort", "sideChannel", "obj", "tmpSc", "step", "findFlag", "get", "pos", "RangeError", "maybeMap", "value", "<PERSON><PERSON><PERSON><PERSON>", "keyValue", "String", "values", "ob<PERSON><PERSON><PERSON><PERSON>", "length", "join", "keys", "encodedPrefix", "replace", "adjustedPrefix", "j", "<PERSON><PERSON><PERSON>", "keyPrefix", "set", "valueSideChannel", "normalizeStringifyOptions", "opts", "TypeError", "module", "exports", "options", "i", "joined"], "sources": ["C:/Users/<USER>/Projects/Python/EU4/frontend/node_modules/qs/lib/stringify.js"], "sourcesContent": ["'use strict';\n\nvar getSideChannel = require('side-channel');\nvar utils = require('./utils');\nvar formats = require('./formats');\nvar has = Object.prototype.hasOwnProperty;\n\nvar arrayPrefixGenerators = {\n    brackets: function brackets(prefix) {\n        return prefix + '[]';\n    },\n    comma: 'comma',\n    indices: function indices(prefix, key) {\n        return prefix + '[' + key + ']';\n    },\n    repeat: function repeat(prefix) {\n        return prefix;\n    }\n};\n\nvar isArray = Array.isArray;\nvar push = Array.prototype.push;\nvar pushToArray = function (arr, valueOrArray) {\n    push.apply(arr, isArray(valueOrArray) ? valueOrArray : [valueOrArray]);\n};\n\nvar toISO = Date.prototype.toISOString;\n\nvar defaultFormat = formats['default'];\nvar defaults = {\n    addQueryPrefix: false,\n    allowDots: false,\n    allowEmptyArrays: false,\n    arrayFormat: 'indices',\n    charset: 'utf-8',\n    charsetSentinel: false,\n    commaRoundTrip: false,\n    delimiter: '&',\n    encode: true,\n    encodeDotInKeys: false,\n    encoder: utils.encode,\n    encodeValuesOnly: false,\n    filter: void undefined,\n    format: defaultFormat,\n    formatter: formats.formatters[defaultFormat],\n    // deprecated\n    indices: false,\n    serializeDate: function serializeDate(date) {\n        return toISO.call(date);\n    },\n    skipNulls: false,\n    strictNullHandling: false\n};\n\nvar isNonNullishPrimitive = function isNonNullishPrimitive(v) {\n    return typeof v === 'string'\n        || typeof v === 'number'\n        || typeof v === 'boolean'\n        || typeof v === 'symbol'\n        || typeof v === 'bigint';\n};\n\nvar sentinel = {};\n\nvar stringify = function stringify(\n    object,\n    prefix,\n    generateArrayPrefix,\n    commaRoundTrip,\n    allowEmptyArrays,\n    strictNullHandling,\n    skipNulls,\n    encodeDotInKeys,\n    encoder,\n    filter,\n    sort,\n    allowDots,\n    serializeDate,\n    format,\n    formatter,\n    encodeValuesOnly,\n    charset,\n    sideChannel\n) {\n    var obj = object;\n\n    var tmpSc = sideChannel;\n    var step = 0;\n    var findFlag = false;\n    while ((tmpSc = tmpSc.get(sentinel)) !== void undefined && !findFlag) {\n        // Where object last appeared in the ref tree\n        var pos = tmpSc.get(object);\n        step += 1;\n        if (typeof pos !== 'undefined') {\n            if (pos === step) {\n                throw new RangeError('Cyclic object value');\n            } else {\n                findFlag = true; // Break while\n            }\n        }\n        if (typeof tmpSc.get(sentinel) === 'undefined') {\n            step = 0;\n        }\n    }\n\n    if (typeof filter === 'function') {\n        obj = filter(prefix, obj);\n    } else if (obj instanceof Date) {\n        obj = serializeDate(obj);\n    } else if (generateArrayPrefix === 'comma' && isArray(obj)) {\n        obj = utils.maybeMap(obj, function (value) {\n            if (value instanceof Date) {\n                return serializeDate(value);\n            }\n            return value;\n        });\n    }\n\n    if (obj === null) {\n        if (strictNullHandling) {\n            return encoder && !encodeValuesOnly ? encoder(prefix, defaults.encoder, charset, 'key', format) : prefix;\n        }\n\n        obj = '';\n    }\n\n    if (isNonNullishPrimitive(obj) || utils.isBuffer(obj)) {\n        if (encoder) {\n            var keyValue = encodeValuesOnly ? prefix : encoder(prefix, defaults.encoder, charset, 'key', format);\n            return [formatter(keyValue) + '=' + formatter(encoder(obj, defaults.encoder, charset, 'value', format))];\n        }\n        return [formatter(prefix) + '=' + formatter(String(obj))];\n    }\n\n    var values = [];\n\n    if (typeof obj === 'undefined') {\n        return values;\n    }\n\n    var objKeys;\n    if (generateArrayPrefix === 'comma' && isArray(obj)) {\n        // we need to join elements in\n        if (encodeValuesOnly && encoder) {\n            obj = utils.maybeMap(obj, encoder);\n        }\n        objKeys = [{ value: obj.length > 0 ? obj.join(',') || null : void undefined }];\n    } else if (isArray(filter)) {\n        objKeys = filter;\n    } else {\n        var keys = Object.keys(obj);\n        objKeys = sort ? keys.sort(sort) : keys;\n    }\n\n    var encodedPrefix = encodeDotInKeys ? String(prefix).replace(/\\./g, '%2E') : String(prefix);\n\n    var adjustedPrefix = commaRoundTrip && isArray(obj) && obj.length === 1 ? encodedPrefix + '[]' : encodedPrefix;\n\n    if (allowEmptyArrays && isArray(obj) && obj.length === 0) {\n        return adjustedPrefix + '[]';\n    }\n\n    for (var j = 0; j < objKeys.length; ++j) {\n        var key = objKeys[j];\n        var value = typeof key === 'object' && key && typeof key.value !== 'undefined'\n            ? key.value\n            : obj[key];\n\n        if (skipNulls && value === null) {\n            continue;\n        }\n\n        var encodedKey = allowDots && encodeDotInKeys ? String(key).replace(/\\./g, '%2E') : String(key);\n        var keyPrefix = isArray(obj)\n            ? typeof generateArrayPrefix === 'function' ? generateArrayPrefix(adjustedPrefix, encodedKey) : adjustedPrefix\n            : adjustedPrefix + (allowDots ? '.' + encodedKey : '[' + encodedKey + ']');\n\n        sideChannel.set(object, step);\n        var valueSideChannel = getSideChannel();\n        valueSideChannel.set(sentinel, sideChannel);\n        pushToArray(values, stringify(\n            value,\n            keyPrefix,\n            generateArrayPrefix,\n            commaRoundTrip,\n            allowEmptyArrays,\n            strictNullHandling,\n            skipNulls,\n            encodeDotInKeys,\n            generateArrayPrefix === 'comma' && encodeValuesOnly && isArray(obj) ? null : encoder,\n            filter,\n            sort,\n            allowDots,\n            serializeDate,\n            format,\n            formatter,\n            encodeValuesOnly,\n            charset,\n            valueSideChannel\n        ));\n    }\n\n    return values;\n};\n\nvar normalizeStringifyOptions = function normalizeStringifyOptions(opts) {\n    if (!opts) {\n        return defaults;\n    }\n\n    if (typeof opts.allowEmptyArrays !== 'undefined' && typeof opts.allowEmptyArrays !== 'boolean') {\n        throw new TypeError('`allowEmptyArrays` option can only be `true` or `false`, when provided');\n    }\n\n    if (typeof opts.encodeDotInKeys !== 'undefined' && typeof opts.encodeDotInKeys !== 'boolean') {\n        throw new TypeError('`encodeDotInKeys` option can only be `true` or `false`, when provided');\n    }\n\n    if (opts.encoder !== null && typeof opts.encoder !== 'undefined' && typeof opts.encoder !== 'function') {\n        throw new TypeError('Encoder has to be a function.');\n    }\n\n    var charset = opts.charset || defaults.charset;\n    if (typeof opts.charset !== 'undefined' && opts.charset !== 'utf-8' && opts.charset !== 'iso-8859-1') {\n        throw new TypeError('The charset option must be either utf-8, iso-8859-1, or undefined');\n    }\n\n    var format = formats['default'];\n    if (typeof opts.format !== 'undefined') {\n        if (!has.call(formats.formatters, opts.format)) {\n            throw new TypeError('Unknown format option provided.');\n        }\n        format = opts.format;\n    }\n    var formatter = formats.formatters[format];\n\n    var filter = defaults.filter;\n    if (typeof opts.filter === 'function' || isArray(opts.filter)) {\n        filter = opts.filter;\n    }\n\n    var arrayFormat;\n    if (opts.arrayFormat in arrayPrefixGenerators) {\n        arrayFormat = opts.arrayFormat;\n    } else if ('indices' in opts) {\n        arrayFormat = opts.indices ? 'indices' : 'repeat';\n    } else {\n        arrayFormat = defaults.arrayFormat;\n    }\n\n    if ('commaRoundTrip' in opts && typeof opts.commaRoundTrip !== 'boolean') {\n        throw new TypeError('`commaRoundTrip` must be a boolean, or absent');\n    }\n\n    var allowDots = typeof opts.allowDots === 'undefined' ? opts.encodeDotInKeys === true ? true : defaults.allowDots : !!opts.allowDots;\n\n    return {\n        addQueryPrefix: typeof opts.addQueryPrefix === 'boolean' ? opts.addQueryPrefix : defaults.addQueryPrefix,\n        allowDots: allowDots,\n        allowEmptyArrays: typeof opts.allowEmptyArrays === 'boolean' ? !!opts.allowEmptyArrays : defaults.allowEmptyArrays,\n        arrayFormat: arrayFormat,\n        charset: charset,\n        charsetSentinel: typeof opts.charsetSentinel === 'boolean' ? opts.charsetSentinel : defaults.charsetSentinel,\n        commaRoundTrip: !!opts.commaRoundTrip,\n        delimiter: typeof opts.delimiter === 'undefined' ? defaults.delimiter : opts.delimiter,\n        encode: typeof opts.encode === 'boolean' ? opts.encode : defaults.encode,\n        encodeDotInKeys: typeof opts.encodeDotInKeys === 'boolean' ? opts.encodeDotInKeys : defaults.encodeDotInKeys,\n        encoder: typeof opts.encoder === 'function' ? opts.encoder : defaults.encoder,\n        encodeValuesOnly: typeof opts.encodeValuesOnly === 'boolean' ? opts.encodeValuesOnly : defaults.encodeValuesOnly,\n        filter: filter,\n        format: format,\n        formatter: formatter,\n        serializeDate: typeof opts.serializeDate === 'function' ? opts.serializeDate : defaults.serializeDate,\n        skipNulls: typeof opts.skipNulls === 'boolean' ? opts.skipNulls : defaults.skipNulls,\n        sort: typeof opts.sort === 'function' ? opts.sort : null,\n        strictNullHandling: typeof opts.strictNullHandling === 'boolean' ? opts.strictNullHandling : defaults.strictNullHandling\n    };\n};\n\nmodule.exports = function (object, opts) {\n    var obj = object;\n    var options = normalizeStringifyOptions(opts);\n\n    var objKeys;\n    var filter;\n\n    if (typeof options.filter === 'function') {\n        filter = options.filter;\n        obj = filter('', obj);\n    } else if (isArray(options.filter)) {\n        filter = options.filter;\n        objKeys = filter;\n    }\n\n    var keys = [];\n\n    if (typeof obj !== 'object' || obj === null) {\n        return '';\n    }\n\n    var generateArrayPrefix = arrayPrefixGenerators[options.arrayFormat];\n    var commaRoundTrip = generateArrayPrefix === 'comma' && options.commaRoundTrip;\n\n    if (!objKeys) {\n        objKeys = Object.keys(obj);\n    }\n\n    if (options.sort) {\n        objKeys.sort(options.sort);\n    }\n\n    var sideChannel = getSideChannel();\n    for (var i = 0; i < objKeys.length; ++i) {\n        var key = objKeys[i];\n        var value = obj[key];\n\n        if (options.skipNulls && value === null) {\n            continue;\n        }\n        pushToArray(keys, stringify(\n            value,\n            key,\n            generateArrayPrefix,\n            commaRoundTrip,\n            options.allowEmptyArrays,\n            options.strictNullHandling,\n            options.skipNulls,\n            options.encodeDotInKeys,\n            options.encode ? options.encoder : null,\n            options.filter,\n            options.sort,\n            options.allowDots,\n            options.serializeDate,\n            options.format,\n            options.formatter,\n            options.encodeValuesOnly,\n            options.charset,\n            sideChannel\n        ));\n    }\n\n    var joined = keys.join(options.delimiter);\n    var prefix = options.addQueryPrefix === true ? '?' : '';\n\n    if (options.charsetSentinel) {\n        if (options.charset === 'iso-8859-1') {\n            // encodeURIComponent('&#10003;'), the \"numeric entity\" representation of a checkmark\n            prefix += 'utf8=%26%2310003%3B&';\n        } else {\n            // encodeURIComponent('✓')\n            prefix += 'utf8=%E2%9C%93&';\n        }\n    }\n\n    return joined.length > 0 ? prefix + joined : '';\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,cAAc,GAAGC,OAAO,CAAC,cAAc,CAAC;AAC5C,IAAIC,KAAK,GAAGD,OAAO,CAAC,SAAS,CAAC;AAC9B,IAAIE,OAAO,GAAGF,OAAO,CAAC,WAAW,CAAC;AAClC,IAAIG,GAAG,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AAEzC,IAAIC,qBAAqB,GAAG;EACxBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,MAAM,EAAE;IAChC,OAAOA,MAAM,GAAG,IAAI;EACxB,CAAC;EACDC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAE,SAASA,OAAOA,CAACF,MAAM,EAAEG,GAAG,EAAE;IACnC,OAAOH,MAAM,GAAG,GAAG,GAAGG,GAAG,GAAG,GAAG;EACnC,CAAC;EACDC,MAAM,EAAE,SAASA,MAAMA,CAACJ,MAAM,EAAE;IAC5B,OAAOA,MAAM;EACjB;AACJ,CAAC;AAED,IAAIK,OAAO,GAAGC,KAAK,CAACD,OAAO;AAC3B,IAAIE,IAAI,GAAGD,KAAK,CAACV,SAAS,CAACW,IAAI;AAC/B,IAAIC,WAAW,GAAG,SAAAA,CAAUC,GAAG,EAAEC,YAAY,EAAE;EAC3CH,IAAI,CAACI,KAAK,CAACF,GAAG,EAAEJ,OAAO,CAACK,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC,CAAC;AAC1E,CAAC;AAED,IAAIE,KAAK,GAAGC,IAAI,CAACjB,SAAS,CAACkB,WAAW;AAEtC,IAAIC,aAAa,GAAGtB,OAAO,CAAC,SAAS,CAAC;AACtC,IAAIuB,QAAQ,GAAG;EACXC,cAAc,EAAE,KAAK;EACrBC,SAAS,EAAE,KAAK;EAChBC,gBAAgB,EAAE,KAAK;EACvBC,WAAW,EAAE,SAAS;EACtBC,OAAO,EAAE,OAAO;EAChBC,eAAe,EAAE,KAAK;EACtBC,cAAc,EAAE,KAAK;EACrBC,SAAS,EAAE,GAAG;EACdC,MAAM,EAAE,IAAI;EACZC,eAAe,EAAE,KAAK;EACtBC,OAAO,EAAEnC,KAAK,CAACiC,MAAM;EACrBG,gBAAgB,EAAE,KAAK;EACvBC,MAAM,EAAE,KAAKC,SAAS;EACtBC,MAAM,EAAEhB,aAAa;EACrBiB,SAAS,EAAEvC,OAAO,CAACwC,UAAU,CAAClB,aAAa,CAAC;EAC5C;EACAb,OAAO,EAAE,KAAK;EACdgC,aAAa,EAAE,SAASA,aAAaA,CAACC,IAAI,EAAE;IACxC,OAAOvB,KAAK,CAACwB,IAAI,CAACD,IAAI,CAAC;EAC3B,CAAC;EACDE,SAAS,EAAE,KAAK;EAChBC,kBAAkB,EAAE;AACxB,CAAC;AAED,IAAIC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,CAAC,EAAE;EAC1D,OAAO,OAAOA,CAAC,KAAK,QAAQ,IACrB,OAAOA,CAAC,KAAK,QAAQ,IACrB,OAAOA,CAAC,KAAK,SAAS,IACtB,OAAOA,CAAC,KAAK,QAAQ,IACrB,OAAOA,CAAC,KAAK,QAAQ;AAChC,CAAC;AAED,IAAIC,QAAQ,GAAG,CAAC,CAAC;AAEjB,IAAIC,SAAS,GAAG,SAASA,SAASA,CAC9BC,MAAM,EACN3C,MAAM,EACN4C,mBAAmB,EACnBrB,cAAc,EACdJ,gBAAgB,EAChBmB,kBAAkB,EAClBD,SAAS,EACTX,eAAe,EACfC,OAAO,EACPE,MAAM,EACNgB,IAAI,EACJ3B,SAAS,EACTgB,aAAa,EACbH,MAAM,EACNC,SAAS,EACTJ,gBAAgB,EAChBP,OAAO,EACPyB,WAAW,EACb;EACE,IAAIC,GAAG,GAAGJ,MAAM;EAEhB,IAAIK,KAAK,GAAGF,WAAW;EACvB,IAAIG,IAAI,GAAG,CAAC;EACZ,IAAIC,QAAQ,GAAG,KAAK;EACpB,OAAO,CAACF,KAAK,GAAGA,KAAK,CAACG,GAAG,CAACV,QAAQ,CAAC,MAAM,KAAKX,SAAS,IAAI,CAACoB,QAAQ,EAAE;IAClE;IACA,IAAIE,GAAG,GAAGJ,KAAK,CAACG,GAAG,CAACR,MAAM,CAAC;IAC3BM,IAAI,IAAI,CAAC;IACT,IAAI,OAAOG,GAAG,KAAK,WAAW,EAAE;MAC5B,IAAIA,GAAG,KAAKH,IAAI,EAAE;QACd,MAAM,IAAII,UAAU,CAAC,qBAAqB,CAAC;MAC/C,CAAC,MAAM;QACHH,QAAQ,GAAG,IAAI,CAAC,CAAC;MACrB;IACJ;IACA,IAAI,OAAOF,KAAK,CAACG,GAAG,CAACV,QAAQ,CAAC,KAAK,WAAW,EAAE;MAC5CQ,IAAI,GAAG,CAAC;IACZ;EACJ;EAEA,IAAI,OAAOpB,MAAM,KAAK,UAAU,EAAE;IAC9BkB,GAAG,GAAGlB,MAAM,CAAC7B,MAAM,EAAE+C,GAAG,CAAC;EAC7B,CAAC,MAAM,IAAIA,GAAG,YAAYlC,IAAI,EAAE;IAC5BkC,GAAG,GAAGb,aAAa,CAACa,GAAG,CAAC;EAC5B,CAAC,MAAM,IAAIH,mBAAmB,KAAK,OAAO,IAAIvC,OAAO,CAAC0C,GAAG,CAAC,EAAE;IACxDA,GAAG,GAAGvD,KAAK,CAAC8D,QAAQ,CAACP,GAAG,EAAE,UAAUQ,KAAK,EAAE;MACvC,IAAIA,KAAK,YAAY1C,IAAI,EAAE;QACvB,OAAOqB,aAAa,CAACqB,KAAK,CAAC;MAC/B;MACA,OAAOA,KAAK;IAChB,CAAC,CAAC;EACN;EAEA,IAAIR,GAAG,KAAK,IAAI,EAAE;IACd,IAAIT,kBAAkB,EAAE;MACpB,OAAOX,OAAO,IAAI,CAACC,gBAAgB,GAAGD,OAAO,CAAC3B,MAAM,EAAEgB,QAAQ,CAACW,OAAO,EAAEN,OAAO,EAAE,KAAK,EAAEU,MAAM,CAAC,GAAG/B,MAAM;IAC5G;IAEA+C,GAAG,GAAG,EAAE;EACZ;EAEA,IAAIR,qBAAqB,CAACQ,GAAG,CAAC,IAAIvD,KAAK,CAACgE,QAAQ,CAACT,GAAG,CAAC,EAAE;IACnD,IAAIpB,OAAO,EAAE;MACT,IAAI8B,QAAQ,GAAG7B,gBAAgB,GAAG5B,MAAM,GAAG2B,OAAO,CAAC3B,MAAM,EAAEgB,QAAQ,CAACW,OAAO,EAAEN,OAAO,EAAE,KAAK,EAAEU,MAAM,CAAC;MACpG,OAAO,CAACC,SAAS,CAACyB,QAAQ,CAAC,GAAG,GAAG,GAAGzB,SAAS,CAACL,OAAO,CAACoB,GAAG,EAAE/B,QAAQ,CAACW,OAAO,EAAEN,OAAO,EAAE,OAAO,EAAEU,MAAM,CAAC,CAAC,CAAC;IAC5G;IACA,OAAO,CAACC,SAAS,CAAChC,MAAM,CAAC,GAAG,GAAG,GAAGgC,SAAS,CAAC0B,MAAM,CAACX,GAAG,CAAC,CAAC,CAAC;EAC7D;EAEA,IAAIY,MAAM,GAAG,EAAE;EAEf,IAAI,OAAOZ,GAAG,KAAK,WAAW,EAAE;IAC5B,OAAOY,MAAM;EACjB;EAEA,IAAIC,OAAO;EACX,IAAIhB,mBAAmB,KAAK,OAAO,IAAIvC,OAAO,CAAC0C,GAAG,CAAC,EAAE;IACjD;IACA,IAAInB,gBAAgB,IAAID,OAAO,EAAE;MAC7BoB,GAAG,GAAGvD,KAAK,CAAC8D,QAAQ,CAACP,GAAG,EAAEpB,OAAO,CAAC;IACtC;IACAiC,OAAO,GAAG,CAAC;MAAEL,KAAK,EAAER,GAAG,CAACc,MAAM,GAAG,CAAC,GAAGd,GAAG,CAACe,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,KAAKhC;IAAU,CAAC,CAAC;EAClF,CAAC,MAAM,IAAIzB,OAAO,CAACwB,MAAM,CAAC,EAAE;IACxB+B,OAAO,GAAG/B,MAAM;EACpB,CAAC,MAAM;IACH,IAAIkC,IAAI,GAAGpE,MAAM,CAACoE,IAAI,CAAChB,GAAG,CAAC;IAC3Ba,OAAO,GAAGf,IAAI,GAAGkB,IAAI,CAAClB,IAAI,CAACA,IAAI,CAAC,GAAGkB,IAAI;EAC3C;EAEA,IAAIC,aAAa,GAAGtC,eAAe,GAAGgC,MAAM,CAAC1D,MAAM,CAAC,CAACiE,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,GAAGP,MAAM,CAAC1D,MAAM,CAAC;EAE3F,IAAIkE,cAAc,GAAG3C,cAAc,IAAIlB,OAAO,CAAC0C,GAAG,CAAC,IAAIA,GAAG,CAACc,MAAM,KAAK,CAAC,GAAGG,aAAa,GAAG,IAAI,GAAGA,aAAa;EAE9G,IAAI7C,gBAAgB,IAAId,OAAO,CAAC0C,GAAG,CAAC,IAAIA,GAAG,CAACc,MAAM,KAAK,CAAC,EAAE;IACtD,OAAOK,cAAc,GAAG,IAAI;EAChC;EAEA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,OAAO,CAACC,MAAM,EAAE,EAAEM,CAAC,EAAE;IACrC,IAAIhE,GAAG,GAAGyD,OAAO,CAACO,CAAC,CAAC;IACpB,IAAIZ,KAAK,GAAG,OAAOpD,GAAG,KAAK,QAAQ,IAAIA,GAAG,IAAI,OAAOA,GAAG,CAACoD,KAAK,KAAK,WAAW,GACxEpD,GAAG,CAACoD,KAAK,GACTR,GAAG,CAAC5C,GAAG,CAAC;IAEd,IAAIkC,SAAS,IAAIkB,KAAK,KAAK,IAAI,EAAE;MAC7B;IACJ;IAEA,IAAIa,UAAU,GAAGlD,SAAS,IAAIQ,eAAe,GAAGgC,MAAM,CAACvD,GAAG,CAAC,CAAC8D,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,GAAGP,MAAM,CAACvD,GAAG,CAAC;IAC/F,IAAIkE,SAAS,GAAGhE,OAAO,CAAC0C,GAAG,CAAC,GACtB,OAAOH,mBAAmB,KAAK,UAAU,GAAGA,mBAAmB,CAACsB,cAAc,EAAEE,UAAU,CAAC,GAAGF,cAAc,GAC5GA,cAAc,IAAIhD,SAAS,GAAG,GAAG,GAAGkD,UAAU,GAAG,GAAG,GAAGA,UAAU,GAAG,GAAG,CAAC;IAE9EtB,WAAW,CAACwB,GAAG,CAAC3B,MAAM,EAAEM,IAAI,CAAC;IAC7B,IAAIsB,gBAAgB,GAAGjF,cAAc,CAAC,CAAC;IACvCiF,gBAAgB,CAACD,GAAG,CAAC7B,QAAQ,EAAEK,WAAW,CAAC;IAC3CtC,WAAW,CAACmD,MAAM,EAAEjB,SAAS,CACzBa,KAAK,EACLc,SAAS,EACTzB,mBAAmB,EACnBrB,cAAc,EACdJ,gBAAgB,EAChBmB,kBAAkB,EAClBD,SAAS,EACTX,eAAe,EACfkB,mBAAmB,KAAK,OAAO,IAAIhB,gBAAgB,IAAIvB,OAAO,CAAC0C,GAAG,CAAC,GAAG,IAAI,GAAGpB,OAAO,EACpFE,MAAM,EACNgB,IAAI,EACJ3B,SAAS,EACTgB,aAAa,EACbH,MAAM,EACNC,SAAS,EACTJ,gBAAgB,EAChBP,OAAO,EACPkD,gBACJ,CAAC,CAAC;EACN;EAEA,OAAOZ,MAAM;AACjB,CAAC;AAED,IAAIa,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,IAAI,EAAE;EACrE,IAAI,CAACA,IAAI,EAAE;IACP,OAAOzD,QAAQ;EACnB;EAEA,IAAI,OAAOyD,IAAI,CAACtD,gBAAgB,KAAK,WAAW,IAAI,OAAOsD,IAAI,CAACtD,gBAAgB,KAAK,SAAS,EAAE;IAC5F,MAAM,IAAIuD,SAAS,CAAC,wEAAwE,CAAC;EACjG;EAEA,IAAI,OAAOD,IAAI,CAAC/C,eAAe,KAAK,WAAW,IAAI,OAAO+C,IAAI,CAAC/C,eAAe,KAAK,SAAS,EAAE;IAC1F,MAAM,IAAIgD,SAAS,CAAC,uEAAuE,CAAC;EAChG;EAEA,IAAID,IAAI,CAAC9C,OAAO,KAAK,IAAI,IAAI,OAAO8C,IAAI,CAAC9C,OAAO,KAAK,WAAW,IAAI,OAAO8C,IAAI,CAAC9C,OAAO,KAAK,UAAU,EAAE;IACpG,MAAM,IAAI+C,SAAS,CAAC,+BAA+B,CAAC;EACxD;EAEA,IAAIrD,OAAO,GAAGoD,IAAI,CAACpD,OAAO,IAAIL,QAAQ,CAACK,OAAO;EAC9C,IAAI,OAAOoD,IAAI,CAACpD,OAAO,KAAK,WAAW,IAAIoD,IAAI,CAACpD,OAAO,KAAK,OAAO,IAAIoD,IAAI,CAACpD,OAAO,KAAK,YAAY,EAAE;IAClG,MAAM,IAAIqD,SAAS,CAAC,mEAAmE,CAAC;EAC5F;EAEA,IAAI3C,MAAM,GAAGtC,OAAO,CAAC,SAAS,CAAC;EAC/B,IAAI,OAAOgF,IAAI,CAAC1C,MAAM,KAAK,WAAW,EAAE;IACpC,IAAI,CAACrC,GAAG,CAAC0C,IAAI,CAAC3C,OAAO,CAACwC,UAAU,EAAEwC,IAAI,CAAC1C,MAAM,CAAC,EAAE;MAC5C,MAAM,IAAI2C,SAAS,CAAC,iCAAiC,CAAC;IAC1D;IACA3C,MAAM,GAAG0C,IAAI,CAAC1C,MAAM;EACxB;EACA,IAAIC,SAAS,GAAGvC,OAAO,CAACwC,UAAU,CAACF,MAAM,CAAC;EAE1C,IAAIF,MAAM,GAAGb,QAAQ,CAACa,MAAM;EAC5B,IAAI,OAAO4C,IAAI,CAAC5C,MAAM,KAAK,UAAU,IAAIxB,OAAO,CAACoE,IAAI,CAAC5C,MAAM,CAAC,EAAE;IAC3DA,MAAM,GAAG4C,IAAI,CAAC5C,MAAM;EACxB;EAEA,IAAIT,WAAW;EACf,IAAIqD,IAAI,CAACrD,WAAW,IAAItB,qBAAqB,EAAE;IAC3CsB,WAAW,GAAGqD,IAAI,CAACrD,WAAW;EAClC,CAAC,MAAM,IAAI,SAAS,IAAIqD,IAAI,EAAE;IAC1BrD,WAAW,GAAGqD,IAAI,CAACvE,OAAO,GAAG,SAAS,GAAG,QAAQ;EACrD,CAAC,MAAM;IACHkB,WAAW,GAAGJ,QAAQ,CAACI,WAAW;EACtC;EAEA,IAAI,gBAAgB,IAAIqD,IAAI,IAAI,OAAOA,IAAI,CAAClD,cAAc,KAAK,SAAS,EAAE;IACtE,MAAM,IAAImD,SAAS,CAAC,+CAA+C,CAAC;EACxE;EAEA,IAAIxD,SAAS,GAAG,OAAOuD,IAAI,CAACvD,SAAS,KAAK,WAAW,GAAGuD,IAAI,CAAC/C,eAAe,KAAK,IAAI,GAAG,IAAI,GAAGV,QAAQ,CAACE,SAAS,GAAG,CAAC,CAACuD,IAAI,CAACvD,SAAS;EAEpI,OAAO;IACHD,cAAc,EAAE,OAAOwD,IAAI,CAACxD,cAAc,KAAK,SAAS,GAAGwD,IAAI,CAACxD,cAAc,GAAGD,QAAQ,CAACC,cAAc;IACxGC,SAAS,EAAEA,SAAS;IACpBC,gBAAgB,EAAE,OAAOsD,IAAI,CAACtD,gBAAgB,KAAK,SAAS,GAAG,CAAC,CAACsD,IAAI,CAACtD,gBAAgB,GAAGH,QAAQ,CAACG,gBAAgB;IAClHC,WAAW,EAAEA,WAAW;IACxBC,OAAO,EAAEA,OAAO;IAChBC,eAAe,EAAE,OAAOmD,IAAI,CAACnD,eAAe,KAAK,SAAS,GAAGmD,IAAI,CAACnD,eAAe,GAAGN,QAAQ,CAACM,eAAe;IAC5GC,cAAc,EAAE,CAAC,CAACkD,IAAI,CAAClD,cAAc;IACrCC,SAAS,EAAE,OAAOiD,IAAI,CAACjD,SAAS,KAAK,WAAW,GAAGR,QAAQ,CAACQ,SAAS,GAAGiD,IAAI,CAACjD,SAAS;IACtFC,MAAM,EAAE,OAAOgD,IAAI,CAAChD,MAAM,KAAK,SAAS,GAAGgD,IAAI,CAAChD,MAAM,GAAGT,QAAQ,CAACS,MAAM;IACxEC,eAAe,EAAE,OAAO+C,IAAI,CAAC/C,eAAe,KAAK,SAAS,GAAG+C,IAAI,CAAC/C,eAAe,GAAGV,QAAQ,CAACU,eAAe;IAC5GC,OAAO,EAAE,OAAO8C,IAAI,CAAC9C,OAAO,KAAK,UAAU,GAAG8C,IAAI,CAAC9C,OAAO,GAAGX,QAAQ,CAACW,OAAO;IAC7EC,gBAAgB,EAAE,OAAO6C,IAAI,CAAC7C,gBAAgB,KAAK,SAAS,GAAG6C,IAAI,CAAC7C,gBAAgB,GAAGZ,QAAQ,CAACY,gBAAgB;IAChHC,MAAM,EAAEA,MAAM;IACdE,MAAM,EAAEA,MAAM;IACdC,SAAS,EAAEA,SAAS;IACpBE,aAAa,EAAE,OAAOuC,IAAI,CAACvC,aAAa,KAAK,UAAU,GAAGuC,IAAI,CAACvC,aAAa,GAAGlB,QAAQ,CAACkB,aAAa;IACrGG,SAAS,EAAE,OAAOoC,IAAI,CAACpC,SAAS,KAAK,SAAS,GAAGoC,IAAI,CAACpC,SAAS,GAAGrB,QAAQ,CAACqB,SAAS;IACpFQ,IAAI,EAAE,OAAO4B,IAAI,CAAC5B,IAAI,KAAK,UAAU,GAAG4B,IAAI,CAAC5B,IAAI,GAAG,IAAI;IACxDP,kBAAkB,EAAE,OAAOmC,IAAI,CAACnC,kBAAkB,KAAK,SAAS,GAAGmC,IAAI,CAACnC,kBAAkB,GAAGtB,QAAQ,CAACsB;EAC1G,CAAC;AACL,CAAC;AAEDqC,MAAM,CAACC,OAAO,GAAG,UAAUjC,MAAM,EAAE8B,IAAI,EAAE;EACrC,IAAI1B,GAAG,GAAGJ,MAAM;EAChB,IAAIkC,OAAO,GAAGL,yBAAyB,CAACC,IAAI,CAAC;EAE7C,IAAIb,OAAO;EACX,IAAI/B,MAAM;EAEV,IAAI,OAAOgD,OAAO,CAAChD,MAAM,KAAK,UAAU,EAAE;IACtCA,MAAM,GAAGgD,OAAO,CAAChD,MAAM;IACvBkB,GAAG,GAAGlB,MAAM,CAAC,EAAE,EAAEkB,GAAG,CAAC;EACzB,CAAC,MAAM,IAAI1C,OAAO,CAACwE,OAAO,CAAChD,MAAM,CAAC,EAAE;IAChCA,MAAM,GAAGgD,OAAO,CAAChD,MAAM;IACvB+B,OAAO,GAAG/B,MAAM;EACpB;EAEA,IAAIkC,IAAI,GAAG,EAAE;EAEb,IAAI,OAAOhB,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;IACzC,OAAO,EAAE;EACb;EAEA,IAAIH,mBAAmB,GAAG9C,qBAAqB,CAAC+E,OAAO,CAACzD,WAAW,CAAC;EACpE,IAAIG,cAAc,GAAGqB,mBAAmB,KAAK,OAAO,IAAIiC,OAAO,CAACtD,cAAc;EAE9E,IAAI,CAACqC,OAAO,EAAE;IACVA,OAAO,GAAGjE,MAAM,CAACoE,IAAI,CAAChB,GAAG,CAAC;EAC9B;EAEA,IAAI8B,OAAO,CAAChC,IAAI,EAAE;IACde,OAAO,CAACf,IAAI,CAACgC,OAAO,CAAChC,IAAI,CAAC;EAC9B;EAEA,IAAIC,WAAW,GAAGxD,cAAc,CAAC,CAAC;EAClC,KAAK,IAAIwF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,OAAO,CAACC,MAAM,EAAE,EAAEiB,CAAC,EAAE;IACrC,IAAI3E,GAAG,GAAGyD,OAAO,CAACkB,CAAC,CAAC;IACpB,IAAIvB,KAAK,GAAGR,GAAG,CAAC5C,GAAG,CAAC;IAEpB,IAAI0E,OAAO,CAACxC,SAAS,IAAIkB,KAAK,KAAK,IAAI,EAAE;MACrC;IACJ;IACA/C,WAAW,CAACuD,IAAI,EAAErB,SAAS,CACvBa,KAAK,EACLpD,GAAG,EACHyC,mBAAmB,EACnBrB,cAAc,EACdsD,OAAO,CAAC1D,gBAAgB,EACxB0D,OAAO,CAACvC,kBAAkB,EAC1BuC,OAAO,CAACxC,SAAS,EACjBwC,OAAO,CAACnD,eAAe,EACvBmD,OAAO,CAACpD,MAAM,GAAGoD,OAAO,CAAClD,OAAO,GAAG,IAAI,EACvCkD,OAAO,CAAChD,MAAM,EACdgD,OAAO,CAAChC,IAAI,EACZgC,OAAO,CAAC3D,SAAS,EACjB2D,OAAO,CAAC3C,aAAa,EACrB2C,OAAO,CAAC9C,MAAM,EACd8C,OAAO,CAAC7C,SAAS,EACjB6C,OAAO,CAACjD,gBAAgB,EACxBiD,OAAO,CAACxD,OAAO,EACfyB,WACJ,CAAC,CAAC;EACN;EAEA,IAAIiC,MAAM,GAAGhB,IAAI,CAACD,IAAI,CAACe,OAAO,CAACrD,SAAS,CAAC;EACzC,IAAIxB,MAAM,GAAG6E,OAAO,CAAC5D,cAAc,KAAK,IAAI,GAAG,GAAG,GAAG,EAAE;EAEvD,IAAI4D,OAAO,CAACvD,eAAe,EAAE;IACzB,IAAIuD,OAAO,CAACxD,OAAO,KAAK,YAAY,EAAE;MAClC;MACArB,MAAM,IAAI,sBAAsB;IACpC,CAAC,MAAM;MACH;MACAA,MAAM,IAAI,iBAAiB;IAC/B;EACJ;EAEA,OAAO+E,MAAM,CAAClB,MAAM,GAAG,CAAC,GAAG7D,MAAM,GAAG+E,MAAM,GAAG,EAAE;AACnD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}