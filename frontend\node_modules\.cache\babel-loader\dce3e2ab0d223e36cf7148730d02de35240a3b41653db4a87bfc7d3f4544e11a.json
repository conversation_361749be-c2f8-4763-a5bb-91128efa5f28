{"ast": null, "code": "import { SHAPES } from \"@pixi/core\";\nconst buildCircle = {\n  build(graphicsData) {\n    const points = graphicsData.points;\n    let x, y, dx, dy, rx, ry;\n    if (graphicsData.type === SHAPES.CIRC) {\n      const circle = graphicsData.shape;\n      x = circle.x, y = circle.y, rx = ry = circle.radius, dx = dy = 0;\n    } else if (graphicsData.type === SHAPES.ELIP) {\n      const ellipse = graphicsData.shape;\n      x = ellipse.x, y = ellipse.y, rx = ellipse.width, ry = ellipse.height, dx = dy = 0;\n    } else {\n      const roundedRect = graphicsData.shape,\n        halfWidth = roundedRect.width / 2,\n        halfHeight = roundedRect.height / 2;\n      x = roundedRect.x + halfWidth, y = roundedRect.y + halfHeight, rx = ry = Math.max(0, Math.min(roundedRect.radius, Math.min(halfWidth, halfHeight))), dx = halfWidth - rx, dy = halfHeight - ry;\n    }\n    if (!(rx >= 0 && ry >= 0 && dx >= 0 && dy >= 0)) {\n      points.length = 0;\n      return;\n    }\n    const n = Math.ceil(2.3 * Math.sqrt(rx + ry)),\n      m = n * 8 + (dx ? 4 : 0) + (dy ? 4 : 0);\n    if (points.length = m, m === 0) return;\n    if (n === 0) {\n      points.length = 8, points[0] = points[6] = x + dx, points[1] = points[3] = y + dy, points[2] = points[4] = x - dx, points[5] = points[7] = y - dy;\n      return;\n    }\n    let j1 = 0,\n      j2 = n * 4 + (dx ? 2 : 0) + 2,\n      j3 = j2,\n      j4 = m;\n    {\n      const x0 = dx + rx,\n        y0 = dy,\n        x1 = x + x0,\n        x2 = x - x0,\n        y1 = y + y0;\n      if (points[j1++] = x1, points[j1++] = y1, points[--j2] = y1, points[--j2] = x2, dy) {\n        const y2 = y - y0;\n        points[j3++] = x2, points[j3++] = y2, points[--j4] = y2, points[--j4] = x1;\n      }\n    }\n    for (let i = 1; i < n; i++) {\n      const a = Math.PI / 2 * (i / n),\n        x0 = dx + Math.cos(a) * rx,\n        y0 = dy + Math.sin(a) * ry,\n        x1 = x + x0,\n        x2 = x - x0,\n        y1 = y + y0,\n        y2 = y - y0;\n      points[j1++] = x1, points[j1++] = y1, points[--j2] = y1, points[--j2] = x2, points[j3++] = x2, points[j3++] = y2, points[--j4] = y2, points[--j4] = x1;\n    }\n    {\n      const x0 = dx,\n        y0 = dy + ry,\n        x1 = x + x0,\n        x2 = x - x0,\n        y1 = y + y0,\n        y2 = y - y0;\n      points[j1++] = x1, points[j1++] = y1, points[--j4] = y2, points[--j4] = x1, dx && (points[j1++] = x2, points[j1++] = y1, points[--j4] = y2, points[--j4] = x2);\n    }\n  },\n  triangulate(graphicsData, graphicsGeometry) {\n    const points = graphicsData.points,\n      verts = graphicsGeometry.points,\n      indices = graphicsGeometry.indices;\n    if (points.length === 0) return;\n    let vertPos = verts.length / 2;\n    const center = vertPos;\n    let x, y;\n    if (graphicsData.type !== SHAPES.RREC) {\n      const circle = graphicsData.shape;\n      x = circle.x, y = circle.y;\n    } else {\n      const roundedRect = graphicsData.shape;\n      x = roundedRect.x + roundedRect.width / 2, y = roundedRect.y + roundedRect.height / 2;\n    }\n    const matrix = graphicsData.matrix;\n    verts.push(graphicsData.matrix ? matrix.a * x + matrix.c * y + matrix.tx : x, graphicsData.matrix ? matrix.b * x + matrix.d * y + matrix.ty : y), vertPos++, verts.push(points[0], points[1]);\n    for (let i = 2; i < points.length; i += 2) verts.push(points[i], points[i + 1]), indices.push(vertPos++, center, vertPos);\n    indices.push(center + 1, center, vertPos);\n  }\n};\nexport { buildCircle };", "map": {"version": 3, "names": ["buildCircle", "build", "graphicsData", "points", "x", "y", "dx", "dy", "rx", "ry", "type", "SHAPES", "CIRC", "circle", "shape", "radius", "ELIP", "ellipse", "width", "height", "roundedRect", "halfWidth", "halfHeight", "Math", "max", "min", "length", "n", "ceil", "sqrt", "m", "j1", "j2", "j3", "j4", "x0", "y0", "x1", "x2", "y1", "y2", "i", "a", "PI", "cos", "sin", "triangulate", "graphicsGeometry", "verts", "indices", "vertPos", "center", "RREC", "matrix", "push", "c", "tx", "b", "d", "ty"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\graphics\\src\\utils\\buildCircle.ts"], "sourcesContent": ["// for type only\nimport { SHAPES } from '@pixi/core';\n\nimport type { Circle, Ellipse, RoundedRectangle } from '@pixi/core';\nimport type { IShapeBuildCommand } from './IShapeBuildCommand';\n\n/**\n * Builds a circle to draw\n *\n * Ignored from docs since it is not directly exposed.\n * @ignore\n * @private\n * @param {PIXI.WebGLGraphicsData} graphicsData - The graphics object to draw\n * @param {object} webGLData - an object containing all the WebGL-specific information to create this shape\n * @param {object} webGLDataNativeLines - an object containing all the WebGL-specific information to create nativeLines\n */\nexport const buildCircle: IShapeBuildCommand = {\n\n    build(graphicsData)\n    {\n        // need to convert points to a nice regular data\n        const points = graphicsData.points;\n\n        let x;\n        let y;\n        let dx;\n        let dy;\n        let rx;\n        let ry;\n\n        if (graphicsData.type === SHAPES.CIRC)\n        {\n            const circle = graphicsData.shape as Circle;\n\n            x = circle.x;\n            y = circle.y;\n            rx = ry = circle.radius;\n            dx = dy = 0;\n        }\n        else if (graphicsData.type === SHAPES.ELIP)\n        {\n            const ellipse = graphicsData.shape as Ellipse;\n\n            x = ellipse.x;\n            y = ellipse.y;\n            rx = ellipse.width;\n            ry = ellipse.height;\n            dx = dy = 0;\n        }\n        else\n        {\n            const roundedRect = graphicsData.shape as RoundedRectangle;\n            const halfWidth = roundedRect.width / 2;\n            const halfHeight = roundedRect.height / 2;\n\n            x = roundedRect.x + halfWidth;\n            y = roundedRect.y + halfHeight;\n            rx = ry = Math.max(0, Math.min(roundedRect.radius, Math.min(halfWidth, halfHeight)));\n            dx = halfWidth - rx;\n            dy = halfHeight - ry;\n        }\n\n        if (!(rx >= 0 && ry >= 0 && dx >= 0 && dy >= 0))\n        {\n            points.length = 0;\n\n            return;\n        }\n\n        // Choose a number of segments such that the maximum absolute deviation from the circle is approximately 0.029\n        const n = Math.ceil(2.3 * Math.sqrt(rx + ry));\n        const m = (n * 8) + (dx ? 4 : 0) + (dy ? 4 : 0);\n\n        points.length = m;\n\n        if (m === 0)\n        {\n            return;\n        }\n\n        if (n === 0)\n        {\n            points.length = 8;\n            points[0] = points[6] = x + dx;\n            points[1] = points[3] = y + dy;\n            points[2] = points[4] = x - dx;\n            points[5] = points[7] = y - dy;\n\n            return;\n        }\n\n        let j1 = 0;\n        let j2 = (n * 4) + (dx ? 2 : 0) + 2;\n        let j3 = j2;\n        let j4 = m;\n\n        {\n            const x0 = dx + rx;\n            const y0 = dy;\n            const x1 = x + x0;\n            const x2 = x - x0;\n            const y1 = y + y0;\n\n            points[j1++] = x1;\n            points[j1++] = y1;\n            points[--j2] = y1;\n            points[--j2] = x2;\n\n            if (dy)\n            {\n                const y2 = y - y0;\n\n                points[j3++] = x2;\n                points[j3++] = y2;\n                points[--j4] = y2;\n                points[--j4] = x1;\n            }\n        }\n\n        for (let i = 1; i < n; i++)\n        {\n            const a = Math.PI / 2 * (i / n);\n            const x0 = dx + (Math.cos(a) * rx);\n            const y0 = dy + (Math.sin(a) * ry);\n            const x1 = x + x0;\n            const x2 = x - x0;\n            const y1 = y + y0;\n            const y2 = y - y0;\n\n            points[j1++] = x1;\n            points[j1++] = y1;\n            points[--j2] = y1;\n            points[--j2] = x2;\n            points[j3++] = x2;\n            points[j3++] = y2;\n            points[--j4] = y2;\n            points[--j4] = x1;\n        }\n\n        {\n            const x0 = dx;\n            const y0 = dy + ry;\n            const x1 = x + x0;\n            const x2 = x - x0;\n            const y1 = y + y0;\n            const y2 = y - y0;\n\n            points[j1++] = x1;\n            points[j1++] = y1;\n            points[--j4] = y2;\n            points[--j4] = x1;\n\n            if (dx)\n            {\n                points[j1++] = x2;\n                points[j1++] = y1;\n                points[--j4] = y2;\n                points[--j4] = x2;\n            }\n        }\n    },\n\n    triangulate(graphicsData, graphicsGeometry)\n    {\n        const points = graphicsData.points;\n        const verts = graphicsGeometry.points;\n        const indices = graphicsGeometry.indices;\n\n        if (points.length === 0)\n        {\n            return;\n        }\n\n        let vertPos = verts.length / 2;\n        const center = vertPos;\n\n        let x;\n        let y;\n\n        if (graphicsData.type !== SHAPES.RREC)\n        {\n            const circle = graphicsData.shape as Circle;\n\n            x = circle.x;\n            y = circle.y;\n        }\n        else\n        {\n            const roundedRect = graphicsData.shape as RoundedRectangle;\n\n            x = roundedRect.x + (roundedRect.width / 2);\n            y = roundedRect.y + (roundedRect.height / 2);\n        }\n\n        const matrix = graphicsData.matrix;\n\n        // Push center (special point)\n        verts.push(\n            graphicsData.matrix ? (matrix.a * x) + (matrix.c * y) + matrix.tx : x,\n            graphicsData.matrix ? (matrix.b * x) + (matrix.d * y) + matrix.ty : y);\n\n        vertPos++;\n\n        verts.push(points[0], points[1]);\n\n        for (let i = 2; i < points.length; i += 2)\n        {\n            verts.push(points[i], points[i + 1]);\n\n            // add some uvs\n            indices.push(vertPos++, center, vertPos);\n        }\n\n        indices.push(center + 1, center, vertPos);\n    },\n};\n"], "mappings": ";AAgBO,MAAMA,WAAA,GAAkC;EAE3CC,MAAMC,YAAA,EACN;IAEI,MAAMC,MAAA,GAASD,YAAA,CAAaC,MAAA;IAE5B,IAAIC,CAAA,EACAC,CAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA;IAEA,IAAAP,YAAA,CAAaQ,IAAA,KAASC,MAAA,CAAOC,IAAA,EACjC;MACI,MAAMC,MAAA,GAASX,YAAA,CAAaY,KAAA;MAExBV,CAAA,GAAAS,MAAA,CAAOT,CAAA,EACXC,CAAA,GAAIQ,MAAA,CAAOR,CAAA,EACXG,EAAA,GAAKC,EAAA,GAAKI,MAAA,CAAOE,MAAA,EACjBT,EAAA,GAAKC,EAAA,GAAK;IAEL,WAAAL,YAAA,CAAaQ,IAAA,KAASC,MAAA,CAAOK,IAAA,EACtC;MACI,MAAMC,OAAA,GAAUf,YAAA,CAAaY,KAAA;MAE7BV,CAAA,GAAIa,OAAA,CAAQb,CAAA,EACZC,CAAA,GAAIY,OAAA,CAAQZ,CAAA,EACZG,EAAA,GAAKS,OAAA,CAAQC,KAAA,EACbT,EAAA,GAAKQ,OAAA,CAAQE,MAAA,EACbb,EAAA,GAAKC,EAAA,GAAK;IAAA,OAGd;MACU,MAAAa,WAAA,GAAclB,YAAA,CAAaY,KAAA;QAC3BO,SAAA,GAAYD,WAAA,CAAYF,KAAA,GAAQ;QAChCI,UAAA,GAAaF,WAAA,CAAYD,MAAA,GAAS;MAEpCf,CAAA,GAAAgB,WAAA,CAAYhB,CAAA,GAAIiB,SAAA,EACpBhB,CAAA,GAAIe,WAAA,CAAYf,CAAA,GAAIiB,UAAA,EACpBd,EAAA,GAAKC,EAAA,GAAKc,IAAA,CAAKC,GAAA,CAAI,GAAGD,IAAA,CAAKE,GAAA,CAAIL,WAAA,CAAYL,MAAA,EAAQQ,IAAA,CAAKE,GAAA,CAAIJ,SAAA,EAAWC,UAAU,CAAC,CAAC,GACnFhB,EAAA,GAAKe,SAAA,GAAYb,EAAA,EACjBD,EAAA,GAAKe,UAAA,GAAab,EAAA;IACtB;IAEI,MAAED,EAAA,IAAM,KAAKC,EAAA,IAAM,KAAKH,EAAA,IAAM,KAAKC,EAAA,IAAM,IAC7C;MACIJ,MAAA,CAAOuB,MAAA,GAAS;MAEhB;IACJ;IAGA,MAAMC,CAAA,GAAIJ,IAAA,CAAKK,IAAA,CAAK,MAAML,IAAA,CAAKM,IAAA,CAAKrB,EAAA,GAAKC,EAAE,CAAC;MACtCqB,CAAA,GAAKH,CAAA,GAAI,KAAMrB,EAAA,GAAK,IAAI,MAAMC,EAAA,GAAK,IAAI;IAE7C,IAAAJ,MAAA,CAAOuB,MAAA,GAASI,CAAA,EAEZA,CAAA,KAAM,GAEN;IAGJ,IAAIH,CAAA,KAAM,GACV;MACIxB,MAAA,CAAOuB,MAAA,GAAS,GAChBvB,MAAA,CAAO,CAAC,IAAIA,MAAA,CAAO,CAAC,IAAIC,CAAA,GAAIE,EAAA,EAC5BH,MAAA,CAAO,CAAC,IAAIA,MAAA,CAAO,CAAC,IAAIE,CAAA,GAAIE,EAAA,EAC5BJ,MAAA,CAAO,CAAC,IAAIA,MAAA,CAAO,CAAC,IAAIC,CAAA,GAAIE,EAAA,EAC5BH,MAAA,CAAO,CAAC,IAAIA,MAAA,CAAO,CAAC,IAAIE,CAAA,GAAIE,EAAA;MAE5B;IACJ;IAEI,IAAAwB,EAAA,GAAK;MACLC,EAAA,GAAML,CAAA,GAAI,KAAMrB,EAAA,GAAK,IAAI,KAAK;MAC9B2B,EAAA,GAAKD,EAAA;MACLE,EAAA,GAAKJ,CAAA;IAET;MACI,MAAMK,EAAA,GAAK7B,EAAA,GAAKE,EAAA;QACV4B,EAAA,GAAK7B,EAAA;QACL8B,EAAA,GAAKjC,CAAA,GAAI+B,EAAA;QACTG,EAAA,GAAKlC,CAAA,GAAI+B,EAAA;QACTI,EAAA,GAAKlC,CAAA,GAAI+B,EAAA;MAOf,IALAjC,MAAA,CAAO4B,EAAA,EAAI,IAAIM,EAAA,EACflC,MAAA,CAAO4B,EAAA,EAAI,IAAIQ,EAAA,EACfpC,MAAA,CAAO,EAAE6B,EAAE,IAAIO,EAAA,EACfpC,MAAA,CAAO,EAAE6B,EAAE,IAAIM,EAAA,EAEX/B,EAAA,EACJ;QACI,MAAMiC,EAAA,GAAKnC,CAAA,GAAI+B,EAAA;QAEfjC,MAAA,CAAO8B,EAAA,EAAI,IAAIK,EAAA,EACfnC,MAAA,CAAO8B,EAAA,EAAI,IAAIO,EAAA,EACfrC,MAAA,CAAO,EAAE+B,EAAE,IAAIM,EAAA,EACfrC,MAAA,CAAO,EAAE+B,EAAE,IAAIG,EAAA;MACnB;IACJ;IAEA,SAASI,CAAA,GAAI,GAAGA,CAAA,GAAId,CAAA,EAAGc,CAAA,IACvB;MACI,MAAMC,CAAA,GAAInB,IAAA,CAAKoB,EAAA,GAAK,KAAKF,CAAA,GAAId,CAAA;QACvBQ,EAAA,GAAK7B,EAAA,GAAMiB,IAAA,CAAKqB,GAAA,CAAIF,CAAC,IAAIlC,EAAA;QACzB4B,EAAA,GAAK7B,EAAA,GAAMgB,IAAA,CAAKsB,GAAA,CAAIH,CAAC,IAAIjC,EAAA;QACzB4B,EAAA,GAAKjC,CAAA,GAAI+B,EAAA;QACTG,EAAA,GAAKlC,CAAA,GAAI+B,EAAA;QACTI,EAAA,GAAKlC,CAAA,GAAI+B,EAAA;QACTI,EAAA,GAAKnC,CAAA,GAAI+B,EAAA;MAEfjC,MAAA,CAAO4B,EAAA,EAAI,IAAIM,EAAA,EACflC,MAAA,CAAO4B,EAAA,EAAI,IAAIQ,EAAA,EACfpC,MAAA,CAAO,EAAE6B,EAAE,IAAIO,EAAA,EACfpC,MAAA,CAAO,EAAE6B,EAAE,IAAIM,EAAA,EACfnC,MAAA,CAAO8B,EAAA,EAAI,IAAIK,EAAA,EACfnC,MAAA,CAAO8B,EAAA,EAAI,IAAIO,EAAA,EACfrC,MAAA,CAAO,EAAE+B,EAAE,IAAIM,EAAA,EACfrC,MAAA,CAAO,EAAE+B,EAAE,IAAIG,EAAA;IACnB;IAEA;MACI,MAAMF,EAAA,GAAK7B,EAAA;QACL8B,EAAA,GAAK7B,EAAA,GAAKE,EAAA;QACV4B,EAAA,GAAKjC,CAAA,GAAI+B,EAAA;QACTG,EAAA,GAAKlC,CAAA,GAAI+B,EAAA;QACTI,EAAA,GAAKlC,CAAA,GAAI+B,EAAA;QACTI,EAAA,GAAKnC,CAAA,GAAI+B,EAAA;MAEfjC,MAAA,CAAO4B,EAAA,EAAI,IAAIM,EAAA,EACflC,MAAA,CAAO4B,EAAA,EAAI,IAAIQ,EAAA,EACfpC,MAAA,CAAO,EAAE+B,EAAE,IAAIM,EAAA,EACfrC,MAAA,CAAO,EAAE+B,EAAE,IAAIG,EAAA,EAEX/B,EAAA,KAEAH,MAAA,CAAO4B,EAAA,EAAI,IAAIO,EAAA,EACfnC,MAAA,CAAO4B,EAAA,EAAI,IAAIQ,EAAA,EACfpC,MAAA,CAAO,EAAE+B,EAAE,IAAIM,EAAA,EACfrC,MAAA,CAAO,EAAE+B,EAAE,IAAII,EAAA;IAEvB;EACJ;EAEAQ,YAAY5C,YAAA,EAAc6C,gBAAA,EAC1B;IACI,MAAM5C,MAAA,GAASD,YAAA,CAAaC,MAAA;MACtB6C,KAAA,GAAQD,gBAAA,CAAiB5C,MAAA;MACzB8C,OAAA,GAAUF,gBAAA,CAAiBE,OAAA;IAEjC,IAAI9C,MAAA,CAAOuB,MAAA,KAAW,GAElB;IAGA,IAAAwB,OAAA,GAAUF,KAAA,CAAMtB,MAAA,GAAS;IAC7B,MAAMyB,MAAA,GAASD,OAAA;IAEf,IAAI9C,CAAA,EACAC,CAAA;IAEA,IAAAH,YAAA,CAAaQ,IAAA,KAASC,MAAA,CAAOyC,IAAA,EACjC;MACI,MAAMvC,MAAA,GAASX,YAAA,CAAaY,KAAA;MAExBV,CAAA,GAAAS,MAAA,CAAOT,CAAA,EACXC,CAAA,GAAIQ,MAAA,CAAOR,CAAA;IAAA,OAGf;MACI,MAAMe,WAAA,GAAclB,YAAA,CAAaY,KAAA;MAE7BV,CAAA,GAAAgB,WAAA,CAAYhB,CAAA,GAAKgB,WAAA,CAAYF,KAAA,GAAQ,GACzCb,CAAA,GAAIe,WAAA,CAAYf,CAAA,GAAKe,WAAA,CAAYD,MAAA,GAAS;IAC9C;IAEA,MAAMkC,MAAA,GAASnD,YAAA,CAAamD,MAAA;IAGtBL,KAAA,CAAAM,IAAA,CACFpD,YAAA,CAAamD,MAAA,GAAUA,MAAA,CAAOX,CAAA,GAAItC,CAAA,GAAMiD,MAAA,CAAOE,CAAA,GAAIlD,CAAA,GAAKgD,MAAA,CAAOG,EAAA,GAAKpD,CAAA,EACpEF,YAAA,CAAamD,MAAA,GAAUA,MAAA,CAAOI,CAAA,GAAIrD,CAAA,GAAMiD,MAAA,CAAOK,CAAA,GAAIrD,CAAA,GAAKgD,MAAA,CAAOM,EAAA,GAAKtD,CAAA,GAExE6C,OAAA,IAEAF,KAAA,CAAMM,IAAA,CAAKnD,MAAA,CAAO,CAAC,GAAGA,MAAA,CAAO,CAAC,CAAC;IAE/B,SAASsC,CAAA,GAAI,GAAGA,CAAA,GAAItC,MAAA,CAAOuB,MAAA,EAAQe,CAAA,IAAK,GAEpCO,KAAA,CAAMM,IAAA,CAAKnD,MAAA,CAAOsC,CAAC,GAAGtC,MAAA,CAAOsC,CAAA,GAAI,CAAC,CAAC,GAGnCQ,OAAA,CAAQK,IAAA,CAAKJ,OAAA,IAAWC,MAAA,EAAQD,OAAO;IAG3CD,OAAA,CAAQK,IAAA,CAAKH,MAAA,GAAS,GAAGA,MAAA,EAAQD,OAAO;EAC5C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}