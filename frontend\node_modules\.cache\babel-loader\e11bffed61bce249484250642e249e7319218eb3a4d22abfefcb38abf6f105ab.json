{"ast": null, "code": "import { ExtensionType, extensions } from \"@pixi/core\";\nconst assetKeyMap = {\n  loader: ExtensionType.LoadParser,\n  resolver: ExtensionType.ResolveParser,\n  cache: ExtensionType.CacheParser,\n  detection: ExtensionType.DetectionParser\n};\nextensions.handle(ExtensionType.Asset, extension => {\n  const ref = extension.ref;\n  Object.entries(assetKeyMap).filter(([key]) => !!ref[key]).forEach(([key, type]) => extensions.add(Object.assign(ref[key],\n  // Allow the function to optionally define it's own\n  // ExtensionMetadata, the use cases here is priority for LoaderParsers\n  {\n    extension: ref[key].extension ?? type\n  })));\n}, extension => {\n  const ref = extension.ref;\n  Object.keys(assetKeyMap).filter(key => !!ref[key]).forEach(key => extensions.remove(ref[key]));\n});", "map": {"version": 3, "names": ["assetKeyMap", "loader", "ExtensionType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resolver", "Resolve<PERSON><PERSON>er", "cache", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detection", "DetectionParser", "extensions", "handle", "<PERSON><PERSON>", "extension", "ref", "Object", "entries", "filter", "key", "for<PERSON>ach", "type", "add", "assign", "keys", "remove"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\assets\\src\\AssetExtension.ts"], "sourcesContent": ["import { extensions, ExtensionType } from '@pixi/core';\n\nimport type { <PERSON>acheParser } from './cache';\nimport type { FormatDetectionParser } from './detections';\nimport type { LoaderParser } from './loader';\nimport type { ResolveURLParser } from './resolver';\n\nconst assetKeyMap = {\n    loader: ExtensionType.LoadParser,\n    resolver: ExtensionType.ResolveParser,\n    cache: ExtensionType.CacheParser,\n    detection: ExtensionType.DetectionParser,\n};\n\ntype AssetType = keyof typeof assetKeyMap;\n\n/**\n * This developer convenience object allows developers to group\n * together the various asset parsers into a single object.\n * @memberof PIXI\n */\ninterface AssetExtension<ASSET = any, META_DATA = any>\n{\n    extension: ExtensionType.Asset,\n    loader?: Partial<LoaderParser<ASSET, META_DATA>>,\n    resolver?: Partial<ResolveURLParser>,\n    cache?: Partial<CacheParser<ASSET>>,\n    detection?: Partial<FormatDetectionParser>,\n}\n\n// Split the Asset extension into it's various parts\n// these are handled in the Assets.ts file\nextensions.handle(ExtensionType.Asset, (extension) =>\n{\n    const ref = extension.ref as AssetExtension;\n\n    Object.entries(assetKeyMap)\n        .filter(([key]) => !!ref[key as AssetType])\n        .forEach(([key, type]) => extensions.add(Object.assign(\n            ref[key as AssetType],\n            // Allow the function to optionally define it's own\n            // ExtensionMetadata, the use cases here is priority for LoaderParsers\n            { extension: ref[key as AssetType].extension ?? type },\n        )));\n}, (extension) =>\n{\n    const ref = extension.ref as AssetExtension;\n\n    Object.keys(assetKeyMap)\n        .filter((key) => !!ref[key as AssetType])\n        .forEach((key) => extensions.remove(ref[key as AssetType]));\n});\n\nexport type { AssetExtension };\n"], "mappings": ";AAOA,MAAMA,WAAA,GAAc;EAChBC,MAAA,EAAQC,aAAA,CAAcC,UAAA;EACtBC,QAAA,EAAUF,aAAA,CAAcG,aAAA;EACxBC,KAAA,EAAOJ,aAAA,CAAcK,WAAA;EACrBC,SAAA,EAAWN,aAAA,CAAcO;AAC7B;AAoBAC,UAAA,CAAWC,MAAA,CAAOT,aAAA,CAAcU,KAAA,EAAQC,SAAA,IACxC;EACI,MAAMC,GAAA,GAAMD,SAAA,CAAUC,GAAA;EAEfC,MAAA,CAAAC,OAAA,CAAQhB,WAAW,EACrBiB,MAAA,CAAO,CAAC,CAACC,GAAG,MAAM,CAAC,CAACJ,GAAA,CAAII,GAAgB,CAAC,EACzCC,OAAA,CAAQ,CAAC,CAACD,GAAA,EAAKE,IAAI,MAAMV,UAAA,CAAWW,GAAA,CAAIN,MAAA,CAAOO,MAAA,CAC5CR,GAAA,CAAII,GAAgB;EAAA;EAAA;EAGpB;IAAEL,SAAA,EAAWC,GAAA,CAAII,GAAgB,EAAEL,SAAA,IAAaO;EAAK,CACxD,EAAC;AACV,GAAIP,SAAA,IACJ;EACI,MAAMC,GAAA,GAAMD,SAAA,CAAUC,GAAA;EAEfC,MAAA,CAAAQ,IAAA,CAAKvB,WAAW,EAClBiB,MAAA,CAAQC,GAAA,IAAQ,CAAC,CAACJ,GAAA,CAAII,GAAgB,CAAC,EACvCC,OAAA,CAASD,GAAA,IAAQR,UAAA,CAAWc,MAAA,CAAOV,GAAA,CAAII,GAAgB,CAAC,CAAC;AAClE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}