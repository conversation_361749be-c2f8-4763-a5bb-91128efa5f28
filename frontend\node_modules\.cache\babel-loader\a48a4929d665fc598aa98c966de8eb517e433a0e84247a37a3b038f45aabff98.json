{"ast": null, "code": "let unsafeEval;\nfunction unsafeEvalSupported() {\n  if (typeof unsafeEval == \"boolean\") return unsafeEval;\n  try {\n    unsafeEval = new Function(\"param1\", \"param2\", \"param3\", \"return param1[param2] === param3;\")({\n      a: \"b\"\n    }, \"a\", \"b\") === !0;\n  } catch {\n    unsafeEval = !1;\n  }\n  return unsafeEval;\n}\nexport { unsafeEvalSupported };", "map": {"version": 3, "names": ["unsafeEval", "unsafeEvalSupported", "Function", "a"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\shader\\utils\\unsafeEvalSupported.ts"], "sourcesContent": ["// Cache the result to prevent running this over and over\nlet unsafeEval: boolean;\n\n/**\n * Not all platforms allow to generate function code (e.g., `new Function`).\n * this provides the platform-level detection.\n * @private\n * @returns {boolean} `true` if `new Function` is supported.\n */\nexport function unsafeEvalSupported(): boolean\n{\n    if (typeof unsafeEval === 'boolean')\n    {\n        return unsafeEval;\n    }\n\n    try\n    {\n        /* eslint-disable no-new-func */\n        const func = new Function('param1', 'param2', 'param3', 'return param1[param2] === param3;');\n        /* eslint-enable no-new-func */\n\n        unsafeEval = func({ a: 'b' }, 'a', 'b') === true;\n    }\n    catch (e)\n    {\n        unsafeEval = false;\n    }\n\n    return unsafeEval;\n}\n"], "mappings": "AACA,IAAIA,UAAA;AAQG,SAASC,oBAAA,EAChB;EACI,IAAI,OAAOD,UAAA,IAAe,WAEf,OAAAA,UAAA;EAIX;IAKIA,UAAA,GAHa,IAAIE,QAAA,CAAS,UAAU,UAAU,UAAU,mCAAmC,EAGzE;MAAEC,CAAA,EAAG;IAAA,GAAO,KAAK,GAAG,MAAM;EAAA,QAGhD;IACiBH,UAAA;EACjB;EAEO,OAAAA,UAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}