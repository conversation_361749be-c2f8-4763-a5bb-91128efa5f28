{"ast": null, "code": "import { utils } from \"@pixi/core\";\nimport { DisplayObject } from \"@pixi/display\";\nimport { EventSystem } from \"./EventSystem.mjs\";\nimport { FederatedEvent } from \"./FederatedEvent.mjs\";\nfunction convertEventModeToInteractiveMode(mode) {\n  return mode === \"dynamic\" || mode === \"static\";\n}\nconst FederatedDisplayObject = {\n  /**\n   * Property-based event handler for the `click` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onclick = (event) => {\n   *  //some function here that happens on click\n   * }\n   */\n  onclick: null,\n  /**\n   * Property-based event handler for the `mousedown` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onmousedown = (event) => {\n   *  //some function here that happens on mousedown\n   * }\n   */\n  onmousedown: null,\n  /**\n   * Property-based event handler for the `mouseenter` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onmouseenter = (event) => {\n   *  //some function here that happens on mouseenter\n   * }\n   */\n  onmouseenter: null,\n  /**\n   * Property-based event handler for the `mouseleave` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onmouseleave = (event) => {\n   *  //some function here that happens on mouseleave\n   * }\n   */\n  onmouseleave: null,\n  /**\n   * Property-based event handler for the `mousemove` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onmousemove = (event) => {\n   *  //some function here that happens on mousemove\n   * }\n   */\n  onmousemove: null,\n  /**\n   * Property-based event handler for the `globalmousemove` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onglobalmousemove = (event) => {\n   *  //some function here that happens on globalmousemove\n   * }\n   */\n  onglobalmousemove: null,\n  /**\n   * Property-based event handler for the `mouseout` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onmouseout = (event) => {\n   *  //some function here that happens on mouseout\n   * }\n   */\n  onmouseout: null,\n  /**\n   * Property-based event handler for the `mouseover` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onmouseover = (event) => {\n   *  //some function here that happens on mouseover\n   * }\n   */\n  onmouseover: null,\n  /**\n   * Property-based event handler for the `mouseup` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onmouseup = (event) => {\n   *  //some function here that happens on mouseup\n   * }\n   */\n  onmouseup: null,\n  /**\n   * Property-based event handler for the `mouseupoutside` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onmouseupoutside = (event) => {\n   *  //some function here that happens on mouseupoutside\n   * }\n   */\n  onmouseupoutside: null,\n  /**\n   * Property-based event handler for the `pointercancel` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onpointercancel = (event) => {\n   *  //some function here that happens on pointercancel\n   * }\n   */\n  onpointercancel: null,\n  /**\n   * Property-based event handler for the `pointerdown` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onpointerdown = (event) => {\n   *  //some function here that happens on pointerdown\n   * }\n   */\n  onpointerdown: null,\n  /**\n   * Property-based event handler for the `pointerenter` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onpointerenter = (event) => {\n   *  //some function here that happens on pointerenter\n   * }\n   */\n  onpointerenter: null,\n  /**\n   * Property-based event handler for the `pointerleave` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onpointerleave = (event) => {\n   *  //some function here that happens on pointerleave\n   * }\n   */\n  onpointerleave: null,\n  /**\n   * Property-based event handler for the `pointermove` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onpointermove = (event) => {\n   *  //some function here that happens on pointermove\n   * }\n   */\n  onpointermove: null,\n  /**\n   * Property-based event handler for the `globalpointermove` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onglobalpointermove = (event) => {\n   *  //some function here that happens on globalpointermove\n   * }\n   */\n  onglobalpointermove: null,\n  /**\n   * Property-based event handler for the `pointerout` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onpointerout = (event) => {\n   *  //some function here that happens on pointerout\n   * }\n   */\n  onpointerout: null,\n  /**\n   * Property-based event handler for the `pointerover` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onpointerover = (event) => {\n   *  //some function here that happens on pointerover\n   * }\n   */\n  onpointerover: null,\n  /**\n   * Property-based event handler for the `pointertap` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onpointertap = (event) => {\n   *  //some function here that happens on pointertap\n   * }\n   */\n  onpointertap: null,\n  /**\n   * Property-based event handler for the `pointerup` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onpointerup = (event) => {\n   *  //some function here that happens on pointerup\n   * }\n   */\n  onpointerup: null,\n  /**\n   * Property-based event handler for the `pointerupoutside` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onpointerupoutside = (event) => {\n   *  //some function here that happens on pointerupoutside\n   * }\n   */\n  onpointerupoutside: null,\n  /**\n   * Property-based event handler for the `rightclick` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onrightclick = (event) => {\n   *  //some function here that happens on rightclick\n   * }\n   */\n  onrightclick: null,\n  /**\n   * Property-based event handler for the `rightdown` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onrightdown = (event) => {\n   *  //some function here that happens on rightdown\n   * }\n   */\n  onrightdown: null,\n  /**\n   * Property-based event handler for the `rightup` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onrightup = (event) => {\n   *  //some function here that happens on rightup\n   * }\n   */\n  onrightup: null,\n  /**\n   * Property-based event handler for the `rightupoutside` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onrightupoutside = (event) => {\n   *  //some function here that happens on rightupoutside\n   * }\n   */\n  onrightupoutside: null,\n  /**\n   * Property-based event handler for the `tap` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.ontap = (event) => {\n   *  //some function here that happens on tap\n   * }\n   */\n  ontap: null,\n  /**\n   * Property-based event handler for the `touchcancel` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.ontouchcancel = (event) => {\n   *  //some function here that happens on touchcancel\n   * }\n   */\n  ontouchcancel: null,\n  /**\n   * Property-based event handler for the `touchend` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.ontouchend = (event) => {\n   *  //some function here that happens on touchend\n   * }\n   */\n  ontouchend: null,\n  /**\n   * Property-based event handler for the `touchendoutside` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.ontouchendoutside = (event) => {\n   *  //some function here that happens on touchendoutside\n   * }\n   */\n  ontouchendoutside: null,\n  /**\n   * Property-based event handler for the `touchmove` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.ontouchmove = (event) => {\n   *  //some function here that happens on touchmove\n   * }\n   */\n  ontouchmove: null,\n  /**\n   * Property-based event handler for the `globaltouchmove` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onglobaltouchmove = (event) => {\n   *  //some function here that happens on globaltouchmove\n   * }\n   */\n  onglobaltouchmove: null,\n  /**\n   * Property-based event handler for the `touchstart` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.ontouchstart = (event) => {\n   *  //some function here that happens on touchstart\n   * }\n   */\n  ontouchstart: null,\n  /**\n   * Property-based event handler for the `wheel` event.\n   * @memberof PIXI.DisplayObject#\n   * @default null\n   * @example\n   * this.onwheel = (event) => {\n   *  //some function here that happens on wheel\n   * }\n   */\n  onwheel: null,\n  /**\n   * @ignore\n   */\n  _internalInteractive: void 0,\n  /**\n   * Enable interaction events for the DisplayObject. Touch, pointer and mouse\n   * @memberof PIXI.DisplayObject#\n   */\n  get interactive() {\n    return this._internalInteractive ?? convertEventModeToInteractiveMode(EventSystem.defaultEventMode);\n  },\n  set interactive(value) {\n    utils.deprecation(\"7.2.0\",\n    // eslint-disable-next-line max-len\n    \"Setting interactive is deprecated, use eventMode = 'none'/'passive'/'auto'/'static'/'dynamic' instead.\"), this._internalInteractive = value, this.eventMode = value ? \"static\" : \"auto\";\n  },\n  /**\n   * @ignore\n   */\n  _internalEventMode: void 0,\n  /**\n   * Enable interaction events for the DisplayObject. Touch, pointer and mouse.\n   * This now replaces the `interactive` property.\n   * There are 5 types of interaction settings:\n   * - `'none'`: Ignores all interaction events, even on its children.\n   * - `'passive'`: Does not emit events and ignores all hit testing on itself and non-interactive children.\n   * Interactive children will still emit events.\n   * - `'auto'`: Does not emit events but is hit tested if parent is interactive. Same as `interactive = false` in v7\n   * - `'static'`: Emit events and is hit tested. Same as `interaction = true` in v7\n   * - `'dynamic'`: Emits events and is hit tested but will also receive mock interaction events fired from a ticker to\n   * allow for interaction when the mouse isn't moving\n   * @example\n   * import { Sprite } from 'pixi.js';\n   *\n   * const sprite = new Sprite(texture);\n   * sprite.eventMode = 'static';\n   * sprite.on('tap', (event) => {\n   *     // Handle event\n   * });\n   * @memberof PIXI.DisplayObject#\n   * @since 7.2.0\n   */\n  get eventMode() {\n    return this._internalEventMode ?? EventSystem.defaultEventMode;\n  },\n  set eventMode(value) {\n    this._internalInteractive = convertEventModeToInteractiveMode(value), this._internalEventMode = value;\n  },\n  /**\n   * Determines if the displayObject is interactive or not\n   * @returns {boolean} Whether the displayObject is interactive or not\n   * @memberof PIXI.DisplayObject#\n   * @since 7.2.0\n   * @example\n   * import { Sprite } from 'pixi.js';\n   * const sprite = new Sprite(texture);\n   * sprite.eventMode = 'static';\n   * sprite.isInteractive(); // true\n   *\n   * sprite.eventMode = 'dynamic';\n   * sprite.isInteractive(); // true\n   *\n   * sprite.eventMode = 'none';\n   * sprite.isInteractive(); // false\n   *\n   * sprite.eventMode = 'passive';\n   * sprite.isInteractive(); // false\n   *\n   * sprite.eventMode = 'auto';\n   * sprite.isInteractive(); // false\n   */\n  isInteractive() {\n    return this.eventMode === \"static\" || this.eventMode === \"dynamic\";\n  },\n  /**\n   * Determines if the children to the displayObject can be clicked/touched\n   * Setting this to false allows PixiJS to bypass a recursive `hitTest` function\n   * @memberof PIXI.Container#\n   */\n  interactiveChildren: !0,\n  /**\n   * Interaction shape. Children will be hit first, then this shape will be checked.\n   * Setting this will cause this shape to be checked in hit tests rather than the displayObject's bounds.\n   * @example\n   * import { Rectangle, Sprite } from 'pixi.js';\n   *\n   * const sprite = new Sprite(texture);\n   * sprite.interactive = true;\n   * sprite.hitArea = new Rectangle(0, 0, 100, 100);\n   * @member {PIXI.IHitArea}\n   * @memberof PIXI.DisplayObject#\n   */\n  hitArea: null,\n  /**\n   * Unlike `on` or `addListener` which are methods from EventEmitter, `addEventListener`\n   * seeks to be compatible with the DOM's `addEventListener` with support for options.\n   * **IMPORTANT:** _Only_ available if using the `@pixi/events` package.\n   * @memberof PIXI.DisplayObject\n   * @param type - The type of event to listen to.\n   * @param listener - The listener callback or object.\n   * @param options - Listener options, used for capture phase.\n   * @example\n   * // Tell the user whether they did a single, double, triple, or nth click.\n   * button.addEventListener('click', {\n   *     handleEvent(e): {\n   *         let prefix;\n   *\n   *         switch (e.detail) {\n   *             case 1: prefix = 'single'; break;\n   *             case 2: prefix = 'double'; break;\n   *             case 3: prefix = 'triple'; break;\n   *             default: prefix = e.detail + 'th'; break;\n   *         }\n   *\n   *         console.log('That was a ' + prefix + 'click');\n   *     }\n   * });\n   *\n   * // But skip the first click!\n   * button.parent.addEventListener('click', function blockClickOnce(e) {\n   *     e.stopImmediatePropagation();\n   *     button.parent.removeEventListener('click', blockClickOnce, true);\n   * }, {\n   *     capture: true,\n   * });\n   */\n  addEventListener(type, listener, options) {\n    const capture = typeof options == \"boolean\" && options || typeof options == \"object\" && options.capture,\n      signal = typeof options == \"object\" ? options.signal : void 0,\n      once = typeof options == \"object\" ? options.once === !0 : !1,\n      context = typeof listener == \"function\" ? void 0 : listener;\n    type = capture ? `${type}capture` : type;\n    const listenerFn = typeof listener == \"function\" ? listener : listener.handleEvent,\n      emitter = this;\n    signal && signal.addEventListener(\"abort\", () => {\n      emitter.off(type, listenerFn, context);\n    }), once ? emitter.once(type, listenerFn, context) : emitter.on(type, listenerFn, context);\n  },\n  /**\n   * Unlike `off` or `removeListener` which are methods from EventEmitter, `removeEventListener`\n   * seeks to be compatible with the DOM's `removeEventListener` with support for options.\n   * **IMPORTANT:** _Only_ available if using the `@pixi/events` package.\n   * @memberof PIXI.DisplayObject\n   * @param type - The type of event the listener is bound to.\n   * @param listener - The listener callback or object.\n   * @param options - The original listener options. This is required to deregister a capture phase listener.\n   */\n  removeEventListener(type, listener, options) {\n    const capture = typeof options == \"boolean\" && options || typeof options == \"object\" && options.capture,\n      context = typeof listener == \"function\" ? void 0 : listener;\n    type = capture ? `${type}capture` : type, listener = typeof listener == \"function\" ? listener : listener.handleEvent, this.off(type, listener, context);\n  },\n  /**\n   * Dispatch the event on this {@link PIXI.DisplayObject} using the event's {@link PIXI.EventBoundary}.\n   *\n   * The target of the event is set to `this` and the `defaultPrevented` flag is cleared before dispatch.\n   *\n   * **IMPORTANT:** _Only_ available if using the `@pixi/events` package.\n   * @memberof PIXI.DisplayObject\n   * @param e - The event to dispatch.\n   * @returns Whether the {@link PIXI.FederatedEvent.preventDefault preventDefault}() method was not invoked.\n   * @example\n   * // Reuse a click event!\n   * button.dispatchEvent(clickEvent);\n   */\n  dispatchEvent(e) {\n    if (!(e instanceof FederatedEvent)) throw new Error(\"DisplayObject cannot propagate events outside of the Federated Events API\");\n    return e.defaultPrevented = !1, e.path = null, e.target = this, e.manager.dispatchEvent(e), !e.defaultPrevented;\n  }\n};\nDisplayObject.mixin(FederatedDisplayObject);\nexport { FederatedDisplayObject };", "map": {"version": 3, "names": ["convertEventModeToInteractiveMode", "mode", "FederatedDisplayObject", "onclick", "onmousedown", "onmouseenter", "onmouseleave", "<PERSON><PERSON><PERSON><PERSON>", "onglobalmousemove", "onmouseout", "on<PERSON><PERSON>ver", "onmouseup", "onmouseupoutside", "onpointercancel", "onpointerdown", "onpointere<PERSON>", "onpointer<PERSON>ve", "onpointermove", "onglobalpointermove", "onpointerout", "onpointerover", "onpointertap", "onpointerup", "onpointer<PERSON><PERSON>ide", "onrightclick", "onrightdown", "onrightup", "onrightupoutside", "ontap", "ontouchcancel", "ontouchend", "ontouchendoutside", "ontouchmove", "onglobaltouchmove", "ontouchstart", "onwheel", "_internalInteractive", "interactive", "EventSystem", "defaultEventMode", "value", "utils", "deprecation", "eventMode", "_internalEventMode", "isInteractive", "interactiveChildren", "hitArea", "addEventListener", "type", "listener", "options", "capture", "signal", "once", "context", "listenerFn", "handleEvent", "emitter", "off", "on", "removeEventListener", "dispatchEvent", "e", "FederatedEvent", "Error", "defaultPrevented", "path", "target", "manager", "DisplayObject", "mixin"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\events\\src\\FederatedEventTarget.ts"], "sourcesContent": ["import { utils } from '@pixi/core';\nimport { DisplayObject } from '@pixi/display';\nimport { EventSystem } from './EventSystem';\nimport { FederatedEvent } from './FederatedEvent';\n\nimport type { AllFederatedEventMap } from './FederatedEventMap';\nimport type { FederatedPointerEvent } from './FederatedPointerEvent';\nimport type { FederatedWheelEvent } from './FederatedWheelEvent';\n\nexport type Cursor = 'auto'\n| 'default'\n| 'none'\n| 'context-menu'\n| 'help'\n| 'pointer'\n| 'progress'\n| 'wait'\n| 'cell'\n| 'crosshair'\n| 'text'\n| 'vertical-text'\n| 'alias'\n| 'copy'\n| 'move'\n| 'no-drop'\n| 'not-allowed'\n| 'e-resize'\n| 'n-resize'\n| 'ne-resize'\n| 'nw-resize'\n| 's-resize'\n| 'se-resize'\n| 'sw-resize'\n| 'w-resize'\n| 'ns-resize'\n| 'ew-resize'\n| 'nesw-resize'\n| 'col-resize'\n| 'nwse-resize'\n| 'row-resize'\n| 'all-scroll'\n| 'zoom-in'\n| 'zoom-out'\n| 'grab'\n| 'grabbing';\n\n// @ignore - This is documented elsewhere.\nexport interface IHitArea\n{\n    contains(x: number, y: number): boolean;\n}\n\n/**\n * Function type for handlers, e.g., onclick\n * @memberof PIXI\n */\nexport type FederatedEventHandler<T= FederatedPointerEvent> = (event: T) => void;\n\n/**\n * The type of interaction a DisplayObject can be. For more information on values and their meaning,\n * see {@link PIXI.DisplayObject.eventMode DisplayObject's eventMode property}.\n * @memberof PIXI\n * @since 7.2.0\n */\nexport type EventMode = 'none' | 'passive' | 'auto' | 'static' | 'dynamic';\n\n/**\n * Describes the shape for a {@link PIXI.FederatedEvent}'s' `eventTarget`.\n * @memberof PIXI\n */\nexport interface FederatedEventTarget extends utils.EventEmitter, EventTarget\n{\n    /** The cursor preferred when the mouse pointer is hovering over. */\n    cursor: Cursor | string;\n\n    /** The parent of this event target. */\n    readonly parent?: FederatedEventTarget;\n\n    /** The children of this event target. */\n    readonly children?: ReadonlyArray<FederatedEventTarget>;\n\n    /** Whether this event target should fire UI events. */\n    interactive: boolean\n    _internalInteractive: boolean;\n    /** The mode of interaction for this object */\n    eventMode: EventMode;\n    _internalEventMode: EventMode;\n\n    /** Returns true if the DisplayObject has interactive 'static' or 'dynamic' */\n    isInteractive: () => boolean;\n\n    /** Whether this event target has any children that need UI events. This can be used optimize event propagation. */\n    interactiveChildren: boolean;\n\n    /** The hit-area specifies the area for which pointer events should be captured by this event target. */\n    hitArea: IHitArea | null;\n\n    // In Angular projects, zone.js is monkey patching the `EventTarget`\n    // by adding its own `removeAllListeners(event?: string): void;` method,\n    // so we have to override this signature when extending both `EventTarget` and `utils.EventEmitter`\n    // to make it compatible with Angular projects\n    // @see https://github.com/pixijs/pixijs/issues/8794\n\n    /** Remove all listeners, or those of the specified event. */\n    removeAllListeners(event?: string | symbol): this;\n\n    /** Handler for 'click' event */\n    onclick: FederatedEventHandler | null;\n    /** Handler for 'mousedown' event */\n    onmousedown: FederatedEventHandler | null;\n    /** Handler for 'mouseenter' event */\n    onmouseenter: FederatedEventHandler | null;\n    /** Handler for 'mouseleave' event */\n    onmouseleave: FederatedEventHandler | null;\n    /** Handler for 'mousemove' event */\n    onmousemove: FederatedEventHandler | null;\n    /** Handler for 'globalmousemove' event */\n    onglobalmousemove: FederatedEventHandler | null;\n    /** Handler for 'mouseout' event */\n    onmouseout: FederatedEventHandler | null;\n    /** Handler for 'mouseover' event */\n    onmouseover: FederatedEventHandler | null;\n    /** Handler for 'mouseup' event */\n    onmouseup: FederatedEventHandler | null;\n    /** Handler for 'mouseupoutside' event */\n    onmouseupoutside: FederatedEventHandler | null;\n    /** Handler for 'pointercancel' event */\n    onpointercancel: FederatedEventHandler | null;\n    /** Handler for 'pointerdown' event */\n    onpointerdown: FederatedEventHandler | null;\n    /** Handler for 'pointerenter' event */\n    onpointerenter: FederatedEventHandler | null;\n    /** Handler for 'pointerleave' event */\n    onpointerleave: FederatedEventHandler | null;\n    /** Handler for 'pointermove' event */\n    onpointermove: FederatedEventHandler | null;\n    /** Handler for 'globalpointermove' event */\n    onglobalpointermove: FederatedEventHandler | null;\n    /** Handler for 'pointerout' event */\n    onpointerout: FederatedEventHandler | null;\n    /** Handler for 'pointerover' event */\n    onpointerover: FederatedEventHandler | null;\n    /** Handler for 'pointertap' event */\n    onpointertap: FederatedEventHandler | null;\n    /** Handler for 'pointerup' event */\n    onpointerup: FederatedEventHandler | null;\n    /** Handler for 'pointerupoutside' event */\n    onpointerupoutside: FederatedEventHandler | null;\n    /** Handler for 'rightclick' event */\n    onrightclick: FederatedEventHandler | null;\n    /** Handler for 'rightdown' event */\n    onrightdown: FederatedEventHandler | null;\n    /** Handler for 'rightup' event */\n    onrightup: FederatedEventHandler | null;\n    /** Handler for 'rightupoutside' event */\n    onrightupoutside: FederatedEventHandler | null;\n    /** Handler for 'tap' event */\n    ontap: FederatedEventHandler | null;\n    /** Handler for 'touchcancel' event */\n    ontouchcancel: FederatedEventHandler | null;\n    /** Handler for 'touchend' event */\n    ontouchend: FederatedEventHandler | null;\n    /** Handler for 'touchendoutside' event */\n    ontouchendoutside: FederatedEventHandler | null;\n    /** Handler for 'touchmove' event */\n    ontouchmove: FederatedEventHandler | null;\n    /** Handler for 'globaltouchmove' event */\n    onglobaltouchmove: FederatedEventHandler | null;\n    /** Handler for 'touchstart' event */\n    ontouchstart: FederatedEventHandler | null;\n    /** Handler for 'wheel' event */\n    onwheel: FederatedEventHandler<FederatedWheelEvent> | null;\n}\n\ntype AddListenerOptions = boolean | AddEventListenerOptions;\ntype RemoveListenerOptions = boolean | EventListenerOptions;\n\nexport interface IFederatedDisplayObject\n    extends Omit<FederatedEventTarget, 'parent' | 'children' | keyof utils.EventEmitter | 'cursor'>\n{\n    addEventListener<K extends keyof AllFederatedEventMap>(\n        type: K,\n        listener: (e: AllFederatedEventMap[K]) => any,\n        options?: AddListenerOptions\n    ): void;\n    addEventListener(\n        type: string,\n        listener: EventListenerOrEventListenerObject,\n        options?: AddListenerOptions\n    ): void;\n    removeEventListener<K extends keyof AllFederatedEventMap>(\n        type: K,\n        listener: (e: AllFederatedEventMap[K]) => any,\n        options?: RemoveListenerOptions\n    ): void;\n    removeEventListener(\n        type: string,\n        listener: EventListenerOrEventListenerObject,\n        options?: RemoveListenerOptions\n    ): void;\n}\n\nfunction convertEventModeToInteractiveMode(mode: EventMode): boolean\n{\n    return mode === 'dynamic' || mode === 'static';\n}\n\nexport const FederatedDisplayObject: IFederatedDisplayObject = {\n\n    /**\n     * Property-based event handler for the `click` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onclick = (event) => {\n     *  //some function here that happens on click\n     * }\n     */\n    onclick: null,\n\n    /**\n     * Property-based event handler for the `mousedown` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onmousedown = (event) => {\n     *  //some function here that happens on mousedown\n     * }\n     */\n    onmousedown: null,\n\n    /**\n     * Property-based event handler for the `mouseenter` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onmouseenter = (event) => {\n     *  //some function here that happens on mouseenter\n     * }\n     */\n    onmouseenter: null,\n\n    /**\n     * Property-based event handler for the `mouseleave` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onmouseleave = (event) => {\n     *  //some function here that happens on mouseleave\n     * }\n     */\n    onmouseleave: null,\n\n    /**\n     * Property-based event handler for the `mousemove` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onmousemove = (event) => {\n     *  //some function here that happens on mousemove\n     * }\n     */\n    onmousemove: null,\n\n    /**\n     * Property-based event handler for the `globalmousemove` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onglobalmousemove = (event) => {\n     *  //some function here that happens on globalmousemove\n     * }\n     */\n    onglobalmousemove: null,\n\n    /**\n     * Property-based event handler for the `mouseout` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onmouseout = (event) => {\n     *  //some function here that happens on mouseout\n     * }\n     */\n    onmouseout: null,\n\n    /**\n     * Property-based event handler for the `mouseover` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onmouseover = (event) => {\n     *  //some function here that happens on mouseover\n     * }\n     */\n    onmouseover:  null,\n\n    /**\n     * Property-based event handler for the `mouseup` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onmouseup = (event) => {\n     *  //some function here that happens on mouseup\n     * }\n     */\n    onmouseup:  null,\n\n    /**\n     * Property-based event handler for the `mouseupoutside` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onmouseupoutside = (event) => {\n     *  //some function here that happens on mouseupoutside\n     * }\n     */\n    onmouseupoutside:  null,\n\n    /**\n     * Property-based event handler for the `pointercancel` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onpointercancel = (event) => {\n     *  //some function here that happens on pointercancel\n     * }\n     */\n    onpointercancel:  null,\n\n    /**\n     * Property-based event handler for the `pointerdown` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onpointerdown = (event) => {\n     *  //some function here that happens on pointerdown\n     * }\n     */\n    onpointerdown:  null,\n\n    /**\n     * Property-based event handler for the `pointerenter` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onpointerenter = (event) => {\n     *  //some function here that happens on pointerenter\n     * }\n     */\n    onpointerenter:  null,\n\n    /**\n     * Property-based event handler for the `pointerleave` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onpointerleave = (event) => {\n     *  //some function here that happens on pointerleave\n     * }\n     */\n    onpointerleave:  null,\n\n    /**\n     * Property-based event handler for the `pointermove` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onpointermove = (event) => {\n     *  //some function here that happens on pointermove\n     * }\n     */\n    onpointermove:  null,\n\n    /**\n     * Property-based event handler for the `globalpointermove` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onglobalpointermove = (event) => {\n     *  //some function here that happens on globalpointermove\n     * }\n     */\n    onglobalpointermove:  null,\n\n    /**\n     * Property-based event handler for the `pointerout` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onpointerout = (event) => {\n     *  //some function here that happens on pointerout\n     * }\n     */\n    onpointerout:  null,\n\n    /**\n     * Property-based event handler for the `pointerover` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onpointerover = (event) => {\n     *  //some function here that happens on pointerover\n     * }\n     */\n    onpointerover:  null,\n\n    /**\n     * Property-based event handler for the `pointertap` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onpointertap = (event) => {\n     *  //some function here that happens on pointertap\n     * }\n     */\n    onpointertap:  null,\n\n    /**\n     * Property-based event handler for the `pointerup` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onpointerup = (event) => {\n     *  //some function here that happens on pointerup\n     * }\n     */\n    onpointerup:  null,\n\n    /**\n     * Property-based event handler for the `pointerupoutside` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onpointerupoutside = (event) => {\n     *  //some function here that happens on pointerupoutside\n     * }\n     */\n    onpointerupoutside:  null,\n\n    /**\n     * Property-based event handler for the `rightclick` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onrightclick = (event) => {\n     *  //some function here that happens on rightclick\n     * }\n     */\n    onrightclick:  null,\n\n    /**\n     * Property-based event handler for the `rightdown` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onrightdown = (event) => {\n     *  //some function here that happens on rightdown\n     * }\n     */\n    onrightdown:  null,\n\n    /**\n     * Property-based event handler for the `rightup` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onrightup = (event) => {\n     *  //some function here that happens on rightup\n     * }\n     */\n    onrightup:  null,\n\n    /**\n     * Property-based event handler for the `rightupoutside` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onrightupoutside = (event) => {\n     *  //some function here that happens on rightupoutside\n     * }\n     */\n    onrightupoutside:  null,\n\n    /**\n     * Property-based event handler for the `tap` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.ontap = (event) => {\n     *  //some function here that happens on tap\n     * }\n     */\n    ontap:  null,\n\n    /**\n     * Property-based event handler for the `touchcancel` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.ontouchcancel = (event) => {\n     *  //some function here that happens on touchcancel\n     * }\n     */\n    ontouchcancel:  null,\n\n    /**\n     * Property-based event handler for the `touchend` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.ontouchend = (event) => {\n     *  //some function here that happens on touchend\n     * }\n     */\n    ontouchend:  null,\n\n    /**\n     * Property-based event handler for the `touchendoutside` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.ontouchendoutside = (event) => {\n     *  //some function here that happens on touchendoutside\n     * }\n     */\n    ontouchendoutside:  null,\n\n    /**\n     * Property-based event handler for the `touchmove` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.ontouchmove = (event) => {\n     *  //some function here that happens on touchmove\n     * }\n     */\n    ontouchmove:  null,\n\n    /**\n     * Property-based event handler for the `globaltouchmove` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onglobaltouchmove = (event) => {\n     *  //some function here that happens on globaltouchmove\n     * }\n     */\n    onglobaltouchmove:  null,\n\n    /**\n     * Property-based event handler for the `touchstart` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.ontouchstart = (event) => {\n     *  //some function here that happens on touchstart\n     * }\n     */\n    ontouchstart:  null,\n\n    /**\n     * Property-based event handler for the `wheel` event.\n     * @memberof PIXI.DisplayObject#\n     * @default null\n     * @example\n     * this.onwheel = (event) => {\n     *  //some function here that happens on wheel\n     * }\n     */\n    onwheel:  null,\n    /**\n     * @ignore\n     */\n    _internalInteractive: undefined,\n    /**\n     * Enable interaction events for the DisplayObject. Touch, pointer and mouse\n     * @memberof PIXI.DisplayObject#\n     */\n    get interactive()\n    {\n        return this._internalInteractive ?? convertEventModeToInteractiveMode(EventSystem.defaultEventMode);\n    },\n    set interactive(value: boolean)\n    {\n        if (process.env.DEBUG)\n        {\n            utils.deprecation(\n                '7.2.0',\n                // eslint-disable-next-line max-len\n                `Setting interactive is deprecated, use eventMode = 'none'/'passive'/'auto'/'static'/'dynamic' instead.`\n            );\n        }\n\n        this._internalInteractive = value;\n        this.eventMode = value ? 'static' : 'auto';\n    },\n    /**\n     * @ignore\n     */\n    _internalEventMode: undefined,\n    /**\n     * Enable interaction events for the DisplayObject. Touch, pointer and mouse.\n     * This now replaces the `interactive` property.\n     * There are 5 types of interaction settings:\n     * - `'none'`: Ignores all interaction events, even on its children.\n     * - `'passive'`: Does not emit events and ignores all hit testing on itself and non-interactive children.\n     * Interactive children will still emit events.\n     * - `'auto'`: Does not emit events but is hit tested if parent is interactive. Same as `interactive = false` in v7\n     * - `'static'`: Emit events and is hit tested. Same as `interaction = true` in v7\n     * - `'dynamic'`: Emits events and is hit tested but will also receive mock interaction events fired from a ticker to\n     * allow for interaction when the mouse isn't moving\n     * @example\n     * import { Sprite } from 'pixi.js';\n     *\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     * sprite.on('tap', (event) => {\n     *     // Handle event\n     * });\n     * @memberof PIXI.DisplayObject#\n     * @since 7.2.0\n     */\n    get eventMode()\n    {\n        return this._internalEventMode ?? EventSystem.defaultEventMode;\n    },\n    set eventMode(value)\n    {\n        this._internalInteractive = convertEventModeToInteractiveMode(value);\n        this._internalEventMode = value;\n    },\n\n    /**\n     * Determines if the displayObject is interactive or not\n     * @returns {boolean} Whether the displayObject is interactive or not\n     * @memberof PIXI.DisplayObject#\n     * @since 7.2.0\n     * @example\n     * import { Sprite } from 'pixi.js';\n     * const sprite = new Sprite(texture);\n     * sprite.eventMode = 'static';\n     * sprite.isInteractive(); // true\n     *\n     * sprite.eventMode = 'dynamic';\n     * sprite.isInteractive(); // true\n     *\n     * sprite.eventMode = 'none';\n     * sprite.isInteractive(); // false\n     *\n     * sprite.eventMode = 'passive';\n     * sprite.isInteractive(); // false\n     *\n     * sprite.eventMode = 'auto';\n     * sprite.isInteractive(); // false\n     */\n    isInteractive()\n    {\n        return this.eventMode === 'static' || this.eventMode === 'dynamic';\n    },\n\n    /**\n     * Determines if the children to the displayObject can be clicked/touched\n     * Setting this to false allows PixiJS to bypass a recursive `hitTest` function\n     * @memberof PIXI.Container#\n     */\n    interactiveChildren: true,\n\n    /**\n     * Interaction shape. Children will be hit first, then this shape will be checked.\n     * Setting this will cause this shape to be checked in hit tests rather than the displayObject's bounds.\n     * @example\n     * import { Rectangle, Sprite } from 'pixi.js';\n     *\n     * const sprite = new Sprite(texture);\n     * sprite.interactive = true;\n     * sprite.hitArea = new Rectangle(0, 0, 100, 100);\n     * @member {PIXI.IHitArea}\n     * @memberof PIXI.DisplayObject#\n     */\n    hitArea: null,\n\n    /**\n     * Unlike `on` or `addListener` which are methods from EventEmitter, `addEventListener`\n     * seeks to be compatible with the DOM's `addEventListener` with support for options.\n     * **IMPORTANT:** _Only_ available if using the `@pixi/events` package.\n     * @memberof PIXI.DisplayObject\n     * @param type - The type of event to listen to.\n     * @param listener - The listener callback or object.\n     * @param options - Listener options, used for capture phase.\n     * @example\n     * // Tell the user whether they did a single, double, triple, or nth click.\n     * button.addEventListener('click', {\n     *     handleEvent(e): {\n     *         let prefix;\n     *\n     *         switch (e.detail) {\n     *             case 1: prefix = 'single'; break;\n     *             case 2: prefix = 'double'; break;\n     *             case 3: prefix = 'triple'; break;\n     *             default: prefix = e.detail + 'th'; break;\n     *         }\n     *\n     *         console.log('That was a ' + prefix + 'click');\n     *     }\n     * });\n     *\n     * // But skip the first click!\n     * button.parent.addEventListener('click', function blockClickOnce(e) {\n     *     e.stopImmediatePropagation();\n     *     button.parent.removeEventListener('click', blockClickOnce, true);\n     * }, {\n     *     capture: true,\n     * });\n     */\n    addEventListener(\n        type: string,\n        listener: EventListenerOrEventListenerObject,\n        options?: AddListenerOptions\n    )\n    {\n        const capture = (typeof options === 'boolean' && options)\n            || (typeof options === 'object' && options.capture);\n        const signal = typeof options === 'object' ? options.signal : undefined;\n        const once = typeof options === 'object' ? (options.once === true) : false;\n        const context = typeof listener === 'function' ? undefined : listener;\n\n        type = capture ? `${type}capture` : type;\n        const listenerFn = typeof listener === 'function' ? listener : listener.handleEvent;\n\n        const emitter = (this as unknown as utils.EventEmitter);\n\n        if (signal)\n        {\n            signal.addEventListener('abort', () =>\n            {\n                emitter.off(type, listenerFn, context);\n            });\n        }\n\n        if (once)\n        {\n            emitter.once(type, listenerFn, context);\n        }\n        else\n        {\n            emitter.on(type, listenerFn, context);\n        }\n    },\n\n    /**\n     * Unlike `off` or `removeListener` which are methods from EventEmitter, `removeEventListener`\n     * seeks to be compatible with the DOM's `removeEventListener` with support for options.\n     * **IMPORTANT:** _Only_ available if using the `@pixi/events` package.\n     * @memberof PIXI.DisplayObject\n     * @param type - The type of event the listener is bound to.\n     * @param listener - The listener callback or object.\n     * @param options - The original listener options. This is required to deregister a capture phase listener.\n     */\n    removeEventListener(\n        type: string,\n        listener: EventListenerOrEventListenerObject,\n        options?: RemoveListenerOptions\n    )\n    {\n        const capture = (typeof options === 'boolean' && options)\n            || (typeof options === 'object' && options.capture);\n        const context = typeof listener === 'function' ? undefined : listener;\n\n        type = capture ? `${type}capture` : type;\n        listener = typeof listener === 'function' ? listener : listener.handleEvent;\n\n        (this as unknown as utils.EventEmitter).off(type, listener, context);\n    },\n\n    /**\n     * Dispatch the event on this {@link PIXI.DisplayObject} using the event's {@link PIXI.EventBoundary}.\n     *\n     * The target of the event is set to `this` and the `defaultPrevented` flag is cleared before dispatch.\n     *\n     * **IMPORTANT:** _Only_ available if using the `@pixi/events` package.\n     * @memberof PIXI.DisplayObject\n     * @param e - The event to dispatch.\n     * @returns Whether the {@link PIXI.FederatedEvent.preventDefault preventDefault}() method was not invoked.\n     * @example\n     * // Reuse a click event!\n     * button.dispatchEvent(clickEvent);\n     */\n    dispatchEvent(e: Event): boolean\n    {\n        if (!(e instanceof FederatedEvent))\n        {\n            throw new Error('DisplayObject cannot propagate events outside of the Federated Events API');\n        }\n\n        e.defaultPrevented = false;\n        e.path = null;\n        e.target = this as unknown as FederatedEventTarget;\n        e.manager.dispatchEvent(e);\n\n        return !e.defaultPrevented;\n    }\n};\n\nDisplayObject.mixin(FederatedDisplayObject);\n"], "mappings": ";;;;AA0MA,SAASA,kCAAkCC,IAAA,EAC3C;EACW,OAAAA,IAAA,KAAS,aAAaA,IAAA,KAAS;AAC1C;AAEO,MAAMC,sBAAA,GAAkD;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAW3DC,OAAA,EAAS;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWTC,WAAA,EAAa;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWbC,YAAA,EAAc;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWdC,YAAA,EAAc;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWdC,WAAA,EAAa;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWbC,iBAAA,EAAmB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWnBC,UAAA,EAAY;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWZC,WAAA,EAAc;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWdC,SAAA,EAAY;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWZC,gBAAA,EAAmB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWnBC,eAAA,EAAkB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWlBC,aAAA,EAAgB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWhBC,cAAA,EAAiB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWjBC,cAAA,EAAiB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWjBC,aAAA,EAAgB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWhBC,mBAAA,EAAsB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWtBC,YAAA,EAAe;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWfC,aAAA,EAAgB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWhBC,YAAA,EAAe;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWfC,WAAA,EAAc;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWdC,kBAAA,EAAqB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWrBC,YAAA,EAAe;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWfC,WAAA,EAAc;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWdC,SAAA,EAAY;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWZC,gBAAA,EAAmB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWnBC,KAAA,EAAQ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWRC,aAAA,EAAgB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWhBC,UAAA,EAAa;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWbC,iBAAA,EAAoB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWpBC,WAAA,EAAc;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWdC,iBAAA,EAAoB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWpBC,YAAA,EAAe;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWfC,OAAA,EAAU;EAAA;AAAA;AAAA;EAIVC,oBAAA,EAAsB;EAAA;AAAA;AAAA;AAAA;EAKtB,IAAIC,YAAA,EACJ;IACI,OAAO,KAAKD,oBAAA,IAAwBpC,iCAAA,CAAkCsC,WAAA,CAAYC,gBAAgB;EACtG;EACA,IAAIF,YAAYG,KAAA,EAChB;IAGcC,KAAA,CAAAC,WAAA,CACF;IAAA;IAEA,2GAIR,KAAKN,oBAAA,GAAuBI,KAAA,EAC5B,KAAKG,SAAA,GAAYH,KAAA,GAAQ,WAAW;EACxC;EAAA;AAAA;AAAA;EAIAI,kBAAA,EAAoB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAuBpB,IAAID,UAAA,EACJ;IACW,YAAKC,kBAAA,IAAsBN,WAAA,CAAYC,gBAAA;EAClD;EACA,IAAII,UAAUH,KAAA,EACd;IACI,KAAKJ,oBAAA,GAAuBpC,iCAAA,CAAkCwC,KAAK,GACnE,KAAKI,kBAAA,GAAqBJ,KAAA;EAC9B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAyBAK,cAAA,EACA;IACI,OAAO,KAAKF,SAAA,KAAc,YAAY,KAAKA,SAAA,KAAc;EAC7D;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAG,mBAAA,EAAqB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAcrBC,OAAA,EAAS;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAmCTC,iBACIC,IAAA,EACAC,QAAA,EACAC,OAAA,EAEJ;IACI,MAAMC,OAAA,GAAW,OAAOD,OAAA,IAAY,aAAaA,OAAA,IACzC,OAAOA,OAAA,IAAY,YAAYA,OAAA,CAAQC,OAAA;MACzCC,MAAA,GAAS,OAAOF,OAAA,IAAY,WAAWA,OAAA,CAAQE,MAAA,GAAS;MACxDC,IAAA,GAAO,OAAOH,OAAA,IAAY,WAAYA,OAAA,CAAQG,IAAA,KAAS,KAAQ;MAC/DC,OAAA,GAAU,OAAOL,QAAA,IAAa,aAAa,SAAYA,QAAA;IAEtDD,IAAA,GAAAG,OAAA,GAAU,GAAGH,IAAI,YAAYA,IAAA;IACpC,MAAMO,UAAA,GAAa,OAAON,QAAA,IAAa,aAAaA,QAAA,GAAWA,QAAA,CAASO,WAAA;MAElEC,OAAA,GAAW;IAEbL,MAAA,IAEAA,MAAA,CAAOL,gBAAA,CAAiB,SAAS,MACjC;MACYU,OAAA,CAAAC,GAAA,CAAIV,IAAA,EAAMO,UAAA,EAAYD,OAAO;IACxC,IAGDD,IAAA,GAEAI,OAAA,CAAQJ,IAAA,CAAKL,IAAA,EAAMO,UAAA,EAAYD,OAAO,IAItCG,OAAA,CAAQE,EAAA,CAAGX,IAAA,EAAMO,UAAA,EAAYD,OAAO;EAE5C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWAM,oBACIZ,IAAA,EACAC,QAAA,EACAC,OAAA,EAEJ;IACI,MAAMC,OAAA,GAAW,OAAOD,OAAA,IAAY,aAAaA,OAAA,IACzC,OAAOA,OAAA,IAAY,YAAYA,OAAA,CAAQC,OAAA;MACzCG,OAAA,GAAU,OAAOL,QAAA,IAAa,aAAa,SAAYA,QAAA;IAE7DD,IAAA,GAAOG,OAAA,GAAU,GAAGH,IAAI,YAAYA,IAAA,EACpCC,QAAA,GAAW,OAAOA,QAAA,IAAa,aAAaA,QAAA,GAAWA,QAAA,CAASO,WAAA,EAE/D,KAAuCE,GAAA,CAAIV,IAAA,EAAMC,QAAA,EAAUK,OAAO;EACvE;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAeAO,cAAcC,CAAA,EACd;IACI,IAAI,EAAEA,CAAA,YAAaC,cAAA,GAET,UAAIC,KAAA,CAAM,2EAA2E;IAG/F,OAAAF,CAAA,CAAEG,gBAAA,GAAmB,IACrBH,CAAA,CAAEI,IAAA,GAAO,MACTJ,CAAA,CAAEK,MAAA,GAAS,MACXL,CAAA,CAAEM,OAAA,CAAQP,aAAA,CAAcC,CAAC,GAElB,CAACA,CAAA,CAAEG,gBAAA;EACd;AACJ;AAEAI,aAAA,CAAcC,KAAA,CAAMrE,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}