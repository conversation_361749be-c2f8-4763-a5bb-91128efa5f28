{"ast": null, "code": "import { utils, Transform, Rectangle, RAD_TO_DEG, DEG_TO_RAD } from \"@pixi/core\";\nimport { Bounds } from \"./Bounds.mjs\";\nclass DisplayObject extends utils.EventEmitter {\n  constructor() {\n    super(), this.tempDisplayObjectParent = null, this.transform = new Transform(), this.alpha = 1, this.visible = !0, this.renderable = !0, this.cullable = !1, this.cullArea = null, this.parent = null, this.worldAlpha = 1, this._lastSortedIndex = 0, this._zIndex = 0, this.filterArea = null, this.filters = null, this._enabledFilters = null, this._bounds = new Bounds(), this._localBounds = null, this._boundsID = 0, this._boundsRect = null, this._localBoundsRect = null, this._mask = null, this._maskRefCount = 0, this._destroyed = !1, this.isSprite = !1, this.isMask = !1;\n  }\n  /**\n   * Mixes all enumerable properties and methods from a source object to DisplayObject.\n   * @param source - The source of properties and methods to mix in.\n   */\n  static mixin(source) {\n    const keys = Object.keys(source);\n    for (let i = 0; i < keys.length; ++i) {\n      const propertyName = keys[i];\n      Object.defineProperty(DisplayObject.prototype, propertyName, Object.getOwnPropertyDescriptor(source, propertyName));\n    }\n  }\n  /**\n   * Fired when this DisplayObject is added to a Container.\n   * @instance\n   * @event added\n   * @param {PIXI.Container} container - The container added to.\n   */\n  /**\n   * Fired when this DisplayObject is removed from a Container.\n   * @instance\n   * @event removed\n   * @param {PIXI.Container} container - The container removed from.\n   */\n  /**\n   * Fired when this DisplayObject is destroyed. This event is emitted once\n   * destroy is finished.\n   * @instance\n   * @event destroyed\n   */\n  /** Readonly flag for destroyed display objects. */\n  get destroyed() {\n    return this._destroyed;\n  }\n  /** Recursively updates transform of all objects from the root to this one internal function for toLocal() */\n  _recursivePostUpdateTransform() {\n    this.parent ? (this.parent._recursivePostUpdateTransform(), this.transform.updateTransform(this.parent.transform)) : this.transform.updateTransform(this._tempDisplayObjectParent.transform);\n  }\n  /** Updates the object transform for rendering. TODO - Optimization pass! */\n  updateTransform() {\n    this._boundsID++, this.transform.updateTransform(this.parent.transform), this.worldAlpha = this.alpha * this.parent.worldAlpha;\n  }\n  /**\n   * Calculates and returns the (world) bounds of the display object as a [Rectangle]{@link PIXI.Rectangle}.\n   *\n   * This method is expensive on containers with a large subtree (like the stage). This is because the bounds\n   * of a container depend on its children's bounds, which recursively causes all bounds in the subtree to\n   * be recalculated. The upside, however, is that calling `getBounds` once on a container will indeed update\n   * the bounds of all children (the whole subtree, in fact). This side effect should be exploited by using\n   * `displayObject._bounds.getRectangle()` when traversing through all the bounds in a scene graph. Otherwise,\n   * calling `getBounds` on each object in a subtree will cause the total cost to increase quadratically as\n   * its height increases.\n   *\n   * The transforms of all objects in a container's **subtree** and of all **ancestors** are updated.\n   * The world bounds of all display objects in a container's **subtree** will also be recalculated.\n   *\n   * The `_bounds` object stores the last calculation of the bounds. You can use to entirely skip bounds\n   * calculation if needed.\n   *\n   * ```js\n   * const lastCalculatedBounds = displayObject._bounds.getRectangle(optionalRect);\n   * ```\n   *\n   * Do know that usage of `getLocalBounds` can corrupt the `_bounds` of children (the whole subtree, actually). This\n   * is a known issue that has not been solved. See [getLocalBounds]{@link PIXI.DisplayObject#getLocalBounds} for more\n   * details.\n   *\n   * `getBounds` should be called with `skipUpdate` equal to `true` in a render() call. This is because the transforms\n   * are guaranteed to be update-to-date. In fact, recalculating inside a render() call may cause corruption in certain\n   * cases.\n   * @param skipUpdate - Setting to `true` will stop the transforms of the scene graph from\n   *  being updated. This means the calculation returned MAY be out of date BUT will give you a\n   *  nice performance boost.\n   * @param rect - Optional rectangle to store the result of the bounds calculation.\n   * @returns - The minimum axis-aligned rectangle in world space that fits around this object.\n   */\n  getBounds(skipUpdate, rect) {\n    return skipUpdate || (this.parent ? (this._recursivePostUpdateTransform(), this.updateTransform()) : (this.parent = this._tempDisplayObjectParent, this.updateTransform(), this.parent = null)), this._bounds.updateID !== this._boundsID && (this.calculateBounds(), this._bounds.updateID = this._boundsID), rect || (this._boundsRect || (this._boundsRect = new Rectangle()), rect = this._boundsRect), this._bounds.getRectangle(rect);\n  }\n  /**\n   * Retrieves the local bounds of the displayObject as a rectangle object.\n   * @param rect - Optional rectangle to store the result of the bounds calculation.\n   * @returns - The rectangular bounding area.\n   */\n  getLocalBounds(rect) {\n    rect || (this._localBoundsRect || (this._localBoundsRect = new Rectangle()), rect = this._localBoundsRect), this._localBounds || (this._localBounds = new Bounds());\n    const transformRef = this.transform,\n      parentRef = this.parent;\n    this.parent = null, this._tempDisplayObjectParent.worldAlpha = parentRef?.worldAlpha ?? 1, this.transform = this._tempDisplayObjectParent.transform;\n    const worldBounds = this._bounds,\n      worldBoundsID = this._boundsID;\n    this._bounds = this._localBounds;\n    const bounds = this.getBounds(!1, rect);\n    return this.parent = parentRef, this.transform = transformRef, this._bounds = worldBounds, this._bounds.updateID += this._boundsID - worldBoundsID, bounds;\n  }\n  /**\n   * Calculates the global position of the display object.\n   * @param position - The world origin to calculate from.\n   * @param point - A Point object in which to store the value, optional\n   *  (otherwise will create a new Point).\n   * @param skipUpdate - Should we skip the update transform.\n   * @returns - A point object representing the position of this object.\n   */\n  toGlobal(position, point, skipUpdate = !1) {\n    return skipUpdate || (this._recursivePostUpdateTransform(), this.parent ? this.displayObjectUpdateTransform() : (this.parent = this._tempDisplayObjectParent, this.displayObjectUpdateTransform(), this.parent = null)), this.worldTransform.apply(position, point);\n  }\n  /**\n   * Calculates the local position of the display object relative to another point.\n   * @param position - The world origin to calculate from.\n   * @param from - The DisplayObject to calculate the global position from.\n   * @param point - A Point object in which to store the value, optional\n   *  (otherwise will create a new Point).\n   * @param skipUpdate - Should we skip the update transform\n   * @returns - A point object representing the position of this object\n   */\n  toLocal(position, from, point, skipUpdate) {\n    return from && (position = from.toGlobal(position, point, skipUpdate)), skipUpdate || (this._recursivePostUpdateTransform(), this.parent ? this.displayObjectUpdateTransform() : (this.parent = this._tempDisplayObjectParent, this.displayObjectUpdateTransform(), this.parent = null)), this.worldTransform.applyInverse(position, point);\n  }\n  /**\n   * Set the parent Container of this DisplayObject.\n   * @param container - The Container to add this DisplayObject to.\n   * @returns - The Container that this DisplayObject was added to.\n   */\n  setParent(container) {\n    if (!container || !container.addChild) throw new Error(\"setParent: Argument must be a Container\");\n    return container.addChild(this), container;\n  }\n  /** Remove the DisplayObject from its parent Container. If the DisplayObject has no parent, do nothing. */\n  removeFromParent() {\n    this.parent?.removeChild(this);\n  }\n  /**\n   * Convenience function to set the position, scale, skew and pivot at once.\n   * @param x - The X position\n   * @param y - The Y position\n   * @param scaleX - The X scale value\n   * @param scaleY - The Y scale value\n   * @param rotation - The rotation\n   * @param skewX - The X skew value\n   * @param skewY - The Y skew value\n   * @param pivotX - The X pivot value\n   * @param pivotY - The Y pivot value\n   * @returns - The DisplayObject instance\n   */\n  setTransform(x = 0, y = 0, scaleX = 1, scaleY = 1, rotation = 0, skewX = 0, skewY = 0, pivotX = 0, pivotY = 0) {\n    return this.position.x = x, this.position.y = y, this.scale.x = scaleX || 1, this.scale.y = scaleY || 1, this.rotation = rotation, this.skew.x = skewX, this.skew.y = skewY, this.pivot.x = pivotX, this.pivot.y = pivotY, this;\n  }\n  /**\n   * Base destroy method for generic display objects. This will automatically\n   * remove the display object from its parent Container as well as remove\n   * all current event listeners and internal references. Do not use a DisplayObject\n   * after calling `destroy()`.\n   * @param _options\n   */\n  destroy(_options) {\n    this.removeFromParent(), this._destroyed = !0, this.transform = null, this.parent = null, this._bounds = null, this.mask = null, this.cullArea = null, this.filters = null, this.filterArea = null, this.hitArea = null, this.eventMode = \"auto\", this.interactiveChildren = !1, this.emit(\"destroyed\"), this.removeAllListeners();\n  }\n  /**\n   * @protected\n   * @member {PIXI.Container}\n   */\n  get _tempDisplayObjectParent() {\n    return this.tempDisplayObjectParent === null && (this.tempDisplayObjectParent = new TemporaryDisplayObject()), this.tempDisplayObjectParent;\n  }\n  /**\n   * Used in Renderer, cacheAsBitmap and other places where you call an `updateTransform` on root.\n   *\n   * ```js\n   * const cacheParent = elem.enableTempParent();\n   * elem.updateTransform();\n   * elem.disableTempParent(cacheParent);\n   * ```\n   * @returns - Current parent\n   */\n  enableTempParent() {\n    const myParent = this.parent;\n    return this.parent = this._tempDisplayObjectParent, myParent;\n  }\n  /**\n   * Pair method for `enableTempParent`\n   * @param cacheParent - Actual parent of element\n   */\n  disableTempParent(cacheParent) {\n    this.parent = cacheParent;\n  }\n  /**\n   * The position of the displayObject on the x axis relative to the local coordinates of the parent.\n   * An alias to position.x\n   */\n  get x() {\n    return this.position.x;\n  }\n  set x(value) {\n    this.transform.position.x = value;\n  }\n  /**\n   * The position of the displayObject on the y axis relative to the local coordinates of the parent.\n   * An alias to position.y\n   */\n  get y() {\n    return this.position.y;\n  }\n  set y(value) {\n    this.transform.position.y = value;\n  }\n  /**\n   * Current transform of the object based on world (parent) factors.\n   * @readonly\n   */\n  get worldTransform() {\n    return this.transform.worldTransform;\n  }\n  /**\n   * Current transform of the object based on local factors: position, scale, other stuff.\n   * @readonly\n   */\n  get localTransform() {\n    return this.transform.localTransform;\n  }\n  /**\n   * The coordinate of the object relative to the local coordinates of the parent.\n   * @since 4.0.0\n   */\n  get position() {\n    return this.transform.position;\n  }\n  set position(value) {\n    this.transform.position.copyFrom(value);\n  }\n  /**\n   * The scale factors of this object along the local coordinate axes.\n   *\n   * The default scale is (1, 1).\n   * @since 4.0.0\n   */\n  get scale() {\n    return this.transform.scale;\n  }\n  set scale(value) {\n    this.transform.scale.copyFrom(value);\n  }\n  /**\n   * The center of rotation, scaling, and skewing for this display object in its local space. The `position`\n   * is the projection of `pivot` in the parent's local space.\n   *\n   * By default, the pivot is the origin (0, 0).\n   * @since 4.0.0\n   */\n  get pivot() {\n    return this.transform.pivot;\n  }\n  set pivot(value) {\n    this.transform.pivot.copyFrom(value);\n  }\n  /**\n   * The skew factor for the object in radians.\n   * @since 4.0.0\n   */\n  get skew() {\n    return this.transform.skew;\n  }\n  set skew(value) {\n    this.transform.skew.copyFrom(value);\n  }\n  /**\n   * The rotation of the object in radians.\n   * 'rotation' and 'angle' have the same effect on a display object; rotation is in radians, angle is in degrees.\n   */\n  get rotation() {\n    return this.transform.rotation;\n  }\n  set rotation(value) {\n    this.transform.rotation = value;\n  }\n  /**\n   * The angle of the object in degrees.\n   * 'rotation' and 'angle' have the same effect on a display object; rotation is in radians, angle is in degrees.\n   */\n  get angle() {\n    return this.transform.rotation * RAD_TO_DEG;\n  }\n  set angle(value) {\n    this.transform.rotation = value * DEG_TO_RAD;\n  }\n  /**\n   * The zIndex of the displayObject.\n   *\n   * If a container has the sortableChildren property set to true, children will be automatically\n   * sorted by zIndex value; a higher value will mean it will be moved towards the end of the array,\n   * and thus rendered on top of other display objects within the same container.\n   * @see PIXI.Container#sortableChildren\n   */\n  get zIndex() {\n    return this._zIndex;\n  }\n  set zIndex(value) {\n    this._zIndex !== value && (this._zIndex = value, this.parent && (this.parent.sortDirty = !0));\n  }\n  /**\n   * Indicates if the object is globally visible.\n   * @readonly\n   */\n  get worldVisible() {\n    let item = this;\n    do {\n      if (!item.visible) return !1;\n      item = item.parent;\n    } while (item);\n    return !0;\n  }\n  /**\n   * Sets a mask for the displayObject. A mask is an object that limits the visibility of an\n   * object to the shape of the mask applied to it. In PixiJS a regular mask must be a\n   * {@link PIXI.Graphics} or a {@link PIXI.Sprite} object. This allows for much faster masking in canvas as it\n   * utilities shape clipping. Furthermore, a mask of an object must be in the subtree of its parent.\n   * Otherwise, `getLocalBounds` may calculate incorrect bounds, which makes the container's width and height wrong.\n   * To remove a mask, set this property to `null`.\n   *\n   * For sprite mask both alpha and red channel are used. Black mask is the same as transparent mask.\n   * @example\n   * import { Graphics, Sprite } from 'pixi.js';\n   *\n   * const graphics = new Graphics();\n   * graphics.beginFill(0xFF3300);\n   * graphics.drawRect(50, 250, 100, 100);\n   * graphics.endFill();\n   *\n   * const sprite = new Sprite(texture);\n   * sprite.mask = graphics;\n   * @todo At the moment, CanvasRenderer doesn't support Sprite as mask.\n   */\n  get mask() {\n    return this._mask;\n  }\n  set mask(value) {\n    if (this._mask !== value) {\n      if (this._mask) {\n        const maskObject = this._mask.isMaskData ? this._mask.maskObject : this._mask;\n        maskObject && (maskObject._maskRefCount--, maskObject._maskRefCount === 0 && (maskObject.renderable = !0, maskObject.isMask = !1));\n      }\n      if (this._mask = value, this._mask) {\n        const maskObject = this._mask.isMaskData ? this._mask.maskObject : this._mask;\n        maskObject && (maskObject._maskRefCount === 0 && (maskObject.renderable = !1, maskObject.isMask = !0), maskObject._maskRefCount++);\n      }\n    }\n  }\n}\nclass TemporaryDisplayObject extends DisplayObject {\n  constructor() {\n    super(...arguments), this.sortDirty = null;\n  }\n}\nDisplayObject.prototype.displayObjectUpdateTransform = DisplayObject.prototype.updateTransform;\nexport { DisplayObject, TemporaryDisplayObject };", "map": {"version": 3, "names": ["DisplayObject", "utils", "EventEmitter", "constructor", "tempDisplayObjectParent", "transform", "Transform", "alpha", "visible", "renderable", "cullable", "cull<PERSON><PERSON>", "parent", "worldAlpha", "_lastSortedIndex", "_zIndex", "filterArea", "filters", "_enabledFilters", "_bounds", "Bounds", "_localBounds", "_boundsID", "_boundsRect", "_localBoundsRect", "_mask", "_maskRefCount", "_destroyed", "isSprite", "isMask", "mixin", "source", "keys", "Object", "i", "length", "propertyName", "defineProperty", "prototype", "getOwnPropertyDescriptor", "destroyed", "_recursivePostUpdateTransform", "updateTransform", "_tempDisplayObjectParent", "getBounds", "skipUpdate", "rect", "updateID", "calculateBounds", "Rectangle", "getRectangle", "getLocalBounds", "transformRef", "parentRef", "worldBounds", "worldBoundsID", "bounds", "toGlobal", "position", "point", "displayObjectUpdateTransform", "worldTransform", "apply", "toLocal", "from", "applyInverse", "setParent", "container", "<PERSON><PERSON><PERSON><PERSON>", "Error", "removeFromParent", "<PERSON><PERSON><PERSON><PERSON>", "setTransform", "x", "y", "scaleX", "scaleY", "rotation", "skewX", "skewY", "pivotX", "pivotY", "scale", "skew", "pivot", "destroy", "_options", "mask", "hitArea", "eventMode", "interactiveChildren", "emit", "removeAllListeners", "TemporaryDisplayObject", "enableTempParent", "myParent", "disableTempParent", "cacheParent", "value", "localTransform", "copyFrom", "angle", "RAD_TO_DEG", "DEG_TO_RAD", "zIndex", "sortDirty", "worldVisible", "item", "maskObject", "isMaskData", "arguments"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\display\\src\\DisplayObject.ts"], "sourcesContent": ["import { DEG_TO_RAD, RAD_TO_DEG, Rectangle, Transform, utils } from '@pixi/core';\nimport { Bounds } from './Bounds';\n\nimport type { Filter, IPointData, MaskData, Matrix, ObservablePoint, Point, Renderer } from '@pixi/core';\nimport type { Container } from './Container';\n\nexport interface IDestroyOptions\n{\n    children?: boolean;\n    texture?: boolean;\n    baseTexture?: boolean;\n}\n\nexport interface DisplayObjectEvents extends GlobalMixins.DisplayObjectEvents\n{\n    added: [container: Container];\n    childAdded: [child: DisplayObject, container: Container, index: number];\n    childRemoved: [child: DisplayObject, container: Container, index: number];\n    destroyed: [];\n    removed: [container: Container];\n}\n\nexport interface DisplayObject\n    extends Omit<GlobalMixins.DisplayObject, keyof utils.EventEmitter<DisplayObjectEvents>>,\n    utils.EventEmitter<DisplayObjectEvents> {}\n\n/**\n * The base class for all objects that are rendered on the screen.\n *\n * This is an abstract class and can not be used on its own; rather it should be extended.\n *\n * ## Display objects implemented in PixiJS\n *\n * | Display Object                  | Description                                                           |\n * | ------------------------------- | --------------------------------------------------------------------- |\n * | {@link PIXI.Container}          | Adds support for `children` to DisplayObject                          |\n * | {@link PIXI.Graphics}           | Shape-drawing display object similar to the Canvas API                |\n * | {@link PIXI.Sprite}             | Draws textures (i.e. images)                                          |\n * | {@link PIXI.Text}               | Draws text using the Canvas API internally                            |\n * | {@link PIXI.BitmapText}         | More scaleable solution for text rendering, reusing glyph textures    |\n * | {@link PIXI.TilingSprite}       | Draws textures/images in a tiled fashion                              |\n * | {@link PIXI.AnimatedSprite}     | Draws an animation of multiple images                                 |\n * | {@link PIXI.Mesh}               | Provides a lower-level API for drawing meshes with custom data        |\n * | {@link PIXI.NineSlicePlane}     | Mesh-related                                                          |\n * | {@link PIXI.SimpleMesh}         | v4-compatible mesh                                                    |\n * | {@link PIXI.SimplePlane}        | Mesh-related                                                          |\n * | {@link PIXI.SimpleRope}         | Mesh-related                                                          |\n *\n * ## Transforms\n *\n * The [transform]{@link PIXI.DisplayObject#transform} of a display object describes the projection from its\n * local coordinate space to its parent's local coordinate space. The following properties are derived\n * from the transform:\n *\n * <table>\n *   <thead>\n *     <tr>\n *       <th>Property</th>\n *       <th>Description</th>\n *     </tr>\n *   </thead>\n *   <tbody>\n *     <tr>\n *       <td>[pivot]{@link PIXI.DisplayObject#pivot}</td>\n *       <td>\n *         Invariant under rotation, scaling, and skewing. The projection of into the parent's space of the pivot\n *         is equal to position, regardless of the other three transformations. In other words, It is the center of\n *         rotation, scaling, and skewing.\n *       </td>\n *     </tr>\n *     <tr>\n *       <td>[position]{@link PIXI.DisplayObject#position}</td>\n *       <td>\n *         Translation. This is the position of the [pivot]{@link PIXI.DisplayObject#pivot} in the parent's local\n *         space. The default value of the pivot is the origin (0,0). If the top-left corner of your display object\n *         is (0,0) in its local space, then the position will be its top-left corner in the parent's local space.\n *       </td>\n *     </tr>\n *     <tr>\n *       <td>[scale]{@link PIXI.DisplayObject#scale}</td>\n *       <td>\n *         Scaling. This will stretch (or compress) the display object's projection. The scale factors are along the\n *         local coordinate axes. In other words, the display object is scaled before rotated or skewed. The center\n *         of scaling is the [pivot]{@link PIXI.DisplayObject#pivot}.\n *       </td>\n *     </tr>\n *     <tr>\n *       <td>[rotation]{@link PIXI.DisplayObject#rotation}</td>\n *       <td>\n *          Rotation. This will rotate the display object's projection by this angle (in radians).\n *       </td>\n *     </tr>\n *     <tr>\n *       <td>[skew]{@link PIXI.DisplayObject#skew}</td>\n *       <td>\n *         <p>Skewing. This can be used to deform a rectangular display object into a parallelogram.</p>\n *         <p>\n *         In PixiJS, skew has a slightly different behaviour than the conventional meaning. It can be\n *         thought of the net rotation applied to the coordinate axes (separately). For example, if \"skew.x\" is\n *         ⍺ and \"skew.y\" is β, then the line x = 0 will be rotated by ⍺ (y = -x*cot⍺) and the line y = 0 will be\n *         rotated by β (y = x*tanβ). A line y = x*tanϴ (i.e. a line at angle ϴ to the x-axis in local-space) will\n *         be rotated by an angle between ⍺ and β.\n *         </p>\n *         <p>\n *         It can be observed that if skew is applied equally to both axes, then it will be equivalent to applying\n *         a rotation. Indeed, if \"skew.x\" = -ϴ and \"skew.y\" = ϴ, it will produce an equivalent of \"rotation\" = ϴ.\n *         </p>\n *         <p>\n *         Another quite interesting observation is that \"skew.x\", \"skew.y\", rotation are commutative operations. Indeed,\n *         because rotation is essentially a careful combination of the two.\n *         </p>\n *       </td>\n *     </tr>\n *     <tr>\n *       <td>angle</td>\n *       <td>Rotation. This is an alias for [rotation]{@link PIXI.DisplayObject#rotation}, but in degrees.</td>\n *     </tr>\n *     <tr>\n *       <td>x</td>\n *       <td>Translation. This is an alias for position.x!</td>\n *     </tr>\n *     <tr>\n *       <td>y</td>\n *       <td>Translation. This is an alias for position.y!</td>\n *     </tr>\n *     <tr>\n *       <td>width</td>\n *       <td>\n *         Implemented in [Container]{@link PIXI.Container}. Scaling. The width property calculates scale.x by dividing\n *         the \"requested\" width by the local bounding box width. It is indirectly an abstraction over scale.x, and there\n *         is no concept of user-defined width.\n *       </td>\n *     </tr>\n *     <tr>\n *       <td>height</td>\n *       <td>\n *         Implemented in [Container]{@link PIXI.Container}. Scaling. The height property calculates scale.y by dividing\n *         the \"requested\" height by the local bounding box height. It is indirectly an abstraction over scale.y, and there\n *         is no concept of user-defined height.\n *       </td>\n *     </tr>\n *   </tbody>\n * </table>\n *\n * ## Bounds\n *\n * The bounds of a display object is defined by the minimum axis-aligned rectangle in world space that can fit\n * around it. The abstract `calculateBounds` method is responsible for providing it (and it should use the\n * `worldTransform` to calculate in world space).\n *\n * There are a few additional types of bounding boxes:\n *\n * | Bounds                | Description                                                                              |\n * | --------------------- | ---------------------------------------------------------------------------------------- |\n * | World Bounds          | This is synonymous is the regular bounds described above. See `getBounds()`.             |\n * | Local Bounds          | This the axis-aligned bounding box in the parent's local space. See `getLocalBounds()`.  |\n * | Render Bounds         | The bounds, but including extra rendering effects like filter padding.                   |\n * | Projected Bounds      | The bounds of the projected display object onto the screen. Usually equals world bounds. |\n * | Relative Bounds       | The bounds of a display object when projected onto a ancestor's (or parent's) space.     |\n * | Natural Bounds        | The bounds of an object in its own local space (not parent's space, like in local bounds)|\n * | Content Bounds        | The natural bounds when excluding all children of a `Container`.                         |\n *\n * ### calculateBounds\n *\n * [Container]{@link PIXI.Container} already implements `calculateBounds` in a manner that includes children.\n *\n * But for a non-Container display object, the `calculateBounds` method must be overridden in order for `getBounds` and\n * `getLocalBounds` to work. This method must write the bounds into `this._bounds`.\n *\n * Generally, the following technique works for most simple cases: take the list of points\n * forming the \"hull\" of the object (i.e. outline of the object's shape), and then add them\n * using {@link PIXI.Bounds#addPointMatrix}.\n *\n * ```js\n * calculateBounds()\n * {\n *     const points = [...];\n *\n *     for (let i = 0, j = points.length; i < j; i++)\n *     {\n *         this._bounds.addPointMatrix(this.worldTransform, points[i]);\n *     }\n * }\n * ```\n *\n * You can optimize this for a large number of points by using {@link PIXI.Bounds#addVerticesMatrix} to pass them\n * in one array together.\n *\n * ## Alpha\n *\n * This alpha sets a display object's **relative opacity** w.r.t its parent. For example, if the alpha of a display\n * object is 0.5 and its parent's alpha is 0.5, then it will be rendered with 25% opacity (assuming alpha is not\n * applied on any ancestor further up the chain).\n *\n * The alpha with which the display object will be rendered is called the [worldAlpha]{@link PIXI.DisplayObject#worldAlpha}.\n *\n * ## Renderable vs Visible\n *\n * The `renderable` and `visible` properties can be used to prevent a display object from being rendered to the\n * screen. However, there is a subtle difference between the two. When using `renderable`, the transforms  of the display\n * object (and its children subtree) will continue to be calculated. When using `visible`, the transforms will not\n * be calculated.\n *\n * For culling purposes, it is recommended that applications use the [cullable]{@link PIXI.DisplayObject#cullable} property.\n *\n * Otherwise, to prevent an object from rendering in the general-purpose sense - `visible` is the property to use. This\n * one is also better in terms of performance.\n * @memberof PIXI\n */\nexport abstract class DisplayObject extends utils.EventEmitter<DisplayObjectEvents>\n{\n    abstract sortDirty: boolean;\n\n    /** The display object container that contains this display object. */\n    public parent: Container;\n\n    /**\n     * The multiplied alpha of the displayObject.\n     * @readonly\n     */\n    public worldAlpha: number;\n\n    /**\n     * World transform and local transform of this object.\n     * This will become read-only later, please do not assign anything there unless you know what are you doing.\n     */\n    public transform: Transform;\n\n    /** The opacity of the object. */\n    public alpha: number;\n\n    /**\n     * The visibility of the object. If false the object will not be drawn, and\n     * the updateTransform function will not be called.\n     *\n     * Only affects recursive calls from parent. You can ask for bounds or call updateTransform manually.\n     */\n    public visible: boolean;\n\n    /**\n     * Can this object be rendered, if false the object will not be drawn but the updateTransform\n     * methods will still be called.\n     *\n     * Only affects recursive calls from parent. You can ask for bounds manually.\n     */\n    public renderable: boolean;\n\n    /**\n     * Should this object be rendered if the bounds of this object are out of frame?\n     *\n     * Culling has no effect on whether updateTransform is called.\n     */\n    public cullable: boolean;\n\n    /**\n     * If set, this shape is used for culling instead of the bounds of this object.\n     * It can improve the culling performance of objects with many children.\n     * The culling area is defined in local space.\n     */\n    public cullArea: Rectangle;\n\n    /**\n     * The area the filter is applied to. This is used as more of an optimization\n     * rather than figuring out the dimensions of the displayObject each frame you can set this rectangle.\n     *\n     * Also works as an interaction mask.\n     */\n    public filterArea: Rectangle;\n\n    /**\n     * Sets the filters for the displayObject.\n     * IMPORTANT: This is a WebGL only feature and will be ignored by the canvas renderer.\n     * To remove filters simply set this property to `'null'`.\n     */\n    public filters: Filter[] | null;\n\n    /** Used to fast check if a sprite is.. a sprite! */\n    public isSprite: boolean;\n\n    /** Does any other displayObject use this object as a mask? */\n    public isMask: boolean;\n\n    /**\n     * Which index in the children array the display component was before the previous zIndex sort.\n     * Used by containers to help sort objects with the same zIndex, by using previous array index as the decider.\n     * @protected\n     */\n    public _lastSortedIndex: number;\n\n    /**\n     * The original, cached mask of the object.\n     * @protected\n     */\n    public _mask: Container | MaskData;\n\n    /** The bounds object, this is used to calculate and store the bounds of the displayObject. */\n    public _bounds: Bounds;\n\n    /** Local bounds object, swapped with `_bounds` when using `getLocalBounds()`. */\n    public _localBounds: Bounds;\n\n    /**\n     * The zIndex of the displayObject.\n     * A higher value will mean it will be rendered on top of other displayObjects within the same container.\n     * @protected\n     */\n    protected _zIndex: number;\n\n    /**\n     * Currently enabled filters.\n     * @protected\n     */\n    protected _enabledFilters: Filter[];\n\n    /** Flags the cached bounds as dirty. */\n    protected _boundsID: number;\n\n    /** Cache of this display-object's bounds-rectangle. */\n    protected _boundsRect: Rectangle;\n\n    /** Cache of this display-object's local-bounds rectangle. */\n    protected _localBoundsRect: Rectangle;\n\n    /** If the object has been destroyed via destroy(). If true, it should not be used. */\n    protected _destroyed: boolean;\n\n    /** The number of times this object is used as a mask by another object. */\n    private _maskRefCount: number;\n    private tempDisplayObjectParent: TemporaryDisplayObject;\n    public displayObjectUpdateTransform: () => void;\n\n    /**\n     * Mixes all enumerable properties and methods from a source object to DisplayObject.\n     * @param source - The source of properties and methods to mix in.\n     */\n    static mixin(source: utils.Dict<any>): void\n    {\n        // in ES8/ES2017, this would be really easy:\n        // Object.defineProperties(DisplayObject.prototype, Object.getOwnPropertyDescriptors(source));\n\n        // get all the enumerable property keys\n        const keys = Object.keys(source);\n\n        // loop through properties\n        for (let i = 0; i < keys.length; ++i)\n        {\n            const propertyName = keys[i];\n\n            // Set the property using the property descriptor - this works for accessors and normal value properties\n            Object.defineProperty(\n                DisplayObject.prototype,\n                propertyName,\n                Object.getOwnPropertyDescriptor(source, propertyName)\n            );\n        }\n    }\n\n    constructor()\n    {\n        super();\n\n        this.tempDisplayObjectParent = null;\n\n        // TODO: need to create Transform from factory\n        this.transform = new Transform();\n        this.alpha = 1;\n        this.visible = true;\n        this.renderable = true;\n        this.cullable = false;\n        this.cullArea = null;\n\n        this.parent = null;\n        this.worldAlpha = 1;\n\n        this._lastSortedIndex = 0;\n        this._zIndex = 0;\n\n        this.filterArea = null;\n        this.filters = null;\n        this._enabledFilters = null;\n\n        this._bounds = new Bounds();\n        this._localBounds = null;\n        this._boundsID = 0;\n        this._boundsRect = null;\n        this._localBoundsRect = null;\n        this._mask = null;\n        this._maskRefCount = 0;\n        this._destroyed = false;\n\n        this.isSprite = false;\n        this.isMask = false;\n    }\n\n    /**\n     * Fired when this DisplayObject is added to a Container.\n     * @instance\n     * @event added\n     * @param {PIXI.Container} container - The container added to.\n     */\n\n    /**\n     * Fired when this DisplayObject is removed from a Container.\n     * @instance\n     * @event removed\n     * @param {PIXI.Container} container - The container removed from.\n     */\n\n    /**\n     * Fired when this DisplayObject is destroyed. This event is emitted once\n     * destroy is finished.\n     * @instance\n     * @event destroyed\n     */\n\n    /** Readonly flag for destroyed display objects. */\n    get destroyed(): boolean\n    {\n        return this._destroyed;\n    }\n\n    /** Recalculates the bounds of the display object. */\n    abstract calculateBounds(): void;\n\n    abstract removeChild(child: DisplayObject): void;\n\n    /**\n     * Renders the object using the WebGL renderer.\n     * @param renderer - The renderer.\n     */\n    abstract render(renderer: Renderer): void;\n\n    /** Recursively updates transform of all objects from the root to this one internal function for toLocal() */\n    protected _recursivePostUpdateTransform(): void\n    {\n        if (this.parent)\n        {\n            this.parent._recursivePostUpdateTransform();\n            this.transform.updateTransform(this.parent.transform);\n        }\n        else\n        {\n            this.transform.updateTransform(this._tempDisplayObjectParent.transform);\n        }\n    }\n\n    /** Updates the object transform for rendering. TODO - Optimization pass! */\n    updateTransform(): void\n    {\n        this._boundsID++;\n\n        this.transform.updateTransform(this.parent.transform);\n        // multiply the alphas..\n        this.worldAlpha = this.alpha * this.parent.worldAlpha;\n    }\n\n    /**\n     * Calculates and returns the (world) bounds of the display object as a [Rectangle]{@link PIXI.Rectangle}.\n     *\n     * This method is expensive on containers with a large subtree (like the stage). This is because the bounds\n     * of a container depend on its children's bounds, which recursively causes all bounds in the subtree to\n     * be recalculated. The upside, however, is that calling `getBounds` once on a container will indeed update\n     * the bounds of all children (the whole subtree, in fact). This side effect should be exploited by using\n     * `displayObject._bounds.getRectangle()` when traversing through all the bounds in a scene graph. Otherwise,\n     * calling `getBounds` on each object in a subtree will cause the total cost to increase quadratically as\n     * its height increases.\n     *\n     * The transforms of all objects in a container's **subtree** and of all **ancestors** are updated.\n     * The world bounds of all display objects in a container's **subtree** will also be recalculated.\n     *\n     * The `_bounds` object stores the last calculation of the bounds. You can use to entirely skip bounds\n     * calculation if needed.\n     *\n     * ```js\n     * const lastCalculatedBounds = displayObject._bounds.getRectangle(optionalRect);\n     * ```\n     *\n     * Do know that usage of `getLocalBounds` can corrupt the `_bounds` of children (the whole subtree, actually). This\n     * is a known issue that has not been solved. See [getLocalBounds]{@link PIXI.DisplayObject#getLocalBounds} for more\n     * details.\n     *\n     * `getBounds` should be called with `skipUpdate` equal to `true` in a render() call. This is because the transforms\n     * are guaranteed to be update-to-date. In fact, recalculating inside a render() call may cause corruption in certain\n     * cases.\n     * @param skipUpdate - Setting to `true` will stop the transforms of the scene graph from\n     *  being updated. This means the calculation returned MAY be out of date BUT will give you a\n     *  nice performance boost.\n     * @param rect - Optional rectangle to store the result of the bounds calculation.\n     * @returns - The minimum axis-aligned rectangle in world space that fits around this object.\n     */\n    getBounds(skipUpdate?: boolean, rect?: Rectangle): Rectangle\n    {\n        if (!skipUpdate)\n        {\n            if (!this.parent)\n            {\n                this.parent = this._tempDisplayObjectParent as Container;\n                this.updateTransform();\n                this.parent = null;\n            }\n            else\n            {\n                this._recursivePostUpdateTransform();\n                this.updateTransform();\n            }\n        }\n\n        if (this._bounds.updateID !== this._boundsID)\n        {\n            this.calculateBounds();\n            this._bounds.updateID = this._boundsID;\n        }\n\n        if (!rect)\n        {\n            if (!this._boundsRect)\n            {\n                this._boundsRect = new Rectangle();\n            }\n\n            rect = this._boundsRect;\n        }\n\n        return this._bounds.getRectangle(rect);\n    }\n\n    /**\n     * Retrieves the local bounds of the displayObject as a rectangle object.\n     * @param rect - Optional rectangle to store the result of the bounds calculation.\n     * @returns - The rectangular bounding area.\n     */\n    getLocalBounds(rect?: Rectangle): Rectangle\n    {\n        if (!rect)\n        {\n            if (!this._localBoundsRect)\n            {\n                this._localBoundsRect = new Rectangle();\n            }\n\n            rect = this._localBoundsRect;\n        }\n\n        if (!this._localBounds)\n        {\n            this._localBounds = new Bounds();\n        }\n\n        const transformRef = this.transform;\n        const parentRef = this.parent;\n\n        this.parent = null;\n        // inherit the worldAlpha of the parent node\n        this._tempDisplayObjectParent.worldAlpha = parentRef?.worldAlpha ?? 1;\n        this.transform = this._tempDisplayObjectParent.transform;\n\n        const worldBounds = this._bounds;\n        const worldBoundsID = this._boundsID;\n\n        this._bounds = this._localBounds;\n\n        const bounds = this.getBounds(false, rect);\n\n        this.parent = parentRef;\n        this.transform = transformRef;\n\n        this._bounds = worldBounds;\n        this._bounds.updateID += this._boundsID - worldBoundsID;// reflect side-effects\n\n        return bounds;\n    }\n\n    /**\n     * Calculates the global position of the display object.\n     * @param position - The world origin to calculate from.\n     * @param point - A Point object in which to store the value, optional\n     *  (otherwise will create a new Point).\n     * @param skipUpdate - Should we skip the update transform.\n     * @returns - A point object representing the position of this object.\n     */\n    toGlobal<P extends IPointData = Point>(position: IPointData, point?: P, skipUpdate = false): P\n    {\n        if (!skipUpdate)\n        {\n            this._recursivePostUpdateTransform();\n\n            // this parent check is for just in case the item is a root object.\n            // If it is we need to give it a temporary parent so that displayObjectUpdateTransform works correctly\n            // this is mainly to avoid a parent check in the main loop. Every little helps for performance :)\n            if (!this.parent)\n            {\n                this.parent = this._tempDisplayObjectParent as Container;\n                this.displayObjectUpdateTransform();\n                this.parent = null;\n            }\n            else\n            {\n                this.displayObjectUpdateTransform();\n            }\n        }\n\n        // don't need to update the lot\n        return this.worldTransform.apply<P>(position, point);\n    }\n\n    /**\n     * Calculates the local position of the display object relative to another point.\n     * @param position - The world origin to calculate from.\n     * @param from - The DisplayObject to calculate the global position from.\n     * @param point - A Point object in which to store the value, optional\n     *  (otherwise will create a new Point).\n     * @param skipUpdate - Should we skip the update transform\n     * @returns - A point object representing the position of this object\n     */\n    toLocal<P extends IPointData = Point>(position: IPointData, from?: DisplayObject, point?: P, skipUpdate?: boolean): P\n    {\n        if (from)\n        {\n            position = from.toGlobal(position, point, skipUpdate);\n        }\n\n        if (!skipUpdate)\n        {\n            this._recursivePostUpdateTransform();\n\n            // this parent check is for just in case the item is a root object.\n            // If it is we need to give it a temporary parent so that displayObjectUpdateTransform works correctly\n            // this is mainly to avoid a parent check in the main loop. Every little helps for performance :)\n            if (!this.parent)\n            {\n                this.parent = this._tempDisplayObjectParent as Container;\n                this.displayObjectUpdateTransform();\n                this.parent = null;\n            }\n            else\n            {\n                this.displayObjectUpdateTransform();\n            }\n        }\n\n        // simply apply the matrix..\n        return this.worldTransform.applyInverse<P>(position, point);\n    }\n\n    /**\n     * Set the parent Container of this DisplayObject.\n     * @param container - The Container to add this DisplayObject to.\n     * @returns - The Container that this DisplayObject was added to.\n     */\n    setParent(container: Container): Container\n    {\n        if (!container || !container.addChild)\n        {\n            throw new Error('setParent: Argument must be a Container');\n        }\n\n        container.addChild(this);\n\n        return container;\n    }\n\n    /** Remove the DisplayObject from its parent Container. If the DisplayObject has no parent, do nothing. */\n    removeFromParent()\n    {\n        this.parent?.removeChild(this);\n    }\n\n    /**\n     * Convenience function to set the position, scale, skew and pivot at once.\n     * @param x - The X position\n     * @param y - The Y position\n     * @param scaleX - The X scale value\n     * @param scaleY - The Y scale value\n     * @param rotation - The rotation\n     * @param skewX - The X skew value\n     * @param skewY - The Y skew value\n     * @param pivotX - The X pivot value\n     * @param pivotY - The Y pivot value\n     * @returns - The DisplayObject instance\n     */\n    setTransform(x = 0, y = 0, scaleX = 1, scaleY = 1, rotation = 0, skewX = 0, skewY = 0, pivotX = 0, pivotY = 0): this\n    {\n        this.position.x = x;\n        this.position.y = y;\n        this.scale.x = !scaleX ? 1 : scaleX;\n        this.scale.y = !scaleY ? 1 : scaleY;\n        this.rotation = rotation;\n        this.skew.x = skewX;\n        this.skew.y = skewY;\n        this.pivot.x = pivotX;\n        this.pivot.y = pivotY;\n\n        return this;\n    }\n\n    /**\n     * Base destroy method for generic display objects. This will automatically\n     * remove the display object from its parent Container as well as remove\n     * all current event listeners and internal references. Do not use a DisplayObject\n     * after calling `destroy()`.\n     * @param _options\n     */\n    destroy(_options?: IDestroyOptions | boolean): void\n    {\n        this.removeFromParent();\n\n        this._destroyed = true;\n        this.transform = null;\n\n        this.parent = null;\n        this._bounds = null;\n        this.mask = null;\n\n        this.cullArea = null;\n        this.filters = null;\n        this.filterArea = null;\n        this.hitArea = null;\n\n        this.eventMode = 'auto';\n        this.interactiveChildren = false;\n\n        this.emit('destroyed');\n        this.removeAllListeners();\n    }\n\n    /**\n     * @protected\n     * @member {PIXI.Container}\n     */\n    get _tempDisplayObjectParent(): TemporaryDisplayObject\n    {\n        if (this.tempDisplayObjectParent === null)\n        {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            this.tempDisplayObjectParent = new TemporaryDisplayObject();\n        }\n\n        return this.tempDisplayObjectParent;\n    }\n\n    /**\n     * Used in Renderer, cacheAsBitmap and other places where you call an `updateTransform` on root.\n     *\n     * ```js\n     * const cacheParent = elem.enableTempParent();\n     * elem.updateTransform();\n     * elem.disableTempParent(cacheParent);\n     * ```\n     * @returns - Current parent\n     */\n    enableTempParent(): Container\n    {\n        const myParent = this.parent;\n\n        this.parent = this._tempDisplayObjectParent as Container;\n\n        return myParent;\n    }\n\n    /**\n     * Pair method for `enableTempParent`\n     * @param cacheParent - Actual parent of element\n     */\n    disableTempParent(cacheParent: Container): void\n    {\n        this.parent = cacheParent;\n    }\n\n    /**\n     * The position of the displayObject on the x axis relative to the local coordinates of the parent.\n     * An alias to position.x\n     */\n    get x(): number\n    {\n        return this.position.x;\n    }\n\n    set x(value: number)\n    {\n        this.transform.position.x = value;\n    }\n\n    /**\n     * The position of the displayObject on the y axis relative to the local coordinates of the parent.\n     * An alias to position.y\n     */\n    get y(): number\n    {\n        return this.position.y;\n    }\n\n    set y(value: number)\n    {\n        this.transform.position.y = value;\n    }\n\n    /**\n     * Current transform of the object based on world (parent) factors.\n     * @readonly\n     */\n    get worldTransform(): Matrix\n    {\n        return this.transform.worldTransform;\n    }\n\n    /**\n     * Current transform of the object based on local factors: position, scale, other stuff.\n     * @readonly\n     */\n    get localTransform(): Matrix\n    {\n        return this.transform.localTransform;\n    }\n\n    /**\n     * The coordinate of the object relative to the local coordinates of the parent.\n     * @since 4.0.0\n     */\n    get position(): ObservablePoint\n    {\n        return this.transform.position;\n    }\n\n    set position(value: IPointData)\n    {\n        this.transform.position.copyFrom(value);\n    }\n\n    /**\n     * The scale factors of this object along the local coordinate axes.\n     *\n     * The default scale is (1, 1).\n     * @since 4.0.0\n     */\n    get scale(): ObservablePoint\n    {\n        return this.transform.scale;\n    }\n\n    set scale(value: IPointData)\n    {\n        this.transform.scale.copyFrom(value);\n    }\n\n    /**\n     * The center of rotation, scaling, and skewing for this display object in its local space. The `position`\n     * is the projection of `pivot` in the parent's local space.\n     *\n     * By default, the pivot is the origin (0, 0).\n     * @since 4.0.0\n     */\n    get pivot(): ObservablePoint\n    {\n        return this.transform.pivot;\n    }\n\n    set pivot(value: IPointData)\n    {\n        this.transform.pivot.copyFrom(value);\n    }\n\n    /**\n     * The skew factor for the object in radians.\n     * @since 4.0.0\n     */\n    get skew(): ObservablePoint\n    {\n        return this.transform.skew;\n    }\n\n    set skew(value: IPointData)\n    {\n        this.transform.skew.copyFrom(value);\n    }\n\n    /**\n     * The rotation of the object in radians.\n     * 'rotation' and 'angle' have the same effect on a display object; rotation is in radians, angle is in degrees.\n     */\n    get rotation(): number\n    {\n        return this.transform.rotation;\n    }\n\n    set rotation(value: number)\n    {\n        this.transform.rotation = value;\n    }\n\n    /**\n     * The angle of the object in degrees.\n     * 'rotation' and 'angle' have the same effect on a display object; rotation is in radians, angle is in degrees.\n     */\n    get angle(): number\n    {\n        return this.transform.rotation * RAD_TO_DEG;\n    }\n\n    set angle(value: number)\n    {\n        this.transform.rotation = value * DEG_TO_RAD;\n    }\n\n    /**\n     * The zIndex of the displayObject.\n     *\n     * If a container has the sortableChildren property set to true, children will be automatically\n     * sorted by zIndex value; a higher value will mean it will be moved towards the end of the array,\n     * and thus rendered on top of other display objects within the same container.\n     * @see PIXI.Container#sortableChildren\n     */\n    get zIndex(): number\n    {\n        return this._zIndex;\n    }\n\n    set zIndex(value: number)\n    {\n        if (this._zIndex === value)\n        {\n            return;\n        }\n\n        this._zIndex = value;\n        if (this.parent)\n        {\n            this.parent.sortDirty = true;\n        }\n    }\n\n    /**\n     * Indicates if the object is globally visible.\n     * @readonly\n     */\n    get worldVisible(): boolean\n    {\n        let item = this as DisplayObject;\n\n        do\n        {\n            if (!item.visible)\n            {\n                return false;\n            }\n\n            item = item.parent;\n        } while (item);\n\n        return true;\n    }\n\n    /**\n     * Sets a mask for the displayObject. A mask is an object that limits the visibility of an\n     * object to the shape of the mask applied to it. In PixiJS a regular mask must be a\n     * {@link PIXI.Graphics} or a {@link PIXI.Sprite} object. This allows for much faster masking in canvas as it\n     * utilities shape clipping. Furthermore, a mask of an object must be in the subtree of its parent.\n     * Otherwise, `getLocalBounds` may calculate incorrect bounds, which makes the container's width and height wrong.\n     * To remove a mask, set this property to `null`.\n     *\n     * For sprite mask both alpha and red channel are used. Black mask is the same as transparent mask.\n     * @example\n     * import { Graphics, Sprite } from 'pixi.js';\n     *\n     * const graphics = new Graphics();\n     * graphics.beginFill(0xFF3300);\n     * graphics.drawRect(50, 250, 100, 100);\n     * graphics.endFill();\n     *\n     * const sprite = new Sprite(texture);\n     * sprite.mask = graphics;\n     * @todo At the moment, CanvasRenderer doesn't support Sprite as mask.\n     */\n    get mask(): Container | MaskData | null\n    {\n        return this._mask;\n    }\n\n    set mask(value: Container | MaskData | null)\n    {\n        if (this._mask === value)\n        {\n            return;\n        }\n\n        if (this._mask)\n        {\n            const maskObject = ((this._mask as MaskData).isMaskData\n                ? (this._mask as MaskData).maskObject : this._mask) as Container;\n\n            if (maskObject)\n            {\n                maskObject._maskRefCount--;\n\n                if (maskObject._maskRefCount === 0)\n                {\n                    maskObject.renderable = true;\n                    maskObject.isMask = false;\n                }\n            }\n        }\n\n        this._mask = value;\n\n        if (this._mask)\n        {\n            const maskObject = ((this._mask as MaskData).isMaskData\n                ? (this._mask as MaskData).maskObject : this._mask) as Container;\n\n            if (maskObject)\n            {\n                if (maskObject._maskRefCount === 0)\n                {\n                    maskObject.renderable = false;\n                    maskObject.isMask = true;\n                }\n\n                maskObject._maskRefCount++;\n            }\n        }\n    }\n}\n\n/**\n * @private\n */\nexport class TemporaryDisplayObject extends DisplayObject\n{\n    calculateBounds: () => null;\n    removeChild: (child: DisplayObject) => null;\n    render: (renderer: Renderer) => null;\n    sortDirty: boolean = null;\n}\n\n/**\n * DisplayObject default updateTransform, does not update children of container.\n * Will crash if there's no parent element.\n * @memberof PIXI.DisplayObject#\n * @method displayObjectUpdateTransform\n */\nDisplayObject.prototype.displayObjectUpdateTransform = DisplayObject.prototype.updateTransform;\n"], "mappings": ";;AAiNsB,MAAAA,aAAA,SAAsBC,KAAA,CAAMC,YAAA,CAClD;EAmJIC,YAAA,EACA;IACU,SAEN,KAAKC,uBAAA,GAA0B,MAG/B,KAAKC,SAAA,GAAY,IAAIC,SAAA,CAAU,GAC/B,KAAKC,KAAA,GAAQ,GACb,KAAKC,OAAA,GAAU,IACf,KAAKC,UAAA,GAAa,IAClB,KAAKC,QAAA,GAAW,IAChB,KAAKC,QAAA,GAAW,MAEhB,KAAKC,MAAA,GAAS,MACd,KAAKC,UAAA,GAAa,GAElB,KAAKC,gBAAA,GAAmB,GACxB,KAAKC,OAAA,GAAU,GAEf,KAAKC,UAAA,GAAa,MAClB,KAAKC,OAAA,GAAU,MACf,KAAKC,eAAA,GAAkB,MAEvB,KAAKC,OAAA,GAAU,IAAIC,MAAA,IACnB,KAAKC,YAAA,GAAe,MACpB,KAAKC,SAAA,GAAY,GACjB,KAAKC,WAAA,GAAc,MACnB,KAAKC,gBAAA,GAAmB,MACxB,KAAKC,KAAA,GAAQ,MACb,KAAKC,aAAA,GAAgB,GACrB,KAAKC,UAAA,GAAa,IAElB,KAAKC,QAAA,GAAW,IAChB,KAAKC,MAAA,GAAS;EAClB;EAAA;AAAA;AAAA;AAAA;EAzDA,OAAOC,MAAMC,MAAA,EACb;IAKU,MAAAC,IAAA,GAAOC,MAAA,CAAOD,IAAA,CAAKD,MAAM;IAG/B,SAASG,CAAA,GAAI,GAAGA,CAAA,GAAIF,IAAA,CAAKG,MAAA,EAAQ,EAAED,CAAA,EACnC;MACU,MAAAE,YAAA,GAAeJ,IAAA,CAAKE,CAAC;MAGpBD,MAAA,CAAAI,cAAA,CACHrC,aAAA,CAAcsC,SAAA,EACdF,YAAA,EACAH,MAAA,CAAOM,wBAAA,CAAyBR,MAAA,EAAQK,YAAY;IAE5D;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAAA;EA6DA,IAAII,UAAA,EACJ;IACI,OAAO,KAAKb,UAAA;EAChB;EAAA;EAcUc,8BAAA,EACV;IACQ,KAAK7B,MAAA,IAEL,KAAKA,MAAA,CAAO6B,6BAAA,CAA8B,GAC1C,KAAKpC,SAAA,CAAUqC,eAAA,CAAgB,KAAK9B,MAAA,CAAOP,SAAS,KAIpD,KAAKA,SAAA,CAAUqC,eAAA,CAAgB,KAAKC,wBAAA,CAAyBtC,SAAS;EAE9E;EAAA;EAGAqC,gBAAA,EACA;IACI,KAAKpB,SAAA,IAEL,KAAKjB,SAAA,CAAUqC,eAAA,CAAgB,KAAK9B,MAAA,CAAOP,SAAS,GAEpD,KAAKQ,UAAA,GAAa,KAAKN,KAAA,GAAQ,KAAKK,MAAA,CAAOC,UAAA;EAC/C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAoCA+B,UAAUC,UAAA,EAAsBC,IAAA,EAChC;IACS,OAAAD,UAAA,KAEI,KAAKjC,MAAA,IAQN,KAAK6B,6BAAA,IACL,KAAKC,eAAA,OAPL,KAAK9B,MAAA,GAAS,KAAK+B,wBAAA,EACnB,KAAKD,eAAA,IACL,KAAK9B,MAAA,GAAS,QASlB,KAAKO,OAAA,CAAQ4B,QAAA,KAAa,KAAKzB,SAAA,KAE/B,KAAK0B,eAAA,IACL,KAAK7B,OAAA,CAAQ4B,QAAA,GAAW,KAAKzB,SAAA,GAG5BwB,IAAA,KAEI,KAAKvB,WAAA,KAEN,KAAKA,WAAA,GAAc,IAAI0B,SAAA,KAG3BH,IAAA,GAAO,KAAKvB,WAAA,GAGT,KAAKJ,OAAA,CAAQ+B,YAAA,CAAaJ,IAAI;EACzC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAK,eAAeL,IAAA,EACf;IACSA,IAAA,KAEI,KAAKtB,gBAAA,KAEN,KAAKA,gBAAA,GAAmB,IAAIyB,SAAA,CAAU,IAG1CH,IAAA,GAAO,KAAKtB,gBAAA,GAGX,KAAKH,YAAA,KAEN,KAAKA,YAAA,GAAe,IAAID,MAAA,CAAO;IAGnC,MAAMgC,YAAA,GAAe,KAAK/C,SAAA;MACpBgD,SAAA,GAAY,KAAKzC,MAAA;IAElB,KAAAA,MAAA,GAAS,MAEd,KAAK+B,wBAAA,CAAyB9B,UAAA,GAAawC,SAAA,EAAWxC,UAAA,IAAc,GACpE,KAAKR,SAAA,GAAY,KAAKsC,wBAAA,CAAyBtC,SAAA;IAE/C,MAAMiD,WAAA,GAAc,KAAKnC,OAAA;MACnBoC,aAAA,GAAgB,KAAKjC,SAAA;IAE3B,KAAKH,OAAA,GAAU,KAAKE,YAAA;IAEpB,MAAMmC,MAAA,GAAS,KAAKZ,SAAA,CAAU,IAAOE,IAAI;IAEzC,YAAKlC,MAAA,GAASyC,SAAA,EACd,KAAKhD,SAAA,GAAY+C,YAAA,EAEjB,KAAKjC,OAAA,GAAUmC,WAAA,EACf,KAAKnC,OAAA,CAAQ4B,QAAA,IAAY,KAAKzB,SAAA,GAAYiC,aAAA,EAEnCC,MAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUAC,SAAuCC,QAAA,EAAsBC,KAAA,EAAWd,UAAA,GAAa,IACrF;IACS,OAAAA,UAAA,KAED,KAAKJ,6BAAA,CAA8B,GAK9B,KAAK7B,MAAA,GAQN,KAAKgD,4BAAA,CAA6B,KANlC,KAAKhD,MAAA,GAAS,KAAK+B,wBAAA,EACnB,KAAKiB,4BAAA,CAA6B,GAClC,KAAKhD,MAAA,GAAS,QASf,KAAKiD,cAAA,CAAeC,KAAA,CAASJ,QAAA,EAAUC,KAAK;EACvD;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWAI,QAAsCL,QAAA,EAAsBM,IAAA,EAAsBL,KAAA,EAAWd,UAAA,EAC7F;IACI,OAAImB,IAAA,KAEAN,QAAA,GAAWM,IAAA,CAAKP,QAAA,CAASC,QAAA,EAAUC,KAAA,EAAOd,UAAU,IAGnDA,UAAA,KAED,KAAKJ,6BAAA,CAA8B,GAK9B,KAAK7B,MAAA,GAQN,KAAKgD,4BAAA,CAA6B,KANlC,KAAKhD,MAAA,GAAS,KAAK+B,wBAAA,EACnB,KAAKiB,4BAAA,CAA6B,GAClC,KAAKhD,MAAA,GAAS,QASf,KAAKiD,cAAA,CAAeI,YAAA,CAAgBP,QAAA,EAAUC,KAAK;EAC9D;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAO,UAAUC,SAAA,EACV;IACQ,KAACA,SAAA,IAAa,CAACA,SAAA,CAAUC,QAAA,EAEnB,UAAIC,KAAA,CAAM,yCAAyC;IAGnD,OAAAF,SAAA,CAAAC,QAAA,CAAS,IAAI,GAEhBD,SAAA;EACX;EAAA;EAGAG,iBAAA,EACA;IACS,KAAA1D,MAAA,EAAQ2D,WAAA,CAAY,IAAI;EACjC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAeAC,aAAaC,CAAA,GAAI,GAAGC,CAAA,GAAI,GAAGC,MAAA,GAAS,GAAGC,MAAA,GAAS,GAAGC,QAAA,GAAW,GAAGC,KAAA,GAAQ,GAAGC,KAAA,GAAQ,GAAGC,MAAA,GAAS,GAAGC,MAAA,GAAS,GAC5G;IACI,YAAKvB,QAAA,CAASe,CAAA,GAAIA,CAAA,EAClB,KAAKf,QAAA,CAASgB,CAAA,GAAIA,CAAA,EAClB,KAAKQ,KAAA,CAAMT,CAAA,GAAKE,MAAA,IAAS,GACzB,KAAKO,KAAA,CAAMR,CAAA,GAAKE,MAAA,IAAS,GACzB,KAAKC,QAAA,GAAWA,QAAA,EAChB,KAAKM,IAAA,CAAKV,CAAA,GAAIK,KAAA,EACd,KAAKK,IAAA,CAAKT,CAAA,GAAIK,KAAA,EACd,KAAKK,KAAA,CAAMX,CAAA,GAAIO,MAAA,EACf,KAAKI,KAAA,CAAMV,CAAA,GAAIO,MAAA,EAER;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAI,QAAQC,QAAA,EACR;IACI,KAAKhB,gBAAA,IAEL,KAAK3C,UAAA,GAAa,IAClB,KAAKtB,SAAA,GAAY,MAEjB,KAAKO,MAAA,GAAS,MACd,KAAKO,OAAA,GAAU,MACf,KAAKoE,IAAA,GAAO,MAEZ,KAAK5E,QAAA,GAAW,MAChB,KAAKM,OAAA,GAAU,MACf,KAAKD,UAAA,GAAa,MAClB,KAAKwE,OAAA,GAAU,MAEf,KAAKC,SAAA,GAAY,QACjB,KAAKC,mBAAA,GAAsB,IAE3B,KAAKC,IAAA,CAAK,WAAW,GACrB,KAAKC,kBAAA;EACT;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIjD,yBAAA,EACJ;IACQ,YAAKvC,uBAAA,KAA4B,SAGjC,KAAKA,uBAAA,GAA0B,IAAIyF,sBAAA,KAGhC,KAAKzF,uBAAA;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYA0F,iBAAA,EACA;IACI,MAAMC,QAAA,GAAW,KAAKnF,MAAA;IAEjB,YAAAA,MAAA,GAAS,KAAK+B,wBAAA,EAEZoD,QAAA;EACX;EAAA;AAAA;AAAA;AAAA;EAMAC,kBAAkBC,WAAA,EAClB;IACI,KAAKrF,MAAA,GAASqF,WAAA;EAClB;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIxB,EAAA,EACJ;IACI,OAAO,KAAKf,QAAA,CAASe,CAAA;EACzB;EAEA,IAAIA,EAAEyB,KAAA,EACN;IACS,KAAA7F,SAAA,CAAUqD,QAAA,CAASe,CAAA,GAAIyB,KAAA;EAChC;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIxB,EAAA,EACJ;IACI,OAAO,KAAKhB,QAAA,CAASgB,CAAA;EACzB;EAEA,IAAIA,EAAEwB,KAAA,EACN;IACS,KAAA7F,SAAA,CAAUqD,QAAA,CAASgB,CAAA,GAAIwB,KAAA;EAChC;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIrC,eAAA,EACJ;IACI,OAAO,KAAKxD,SAAA,CAAUwD,cAAA;EAC1B;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIsC,eAAA,EACJ;IACI,OAAO,KAAK9F,SAAA,CAAU8F,cAAA;EAC1B;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIzC,SAAA,EACJ;IACI,OAAO,KAAKrD,SAAA,CAAUqD,QAAA;EAC1B;EAEA,IAAIA,SAASwC,KAAA,EACb;IACS,KAAA7F,SAAA,CAAUqD,QAAA,CAAS0C,QAAA,CAASF,KAAK;EAC1C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQA,IAAIhB,MAAA,EACJ;IACI,OAAO,KAAK7E,SAAA,CAAU6E,KAAA;EAC1B;EAEA,IAAIA,MAAMgB,KAAA,EACV;IACS,KAAA7F,SAAA,CAAU6E,KAAA,CAAMkB,QAAA,CAASF,KAAK;EACvC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASA,IAAId,MAAA,EACJ;IACI,OAAO,KAAK/E,SAAA,CAAU+E,KAAA;EAC1B;EAEA,IAAIA,MAAMc,KAAA,EACV;IACS,KAAA7F,SAAA,CAAU+E,KAAA,CAAMgB,QAAA,CAASF,KAAK;EACvC;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIf,KAAA,EACJ;IACI,OAAO,KAAK9E,SAAA,CAAU8E,IAAA;EAC1B;EAEA,IAAIA,KAAKe,KAAA,EACT;IACS,KAAA7F,SAAA,CAAU8E,IAAA,CAAKiB,QAAA,CAASF,KAAK;EACtC;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIrB,SAAA,EACJ;IACI,OAAO,KAAKxE,SAAA,CAAUwE,QAAA;EAC1B;EAEA,IAAIA,SAASqB,KAAA,EACb;IACI,KAAK7F,SAAA,CAAUwE,QAAA,GAAWqB,KAAA;EAC9B;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIG,MAAA,EACJ;IACW,YAAKhG,SAAA,CAAUwE,QAAA,GAAWyB,UAAA;EACrC;EAEA,IAAID,MAAMH,KAAA,EACV;IACS,KAAA7F,SAAA,CAAUwE,QAAA,GAAWqB,KAAA,GAAQK,UAAA;EACtC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUA,IAAIC,OAAA,EACJ;IACI,OAAO,KAAKzF,OAAA;EAChB;EAEA,IAAIyF,OAAON,KAAA,EACX;IACQ,KAAKnF,OAAA,KAAYmF,KAAA,KAKrB,KAAKnF,OAAA,GAAUmF,KAAA,EACX,KAAKtF,MAAA,KAEL,KAAKA,MAAA,CAAO6F,SAAA,GAAY;EAEhC;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIC,aAAA,EACJ;IACI,IAAIC,IAAA,GAAO;IAGX;MACI,IAAI,CAACA,IAAA,CAAKnG,OAAA,EAEC;MAGXmG,IAAA,GAAOA,IAAA,CAAK/F,MAAA;IACP,SAAA+F,IAAA;IAEF;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAuBA,IAAIpB,KAAA,EACJ;IACI,OAAO,KAAK9D,KAAA;EAChB;EAEA,IAAI8D,KAAKW,KAAA,EACT;IACQ,SAAKzE,KAAA,KAAUyE,KAAA,EAKnB;MAAA,IAAI,KAAKzE,KAAA,EACT;QACI,MAAMmF,UAAA,GAAe,KAAKnF,KAAA,CAAmBoF,UAAA,GACtC,KAAKpF,KAAA,CAAmBmF,UAAA,GAAa,KAAKnF,KAAA;QAE7CmF,UAAA,KAEAA,UAAA,CAAWlF,aAAA,IAEPkF,UAAA,CAAWlF,aAAA,KAAkB,MAE7BkF,UAAA,CAAWnG,UAAA,GAAa,IACxBmG,UAAA,CAAW/E,MAAA,GAAS;MAGhC;MAIA,IAFA,KAAKJ,KAAA,GAAQyE,KAAA,EAET,KAAKzE,KAAA,EACT;QACI,MAAMmF,UAAA,GAAe,KAAKnF,KAAA,CAAmBoF,UAAA,GACtC,KAAKpF,KAAA,CAAmBmF,UAAA,GAAa,KAAKnF,KAAA;QAE7CmF,UAAA,KAEIA,UAAA,CAAWlF,aAAA,KAAkB,MAE7BkF,UAAA,CAAWnG,UAAA,GAAa,IACxBmG,UAAA,CAAW/E,MAAA,GAAS,KAGxB+E,UAAA,CAAWlF,aAAA;MAEnB;IAAA;EACJ;AACJ;AAKO,MAAMmE,sBAAA,SAA+B7F,aAAA,CAC5C;EADOG,YAAA;IAAA,SAAA2G,SAAA,GAKkB,KAAAL,SAAA;EAAA;AACzB;AAQAzG,aAAA,CAAcsC,SAAA,CAAUsB,4BAAA,GAA+B5D,aAAA,CAAcsC,SAAA,CAAUI,eAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}