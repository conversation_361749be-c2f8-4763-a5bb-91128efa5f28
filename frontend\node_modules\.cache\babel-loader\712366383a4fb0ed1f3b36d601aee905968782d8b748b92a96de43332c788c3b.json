{"ast": null, "code": "import { Filter } from \"@pixi/core\";\nimport fragment from \"./fxaa.frag.mjs\";\nimport vertex from \"./fxaa.vert.mjs\";\nclass FXAAFilter extends Filter {\n  constructor() {\n    super(vertex, fragment);\n  }\n}\nexport { FXAAFilter };", "map": {"version": 3, "names": ["FXAAFilter", "Filter", "constructor", "vertex", "fragment"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\filter-fxaa\\src\\FXAAFilter.ts"], "sourcesContent": ["import { Filter } from '@pixi/core';\nimport fragment from './fxaa.frag';\nimport vertex from './fxaa.vert';\n\n/**\n * Basic FXAA (Fast Approximate Anti-Aliasing) implementation based on the code on geeks3d.com\n * with the modification that the texture2DLod stuff was removed since it is unsupported by WebGL.\n * @see https://github.com/mitsuhiko/webgl-meincraft\n * @memberof PIXI\n */\nexport class FXAAFilter extends Filter\n{\n    constructor()\n    {\n        // TODO - needs work\n        super(vertex, fragment);\n    }\n}\n"], "mappings": ";;;AAUO,MAAMA,UAAA,SAAmBC,MAAA,CAChC;EACIC,YAAA,EACA;IAEI,MAAMC,MAAA,EAAQC,QAAQ;EAC1B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}