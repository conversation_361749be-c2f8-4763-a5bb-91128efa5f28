{"ast": null, "code": "import { SHAPES } from \"../const.mjs\";\nclass RoundedRectangle {\n  /**\n   * @param x - The X coordinate of the upper-left corner of the rounded rectangle\n   * @param y - The Y coordinate of the upper-left corner of the rounded rectangle\n   * @param width - The overall width of this rounded rectangle\n   * @param height - The overall height of this rounded rectangle\n   * @param radius - Controls the radius of the rounded corners\n   */\n  constructor(x = 0, y = 0, width = 0, height = 0, radius = 20) {\n    this.x = x, this.y = y, this.width = width, this.height = height, this.radius = radius, this.type = SHAPES.RREC;\n  }\n  /**\n   * Creates a clone of this Rounded Rectangle.\n   * @returns - A copy of the rounded rectangle.\n   */\n  clone() {\n    return new RoundedRectangle(this.x, this.y, this.width, this.height, this.radius);\n  }\n  /**\n   * Checks whether the x and y coordinates given are contained within this Rounded Rectangle\n   * @param x - The X coordinate of the point to test.\n   * @param y - The Y coordinate of the point to test.\n   * @returns - Whether the x/y coordinates are within this Rounded Rectangle.\n   */\n  contains(x, y) {\n    if (this.width <= 0 || this.height <= 0) return !1;\n    if (x >= this.x && x <= this.x + this.width && y >= this.y && y <= this.y + this.height) {\n      const radius = Math.max(0, Math.min(this.radius, Math.min(this.width, this.height) / 2));\n      if (y >= this.y + radius && y <= this.y + this.height - radius || x >= this.x + radius && x <= this.x + this.width - radius) return !0;\n      let dx = x - (this.x + radius),\n        dy = y - (this.y + radius);\n      const radius2 = radius * radius;\n      if (dx * dx + dy * dy <= radius2 || (dx = x - (this.x + this.width - radius), dx * dx + dy * dy <= radius2) || (dy = y - (this.y + this.height - radius), dx * dx + dy * dy <= radius2) || (dx = x - (this.x + radius), dx * dx + dy * dy <= radius2)) return !0;\n    }\n    return !1;\n  }\n}\nRoundedRectangle.prototype.toString = function () {\n  return `[@pixi/math:RoundedRectangle x=${this.x} y=${this.y}width=${this.width} height=${this.height} radius=${this.radius}]`;\n};\nexport { RoundedRectangle };", "map": {"version": 3, "names": ["RoundedRectangle", "constructor", "x", "y", "width", "height", "radius", "type", "SHAPES", "RREC", "clone", "contains", "Math", "max", "min", "dx", "dy", "radius2", "prototype", "toString"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\math\\src\\shapes\\RoundedRectangle.ts"], "sourcesContent": ["import { SHAPES } from '../const';\n\n/**\n * The Rounded Rectangle object is an area that has nice rounded corners, as indicated by its\n * top-left corner point (x, y) and by its width and its height and its radius.\n * @memberof PIXI\n */\nexport class RoundedRectangle\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n\n    /** @default 0 */\n    public width: number;\n\n    /** @default 0 */\n    public height: number;\n\n    /** @default 20 */\n    public radius: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.RREC\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.RREC;\n\n    /**\n     * @param x - The X coordinate of the upper-left corner of the rounded rectangle\n     * @param y - The Y coordinate of the upper-left corner of the rounded rectangle\n     * @param width - The overall width of this rounded rectangle\n     * @param height - The overall height of this rounded rectangle\n     * @param radius - Controls the radius of the rounded corners\n     */\n    constructor(x = 0, y = 0, width = 0, height = 0, radius = 20)\n    {\n        this.x = x;\n        this.y = y;\n        this.width = width;\n        this.height = height;\n        this.radius = radius;\n        this.type = SHAPES.RREC;\n    }\n\n    /**\n     * Creates a clone of this Rounded Rectangle.\n     * @returns - A copy of the rounded rectangle.\n     */\n    clone(): RoundedRectangle\n    {\n        return new RoundedRectangle(this.x, this.y, this.width, this.height, this.radius);\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this Rounded Rectangle\n     * @param x - The X coordinate of the point to test.\n     * @param y - The Y coordinate of the point to test.\n     * @returns - Whether the x/y coordinates are within this Rounded Rectangle.\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.width <= 0 || this.height <= 0)\n        {\n            return false;\n        }\n        if (x >= this.x && x <= this.x + this.width)\n        {\n            if (y >= this.y && y <= this.y + this.height)\n            {\n                const radius = Math.max(0, Math.min(this.radius, Math.min(this.width, this.height) / 2));\n\n                if ((y >= this.y + radius && y <= this.y + this.height - radius)\n                || (x >= this.x + radius && x <= this.x + this.width - radius))\n                {\n                    return true;\n                }\n                let dx = x - (this.x + radius);\n                let dy = y - (this.y + radius);\n                const radius2 = radius * radius;\n\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n                dx = x - (this.x + this.width - radius);\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n                dy = y - (this.y + this.height - radius);\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n                dx = x - (this.x + radius);\n                if ((dx * dx) + (dy * dy) <= radius2)\n                {\n                    return true;\n                }\n            }\n        }\n\n        return false;\n    }\n}\n\nif (process.env.DEBUG)\n{\n    RoundedRectangle.prototype.toString = function toString(): string\n    {\n        return `[@pixi/math:RoundedRectangle x=${this.x} y=${this.y}`\n            + `width=${this.width} height=${this.height} radius=${this.radius}]`;\n    };\n}\n"], "mappings": ";AAOO,MAAMA,gBAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EA8BIC,YAAYC,CAAA,GAAI,GAAGC,CAAA,GAAI,GAAGC,KAAA,GAAQ,GAAGC,MAAA,GAAS,GAAGC,MAAA,GAAS,IAC1D;IACI,KAAKJ,CAAA,GAAIA,CAAA,EACT,KAAKC,CAAA,GAAIA,CAAA,EACT,KAAKC,KAAA,GAAQA,KAAA,EACb,KAAKC,MAAA,GAASA,MAAA,EACd,KAAKC,MAAA,GAASA,MAAA,EACd,KAAKC,IAAA,GAAOC,MAAA,CAAOC,IAAA;EACvB;EAAA;AAAA;AAAA;AAAA;EAMAC,MAAA,EACA;IACW,WAAIV,gBAAA,CAAiB,KAAKE,CAAA,EAAG,KAAKC,CAAA,EAAG,KAAKC,KAAA,EAAO,KAAKC,MAAA,EAAQ,KAAKC,MAAM;EACpF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAK,SAAST,CAAA,EAAWC,CAAA,EACpB;IACI,IAAI,KAAKC,KAAA,IAAS,KAAK,KAAKC,MAAA,IAAU,GAE3B;IAEX,IAAIH,CAAA,IAAK,KAAKA,CAAA,IAAKA,CAAA,IAAK,KAAKA,CAAA,GAAI,KAAKE,KAAA,IAE9BD,CAAA,IAAK,KAAKA,CAAA,IAAKA,CAAA,IAAK,KAAKA,CAAA,GAAI,KAAKE,MAAA,EACtC;MACI,MAAMC,MAAA,GAASM,IAAA,CAAKC,GAAA,CAAI,GAAGD,IAAA,CAAKE,GAAA,CAAI,KAAKR,MAAA,EAAQM,IAAA,CAAKE,GAAA,CAAI,KAAKV,KAAA,EAAO,KAAKC,MAAM,IAAI,CAAC,CAAC;MAEvF,IAAKF,CAAA,IAAK,KAAKA,CAAA,GAAIG,MAAA,IAAUH,CAAA,IAAK,KAAKA,CAAA,GAAI,KAAKE,MAAA,GAASC,MAAA,IACrDJ,CAAA,IAAK,KAAKA,CAAA,GAAII,MAAA,IAAUJ,CAAA,IAAK,KAAKA,CAAA,GAAI,KAAKE,KAAA,GAAQE,MAAA,EAE5C;MAEP,IAAAS,EAAA,GAAKb,CAAA,IAAK,KAAKA,CAAA,GAAII,MAAA;QACnBU,EAAA,GAAKb,CAAA,IAAK,KAAKA,CAAA,GAAIG,MAAA;MACvB,MAAMW,OAAA,GAAUX,MAAA,GAASA,MAAA;MAiBzB,IAfKS,EAAA,GAAKA,EAAA,GAAOC,EAAA,GAAKA,EAAA,IAAOC,OAAA,KAI7BF,EAAA,GAAKb,CAAA,IAAK,KAAKA,CAAA,GAAI,KAAKE,KAAA,GAAQE,MAAA,GAC3BS,EAAA,GAAKA,EAAA,GAAOC,EAAA,GAAKA,EAAA,IAAOC,OAAA,MAI7BD,EAAA,GAAKb,CAAA,IAAK,KAAKA,CAAA,GAAI,KAAKE,MAAA,GAASC,MAAA,GAC5BS,EAAA,GAAKA,EAAA,GAAOC,EAAA,GAAKA,EAAA,IAAOC,OAAA,MAI7BF,EAAA,GAAKb,CAAA,IAAK,KAAKA,CAAA,GAAII,MAAA,GACdS,EAAA,GAAKA,EAAA,GAAOC,EAAA,GAAKA,EAAA,IAAOC,OAAA,GAElB;IAEf;IAGG;EACX;AACJ;AAIIjB,gBAAA,CAAiBkB,SAAA,CAAUC,QAAA,GAAW,YACtC;EACI,OAAO,kCAAkC,KAAKjB,CAAC,MAAM,KAAKC,CAAC,SAC5C,KAAKC,KAAK,WAAW,KAAKC,MAAM,WAAW,KAAKC,MAAM;AACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}