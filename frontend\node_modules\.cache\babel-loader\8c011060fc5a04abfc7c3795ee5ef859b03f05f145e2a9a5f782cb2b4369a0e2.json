{"ast": null, "code": "import { utils, ExtensionType, extensions } from \"@pixi/core\";\nimport { DisplayObject } from \"@pixi/display\";\nimport { FederatedEvent } from \"@pixi/events\";\nimport { accessibleTarget } from \"./accessibleTarget.mjs\";\nDisplayObject.mixin(accessibleTarget);\nconst KEY_CODE_TAB = 9,\n  DIV_TOUCH_SIZE = 100,\n  DIV_TOUCH_POS_X = 0,\n  DIV_TOUCH_POS_Y = 0,\n  DIV_TOUCH_ZINDEX = 2,\n  DIV_HOOK_SIZE = 1,\n  DIV_HOOK_POS_X = -1e3,\n  DIV_HOOK_POS_Y = -1e3,\n  DIV_HOOK_ZINDEX = 2;\nclass AccessibilityManager {\n  // 2fps\n  /**\n   * @param {PIXI.CanvasRenderer|PIXI.Renderer} renderer - A reference to the current renderer\n   */\n  constructor(renderer) {\n    this.debug = !1, this._isActive = !1, this._isMobileAccessibility = !1, this.pool = [], this.renderId = 0, this.children = [], this.androidUpdateCount = 0, this.androidUpdateFrequency = 500, this._hookDiv = null, (utils.isMobile.tablet || utils.isMobile.phone) && this.createTouchHook();\n    const div = document.createElement(\"div\");\n    div.style.width = `${DIV_TOUCH_SIZE}px`, div.style.height = `${DIV_TOUCH_SIZE}px`, div.style.position = \"absolute\", div.style.top = `${DIV_TOUCH_POS_X}px`, div.style.left = `${DIV_TOUCH_POS_Y}px`, div.style.zIndex = DIV_TOUCH_ZINDEX.toString(), this.div = div, this.renderer = renderer, this._onKeyDown = this._onKeyDown.bind(this), this._onMouseMove = this._onMouseMove.bind(this), globalThis.addEventListener(\"keydown\", this._onKeyDown, !1);\n  }\n  /**\n   * Value of `true` if accessibility is currently active and accessibility layers are showing.\n   * @member {boolean}\n   * @readonly\n   */\n  get isActive() {\n    return this._isActive;\n  }\n  /**\n   * Value of `true` if accessibility is enabled for touch devices.\n   * @member {boolean}\n   * @readonly\n   */\n  get isMobileAccessibility() {\n    return this._isMobileAccessibility;\n  }\n  /**\n   * Creates the touch hooks.\n   * @private\n   */\n  createTouchHook() {\n    const hookDiv = document.createElement(\"button\");\n    hookDiv.style.width = `${DIV_HOOK_SIZE}px`, hookDiv.style.height = `${DIV_HOOK_SIZE}px`, hookDiv.style.position = \"absolute\", hookDiv.style.top = `${DIV_HOOK_POS_X}px`, hookDiv.style.left = `${DIV_HOOK_POS_Y}px`, hookDiv.style.zIndex = DIV_HOOK_ZINDEX.toString(), hookDiv.style.backgroundColor = \"#FF0000\", hookDiv.title = \"select to enable accessibility for this content\", hookDiv.addEventListener(\"focus\", () => {\n      this._isMobileAccessibility = !0, this.activate(), this.destroyTouchHook();\n    }), document.body.appendChild(hookDiv), this._hookDiv = hookDiv;\n  }\n  /**\n   * Destroys the touch hooks.\n   * @private\n   */\n  destroyTouchHook() {\n    this._hookDiv && (document.body.removeChild(this._hookDiv), this._hookDiv = null);\n  }\n  /**\n   * Activating will cause the Accessibility layer to be shown.\n   * This is called when a user presses the tab key.\n   * @private\n   */\n  activate() {\n    this._isActive || (this._isActive = !0, globalThis.document.addEventListener(\"mousemove\", this._onMouseMove, !0), globalThis.removeEventListener(\"keydown\", this._onKeyDown, !1), this.renderer.on(\"postrender\", this.update, this), this.renderer.view.parentNode?.appendChild(this.div));\n  }\n  /**\n   * Deactivating will cause the Accessibility layer to be hidden.\n   * This is called when a user moves the mouse.\n   * @private\n   */\n  deactivate() {\n    !this._isActive || this._isMobileAccessibility || (this._isActive = !1, globalThis.document.removeEventListener(\"mousemove\", this._onMouseMove, !0), globalThis.addEventListener(\"keydown\", this._onKeyDown, !1), this.renderer.off(\"postrender\", this.update), this.div.parentNode?.removeChild(this.div));\n  }\n  /**\n   * This recursive function will run through the scene graph and add any new accessible objects to the DOM layer.\n   * @private\n   * @param {PIXI.Container} displayObject - The DisplayObject to check.\n   */\n  updateAccessibleObjects(displayObject) {\n    if (!displayObject.visible || !displayObject.accessibleChildren) return;\n    displayObject.accessible && displayObject.isInteractive() && (displayObject._accessibleActive || this.addChild(displayObject), displayObject.renderId = this.renderId);\n    const children = displayObject.children;\n    if (children) for (let i = 0; i < children.length; i++) this.updateAccessibleObjects(children[i]);\n  }\n  /**\n   * Before each render this function will ensure that all divs are mapped correctly to their DisplayObjects.\n   * @private\n   */\n  update() {\n    const now = performance.now();\n    if (utils.isMobile.android.device && now < this.androidUpdateCount || (this.androidUpdateCount = now + this.androidUpdateFrequency, !this.renderer.renderingToScreen)) return;\n    this.renderer.lastObjectRendered && this.updateAccessibleObjects(this.renderer.lastObjectRendered);\n    const {\n        x,\n        y,\n        width,\n        height\n      } = this.renderer.view.getBoundingClientRect(),\n      {\n        width: viewWidth,\n        height: viewHeight,\n        resolution\n      } = this.renderer,\n      sx = width / viewWidth * resolution,\n      sy = height / viewHeight * resolution;\n    let div = this.div;\n    div.style.left = `${x}px`, div.style.top = `${y}px`, div.style.width = `${viewWidth}px`, div.style.height = `${viewHeight}px`;\n    for (let i = 0; i < this.children.length; i++) {\n      const child = this.children[i];\n      if (child.renderId !== this.renderId) child._accessibleActive = !1, utils.removeItems(this.children, i, 1), this.div.removeChild(child._accessibleDiv), this.pool.push(child._accessibleDiv), child._accessibleDiv = null, i--;else {\n        div = child._accessibleDiv;\n        let hitArea = child.hitArea;\n        const wt = child.worldTransform;\n        child.hitArea ? (div.style.left = `${(wt.tx + hitArea.x * wt.a) * sx}px`, div.style.top = `${(wt.ty + hitArea.y * wt.d) * sy}px`, div.style.width = `${hitArea.width * wt.a * sx}px`, div.style.height = `${hitArea.height * wt.d * sy}px`) : (hitArea = child.getBounds(), this.capHitArea(hitArea), div.style.left = `${hitArea.x * sx}px`, div.style.top = `${hitArea.y * sy}px`, div.style.width = `${hitArea.width * sx}px`, div.style.height = `${hitArea.height * sy}px`, div.title !== child.accessibleTitle && child.accessibleTitle !== null && (div.title = child.accessibleTitle), div.getAttribute(\"aria-label\") !== child.accessibleHint && child.accessibleHint !== null && div.setAttribute(\"aria-label\", child.accessibleHint)), (child.accessibleTitle !== div.title || child.tabIndex !== div.tabIndex) && (div.title = child.accessibleTitle, div.tabIndex = child.tabIndex, this.debug && this.updateDebugHTML(div));\n      }\n    }\n    this.renderId++;\n  }\n  /**\n   * private function that will visually add the information to the\n   * accessability div\n   * @param {HTMLElement} div -\n   */\n  updateDebugHTML(div) {\n    div.innerHTML = `type: ${div.type}</br> title : ${div.title}</br> tabIndex: ${div.tabIndex}`;\n  }\n  /**\n   * Adjust the hit area based on the bounds of a display object\n   * @param {PIXI.Rectangle} hitArea - Bounds of the child\n   */\n  capHitArea(hitArea) {\n    hitArea.x < 0 && (hitArea.width += hitArea.x, hitArea.x = 0), hitArea.y < 0 && (hitArea.height += hitArea.y, hitArea.y = 0);\n    const {\n      width: viewWidth,\n      height: viewHeight\n    } = this.renderer;\n    hitArea.x + hitArea.width > viewWidth && (hitArea.width = viewWidth - hitArea.x), hitArea.y + hitArea.height > viewHeight && (hitArea.height = viewHeight - hitArea.y);\n  }\n  /**\n   * Adds a DisplayObject to the accessibility manager\n   * @private\n   * @param {PIXI.DisplayObject} displayObject - The child to make accessible.\n   */\n  addChild(displayObject) {\n    let div = this.pool.pop();\n    div || (div = document.createElement(\"button\"), div.style.width = `${DIV_TOUCH_SIZE}px`, div.style.height = `${DIV_TOUCH_SIZE}px`, div.style.backgroundColor = this.debug ? \"rgba(255,255,255,0.5)\" : \"transparent\", div.style.position = \"absolute\", div.style.zIndex = DIV_TOUCH_ZINDEX.toString(), div.style.borderStyle = \"none\", navigator.userAgent.toLowerCase().includes(\"chrome\") ? div.setAttribute(\"aria-live\", \"off\") : div.setAttribute(\"aria-live\", \"polite\"), navigator.userAgent.match(/rv:.*Gecko\\//) ? div.setAttribute(\"aria-relevant\", \"additions\") : div.setAttribute(\"aria-relevant\", \"text\"), div.addEventListener(\"click\", this._onClick.bind(this)), div.addEventListener(\"focus\", this._onFocus.bind(this)), div.addEventListener(\"focusout\", this._onFocusOut.bind(this))), div.style.pointerEvents = displayObject.accessiblePointerEvents, div.type = displayObject.accessibleType, displayObject.accessibleTitle && displayObject.accessibleTitle !== null ? div.title = displayObject.accessibleTitle : (!displayObject.accessibleHint || displayObject.accessibleHint === null) && (div.title = `displayObject ${displayObject.tabIndex}`), displayObject.accessibleHint && displayObject.accessibleHint !== null && div.setAttribute(\"aria-label\", displayObject.accessibleHint), this.debug && this.updateDebugHTML(div), displayObject._accessibleActive = !0, displayObject._accessibleDiv = div, div.displayObject = displayObject, this.children.push(displayObject), this.div.appendChild(displayObject._accessibleDiv), displayObject._accessibleDiv.tabIndex = displayObject.tabIndex;\n  }\n  /**\n   * Dispatch events with the EventSystem.\n   * @param e\n   * @param type\n   * @private\n   */\n  _dispatchEvent(e, type) {\n    const {\n        displayObject: target\n      } = e.target,\n      boundry = this.renderer.events.rootBoundary,\n      event = Object.assign(new FederatedEvent(boundry), {\n        target\n      });\n    boundry.rootTarget = this.renderer.lastObjectRendered, type.forEach(type2 => boundry.dispatchEvent(event, type2));\n  }\n  /**\n   * Maps the div button press to pixi's EventSystem (click)\n   * @private\n   * @param {MouseEvent} e - The click event.\n   */\n  _onClick(e) {\n    this._dispatchEvent(e, [\"click\", \"pointertap\", \"tap\"]);\n  }\n  /**\n   * Maps the div focus events to pixi's EventSystem (mouseover)\n   * @private\n   * @param {FocusEvent} e - The focus event.\n   */\n  _onFocus(e) {\n    e.target.getAttribute(\"aria-live\") || e.target.setAttribute(\"aria-live\", \"assertive\"), this._dispatchEvent(e, [\"mouseover\"]);\n  }\n  /**\n   * Maps the div focus events to pixi's EventSystem (mouseout)\n   * @private\n   * @param {FocusEvent} e - The focusout event.\n   */\n  _onFocusOut(e) {\n    e.target.getAttribute(\"aria-live\") || e.target.setAttribute(\"aria-live\", \"polite\"), this._dispatchEvent(e, [\"mouseout\"]);\n  }\n  /**\n   * Is called when a key is pressed\n   * @private\n   * @param {KeyboardEvent} e - The keydown event.\n   */\n  _onKeyDown(e) {\n    e.keyCode === KEY_CODE_TAB && this.activate();\n  }\n  /**\n   * Is called when the mouse moves across the renderer element\n   * @private\n   * @param {MouseEvent} e - The mouse event.\n   */\n  _onMouseMove(e) {\n    e.movementX === 0 && e.movementY === 0 || this.deactivate();\n  }\n  /** Destroys the accessibility manager */\n  destroy() {\n    this.destroyTouchHook(), this.div = null, globalThis.document.removeEventListener(\"mousemove\", this._onMouseMove, !0), globalThis.removeEventListener(\"keydown\", this._onKeyDown), this.pool = null, this.children = null, this.renderer = null;\n  }\n}\nAccessibilityManager.extension = {\n  name: \"accessibility\",\n  type: [ExtensionType.RendererPlugin, ExtensionType.CanvasRendererPlugin]\n};\nextensions.add(AccessibilityManager);\nexport { AccessibilityManager };", "map": {"version": 3, "names": ["DisplayObject", "mixin", "accessibleTarget", "KEY_CODE_TAB", "DIV_TOUCH_SIZE", "DIV_TOUCH_POS_X", "DIV_TOUCH_POS_Y", "DIV_TOUCH_ZINDEX", "DIV_HOOK_SIZE", "DIV_HOOK_POS_X", "DIV_HOOK_POS_Y", "DIV_HOOK_ZINDEX", "AccessibilityManager", "constructor", "renderer", "debug", "_isActive", "_isMobileAccessibility", "pool", "renderId", "children", "androidUpdateCount", "androidUpdateFrequency", "_hookDiv", "utils", "isMobile", "tablet", "phone", "createTouchHook", "div", "document", "createElement", "style", "width", "height", "position", "top", "left", "zIndex", "toString", "_onKeyDown", "bind", "_onMouseMove", "globalThis", "addEventListener", "isActive", "isMobileAccessibility", "hookDiv", "backgroundColor", "title", "activate", "destroyTouchHook", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "on", "update", "view", "parentNode", "deactivate", "off", "updateAccessibleObjects", "displayObject", "visible", "accessibleChildren", "accessible", "isInteractive", "_accessibleActive", "<PERSON><PERSON><PERSON><PERSON>", "i", "length", "now", "performance", "android", "device", "renderingToScreen", "lastObjectRendered", "x", "y", "getBoundingClientRect", "viewWidth", "viewHeight", "resolution", "sx", "sy", "child", "removeItems", "_accessibleDiv", "push", "hitArea", "wt", "worldTransform", "tx", "a", "ty", "d", "getBounds", "capHitArea", "accessibleTitle", "getAttribute", "accessibleHint", "setAttribute", "tabIndex", "updateDebugHTML", "innerHTML", "type", "pop", "borderStyle", "navigator", "userAgent", "toLowerCase", "includes", "match", "_onClick", "_onFocus", "_onFocusOut", "pointerEvents", "accessiblePointerEvents", "accessibleType", "_dispatchEvent", "e", "target", "boundry", "events", "rootBoundary", "event", "Object", "assign", "FederatedEvent", "rootTarget", "for<PERSON>ach", "type2", "dispatchEvent", "keyCode", "movementX", "movementY", "destroy", "extension", "name", "ExtensionType", "RendererPlugin", "CanvasRendererPlugin", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\accessibility\\src\\AccessibilityManager.ts"], "sourcesContent": ["import { extensions, ExtensionType, utils } from '@pixi/core';\nimport { DisplayObject } from '@pixi/display';\nimport { FederatedEvent } from '@pixi/events';\nimport { accessibleTarget } from './accessibleTarget';\n\nimport type { ExtensionMetadata, IRenderer, Rectangle } from '@pixi/core';\nimport type { Container } from '@pixi/display';\nimport type { IAccessibleHTMLElement } from './accessibleTarget';\n\n// add some extra variables to the container..\nDisplayObject.mixin(accessibleTarget);\n\nconst KEY_CODE_TAB = 9;\n\nconst DIV_TOUCH_SIZE = 100;\nconst DIV_TOUCH_POS_X = 0;\nconst DIV_TOUCH_POS_Y = 0;\nconst DIV_TOUCH_ZINDEX = 2;\n\nconst DIV_HOOK_SIZE = 1;\nconst DIV_HOOK_POS_X = -1000;\nconst DIV_HOOK_POS_Y = -1000;\nconst DIV_HOOK_ZINDEX = 2;\n\n/**\n * The Accessibility manager recreates the ability to tab and have content read by screen readers.\n * This is very important as it can possibly help people with disabilities access PixiJS content.\n *\n * A DisplayObject can be made accessible just like it can be made interactive. This manager will map the\n * events as if the mouse was being used, minimizing the effort required to implement.\n *\n * An instance of this class is automatically created by default, and can be found at `renderer.plugins.accessibility`\n * @class\n * @memberof PIXI\n */\nexport class AccessibilityManager\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'accessibility',\n        type: [\n            ExtensionType.RendererPlugin,\n            ExtensionType.CanvasRendererPlugin,\n        ],\n    };\n\n    /** Setting this to true will visually show the divs. */\n    public debug = false;\n\n    /**\n     * The renderer this accessibility manager works for.\n     * @type {PIXI.CanvasRenderer|PIXI.Renderer}\n     */\n    public renderer: IRenderer;\n\n    /** Internal variable, see isActive getter. */\n    private _isActive = false;\n\n    /** Internal variable, see isMobileAccessibility getter. */\n    private _isMobileAccessibility = false;\n\n    /** Button element for handling touch hooks. */\n    private _hookDiv: HTMLElement;\n\n    /** This is the dom element that will sit over the PixiJS element. This is where the div overlays will go. */\n    private div: HTMLElement;\n\n    /** A simple pool for storing divs. */\n    private pool: IAccessibleHTMLElement[] = [];\n\n    /** This is a tick used to check if an object is no longer being rendered. */\n    private renderId = 0;\n\n    /** The array of currently active accessible items. */\n    private children: DisplayObject[] = [];\n\n    /** Count to throttle div updates on android devices. */\n    private androidUpdateCount = 0;\n\n    /**  The frequency to update the div elements. */\n    private androidUpdateFrequency = 500; // 2fps\n\n    /**\n     * @param {PIXI.CanvasRenderer|PIXI.Renderer} renderer - A reference to the current renderer\n     */\n    constructor(renderer: IRenderer)\n    {\n        this._hookDiv = null;\n\n        if (utils.isMobile.tablet || utils.isMobile.phone)\n        {\n            this.createTouchHook();\n        }\n\n        // first we create a div that will sit over the PixiJS element. This is where the div overlays will go.\n        const div = document.createElement('div');\n\n        div.style.width = `${DIV_TOUCH_SIZE}px`;\n        div.style.height = `${DIV_TOUCH_SIZE}px`;\n        div.style.position = 'absolute';\n        div.style.top = `${DIV_TOUCH_POS_X}px`;\n        div.style.left = `${DIV_TOUCH_POS_Y}px`;\n        div.style.zIndex = DIV_TOUCH_ZINDEX.toString();\n\n        this.div = div;\n        this.renderer = renderer;\n\n        /**\n         * pre-bind the functions\n         * @type {Function}\n         * @private\n         */\n        this._onKeyDown = this._onKeyDown.bind(this);\n\n        /**\n         * pre-bind the functions\n         * @type {Function}\n         * @private\n         */\n        this._onMouseMove = this._onMouseMove.bind(this);\n\n        // let listen for tab.. once pressed we can fire up and show the accessibility layer\n        globalThis.addEventListener('keydown', this._onKeyDown, false);\n    }\n\n    /**\n     * Value of `true` if accessibility is currently active and accessibility layers are showing.\n     * @member {boolean}\n     * @readonly\n     */\n    get isActive(): boolean\n    {\n        return this._isActive;\n    }\n\n    /**\n     * Value of `true` if accessibility is enabled for touch devices.\n     * @member {boolean}\n     * @readonly\n     */\n    get isMobileAccessibility(): boolean\n    {\n        return this._isMobileAccessibility;\n    }\n\n    /**\n     * Creates the touch hooks.\n     * @private\n     */\n    private createTouchHook(): void\n    {\n        const hookDiv = document.createElement('button');\n\n        hookDiv.style.width = `${DIV_HOOK_SIZE}px`;\n        hookDiv.style.height = `${DIV_HOOK_SIZE}px`;\n        hookDiv.style.position = 'absolute';\n        hookDiv.style.top = `${DIV_HOOK_POS_X}px`;\n        hookDiv.style.left = `${DIV_HOOK_POS_Y}px`;\n        hookDiv.style.zIndex = DIV_HOOK_ZINDEX.toString();\n        hookDiv.style.backgroundColor = '#FF0000';\n        hookDiv.title = 'select to enable accessibility for this content';\n\n        hookDiv.addEventListener('focus', () =>\n        {\n            this._isMobileAccessibility = true;\n            this.activate();\n            this.destroyTouchHook();\n        });\n\n        document.body.appendChild(hookDiv);\n        this._hookDiv = hookDiv;\n    }\n\n    /**\n     * Destroys the touch hooks.\n     * @private\n     */\n    private destroyTouchHook(): void\n    {\n        if (!this._hookDiv)\n        {\n            return;\n        }\n        document.body.removeChild(this._hookDiv);\n        this._hookDiv = null;\n    }\n\n    /**\n     * Activating will cause the Accessibility layer to be shown.\n     * This is called when a user presses the tab key.\n     * @private\n     */\n    private activate(): void\n    {\n        if (this._isActive)\n        {\n            return;\n        }\n\n        this._isActive = true;\n\n        globalThis.document.addEventListener('mousemove', this._onMouseMove, true);\n        globalThis.removeEventListener('keydown', this._onKeyDown, false);\n\n        this.renderer.on('postrender', this.update, this);\n        this.renderer.view.parentNode?.appendChild(this.div);\n    }\n\n    /**\n     * Deactivating will cause the Accessibility layer to be hidden.\n     * This is called when a user moves the mouse.\n     * @private\n     */\n    private deactivate(): void\n    {\n        if (!this._isActive || this._isMobileAccessibility)\n        {\n            return;\n        }\n\n        this._isActive = false;\n\n        globalThis.document.removeEventListener('mousemove', this._onMouseMove, true);\n        globalThis.addEventListener('keydown', this._onKeyDown, false);\n\n        this.renderer.off('postrender', this.update);\n        this.div.parentNode?.removeChild(this.div);\n    }\n\n    /**\n     * This recursive function will run through the scene graph and add any new accessible objects to the DOM layer.\n     * @private\n     * @param {PIXI.Container} displayObject - The DisplayObject to check.\n     */\n    private updateAccessibleObjects(displayObject: Container): void\n    {\n        if (!displayObject.visible || !displayObject.accessibleChildren)\n        {\n            return;\n        }\n\n        if (displayObject.accessible && displayObject.isInteractive())\n        {\n            if (!displayObject._accessibleActive)\n            {\n                this.addChild(displayObject);\n            }\n\n            displayObject.renderId = this.renderId;\n        }\n\n        const children = displayObject.children;\n\n        if (children)\n        {\n            for (let i = 0; i < children.length; i++)\n            {\n                this.updateAccessibleObjects(children[i] as Container);\n            }\n        }\n    }\n\n    /**\n     * Before each render this function will ensure that all divs are mapped correctly to their DisplayObjects.\n     * @private\n     */\n    private update(): void\n    {\n        /* On Android default web browser, tab order seems to be calculated by position rather than tabIndex,\n        *  moving buttons can cause focus to flicker between two buttons making it hard/impossible to navigate,\n        *  so I am just running update every half a second, seems to fix it.\n        */\n        const now = performance.now();\n\n        if (utils.isMobile.android.device && now < this.androidUpdateCount)\n        {\n            return;\n        }\n\n        this.androidUpdateCount = now + this.androidUpdateFrequency;\n\n        if (!this.renderer.renderingToScreen)\n        {\n            return;\n        }\n\n        // update children...\n        if (this.renderer.lastObjectRendered)\n        {\n            this.updateAccessibleObjects(this.renderer.lastObjectRendered as Container);\n        }\n\n        const { x, y, width, height } = this.renderer.view.getBoundingClientRect();\n        const { width: viewWidth, height: viewHeight, resolution } = this.renderer;\n\n        const sx = (width / viewWidth) * resolution;\n        const sy = (height / viewHeight) * resolution;\n\n        let div = this.div;\n\n        div.style.left = `${x}px`;\n        div.style.top = `${y}px`;\n        div.style.width = `${viewWidth}px`;\n        div.style.height = `${viewHeight}px`;\n\n        for (let i = 0; i < this.children.length; i++)\n        {\n            const child = this.children[i];\n\n            if (child.renderId !== this.renderId)\n            {\n                child._accessibleActive = false;\n\n                utils.removeItems(this.children, i, 1);\n                this.div.removeChild(child._accessibleDiv);\n                this.pool.push(child._accessibleDiv);\n                child._accessibleDiv = null;\n\n                i--;\n            }\n            else\n            {\n                // map div to display..\n                div = child._accessibleDiv;\n                let hitArea = child.hitArea as Rectangle;\n                const wt = child.worldTransform;\n\n                if (child.hitArea)\n                {\n                    div.style.left = `${(wt.tx + (hitArea.x * wt.a)) * sx}px`;\n                    div.style.top = `${(wt.ty + (hitArea.y * wt.d)) * sy}px`;\n\n                    div.style.width = `${hitArea.width * wt.a * sx}px`;\n                    div.style.height = `${hitArea.height * wt.d * sy}px`;\n                }\n                else\n                {\n                    hitArea = child.getBounds();\n\n                    this.capHitArea(hitArea);\n\n                    div.style.left = `${hitArea.x * sx}px`;\n                    div.style.top = `${hitArea.y * sy}px`;\n\n                    div.style.width = `${hitArea.width * sx}px`;\n                    div.style.height = `${hitArea.height * sy}px`;\n\n                    // update button titles and hints if they exist and they've changed\n                    if (div.title !== child.accessibleTitle && child.accessibleTitle !== null)\n                    {\n                        div.title = child.accessibleTitle;\n                    }\n                    if (div.getAttribute('aria-label') !== child.accessibleHint\n                        && child.accessibleHint !== null)\n                    {\n                        div.setAttribute('aria-label', child.accessibleHint);\n                    }\n                }\n\n                // the title or index may have changed, if so lets update it!\n                if (child.accessibleTitle !== div.title || child.tabIndex !== div.tabIndex)\n                {\n                    div.title = child.accessibleTitle;\n                    div.tabIndex = child.tabIndex;\n                    if (this.debug) this.updateDebugHTML(div);\n                }\n            }\n        }\n\n        // increment the render id..\n        this.renderId++;\n    }\n\n    /**\n     * private function that will visually add the information to the\n     * accessability div\n     * @param {HTMLElement} div -\n     */\n    public updateDebugHTML(div: IAccessibleHTMLElement): void\n    {\n        div.innerHTML = `type: ${div.type}</br> title : ${div.title}</br> tabIndex: ${div.tabIndex}`;\n    }\n\n    /**\n     * Adjust the hit area based on the bounds of a display object\n     * @param {PIXI.Rectangle} hitArea - Bounds of the child\n     */\n    public capHitArea(hitArea: Rectangle): void\n    {\n        if (hitArea.x < 0)\n        {\n            hitArea.width += hitArea.x;\n            hitArea.x = 0;\n        }\n\n        if (hitArea.y < 0)\n        {\n            hitArea.height += hitArea.y;\n            hitArea.y = 0;\n        }\n\n        const { width: viewWidth, height: viewHeight } = this.renderer;\n\n        if (hitArea.x + hitArea.width > viewWidth)\n        {\n            hitArea.width = viewWidth - hitArea.x;\n        }\n\n        if (hitArea.y + hitArea.height > viewHeight)\n        {\n            hitArea.height = viewHeight - hitArea.y;\n        }\n    }\n\n    /**\n     * Adds a DisplayObject to the accessibility manager\n     * @private\n     * @param {PIXI.DisplayObject} displayObject - The child to make accessible.\n     */\n    private addChild<T extends DisplayObject>(displayObject: T): void\n    {\n        //    this.activate();\n\n        let div = this.pool.pop();\n\n        if (!div)\n        {\n            div = document.createElement('button');\n\n            div.style.width = `${DIV_TOUCH_SIZE}px`;\n            div.style.height = `${DIV_TOUCH_SIZE}px`;\n            div.style.backgroundColor = this.debug ? 'rgba(255,255,255,0.5)' : 'transparent';\n            div.style.position = 'absolute';\n            div.style.zIndex = DIV_TOUCH_ZINDEX.toString();\n            div.style.borderStyle = 'none';\n\n            // ARIA attributes ensure that button title and hint updates are announced properly\n            if (navigator.userAgent.toLowerCase().includes('chrome'))\n            {\n                // Chrome doesn't need aria-live to work as intended; in fact it just gets more confused.\n                div.setAttribute('aria-live', 'off');\n            }\n            else\n            {\n                div.setAttribute('aria-live', 'polite');\n            }\n\n            if (navigator.userAgent.match(/rv:.*Gecko\\//))\n            {\n                // FireFox needs this to announce only the new button name\n                div.setAttribute('aria-relevant', 'additions');\n            }\n            else\n            {\n                // required by IE, other browsers don't much care\n                div.setAttribute('aria-relevant', 'text');\n            }\n\n            div.addEventListener('click', this._onClick.bind(this));\n            div.addEventListener('focus', this._onFocus.bind(this));\n            div.addEventListener('focusout', this._onFocusOut.bind(this));\n        }\n\n        // set pointer events\n        div.style.pointerEvents = displayObject.accessiblePointerEvents;\n        // set the type, this defaults to button!\n        div.type = displayObject.accessibleType;\n\n        if (displayObject.accessibleTitle && displayObject.accessibleTitle !== null)\n        {\n            div.title = displayObject.accessibleTitle;\n        }\n        else if (!displayObject.accessibleHint\n                 || displayObject.accessibleHint === null)\n        {\n            div.title = `displayObject ${displayObject.tabIndex}`;\n        }\n\n        if (displayObject.accessibleHint\n            && displayObject.accessibleHint !== null)\n        {\n            div.setAttribute('aria-label', displayObject.accessibleHint);\n        }\n\n        if (this.debug) this.updateDebugHTML(div);\n\n        displayObject._accessibleActive = true;\n        displayObject._accessibleDiv = div;\n        div.displayObject = displayObject;\n\n        this.children.push(displayObject);\n        this.div.appendChild(displayObject._accessibleDiv);\n        displayObject._accessibleDiv.tabIndex = displayObject.tabIndex;\n    }\n\n    /**\n     * Dispatch events with the EventSystem.\n     * @param e\n     * @param type\n     * @private\n     */\n    private _dispatchEvent(e: UIEvent, type: string[]): void\n    {\n        const { displayObject: target } = e.target as IAccessibleHTMLElement;\n        const boundry = this.renderer.events.rootBoundary;\n        const event: FederatedEvent = Object.assign(new FederatedEvent(boundry), { target });\n\n        boundry.rootTarget = this.renderer.lastObjectRendered as DisplayObject;\n        type.forEach((type) => boundry.dispatchEvent(event, type));\n    }\n\n    /**\n     * Maps the div button press to pixi's EventSystem (click)\n     * @private\n     * @param {MouseEvent} e - The click event.\n     */\n    private _onClick(e: MouseEvent): void\n    {\n        this._dispatchEvent(e, ['click', 'pointertap', 'tap']);\n    }\n\n    /**\n     * Maps the div focus events to pixi's EventSystem (mouseover)\n     * @private\n     * @param {FocusEvent} e - The focus event.\n     */\n    private _onFocus(e: FocusEvent): void\n    {\n        if (!(e.target as Element).getAttribute('aria-live'))\n        {\n            (e.target as Element).setAttribute('aria-live', 'assertive');\n        }\n\n        this._dispatchEvent(e, ['mouseover']);\n    }\n\n    /**\n     * Maps the div focus events to pixi's EventSystem (mouseout)\n     * @private\n     * @param {FocusEvent} e - The focusout event.\n     */\n    private _onFocusOut(e: FocusEvent): void\n    {\n        if (!(e.target as Element).getAttribute('aria-live'))\n        {\n            (e.target as Element).setAttribute('aria-live', 'polite');\n        }\n\n        this._dispatchEvent(e, ['mouseout']);\n    }\n\n    /**\n     * Is called when a key is pressed\n     * @private\n     * @param {KeyboardEvent} e - The keydown event.\n     */\n    private _onKeyDown(e: KeyboardEvent): void\n    {\n        if (e.keyCode !== KEY_CODE_TAB)\n        {\n            return;\n        }\n\n        this.activate();\n    }\n\n    /**\n     * Is called when the mouse moves across the renderer element\n     * @private\n     * @param {MouseEvent} e - The mouse event.\n     */\n    private _onMouseMove(e: MouseEvent): void\n    {\n        if (e.movementX === 0 && e.movementY === 0)\n        {\n            return;\n        }\n\n        this.deactivate();\n    }\n\n    /** Destroys the accessibility manager */\n    public destroy(): void\n    {\n        this.destroyTouchHook();\n        this.div = null;\n\n        globalThis.document.removeEventListener('mousemove', this._onMouseMove, true);\n        globalThis.removeEventListener('keydown', this._onKeyDown);\n\n        this.pool = null;\n        this.children = null;\n        this.renderer = null;\n    }\n}\n\nextensions.add(AccessibilityManager);\n"], "mappings": ";;;;AAUAA,aAAA,CAAcC,KAAA,CAAMC,gBAAgB;AAEpC,MAAMC,YAAA,GAAe;EAEfC,cAAA,GAAiB;EACjBC,eAAA,GAAkB;EAClBC,eAAA,GAAkB;EAClBC,gBAAA,GAAmB;EAEnBC,aAAA,GAAgB;EAChBC,cAAA,GAAiB;EACjBC,cAAA,GAAiB;EACjBC,eAAA,GAAkB;AAajB,MAAMC,oBAAA,CACb;EAAA;EAAA;AAAA;AAAA;EAiDIC,YAAYC,QAAA,EACZ;IAvCA,KAAOC,KAAA,GAAQ,IASf,KAAQC,SAAA,GAAY,IAGpB,KAAQC,sBAAA,GAAyB,IASjC,KAAQC,IAAA,GAAiC,IAGzC,KAAQC,QAAA,GAAW,GAGnB,KAAQC,QAAA,GAA4B,IAGpC,KAAQC,kBAAA,GAAqB,GAG7B,KAAQC,sBAAA,GAAyB,KAOxB,KAAAC,QAAA,GAAW,OAEZC,KAAA,CAAMC,QAAA,CAASC,MAAA,IAAUF,KAAA,CAAMC,QAAA,CAASE,KAAA,KAExC,KAAKC,eAAA,CAAgB;IAInB,MAAAC,GAAA,GAAMC,QAAA,CAASC,aAAA,CAAc,KAAK;IAEpCF,GAAA,CAAAG,KAAA,CAAMC,KAAA,GAAQ,GAAG7B,cAAc,MACnCyB,GAAA,CAAIG,KAAA,CAAME,MAAA,GAAS,GAAG9B,cAAc,MACpCyB,GAAA,CAAIG,KAAA,CAAMG,QAAA,GAAW,YACrBN,GAAA,CAAIG,KAAA,CAAMI,GAAA,GAAM,GAAG/B,eAAe,MAClCwB,GAAA,CAAIG,KAAA,CAAMK,IAAA,GAAO,GAAG/B,eAAe,MACnCuB,GAAA,CAAIG,KAAA,CAAMM,MAAA,GAAS/B,gBAAA,CAAiBgC,QAAA,IAEpC,KAAKV,GAAA,GAAMA,GAAA,EACX,KAAKf,QAAA,GAAWA,QAAA,EAOhB,KAAK0B,UAAA,GAAa,KAAKA,UAAA,CAAWC,IAAA,CAAK,IAAI,GAO3C,KAAKC,YAAA,GAAe,KAAKA,YAAA,CAAaD,IAAA,CAAK,IAAI,GAG/CE,UAAA,CAAWC,gBAAA,CAAiB,WAAW,KAAKJ,UAAA,EAAY,EAAK;EACjE;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,IAAIK,SAAA,EACJ;IACI,OAAO,KAAK7B,SAAA;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,IAAI8B,sBAAA,EACJ;IACI,OAAO,KAAK7B,sBAAA;EAChB;EAAA;AAAA;AAAA;AAAA;EAMQW,gBAAA,EACR;IACU,MAAAmB,OAAA,GAAUjB,QAAA,CAASC,aAAA,CAAc,QAAQ;IAE/CgB,OAAA,CAAQf,KAAA,CAAMC,KAAA,GAAQ,GAAGzB,aAAa,MACtCuC,OAAA,CAAQf,KAAA,CAAME,MAAA,GAAS,GAAG1B,aAAa,MACvCuC,OAAA,CAAQf,KAAA,CAAMG,QAAA,GAAW,YACzBY,OAAA,CAAQf,KAAA,CAAMI,GAAA,GAAM,GAAG3B,cAAc,MACrCsC,OAAA,CAAQf,KAAA,CAAMK,IAAA,GAAO,GAAG3B,cAAc,MACtCqC,OAAA,CAAQf,KAAA,CAAMM,MAAA,GAAS3B,eAAA,CAAgB4B,QAAA,IACvCQ,OAAA,CAAQf,KAAA,CAAMgB,eAAA,GAAkB,WAChCD,OAAA,CAAQE,KAAA,GAAQ,mDAEhBF,OAAA,CAAQH,gBAAA,CAAiB,SAAS,MAClC;MACI,KAAK3B,sBAAA,GAAyB,IAC9B,KAAKiC,QAAA,IACL,KAAKC,gBAAA;IAAiB,CACzB,GAEDrB,QAAA,CAASsB,IAAA,CAAKC,WAAA,CAAYN,OAAO,GACjC,KAAKxB,QAAA,GAAWwB,OAAA;EACpB;EAAA;AAAA;AAAA;AAAA;EAMQI,iBAAA,EACR;IACS,KAAK5B,QAAA,KAIVO,QAAA,CAASsB,IAAA,CAAKE,WAAA,CAAY,KAAK/B,QAAQ,GACvC,KAAKA,QAAA,GAAW;EACpB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOQ2B,SAAA,EACR;IACQ,KAAKlC,SAAA,KAKT,KAAKA,SAAA,GAAY,IAEjB2B,UAAA,CAAWb,QAAA,CAASc,gBAAA,CAAiB,aAAa,KAAKF,YAAA,EAAc,EAAI,GACzEC,UAAA,CAAWY,mBAAA,CAAoB,WAAW,KAAKf,UAAA,EAAY,EAAK,GAEhE,KAAK1B,QAAA,CAAS0C,EAAA,CAAG,cAAc,KAAKC,MAAA,EAAQ,IAAI,GAChD,KAAK3C,QAAA,CAAS4C,IAAA,CAAKC,UAAA,EAAYN,WAAA,CAAY,KAAKxB,GAAG;EACvD;EAAA;AAAA;AAAA;AAAA;AAAA;EAOQ+B,WAAA,EACR;IACQ,CAAC,KAAK5C,SAAA,IAAa,KAAKC,sBAAA,KAK5B,KAAKD,SAAA,GAAY,IAEjB2B,UAAA,CAAWb,QAAA,CAASyB,mBAAA,CAAoB,aAAa,KAAKb,YAAA,EAAc,EAAI,GAC5EC,UAAA,CAAWC,gBAAA,CAAiB,WAAW,KAAKJ,UAAA,EAAY,EAAK,GAE7D,KAAK1B,QAAA,CAAS+C,GAAA,CAAI,cAAc,KAAKJ,MAAM,GAC3C,KAAK5B,GAAA,CAAI8B,UAAA,EAAYL,WAAA,CAAY,KAAKzB,GAAG;EAC7C;EAAA;AAAA;AAAA;AAAA;AAAA;EAOQiC,wBAAwBC,aAAA,EAChC;IACI,IAAI,CAACA,aAAA,CAAcC,OAAA,IAAW,CAACD,aAAA,CAAcE,kBAAA,EAEzC;IAGAF,aAAA,CAAcG,UAAA,IAAcH,aAAA,CAAcI,aAAA,OAErCJ,aAAA,CAAcK,iBAAA,IAEf,KAAKC,QAAA,CAASN,aAAa,GAG/BA,aAAA,CAAc5C,QAAA,GAAW,KAAKA,QAAA;IAGlC,MAAMC,QAAA,GAAW2C,aAAA,CAAc3C,QAAA;IAE3B,IAAAA,QAAA,EAEA,SAASkD,CAAA,GAAI,GAAGA,CAAA,GAAIlD,QAAA,CAASmD,MAAA,EAAQD,CAAA,IAE5B,KAAAR,uBAAA,CAAwB1C,QAAA,CAASkD,CAAC,CAAc;EAGjE;EAAA;AAAA;AAAA;AAAA;EAMQb,OAAA,EACR;IAKU,MAAAe,GAAA,GAAMC,WAAA,CAAYD,GAAA;IASxB,IAPIhD,KAAA,CAAMC,QAAA,CAASiD,OAAA,CAAQC,MAAA,IAAUH,GAAA,GAAM,KAAKnD,kBAAA,KAKhD,KAAKA,kBAAA,GAAqBmD,GAAA,GAAM,KAAKlD,sBAAA,EAEjC,CAAC,KAAKR,QAAA,CAAS8D,iBAAA,GAEf;IAIA,KAAK9D,QAAA,CAAS+D,kBAAA,IAEd,KAAKf,uBAAA,CAAwB,KAAKhD,QAAA,CAAS+D,kBAA+B;IAGxE;QAAEC,CAAA;QAAGC,CAAA;QAAG9C,KAAA;QAAOC;MAAW,SAAKpB,QAAA,CAAS4C,IAAA,CAAKsB,qBAAA;MAC7C;QAAE/C,KAAA,EAAOgD,SAAA;QAAW/C,MAAA,EAAQgD,UAAA;QAAYC;MAAW,IAAI,KAAKrE,QAAA;MAE5DsE,EAAA,GAAMnD,KAAA,GAAQgD,SAAA,GAAaE,UAAA;MAC3BE,EAAA,GAAMnD,MAAA,GAASgD,UAAA,GAAcC,UAAA;IAEnC,IAAItD,GAAA,GAAM,KAAKA,GAAA;IAEXA,GAAA,CAAAG,KAAA,CAAMK,IAAA,GAAO,GAAGyC,CAAC,MACrBjD,GAAA,CAAIG,KAAA,CAAMI,GAAA,GAAM,GAAG2C,CAAC,MACpBlD,GAAA,CAAIG,KAAA,CAAMC,KAAA,GAAQ,GAAGgD,SAAS,MAC9BpD,GAAA,CAAIG,KAAA,CAAME,MAAA,GAAS,GAAGgD,UAAU;IAEhC,SAASZ,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKlD,QAAA,CAASmD,MAAA,EAAQD,CAAA,IAC1C;MACU,MAAAgB,KAAA,GAAQ,KAAKlE,QAAA,CAASkD,CAAC;MAEzB,IAAAgB,KAAA,CAAMnE,QAAA,KAAa,KAAKA,QAAA,EAElBmE,KAAA,CAAAlB,iBAAA,GAAoB,IAE1B5C,KAAA,CAAM+D,WAAA,CAAY,KAAKnE,QAAA,EAAUkD,CAAA,EAAG,CAAC,GACrC,KAAKzC,GAAA,CAAIyB,WAAA,CAAYgC,KAAA,CAAME,cAAc,GACzC,KAAKtE,IAAA,CAAKuE,IAAA,CAAKH,KAAA,CAAME,cAAc,GACnCF,KAAA,CAAME,cAAA,GAAiB,MAEvBlB,CAAA,QAGJ;QAEIzC,GAAA,GAAMyD,KAAA,CAAME,cAAA;QACZ,IAAIE,OAAA,GAAUJ,KAAA,CAAMI,OAAA;QACpB,MAAMC,EAAA,GAAKL,KAAA,CAAMM,cAAA;QAEbN,KAAA,CAAMI,OAAA,IAEN7D,GAAA,CAAIG,KAAA,CAAMK,IAAA,GAAO,IAAIsD,EAAA,CAAGE,EAAA,GAAMH,OAAA,CAAQZ,CAAA,GAAIa,EAAA,CAAGG,CAAA,IAAMV,EAAE,MACrDvD,GAAA,CAAIG,KAAA,CAAMI,GAAA,GAAM,IAAIuD,EAAA,CAAGI,EAAA,GAAML,OAAA,CAAQX,CAAA,GAAIY,EAAA,CAAGK,CAAA,IAAMX,EAAE,MAEpDxD,GAAA,CAAIG,KAAA,CAAMC,KAAA,GAAQ,GAAGyD,OAAA,CAAQzD,KAAA,GAAQ0D,EAAA,CAAGG,CAAA,GAAIV,EAAE,MAC9CvD,GAAA,CAAIG,KAAA,CAAME,MAAA,GAAS,GAAGwD,OAAA,CAAQxD,MAAA,GAASyD,EAAA,CAAGK,CAAA,GAAIX,EAAE,SAIhDK,OAAA,GAAUJ,KAAA,CAAMW,SAAA,CAEhB,QAAKC,UAAA,CAAWR,OAAO,GAEvB7D,GAAA,CAAIG,KAAA,CAAMK,IAAA,GAAO,GAAGqD,OAAA,CAAQZ,CAAA,GAAIM,EAAE,MAClCvD,GAAA,CAAIG,KAAA,CAAMI,GAAA,GAAM,GAAGsD,OAAA,CAAQX,CAAA,GAAIM,EAAE,MAEjCxD,GAAA,CAAIG,KAAA,CAAMC,KAAA,GAAQ,GAAGyD,OAAA,CAAQzD,KAAA,GAAQmD,EAAE,MACvCvD,GAAA,CAAIG,KAAA,CAAME,MAAA,GAAS,GAAGwD,OAAA,CAAQxD,MAAA,GAASmD,EAAE,MAGrCxD,GAAA,CAAIoB,KAAA,KAAUqC,KAAA,CAAMa,eAAA,IAAmBb,KAAA,CAAMa,eAAA,KAAoB,SAEjEtE,GAAA,CAAIoB,KAAA,GAAQqC,KAAA,CAAMa,eAAA,GAElBtE,GAAA,CAAIuE,YAAA,CAAa,YAAY,MAAMd,KAAA,CAAMe,cAAA,IACtCf,KAAA,CAAMe,cAAA,KAAmB,QAE5BxE,GAAA,CAAIyE,YAAA,CAAa,cAAchB,KAAA,CAAMe,cAAc,KAKvDf,KAAA,CAAMa,eAAA,KAAoBtE,GAAA,CAAIoB,KAAA,IAASqC,KAAA,CAAMiB,QAAA,KAAa1E,GAAA,CAAI0E,QAAA,MAE9D1E,GAAA,CAAIoB,KAAA,GAAQqC,KAAA,CAAMa,eAAA,EAClBtE,GAAA,CAAI0E,QAAA,GAAWjB,KAAA,CAAMiB,QAAA,EACjB,KAAKxF,KAAA,IAAO,KAAKyF,eAAA,CAAgB3E,GAAG;MAEhD;IACJ;IAGK,KAAAV,QAAA;EACT;EAAA;AAAA;AAAA;AAAA;AAAA;EAOOqF,gBAAgB3E,GAAA,EACvB;IACQA,GAAA,CAAA4E,SAAA,GAAY,SAAS5E,GAAA,CAAI6E,IAAI,iBAAiB7E,GAAA,CAAIoB,KAAK,mBAAmBpB,GAAA,CAAI0E,QAAQ;EAC9F;EAAA;AAAA;AAAA;AAAA;EAMOL,WAAWR,OAAA,EAClB;IACQA,OAAA,CAAQZ,CAAA,GAAI,MAEZY,OAAA,CAAQzD,KAAA,IAASyD,OAAA,CAAQZ,CAAA,EACzBY,OAAA,CAAQZ,CAAA,GAAI,IAGZY,OAAA,CAAQX,CAAA,GAAI,MAEZW,OAAA,CAAQxD,MAAA,IAAUwD,OAAA,CAAQX,CAAA,EAC1BW,OAAA,CAAQX,CAAA,GAAI;IAGhB,MAAM;MAAE9C,KAAA,EAAOgD,SAAA;MAAW/C,MAAA,EAAQgD;IAAA,IAAe,KAAKpE,QAAA;IAElD4E,OAAA,CAAQZ,CAAA,GAAIY,OAAA,CAAQzD,KAAA,GAAQgD,SAAA,KAE5BS,OAAA,CAAQzD,KAAA,GAAQgD,SAAA,GAAYS,OAAA,CAAQZ,CAAA,GAGpCY,OAAA,CAAQX,CAAA,GAAIW,OAAA,CAAQxD,MAAA,GAASgD,UAAA,KAE7BQ,OAAA,CAAQxD,MAAA,GAASgD,UAAA,GAAaQ,OAAA,CAAQX,CAAA;EAE9C;EAAA;AAAA;AAAA;AAAA;AAAA;EAOQV,SAAkCN,aAAA,EAC1C;IAGQ,IAAAlC,GAAA,GAAM,KAAKX,IAAA,CAAKyF,GAAA,CAAI;IAEnB9E,GAAA,KAEDA,GAAA,GAAMC,QAAA,CAASC,aAAA,CAAc,QAAQ,GAErCF,GAAA,CAAIG,KAAA,CAAMC,KAAA,GAAQ,GAAG7B,cAAc,MACnCyB,GAAA,CAAIG,KAAA,CAAME,MAAA,GAAS,GAAG9B,cAAc,MACpCyB,GAAA,CAAIG,KAAA,CAAMgB,eAAA,GAAkB,KAAKjC,KAAA,GAAQ,0BAA0B,eACnEc,GAAA,CAAIG,KAAA,CAAMG,QAAA,GAAW,YACrBN,GAAA,CAAIG,KAAA,CAAMM,MAAA,GAAS/B,gBAAA,CAAiBgC,QAAA,CAAS,GAC7CV,GAAA,CAAIG,KAAA,CAAM4E,WAAA,GAAc,QAGpBC,SAAA,CAAUC,SAAA,CAAUC,WAAA,CAAY,EAAEC,QAAA,CAAS,QAAQ,IAGnDnF,GAAA,CAAIyE,YAAA,CAAa,aAAa,KAAK,IAInCzE,GAAA,CAAIyE,YAAA,CAAa,aAAa,QAAQ,GAGtCO,SAAA,CAAUC,SAAA,CAAUG,KAAA,CAAM,cAAc,IAGxCpF,GAAA,CAAIyE,YAAA,CAAa,iBAAiB,WAAW,IAK7CzE,GAAA,CAAIyE,YAAA,CAAa,iBAAiB,MAAM,GAG5CzE,GAAA,CAAIe,gBAAA,CAAiB,SAAS,KAAKsE,QAAA,CAASzE,IAAA,CAAK,IAAI,CAAC,GACtDZ,GAAA,CAAIe,gBAAA,CAAiB,SAAS,KAAKuE,QAAA,CAAS1E,IAAA,CAAK,IAAI,CAAC,GACtDZ,GAAA,CAAIe,gBAAA,CAAiB,YAAY,KAAKwE,WAAA,CAAY3E,IAAA,CAAK,IAAI,CAAC,IAIhEZ,GAAA,CAAIG,KAAA,CAAMqF,aAAA,GAAgBtD,aAAA,CAAcuD,uBAAA,EAExCzF,GAAA,CAAI6E,IAAA,GAAO3C,aAAA,CAAcwD,cAAA,EAErBxD,aAAA,CAAcoC,eAAA,IAAmBpC,aAAA,CAAcoC,eAAA,KAAoB,OAEnEtE,GAAA,CAAIoB,KAAA,GAAQc,aAAA,CAAcoC,eAAA,IAErB,CAACpC,aAAA,CAAcsC,cAAA,IACZtC,aAAA,CAAcsC,cAAA,KAAmB,UAEzCxE,GAAA,CAAIoB,KAAA,GAAQ,iBAAiBc,aAAA,CAAcwC,QAAQ,KAGnDxC,aAAA,CAAcsC,cAAA,IACXtC,aAAA,CAAcsC,cAAA,KAAmB,QAEpCxE,GAAA,CAAIyE,YAAA,CAAa,cAAcvC,aAAA,CAAcsC,cAAc,GAG3D,KAAKtF,KAAA,IAAO,KAAKyF,eAAA,CAAgB3E,GAAG,GAExCkC,aAAA,CAAcK,iBAAA,GAAoB,IAClCL,aAAA,CAAcyB,cAAA,GAAiB3D,GAAA,EAC/BA,GAAA,CAAIkC,aAAA,GAAgBA,aAAA,EAEpB,KAAK3C,QAAA,CAASqE,IAAA,CAAK1B,aAAa,GAChC,KAAKlC,GAAA,CAAIwB,WAAA,CAAYU,aAAA,CAAcyB,cAAc,GACjDzB,aAAA,CAAcyB,cAAA,CAAee,QAAA,GAAWxC,aAAA,CAAcwC,QAAA;EAC1D;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQQiB,eAAeC,CAAA,EAAYf,IAAA,EACnC;IACU;QAAE3C,aAAA,EAAe2D;MAAA,IAAWD,CAAA,CAAEC,MAAA;MAC9BC,OAAA,GAAU,KAAK7G,QAAA,CAAS8G,MAAA,CAAOC,YAAA;MAC/BC,KAAA,GAAwBC,MAAA,CAAOC,MAAA,CAAO,IAAIC,cAAA,CAAeN,OAAO,GAAG;QAAED;MAAA,CAAQ;IAEnFC,OAAA,CAAQO,UAAA,GAAa,KAAKpH,QAAA,CAAS+D,kBAAA,EACnC6B,IAAA,CAAKyB,OAAA,CAASC,KAAA,IAAST,OAAA,CAAQU,aAAA,CAAcP,KAAA,EAAOM,KAAI,CAAC;EAC7D;EAAA;AAAA;AAAA;AAAA;AAAA;EAOQlB,SAASO,CAAA,EACjB;IACI,KAAKD,cAAA,CAAeC,CAAA,EAAG,CAAC,SAAS,cAAc,KAAK,CAAC;EACzD;EAAA;AAAA;AAAA;AAAA;AAAA;EAOQN,SAASM,CAAA,EACjB;IACUA,CAAA,CAAEC,MAAA,CAAmBtB,YAAA,CAAa,WAAW,KAE9CqB,CAAA,CAAEC,MAAA,CAAmBpB,YAAA,CAAa,aAAa,WAAW,GAG/D,KAAKkB,cAAA,CAAeC,CAAA,EAAG,CAAC,WAAW,CAAC;EACxC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOQL,YAAYK,CAAA,EACpB;IACUA,CAAA,CAAEC,MAAA,CAAmBtB,YAAA,CAAa,WAAW,KAE9CqB,CAAA,CAAEC,MAAA,CAAmBpB,YAAA,CAAa,aAAa,QAAQ,GAG5D,KAAKkB,cAAA,CAAeC,CAAA,EAAG,CAAC,UAAU,CAAC;EACvC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOQjF,WAAWiF,CAAA,EACnB;IACQA,CAAA,CAAEa,OAAA,KAAYnI,YAAA,IAKlB,KAAK+C,QAAA,CAAS;EAClB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOQR,aAAa+E,CAAA,EACrB;IACQA,CAAA,CAAEc,SAAA,KAAc,KAAKd,CAAA,CAAEe,SAAA,KAAc,KAKzC,KAAK5E,UAAA;EACT;EAAA;EAGO6E,QAAA,EACP;IACS,KAAAtF,gBAAA,CACL,QAAKtB,GAAA,GAAM,MAEXc,UAAA,CAAWb,QAAA,CAASyB,mBAAA,CAAoB,aAAa,KAAKb,YAAA,EAAc,EAAI,GAC5EC,UAAA,CAAWY,mBAAA,CAAoB,WAAW,KAAKf,UAAU,GAEzD,KAAKtB,IAAA,GAAO,MACZ,KAAKE,QAAA,GAAW,MAChB,KAAKN,QAAA,GAAW;EACpB;AACJ;AA/iBaF,oBAAA,CAGF8H,SAAA,GAA+B;EAClCC,IAAA,EAAM;EACNjC,IAAA,EAAM,CACFkC,aAAA,CAAcC,cAAA,EACdD,aAAA,CAAcE,oBAAA;AAEtB;AAwiBJC,UAAA,CAAWC,GAAA,CAAIpI,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}