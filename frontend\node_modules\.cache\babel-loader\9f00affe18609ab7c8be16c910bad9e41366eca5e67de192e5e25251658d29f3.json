{"ast": null, "code": "import { BitmapFont } from \"./BitmapFont.mjs\";\nimport { BitmapFontData } from \"./BitmapFontData.mjs\";\nimport { BitmapText } from \"./BitmapText.mjs\";\nimport \"./BitmapTextStyle.mjs\";\nimport { autoDetectFormat } from \"./formats/index.mjs\";\nimport { loadBitmapFont } from \"./loadBitmapFont.mjs\";\nimport { TextFormat } from \"./formats/TextFormat.mjs\";\nimport { XMLFormat } from \"./formats/XMLFormat.mjs\";\nimport { XMLStringFormat } from \"./formats/XMLStringFormat.mjs\";\nexport { BitmapFont, BitmapFontData, BitmapText, TextFormat, XMLFormat, XMLStringFormat, autoDetectFormat, loadBitmapFont };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import { BitmapFont } from \"./BitmapFont.mjs\";\nimport { BitmapFontData } from \"./BitmapFontData.mjs\";\nimport { BitmapText } from \"./BitmapText.mjs\";\nimport \"./BitmapTextStyle.mjs\";\nimport { autoDetectFormat } from \"./formats/index.mjs\";\nimport { loadBitmapFont } from \"./loadBitmapFont.mjs\";\nimport { TextFormat } from \"./formats/TextFormat.mjs\";\nimport { XMLFormat } from \"./formats/XMLFormat.mjs\";\nimport { XMLStringFormat } from \"./formats/XMLStringFormat.mjs\";\nexport {\n  BitmapFont,\n  BitmapFontData,\n  BitmapText,\n  TextFormat,\n  XMLFormat,\n  XMLStringFormat,\n  autoDetectFormat,\n  loadBitmapFont\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}