{"ast": null, "code": "import { ALPHA_MODES } from \"@pixi/constants\";\nimport { settings } from \"@pixi/settings\";\nimport { BaseImageResource } from \"./BaseImageResource.mjs\";\nclass ImageBitmapResource extends BaseImageResource {\n  /**\n   * @param source - ImageBitmap or URL to use.\n   * @param options - Options to use.\n   */\n  constructor(source, options) {\n    options = options || {};\n    let baseSource, url, ownsImageBitmap;\n    typeof source == \"string\" ? (baseSource = ImageBitmapResource.EMPTY, url = source, ownsImageBitmap = !0) : (baseSource = source, url = null, ownsImageBitmap = !1), super(baseSource), this.url = url, this.crossOrigin = options.crossOrigin ?? !0, this.alphaMode = typeof options.alphaMode == \"number\" ? options.alphaMode : null, this.ownsImageBitmap = options.ownsImageBitmap ?? ownsImageBitmap, this._load = null, options.autoLoad !== !1 && this.load();\n  }\n  load() {\n    return this._load ? this._load : (this._load = new Promise(async (resolve, reject) => {\n      if (this.url === null) {\n        resolve(this);\n        return;\n      }\n      try {\n        const response = await settings.ADAPTER.fetch(this.url, {\n          mode: this.crossOrigin ? \"cors\" : \"no-cors\"\n        });\n        if (this.destroyed) return;\n        const imageBlob = await response.blob();\n        if (this.destroyed) return;\n        const imageBitmap = await createImageBitmap(imageBlob, {\n          premultiplyAlpha: this.alphaMode === null || this.alphaMode === ALPHA_MODES.UNPACK ? \"premultiply\" : \"none\"\n        });\n        if (this.destroyed) {\n          imageBitmap.close();\n          return;\n        }\n        this.source = imageBitmap, this.update(), resolve(this);\n      } catch (e) {\n        if (this.destroyed) return;\n        reject(e), this.onError.emit(e);\n      }\n    }), this._load);\n  }\n  /**\n   * Upload the image bitmap resource to GPU.\n   * @param renderer - Renderer to upload to\n   * @param baseTexture - BaseTexture for this resource\n   * @param glTexture - GLTexture to use\n   * @returns {boolean} true is success\n   */\n  upload(renderer, baseTexture, glTexture) {\n    return this.source instanceof ImageBitmap ? (typeof this.alphaMode == \"number\" && (baseTexture.alphaMode = this.alphaMode), super.upload(renderer, baseTexture, glTexture)) : (this.load(), !1);\n  }\n  /** Destroys this resource. */\n  dispose() {\n    this.ownsImageBitmap && this.source instanceof ImageBitmap && this.source.close(), super.dispose(), this._load = null;\n  }\n  /**\n   * Used to auto-detect the type of resource.\n   * @param {*} source - The source object\n   * @returns {boolean} `true` if current environment support ImageBitmap, and source is string or ImageBitmap\n   */\n  static test(source) {\n    return !!globalThis.createImageBitmap && typeof ImageBitmap < \"u\" && (typeof source == \"string\" || source instanceof ImageBitmap);\n  }\n  /**\n   * ImageBitmap cannot be created synchronously, so a empty placeholder canvas is needed when loading from URLs.\n   * Only for internal usage.\n   * @returns The cached placeholder canvas.\n   */\n  static get EMPTY() {\n    return ImageBitmapResource._EMPTY = ImageBitmapResource._EMPTY ?? settings.ADAPTER.createCanvas(0, 0), ImageBitmapResource._EMPTY;\n  }\n}\nexport { ImageBitmapResource };", "map": {"version": 3, "names": ["ImageBitmapResource", "BaseImageResource", "constructor", "source", "options", "baseSource", "url", "ownsImageBitmap", "EMPTY", "crossOrigin", "alphaMode", "_load", "autoLoad", "load", "Promise", "resolve", "reject", "response", "settings", "ADAPTER", "fetch", "mode", "destroyed", "imageBlob", "blob", "imageBitmap", "createImageBitmap", "premultiplyAlpha", "ALPHA_MODES", "UNPACK", "close", "update", "e", "onError", "emit", "upload", "renderer", "baseTexture", "glTexture", "ImageBitmap", "dispose", "test", "globalThis", "_EMPTY", "createCanvas"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\textures\\resources\\ImageBitmapResource.ts"], "sourcesContent": ["import { ALPHA_MODES } from '@pixi/constants';\nimport { settings } from '@pixi/settings';\nimport { BaseImageResource } from './BaseImageResource';\n\nimport type { ICanvas } from '@pixi/settings';\nimport type { Renderer } from '../../Renderer';\nimport type { BaseTexture } from '../BaseTexture';\nimport type { GLTexture } from '../GLTexture';\n\n/**\n * Options for ImageBitmapResource.\n * @memberof PIXI\n */\nexport interface IImageBitmapResourceOptions\n{\n    /** Start loading process automatically when constructed. */\n    autoLoad?: boolean;\n\n    /** Load image using cross origin. */\n    crossOrigin?: boolean;\n\n    /** Alpha mode used when creating the ImageBitmap. */\n    alphaMode?: ALPHA_MODES;\n\n    /**\n     * Whether the underlying ImageBitmap is owned by the {@link PIXI.ImageBitmapResource}. When set to `true`,\n     * the underlying ImageBitmap will be disposed automatically when disposing {@link PIXI.ImageBitmapResource}.\n     * If this option is not set, whether it owns the underlying ImageBitmap is determained by the type of `source`\n     * used when constructing {@link PIXI.ImageBitmapResource}:\n     * - When `source` is `ImageBitmap`, the underlying ImageBitmap is not owned by default.\n     * - When `source` is `string` (a URL), the underlying ImageBitmap is owned by default.\n     * @see PIXI.ImageBitmapResource.ownsImageBitmap\n     */\n    ownsImageBitmap?: boolean;\n}\n\n/**\n * Resource type for ImageBitmap.\n * @memberof PIXI\n */\nexport class ImageBitmapResource extends BaseImageResource\n{\n    /** URL of the image source. */\n    url: string | null;\n\n    /**\n     * Load image using cross origin.\n     * @default false\n     */\n    crossOrigin: boolean;\n\n    /**\n     * Controls texture alphaMode field\n     * Copies from options\n     * Default is `null`, copies option from baseTexture\n     * @readonly\n     */\n    alphaMode: ALPHA_MODES | null;\n\n    /**\n     * Whether the underlying ImageBitmap is owned by the ImageBitmapResource.\n     * @see PIXI.IImageBitmapResourceOptions.ownsImageBitmap\n     */\n    private ownsImageBitmap: boolean;\n\n    /**\n     * Promise when loading.\n     * @default null\n     */\n    private _load: Promise<this>;\n\n    /**\n     * @param source - ImageBitmap or URL to use.\n     * @param options - Options to use.\n     */\n    constructor(source: ImageBitmap | string, options?: IImageBitmapResourceOptions)\n    {\n        options = options || {};\n\n        let baseSource;\n        let url;\n        let ownsImageBitmap;\n\n        if (typeof source === 'string')\n        {\n            baseSource = ImageBitmapResource.EMPTY;\n            url = source;\n            ownsImageBitmap = true;\n        }\n        else\n        {\n            baseSource = source;\n            url = null;\n            ownsImageBitmap = false;\n        }\n        // Using super() in if() can cause transpilation problems in some cases, so take it out of if().\n        // See https://github.com/pixijs/pixijs/pull/9093 for details.\n        super(baseSource);\n        this.url = url;\n\n        this.crossOrigin = options.crossOrigin ?? true;\n        this.alphaMode = typeof options.alphaMode === 'number' ? options.alphaMode : null;\n        this.ownsImageBitmap = options.ownsImageBitmap ?? ownsImageBitmap;\n\n        this._load = null;\n\n        if (options.autoLoad !== false)\n        {\n            this.load();\n        }\n    }\n\n    load(): Promise<this>\n    {\n        if (this._load)\n        {\n            return this._load;\n        }\n\n        this._load = new Promise(async (resolve, reject) =>\n        {\n            if (this.url === null)\n            {\n                resolve(this);\n\n                return;\n            }\n\n            try\n            {\n                const response = await settings.ADAPTER.fetch(this.url, {\n                    mode: this.crossOrigin ? 'cors' : 'no-cors'\n                });\n\n                if (this.destroyed) return;\n\n                const imageBlob = await response.blob();\n\n                if (this.destroyed) return;\n\n                const imageBitmap = await createImageBitmap(imageBlob, {\n                    premultiplyAlpha: this.alphaMode === null || this.alphaMode === ALPHA_MODES.UNPACK\n                        ? 'premultiply' : 'none',\n                });\n\n                if (this.destroyed)\n                {\n                    imageBitmap.close();\n\n                    return;\n                }\n\n                this.source = imageBitmap;\n                this.update();\n\n                resolve(this);\n            }\n            catch (e)\n            {\n                if (this.destroyed) return;\n\n                reject(e);\n                this.onError.emit(e);\n            }\n        });\n\n        return this._load;\n    }\n\n    /**\n     * Upload the image bitmap resource to GPU.\n     * @param renderer - Renderer to upload to\n     * @param baseTexture - BaseTexture for this resource\n     * @param glTexture - GLTexture to use\n     * @returns {boolean} true is success\n     */\n    override upload(renderer: Renderer, baseTexture: BaseTexture, glTexture: GLTexture): boolean\n    {\n        if (!(this.source instanceof ImageBitmap))\n        {\n            this.load();\n\n            return false;\n        }\n\n        if (typeof this.alphaMode === 'number')\n        {\n            baseTexture.alphaMode = this.alphaMode;\n        }\n\n        return super.upload(renderer, baseTexture, glTexture);\n    }\n\n    /** Destroys this resource. */\n    override dispose(): void\n    {\n        if (this.ownsImageBitmap && this.source instanceof ImageBitmap)\n        {\n            this.source.close();\n        }\n\n        super.dispose();\n\n        this._load = null;\n    }\n\n    /**\n     * Used to auto-detect the type of resource.\n     * @param {*} source - The source object\n     * @returns {boolean} `true` if current environment support ImageBitmap, and source is string or ImageBitmap\n     */\n    static override test(source: unknown): source is string | ImageBitmap\n    {\n        return !!globalThis.createImageBitmap && typeof ImageBitmap !== 'undefined'\n            && (typeof source === 'string' || source instanceof ImageBitmap);\n    }\n\n    /**\n     * Cached empty placeholder canvas.\n     * @see EMPTY\n     */\n    private static _EMPTY: ICanvas;\n\n    /**\n     * ImageBitmap cannot be created synchronously, so a empty placeholder canvas is needed when loading from URLs.\n     * Only for internal usage.\n     * @returns The cached placeholder canvas.\n     */\n    private static get EMPTY(): ICanvas\n    {\n        ImageBitmapResource._EMPTY = ImageBitmapResource._EMPTY ?? settings.ADAPTER.createCanvas(0, 0);\n\n        return ImageBitmapResource._EMPTY;\n    }\n}\n"], "mappings": ";;;AAwCO,MAAMA,mBAAA,SAA4BC,iBAAA,CACzC;EAAA;AAAA;AAAA;AAAA;EAkCIC,YAAYC,MAAA,EAA8BC,OAAA,EAC1C;IACIA,OAAA,GAAUA,OAAA,IAAW;IAErB,IAAIC,UAAA,EACAC,GAAA,EACAC,eAAA;IAEA,OAAOJ,MAAA,IAAW,YAElBE,UAAA,GAAaL,mBAAA,CAAoBQ,KAAA,EACjCF,GAAA,GAAMH,MAAA,EACNI,eAAA,GAAkB,OAIlBF,UAAA,GAAaF,MAAA,EACbG,GAAA,GAAM,MACNC,eAAA,GAAkB,KAItB,MAAMF,UAAU,GAChB,KAAKC,GAAA,GAAMA,GAAA,EAEX,KAAKG,WAAA,GAAcL,OAAA,CAAQK,WAAA,IAAe,IAC1C,KAAKC,SAAA,GAAY,OAAON,OAAA,CAAQM,SAAA,IAAc,WAAWN,OAAA,CAAQM,SAAA,GAAY,MAC7E,KAAKH,eAAA,GAAkBH,OAAA,CAAQG,eAAA,IAAmBA,eAAA,EAElD,KAAKI,KAAA,GAAQ,MAETP,OAAA,CAAQQ,QAAA,KAAa,MAErB,KAAKC,IAAA;EAEb;EAEAA,KAAA,EACA;IACQ,YAAKF,KAAA,GAEE,KAAKA,KAAA,IAGhB,KAAKA,KAAA,GAAQ,IAAIG,OAAA,CAAQ,OAAOC,OAAA,EAASC,MAAA,KACzC;MACQ,SAAKV,GAAA,KAAQ,MACjB;QACIS,OAAA,CAAQ,IAAI;QAEZ;MACJ;MAGA;QACI,MAAME,QAAA,GAAW,MAAMC,QAAA,CAASC,OAAA,CAAQC,KAAA,CAAM,KAAKd,GAAA,EAAK;UACpDe,IAAA,EAAM,KAAKZ,WAAA,GAAc,SAAS;QAAA,CACrC;QAED,IAAI,KAAKa,SAAA,EAAW;QAEd,MAAAC,SAAA,GAAY,MAAMN,QAAA,CAASO,IAAA;QAEjC,IAAI,KAAKF,SAAA,EAAW;QAEd,MAAAG,WAAA,GAAc,MAAMC,iBAAA,CAAkBH,SAAA,EAAW;UACnDI,gBAAA,EAAkB,KAAKjB,SAAA,KAAc,QAAQ,KAAKA,SAAA,KAAckB,WAAA,CAAYC,MAAA,GACtE,gBAAgB;QAAA,CACzB;QAED,IAAI,KAAKP,SAAA,EACT;UACIG,WAAA,CAAYK,KAAA,CAAM;UAElB;QACJ;QAEA,KAAK3B,MAAA,GAASsB,WAAA,EACd,KAAKM,MAAA,IAELhB,OAAA,CAAQ,IAAI;MAAA,SAETiB,CAAA,EACP;QACI,IAAI,KAAKV,SAAA,EAAW;QAEpBN,MAAA,CAAOgB,CAAC,GACR,KAAKC,OAAA,CAAQC,IAAA,CAAKF,CAAC;MACvB;IAAA,CACH,GAEM,KAAKrB,KAAA;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASSwB,OAAOC,QAAA,EAAoBC,WAAA,EAA0BC,SAAA,EAC9D;IACI,OAAM,KAAKnC,MAAA,YAAkBoC,WAAA,IAOzB,OAAO,KAAK7B,SAAA,IAAc,aAE1B2B,WAAA,CAAY3B,SAAA,GAAY,KAAKA,SAAA,GAG1B,MAAMyB,MAAA,CAAOC,QAAA,EAAUC,WAAA,EAAaC,SAAS,MAVhD,KAAKzB,IAAA,IAEE;EASf;EAAA;EAGS2B,QAAA,EACT;IACQ,KAAKjC,eAAA,IAAmB,KAAKJ,MAAA,YAAkBoC,WAAA,IAE/C,KAAKpC,MAAA,CAAO2B,KAAA,CAGhB,SAAMU,OAAA,CAAQ,GAEd,KAAK7B,KAAA,GAAQ;EACjB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,OAAgB8B,KAAKtC,MAAA,EACrB;IACW,QAAC,CAACuC,UAAA,CAAWhB,iBAAA,IAAqB,OAAOa,WAAA,GAAgB,QACxD,OAAOpC,MAAA,IAAW,YAAYA,MAAA,YAAkBoC,WAAA;EAC5D;EAAA;AAAA;AAAA;AAAA;AAAA;EAaA,WAAmB/B,MAAA,EACnB;IACwB,OAAAR,mBAAA,CAAA2C,MAAA,GAAS3C,mBAAA,CAAoB2C,MAAA,IAAUzB,QAAA,CAASC,OAAA,CAAQyB,YAAA,CAAa,GAAG,CAAC,GAEtF5C,mBAAA,CAAoB2C,MAAA;EAC/B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}