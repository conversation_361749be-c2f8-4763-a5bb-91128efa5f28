{"ast": null, "code": "var TEXT_GRADIENT = /* @__PURE__ */(TEXT_GRADIENT2 => (TEXT_GRADIENT2[TEXT_GRADIENT2.LINEAR_VERTICAL = 0] = \"LINEAR_VERTICAL\", TEXT_GRADIENT2[TEXT_GRADIENT2.LINEAR_HORIZONTAL = 1] = \"LINEAR_HORIZONTAL\", TEXT_GRADIENT2))(TEXT_GRADIENT || {});\nexport { TEXT_GRADIENT };", "map": {"version": 3, "names": ["TEXT_GRADIENT", "TEXT_GRADIENT2", "LINEAR_VERTICAL", "LINEAR_HORIZONTAL"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\text\\src\\const.ts"], "sourcesContent": ["/**\n * Constants that define the type of gradient on text.\n * @static\n * @memberof PIXI\n * @type {object}\n */\nexport enum TEXT_GRADIENT\n// eslint-disable-next-line @typescript-eslint/indent\n{\n    /**\n     * Vertical gradient\n     * @default 0\n     */\n    LINEAR_VERTICAL = 0,\n    /**\n     * Linear gradient\n     * @default 1\n     */\n    LINEAR_HORIZONTAL = 1\n}\n"], "mappings": "AAMY,IAAAA,aAAA,mBAAAC,cAAA,KAORA,cAAA,CAAAA,cAAA,CAAAC,eAAA,GAAkB,CAAlB,uBAKAD,cAAA,CAAAA,cAAA,CAAAE,iBAAA,GAAoB,CAApB,yBAZQF,cAAA,GAAAD,aAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}