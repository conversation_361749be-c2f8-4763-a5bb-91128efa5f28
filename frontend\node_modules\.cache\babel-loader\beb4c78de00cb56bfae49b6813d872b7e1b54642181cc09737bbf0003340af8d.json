{"ast": null, "code": "'use strict';\n\nmodule.exports = earcut;\nmodule.exports.default = earcut;\nfunction earcut(data, holeIndices, dim) {\n  dim = dim || 2;\n  var hasHoles = holeIndices && holeIndices.length,\n    outerLen = hasHoles ? holeIndices[0] * dim : data.length,\n    outerNode = linkedList(data, 0, outerLen, dim, true),\n    triangles = [];\n  if (!outerNode || outerNode.next === outerNode.prev) return triangles;\n  var minX, minY, maxX, maxY, x, y, invSize;\n  if (hasHoles) outerNode = eliminateHoles(data, holeIndices, outerNode, dim);\n\n  // if the shape is not too simple, we'll use z-order curve hash later; calculate polygon bbox\n  if (data.length > 80 * dim) {\n    minX = maxX = data[0];\n    minY = maxY = data[1];\n    for (var i = dim; i < outerLen; i += dim) {\n      x = data[i];\n      y = data[i + 1];\n      if (x < minX) minX = x;\n      if (y < minY) minY = y;\n      if (x > maxX) maxX = x;\n      if (y > maxY) maxY = y;\n    }\n\n    // minX, minY and invSize are later used to transform coords into integers for z-order calculation\n    invSize = Math.max(maxX - minX, maxY - minY);\n    invSize = invSize !== 0 ? 32767 / invSize : 0;\n  }\n  earcutLinked(outerNode, triangles, dim, minX, minY, invSize, 0);\n  return triangles;\n}\n\n// create a circular doubly linked list from polygon points in the specified winding order\nfunction linkedList(data, start, end, dim, clockwise) {\n  var i, last;\n  if (clockwise === signedArea(data, start, end, dim) > 0) {\n    for (i = start; i < end; i += dim) last = insertNode(i, data[i], data[i + 1], last);\n  } else {\n    for (i = end - dim; i >= start; i -= dim) last = insertNode(i, data[i], data[i + 1], last);\n  }\n  if (last && equals(last, last.next)) {\n    removeNode(last);\n    last = last.next;\n  }\n  return last;\n}\n\n// eliminate colinear or duplicate points\nfunction filterPoints(start, end) {\n  if (!start) return start;\n  if (!end) end = start;\n  var p = start,\n    again;\n  do {\n    again = false;\n    if (!p.steiner && (equals(p, p.next) || area(p.prev, p, p.next) === 0)) {\n      removeNode(p);\n      p = end = p.prev;\n      if (p === p.next) break;\n      again = true;\n    } else {\n      p = p.next;\n    }\n  } while (again || p !== end);\n  return end;\n}\n\n// main ear slicing loop which triangulates a polygon (given as a linked list)\nfunction earcutLinked(ear, triangles, dim, minX, minY, invSize, pass) {\n  if (!ear) return;\n\n  // interlink polygon nodes in z-order\n  if (!pass && invSize) indexCurve(ear, minX, minY, invSize);\n  var stop = ear,\n    prev,\n    next;\n\n  // iterate through ears, slicing them one by one\n  while (ear.prev !== ear.next) {\n    prev = ear.prev;\n    next = ear.next;\n    if (invSize ? isEarHashed(ear, minX, minY, invSize) : isEar(ear)) {\n      // cut off the triangle\n      triangles.push(prev.i / dim | 0);\n      triangles.push(ear.i / dim | 0);\n      triangles.push(next.i / dim | 0);\n      removeNode(ear);\n\n      // skipping the next vertex leads to less sliver triangles\n      ear = next.next;\n      stop = next.next;\n      continue;\n    }\n    ear = next;\n\n    // if we looped through the whole remaining polygon and can't find any more ears\n    if (ear === stop) {\n      // try filtering points and slicing again\n      if (!pass) {\n        earcutLinked(filterPoints(ear), triangles, dim, minX, minY, invSize, 1);\n\n        // if this didn't work, try curing all small self-intersections locally\n      } else if (pass === 1) {\n        ear = cureLocalIntersections(filterPoints(ear), triangles, dim);\n        earcutLinked(ear, triangles, dim, minX, minY, invSize, 2);\n\n        // as a last resort, try splitting the remaining polygon into two\n      } else if (pass === 2) {\n        splitEarcut(ear, triangles, dim, minX, minY, invSize);\n      }\n      break;\n    }\n  }\n}\n\n// check whether a polygon node forms a valid ear with adjacent nodes\nfunction isEar(ear) {\n  var a = ear.prev,\n    b = ear,\n    c = ear.next;\n  if (area(a, b, c) >= 0) return false; // reflex, can't be an ear\n\n  // now make sure we don't have other points inside the potential ear\n  var ax = a.x,\n    bx = b.x,\n    cx = c.x,\n    ay = a.y,\n    by = b.y,\n    cy = c.y;\n\n  // triangle bbox; min & max are calculated like this for speed\n  var x0 = ax < bx ? ax < cx ? ax : cx : bx < cx ? bx : cx,\n    y0 = ay < by ? ay < cy ? ay : cy : by < cy ? by : cy,\n    x1 = ax > bx ? ax > cx ? ax : cx : bx > cx ? bx : cx,\n    y1 = ay > by ? ay > cy ? ay : cy : by > cy ? by : cy;\n  var p = c.next;\n  while (p !== a) {\n    if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 && pointInTriangle(ax, ay, bx, by, cx, cy, p.x, p.y) && area(p.prev, p, p.next) >= 0) return false;\n    p = p.next;\n  }\n  return true;\n}\nfunction isEarHashed(ear, minX, minY, invSize) {\n  var a = ear.prev,\n    b = ear,\n    c = ear.next;\n  if (area(a, b, c) >= 0) return false; // reflex, can't be an ear\n\n  var ax = a.x,\n    bx = b.x,\n    cx = c.x,\n    ay = a.y,\n    by = b.y,\n    cy = c.y;\n\n  // triangle bbox; min & max are calculated like this for speed\n  var x0 = ax < bx ? ax < cx ? ax : cx : bx < cx ? bx : cx,\n    y0 = ay < by ? ay < cy ? ay : cy : by < cy ? by : cy,\n    x1 = ax > bx ? ax > cx ? ax : cx : bx > cx ? bx : cx,\n    y1 = ay > by ? ay > cy ? ay : cy : by > cy ? by : cy;\n\n  // z-order range for the current triangle bbox;\n  var minZ = zOrder(x0, y0, minX, minY, invSize),\n    maxZ = zOrder(x1, y1, minX, minY, invSize);\n  var p = ear.prevZ,\n    n = ear.nextZ;\n\n  // look for points inside the triangle in both directions\n  while (p && p.z >= minZ && n && n.z <= maxZ) {\n    if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 && p !== a && p !== c && pointInTriangle(ax, ay, bx, by, cx, cy, p.x, p.y) && area(p.prev, p, p.next) >= 0) return false;\n    p = p.prevZ;\n    if (n.x >= x0 && n.x <= x1 && n.y >= y0 && n.y <= y1 && n !== a && n !== c && pointInTriangle(ax, ay, bx, by, cx, cy, n.x, n.y) && area(n.prev, n, n.next) >= 0) return false;\n    n = n.nextZ;\n  }\n\n  // look for remaining points in decreasing z-order\n  while (p && p.z >= minZ) {\n    if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 && p !== a && p !== c && pointInTriangle(ax, ay, bx, by, cx, cy, p.x, p.y) && area(p.prev, p, p.next) >= 0) return false;\n    p = p.prevZ;\n  }\n\n  // look for remaining points in increasing z-order\n  while (n && n.z <= maxZ) {\n    if (n.x >= x0 && n.x <= x1 && n.y >= y0 && n.y <= y1 && n !== a && n !== c && pointInTriangle(ax, ay, bx, by, cx, cy, n.x, n.y) && area(n.prev, n, n.next) >= 0) return false;\n    n = n.nextZ;\n  }\n  return true;\n}\n\n// go through all polygon nodes and cure small local self-intersections\nfunction cureLocalIntersections(start, triangles, dim) {\n  var p = start;\n  do {\n    var a = p.prev,\n      b = p.next.next;\n    if (!equals(a, b) && intersects(a, p, p.next, b) && locallyInside(a, b) && locallyInside(b, a)) {\n      triangles.push(a.i / dim | 0);\n      triangles.push(p.i / dim | 0);\n      triangles.push(b.i / dim | 0);\n\n      // remove two nodes involved\n      removeNode(p);\n      removeNode(p.next);\n      p = start = b;\n    }\n    p = p.next;\n  } while (p !== start);\n  return filterPoints(p);\n}\n\n// try splitting polygon into two and triangulate them independently\nfunction splitEarcut(start, triangles, dim, minX, minY, invSize) {\n  // look for a valid diagonal that divides the polygon into two\n  var a = start;\n  do {\n    var b = a.next.next;\n    while (b !== a.prev) {\n      if (a.i !== b.i && isValidDiagonal(a, b)) {\n        // split the polygon in two by the diagonal\n        var c = splitPolygon(a, b);\n\n        // filter colinear points around the cuts\n        a = filterPoints(a, a.next);\n        c = filterPoints(c, c.next);\n\n        // run earcut on each half\n        earcutLinked(a, triangles, dim, minX, minY, invSize, 0);\n        earcutLinked(c, triangles, dim, minX, minY, invSize, 0);\n        return;\n      }\n      b = b.next;\n    }\n    a = a.next;\n  } while (a !== start);\n}\n\n// link every hole into the outer loop, producing a single-ring polygon without holes\nfunction eliminateHoles(data, holeIndices, outerNode, dim) {\n  var queue = [],\n    i,\n    len,\n    start,\n    end,\n    list;\n  for (i = 0, len = holeIndices.length; i < len; i++) {\n    start = holeIndices[i] * dim;\n    end = i < len - 1 ? holeIndices[i + 1] * dim : data.length;\n    list = linkedList(data, start, end, dim, false);\n    if (list === list.next) list.steiner = true;\n    queue.push(getLeftmost(list));\n  }\n  queue.sort(compareX);\n\n  // process holes from left to right\n  for (i = 0; i < queue.length; i++) {\n    outerNode = eliminateHole(queue[i], outerNode);\n  }\n  return outerNode;\n}\nfunction compareX(a, b) {\n  return a.x - b.x;\n}\n\n// find a bridge between vertices that connects hole with an outer ring and and link it\nfunction eliminateHole(hole, outerNode) {\n  var bridge = findHoleBridge(hole, outerNode);\n  if (!bridge) {\n    return outerNode;\n  }\n  var bridgeReverse = splitPolygon(bridge, hole);\n\n  // filter collinear points around the cuts\n  filterPoints(bridgeReverse, bridgeReverse.next);\n  return filterPoints(bridge, bridge.next);\n}\n\n// David Eberly's algorithm for finding a bridge between hole and outer polygon\nfunction findHoleBridge(hole, outerNode) {\n  var p = outerNode,\n    hx = hole.x,\n    hy = hole.y,\n    qx = -Infinity,\n    m;\n\n  // find a segment intersected by a ray from the hole's leftmost point to the left;\n  // segment's endpoint with lesser x will be potential connection point\n  do {\n    if (hy <= p.y && hy >= p.next.y && p.next.y !== p.y) {\n      var x = p.x + (hy - p.y) * (p.next.x - p.x) / (p.next.y - p.y);\n      if (x <= hx && x > qx) {\n        qx = x;\n        m = p.x < p.next.x ? p : p.next;\n        if (x === hx) return m; // hole touches outer segment; pick leftmost endpoint\n      }\n    }\n    p = p.next;\n  } while (p !== outerNode);\n  if (!m) return null;\n\n  // look for points inside the triangle of hole point, segment intersection and endpoint;\n  // if there are no points found, we have a valid connection;\n  // otherwise choose the point of the minimum angle with the ray as connection point\n\n  var stop = m,\n    mx = m.x,\n    my = m.y,\n    tanMin = Infinity,\n    tan;\n  p = m;\n  do {\n    if (hx >= p.x && p.x >= mx && hx !== p.x && pointInTriangle(hy < my ? hx : qx, hy, mx, my, hy < my ? qx : hx, hy, p.x, p.y)) {\n      tan = Math.abs(hy - p.y) / (hx - p.x); // tangential\n\n      if (locallyInside(p, hole) && (tan < tanMin || tan === tanMin && (p.x > m.x || p.x === m.x && sectorContainsSector(m, p)))) {\n        m = p;\n        tanMin = tan;\n      }\n    }\n    p = p.next;\n  } while (p !== stop);\n  return m;\n}\n\n// whether sector in vertex m contains sector in vertex p in the same coordinates\nfunction sectorContainsSector(m, p) {\n  return area(m.prev, m, p.prev) < 0 && area(p.next, m, m.next) < 0;\n}\n\n// interlink polygon nodes in z-order\nfunction indexCurve(start, minX, minY, invSize) {\n  var p = start;\n  do {\n    if (p.z === 0) p.z = zOrder(p.x, p.y, minX, minY, invSize);\n    p.prevZ = p.prev;\n    p.nextZ = p.next;\n    p = p.next;\n  } while (p !== start);\n  p.prevZ.nextZ = null;\n  p.prevZ = null;\n  sortLinked(p);\n}\n\n// Simon Tatham's linked list merge sort algorithm\n// http://www.chiark.greenend.org.uk/~sgtatham/algorithms/listsort.html\nfunction sortLinked(list) {\n  var i,\n    p,\n    q,\n    e,\n    tail,\n    numMerges,\n    pSize,\n    qSize,\n    inSize = 1;\n  do {\n    p = list;\n    list = null;\n    tail = null;\n    numMerges = 0;\n    while (p) {\n      numMerges++;\n      q = p;\n      pSize = 0;\n      for (i = 0; i < inSize; i++) {\n        pSize++;\n        q = q.nextZ;\n        if (!q) break;\n      }\n      qSize = inSize;\n      while (pSize > 0 || qSize > 0 && q) {\n        if (pSize !== 0 && (qSize === 0 || !q || p.z <= q.z)) {\n          e = p;\n          p = p.nextZ;\n          pSize--;\n        } else {\n          e = q;\n          q = q.nextZ;\n          qSize--;\n        }\n        if (tail) tail.nextZ = e;else list = e;\n        e.prevZ = tail;\n        tail = e;\n      }\n      p = q;\n    }\n    tail.nextZ = null;\n    inSize *= 2;\n  } while (numMerges > 1);\n  return list;\n}\n\n// z-order of a point given coords and inverse of the longer side of data bbox\nfunction zOrder(x, y, minX, minY, invSize) {\n  // coords are transformed into non-negative 15-bit integer range\n  x = (x - minX) * invSize | 0;\n  y = (y - minY) * invSize | 0;\n  x = (x | x << 8) & 0x00FF00FF;\n  x = (x | x << 4) & 0x0F0F0F0F;\n  x = (x | x << 2) & 0x33333333;\n  x = (x | x << 1) & 0x55555555;\n  y = (y | y << 8) & 0x00FF00FF;\n  y = (y | y << 4) & 0x0F0F0F0F;\n  y = (y | y << 2) & 0x33333333;\n  y = (y | y << 1) & 0x55555555;\n  return x | y << 1;\n}\n\n// find the leftmost node of a polygon ring\nfunction getLeftmost(start) {\n  var p = start,\n    leftmost = start;\n  do {\n    if (p.x < leftmost.x || p.x === leftmost.x && p.y < leftmost.y) leftmost = p;\n    p = p.next;\n  } while (p !== start);\n  return leftmost;\n}\n\n// check if a point lies within a convex triangle\nfunction pointInTriangle(ax, ay, bx, by, cx, cy, px, py) {\n  return (cx - px) * (ay - py) >= (ax - px) * (cy - py) && (ax - px) * (by - py) >= (bx - px) * (ay - py) && (bx - px) * (cy - py) >= (cx - px) * (by - py);\n}\n\n// check if a diagonal between two polygon nodes is valid (lies in polygon interior)\nfunction isValidDiagonal(a, b) {\n  return a.next.i !== b.i && a.prev.i !== b.i && !intersectsPolygon(a, b) && (\n  // dones't intersect other edges\n  locallyInside(a, b) && locallyInside(b, a) && middleInside(a, b) && (\n  // locally visible\n  area(a.prev, a, b.prev) || area(a, b.prev, b)) ||\n  // does not create opposite-facing sectors\n  equals(a, b) && area(a.prev, a, a.next) > 0 && area(b.prev, b, b.next) > 0); // special zero-length case\n}\n\n// signed area of a triangle\nfunction area(p, q, r) {\n  return (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y);\n}\n\n// check if two points are equal\nfunction equals(p1, p2) {\n  return p1.x === p2.x && p1.y === p2.y;\n}\n\n// check if two segments intersect\nfunction intersects(p1, q1, p2, q2) {\n  var o1 = sign(area(p1, q1, p2));\n  var o2 = sign(area(p1, q1, q2));\n  var o3 = sign(area(p2, q2, p1));\n  var o4 = sign(area(p2, q2, q1));\n  if (o1 !== o2 && o3 !== o4) return true; // general case\n\n  if (o1 === 0 && onSegment(p1, p2, q1)) return true; // p1, q1 and p2 are collinear and p2 lies on p1q1\n  if (o2 === 0 && onSegment(p1, q2, q1)) return true; // p1, q1 and q2 are collinear and q2 lies on p1q1\n  if (o3 === 0 && onSegment(p2, p1, q2)) return true; // p2, q2 and p1 are collinear and p1 lies on p2q2\n  if (o4 === 0 && onSegment(p2, q1, q2)) return true; // p2, q2 and q1 are collinear and q1 lies on p2q2\n\n  return false;\n}\n\n// for collinear points p, q, r, check if point q lies on segment pr\nfunction onSegment(p, q, r) {\n  return q.x <= Math.max(p.x, r.x) && q.x >= Math.min(p.x, r.x) && q.y <= Math.max(p.y, r.y) && q.y >= Math.min(p.y, r.y);\n}\nfunction sign(num) {\n  return num > 0 ? 1 : num < 0 ? -1 : 0;\n}\n\n// check if a polygon diagonal intersects any polygon segments\nfunction intersectsPolygon(a, b) {\n  var p = a;\n  do {\n    if (p.i !== a.i && p.next.i !== a.i && p.i !== b.i && p.next.i !== b.i && intersects(p, p.next, a, b)) return true;\n    p = p.next;\n  } while (p !== a);\n  return false;\n}\n\n// check if a polygon diagonal is locally inside the polygon\nfunction locallyInside(a, b) {\n  return area(a.prev, a, a.next) < 0 ? area(a, b, a.next) >= 0 && area(a, a.prev, b) >= 0 : area(a, b, a.prev) < 0 || area(a, a.next, b) < 0;\n}\n\n// check if the middle point of a polygon diagonal is inside the polygon\nfunction middleInside(a, b) {\n  var p = a,\n    inside = false,\n    px = (a.x + b.x) / 2,\n    py = (a.y + b.y) / 2;\n  do {\n    if (p.y > py !== p.next.y > py && p.next.y !== p.y && px < (p.next.x - p.x) * (py - p.y) / (p.next.y - p.y) + p.x) inside = !inside;\n    p = p.next;\n  } while (p !== a);\n  return inside;\n}\n\n// link two polygon vertices with a bridge; if the vertices belong to the same ring, it splits polygon into two;\n// if one belongs to the outer ring and another to a hole, it merges it into a single ring\nfunction splitPolygon(a, b) {\n  var a2 = new Node(a.i, a.x, a.y),\n    b2 = new Node(b.i, b.x, b.y),\n    an = a.next,\n    bp = b.prev;\n  a.next = b;\n  b.prev = a;\n  a2.next = an;\n  an.prev = a2;\n  b2.next = a2;\n  a2.prev = b2;\n  bp.next = b2;\n  b2.prev = bp;\n  return b2;\n}\n\n// create a node and optionally link it with previous one (in a circular doubly linked list)\nfunction insertNode(i, x, y, last) {\n  var p = new Node(i, x, y);\n  if (!last) {\n    p.prev = p;\n    p.next = p;\n  } else {\n    p.next = last.next;\n    p.prev = last;\n    last.next.prev = p;\n    last.next = p;\n  }\n  return p;\n}\nfunction removeNode(p) {\n  p.next.prev = p.prev;\n  p.prev.next = p.next;\n  if (p.prevZ) p.prevZ.nextZ = p.nextZ;\n  if (p.nextZ) p.nextZ.prevZ = p.prevZ;\n}\nfunction Node(i, x, y) {\n  // vertex index in coordinates array\n  this.i = i;\n\n  // vertex coordinates\n  this.x = x;\n  this.y = y;\n\n  // previous and next vertex nodes in a polygon ring\n  this.prev = null;\n  this.next = null;\n\n  // z-order curve value\n  this.z = 0;\n\n  // previous and next nodes in z-order\n  this.prevZ = null;\n  this.nextZ = null;\n\n  // indicates whether this is a steiner point\n  this.steiner = false;\n}\n\n// return a percentage difference between the polygon area and its triangulation area;\n// used to verify correctness of triangulation\nearcut.deviation = function (data, holeIndices, dim, triangles) {\n  var hasHoles = holeIndices && holeIndices.length;\n  var outerLen = hasHoles ? holeIndices[0] * dim : data.length;\n  var polygonArea = Math.abs(signedArea(data, 0, outerLen, dim));\n  if (hasHoles) {\n    for (var i = 0, len = holeIndices.length; i < len; i++) {\n      var start = holeIndices[i] * dim;\n      var end = i < len - 1 ? holeIndices[i + 1] * dim : data.length;\n      polygonArea -= Math.abs(signedArea(data, start, end, dim));\n    }\n  }\n  var trianglesArea = 0;\n  for (i = 0; i < triangles.length; i += 3) {\n    var a = triangles[i] * dim;\n    var b = triangles[i + 1] * dim;\n    var c = triangles[i + 2] * dim;\n    trianglesArea += Math.abs((data[a] - data[c]) * (data[b + 1] - data[a + 1]) - (data[a] - data[b]) * (data[c + 1] - data[a + 1]));\n  }\n  return polygonArea === 0 && trianglesArea === 0 ? 0 : Math.abs((trianglesArea - polygonArea) / polygonArea);\n};\nfunction signedArea(data, start, end, dim) {\n  var sum = 0;\n  for (var i = start, j = end - dim; i < end; i += dim) {\n    sum += (data[j] - data[i]) * (data[i + 1] + data[j + 1]);\n    j = i;\n  }\n  return sum;\n}\n\n// turn a polygon in a multi-dimensional array form (e.g. as in GeoJSON) into a form Earcut accepts\nearcut.flatten = function (data) {\n  var dim = data[0][0].length,\n    result = {\n      vertices: [],\n      holes: [],\n      dimensions: dim\n    },\n    holeIndex = 0;\n  for (var i = 0; i < data.length; i++) {\n    for (var j = 0; j < data[i].length; j++) {\n      for (var d = 0; d < dim; d++) result.vertices.push(data[i][j][d]);\n    }\n    if (i > 0) {\n      holeIndex += data[i - 1].length;\n      result.holes.push(holeIndex);\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["module", "exports", "earcut", "default", "data", "holeIndices", "dim", "hasHoles", "length", "outerLen", "outerNode", "linkedList", "triangles", "next", "prev", "minX", "minY", "maxX", "maxY", "x", "y", "invSize", "eliminateHoles", "i", "Math", "max", "earcutLinked", "start", "end", "clockwise", "last", "signedArea", "insertNode", "equals", "removeNode", "filterPoints", "p", "again", "steiner", "area", "ear", "pass", "indexCurve", "stop", "isEarHashed", "isEar", "push", "cureLocalIntersections", "splitEarcut", "a", "b", "c", "ax", "bx", "cx", "ay", "by", "cy", "x0", "y0", "x1", "y1", "pointInTriangle", "minZ", "zOrder", "maxZ", "prevZ", "n", "nextZ", "z", "intersects", "locallyInside", "isValidDiagonal", "splitPolygon", "queue", "len", "list", "getLeftmost", "sort", "compareX", "eliminateHole", "hole", "bridge", "findHoleBridge", "bridgeReverse", "hx", "hy", "qx", "Infinity", "m", "mx", "my", "tanMin", "tan", "abs", "sectorContainsSector", "sortLinked", "q", "e", "tail", "numMerges", "pSize", "qSize", "inSize", "leftmost", "px", "py", "intersectsPolygon", "middleInside", "r", "p1", "p2", "q1", "q2", "o1", "sign", "o2", "o3", "o4", "onSegment", "min", "num", "inside", "a2", "Node", "b2", "an", "bp", "deviation", "polygonArea", "trianglesArea", "sum", "j", "flatten", "result", "vertices", "holes", "dimensions", "holeIndex", "d"], "sources": ["C:/Users/<USER>/Projects/Python/EU4/frontend/node_modules/earcut/src/earcut.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = earcut;\nmodule.exports.default = earcut;\n\nfunction earcut(data, holeIndices, dim) {\n\n    dim = dim || 2;\n\n    var hasHoles = holeIndices && holeIndices.length,\n        outerLen = hasHoles ? holeIndices[0] * dim : data.length,\n        outerNode = linkedList(data, 0, outerLen, dim, true),\n        triangles = [];\n\n    if (!outerNode || outerNode.next === outerNode.prev) return triangles;\n\n    var minX, minY, maxX, maxY, x, y, invSize;\n\n    if (hasHoles) outerNode = eliminateHoles(data, holeIndices, outerNode, dim);\n\n    // if the shape is not too simple, we'll use z-order curve hash later; calculate polygon bbox\n    if (data.length > 80 * dim) {\n        minX = maxX = data[0];\n        minY = maxY = data[1];\n\n        for (var i = dim; i < outerLen; i += dim) {\n            x = data[i];\n            y = data[i + 1];\n            if (x < minX) minX = x;\n            if (y < minY) minY = y;\n            if (x > maxX) maxX = x;\n            if (y > maxY) maxY = y;\n        }\n\n        // minX, minY and invSize are later used to transform coords into integers for z-order calculation\n        invSize = Math.max(maxX - minX, maxY - minY);\n        invSize = invSize !== 0 ? 32767 / invSize : 0;\n    }\n\n    earcutLinked(outerNode, triangles, dim, minX, minY, invSize, 0);\n\n    return triangles;\n}\n\n// create a circular doubly linked list from polygon points in the specified winding order\nfunction linkedList(data, start, end, dim, clockwise) {\n    var i, last;\n\n    if (clockwise === (signedArea(data, start, end, dim) > 0)) {\n        for (i = start; i < end; i += dim) last = insertNode(i, data[i], data[i + 1], last);\n    } else {\n        for (i = end - dim; i >= start; i -= dim) last = insertNode(i, data[i], data[i + 1], last);\n    }\n\n    if (last && equals(last, last.next)) {\n        removeNode(last);\n        last = last.next;\n    }\n\n    return last;\n}\n\n// eliminate colinear or duplicate points\nfunction filterPoints(start, end) {\n    if (!start) return start;\n    if (!end) end = start;\n\n    var p = start,\n        again;\n    do {\n        again = false;\n\n        if (!p.steiner && (equals(p, p.next) || area(p.prev, p, p.next) === 0)) {\n            removeNode(p);\n            p = end = p.prev;\n            if (p === p.next) break;\n            again = true;\n\n        } else {\n            p = p.next;\n        }\n    } while (again || p !== end);\n\n    return end;\n}\n\n// main ear slicing loop which triangulates a polygon (given as a linked list)\nfunction earcutLinked(ear, triangles, dim, minX, minY, invSize, pass) {\n    if (!ear) return;\n\n    // interlink polygon nodes in z-order\n    if (!pass && invSize) indexCurve(ear, minX, minY, invSize);\n\n    var stop = ear,\n        prev, next;\n\n    // iterate through ears, slicing them one by one\n    while (ear.prev !== ear.next) {\n        prev = ear.prev;\n        next = ear.next;\n\n        if (invSize ? isEarHashed(ear, minX, minY, invSize) : isEar(ear)) {\n            // cut off the triangle\n            triangles.push(prev.i / dim | 0);\n            triangles.push(ear.i / dim | 0);\n            triangles.push(next.i / dim | 0);\n\n            removeNode(ear);\n\n            // skipping the next vertex leads to less sliver triangles\n            ear = next.next;\n            stop = next.next;\n\n            continue;\n        }\n\n        ear = next;\n\n        // if we looped through the whole remaining polygon and can't find any more ears\n        if (ear === stop) {\n            // try filtering points and slicing again\n            if (!pass) {\n                earcutLinked(filterPoints(ear), triangles, dim, minX, minY, invSize, 1);\n\n            // if this didn't work, try curing all small self-intersections locally\n            } else if (pass === 1) {\n                ear = cureLocalIntersections(filterPoints(ear), triangles, dim);\n                earcutLinked(ear, triangles, dim, minX, minY, invSize, 2);\n\n            // as a last resort, try splitting the remaining polygon into two\n            } else if (pass === 2) {\n                splitEarcut(ear, triangles, dim, minX, minY, invSize);\n            }\n\n            break;\n        }\n    }\n}\n\n// check whether a polygon node forms a valid ear with adjacent nodes\nfunction isEar(ear) {\n    var a = ear.prev,\n        b = ear,\n        c = ear.next;\n\n    if (area(a, b, c) >= 0) return false; // reflex, can't be an ear\n\n    // now make sure we don't have other points inside the potential ear\n    var ax = a.x, bx = b.x, cx = c.x, ay = a.y, by = b.y, cy = c.y;\n\n    // triangle bbox; min & max are calculated like this for speed\n    var x0 = ax < bx ? (ax < cx ? ax : cx) : (bx < cx ? bx : cx),\n        y0 = ay < by ? (ay < cy ? ay : cy) : (by < cy ? by : cy),\n        x1 = ax > bx ? (ax > cx ? ax : cx) : (bx > cx ? bx : cx),\n        y1 = ay > by ? (ay > cy ? ay : cy) : (by > cy ? by : cy);\n\n    var p = c.next;\n    while (p !== a) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, p.x, p.y) &&\n            area(p.prev, p, p.next) >= 0) return false;\n        p = p.next;\n    }\n\n    return true;\n}\n\nfunction isEarHashed(ear, minX, minY, invSize) {\n    var a = ear.prev,\n        b = ear,\n        c = ear.next;\n\n    if (area(a, b, c) >= 0) return false; // reflex, can't be an ear\n\n    var ax = a.x, bx = b.x, cx = c.x, ay = a.y, by = b.y, cy = c.y;\n\n    // triangle bbox; min & max are calculated like this for speed\n    var x0 = ax < bx ? (ax < cx ? ax : cx) : (bx < cx ? bx : cx),\n        y0 = ay < by ? (ay < cy ? ay : cy) : (by < cy ? by : cy),\n        x1 = ax > bx ? (ax > cx ? ax : cx) : (bx > cx ? bx : cx),\n        y1 = ay > by ? (ay > cy ? ay : cy) : (by > cy ? by : cy);\n\n    // z-order range for the current triangle bbox;\n    var minZ = zOrder(x0, y0, minX, minY, invSize),\n        maxZ = zOrder(x1, y1, minX, minY, invSize);\n\n    var p = ear.prevZ,\n        n = ear.nextZ;\n\n    // look for points inside the triangle in both directions\n    while (p && p.z >= minZ && n && n.z <= maxZ) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 && p !== a && p !== c &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, p.x, p.y) && area(p.prev, p, p.next) >= 0) return false;\n        p = p.prevZ;\n\n        if (n.x >= x0 && n.x <= x1 && n.y >= y0 && n.y <= y1 && n !== a && n !== c &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, n.x, n.y) && area(n.prev, n, n.next) >= 0) return false;\n        n = n.nextZ;\n    }\n\n    // look for remaining points in decreasing z-order\n    while (p && p.z >= minZ) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 && p !== a && p !== c &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, p.x, p.y) && area(p.prev, p, p.next) >= 0) return false;\n        p = p.prevZ;\n    }\n\n    // look for remaining points in increasing z-order\n    while (n && n.z <= maxZ) {\n        if (n.x >= x0 && n.x <= x1 && n.y >= y0 && n.y <= y1 && n !== a && n !== c &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, n.x, n.y) && area(n.prev, n, n.next) >= 0) return false;\n        n = n.nextZ;\n    }\n\n    return true;\n}\n\n// go through all polygon nodes and cure small local self-intersections\nfunction cureLocalIntersections(start, triangles, dim) {\n    var p = start;\n    do {\n        var a = p.prev,\n            b = p.next.next;\n\n        if (!equals(a, b) && intersects(a, p, p.next, b) && locallyInside(a, b) && locallyInside(b, a)) {\n\n            triangles.push(a.i / dim | 0);\n            triangles.push(p.i / dim | 0);\n            triangles.push(b.i / dim | 0);\n\n            // remove two nodes involved\n            removeNode(p);\n            removeNode(p.next);\n\n            p = start = b;\n        }\n        p = p.next;\n    } while (p !== start);\n\n    return filterPoints(p);\n}\n\n// try splitting polygon into two and triangulate them independently\nfunction splitEarcut(start, triangles, dim, minX, minY, invSize) {\n    // look for a valid diagonal that divides the polygon into two\n    var a = start;\n    do {\n        var b = a.next.next;\n        while (b !== a.prev) {\n            if (a.i !== b.i && isValidDiagonal(a, b)) {\n                // split the polygon in two by the diagonal\n                var c = splitPolygon(a, b);\n\n                // filter colinear points around the cuts\n                a = filterPoints(a, a.next);\n                c = filterPoints(c, c.next);\n\n                // run earcut on each half\n                earcutLinked(a, triangles, dim, minX, minY, invSize, 0);\n                earcutLinked(c, triangles, dim, minX, minY, invSize, 0);\n                return;\n            }\n            b = b.next;\n        }\n        a = a.next;\n    } while (a !== start);\n}\n\n// link every hole into the outer loop, producing a single-ring polygon without holes\nfunction eliminateHoles(data, holeIndices, outerNode, dim) {\n    var queue = [],\n        i, len, start, end, list;\n\n    for (i = 0, len = holeIndices.length; i < len; i++) {\n        start = holeIndices[i] * dim;\n        end = i < len - 1 ? holeIndices[i + 1] * dim : data.length;\n        list = linkedList(data, start, end, dim, false);\n        if (list === list.next) list.steiner = true;\n        queue.push(getLeftmost(list));\n    }\n\n    queue.sort(compareX);\n\n    // process holes from left to right\n    for (i = 0; i < queue.length; i++) {\n        outerNode = eliminateHole(queue[i], outerNode);\n    }\n\n    return outerNode;\n}\n\nfunction compareX(a, b) {\n    return a.x - b.x;\n}\n\n// find a bridge between vertices that connects hole with an outer ring and and link it\nfunction eliminateHole(hole, outerNode) {\n    var bridge = findHoleBridge(hole, outerNode);\n    if (!bridge) {\n        return outerNode;\n    }\n\n    var bridgeReverse = splitPolygon(bridge, hole);\n\n    // filter collinear points around the cuts\n    filterPoints(bridgeReverse, bridgeReverse.next);\n    return filterPoints(bridge, bridge.next);\n}\n\n// David Eberly's algorithm for finding a bridge between hole and outer polygon\nfunction findHoleBridge(hole, outerNode) {\n    var p = outerNode,\n        hx = hole.x,\n        hy = hole.y,\n        qx = -Infinity,\n        m;\n\n    // find a segment intersected by a ray from the hole's leftmost point to the left;\n    // segment's endpoint with lesser x will be potential connection point\n    do {\n        if (hy <= p.y && hy >= p.next.y && p.next.y !== p.y) {\n            var x = p.x + (hy - p.y) * (p.next.x - p.x) / (p.next.y - p.y);\n            if (x <= hx && x > qx) {\n                qx = x;\n                m = p.x < p.next.x ? p : p.next;\n                if (x === hx) return m; // hole touches outer segment; pick leftmost endpoint\n            }\n        }\n        p = p.next;\n    } while (p !== outerNode);\n\n    if (!m) return null;\n\n    // look for points inside the triangle of hole point, segment intersection and endpoint;\n    // if there are no points found, we have a valid connection;\n    // otherwise choose the point of the minimum angle with the ray as connection point\n\n    var stop = m,\n        mx = m.x,\n        my = m.y,\n        tanMin = Infinity,\n        tan;\n\n    p = m;\n\n    do {\n        if (hx >= p.x && p.x >= mx && hx !== p.x &&\n                pointInTriangle(hy < my ? hx : qx, hy, mx, my, hy < my ? qx : hx, hy, p.x, p.y)) {\n\n            tan = Math.abs(hy - p.y) / (hx - p.x); // tangential\n\n            if (locallyInside(p, hole) &&\n                (tan < tanMin || (tan === tanMin && (p.x > m.x || (p.x === m.x && sectorContainsSector(m, p)))))) {\n                m = p;\n                tanMin = tan;\n            }\n        }\n\n        p = p.next;\n    } while (p !== stop);\n\n    return m;\n}\n\n// whether sector in vertex m contains sector in vertex p in the same coordinates\nfunction sectorContainsSector(m, p) {\n    return area(m.prev, m, p.prev) < 0 && area(p.next, m, m.next) < 0;\n}\n\n// interlink polygon nodes in z-order\nfunction indexCurve(start, minX, minY, invSize) {\n    var p = start;\n    do {\n        if (p.z === 0) p.z = zOrder(p.x, p.y, minX, minY, invSize);\n        p.prevZ = p.prev;\n        p.nextZ = p.next;\n        p = p.next;\n    } while (p !== start);\n\n    p.prevZ.nextZ = null;\n    p.prevZ = null;\n\n    sortLinked(p);\n}\n\n// Simon Tatham's linked list merge sort algorithm\n// http://www.chiark.greenend.org.uk/~sgtatham/algorithms/listsort.html\nfunction sortLinked(list) {\n    var i, p, q, e, tail, numMerges, pSize, qSize,\n        inSize = 1;\n\n    do {\n        p = list;\n        list = null;\n        tail = null;\n        numMerges = 0;\n\n        while (p) {\n            numMerges++;\n            q = p;\n            pSize = 0;\n            for (i = 0; i < inSize; i++) {\n                pSize++;\n                q = q.nextZ;\n                if (!q) break;\n            }\n            qSize = inSize;\n\n            while (pSize > 0 || (qSize > 0 && q)) {\n\n                if (pSize !== 0 && (qSize === 0 || !q || p.z <= q.z)) {\n                    e = p;\n                    p = p.nextZ;\n                    pSize--;\n                } else {\n                    e = q;\n                    q = q.nextZ;\n                    qSize--;\n                }\n\n                if (tail) tail.nextZ = e;\n                else list = e;\n\n                e.prevZ = tail;\n                tail = e;\n            }\n\n            p = q;\n        }\n\n        tail.nextZ = null;\n        inSize *= 2;\n\n    } while (numMerges > 1);\n\n    return list;\n}\n\n// z-order of a point given coords and inverse of the longer side of data bbox\nfunction zOrder(x, y, minX, minY, invSize) {\n    // coords are transformed into non-negative 15-bit integer range\n    x = (x - minX) * invSize | 0;\n    y = (y - minY) * invSize | 0;\n\n    x = (x | (x << 8)) & 0x00FF00FF;\n    x = (x | (x << 4)) & 0x0F0F0F0F;\n    x = (x | (x << 2)) & 0x33333333;\n    x = (x | (x << 1)) & 0x55555555;\n\n    y = (y | (y << 8)) & 0x00FF00FF;\n    y = (y | (y << 4)) & 0x0F0F0F0F;\n    y = (y | (y << 2)) & 0x33333333;\n    y = (y | (y << 1)) & 0x55555555;\n\n    return x | (y << 1);\n}\n\n// find the leftmost node of a polygon ring\nfunction getLeftmost(start) {\n    var p = start,\n        leftmost = start;\n    do {\n        if (p.x < leftmost.x || (p.x === leftmost.x && p.y < leftmost.y)) leftmost = p;\n        p = p.next;\n    } while (p !== start);\n\n    return leftmost;\n}\n\n// check if a point lies within a convex triangle\nfunction pointInTriangle(ax, ay, bx, by, cx, cy, px, py) {\n    return (cx - px) * (ay - py) >= (ax - px) * (cy - py) &&\n           (ax - px) * (by - py) >= (bx - px) * (ay - py) &&\n           (bx - px) * (cy - py) >= (cx - px) * (by - py);\n}\n\n// check if a diagonal between two polygon nodes is valid (lies in polygon interior)\nfunction isValidDiagonal(a, b) {\n    return a.next.i !== b.i && a.prev.i !== b.i && !intersectsPolygon(a, b) && // dones't intersect other edges\n           (locallyInside(a, b) && locallyInside(b, a) && middleInside(a, b) && // locally visible\n            (area(a.prev, a, b.prev) || area(a, b.prev, b)) || // does not create opposite-facing sectors\n            equals(a, b) && area(a.prev, a, a.next) > 0 && area(b.prev, b, b.next) > 0); // special zero-length case\n}\n\n// signed area of a triangle\nfunction area(p, q, r) {\n    return (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y);\n}\n\n// check if two points are equal\nfunction equals(p1, p2) {\n    return p1.x === p2.x && p1.y === p2.y;\n}\n\n// check if two segments intersect\nfunction intersects(p1, q1, p2, q2) {\n    var o1 = sign(area(p1, q1, p2));\n    var o2 = sign(area(p1, q1, q2));\n    var o3 = sign(area(p2, q2, p1));\n    var o4 = sign(area(p2, q2, q1));\n\n    if (o1 !== o2 && o3 !== o4) return true; // general case\n\n    if (o1 === 0 && onSegment(p1, p2, q1)) return true; // p1, q1 and p2 are collinear and p2 lies on p1q1\n    if (o2 === 0 && onSegment(p1, q2, q1)) return true; // p1, q1 and q2 are collinear and q2 lies on p1q1\n    if (o3 === 0 && onSegment(p2, p1, q2)) return true; // p2, q2 and p1 are collinear and p1 lies on p2q2\n    if (o4 === 0 && onSegment(p2, q1, q2)) return true; // p2, q2 and q1 are collinear and q1 lies on p2q2\n\n    return false;\n}\n\n// for collinear points p, q, r, check if point q lies on segment pr\nfunction onSegment(p, q, r) {\n    return q.x <= Math.max(p.x, r.x) && q.x >= Math.min(p.x, r.x) && q.y <= Math.max(p.y, r.y) && q.y >= Math.min(p.y, r.y);\n}\n\nfunction sign(num) {\n    return num > 0 ? 1 : num < 0 ? -1 : 0;\n}\n\n// check if a polygon diagonal intersects any polygon segments\nfunction intersectsPolygon(a, b) {\n    var p = a;\n    do {\n        if (p.i !== a.i && p.next.i !== a.i && p.i !== b.i && p.next.i !== b.i &&\n                intersects(p, p.next, a, b)) return true;\n        p = p.next;\n    } while (p !== a);\n\n    return false;\n}\n\n// check if a polygon diagonal is locally inside the polygon\nfunction locallyInside(a, b) {\n    return area(a.prev, a, a.next) < 0 ?\n        area(a, b, a.next) >= 0 && area(a, a.prev, b) >= 0 :\n        area(a, b, a.prev) < 0 || area(a, a.next, b) < 0;\n}\n\n// check if the middle point of a polygon diagonal is inside the polygon\nfunction middleInside(a, b) {\n    var p = a,\n        inside = false,\n        px = (a.x + b.x) / 2,\n        py = (a.y + b.y) / 2;\n    do {\n        if (((p.y > py) !== (p.next.y > py)) && p.next.y !== p.y &&\n                (px < (p.next.x - p.x) * (py - p.y) / (p.next.y - p.y) + p.x))\n            inside = !inside;\n        p = p.next;\n    } while (p !== a);\n\n    return inside;\n}\n\n// link two polygon vertices with a bridge; if the vertices belong to the same ring, it splits polygon into two;\n// if one belongs to the outer ring and another to a hole, it merges it into a single ring\nfunction splitPolygon(a, b) {\n    var a2 = new Node(a.i, a.x, a.y),\n        b2 = new Node(b.i, b.x, b.y),\n        an = a.next,\n        bp = b.prev;\n\n    a.next = b;\n    b.prev = a;\n\n    a2.next = an;\n    an.prev = a2;\n\n    b2.next = a2;\n    a2.prev = b2;\n\n    bp.next = b2;\n    b2.prev = bp;\n\n    return b2;\n}\n\n// create a node and optionally link it with previous one (in a circular doubly linked list)\nfunction insertNode(i, x, y, last) {\n    var p = new Node(i, x, y);\n\n    if (!last) {\n        p.prev = p;\n        p.next = p;\n\n    } else {\n        p.next = last.next;\n        p.prev = last;\n        last.next.prev = p;\n        last.next = p;\n    }\n    return p;\n}\n\nfunction removeNode(p) {\n    p.next.prev = p.prev;\n    p.prev.next = p.next;\n\n    if (p.prevZ) p.prevZ.nextZ = p.nextZ;\n    if (p.nextZ) p.nextZ.prevZ = p.prevZ;\n}\n\nfunction Node(i, x, y) {\n    // vertex index in coordinates array\n    this.i = i;\n\n    // vertex coordinates\n    this.x = x;\n    this.y = y;\n\n    // previous and next vertex nodes in a polygon ring\n    this.prev = null;\n    this.next = null;\n\n    // z-order curve value\n    this.z = 0;\n\n    // previous and next nodes in z-order\n    this.prevZ = null;\n    this.nextZ = null;\n\n    // indicates whether this is a steiner point\n    this.steiner = false;\n}\n\n// return a percentage difference between the polygon area and its triangulation area;\n// used to verify correctness of triangulation\nearcut.deviation = function (data, holeIndices, dim, triangles) {\n    var hasHoles = holeIndices && holeIndices.length;\n    var outerLen = hasHoles ? holeIndices[0] * dim : data.length;\n\n    var polygonArea = Math.abs(signedArea(data, 0, outerLen, dim));\n    if (hasHoles) {\n        for (var i = 0, len = holeIndices.length; i < len; i++) {\n            var start = holeIndices[i] * dim;\n            var end = i < len - 1 ? holeIndices[i + 1] * dim : data.length;\n            polygonArea -= Math.abs(signedArea(data, start, end, dim));\n        }\n    }\n\n    var trianglesArea = 0;\n    for (i = 0; i < triangles.length; i += 3) {\n        var a = triangles[i] * dim;\n        var b = triangles[i + 1] * dim;\n        var c = triangles[i + 2] * dim;\n        trianglesArea += Math.abs(\n            (data[a] - data[c]) * (data[b + 1] - data[a + 1]) -\n            (data[a] - data[b]) * (data[c + 1] - data[a + 1]));\n    }\n\n    return polygonArea === 0 && trianglesArea === 0 ? 0 :\n        Math.abs((trianglesArea - polygonArea) / polygonArea);\n};\n\nfunction signedArea(data, start, end, dim) {\n    var sum = 0;\n    for (var i = start, j = end - dim; i < end; i += dim) {\n        sum += (data[j] - data[i]) * (data[i + 1] + data[j + 1]);\n        j = i;\n    }\n    return sum;\n}\n\n// turn a polygon in a multi-dimensional array form (e.g. as in GeoJSON) into a form Earcut accepts\nearcut.flatten = function (data) {\n    var dim = data[0][0].length,\n        result = {vertices: [], holes: [], dimensions: dim},\n        holeIndex = 0;\n\n    for (var i = 0; i < data.length; i++) {\n        for (var j = 0; j < data[i].length; j++) {\n            for (var d = 0; d < dim; d++) result.vertices.push(data[i][j][d]);\n        }\n        if (i > 0) {\n            holeIndex += data[i - 1].length;\n            result.holes.push(holeIndex);\n        }\n    }\n    return result;\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBF,MAAM,CAACC,OAAO,CAACE,OAAO,GAAGD,MAAM;AAE/B,SAASA,MAAMA,CAACE,IAAI,EAAEC,WAAW,EAAEC,GAAG,EAAE;EAEpCA,GAAG,GAAGA,GAAG,IAAI,CAAC;EAEd,IAAIC,QAAQ,GAAGF,WAAW,IAAIA,WAAW,CAACG,MAAM;IAC5CC,QAAQ,GAAGF,QAAQ,GAAGF,WAAW,CAAC,CAAC,CAAC,GAAGC,GAAG,GAAGF,IAAI,CAACI,MAAM;IACxDE,SAAS,GAAGC,UAAU,CAACP,IAAI,EAAE,CAAC,EAAEK,QAAQ,EAAEH,GAAG,EAAE,IAAI,CAAC;IACpDM,SAAS,GAAG,EAAE;EAElB,IAAI,CAACF,SAAS,IAAIA,SAAS,CAACG,IAAI,KAAKH,SAAS,CAACI,IAAI,EAAE,OAAOF,SAAS;EAErE,IAAIG,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAEC,OAAO;EAEzC,IAAId,QAAQ,EAAEG,SAAS,GAAGY,cAAc,CAAClB,IAAI,EAAEC,WAAW,EAAEK,SAAS,EAAEJ,GAAG,CAAC;;EAE3E;EACA,IAAIF,IAAI,CAACI,MAAM,GAAG,EAAE,GAAGF,GAAG,EAAE;IACxBS,IAAI,GAAGE,IAAI,GAAGb,IAAI,CAAC,CAAC,CAAC;IACrBY,IAAI,GAAGE,IAAI,GAAGd,IAAI,CAAC,CAAC,CAAC;IAErB,KAAK,IAAImB,CAAC,GAAGjB,GAAG,EAAEiB,CAAC,GAAGd,QAAQ,EAAEc,CAAC,IAAIjB,GAAG,EAAE;MACtCa,CAAC,GAAGf,IAAI,CAACmB,CAAC,CAAC;MACXH,CAAC,GAAGhB,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC;MACf,IAAIJ,CAAC,GAAGJ,IAAI,EAAEA,IAAI,GAAGI,CAAC;MACtB,IAAIC,CAAC,GAAGJ,IAAI,EAAEA,IAAI,GAAGI,CAAC;MACtB,IAAID,CAAC,GAAGF,IAAI,EAAEA,IAAI,GAAGE,CAAC;MACtB,IAAIC,CAAC,GAAGF,IAAI,EAAEA,IAAI,GAAGE,CAAC;IAC1B;;IAEA;IACAC,OAAO,GAAGG,IAAI,CAACC,GAAG,CAACR,IAAI,GAAGF,IAAI,EAAEG,IAAI,GAAGF,IAAI,CAAC;IAC5CK,OAAO,GAAGA,OAAO,KAAK,CAAC,GAAG,KAAK,GAAGA,OAAO,GAAG,CAAC;EACjD;EAEAK,YAAY,CAAChB,SAAS,EAAEE,SAAS,EAAEN,GAAG,EAAES,IAAI,EAAEC,IAAI,EAAEK,OAAO,EAAE,CAAC,CAAC;EAE/D,OAAOT,SAAS;AACpB;;AAEA;AACA,SAASD,UAAUA,CAACP,IAAI,EAAEuB,KAAK,EAAEC,GAAG,EAAEtB,GAAG,EAAEuB,SAAS,EAAE;EAClD,IAAIN,CAAC,EAAEO,IAAI;EAEX,IAAID,SAAS,KAAME,UAAU,CAAC3B,IAAI,EAAEuB,KAAK,EAAEC,GAAG,EAAEtB,GAAG,CAAC,GAAG,CAAE,EAAE;IACvD,KAAKiB,CAAC,GAAGI,KAAK,EAAEJ,CAAC,GAAGK,GAAG,EAAEL,CAAC,IAAIjB,GAAG,EAAEwB,IAAI,GAAGE,UAAU,CAACT,CAAC,EAAEnB,IAAI,CAACmB,CAAC,CAAC,EAAEnB,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,EAAEO,IAAI,CAAC;EACvF,CAAC,MAAM;IACH,KAAKP,CAAC,GAAGK,GAAG,GAAGtB,GAAG,EAAEiB,CAAC,IAAII,KAAK,EAAEJ,CAAC,IAAIjB,GAAG,EAAEwB,IAAI,GAAGE,UAAU,CAACT,CAAC,EAAEnB,IAAI,CAACmB,CAAC,CAAC,EAAEnB,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,EAAEO,IAAI,CAAC;EAC9F;EAEA,IAAIA,IAAI,IAAIG,MAAM,CAACH,IAAI,EAAEA,IAAI,CAACjB,IAAI,CAAC,EAAE;IACjCqB,UAAU,CAACJ,IAAI,CAAC;IAChBA,IAAI,GAAGA,IAAI,CAACjB,IAAI;EACpB;EAEA,OAAOiB,IAAI;AACf;;AAEA;AACA,SAASK,YAAYA,CAACR,KAAK,EAAEC,GAAG,EAAE;EAC9B,IAAI,CAACD,KAAK,EAAE,OAAOA,KAAK;EACxB,IAAI,CAACC,GAAG,EAAEA,GAAG,GAAGD,KAAK;EAErB,IAAIS,CAAC,GAAGT,KAAK;IACTU,KAAK;EACT,GAAG;IACCA,KAAK,GAAG,KAAK;IAEb,IAAI,CAACD,CAAC,CAACE,OAAO,KAAKL,MAAM,CAACG,CAAC,EAAEA,CAAC,CAACvB,IAAI,CAAC,IAAI0B,IAAI,CAACH,CAAC,CAACtB,IAAI,EAAEsB,CAAC,EAAEA,CAAC,CAACvB,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;MACpEqB,UAAU,CAACE,CAAC,CAAC;MACbA,CAAC,GAAGR,GAAG,GAAGQ,CAAC,CAACtB,IAAI;MAChB,IAAIsB,CAAC,KAAKA,CAAC,CAACvB,IAAI,EAAE;MAClBwB,KAAK,GAAG,IAAI;IAEhB,CAAC,MAAM;MACHD,CAAC,GAAGA,CAAC,CAACvB,IAAI;IACd;EACJ,CAAC,QAAQwB,KAAK,IAAID,CAAC,KAAKR,GAAG;EAE3B,OAAOA,GAAG;AACd;;AAEA;AACA,SAASF,YAAYA,CAACc,GAAG,EAAE5B,SAAS,EAAEN,GAAG,EAAES,IAAI,EAAEC,IAAI,EAAEK,OAAO,EAAEoB,IAAI,EAAE;EAClE,IAAI,CAACD,GAAG,EAAE;;EAEV;EACA,IAAI,CAACC,IAAI,IAAIpB,OAAO,EAAEqB,UAAU,CAACF,GAAG,EAAEzB,IAAI,EAAEC,IAAI,EAAEK,OAAO,CAAC;EAE1D,IAAIsB,IAAI,GAAGH,GAAG;IACV1B,IAAI;IAAED,IAAI;;EAEd;EACA,OAAO2B,GAAG,CAAC1B,IAAI,KAAK0B,GAAG,CAAC3B,IAAI,EAAE;IAC1BC,IAAI,GAAG0B,GAAG,CAAC1B,IAAI;IACfD,IAAI,GAAG2B,GAAG,CAAC3B,IAAI;IAEf,IAAIQ,OAAO,GAAGuB,WAAW,CAACJ,GAAG,EAAEzB,IAAI,EAAEC,IAAI,EAAEK,OAAO,CAAC,GAAGwB,KAAK,CAACL,GAAG,CAAC,EAAE;MAC9D;MACA5B,SAAS,CAACkC,IAAI,CAAChC,IAAI,CAACS,CAAC,GAAGjB,GAAG,GAAG,CAAC,CAAC;MAChCM,SAAS,CAACkC,IAAI,CAACN,GAAG,CAACjB,CAAC,GAAGjB,GAAG,GAAG,CAAC,CAAC;MAC/BM,SAAS,CAACkC,IAAI,CAACjC,IAAI,CAACU,CAAC,GAAGjB,GAAG,GAAG,CAAC,CAAC;MAEhC4B,UAAU,CAACM,GAAG,CAAC;;MAEf;MACAA,GAAG,GAAG3B,IAAI,CAACA,IAAI;MACf8B,IAAI,GAAG9B,IAAI,CAACA,IAAI;MAEhB;IACJ;IAEA2B,GAAG,GAAG3B,IAAI;;IAEV;IACA,IAAI2B,GAAG,KAAKG,IAAI,EAAE;MACd;MACA,IAAI,CAACF,IAAI,EAAE;QACPf,YAAY,CAACS,YAAY,CAACK,GAAG,CAAC,EAAE5B,SAAS,EAAEN,GAAG,EAAES,IAAI,EAAEC,IAAI,EAAEK,OAAO,EAAE,CAAC,CAAC;;QAE3E;MACA,CAAC,MAAM,IAAIoB,IAAI,KAAK,CAAC,EAAE;QACnBD,GAAG,GAAGO,sBAAsB,CAACZ,YAAY,CAACK,GAAG,CAAC,EAAE5B,SAAS,EAAEN,GAAG,CAAC;QAC/DoB,YAAY,CAACc,GAAG,EAAE5B,SAAS,EAAEN,GAAG,EAAES,IAAI,EAAEC,IAAI,EAAEK,OAAO,EAAE,CAAC,CAAC;;QAE7D;MACA,CAAC,MAAM,IAAIoB,IAAI,KAAK,CAAC,EAAE;QACnBO,WAAW,CAACR,GAAG,EAAE5B,SAAS,EAAEN,GAAG,EAAES,IAAI,EAAEC,IAAI,EAAEK,OAAO,CAAC;MACzD;MAEA;IACJ;EACJ;AACJ;;AAEA;AACA,SAASwB,KAAKA,CAACL,GAAG,EAAE;EAChB,IAAIS,CAAC,GAAGT,GAAG,CAAC1B,IAAI;IACZoC,CAAC,GAAGV,GAAG;IACPW,CAAC,GAAGX,GAAG,CAAC3B,IAAI;EAEhB,IAAI0B,IAAI,CAACU,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC;;EAEtC;EACA,IAAIC,EAAE,GAAGH,CAAC,CAAC9B,CAAC;IAAEkC,EAAE,GAAGH,CAAC,CAAC/B,CAAC;IAAEmC,EAAE,GAAGH,CAAC,CAAChC,CAAC;IAAEoC,EAAE,GAAGN,CAAC,CAAC7B,CAAC;IAAEoC,EAAE,GAAGN,CAAC,CAAC9B,CAAC;IAAEqC,EAAE,GAAGN,CAAC,CAAC/B,CAAC;;EAE9D;EACA,IAAIsC,EAAE,GAAGN,EAAE,GAAGC,EAAE,GAAID,EAAE,GAAGE,EAAE,GAAGF,EAAE,GAAGE,EAAE,GAAKD,EAAE,GAAGC,EAAE,GAAGD,EAAE,GAAGC,EAAG;IACxDK,EAAE,GAAGJ,EAAE,GAAGC,EAAE,GAAID,EAAE,GAAGE,EAAE,GAAGF,EAAE,GAAGE,EAAE,GAAKD,EAAE,GAAGC,EAAE,GAAGD,EAAE,GAAGC,EAAG;IACxDG,EAAE,GAAGR,EAAE,GAAGC,EAAE,GAAID,EAAE,GAAGE,EAAE,GAAGF,EAAE,GAAGE,EAAE,GAAKD,EAAE,GAAGC,EAAE,GAAGD,EAAE,GAAGC,EAAG;IACxDO,EAAE,GAAGN,EAAE,GAAGC,EAAE,GAAID,EAAE,GAAGE,EAAE,GAAGF,EAAE,GAAGE,EAAE,GAAKD,EAAE,GAAGC,EAAE,GAAGD,EAAE,GAAGC,EAAG;EAE5D,IAAIrB,CAAC,GAAGe,CAAC,CAACtC,IAAI;EACd,OAAOuB,CAAC,KAAKa,CAAC,EAAE;IACZ,IAAIb,CAAC,CAACjB,CAAC,IAAIuC,EAAE,IAAItB,CAAC,CAACjB,CAAC,IAAIyC,EAAE,IAAIxB,CAAC,CAAChB,CAAC,IAAIuC,EAAE,IAAIvB,CAAC,CAAChB,CAAC,IAAIyC,EAAE,IAChDC,eAAe,CAACV,EAAE,EAAEG,EAAE,EAAEF,EAAE,EAAEG,EAAE,EAAEF,EAAE,EAAEG,EAAE,EAAErB,CAAC,CAACjB,CAAC,EAAEiB,CAAC,CAAChB,CAAC,CAAC,IACjDmB,IAAI,CAACH,CAAC,CAACtB,IAAI,EAAEsB,CAAC,EAAEA,CAAC,CAACvB,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK;IAC9CuB,CAAC,GAAGA,CAAC,CAACvB,IAAI;EACd;EAEA,OAAO,IAAI;AACf;AAEA,SAAS+B,WAAWA,CAACJ,GAAG,EAAEzB,IAAI,EAAEC,IAAI,EAAEK,OAAO,EAAE;EAC3C,IAAI4B,CAAC,GAAGT,GAAG,CAAC1B,IAAI;IACZoC,CAAC,GAAGV,GAAG;IACPW,CAAC,GAAGX,GAAG,CAAC3B,IAAI;EAEhB,IAAI0B,IAAI,CAACU,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC;;EAEtC,IAAIC,EAAE,GAAGH,CAAC,CAAC9B,CAAC;IAAEkC,EAAE,GAAGH,CAAC,CAAC/B,CAAC;IAAEmC,EAAE,GAAGH,CAAC,CAAChC,CAAC;IAAEoC,EAAE,GAAGN,CAAC,CAAC7B,CAAC;IAAEoC,EAAE,GAAGN,CAAC,CAAC9B,CAAC;IAAEqC,EAAE,GAAGN,CAAC,CAAC/B,CAAC;;EAE9D;EACA,IAAIsC,EAAE,GAAGN,EAAE,GAAGC,EAAE,GAAID,EAAE,GAAGE,EAAE,GAAGF,EAAE,GAAGE,EAAE,GAAKD,EAAE,GAAGC,EAAE,GAAGD,EAAE,GAAGC,EAAG;IACxDK,EAAE,GAAGJ,EAAE,GAAGC,EAAE,GAAID,EAAE,GAAGE,EAAE,GAAGF,EAAE,GAAGE,EAAE,GAAKD,EAAE,GAAGC,EAAE,GAAGD,EAAE,GAAGC,EAAG;IACxDG,EAAE,GAAGR,EAAE,GAAGC,EAAE,GAAID,EAAE,GAAGE,EAAE,GAAGF,EAAE,GAAGE,EAAE,GAAKD,EAAE,GAAGC,EAAE,GAAGD,EAAE,GAAGC,EAAG;IACxDO,EAAE,GAAGN,EAAE,GAAGC,EAAE,GAAID,EAAE,GAAGE,EAAE,GAAGF,EAAE,GAAGE,EAAE,GAAKD,EAAE,GAAGC,EAAE,GAAGD,EAAE,GAAGC,EAAG;;EAE5D;EACA,IAAIM,IAAI,GAAGC,MAAM,CAACN,EAAE,EAAEC,EAAE,EAAE5C,IAAI,EAAEC,IAAI,EAAEK,OAAO,CAAC;IAC1C4C,IAAI,GAAGD,MAAM,CAACJ,EAAE,EAAEC,EAAE,EAAE9C,IAAI,EAAEC,IAAI,EAAEK,OAAO,CAAC;EAE9C,IAAIe,CAAC,GAAGI,GAAG,CAAC0B,KAAK;IACbC,CAAC,GAAG3B,GAAG,CAAC4B,KAAK;;EAEjB;EACA,OAAOhC,CAAC,IAAIA,CAAC,CAACiC,CAAC,IAAIN,IAAI,IAAII,CAAC,IAAIA,CAAC,CAACE,CAAC,IAAIJ,IAAI,EAAE;IACzC,IAAI7B,CAAC,CAACjB,CAAC,IAAIuC,EAAE,IAAItB,CAAC,CAACjB,CAAC,IAAIyC,EAAE,IAAIxB,CAAC,CAAChB,CAAC,IAAIuC,EAAE,IAAIvB,CAAC,CAAChB,CAAC,IAAIyC,EAAE,IAAIzB,CAAC,KAAKa,CAAC,IAAIb,CAAC,KAAKe,CAAC,IACtEW,eAAe,CAACV,EAAE,EAAEG,EAAE,EAAEF,EAAE,EAAEG,EAAE,EAAEF,EAAE,EAAEG,EAAE,EAAErB,CAAC,CAACjB,CAAC,EAAEiB,CAAC,CAAChB,CAAC,CAAC,IAAImB,IAAI,CAACH,CAAC,CAACtB,IAAI,EAAEsB,CAAC,EAAEA,CAAC,CAACvB,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK;IACnGuB,CAAC,GAAGA,CAAC,CAAC8B,KAAK;IAEX,IAAIC,CAAC,CAAChD,CAAC,IAAIuC,EAAE,IAAIS,CAAC,CAAChD,CAAC,IAAIyC,EAAE,IAAIO,CAAC,CAAC/C,CAAC,IAAIuC,EAAE,IAAIQ,CAAC,CAAC/C,CAAC,IAAIyC,EAAE,IAAIM,CAAC,KAAKlB,CAAC,IAAIkB,CAAC,KAAKhB,CAAC,IACtEW,eAAe,CAACV,EAAE,EAAEG,EAAE,EAAEF,EAAE,EAAEG,EAAE,EAAEF,EAAE,EAAEG,EAAE,EAAEU,CAAC,CAAChD,CAAC,EAAEgD,CAAC,CAAC/C,CAAC,CAAC,IAAImB,IAAI,CAAC4B,CAAC,CAACrD,IAAI,EAAEqD,CAAC,EAAEA,CAAC,CAACtD,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK;IACnGsD,CAAC,GAAGA,CAAC,CAACC,KAAK;EACf;;EAEA;EACA,OAAOhC,CAAC,IAAIA,CAAC,CAACiC,CAAC,IAAIN,IAAI,EAAE;IACrB,IAAI3B,CAAC,CAACjB,CAAC,IAAIuC,EAAE,IAAItB,CAAC,CAACjB,CAAC,IAAIyC,EAAE,IAAIxB,CAAC,CAAChB,CAAC,IAAIuC,EAAE,IAAIvB,CAAC,CAAChB,CAAC,IAAIyC,EAAE,IAAIzB,CAAC,KAAKa,CAAC,IAAIb,CAAC,KAAKe,CAAC,IACtEW,eAAe,CAACV,EAAE,EAAEG,EAAE,EAAEF,EAAE,EAAEG,EAAE,EAAEF,EAAE,EAAEG,EAAE,EAAErB,CAAC,CAACjB,CAAC,EAAEiB,CAAC,CAAChB,CAAC,CAAC,IAAImB,IAAI,CAACH,CAAC,CAACtB,IAAI,EAAEsB,CAAC,EAAEA,CAAC,CAACvB,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK;IACnGuB,CAAC,GAAGA,CAAC,CAAC8B,KAAK;EACf;;EAEA;EACA,OAAOC,CAAC,IAAIA,CAAC,CAACE,CAAC,IAAIJ,IAAI,EAAE;IACrB,IAAIE,CAAC,CAAChD,CAAC,IAAIuC,EAAE,IAAIS,CAAC,CAAChD,CAAC,IAAIyC,EAAE,IAAIO,CAAC,CAAC/C,CAAC,IAAIuC,EAAE,IAAIQ,CAAC,CAAC/C,CAAC,IAAIyC,EAAE,IAAIM,CAAC,KAAKlB,CAAC,IAAIkB,CAAC,KAAKhB,CAAC,IACtEW,eAAe,CAACV,EAAE,EAAEG,EAAE,EAAEF,EAAE,EAAEG,EAAE,EAAEF,EAAE,EAAEG,EAAE,EAAEU,CAAC,CAAChD,CAAC,EAAEgD,CAAC,CAAC/C,CAAC,CAAC,IAAImB,IAAI,CAAC4B,CAAC,CAACrD,IAAI,EAAEqD,CAAC,EAAEA,CAAC,CAACtD,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK;IACnGsD,CAAC,GAAGA,CAAC,CAACC,KAAK;EACf;EAEA,OAAO,IAAI;AACf;;AAEA;AACA,SAASrB,sBAAsBA,CAACpB,KAAK,EAAEf,SAAS,EAAEN,GAAG,EAAE;EACnD,IAAI8B,CAAC,GAAGT,KAAK;EACb,GAAG;IACC,IAAIsB,CAAC,GAAGb,CAAC,CAACtB,IAAI;MACVoC,CAAC,GAAGd,CAAC,CAACvB,IAAI,CAACA,IAAI;IAEnB,IAAI,CAACoB,MAAM,CAACgB,CAAC,EAAEC,CAAC,CAAC,IAAIoB,UAAU,CAACrB,CAAC,EAAEb,CAAC,EAAEA,CAAC,CAACvB,IAAI,EAAEqC,CAAC,CAAC,IAAIqB,aAAa,CAACtB,CAAC,EAAEC,CAAC,CAAC,IAAIqB,aAAa,CAACrB,CAAC,EAAED,CAAC,CAAC,EAAE;MAE5FrC,SAAS,CAACkC,IAAI,CAACG,CAAC,CAAC1B,CAAC,GAAGjB,GAAG,GAAG,CAAC,CAAC;MAC7BM,SAAS,CAACkC,IAAI,CAACV,CAAC,CAACb,CAAC,GAAGjB,GAAG,GAAG,CAAC,CAAC;MAC7BM,SAAS,CAACkC,IAAI,CAACI,CAAC,CAAC3B,CAAC,GAAGjB,GAAG,GAAG,CAAC,CAAC;;MAE7B;MACA4B,UAAU,CAACE,CAAC,CAAC;MACbF,UAAU,CAACE,CAAC,CAACvB,IAAI,CAAC;MAElBuB,CAAC,GAAGT,KAAK,GAAGuB,CAAC;IACjB;IACAd,CAAC,GAAGA,CAAC,CAACvB,IAAI;EACd,CAAC,QAAQuB,CAAC,KAAKT,KAAK;EAEpB,OAAOQ,YAAY,CAACC,CAAC,CAAC;AAC1B;;AAEA;AACA,SAASY,WAAWA,CAACrB,KAAK,EAAEf,SAAS,EAAEN,GAAG,EAAES,IAAI,EAAEC,IAAI,EAAEK,OAAO,EAAE;EAC7D;EACA,IAAI4B,CAAC,GAAGtB,KAAK;EACb,GAAG;IACC,IAAIuB,CAAC,GAAGD,CAAC,CAACpC,IAAI,CAACA,IAAI;IACnB,OAAOqC,CAAC,KAAKD,CAAC,CAACnC,IAAI,EAAE;MACjB,IAAImC,CAAC,CAAC1B,CAAC,KAAK2B,CAAC,CAAC3B,CAAC,IAAIiD,eAAe,CAACvB,CAAC,EAAEC,CAAC,CAAC,EAAE;QACtC;QACA,IAAIC,CAAC,GAAGsB,YAAY,CAACxB,CAAC,EAAEC,CAAC,CAAC;;QAE1B;QACAD,CAAC,GAAGd,YAAY,CAACc,CAAC,EAAEA,CAAC,CAACpC,IAAI,CAAC;QAC3BsC,CAAC,GAAGhB,YAAY,CAACgB,CAAC,EAAEA,CAAC,CAACtC,IAAI,CAAC;;QAE3B;QACAa,YAAY,CAACuB,CAAC,EAAErC,SAAS,EAAEN,GAAG,EAAES,IAAI,EAAEC,IAAI,EAAEK,OAAO,EAAE,CAAC,CAAC;QACvDK,YAAY,CAACyB,CAAC,EAAEvC,SAAS,EAAEN,GAAG,EAAES,IAAI,EAAEC,IAAI,EAAEK,OAAO,EAAE,CAAC,CAAC;QACvD;MACJ;MACA6B,CAAC,GAAGA,CAAC,CAACrC,IAAI;IACd;IACAoC,CAAC,GAAGA,CAAC,CAACpC,IAAI;EACd,CAAC,QAAQoC,CAAC,KAAKtB,KAAK;AACxB;;AAEA;AACA,SAASL,cAAcA,CAAClB,IAAI,EAAEC,WAAW,EAAEK,SAAS,EAAEJ,GAAG,EAAE;EACvD,IAAIoE,KAAK,GAAG,EAAE;IACVnD,CAAC;IAAEoD,GAAG;IAAEhD,KAAK;IAAEC,GAAG;IAAEgD,IAAI;EAE5B,KAAKrD,CAAC,GAAG,CAAC,EAAEoD,GAAG,GAAGtE,WAAW,CAACG,MAAM,EAAEe,CAAC,GAAGoD,GAAG,EAAEpD,CAAC,EAAE,EAAE;IAChDI,KAAK,GAAGtB,WAAW,CAACkB,CAAC,CAAC,GAAGjB,GAAG;IAC5BsB,GAAG,GAAGL,CAAC,GAAGoD,GAAG,GAAG,CAAC,GAAGtE,WAAW,CAACkB,CAAC,GAAG,CAAC,CAAC,GAAGjB,GAAG,GAAGF,IAAI,CAACI,MAAM;IAC1DoE,IAAI,GAAGjE,UAAU,CAACP,IAAI,EAAEuB,KAAK,EAAEC,GAAG,EAAEtB,GAAG,EAAE,KAAK,CAAC;IAC/C,IAAIsE,IAAI,KAAKA,IAAI,CAAC/D,IAAI,EAAE+D,IAAI,CAACtC,OAAO,GAAG,IAAI;IAC3CoC,KAAK,CAAC5B,IAAI,CAAC+B,WAAW,CAACD,IAAI,CAAC,CAAC;EACjC;EAEAF,KAAK,CAACI,IAAI,CAACC,QAAQ,CAAC;;EAEpB;EACA,KAAKxD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmD,KAAK,CAAClE,MAAM,EAAEe,CAAC,EAAE,EAAE;IAC/Bb,SAAS,GAAGsE,aAAa,CAACN,KAAK,CAACnD,CAAC,CAAC,EAAEb,SAAS,CAAC;EAClD;EAEA,OAAOA,SAAS;AACpB;AAEA,SAASqE,QAAQA,CAAC9B,CAAC,EAAEC,CAAC,EAAE;EACpB,OAAOD,CAAC,CAAC9B,CAAC,GAAG+B,CAAC,CAAC/B,CAAC;AACpB;;AAEA;AACA,SAAS6D,aAAaA,CAACC,IAAI,EAAEvE,SAAS,EAAE;EACpC,IAAIwE,MAAM,GAAGC,cAAc,CAACF,IAAI,EAAEvE,SAAS,CAAC;EAC5C,IAAI,CAACwE,MAAM,EAAE;IACT,OAAOxE,SAAS;EACpB;EAEA,IAAI0E,aAAa,GAAGX,YAAY,CAACS,MAAM,EAAED,IAAI,CAAC;;EAE9C;EACA9C,YAAY,CAACiD,aAAa,EAAEA,aAAa,CAACvE,IAAI,CAAC;EAC/C,OAAOsB,YAAY,CAAC+C,MAAM,EAAEA,MAAM,CAACrE,IAAI,CAAC;AAC5C;;AAEA;AACA,SAASsE,cAAcA,CAACF,IAAI,EAAEvE,SAAS,EAAE;EACrC,IAAI0B,CAAC,GAAG1B,SAAS;IACb2E,EAAE,GAAGJ,IAAI,CAAC9D,CAAC;IACXmE,EAAE,GAAGL,IAAI,CAAC7D,CAAC;IACXmE,EAAE,GAAG,CAACC,QAAQ;IACdC,CAAC;;EAEL;EACA;EACA,GAAG;IACC,IAAIH,EAAE,IAAIlD,CAAC,CAAChB,CAAC,IAAIkE,EAAE,IAAIlD,CAAC,CAACvB,IAAI,CAACO,CAAC,IAAIgB,CAAC,CAACvB,IAAI,CAACO,CAAC,KAAKgB,CAAC,CAAChB,CAAC,EAAE;MACjD,IAAID,CAAC,GAAGiB,CAAC,CAACjB,CAAC,GAAG,CAACmE,EAAE,GAAGlD,CAAC,CAAChB,CAAC,KAAKgB,CAAC,CAACvB,IAAI,CAACM,CAAC,GAAGiB,CAAC,CAACjB,CAAC,CAAC,IAAIiB,CAAC,CAACvB,IAAI,CAACO,CAAC,GAAGgB,CAAC,CAAChB,CAAC,CAAC;MAC9D,IAAID,CAAC,IAAIkE,EAAE,IAAIlE,CAAC,GAAGoE,EAAE,EAAE;QACnBA,EAAE,GAAGpE,CAAC;QACNsE,CAAC,GAAGrD,CAAC,CAACjB,CAAC,GAAGiB,CAAC,CAACvB,IAAI,CAACM,CAAC,GAAGiB,CAAC,GAAGA,CAAC,CAACvB,IAAI;QAC/B,IAAIM,CAAC,KAAKkE,EAAE,EAAE,OAAOI,CAAC,CAAC,CAAC;MAC5B;IACJ;IACArD,CAAC,GAAGA,CAAC,CAACvB,IAAI;EACd,CAAC,QAAQuB,CAAC,KAAK1B,SAAS;EAExB,IAAI,CAAC+E,CAAC,EAAE,OAAO,IAAI;;EAEnB;EACA;EACA;;EAEA,IAAI9C,IAAI,GAAG8C,CAAC;IACRC,EAAE,GAAGD,CAAC,CAACtE,CAAC;IACRwE,EAAE,GAAGF,CAAC,CAACrE,CAAC;IACRwE,MAAM,GAAGJ,QAAQ;IACjBK,GAAG;EAEPzD,CAAC,GAAGqD,CAAC;EAEL,GAAG;IACC,IAAIJ,EAAE,IAAIjD,CAAC,CAACjB,CAAC,IAAIiB,CAAC,CAACjB,CAAC,IAAIuE,EAAE,IAAIL,EAAE,KAAKjD,CAAC,CAACjB,CAAC,IAChC2C,eAAe,CAACwB,EAAE,GAAGK,EAAE,GAAGN,EAAE,GAAGE,EAAE,EAAED,EAAE,EAAEI,EAAE,EAAEC,EAAE,EAAEL,EAAE,GAAGK,EAAE,GAAGJ,EAAE,GAAGF,EAAE,EAAEC,EAAE,EAAElD,CAAC,CAACjB,CAAC,EAAEiB,CAAC,CAAChB,CAAC,CAAC,EAAE;MAErFyE,GAAG,GAAGrE,IAAI,CAACsE,GAAG,CAACR,EAAE,GAAGlD,CAAC,CAAChB,CAAC,CAAC,IAAIiE,EAAE,GAAGjD,CAAC,CAACjB,CAAC,CAAC,CAAC,CAAC;;MAEvC,IAAIoD,aAAa,CAACnC,CAAC,EAAE6C,IAAI,CAAC,KACrBY,GAAG,GAAGD,MAAM,IAAKC,GAAG,KAAKD,MAAM,KAAKxD,CAAC,CAACjB,CAAC,GAAGsE,CAAC,CAACtE,CAAC,IAAKiB,CAAC,CAACjB,CAAC,KAAKsE,CAAC,CAACtE,CAAC,IAAI4E,oBAAoB,CAACN,CAAC,EAAErD,CAAC,CAAE,CAAE,CAAC,EAAE;QAClGqD,CAAC,GAAGrD,CAAC;QACLwD,MAAM,GAAGC,GAAG;MAChB;IACJ;IAEAzD,CAAC,GAAGA,CAAC,CAACvB,IAAI;EACd,CAAC,QAAQuB,CAAC,KAAKO,IAAI;EAEnB,OAAO8C,CAAC;AACZ;;AAEA;AACA,SAASM,oBAAoBA,CAACN,CAAC,EAAErD,CAAC,EAAE;EAChC,OAAOG,IAAI,CAACkD,CAAC,CAAC3E,IAAI,EAAE2E,CAAC,EAAErD,CAAC,CAACtB,IAAI,CAAC,GAAG,CAAC,IAAIyB,IAAI,CAACH,CAAC,CAACvB,IAAI,EAAE4E,CAAC,EAAEA,CAAC,CAAC5E,IAAI,CAAC,GAAG,CAAC;AACrE;;AAEA;AACA,SAAS6B,UAAUA,CAACf,KAAK,EAAEZ,IAAI,EAAEC,IAAI,EAAEK,OAAO,EAAE;EAC5C,IAAIe,CAAC,GAAGT,KAAK;EACb,GAAG;IACC,IAAIS,CAAC,CAACiC,CAAC,KAAK,CAAC,EAAEjC,CAAC,CAACiC,CAAC,GAAGL,MAAM,CAAC5B,CAAC,CAACjB,CAAC,EAAEiB,CAAC,CAAChB,CAAC,EAAEL,IAAI,EAAEC,IAAI,EAAEK,OAAO,CAAC;IAC1De,CAAC,CAAC8B,KAAK,GAAG9B,CAAC,CAACtB,IAAI;IAChBsB,CAAC,CAACgC,KAAK,GAAGhC,CAAC,CAACvB,IAAI;IAChBuB,CAAC,GAAGA,CAAC,CAACvB,IAAI;EACd,CAAC,QAAQuB,CAAC,KAAKT,KAAK;EAEpBS,CAAC,CAAC8B,KAAK,CAACE,KAAK,GAAG,IAAI;EACpBhC,CAAC,CAAC8B,KAAK,GAAG,IAAI;EAEd8B,UAAU,CAAC5D,CAAC,CAAC;AACjB;;AAEA;AACA;AACA,SAAS4D,UAAUA,CAACpB,IAAI,EAAE;EACtB,IAAIrD,CAAC;IAAEa,CAAC;IAAE6D,CAAC;IAAEC,CAAC;IAAEC,IAAI;IAAEC,SAAS;IAAEC,KAAK;IAAEC,KAAK;IACzCC,MAAM,GAAG,CAAC;EAEd,GAAG;IACCnE,CAAC,GAAGwC,IAAI;IACRA,IAAI,GAAG,IAAI;IACXuB,IAAI,GAAG,IAAI;IACXC,SAAS,GAAG,CAAC;IAEb,OAAOhE,CAAC,EAAE;MACNgE,SAAS,EAAE;MACXH,CAAC,GAAG7D,CAAC;MACLiE,KAAK,GAAG,CAAC;MACT,KAAK9E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgF,MAAM,EAAEhF,CAAC,EAAE,EAAE;QACzB8E,KAAK,EAAE;QACPJ,CAAC,GAAGA,CAAC,CAAC7B,KAAK;QACX,IAAI,CAAC6B,CAAC,EAAE;MACZ;MACAK,KAAK,GAAGC,MAAM;MAEd,OAAOF,KAAK,GAAG,CAAC,IAAKC,KAAK,GAAG,CAAC,IAAIL,CAAE,EAAE;QAElC,IAAII,KAAK,KAAK,CAAC,KAAKC,KAAK,KAAK,CAAC,IAAI,CAACL,CAAC,IAAI7D,CAAC,CAACiC,CAAC,IAAI4B,CAAC,CAAC5B,CAAC,CAAC,EAAE;UAClD6B,CAAC,GAAG9D,CAAC;UACLA,CAAC,GAAGA,CAAC,CAACgC,KAAK;UACXiC,KAAK,EAAE;QACX,CAAC,MAAM;UACHH,CAAC,GAAGD,CAAC;UACLA,CAAC,GAAGA,CAAC,CAAC7B,KAAK;UACXkC,KAAK,EAAE;QACX;QAEA,IAAIH,IAAI,EAAEA,IAAI,CAAC/B,KAAK,GAAG8B,CAAC,CAAC,KACpBtB,IAAI,GAAGsB,CAAC;QAEbA,CAAC,CAAChC,KAAK,GAAGiC,IAAI;QACdA,IAAI,GAAGD,CAAC;MACZ;MAEA9D,CAAC,GAAG6D,CAAC;IACT;IAEAE,IAAI,CAAC/B,KAAK,GAAG,IAAI;IACjBmC,MAAM,IAAI,CAAC;EAEf,CAAC,QAAQH,SAAS,GAAG,CAAC;EAEtB,OAAOxB,IAAI;AACf;;AAEA;AACA,SAASZ,MAAMA,CAAC7C,CAAC,EAAEC,CAAC,EAAEL,IAAI,EAAEC,IAAI,EAAEK,OAAO,EAAE;EACvC;EACAF,CAAC,GAAG,CAACA,CAAC,GAAGJ,IAAI,IAAIM,OAAO,GAAG,CAAC;EAC5BD,CAAC,GAAG,CAACA,CAAC,GAAGJ,IAAI,IAAIK,OAAO,GAAG,CAAC;EAE5BF,CAAC,GAAG,CAACA,CAAC,GAAIA,CAAC,IAAI,CAAE,IAAI,UAAU;EAC/BA,CAAC,GAAG,CAACA,CAAC,GAAIA,CAAC,IAAI,CAAE,IAAI,UAAU;EAC/BA,CAAC,GAAG,CAACA,CAAC,GAAIA,CAAC,IAAI,CAAE,IAAI,UAAU;EAC/BA,CAAC,GAAG,CAACA,CAAC,GAAIA,CAAC,IAAI,CAAE,IAAI,UAAU;EAE/BC,CAAC,GAAG,CAACA,CAAC,GAAIA,CAAC,IAAI,CAAE,IAAI,UAAU;EAC/BA,CAAC,GAAG,CAACA,CAAC,GAAIA,CAAC,IAAI,CAAE,IAAI,UAAU;EAC/BA,CAAC,GAAG,CAACA,CAAC,GAAIA,CAAC,IAAI,CAAE,IAAI,UAAU;EAC/BA,CAAC,GAAG,CAACA,CAAC,GAAIA,CAAC,IAAI,CAAE,IAAI,UAAU;EAE/B,OAAOD,CAAC,GAAIC,CAAC,IAAI,CAAE;AACvB;;AAEA;AACA,SAASyD,WAAWA,CAAClD,KAAK,EAAE;EACxB,IAAIS,CAAC,GAAGT,KAAK;IACT6E,QAAQ,GAAG7E,KAAK;EACpB,GAAG;IACC,IAAIS,CAAC,CAACjB,CAAC,GAAGqF,QAAQ,CAACrF,CAAC,IAAKiB,CAAC,CAACjB,CAAC,KAAKqF,QAAQ,CAACrF,CAAC,IAAIiB,CAAC,CAAChB,CAAC,GAAGoF,QAAQ,CAACpF,CAAE,EAAEoF,QAAQ,GAAGpE,CAAC;IAC9EA,CAAC,GAAGA,CAAC,CAACvB,IAAI;EACd,CAAC,QAAQuB,CAAC,KAAKT,KAAK;EAEpB,OAAO6E,QAAQ;AACnB;;AAEA;AACA,SAAS1C,eAAeA,CAACV,EAAE,EAAEG,EAAE,EAAEF,EAAE,EAAEG,EAAE,EAAEF,EAAE,EAAEG,EAAE,EAAEgD,EAAE,EAAEC,EAAE,EAAE;EACrD,OAAO,CAACpD,EAAE,GAAGmD,EAAE,KAAKlD,EAAE,GAAGmD,EAAE,CAAC,IAAI,CAACtD,EAAE,GAAGqD,EAAE,KAAKhD,EAAE,GAAGiD,EAAE,CAAC,IAC9C,CAACtD,EAAE,GAAGqD,EAAE,KAAKjD,EAAE,GAAGkD,EAAE,CAAC,IAAI,CAACrD,EAAE,GAAGoD,EAAE,KAAKlD,EAAE,GAAGmD,EAAE,CAAC,IAC9C,CAACrD,EAAE,GAAGoD,EAAE,KAAKhD,EAAE,GAAGiD,EAAE,CAAC,IAAI,CAACpD,EAAE,GAAGmD,EAAE,KAAKjD,EAAE,GAAGkD,EAAE,CAAC;AACzD;;AAEA;AACA,SAASlC,eAAeA,CAACvB,CAAC,EAAEC,CAAC,EAAE;EAC3B,OAAOD,CAAC,CAACpC,IAAI,CAACU,CAAC,KAAK2B,CAAC,CAAC3B,CAAC,IAAI0B,CAAC,CAACnC,IAAI,CAACS,CAAC,KAAK2B,CAAC,CAAC3B,CAAC,IAAI,CAACoF,iBAAiB,CAAC1D,CAAC,EAAEC,CAAC,CAAC;EAAI;EACnEqB,aAAa,CAACtB,CAAC,EAAEC,CAAC,CAAC,IAAIqB,aAAa,CAACrB,CAAC,EAAED,CAAC,CAAC,IAAI2D,YAAY,CAAC3D,CAAC,EAAEC,CAAC,CAAC;EAAI;EACnEX,IAAI,CAACU,CAAC,CAACnC,IAAI,EAAEmC,CAAC,EAAEC,CAAC,CAACpC,IAAI,CAAC,IAAIyB,IAAI,CAACU,CAAC,EAAEC,CAAC,CAACpC,IAAI,EAAEoC,CAAC,CAAC,CAAC;EAAI;EACnDjB,MAAM,CAACgB,CAAC,EAAEC,CAAC,CAAC,IAAIX,IAAI,CAACU,CAAC,CAACnC,IAAI,EAAEmC,CAAC,EAAEA,CAAC,CAACpC,IAAI,CAAC,GAAG,CAAC,IAAI0B,IAAI,CAACW,CAAC,CAACpC,IAAI,EAAEoC,CAAC,EAAEA,CAAC,CAACrC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACzF;;AAEA;AACA,SAAS0B,IAAIA,CAACH,CAAC,EAAE6D,CAAC,EAAEY,CAAC,EAAE;EACnB,OAAO,CAACZ,CAAC,CAAC7E,CAAC,GAAGgB,CAAC,CAAChB,CAAC,KAAKyF,CAAC,CAAC1F,CAAC,GAAG8E,CAAC,CAAC9E,CAAC,CAAC,GAAG,CAAC8E,CAAC,CAAC9E,CAAC,GAAGiB,CAAC,CAACjB,CAAC,KAAK0F,CAAC,CAACzF,CAAC,GAAG6E,CAAC,CAAC7E,CAAC,CAAC;AAChE;;AAEA;AACA,SAASa,MAAMA,CAAC6E,EAAE,EAAEC,EAAE,EAAE;EACpB,OAAOD,EAAE,CAAC3F,CAAC,KAAK4F,EAAE,CAAC5F,CAAC,IAAI2F,EAAE,CAAC1F,CAAC,KAAK2F,EAAE,CAAC3F,CAAC;AACzC;;AAEA;AACA,SAASkD,UAAUA,CAACwC,EAAE,EAAEE,EAAE,EAAED,EAAE,EAAEE,EAAE,EAAE;EAChC,IAAIC,EAAE,GAAGC,IAAI,CAAC5E,IAAI,CAACuE,EAAE,EAAEE,EAAE,EAAED,EAAE,CAAC,CAAC;EAC/B,IAAIK,EAAE,GAAGD,IAAI,CAAC5E,IAAI,CAACuE,EAAE,EAAEE,EAAE,EAAEC,EAAE,CAAC,CAAC;EAC/B,IAAII,EAAE,GAAGF,IAAI,CAAC5E,IAAI,CAACwE,EAAE,EAAEE,EAAE,EAAEH,EAAE,CAAC,CAAC;EAC/B,IAAIQ,EAAE,GAAGH,IAAI,CAAC5E,IAAI,CAACwE,EAAE,EAAEE,EAAE,EAAED,EAAE,CAAC,CAAC;EAE/B,IAAIE,EAAE,KAAKE,EAAE,IAAIC,EAAE,KAAKC,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC;;EAEzC,IAAIJ,EAAE,KAAK,CAAC,IAAIK,SAAS,CAACT,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;EACpD,IAAII,EAAE,KAAK,CAAC,IAAIG,SAAS,CAACT,EAAE,EAAEG,EAAE,EAAED,EAAE,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;EACpD,IAAIK,EAAE,KAAK,CAAC,IAAIE,SAAS,CAACR,EAAE,EAAED,EAAE,EAAEG,EAAE,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;EACpD,IAAIK,EAAE,KAAK,CAAC,IAAIC,SAAS,CAACR,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;;EAEpD,OAAO,KAAK;AAChB;;AAEA;AACA,SAASM,SAASA,CAACnF,CAAC,EAAE6D,CAAC,EAAEY,CAAC,EAAE;EACxB,OAAOZ,CAAC,CAAC9E,CAAC,IAAIK,IAAI,CAACC,GAAG,CAACW,CAAC,CAACjB,CAAC,EAAE0F,CAAC,CAAC1F,CAAC,CAAC,IAAI8E,CAAC,CAAC9E,CAAC,IAAIK,IAAI,CAACgG,GAAG,CAACpF,CAAC,CAACjB,CAAC,EAAE0F,CAAC,CAAC1F,CAAC,CAAC,IAAI8E,CAAC,CAAC7E,CAAC,IAAII,IAAI,CAACC,GAAG,CAACW,CAAC,CAAChB,CAAC,EAAEyF,CAAC,CAACzF,CAAC,CAAC,IAAI6E,CAAC,CAAC7E,CAAC,IAAII,IAAI,CAACgG,GAAG,CAACpF,CAAC,CAAChB,CAAC,EAAEyF,CAAC,CAACzF,CAAC,CAAC;AAC3H;AAEA,SAAS+F,IAAIA,CAACM,GAAG,EAAE;EACf,OAAOA,GAAG,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACzC;;AAEA;AACA,SAASd,iBAAiBA,CAAC1D,CAAC,EAAEC,CAAC,EAAE;EAC7B,IAAId,CAAC,GAAGa,CAAC;EACT,GAAG;IACC,IAAIb,CAAC,CAACb,CAAC,KAAK0B,CAAC,CAAC1B,CAAC,IAAIa,CAAC,CAACvB,IAAI,CAACU,CAAC,KAAK0B,CAAC,CAAC1B,CAAC,IAAIa,CAAC,CAACb,CAAC,KAAK2B,CAAC,CAAC3B,CAAC,IAAIa,CAAC,CAACvB,IAAI,CAACU,CAAC,KAAK2B,CAAC,CAAC3B,CAAC,IAC9D+C,UAAU,CAAClC,CAAC,EAAEA,CAAC,CAACvB,IAAI,EAAEoC,CAAC,EAAEC,CAAC,CAAC,EAAE,OAAO,IAAI;IAChDd,CAAC,GAAGA,CAAC,CAACvB,IAAI;EACd,CAAC,QAAQuB,CAAC,KAAKa,CAAC;EAEhB,OAAO,KAAK;AAChB;;AAEA;AACA,SAASsB,aAAaA,CAACtB,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAOX,IAAI,CAACU,CAAC,CAACnC,IAAI,EAAEmC,CAAC,EAAEA,CAAC,CAACpC,IAAI,CAAC,GAAG,CAAC,GAC9B0B,IAAI,CAACU,CAAC,EAAEC,CAAC,EAAED,CAAC,CAACpC,IAAI,CAAC,IAAI,CAAC,IAAI0B,IAAI,CAACU,CAAC,EAAEA,CAAC,CAACnC,IAAI,EAAEoC,CAAC,CAAC,IAAI,CAAC,GAClDX,IAAI,CAACU,CAAC,EAAEC,CAAC,EAAED,CAAC,CAACnC,IAAI,CAAC,GAAG,CAAC,IAAIyB,IAAI,CAACU,CAAC,EAAEA,CAAC,CAACpC,IAAI,EAAEqC,CAAC,CAAC,GAAG,CAAC;AACxD;;AAEA;AACA,SAAS0D,YAAYA,CAAC3D,CAAC,EAAEC,CAAC,EAAE;EACxB,IAAId,CAAC,GAAGa,CAAC;IACLyE,MAAM,GAAG,KAAK;IACdjB,EAAE,GAAG,CAACxD,CAAC,CAAC9B,CAAC,GAAG+B,CAAC,CAAC/B,CAAC,IAAI,CAAC;IACpBuF,EAAE,GAAG,CAACzD,CAAC,CAAC7B,CAAC,GAAG8B,CAAC,CAAC9B,CAAC,IAAI,CAAC;EACxB,GAAG;IACC,IAAMgB,CAAC,CAAChB,CAAC,GAAGsF,EAAE,KAAOtE,CAAC,CAACvB,IAAI,CAACO,CAAC,GAAGsF,EAAG,IAAKtE,CAAC,CAACvB,IAAI,CAACO,CAAC,KAAKgB,CAAC,CAAChB,CAAC,IAC/CqF,EAAE,GAAG,CAACrE,CAAC,CAACvB,IAAI,CAACM,CAAC,GAAGiB,CAAC,CAACjB,CAAC,KAAKuF,EAAE,GAAGtE,CAAC,CAAChB,CAAC,CAAC,IAAIgB,CAAC,CAACvB,IAAI,CAACO,CAAC,GAAGgB,CAAC,CAAChB,CAAC,CAAC,GAAGgB,CAAC,CAACjB,CAAE,EACjEuG,MAAM,GAAG,CAACA,MAAM;IACpBtF,CAAC,GAAGA,CAAC,CAACvB,IAAI;EACd,CAAC,QAAQuB,CAAC,KAAKa,CAAC;EAEhB,OAAOyE,MAAM;AACjB;;AAEA;AACA;AACA,SAASjD,YAAYA,CAACxB,CAAC,EAAEC,CAAC,EAAE;EACxB,IAAIyE,EAAE,GAAG,IAAIC,IAAI,CAAC3E,CAAC,CAAC1B,CAAC,EAAE0B,CAAC,CAAC9B,CAAC,EAAE8B,CAAC,CAAC7B,CAAC,CAAC;IAC5ByG,EAAE,GAAG,IAAID,IAAI,CAAC1E,CAAC,CAAC3B,CAAC,EAAE2B,CAAC,CAAC/B,CAAC,EAAE+B,CAAC,CAAC9B,CAAC,CAAC;IAC5B0G,EAAE,GAAG7E,CAAC,CAACpC,IAAI;IACXkH,EAAE,GAAG7E,CAAC,CAACpC,IAAI;EAEfmC,CAAC,CAACpC,IAAI,GAAGqC,CAAC;EACVA,CAAC,CAACpC,IAAI,GAAGmC,CAAC;EAEV0E,EAAE,CAAC9G,IAAI,GAAGiH,EAAE;EACZA,EAAE,CAAChH,IAAI,GAAG6G,EAAE;EAEZE,EAAE,CAAChH,IAAI,GAAG8G,EAAE;EACZA,EAAE,CAAC7G,IAAI,GAAG+G,EAAE;EAEZE,EAAE,CAAClH,IAAI,GAAGgH,EAAE;EACZA,EAAE,CAAC/G,IAAI,GAAGiH,EAAE;EAEZ,OAAOF,EAAE;AACb;;AAEA;AACA,SAAS7F,UAAUA,CAACT,CAAC,EAAEJ,CAAC,EAAEC,CAAC,EAAEU,IAAI,EAAE;EAC/B,IAAIM,CAAC,GAAG,IAAIwF,IAAI,CAACrG,CAAC,EAAEJ,CAAC,EAAEC,CAAC,CAAC;EAEzB,IAAI,CAACU,IAAI,EAAE;IACPM,CAAC,CAACtB,IAAI,GAAGsB,CAAC;IACVA,CAAC,CAACvB,IAAI,GAAGuB,CAAC;EAEd,CAAC,MAAM;IACHA,CAAC,CAACvB,IAAI,GAAGiB,IAAI,CAACjB,IAAI;IAClBuB,CAAC,CAACtB,IAAI,GAAGgB,IAAI;IACbA,IAAI,CAACjB,IAAI,CAACC,IAAI,GAAGsB,CAAC;IAClBN,IAAI,CAACjB,IAAI,GAAGuB,CAAC;EACjB;EACA,OAAOA,CAAC;AACZ;AAEA,SAASF,UAAUA,CAACE,CAAC,EAAE;EACnBA,CAAC,CAACvB,IAAI,CAACC,IAAI,GAAGsB,CAAC,CAACtB,IAAI;EACpBsB,CAAC,CAACtB,IAAI,CAACD,IAAI,GAAGuB,CAAC,CAACvB,IAAI;EAEpB,IAAIuB,CAAC,CAAC8B,KAAK,EAAE9B,CAAC,CAAC8B,KAAK,CAACE,KAAK,GAAGhC,CAAC,CAACgC,KAAK;EACpC,IAAIhC,CAAC,CAACgC,KAAK,EAAEhC,CAAC,CAACgC,KAAK,CAACF,KAAK,GAAG9B,CAAC,CAAC8B,KAAK;AACxC;AAEA,SAAS0D,IAAIA,CAACrG,CAAC,EAAEJ,CAAC,EAAEC,CAAC,EAAE;EACnB;EACA,IAAI,CAACG,CAAC,GAAGA,CAAC;;EAEV;EACA,IAAI,CAACJ,CAAC,GAAGA,CAAC;EACV,IAAI,CAACC,CAAC,GAAGA,CAAC;;EAEV;EACA,IAAI,CAACN,IAAI,GAAG,IAAI;EAChB,IAAI,CAACD,IAAI,GAAG,IAAI;;EAEhB;EACA,IAAI,CAACwD,CAAC,GAAG,CAAC;;EAEV;EACA,IAAI,CAACH,KAAK,GAAG,IAAI;EACjB,IAAI,CAACE,KAAK,GAAG,IAAI;;EAEjB;EACA,IAAI,CAAC9B,OAAO,GAAG,KAAK;AACxB;;AAEA;AACA;AACApC,MAAM,CAAC8H,SAAS,GAAG,UAAU5H,IAAI,EAAEC,WAAW,EAAEC,GAAG,EAAEM,SAAS,EAAE;EAC5D,IAAIL,QAAQ,GAAGF,WAAW,IAAIA,WAAW,CAACG,MAAM;EAChD,IAAIC,QAAQ,GAAGF,QAAQ,GAAGF,WAAW,CAAC,CAAC,CAAC,GAAGC,GAAG,GAAGF,IAAI,CAACI,MAAM;EAE5D,IAAIyH,WAAW,GAAGzG,IAAI,CAACsE,GAAG,CAAC/D,UAAU,CAAC3B,IAAI,EAAE,CAAC,EAAEK,QAAQ,EAAEH,GAAG,CAAC,CAAC;EAC9D,IAAIC,QAAQ,EAAE;IACV,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEoD,GAAG,GAAGtE,WAAW,CAACG,MAAM,EAAEe,CAAC,GAAGoD,GAAG,EAAEpD,CAAC,EAAE,EAAE;MACpD,IAAII,KAAK,GAAGtB,WAAW,CAACkB,CAAC,CAAC,GAAGjB,GAAG;MAChC,IAAIsB,GAAG,GAAGL,CAAC,GAAGoD,GAAG,GAAG,CAAC,GAAGtE,WAAW,CAACkB,CAAC,GAAG,CAAC,CAAC,GAAGjB,GAAG,GAAGF,IAAI,CAACI,MAAM;MAC9DyH,WAAW,IAAIzG,IAAI,CAACsE,GAAG,CAAC/D,UAAU,CAAC3B,IAAI,EAAEuB,KAAK,EAAEC,GAAG,EAAEtB,GAAG,CAAC,CAAC;IAC9D;EACJ;EAEA,IAAI4H,aAAa,GAAG,CAAC;EACrB,KAAK3G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,SAAS,CAACJ,MAAM,EAAEe,CAAC,IAAI,CAAC,EAAE;IACtC,IAAI0B,CAAC,GAAGrC,SAAS,CAACW,CAAC,CAAC,GAAGjB,GAAG;IAC1B,IAAI4C,CAAC,GAAGtC,SAAS,CAACW,CAAC,GAAG,CAAC,CAAC,GAAGjB,GAAG;IAC9B,IAAI6C,CAAC,GAAGvC,SAAS,CAACW,CAAC,GAAG,CAAC,CAAC,GAAGjB,GAAG;IAC9B4H,aAAa,IAAI1G,IAAI,CAACsE,GAAG,CACrB,CAAC1F,IAAI,CAAC6C,CAAC,CAAC,GAAG7C,IAAI,CAAC+C,CAAC,CAAC,KAAK/C,IAAI,CAAC8C,CAAC,GAAG,CAAC,CAAC,GAAG9C,IAAI,CAAC6C,CAAC,GAAG,CAAC,CAAC,CAAC,GACjD,CAAC7C,IAAI,CAAC6C,CAAC,CAAC,GAAG7C,IAAI,CAAC8C,CAAC,CAAC,KAAK9C,IAAI,CAAC+C,CAAC,GAAG,CAAC,CAAC,GAAG/C,IAAI,CAAC6C,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC1D;EAEA,OAAOgF,WAAW,KAAK,CAAC,IAAIC,aAAa,KAAK,CAAC,GAAG,CAAC,GAC/C1G,IAAI,CAACsE,GAAG,CAAC,CAACoC,aAAa,GAAGD,WAAW,IAAIA,WAAW,CAAC;AAC7D,CAAC;AAED,SAASlG,UAAUA,CAAC3B,IAAI,EAAEuB,KAAK,EAAEC,GAAG,EAAEtB,GAAG,EAAE;EACvC,IAAI6H,GAAG,GAAG,CAAC;EACX,KAAK,IAAI5G,CAAC,GAAGI,KAAK,EAAEyG,CAAC,GAAGxG,GAAG,GAAGtB,GAAG,EAAEiB,CAAC,GAAGK,GAAG,EAAEL,CAAC,IAAIjB,GAAG,EAAE;IAClD6H,GAAG,IAAI,CAAC/H,IAAI,CAACgI,CAAC,CAAC,GAAGhI,IAAI,CAACmB,CAAC,CAAC,KAAKnB,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,GAAGnB,IAAI,CAACgI,CAAC,GAAG,CAAC,CAAC,CAAC;IACxDA,CAAC,GAAG7G,CAAC;EACT;EACA,OAAO4G,GAAG;AACd;;AAEA;AACAjI,MAAM,CAACmI,OAAO,GAAG,UAAUjI,IAAI,EAAE;EAC7B,IAAIE,GAAG,GAAGF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACI,MAAM;IACvB8H,MAAM,GAAG;MAACC,QAAQ,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,UAAU,EAAEnI;IAAG,CAAC;IACnDoI,SAAS,GAAG,CAAC;EAEjB,KAAK,IAAInH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,IAAI,CAACI,MAAM,EAAEe,CAAC,EAAE,EAAE;IAClC,KAAK,IAAI6G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhI,IAAI,CAACmB,CAAC,CAAC,CAACf,MAAM,EAAE4H,CAAC,EAAE,EAAE;MACrC,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrI,GAAG,EAAEqI,CAAC,EAAE,EAAEL,MAAM,CAACC,QAAQ,CAACzF,IAAI,CAAC1C,IAAI,CAACmB,CAAC,CAAC,CAAC6G,CAAC,CAAC,CAACO,CAAC,CAAC,CAAC;IACrE;IACA,IAAIpH,CAAC,GAAG,CAAC,EAAE;MACPmH,SAAS,IAAItI,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,CAACf,MAAM;MAC/B8H,MAAM,CAACE,KAAK,CAAC1F,IAAI,CAAC4F,SAAS,CAAC;IAChC;EACJ;EACA,OAAOJ,MAAM;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}