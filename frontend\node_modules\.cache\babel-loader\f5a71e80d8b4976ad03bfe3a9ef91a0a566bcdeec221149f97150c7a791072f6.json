{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Projects\\\\Python\\\\EU4\\\\frontend\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport axios from \"axios\";\nimport WorldMap from './WorldMap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function App() {\n  _s();\n  const [countries, setCountries] = useState([]);\n  const [selected, setSelected] = useState(null);\n  const [turn, setTurn] = useState(1);\n  useEffect(() => {\n    axios.get(\"http://localhost:8000/countries\").then(r => setCountries(r.data));\n  }, [turn]);\n  const nextTurn = () => {\n    axios.post(\"http://localhost:8000/turn\").then(r => {\n      setTurn(r.data.turn);\n      setCountries(r.data.countries);\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Empires & Revolutions (Earth Edition)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: nextTurn,\n      children: \"Next Turn\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: [\" Turn: \", turn, \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(WorldMap, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), selected && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: selected.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Population: \", selected.population]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Resources: \", selected.resources]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"5Gb4EtThYoXRgBbDHtGsBUrNIas=\");\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "WorldMap", "jsxDEV", "_jsxDEV", "App", "_s", "countries", "setCountries", "selected", "setSelected", "turn", "setTurn", "get", "then", "r", "data", "nextTurn", "post", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "name", "population", "resources", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Projects/Python/EU4/frontend/src/App.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport axios from \"axios\";\r\nimport WorldMap from './WorldMap';\r\n\r\nexport default function App() {\r\n  const [countries, setCountries] = useState([]);\r\n  const [selected, setSelected] = useState(null);\r\n  const [turn, setTurn] = useState(1);\r\n\r\n  useEffect(() => {\r\n    axios.get(\"http://localhost:8000/countries\").then(r => setCountries(r.data));\r\n  }, [turn]);\r\n\r\n  const nextTurn = () => {\r\n    axios.post(\"http://localhost:8000/turn\").then(r => {\r\n      setTurn(r.data.turn);\r\n      setCountries(r.data.countries);\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <h1>Empires & Revolutions (Earth Edition)</h1>\r\n      <button onClick={nextTurn}>Next Turn</button>\r\n      <span> Turn: {turn} </span>\r\n      <WorldMap />\r\n      {selected && (\r\n        <div>\r\n          <h2>{selected.name}</h2>\r\n          <p>Population: {selected.population}</p>\r\n          <p>Resources: {selected.resources}</p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,eAAe,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACW,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC,CAAC,CAAC;EAEnCD,SAAS,CAAC,MAAM;IACdE,KAAK,CAACY,GAAG,CAAC,iCAAiC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIP,YAAY,CAACO,CAAC,CAACC,IAAI,CAAC,CAAC;EAC9E,CAAC,EAAE,CAACL,IAAI,CAAC,CAAC;EAEV,MAAMM,QAAQ,GAAGA,CAAA,KAAM;IACrBhB,KAAK,CAACiB,IAAI,CAAC,4BAA4B,CAAC,CAACJ,IAAI,CAACC,CAAC,IAAI;MACjDH,OAAO,CAACG,CAAC,CAACC,IAAI,CAACL,IAAI,CAAC;MACpBH,YAAY,CAACO,CAAC,CAACC,IAAI,CAACT,SAAS,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC;EAED,oBACEH,OAAA;IAAAe,QAAA,gBACEf,OAAA;MAAAe,QAAA,EAAI;IAAqC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC9CnB,OAAA;MAAQoB,OAAO,EAAEP,QAAS;MAAAE,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAC7CnB,OAAA;MAAAe,QAAA,GAAM,SAAO,EAACR,IAAI,EAAC,GAAC;IAAA;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC3BnB,OAAA,CAACF,QAAQ;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACXd,QAAQ,iBACPL,OAAA;MAAAe,QAAA,gBACEf,OAAA;QAAAe,QAAA,EAAKV,QAAQ,CAACgB;MAAI;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxBnB,OAAA;QAAAe,QAAA,GAAG,cAAY,EAACV,QAAQ,CAACiB,UAAU;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxCnB,OAAA;QAAAe,QAAA,GAAG,aAAW,EAACV,QAAQ,CAACkB,SAAS;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACjB,EAAA,CA/BuBD,GAAG;AAAAuB,EAAA,GAAHvB,GAAG;AAAA,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}