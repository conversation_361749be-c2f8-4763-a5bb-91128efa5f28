{"ast": null, "code": "import { Texture } from \"@pixi/core\";\nimport { Cache } from \"../../../../cache/Cache.mjs\";\nfunction createTexture(base, loader, url) {\n  base.resource.internal = !0;\n  const texture = new Texture(base),\n    unload = () => {\n      delete loader.promiseCache[url], Cache.has(url) && Cache.remove(url);\n    };\n  return texture.baseTexture.once(\"destroyed\", () => {\n    url in loader.promiseCache && (console.warn(\"[Assets] A BaseTexture managed by Assets was destroyed instead of unloaded! Use Assets.unload() instead of destroying the BaseTexture.\"), unload());\n  }), texture.once(\"destroyed\", () => {\n    base.destroyed || (console.warn(\"[Assets] A Texture managed by Assets was destroyed instead of unloaded! Use Assets.unload() instead of destroying the Texture.\"), unload());\n  }), texture;\n}\nexport { createTexture };", "map": {"version": 3, "names": ["createTexture", "base", "loader", "url", "resource", "internal", "texture", "Texture", "unload", "promiseCache", "<PERSON><PERSON>", "has", "remove", "baseTexture", "once", "console", "warn", "destroyed"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\assets\\src\\loader\\parsers\\textures\\utils\\createTexture.ts"], "sourcesContent": ["import { Texture } from '@pixi/core';\nimport { Cache } from '../../../../cache/Cache';\n\nimport type { BaseTexture } from '@pixi/core';\nimport type { Loader } from '../../../Loader';\n\nexport function createTexture(base: BaseTexture, loader: Loader, url: string)\n{\n    // make sure the resource is destroyed when the base texture is destroyed\n    base.resource.internal = true;\n\n    const texture = new Texture(base);\n    const unload = () =>\n    {\n        delete loader.promiseCache[url];\n\n        if (Cache.has(url))\n        {\n            Cache.remove(url);\n        }\n    };\n\n    // remove the promise from the loader and the url from the cache when the texture is destroyed\n    texture.baseTexture.once('destroyed', () =>\n    {\n        if (url in loader.promiseCache)\n        {\n            console.warn('[Assets] A BaseTexture managed by Assets was destroyed instead of unloaded! '\n                + 'Use Assets.unload() instead of destroying the BaseTexture.');\n            unload();\n        }\n    });\n    texture.once('destroyed', () =>\n    {\n        if (!base.destroyed)\n        {\n            console.warn('[Assets] A Texture managed by Assets was destroyed instead of unloaded! '\n                + 'Use Assets.unload() instead of destroying the Texture.');\n            unload();\n        }\n    });\n\n    return texture;\n}\n"], "mappings": ";;AAMgB,SAAAA,cAAcC,IAAA,EAAmBC,MAAA,EAAgBC,GAAA,EACjE;EAEIF,IAAA,CAAKG,QAAA,CAASC,QAAA,GAAW;EAEzB,MAAMC,OAAA,GAAU,IAAIC,OAAA,CAAQN,IAAI;IAC1BO,MAAA,GAASA,CAAA,KACf;MACW,OAAAN,MAAA,CAAOO,YAAA,CAAaN,GAAG,GAE1BO,KAAA,CAAMC,GAAA,CAAIR,GAAG,KAEbO,KAAA,CAAME,MAAA,CAAOT,GAAG;IAAA;EAKhB,OAAAG,OAAA,CAAAO,WAAA,CAAYC,IAAA,CAAK,aAAa,MACtC;IACQX,GAAA,IAAOD,MAAA,CAAOO,YAAA,KAEdM,OAAA,CAAQC,IAAA,CAAK,wIACqD,GAClER,MAAA,CAAO;EAEd,IACDF,OAAA,CAAQQ,IAAA,CAAK,aAAa,MAC1B;IACSb,IAAA,CAAKgB,SAAA,KAENF,OAAA,CAAQC,IAAA,CAAK,gIACiD,GAC9DR,MAAA,CAAO;EAEd,IAEMF,OAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}