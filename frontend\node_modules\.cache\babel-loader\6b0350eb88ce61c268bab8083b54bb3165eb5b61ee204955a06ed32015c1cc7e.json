{"ast": null, "code": "import { utils } from \"@pixi/core\";\nimport { AlphaFilter } from \"@pixi/filter-alpha\";\nimport { BlurFilter, BlurFilterPass } from \"@pixi/filter-blur\";\nimport { ColorMatrixFilter } from \"@pixi/filter-color-matrix\";\nimport { DisplacementFilter } from \"@pixi/filter-displacement\";\nimport { FXAAFilter } from \"@pixi/filter-fxaa\";\nimport { NoiseFilter } from \"@pixi/filter-noise\";\nconst filters = {\n  /**\n   * @class\n   * @memberof PIXI.filters\n   * @deprecated since 7.1.0\n   * @see PIXI.AlphaFilter\n   */\n  AlphaFilter,\n  /**\n   * @class\n   * @memberof PIXI.filters\n   * @deprecated since 7.1.0\n   * @see PIXI.BlurFilter\n   */\n  BlurFilter,\n  /**\n   * @class\n   * @memberof PIXI.filters\n   * @deprecated since 7.1.0\n   * @see PIXI.BlurFilterPass\n   */\n  BlurFilterPass,\n  /**\n   * @class\n   * @memberof PIXI.filters\n   * @deprecated since 7.1.0\n   * @see PIXI.ColorMatrixFilter\n   */\n  ColorMatrixFilter,\n  /**\n   * @class\n   * @memberof PIXI.filters\n   * @deprecated since 7.1.0\n   * @see PIXI.DisplacementFilter\n   */\n  DisplacementFilter,\n  /**\n   * @class\n   * @memberof PIXI.filters\n   * @deprecated since 7.1.0\n   * @see PIXI.FXAAFilter\n   */\n  FXAAFilter,\n  /**\n   * @class\n   * @memberof PIXI.filters\n   * @deprecated since 7.1.0\n   * @see PIXI.NoiseFilter\n   */\n  NoiseFilter\n};\nObject.entries(filters).forEach(([key, FilterClass]) => {\n  Object.defineProperty(filters, key, {\n    get() {\n      return utils.deprecation(\"7.1.0\", `filters.${key} has moved to ${key}`), FilterClass;\n    }\n  });\n});\nexport { filters };", "map": {"version": 3, "names": ["filters", "AlphaFilter", "BlurFilter", "Blur<PERSON>ilterPass", "ColorMatrixFilter", "DisplacementFilter", "FXAAFilter", "NoiseFilter", "Object", "entries", "for<PERSON>ach", "key", "FilterClass", "defineProperty", "get", "utils", "deprecation"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\pixi.js\\src\\filters.ts"], "sourcesContent": ["import { utils } from '@pixi/core';\nimport { AlphaFilter } from '@pixi/filter-alpha';\nimport { BlurFilter, BlurFilterPass } from '@pixi/filter-blur';\nimport { ColorMatrixFilter } from '@pixi/filter-color-matrix';\nimport { DisplacementFilter } from '@pixi/filter-displacement';\nimport { FXAAFilter } from '@pixi/filter-fxaa';\nimport { NoiseFilter } from '@pixi/filter-noise';\n\n/**\n * Filters namespace has been removed. All filters are now available directly from the root of the package.\n * @namespace PIXI.filters\n * @deprecated\n */\nconst filters = {\n    /**\n     * @class\n     * @memberof PIXI.filters\n     * @deprecated since 7.1.0\n     * @see PIXI.AlphaFilter\n     */\n    AlphaFilter,\n    /**\n     * @class\n     * @memberof PIXI.filters\n     * @deprecated since 7.1.0\n     * @see PIXI.BlurFilter\n     */\n    BlurFilter,\n    /**\n     * @class\n     * @memberof PIXI.filters\n     * @deprecated since 7.1.0\n     * @see PIXI.BlurFilterPass\n     */\n    BlurFilterPass,\n    /**\n     * @class\n     * @memberof PIXI.filters\n     * @deprecated since 7.1.0\n     * @see PIXI.ColorMatrixFilter\n     */\n    ColorMatrixFilter,\n    /**\n     * @class\n     * @memberof PIXI.filters\n     * @deprecated since 7.1.0\n     * @see PIXI.DisplacementFilter\n     */\n    DisplacementFilter,\n    /**\n     * @class\n     * @memberof PIXI.filters\n     * @deprecated since 7.1.0\n     * @see PIXI.FXAAFilter\n     */\n    FXAAFilter,\n    /**\n     * @class\n     * @memberof PIXI.filters\n     * @deprecated since 7.1.0\n     * @see PIXI.NoiseFilter\n     */\n    NoiseFilter,\n};\n\nObject.entries(filters).forEach(([key, FilterClass]) =>\n{\n    Object.defineProperty(filters, key, {\n        get()\n        {\n            // #if _DEBUG\n            utils.deprecation('7.1.0', `filters.${key} has moved to ${key}`);\n            // #endif\n\n            return FilterClass;\n        },\n    });\n});\n\nexport { filters };\n"], "mappings": ";;;;;;;AAaA,MAAMA,OAAA,GAAU;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOZC,WAAA;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOAC,UAAA;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOAC,cAAA;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOAC,iBAAA;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOAC,kBAAA;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOAC,UAAA;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOAC;AACJ;AAEAC,MAAA,CAAOC,OAAA,CAAQT,OAAO,EAAEU,OAAA,CAAQ,CAAC,CAACC,GAAA,EAAKC,WAAW,MAClD;EACWJ,MAAA,CAAAK,cAAA,CAAeb,OAAA,EAASW,GAAA,EAAK;IAChCG,IAAA,EACA;MAEI,OAAAC,KAAA,CAAMC,WAAA,CAAY,SAAS,WAAWL,GAAG,iBAAiBA,GAAG,EAAE,GAGxDC,WAAA;IACX;EAAA,CACH;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}