{"ast": null, "code": "class BackgroundLoader {\n  /**\n   * @param loader\n   * @param verbose - should the loader log to the console\n   */\n  constructor(loader, verbose = !1) {\n    this._loader = loader, this._assetList = [], this._isLoading = !1, this._maxConcurrent = 1, this.verbose = verbose;\n  }\n  /**\n   * Adds an array of assets to load.\n   * @param assetUrls - assets to load\n   */\n  add(assetUrls) {\n    assetUrls.forEach(a => {\n      this._assetList.push(a);\n    }), this.verbose && console.log(\"[BackgroundLoader] assets: \", this._assetList), this._isActive && !this._isLoading && this._next();\n  }\n  /**\n   * Loads the next set of assets. Will try to load as many assets as it can at the same time.\n   *\n   * The max assets it will try to load at one time will be 4.\n   */\n  async _next() {\n    if (this._assetList.length && this._isActive) {\n      this._isLoading = !0;\n      const toLoad = [],\n        toLoadAmount = Math.min(this._assetList.length, this._maxConcurrent);\n      for (let i = 0; i < toLoadAmount; i++) toLoad.push(this._assetList.pop());\n      await this._loader.load(toLoad), this._isLoading = !1, this._next();\n    }\n  }\n  /**\n   * Activate/Deactivate the loading. If set to true then it will immediately continue to load the next asset.\n   * @returns whether the class is active\n   */\n  get active() {\n    return this._isActive;\n  }\n  set active(value) {\n    this._isActive !== value && (this._isActive = value, value && !this._isLoading && this._next());\n  }\n}\nexport { BackgroundLoader };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "loader", "verbose", "_loader", "_assetList", "_isLoading", "_maxConcurrent", "add", "assetUrls", "for<PERSON>ach", "a", "push", "console", "log", "_isActive", "_next", "length", "toLoad", "toLoadAmount", "Math", "min", "i", "pop", "load", "active", "value"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\assets\\src\\BackgroundLoader.ts"], "sourcesContent": ["import type { Loader } from './loader/Loader';\nimport type { ResolvedAsset } from './types';\n\n/**\n * Quietly Loads assets in the background.\n * @memberof PIXI\n */\nexport class BackgroundLoader\n{\n    /** Whether or not the loader should continue loading. */\n    private _isActive: boolean;\n\n    /** Assets to load. */\n    private readonly _assetList: ResolvedAsset[];\n\n    /** Whether or not the loader is loading. */\n    private _isLoading: boolean;\n\n    /** Number of assets to load at a time. */\n    private readonly _maxConcurrent: number;\n\n    /** Should the loader log to the console. */\n    public verbose: boolean;\n    private readonly _loader: Loader;\n\n    /**\n     * @param loader\n     * @param verbose - should the loader log to the console\n     */\n    constructor(loader: Loader, verbose = false)\n    {\n        this._loader = loader;\n        this._assetList = [];\n        this._isLoading = false;\n        this._maxConcurrent = 1;\n        this.verbose = verbose;\n    }\n\n    /**\n     * Adds an array of assets to load.\n     * @param assetUrls - assets to load\n     */\n    public add(assetUrls: ResolvedAsset[]): void\n    {\n        assetUrls.forEach((a) =>\n        {\n            this._assetList.push(a);\n        });\n\n        if (this.verbose)\n        {\n            // eslint-disable-next-line no-console\n            console.log('[BackgroundLoader] assets: ', this._assetList);\n        }\n\n        if (this._isActive && !this._isLoading)\n        {\n            this._next();\n        }\n    }\n\n    /**\n     * Loads the next set of assets. Will try to load as many assets as it can at the same time.\n     *\n     * The max assets it will try to load at one time will be 4.\n     */\n    private async _next(): Promise<void>\n    {\n        if (this._assetList.length && this._isActive)\n        {\n            this._isLoading = true;\n\n            const toLoad = [];\n\n            const toLoadAmount = Math.min(this._assetList.length, this._maxConcurrent);\n\n            for (let i = 0; i < toLoadAmount; i++)\n            {\n                toLoad.push(this._assetList.pop());\n            }\n\n            await this._loader.load(toLoad);\n\n            this._isLoading = false;\n\n            this._next();\n        }\n    }\n\n    /**\n     * Activate/Deactivate the loading. If set to true then it will immediately continue to load the next asset.\n     * @returns whether the class is active\n     */\n    get active(): boolean\n    {\n        return this._isActive;\n    }\n\n    set active(value: boolean)\n    {\n        if (this._isActive === value) return;\n\n        this._isActive = value;\n\n        if (value && !this._isLoading)\n        {\n            this._next();\n        }\n    }\n}\n"], "mappings": "AAOO,MAAMA,gBAAA,CACb;EAAA;AAAA;AAAA;AAAA;EAqBIC,YAAYC,MAAA,EAAgBC,OAAA,GAAU,IACtC;IACI,KAAKC,OAAA,GAAUF,MAAA,EACf,KAAKG,UAAA,GAAa,EAAC,EACnB,KAAKC,UAAA,GAAa,IAClB,KAAKC,cAAA,GAAiB,GACtB,KAAKJ,OAAA,GAAUA,OAAA;EACnB;EAAA;AAAA;AAAA;AAAA;EAMOK,IAAIC,SAAA,EACX;IACcA,SAAA,CAAAC,OAAA,CAASC,CAAA,IACnB;MACS,KAAAN,UAAA,CAAWO,IAAA,CAAKD,CAAC;IAAA,CACzB,GAEG,KAAKR,OAAA,IAGLU,OAAA,CAAQC,GAAA,CAAI,+BAA+B,KAAKT,UAAU,GAG1D,KAAKU,SAAA,IAAa,CAAC,KAAKT,UAAA,IAExB,KAAKU,KAAA;EAEb;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,MAAcA,MAAA,EACd;IACI,IAAI,KAAKX,UAAA,CAAWY,MAAA,IAAU,KAAKF,SAAA,EACnC;MACI,KAAKT,UAAA,GAAa;MAEZ,MAAAY,MAAA,GAAS;QAETC,YAAA,GAAeC,IAAA,CAAKC,GAAA,CAAI,KAAKhB,UAAA,CAAWY,MAAA,EAAQ,KAAKV,cAAc;MAEhE,SAAAe,CAAA,GAAI,GAAGA,CAAA,GAAIH,YAAA,EAAcG,CAAA,IAE9BJ,MAAA,CAAON,IAAA,CAAK,KAAKP,UAAA,CAAWkB,GAAA,CAAK;MAG/B,WAAKnB,OAAA,CAAQoB,IAAA,CAAKN,MAAM,GAE9B,KAAKZ,UAAA,GAAa,IAElB,KAAKU,KAAA,CAAM;IACf;EACJ;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIS,OAAA,EACJ;IACI,OAAO,KAAKV,SAAA;EAChB;EAEA,IAAIU,OAAOC,KAAA,EACX;IACQ,KAAKX,SAAA,KAAcW,KAAA,KAEvB,KAAKX,SAAA,GAAYW,KAAA,EAEbA,KAAA,IAAS,CAAC,KAAKpB,UAAA,IAEf,KAAKU,KAAA,CAAM;EAEnB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}