{"ast": null, "code": "import { Application } from \"./Application.mjs\";\nimport { ResizePlugin } from \"./ResizePlugin.mjs\";\nexport { Application, ResizePlugin };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import { Application } from \"./Application.mjs\";\nimport { ResizePlugin } from \"./ResizePlugin.mjs\";\nexport {\n  Application,\n  ResizePlugin\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}