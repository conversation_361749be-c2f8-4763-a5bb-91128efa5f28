{"ast": null, "code": "import { Color } from \"@pixi/color\";\nimport { ExtensionType, extensions } from \"@pixi/extensions\";\nclass BackgroundSystem {\n  constructor() {\n    this.clearBeforeRender = !0, this._backgroundColor = new Color(0), this.alpha = 1;\n  }\n  /**\n   * initiates the background system\n   * @param {PIXI.IRendererOptions} options - the options for the background colors\n   */\n  init(options) {\n    this.clearBeforeRender = options.clearBeforeRender;\n    const {\n        backgroundColor,\n        background,\n        backgroundAlpha\n      } = options,\n      color = background ?? backgroundColor;\n    color !== void 0 && (this.color = color), this.alpha = backgroundAlpha;\n  }\n  /**\n   * The background color to fill if not transparent.\n   * @member {PIXI.ColorSource}\n   */\n  get color() {\n    return this._backgroundColor.value;\n  }\n  set color(value) {\n    this._backgroundColor.setValue(value);\n  }\n  /**\n   * The background color alpha. Setting this to 0 will make the canvas transparent.\n   * @member {number}\n   */\n  get alpha() {\n    return this._backgroundColor.alpha;\n  }\n  set alpha(value) {\n    this._backgroundColor.setAlpha(value);\n  }\n  /** The background color object. */\n  get backgroundColor() {\n    return this._backgroundColor;\n  }\n  destroy() {}\n}\nBackgroundSystem.defaultOptions = {\n  /**\n   * {@link PIXI.IRendererOptions.backgroundAlpha}\n   * @default 1\n   * @memberof PIXI.settings.RENDER_OPTIONS\n   */\n  backgroundAlpha: 1,\n  /**\n   * {@link PIXI.IRendererOptions.backgroundColor}\n   * @default 0x000000\n   * @memberof PIXI.settings.RENDER_OPTIONS\n   */\n  backgroundColor: 0,\n  /**\n   * {@link PIXI.IRendererOptions.clearBeforeRender}\n   * @default true\n   * @memberof PIXI.settings.RENDER_OPTIONS\n   */\n  clearBeforeRender: !0\n}, /** @ignore */\nBackgroundSystem.extension = {\n  type: [ExtensionType.RendererSystem, ExtensionType.CanvasRendererSystem],\n  name: \"background\"\n};\nextensions.add(BackgroundSystem);\nexport { BackgroundSystem };", "map": {"version": 3, "names": ["BackgroundSystem", "constructor", "clearBeforeRender", "_backgroundColor", "Color", "alpha", "init", "options", "backgroundColor", "background", "backgroundAlpha", "color", "value", "setValue", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "defaultOptions", "extension", "type", "ExtensionType", "RendererSystem", "CanvasRendererSystem", "name", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\background\\BackgroundSystem.ts"], "sourcesContent": ["import { Color } from '@pixi/color';\nimport { extensions, ExtensionType } from '@pixi/extensions';\n\nimport type { ColorSource } from '@pixi/color';\nimport type { ExtensionMetadata } from '@pixi/extensions';\nimport type { ISystem } from '../system/ISystem';\n\n/**\n * Options for the background system.\n * @memberof PIXI\n * @deprecated since 7.2.3\n * @see PIXI.BackgroundSystemOptions\n */\nexport type BackgroundSytemOptions = BackgroundSystemOptions;\n\n/**\n * Options for the background system.\n * @memberof PIXI\n */\nexport interface BackgroundSystemOptions\n{\n    /**\n     * The background color used to clear the canvas. See {@link PIXI.ColorSource} for accepted color values.\n     * @memberof PIXI.IRendererOptions\n     */\n    backgroundColor: ColorSource;\n    /**\n     * Alias for {@link PIXI.IRendererOptions.backgroundColor}\n     * @memberof PIXI.IRendererOptions\n     */\n    background?: ColorSource;\n    /**\n     * Transparency of the background color, value from `0` (fully transparent) to `1` (fully opaque).\n     * @memberof PIXI.IRendererOptions\n     */\n    backgroundAlpha: number;\n    /**\n     * Whether to clear the canvas before new render passes.\n     * @memberof PIXI.IRendererOptions\n     */\n    clearBeforeRender: boolean;\n}\n\n/**\n * The background system manages the background color and alpha of the main view.\n * @memberof PIXI\n */\nexport class BackgroundSystem implements ISystem<BackgroundSystemOptions>\n{\n    static defaultOptions: BackgroundSystemOptions = {\n        /**\n         * {@link PIXI.IRendererOptions.backgroundAlpha}\n         * @default 1\n         * @memberof PIXI.settings.RENDER_OPTIONS\n         */\n        backgroundAlpha: 1,\n        /**\n         * {@link PIXI.IRendererOptions.backgroundColor}\n         * @default 0x000000\n         * @memberof PIXI.settings.RENDER_OPTIONS\n         */\n        backgroundColor: 0x0,\n        /**\n         * {@link PIXI.IRendererOptions.clearBeforeRender}\n         * @default true\n         * @memberof PIXI.settings.RENDER_OPTIONS\n         */\n        clearBeforeRender: true,\n    };\n\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        type: [\n            ExtensionType.RendererSystem,\n            ExtensionType.CanvasRendererSystem\n        ],\n        name: 'background',\n    };\n\n    /**\n     * This sets if the CanvasRenderer will clear the canvas or not before the new render pass.\n     * If the scene is NOT transparent PixiJS will use a canvas sized fillRect operation every\n     * frame to set the canvas background color. If the scene is transparent PixiJS will use clearRect\n     * to clear the canvas every frame. Disable this by setting this to false. For example, if\n     * your game has a canvas filling background image you often don't need this set.\n     * @member {boolean}\n     * @default\n     */\n    public clearBeforeRender: boolean;\n\n    /** Reference to the internal color */\n    private _backgroundColor: Color;\n\n    constructor()\n    {\n        this.clearBeforeRender = true;\n        this._backgroundColor = new Color(0x0);\n        this.alpha = 1;\n    }\n\n    /**\n     * initiates the background system\n     * @param {PIXI.IRendererOptions} options - the options for the background colors\n     */\n    init(options: BackgroundSystemOptions): void\n    {\n        this.clearBeforeRender = options.clearBeforeRender;\n        const { backgroundColor, background, backgroundAlpha } = options;\n        const color = background ?? backgroundColor;\n\n        if (color !== undefined)\n        {\n            this.color = color;\n        }\n\n        this.alpha = backgroundAlpha;\n    }\n\n    /**\n     * The background color to fill if not transparent.\n     * @member {PIXI.ColorSource}\n     */\n    get color(): ColorSource\n    {\n        return this._backgroundColor.value;\n    }\n\n    set color(value: ColorSource)\n    {\n        this._backgroundColor.setValue(value);\n    }\n\n    /**\n     * The background color alpha. Setting this to 0 will make the canvas transparent.\n     * @member {number}\n     */\n    get alpha(): number\n    {\n        return this._backgroundColor.alpha;\n    }\n\n    set alpha(value: number)\n    {\n        this._backgroundColor.setAlpha(value);\n    }\n\n    /** The background color object. */\n    get backgroundColor(): Color\n    {\n        return this._backgroundColor;\n    }\n\n    destroy(): void\n    {\n        // ka boom!\n    }\n}\n\nextensions.add(BackgroundSystem);\n"], "mappings": ";;AA+CO,MAAMA,gBAAA,CACb;EA6CIC,YAAA,EACA;IACS,KAAAC,iBAAA,GAAoB,IACzB,KAAKC,gBAAA,GAAmB,IAAIC,KAAA,CAAM,CAAG,GACrC,KAAKC,KAAA,GAAQ;EACjB;EAAA;AAAA;AAAA;AAAA;EAMAC,KAAKC,OAAA,EACL;IACI,KAAKL,iBAAA,GAAoBK,OAAA,CAAQL,iBAAA;IACjC,MAAM;QAAEM,eAAA;QAAiBC,UAAA;QAAYC;MAAA,IAAoBH,OAAA;MACnDI,KAAA,GAAQF,UAAA,IAAcD,eAAA;IAExBG,KAAA,KAAU,WAEV,KAAKA,KAAA,GAAQA,KAAA,GAGjB,KAAKN,KAAA,GAAQK,eAAA;EACjB;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIC,MAAA,EACJ;IACI,OAAO,KAAKR,gBAAA,CAAiBS,KAAA;EACjC;EAEA,IAAID,MAAMC,KAAA,EACV;IACS,KAAAT,gBAAA,CAAiBU,QAAA,CAASD,KAAK;EACxC;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIP,MAAA,EACJ;IACI,OAAO,KAAKF,gBAAA,CAAiBE,KAAA;EACjC;EAEA,IAAIA,MAAMO,KAAA,EACV;IACS,KAAAT,gBAAA,CAAiBW,QAAA,CAASF,KAAK;EACxC;EAAA;EAGA,IAAIJ,gBAAA,EACJ;IACI,OAAO,KAAKL,gBAAA;EAChB;EAEAY,QAAA,EACA,CAEA;AACJ;AA7Gaf,gBAAA,CAEFgB,cAAA,GAA0C;EAAA;AAAA;AAAA;AAAA;AAAA;EAM7CN,eAAA,EAAiB;EAAA;AAAA;AAAA;AAAA;AAAA;EAMjBF,eAAA,EAAiB;EAAA;AAAA;AAAA;AAAA;AAAA;EAMjBN,iBAAA,EAAmB;AACvB;AArBSF,gBAAA,CAwBFiB,SAAA,GAA+B;EAClCC,IAAA,EAAM,CACFC,aAAA,CAAcC,cAAA,EACdD,aAAA,CAAcE,oBAAA,CAClB;EACAC,IAAA,EAAM;AACV;AAiFJC,UAAA,CAAWC,GAAA,CAAIxB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}