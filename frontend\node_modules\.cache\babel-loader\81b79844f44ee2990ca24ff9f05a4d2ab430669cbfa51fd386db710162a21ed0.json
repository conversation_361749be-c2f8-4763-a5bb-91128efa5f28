{"ast": null, "code": "let GL_TABLE = null;\nconst GL_TO_GLSL_TYPES = {\n  FLOAT: \"float\",\n  FLOAT_VEC2: \"vec2\",\n  FLOAT_VEC3: \"vec3\",\n  FLOAT_VEC4: \"vec4\",\n  INT: \"int\",\n  INT_VEC2: \"ivec2\",\n  INT_VEC3: \"ivec3\",\n  INT_VEC4: \"ivec4\",\n  UNSIGNED_INT: \"uint\",\n  UNSIGNED_INT_VEC2: \"uvec2\",\n  UNSIGNED_INT_VEC3: \"uvec3\",\n  UNSIGNED_INT_VEC4: \"uvec4\",\n  BOOL: \"bool\",\n  BOOL_VEC2: \"bvec2\",\n  BOOL_VEC3: \"bvec3\",\n  BOOL_VEC4: \"bvec4\",\n  FLOAT_MAT2: \"mat2\",\n  FLOAT_MAT3: \"mat3\",\n  FLOAT_MAT4: \"mat4\",\n  SAMPLER_2D: \"sampler2D\",\n  INT_SAMPLER_2D: \"sampler2D\",\n  UNSIGNED_INT_SAMPLER_2D: \"sampler2D\",\n  SAMPLER_CUBE: \"samplerCube\",\n  INT_SAMPLER_CUBE: \"samplerCube\",\n  UNSIGNED_INT_SAMPLER_CUBE: \"samplerCube\",\n  SAMPLER_2D_ARRAY: \"sampler2DArray\",\n  INT_SAMPLER_2D_ARRAY: \"sampler2DArray\",\n  UNSIGNED_INT_SAMPLER_2D_ARRAY: \"sampler2DArray\"\n};\nfunction mapType(gl, type) {\n  if (!GL_TABLE) {\n    const typeNames = Object.keys(GL_TO_GLSL_TYPES);\n    GL_TABLE = {};\n    for (let i = 0; i < typeNames.length; ++i) {\n      const tn = typeNames[i];\n      GL_TABLE[gl[tn]] = GL_TO_GLSL_TYPES[tn];\n    }\n  }\n  return GL_TABLE[type];\n}\nexport { mapType };", "map": {"version": 3, "names": ["GL_TABLE", "GL_TO_GLSL_TYPES", "FLOAT", "FLOAT_VEC2", "FLOAT_VEC3", "FLOAT_VEC4", "INT", "INT_VEC2", "INT_VEC3", "INT_VEC4", "UNSIGNED_INT", "UNSIGNED_INT_VEC2", "UNSIGNED_INT_VEC3", "UNSIGNED_INT_VEC4", "BOOL", "BOOL_VEC2", "BOOL_VEC3", "BOOL_VEC4", "FLOAT_MAT2", "FLOAT_MAT3", "FLOAT_MAT4", "SAMPLER_2D", "INT_SAMPLER_2D", "UNSIGNED_INT_SAMPLER_2D", "SAMPLER_CUBE", "INT_SAMPLER_CUBE", "UNSIGNED_INT_SAMPLER_CUBE", "SAMPLER_2D_ARRAY", "INT_SAMPLER_2D_ARRAY", "UNSIGNED_INT_SAMPLER_2D_ARRAY", "mapType", "gl", "type", "typeNames", "Object", "keys", "i", "length", "tn"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\shader\\utils\\mapType.ts"], "sourcesContent": ["import type { Dict } from '@pixi/utils';\n\nlet GL_TABLE: Dict<string> = null;\n\nconst GL_TO_GLSL_TYPES: Dict<string> = {\n    FLOAT:       'float',\n    FLOAT_VEC2:  'vec2',\n    FLOAT_VEC3:  'vec3',\n    FLOAT_VEC4:  'vec4',\n\n    INT:         'int',\n    INT_VEC2:    'ivec2',\n    INT_VEC3:    'ivec3',\n    INT_VEC4:    'ivec4',\n\n    UNSIGNED_INT:         'uint',\n    UNSIGNED_INT_VEC2:    'uvec2',\n    UNSIGNED_INT_VEC3:    'uvec3',\n    UNSIGNED_INT_VEC4:    'uvec4',\n\n    BOOL:        'bool',\n    BOOL_VEC2:   'bvec2',\n    BOOL_VEC3:   'bvec3',\n    BOOL_VEC4:   'bvec4',\n\n    FLOAT_MAT2:  'mat2',\n    FLOAT_MAT3:  'mat3',\n    FLOAT_MAT4:  'mat4',\n\n    SAMPLER_2D:              'sampler2D',\n    INT_SAMPLER_2D:          'sampler2D',\n    UNSIGNED_INT_SAMPLER_2D: 'sampler2D',\n    SAMPLER_CUBE:              'samplerCube',\n    INT_SAMPLER_CUBE:          'samplerCube',\n    UNSIGNED_INT_SAMPLER_CUBE: 'samplerCube',\n    SAMPLER_2D_ARRAY:              'sampler2DArray',\n    INT_SAMPLER_2D_ARRAY:          'sampler2DArray',\n    UNSIGNED_INT_SAMPLER_2D_ARRAY: 'sampler2DArray',\n};\n\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nexport function mapType(gl: any, type: number): string\n{\n    if (!GL_TABLE)\n    {\n        const typeNames = Object.keys(GL_TO_GLSL_TYPES);\n\n        GL_TABLE = {};\n\n        for (let i = 0; i < typeNames.length; ++i)\n        {\n            const tn = typeNames[i];\n\n            GL_TABLE[gl[tn]] = GL_TO_GLSL_TYPES[tn];\n        }\n    }\n\n    return GL_TABLE[type];\n}\n"], "mappings": "AAEA,IAAIA,QAAA,GAAyB;AAE7B,MAAMC,gBAAA,GAAiC;EACnCC,KAAA,EAAa;EACbC,UAAA,EAAa;EACbC,UAAA,EAAa;EACbC,UAAA,EAAa;EAEbC,GAAA,EAAa;EACbC,QAAA,EAAa;EACbC,QAAA,EAAa;EACbC,QAAA,EAAa;EAEbC,YAAA,EAAsB;EACtBC,iBAAA,EAAsB;EACtBC,iBAAA,EAAsB;EACtBC,iBAAA,EAAsB;EAEtBC,IAAA,EAAa;EACbC,SAAA,EAAa;EACbC,SAAA,EAAa;EACbC,SAAA,EAAa;EAEbC,UAAA,EAAa;EACbC,UAAA,EAAa;EACbC,UAAA,EAAa;EAEbC,UAAA,EAAyB;EACzBC,cAAA,EAAyB;EACzBC,uBAAA,EAAyB;EACzBC,YAAA,EAA2B;EAC3BC,gBAAA,EAA2B;EAC3BC,yBAAA,EAA2B;EAC3BC,gBAAA,EAA+B;EAC/BC,oBAAA,EAA+B;EAC/BC,6BAAA,EAA+B;AACnC;AAGgB,SAAAC,QAAQC,EAAA,EAASC,IAAA,EACjC;EACI,IAAI,CAAChC,QAAA,EACL;IACU,MAAAiC,SAAA,GAAYC,MAAA,CAAOC,IAAA,CAAKlC,gBAAgB;IAE9CD,QAAA,GAAW;IAEX,SAASoC,CAAA,GAAI,GAAGA,CAAA,GAAIH,SAAA,CAAUI,MAAA,EAAQ,EAAED,CAAA,EACxC;MACU,MAAAE,EAAA,GAAKL,SAAA,CAAUG,CAAC;MAEtBpC,QAAA,CAAS+B,EAAA,CAAGO,EAAE,CAAC,IAAIrC,gBAAA,CAAiBqC,EAAE;IAC1C;EACJ;EAEA,OAAOtC,QAAA,CAASgC,IAAI;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}