{"ast": null, "code": "import { splitTextToCharacters } from \"./splitTextToCharacters.mjs\";\nfunction resolveCharacters(chars) {\n  typeof chars == \"string\" && (chars = [chars]);\n  const result = [];\n  for (let i = 0, j = chars.length; i < j; i++) {\n    const item = chars[i];\n    if (Array.isArray(item)) {\n      if (item.length !== 2) throw new Error(`[BitmapFont]: Invalid character range length, expecting 2 got ${item.length}.`);\n      const startCode = item[0].charCodeAt(0),\n        endCode = item[1].charCodeAt(0);\n      if (endCode < startCode) throw new Error(\"[BitmapFont]: Invalid character range.\");\n      for (let i2 = startCode, j2 = endCode; i2 <= j2; i2++) result.push(String.fromCharCode(i2));\n    } else result.push(...splitTextToCharacters(item));\n  }\n  if (result.length === 0) throw new Error(\"[BitmapFont]: Empty set when resolving characters.\");\n  return result;\n}\nexport { resolveCharacters };", "map": {"version": 3, "names": ["resolveCharacters", "chars", "result", "i", "j", "length", "item", "Array", "isArray", "Error", "startCode", "charCodeAt", "endCode", "i2", "j2", "push", "String", "fromCharCode", "splitTextToCharacters"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\text-bitmap\\src\\utils\\resolveCharacters.ts"], "sourcesContent": ["import { splitTextToCharacters } from './splitTextToCharacters';\n\n/**\n * Processes the passed character set data and returns a flattened array of all the characters.\n *\n * Ignored because not directly exposed.\n * @ignore\n * @param {string | string[] | string[][] } chars\n * @returns {string[]} the flattened array of characters\n */\nexport function resolveCharacters(chars: string | (string | string[])[]): string[]\n{\n    // Split the chars string into individual characters\n    if (typeof chars === 'string')\n    {\n        chars = [chars];\n    }\n\n    // Handle an array of characters+ranges\n    const result: string[] = [];\n\n    for (let i = 0, j = chars.length; i < j; i++)\n    {\n        const item = chars[i];\n\n        // Handle range delimited by start/end chars\n        if (Array.isArray(item))\n        {\n            if (item.length !== 2)\n            {\n                throw new Error(`[BitmapFont]: Invalid character range length, expecting 2 got ${item.length}.`);\n            }\n\n            const startCode = item[0].charCodeAt(0);\n            const endCode = item[1].charCodeAt(0);\n\n            if (endCode < startCode)\n            {\n                throw new Error('[BitmapFont]: Invalid character range.');\n            }\n\n            for (let i = startCode, j = endCode; i <= j; i++)\n            {\n                result.push(String.fromCharCode(i));\n            }\n        }\n        // Handle a character set string\n        else\n        {\n            result.push(...splitTextToCharacters(item));\n        }\n    }\n\n    if (result.length === 0)\n    {\n        throw new Error('[BitmapFont]: Empty set when resolving characters.');\n    }\n\n    return result;\n}\n"], "mappings": ";AAUO,SAASA,kBAAkBC,KAAA,EAClC;EAEQ,OAAOA,KAAA,IAAU,aAEjBA,KAAA,GAAQ,CAACA,KAAK;EAIlB,MAAMC,MAAA,GAAmB;EAEzB,SAASC,CAAA,GAAI,GAAGC,CAAA,GAAIH,KAAA,CAAMI,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IACzC;IACU,MAAAG,IAAA,GAAOL,KAAA,CAAME,CAAC;IAGhB,IAAAI,KAAA,CAAMC,OAAA,CAAQF,IAAI,GACtB;MACI,IAAIA,IAAA,CAAKD,MAAA,KAAW,GAEhB,MAAM,IAAII,KAAA,CAAM,iEAAiEH,IAAA,CAAKD,MAAM,GAAG;MAGnG,MAAMK,SAAA,GAAYJ,IAAA,CAAK,CAAC,EAAEK,UAAA,CAAW,CAAC;QAChCC,OAAA,GAAUN,IAAA,CAAK,CAAC,EAAEK,UAAA,CAAW,CAAC;MAEpC,IAAIC,OAAA,GAAUF,SAAA,EAEJ,UAAID,KAAA,CAAM,wCAAwC;MAG5D,SAASI,EAAA,GAAIH,SAAA,EAAWI,EAAA,GAAIF,OAAA,EAASC,EAAA,IAAKC,EAAA,EAAGD,EAAA,IAEzCX,MAAA,CAAOa,IAAA,CAAKC,MAAA,CAAOC,YAAA,CAAaJ,EAAC,CAAC;IAE1C,OAIIX,MAAA,CAAOa,IAAA,CAAK,GAAGG,qBAAA,CAAsBZ,IAAI,CAAC;EAElD;EAEA,IAAIJ,MAAA,CAAOG,MAAA,KAAW,GAEZ,UAAII,KAAA,CAAM,oDAAoD;EAGjE,OAAAP,MAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}