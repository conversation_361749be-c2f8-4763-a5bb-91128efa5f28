{"ast": null, "code": "const GAUSSIAN_VALUES = {\n    5: [0.153388, 0.221461, 0.250301],\n    7: [0.071303, 0.131514, 0.189879, 0.214607],\n    9: [0.028532, 0.067234, 0.124009, 0.179044, 0.20236],\n    11: [93e-4, 0.028002, 0.065984, 0.121703, 0.175713, 0.198596],\n    13: [2406e-6, 9255e-6, 0.027867, 0.065666, 0.121117, 0.174868, 0.197641],\n    15: [489e-6, 2403e-6, 9246e-6, 0.02784, 0.065602, 0.120999, 0.174697, 0.197448]\n  },\n  fragTemplate = [\"varying vec2 vBlurTexCoords[%size%];\", \"uniform sampler2D uSampler;\", \"void main(void)\", \"{\", \"    gl_FragColor = vec4(0.0);\", \"    %blur%\", \"}\"].join(`\n`);\nfunction generateBlurFragSource(kernelSize) {\n  const kernel = GAUSSIAN_VALUES[kernelSize],\n    halfLength = kernel.length;\n  let fragSource = fragTemplate,\n    blurLoop = \"\";\n  const template = \"gl_FragColor += texture2D(uSampler, vBlurTexCoords[%index%]) * %value%;\";\n  let value;\n  for (let i = 0; i < kernelSize; i++) {\n    let blur = template.replace(\"%index%\", i.toString());\n    value = i, i >= halfLength && (value = kernelSize - i - 1), blur = blur.replace(\"%value%\", kernel[value].toString()), blurLoop += blur, blurLoop += `\n`;\n  }\n  return fragSource = fragSource.replace(\"%blur%\", blurLoop), fragSource = fragSource.replace(\"%size%\", kernelSize.toString()), fragSource;\n}\nexport { generateBlurFragSource };", "map": {"version": 3, "names": ["GAUSSIAN_VALUES", "fragTemplate", "join", "generateBlurFragSource", "kernelSize", "kernel", "<PERSON><PERSON><PERSON><PERSON>", "length", "fragSource", "blurLoop", "template", "value", "i", "blur", "replace", "toString"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\filter-blur\\src\\generateBlurFragSource.ts"], "sourcesContent": ["interface IGAUSSIAN_VALUES\n{\n    [x: number]: number[];\n}\nconst GAUSSIAN_VALUES: IGAUSSIAN_VALUES = {\n    5: [0.153388, 0.221461, 0.250301],\n    7: [0.071303, 0.131514, 0.189879, 0.214607],\n    9: [0.028532, 0.067234, 0.124009, 0.179044, 0.20236],\n    11: [0.0093, 0.028002, 0.065984, 0.121703, 0.175713, 0.198596],\n    13: [0.002406, 0.009255, 0.027867, 0.065666, 0.121117, 0.174868, 0.197641],\n    15: [0.000489, 0.002403, 0.009246, 0.02784, 0.065602, 0.120999, 0.174697, 0.197448],\n};\n\nconst fragTemplate = [\n    'varying vec2 vBlurTexCoords[%size%];',\n    'uniform sampler2D uSampler;',\n\n    'void main(void)',\n    '{',\n    '    gl_FragColor = vec4(0.0);',\n    '    %blur%',\n    '}',\n\n].join('\\n');\n\nexport function generateBlurFragSource(kernelSize: number): string\n{\n    const kernel = GAUSSIAN_VALUES[kernelSize];\n    const halfLength = kernel.length;\n\n    let fragSource = fragTemplate;\n\n    let blurLoop = '';\n    const template = 'gl_FragColor += texture2D(uSampler, vBlurTexCoords[%index%]) * %value%;';\n    let value: number;\n\n    for (let i = 0; i < kernelSize; i++)\n    {\n        let blur = template.replace('%index%', i.toString());\n\n        value = i;\n\n        if (i >= halfLength)\n        {\n            value = kernelSize - i - 1;\n        }\n\n        blur = blur.replace('%value%', kernel[value].toString());\n\n        blurLoop += blur;\n        blurLoop += '\\n';\n    }\n\n    fragSource = fragSource.replace('%blur%', blurLoop);\n    fragSource = fragSource.replace('%size%', kernelSize.toString());\n\n    return fragSource;\n}\n"], "mappings": "AAIA,MAAMA,eAAA,GAAoC;IACtC,GAAG,CAAC,UAAU,UAAU,QAAQ;IAChC,GAAG,CAAC,UAAU,UAAU,UAAU,QAAQ;IAC1C,GAAG,CAAC,UAAU,UAAU,UAAU,UAAU,OAAO;IACnD,IAAI,CAAC,OAAQ,UAAU,UAAU,UAAU,UAAU,QAAQ;IAC7D,IAAI,CAAC,SAAU,SAAU,UAAU,UAAU,UAAU,UAAU,QAAQ;IACzE,IAAI,CAAC,QAAU,SAAU,SAAU,SAAS,UAAU,UAAU,UAAU,QAAQ;EACtF;EAEMC,YAAA,GAAe,CACjB,wCACA,+BAEA,mBACA,KACA,iCACA,cACA,IAEJ,CAAEC,IAAA,CAAK;AAAA,CAAI;AAEJ,SAASC,uBAAuBC,UAAA,EACvC;EACI,MAAMC,MAAA,GAASL,eAAA,CAAgBI,UAAU;IACnCE,UAAA,GAAaD,MAAA,CAAOE,MAAA;EAEtB,IAAAC,UAAA,GAAaP,YAAA;IAEbQ,QAAA,GAAW;EACf,MAAMC,QAAA,GAAW;EACb,IAAAC,KAAA;EAEJ,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIR,UAAA,EAAYQ,CAAA,IAChC;IACI,IAAIC,IAAA,GAAOH,QAAA,CAASI,OAAA,CAAQ,WAAWF,CAAA,CAAEG,QAAA,EAAU;IAEnDJ,KAAA,GAAQC,CAAA,EAEJA,CAAA,IAAKN,UAAA,KAELK,KAAA,GAAQP,UAAA,GAAaQ,CAAA,GAAI,IAG7BC,IAAA,GAAOA,IAAA,CAAKC,OAAA,CAAQ,WAAWT,MAAA,CAAOM,KAAK,EAAEI,QAAA,CAAU,IAEvDN,QAAA,IAAYI,IAAA,EACZJ,QAAA,IAAY;AAAA;EAChB;EAEA,OAAAD,UAAA,GAAaA,UAAA,CAAWM,OAAA,CAAQ,UAAUL,QAAQ,GAClDD,UAAA,GAAaA,UAAA,CAAWM,OAAA,CAAQ,UAAUV,UAAA,CAAWW,QAAA,CAAU,IAExDP,UAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}