{"ast": null, "code": "import { utils, extensions, ExtensionType } from \"@pixi/core\";\nimport { BackgroundLoader } from \"./BackgroundLoader.mjs\";\nimport { Cache } from \"./cache/Cache.mjs\";\nimport { Loader } from \"./loader/Loader.mjs\";\nimport \"./loader/parsers/index.mjs\";\nimport { Resolver } from \"./resolver/Resolver.mjs\";\nimport { convertToList } from \"./utils/convertToList.mjs\";\nimport { isSingleItem } from \"./utils/isSingleItem.mjs\";\nimport { loadTextures } from \"./loader/parsers/textures/loadTextures.mjs\";\nclass AssetsClass {\n  constructor() {\n    this._detections = [], this._initialized = !1, this.resolver = new Resolver(), this.loader = new Loader(), this.cache = Cache, this._backgroundLoader = new BackgroundLoader(this.loader), this._backgroundLoader.active = !0, this.reset();\n  }\n  /**\n   * Best practice is to call this function before any loading commences\n   * Initiating is the best time to add any customization to the way things are loaded.\n   *\n   * you do not need to call this for the Asset class to work, only if you want to set any initial properties\n   * @param options - options to initialize the Asset manager with\n   */\n  async init(options = {}) {\n    if (this._initialized) {\n      console.warn(\"[Assets]AssetManager already initialized, did you load before calling this Assets.init()?\");\n      return;\n    }\n    if (this._initialized = !0, options.defaultSearchParams && this.resolver.setDefaultSearchParams(options.defaultSearchParams), options.basePath && (this.resolver.basePath = options.basePath), options.bundleIdentifier && this.resolver.setBundleIdentifier(options.bundleIdentifier), options.manifest) {\n      let manifest = options.manifest;\n      typeof manifest == \"string\" && (manifest = await this.load(manifest)), this.resolver.addManifest(manifest);\n    }\n    const resolutionPref = options.texturePreference?.resolution ?? 1,\n      resolution = typeof resolutionPref == \"number\" ? [resolutionPref] : resolutionPref,\n      formats = await this._detectFormats({\n        preferredFormats: options.texturePreference?.format,\n        skipDetections: options.skipDetections,\n        detections: this._detections\n      });\n    this.resolver.prefer({\n      params: {\n        format: formats,\n        resolution\n      }\n    }), options.preferences && this.setPreferences(options.preferences);\n  }\n  add(aliases, srcs, data, format, loadParser) {\n    this.resolver.add(aliases, srcs, data, format, loadParser);\n  }\n  async load(urls, onProgress) {\n    this._initialized || (await this.init());\n    const singleAsset = isSingleItem(urls),\n      urlArray = convertToList(urls).map(url => {\n        if (typeof url != \"string\") {\n          const aliases = this.resolver.getAlias(url);\n          return aliases.some(alias => !this.resolver.hasKey(alias)) && this.add(url), Array.isArray(aliases) ? aliases[0] : aliases;\n        }\n        return this.resolver.hasKey(url) || this.add({\n          alias: url,\n          src: url\n        }), url;\n      }),\n      resolveResults = this.resolver.resolve(urlArray),\n      out = await this._mapLoadToResolve(resolveResults, onProgress);\n    return singleAsset ? out[urlArray[0]] : out;\n  }\n  /**\n   * This adds a bundle of assets in one go so that you can load them as a group.\n   * For example you could add a bundle for each screen in you pixi app\n   * @example\n   * import { Assets } from 'pixi.js';\n   *\n   * Assets.addBundle('animals', {\n   *     bunny: 'bunny.png',\n   *     chicken: 'chicken.png',\n   *     thumper: 'thumper.png',\n   * });\n   *\n   * const assets = await Assets.loadBundle('animals');\n   * @param bundleId - the id of the bundle to add\n   * @param assets - a record of the asset or assets that will be chosen from when loading via the specified key\n   */\n  addBundle(bundleId, assets) {\n    this.resolver.addBundle(bundleId, assets);\n  }\n  /**\n   * Bundles are a way to load multiple assets at once.\n   * If a manifest has been provided to the init function then you can load a bundle, or bundles.\n   * you can also add bundles via `addBundle`\n   * @example\n   * import { Assets } from 'pixi.js';\n   *\n   * // Manifest Example\n   * const manifest = {\n   *     bundles: [\n   *         {\n   *             name: 'load-screen',\n   *             assets: [\n   *                 {\n   *                     alias: 'background',\n   *                     src: 'sunset.png',\n   *                 },\n   *                 {\n   *                     alias: 'bar',\n   *                     src: 'load-bar.{png,webp}',\n   *                 },\n   *             ],\n   *         },\n   *         {\n   *             name: 'game-screen',\n   *             assets: [\n   *                 {\n   *                     alias: 'character',\n   *                     src: 'robot.png',\n   *                 },\n   *                 {\n   *                     alias: 'enemy',\n   *                     src: 'bad-guy.png',\n   *                 },\n   *             ],\n   *         },\n   *     ]\n   * };\n   *\n   * await Assets.init({ manifest });\n   *\n   * // Load a bundle...\n   * loadScreenAssets = await Assets.loadBundle('load-screen');\n   * // Load another bundle...\n   * gameScreenAssets = await Assets.loadBundle('game-screen');\n   * @param bundleIds - the bundle id or ids to load\n   * @param onProgress - Optional function that is called when progress on asset loading is made.\n   * The function is passed a single parameter, `progress`, which represents the percentage (0.0 - 1.0)\n   * of the assets loaded. Do not use this function to detect when assets are complete and available,\n   * instead use the Promise returned by this function.\n   * @returns all the bundles assets or a hash of assets for each bundle specified\n   */\n  async loadBundle(bundleIds, onProgress) {\n    this._initialized || (await this.init());\n    let singleAsset = !1;\n    typeof bundleIds == \"string\" && (singleAsset = !0, bundleIds = [bundleIds]);\n    const resolveResults = this.resolver.resolveBundle(bundleIds),\n      out = {},\n      keys = Object.keys(resolveResults);\n    let count = 0,\n      total = 0;\n    const _onProgress = () => {\n        onProgress?.(++count / total);\n      },\n      promises = keys.map(bundleId => {\n        const resolveResult = resolveResults[bundleId];\n        return total += Object.keys(resolveResult).length, this._mapLoadToResolve(resolveResult, _onProgress).then(resolveResult2 => {\n          out[bundleId] = resolveResult2;\n        });\n      });\n    return await Promise.all(promises), singleAsset ? out[bundleIds[0]] : out;\n  }\n  /**\n   * Initiate a background load of some assets. It will passively begin to load these assets in the background.\n   * So when you actually come to loading them you will get a promise that resolves to the loaded assets immediately\n   *\n   * An example of this might be that you would background load game assets after your inital load.\n   * then when you got to actually load your game screen assets when a player goes to the game - the loading\n   * would already have stared or may even be complete, saving you having to show an interim load bar.\n   * @example\n   * import { Assets } from 'pixi.js';\n   *\n   * Assets.backgroundLoad('bunny.png');\n   *\n   * // later on in your app...\n   * await Assets.loadBundle('bunny.png'); // Will resolve quicker as loading may have completed!\n   * @param urls - the url / urls you want to background load\n   */\n  async backgroundLoad(urls) {\n    this._initialized || (await this.init()), typeof urls == \"string\" && (urls = [urls]);\n    const resolveResults = this.resolver.resolve(urls);\n    this._backgroundLoader.add(Object.values(resolveResults));\n  }\n  /**\n   * Initiate a background of a bundle, works exactly like backgroundLoad but for bundles.\n   * this can only be used if the loader has been initiated with a manifest\n   * @example\n   * import { Assets } from 'pixi.js';\n   *\n   * await Assets.init({\n   *     manifest: {\n   *         bundles: [\n   *             {\n   *                 name: 'load-screen',\n   *                 assets: [...],\n   *             },\n   *             ...\n   *         ],\n   *     },\n   * });\n   *\n   * Assets.backgroundLoadBundle('load-screen');\n   *\n   * // Later on in your app...\n   * await Assets.loadBundle('load-screen'); // Will resolve quicker as loading may have completed!\n   * @param bundleIds - the bundleId / bundleIds you want to background load\n   */\n  async backgroundLoadBundle(bundleIds) {\n    this._initialized || (await this.init()), typeof bundleIds == \"string\" && (bundleIds = [bundleIds]);\n    const resolveResults = this.resolver.resolveBundle(bundleIds);\n    Object.values(resolveResults).forEach(resolveResult => {\n      this._backgroundLoader.add(Object.values(resolveResult));\n    });\n  }\n  /**\n   * Only intended for development purposes.\n   * This will wipe the resolver and caches.\n   * You will need to reinitialize the Asset\n   */\n  reset() {\n    this.resolver.reset(), this.loader.reset(), this.cache.reset(), this._initialized = !1;\n  }\n  get(keys) {\n    if (typeof keys == \"string\") return Cache.get(keys);\n    const assets = {};\n    for (let i = 0; i < keys.length; i++) assets[i] = Cache.get(keys[i]);\n    return assets;\n  }\n  /**\n   * helper function to map resolved assets back to loaded assets\n   * @param resolveResults - the resolve results from the resolver\n   * @param onProgress - the progress callback\n   */\n  async _mapLoadToResolve(resolveResults, onProgress) {\n    const resolveArray = Object.values(resolveResults),\n      resolveKeys = Object.keys(resolveResults);\n    this._backgroundLoader.active = !1;\n    const loadedAssets = await this.loader.load(resolveArray, onProgress);\n    this._backgroundLoader.active = !0;\n    const out = {};\n    return resolveArray.forEach((resolveResult, i) => {\n      const asset = loadedAssets[resolveResult.src],\n        keys = [resolveResult.src];\n      resolveResult.alias && keys.push(...resolveResult.alias), out[resolveKeys[i]] = asset, Cache.set(keys, asset);\n    }), out;\n  }\n  /**\n   * Unload an asset or assets. As the Assets class is responsible for creating the assets via the `load` function\n   * this will make sure to destroy any assets and release them from memory.\n   * Once unloaded, you will need to load the asset again.\n   *\n   * Use this to help manage assets if you find that you have a large app and you want to free up memory.\n   *\n   * - it's up to you as the developer to make sure that textures are not actively being used when you unload them,\n   * Pixi won't break but you will end up with missing assets. Not a good look for the user!\n   * @example\n   * import { Assets } from 'pixi.js';\n   *\n   * // Load a URL:\n   * const myImageTexture = await Assets.load('http://some.url.com/image.png'); // => returns a texture\n   *\n   * await Assets.unload('http://some.url.com/image.png')\n   *\n   * // myImageTexture will be destroyed now.\n   *\n   * // Unload multiple assets:\n   * const textures = await Assets.unload(['thumper', 'chicko']);\n   * @param urls - the urls to unload\n   */\n  async unload(urls) {\n    this._initialized || (await this.init());\n    const urlArray = convertToList(urls).map(url => typeof url != \"string\" ? url.src : url),\n      resolveResults = this.resolver.resolve(urlArray);\n    await this._unloadFromResolved(resolveResults);\n  }\n  /**\n   * Bundles are a way to manage multiple assets at once.\n   * this will unload all files in a bundle.\n   *\n   * once a bundle has been unloaded, you need to load it again to have access to the assets.\n   * @example\n   * import { Assets } from 'pixi.js';\n   *\n   * Assets.addBundle({\n   *     'thumper': 'http://some.url.com/thumper.png',\n   * })\n   *\n   * const assets = await Assets.loadBundle('thumper');\n   *\n   * // Now to unload...\n   *\n   * await Assets.unloadBundle('thumper');\n   *\n   * // All assets in the assets object will now have been destroyed and purged from the cache\n   * @param bundleIds - the bundle id or ids to unload\n   */\n  async unloadBundle(bundleIds) {\n    this._initialized || (await this.init()), bundleIds = convertToList(bundleIds);\n    const resolveResults = this.resolver.resolveBundle(bundleIds),\n      promises = Object.keys(resolveResults).map(bundleId => this._unloadFromResolved(resolveResults[bundleId]));\n    await Promise.all(promises);\n  }\n  async _unloadFromResolved(resolveResult) {\n    const resolveArray = Object.values(resolveResult);\n    resolveArray.forEach(resolveResult2 => {\n      Cache.remove(resolveResult2.src);\n    }), await this.loader.unload(resolveArray);\n  }\n  /**\n   * Detects the supported formats for the browser, and returns an array of supported formats, respecting\n   * the users preferred formats order.\n   * @param options - the options to use when detecting formats\n   * @param options.preferredFormats - the preferred formats to use\n   * @param options.skipDetections - if we should skip the detections altogether\n   * @param options.detections - the detections to use\n   * @returns - the detected formats\n   */\n  async _detectFormats(options) {\n    let formats = [];\n    options.preferredFormats && (formats = Array.isArray(options.preferredFormats) ? options.preferredFormats : [options.preferredFormats]);\n    for (const detection of options.detections) options.skipDetections || (await detection.test()) ? formats = await detection.add(formats) : options.skipDetections || (formats = await detection.remove(formats));\n    return formats = formats.filter((format, index) => formats.indexOf(format) === index), formats;\n  }\n  /** All the detection parsers currently added to the Assets class. */\n  get detections() {\n    return this._detections;\n  }\n  /**\n   * @deprecated since 7.2.0\n   * @see {@link Assets.setPreferences}\n   */\n  get preferWorkers() {\n    return loadTextures.config.preferWorkers;\n  }\n  set preferWorkers(value) {\n    utils.deprecation(\"7.2.0\", \"Assets.prefersWorkers is deprecated, use Assets.setPreferences({ preferWorkers: true }) instead.\"), this.setPreferences({\n      preferWorkers: value\n    });\n  }\n  /**\n   * General setter for preferences. This is a helper function to set preferences on all parsers.\n   * @param preferences - the preferences to set\n   */\n  setPreferences(preferences) {\n    this.loader.parsers.forEach(parser => {\n      parser.config && Object.keys(parser.config).filter(key => key in preferences).forEach(key => {\n        parser.config[key] = preferences[key];\n      });\n    });\n  }\n}\nconst Assets = new AssetsClass();\nextensions.handleByList(ExtensionType.LoadParser, Assets.loader.parsers).handleByList(ExtensionType.ResolveParser, Assets.resolver.parsers).handleByList(ExtensionType.CacheParser, Assets.cache.parsers).handleByList(ExtensionType.DetectionParser, Assets.detections);\nexport { Assets, AssetsClass };", "map": {"version": 3, "names": ["AssetsClass", "constructor", "_detections", "_initialized", "resolver", "Resolver", "loader", "Loader", "cache", "<PERSON><PERSON>", "_background<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "active", "reset", "init", "options", "console", "warn", "defaultSearchParams", "setDefaultSearchParams", "basePath", "bundleIdentifier", "setBundleIdentifier", "manifest", "load", "addManifest", "resolutionPref", "texturePreference", "resolution", "formats", "_detectFormats", "preferredFormats", "format", "skipDetections", "detections", "prefer", "params", "preferences", "setPreferences", "add", "aliases", "srcs", "data", "loadParser", "urls", "onProgress", "singleAsset", "isSingleItem", "urlArray", "convertToList", "map", "url", "<PERSON><PERSON><PERSON><PERSON>", "some", "alias", "<PERSON><PERSON><PERSON>", "Array", "isArray", "src", "resolveResults", "resolve", "out", "_mapLoadToResolve", "addBundle", "bundleId", "assets", "loadBundle", "bundleIds", "resolveBundle", "keys", "Object", "count", "total", "_onProgress", "promises", "resolveResult", "length", "then", "resolveResult2", "Promise", "all", "backgroundLoad", "values", "backgroundLoadBundle", "for<PERSON>ach", "get", "i", "resolveArray", "<PERSON><PERSON><PERSON><PERSON>", "loadedAssets", "asset", "push", "set", "unload", "_unloadFromResolved", "unloadBundle", "remove", "detection", "test", "filter", "index", "indexOf", "preferWorkers", "loadTextures", "config", "value", "utils", "deprecation", "parsers", "parser", "key", "Assets", "extensions", "handleByList", "ExtensionType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Resolve<PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DetectionParser"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\assets\\src\\Assets.ts"], "sourcesContent": ["import { extensions, ExtensionType, utils } from '@pixi/core';\nimport { BackgroundLoader } from './BackgroundLoader';\nimport { Cache } from './cache/Cache';\nimport { Loader } from './loader/Loader';\nimport { loadTextures } from './loader/parsers';\nimport { Resolver } from './resolver/Resolver';\nimport { convertToList } from './utils/convertToList';\nimport { isSingleItem } from './utils/isSingleItem';\n\nimport type { FormatDetectionParser } from './detections';\nimport type { LoadTextureConfig } from './loader/parsers';\nimport type { BundleIdentifierOptions } from './resolver/Resolver';\nimport type {\n    ArrayOr,\n    AssetsBundle,\n    AssetsManifest,\n    AssetSrc,\n    LoadParserName,\n    ResolvedAsset,\n    UnresolvedAsset\n} from './types';\n\nexport type ProgressCallback = (progress: number) => void;\n\n/**\n * Extensible preferences that can be used, for instance, when configuring loaders.\n * @since 7.2.0\n * @memberof PIXI\n */\nexport interface AssetsPreferences extends LoadTextureConfig, GlobalMixins.AssetsPreferences {}\n\n/**\n * Initialization options object for Asset Class.\n * @memberof PIXI\n */\nexport interface AssetInitOptions\n{\n    // basic...\n    /** a base path for any assets loaded */\n    basePath?: string;\n\n    /** a default URL parameter string to append to all assets loaded */\n    defaultSearchParams?: string | Record<string, any>;\n\n    /**\n     * a manifest to tell the asset loader upfront what all your assets are\n     * this can be the manifest object itself, or a URL to the manifest.\n     */\n    manifest?: string | AssetsManifest;\n    /**\n     * optional preferences for which textures preferences you have when resolving assets\n     * for example you might set the resolution to 0.5 if the user is on a rubbish old phone\n     * or you might set the resolution to 2 if the user is on a retina display\n     */\n    texturePreference?: {\n        /** the resolution order you prefer, can be an array (priority order - first is prefered) or a single resolutions  */\n        resolution?: number | number[];\n        /**\n         * the formats you prefer, by default this will be:\n         * ['avif', 'webp', 'png', 'jpg', 'jpeg', 'webm', 'mp4', 'm4v', 'ogv']\n         */\n        format?: ArrayOr<string>;\n    };\n\n    /**\n     * If true, don't attempt to detect whether browser has preferred formats available.\n     * May result in increased performance as it skips detection step.\n     */\n    skipDetections?: boolean;\n\n    /** advanced - override how bundlesIds are generated */\n    bundleIdentifier?: BundleIdentifierOptions;\n\n    /** Optional loader preferences */\n    preferences?: Partial<AssetsPreferences>;\n}\n\n/**\n * A one stop shop for all Pixi resource management!\n * Super modern and easy to use, with enough flexibility to customize and do what you need!\n * @memberof PIXI\n * @namespace Assets\n *\n * Only one Asset Class exists accessed via the Global Asset object.\n *\n * It has four main responsibilities:\n * 1. Allows users to map URLs to keys and resolve them according to the user's browser capabilities\n * 2. Loads the resources and transforms them into assets that developers understand.\n * 3. Caches the assets and provides a way to access them.\n * 4. Allow developers to unload assets and clear the cache.\n *\n * It also has a few advanced features:\n * 1. Allows developers to provide a manifest upfront of all assets and help manage them via 'bundles'.\n * 2. Allows users to background load assets. Shortening (or eliminating) load times and improving UX. With this feature,\n * in-game loading bars can be a thing of the past!\n *\n * ### Assets Loading\n *\n * Do not be afraid to load things multiple times - under the hood, it will NEVER load anything more than once.\n *\n * For example:\n *\n * ```js\n * import { Assets } from 'pixi.js';\n *\n * promise1 = Assets.load('bunny.png')\n * promise2 = Assets.load('bunny.png')\n *\n * // promise1 === promise2\n * ```\n *\n * Here both promises will be the same. Once resolved... Forever resolved! It makes for really easy resource management!\n *\n * Out of the box it supports the following files:\n * - textures (avif, webp, png, jpg, gif, svg)\n * - sprite sheets (json)\n * - bitmap fonts (xml, fnt, txt)\n * - web fonts (ttf, woff, woff2)\n * - json files (json)\n * - text files (txt)\n *\n * More types can be added fairly easily by creating additional loader parsers.\n *\n * ### Textures\n * - Textures are loaded as ImageBitmap on a worker thread where possible.\n * Leading to much less janky load + parse times.\n * - By default, we will prefer to load AVIF and WebP image files if you specify them.\n * But if the browser doesn't support AVIF or WebP we will fall back to png and jpg.\n * - Textures can also be accessed via Texture.from(...) and now use this asset manager under the hood!\n * - Don't worry if you set preferences for textures that don't exist (for example you prefer 2x resolutions images\n *  but only 1x is available for that texture, the Asset manager will pick that up as a fallback automatically)\n *\n * #### Sprite sheets\n * - It's hard to know what resolution a sprite sheet is without loading it first, to address this\n * there is a naming convention we have added that will let Pixi understand the image format and resolution\n * of the spritesheet via its file name:\n *\n * `my-spritesheet{resolution}.{imageFormat}.json`\n *\n * For example:\n *\n * `<EMAIL>` // 2x resolution, WebP sprite sheet\n * `<EMAIL>` // 0.5x resolution, png sprite sheet\n *\n * This is optional! You can just load a sprite sheet as normal.\n * This is only useful if you have a bunch of different res / formatted spritesheets.\n *\n * ### Fonts\n * Web fonts will be loaded with all weights.\n * It is possible to load only specific weights by doing the following:\n *\n * ```js\n * import { Assets } from 'pixi.js';\n *\n * // Load specific weights..\n * await Assets.load({\n *     data: {\n *         weights: ['normal'], // Only loads the weight\n *     },\n *     src: `outfit.woff2`,\n * });\n *\n * // Load everything...\n * await Assets.load(`outfit.woff2`);\n * ```\n *\n * ### Background Loading\n * Background loading will load stuff for you passively behind the scenes. To minimize jank,\n * it will only load one asset at a time. As soon as a developer calls `Assets.load(...)` the\n * background loader is paused and requested assets are loaded as a priority.\n * Don't worry if something is in there that's already loaded, it will just get skipped!\n *\n * You still need to call `Assets.load(...)` to get an asset that has been loaded in the background.\n * It's just that this promise will resolve instantly if the asset\n * has already been loaded.\n *\n * ### Manifest and Bundles\n * - Manifest is a JSON file that contains a list of all assets and their properties.\n * - Bundles are a way to group assets together.\n *\n * ```js\n * import { Assets } from 'pixi.js';\n *\n * // Manifest Example\n * const manifest = {\n *     bundles: [\n *         {\n *             name: 'load-screen',\n *             assets: [\n *                 {\n *                     alias: 'background',\n *                     src: 'sunset.png',\n *                 },\n *                 {\n *                     alias: 'bar',\n *                     src: 'load-bar.{png,webp}',\n *                 },\n *             ],\n *         },\n *         {\n *             name: 'game-screen',\n *             assets: [\n *                 {\n *                     alias: 'character',\n *                     src: 'robot.png',\n *                 },\n *                 {\n *                     alias: 'enemy',\n *                     src: 'bad-guy.png',\n *                 },\n *             ],\n *         },\n *     ]\n * };\n *\n * await Assets.init({ manifest });\n *\n * // Load a bundle...\n * loadScreenAssets = await Assets.loadBundle('load-screen');\n * // Load another bundle...\n * gameScreenAssets = await Assets.loadBundle('game-screen');\n * ```\n * @example\n * import { Assets } from 'pixi.js';\n *\n * const bunny = await Assets.load('bunny.png');\n */\nexport class AssetsClass\n{\n    /** the resolver to map various urls */\n    public resolver: Resolver;\n    /**\n     * The loader, loads stuff!\n     * @type {PIXI.AssetLoader}\n     */\n    public loader: Loader;\n    /**\n     * The global cache of all assets within PixiJS\n     * @type {PIXI.Cache}\n     */\n    public cache: typeof Cache;\n\n    /** takes care of loading assets in the background */\n    private readonly _backgroundLoader: BackgroundLoader;\n\n    private _detections: FormatDetectionParser[] = [];\n\n    private _initialized = false;\n\n    constructor()\n    {\n        this.resolver = new Resolver();\n        this.loader = new Loader();\n        this.cache = Cache;\n\n        this._backgroundLoader = new BackgroundLoader(this.loader);\n        this._backgroundLoader.active = true;\n\n        this.reset();\n    }\n\n    /**\n     * Best practice is to call this function before any loading commences\n     * Initiating is the best time to add any customization to the way things are loaded.\n     *\n     * you do not need to call this for the Asset class to work, only if you want to set any initial properties\n     * @param options - options to initialize the Asset manager with\n     */\n    public async init(options: AssetInitOptions = {}): Promise<void>\n    {\n        if (this._initialized)\n        {\n            if (process.env.DEBUG)\n            {\n                console.warn('[Assets]AssetManager already initialized, did you load before calling this Assets.init()?');\n            }\n\n            return;\n        }\n\n        this._initialized = true;\n\n        if (options.defaultSearchParams)\n        {\n            this.resolver.setDefaultSearchParams(options.defaultSearchParams);\n        }\n\n        if (options.basePath)\n        {\n            this.resolver.basePath = options.basePath;\n        }\n\n        if (options.bundleIdentifier)\n        {\n            this.resolver.setBundleIdentifier(options.bundleIdentifier);\n        }\n\n        if (options.manifest)\n        {\n            let manifest = options.manifest;\n\n            if (typeof manifest === 'string')\n            {\n                manifest = await this.load<AssetsManifest>(manifest);\n            }\n\n            this.resolver.addManifest(manifest);\n        }\n\n        const resolutionPref = options.texturePreference?.resolution ?? 1;\n        const resolution = (typeof resolutionPref === 'number') ? [resolutionPref] : resolutionPref;\n\n        const formats = await this._detectFormats({\n            preferredFormats: options.texturePreference?.format,\n            skipDetections: options.skipDetections,\n            detections: this._detections\n        });\n\n        this.resolver.prefer({\n            params: {\n                format: formats,\n                resolution,\n            },\n        });\n\n        if (options.preferences)\n        {\n            this.setPreferences(options.preferences);\n        }\n    }\n\n    /** @deprecated */\n    public add(a: ArrayOr<string>, s?: string | string[], d?: unknown, f?: string, lp?: LoadParserName): void;\n    /**\n     * Allows you to specify how to resolve any assets load requests.\n     * There are a few ways to add things here as shown below:\n     * @example\n     * import { Assets } from 'pixi.js';\n     *\n     * // Simple\n     * Assets.add({alias: 'bunnyBooBoo', src: 'bunny.png'});\n     * const bunny = await Assets.load('bunnyBooBoo');\n     *\n     * // Multiple keys:\n     * Assets.add({alias: ['burger', 'chicken'], src: 'bunny.png'});\n     *\n     * const bunny = await Assets.load('burger');\n     * const bunny2 = await Assets.load('chicken');\n     *\n     * // passing options to to the object\n     * Assets.add({\n     *     alias: 'bunnyBooBooSmooth',\n     *     src: 'bunny{png,webp}',\n     *     data: { scaleMode: SCALE_MODES.NEAREST }, // Base texture options\n     * });\n     *\n     * // Multiple assets\n     *\n     * // The following all do the same thing:\n     *\n     * Assets.add({alias: 'bunnyBooBoo', src: 'bunny{png,webp}'});\n     *\n     * Assets.add({\n     *     alias: 'bunnyBooBoo',\n     *     src: [\n     *         'bunny.png',\n     *         'bunny.webp',\n     *    ],\n     * });\n     *\n     * const bunny = await Assets.load('bunnyBooBoo'); // Will try to load WebP if available\n     * @param data - the data to add\n     * @param data.aliases - the key or keys that you will reference when loading this asset\n     * @param data.srcs - the asset or assets that will be chosen from when loading via the specified key\n     * @param data.data - asset-specific data that will be passed to the loaders\n     * - Useful if you want to initiate loaded objects with specific data\n     * @param data.format - the format of the asset\n     * @param data.loadParser - the name of the load parser to use\n     */\n    public add(data:(ArrayOr<UnresolvedAsset>)): void;\n    public add(\n        aliases: ArrayOr<string> | ArrayOr<UnresolvedAsset>,\n        srcs?: AssetSrc,\n        data?: unknown,\n        format?: string,\n        loadParser?: LoadParserName\n    ): void\n    {\n        this.resolver.add(aliases as ArrayOr<string>, srcs, data, format, loadParser);\n    }\n\n    /**\n     * Loads your assets! You pass in a key or URL and it will return a promise that\n     * resolves to the loaded asset. If multiple assets a requested, it will return a hash of assets.\n     *\n     * Don't worry about loading things multiple times, behind the scenes assets are only ever loaded\n     * once and the same promise reused behind the scenes so you can safely call this function multiple\n     * times with the same key and it will always return the same asset.\n     * @example\n     * import { Assets } from 'pixi.js';\n     *\n     * // Load a URL:\n     * const myImageTexture = await Assets.load('http://some.url.com/image.png'); // => returns a texture\n     *\n     * Assets.add('thumper', 'bunny.png');\n     * Assets.add('chicko', 'chicken.png');\n     *\n     * // Load multiple assets:\n     * const textures = await Assets.load(['thumper', 'chicko']); // => {thumper: Texture, chicko: Texture}\n     * @param urls - the urls to load\n     * @param onProgress - optional function that is called when progress on asset loading is made.\n     * The function is passed a single parameter, `progress`, which represents the percentage\n     * (0.0 - 1.0) of the assets loaded.\n     * @returns - the assets that were loaded, either a single asset or a hash of assets\n     */\n    public async load<T = any>(\n        urls: string | UnresolvedAsset,\n        onProgress?: ProgressCallback,\n    ): Promise<T>;\n    public async load<T = any>(\n        urls: string[] | UnresolvedAsset[],\n        onProgress?: ProgressCallback,\n    ): Promise<Record<string, T>>;\n    public async load<T = any>(\n        urls: ArrayOr<string> | ArrayOr<UnresolvedAsset>,\n        onProgress?: ProgressCallback\n    ): Promise<T | Record<string, T>>\n    {\n        if (!this._initialized)\n        {\n            await this.init();\n        }\n\n        const singleAsset = isSingleItem(urls);\n\n        const urlArray: string[] = convertToList<UnresolvedAsset | string>(urls)\n            .map((url) =>\n            {\n                if (typeof url !== 'string')\n                {\n                    const aliases = this.resolver.getAlias(url);\n\n                    if (aliases.some((alias) => !this.resolver.hasKey(alias)))\n                    {\n                        this.add(url);\n                    }\n\n                    return Array.isArray(aliases) ? aliases[0] : aliases;\n                }\n\n                // if it hasn't been added, add it now\n                if (!this.resolver.hasKey(url)) this.add({ alias: url, src: url });\n\n                return url;\n            }) as string[];\n\n        // check cache first...\n        const resolveResults = this.resolver.resolve(urlArray);\n\n        // remap to the keys used..\n        const out: Record<string, T> = await this._mapLoadToResolve<T>(resolveResults, onProgress);\n\n        return singleAsset ? out[urlArray[0] as string] : out;\n    }\n\n    /**\n     * This adds a bundle of assets in one go so that you can load them as a group.\n     * For example you could add a bundle for each screen in you pixi app\n     * @example\n     * import { Assets } from 'pixi.js';\n     *\n     * Assets.addBundle('animals', {\n     *     bunny: 'bunny.png',\n     *     chicken: 'chicken.png',\n     *     thumper: 'thumper.png',\n     * });\n     *\n     * const assets = await Assets.loadBundle('animals');\n     * @param bundleId - the id of the bundle to add\n     * @param assets - a record of the asset or assets that will be chosen from when loading via the specified key\n     */\n    public addBundle(bundleId: string, assets: AssetsBundle['assets']): void\n    {\n        this.resolver.addBundle(bundleId, assets);\n    }\n\n    /**\n     * Bundles are a way to load multiple assets at once.\n     * If a manifest has been provided to the init function then you can load a bundle, or bundles.\n     * you can also add bundles via `addBundle`\n     * @example\n     * import { Assets } from 'pixi.js';\n     *\n     * // Manifest Example\n     * const manifest = {\n     *     bundles: [\n     *         {\n     *             name: 'load-screen',\n     *             assets: [\n     *                 {\n     *                     alias: 'background',\n     *                     src: 'sunset.png',\n     *                 },\n     *                 {\n     *                     alias: 'bar',\n     *                     src: 'load-bar.{png,webp}',\n     *                 },\n     *             ],\n     *         },\n     *         {\n     *             name: 'game-screen',\n     *             assets: [\n     *                 {\n     *                     alias: 'character',\n     *                     src: 'robot.png',\n     *                 },\n     *                 {\n     *                     alias: 'enemy',\n     *                     src: 'bad-guy.png',\n     *                 },\n     *             ],\n     *         },\n     *     ]\n     * };\n     *\n     * await Assets.init({ manifest });\n     *\n     * // Load a bundle...\n     * loadScreenAssets = await Assets.loadBundle('load-screen');\n     * // Load another bundle...\n     * gameScreenAssets = await Assets.loadBundle('game-screen');\n     * @param bundleIds - the bundle id or ids to load\n     * @param onProgress - Optional function that is called when progress on asset loading is made.\n     * The function is passed a single parameter, `progress`, which represents the percentage (0.0 - 1.0)\n     * of the assets loaded. Do not use this function to detect when assets are complete and available,\n     * instead use the Promise returned by this function.\n     * @returns all the bundles assets or a hash of assets for each bundle specified\n     */\n    public async loadBundle(bundleIds: ArrayOr<string>, onProgress?: ProgressCallback): Promise<any>\n    {\n        if (!this._initialized)\n        {\n            await this.init();\n        }\n\n        let singleAsset = false;\n\n        if (typeof bundleIds === 'string')\n        {\n            singleAsset = true;\n            bundleIds = [bundleIds];\n        }\n\n        const resolveResults = this.resolver.resolveBundle(bundleIds);\n\n        const out: Record<string, Record<string, any>> = {};\n\n        const keys = Object.keys(resolveResults);\n        let count = 0;\n        let total = 0;\n        const _onProgress = () =>\n        {\n            onProgress?.(++count / total);\n        };\n        const promises = keys.map((bundleId) =>\n        {\n            const resolveResult = resolveResults[bundleId];\n\n            total += Object.keys(resolveResult).length;\n\n            return this._mapLoadToResolve(resolveResult, _onProgress)\n                .then((resolveResult) =>\n                {\n                    out[bundleId] = resolveResult;\n                });\n        });\n\n        await Promise.all(promises);\n\n        return singleAsset ? out[bundleIds[0]] : out;\n    }\n\n    /**\n     * Initiate a background load of some assets. It will passively begin to load these assets in the background.\n     * So when you actually come to loading them you will get a promise that resolves to the loaded assets immediately\n     *\n     * An example of this might be that you would background load game assets after your inital load.\n     * then when you got to actually load your game screen assets when a player goes to the game - the loading\n     * would already have stared or may even be complete, saving you having to show an interim load bar.\n     * @example\n     * import { Assets } from 'pixi.js';\n     *\n     * Assets.backgroundLoad('bunny.png');\n     *\n     * // later on in your app...\n     * await Assets.loadBundle('bunny.png'); // Will resolve quicker as loading may have completed!\n     * @param urls - the url / urls you want to background load\n     */\n    public async backgroundLoad(urls: ArrayOr<string>): Promise<void>\n    {\n        if (!this._initialized)\n        {\n            await this.init();\n        }\n\n        if (typeof urls === 'string')\n        {\n            urls = [urls];\n        }\n\n        const resolveResults = this.resolver.resolve(urls);\n\n        this._backgroundLoader.add(Object.values(resolveResults));\n    }\n\n    /**\n     * Initiate a background of a bundle, works exactly like backgroundLoad but for bundles.\n     * this can only be used if the loader has been initiated with a manifest\n     * @example\n     * import { Assets } from 'pixi.js';\n     *\n     * await Assets.init({\n     *     manifest: {\n     *         bundles: [\n     *             {\n     *                 name: 'load-screen',\n     *                 assets: [...],\n     *             },\n     *             ...\n     *         ],\n     *     },\n     * });\n     *\n     * Assets.backgroundLoadBundle('load-screen');\n     *\n     * // Later on in your app...\n     * await Assets.loadBundle('load-screen'); // Will resolve quicker as loading may have completed!\n     * @param bundleIds - the bundleId / bundleIds you want to background load\n     */\n    public async backgroundLoadBundle(bundleIds: ArrayOr<string>): Promise<void>\n    {\n        if (!this._initialized)\n        {\n            await this.init();\n        }\n\n        if (typeof bundleIds === 'string')\n        {\n            bundleIds = [bundleIds];\n        }\n\n        const resolveResults = this.resolver.resolveBundle(bundleIds);\n\n        Object.values(resolveResults).forEach((resolveResult) =>\n        {\n            this._backgroundLoader.add(Object.values(resolveResult));\n        });\n    }\n\n    /**\n     * Only intended for development purposes.\n     * This will wipe the resolver and caches.\n     * You will need to reinitialize the Asset\n     */\n    public reset(): void\n    {\n        this.resolver.reset();\n        this.loader.reset();\n        this.cache.reset();\n\n        this._initialized = false;\n    }\n\n    /**\n     * Instantly gets an asset already loaded from the cache. If the asset has not yet been loaded,\n     * it will return undefined. So it's on you! When in doubt just use `Assets.load` instead.\n     * (Remember, the loader will never load things more than once!)\n     * @param keys - The key or keys for the assets that you want to access\n     * @returns - The assets or hash of assets requested\n     */\n    public get<T = any>(keys: string): T;\n    public get<T = any>(keys: string[]): Record<string, T>;\n    public get<T = any>(keys: ArrayOr<string>): T | Record<string, T>\n    {\n        if (typeof keys === 'string')\n        {\n            return Cache.get(keys);\n        }\n\n        const assets: Record<string, T> = {};\n\n        for (let i = 0; i < keys.length; i++)\n        {\n            assets[i] = Cache.get(keys[i]);\n        }\n\n        return assets;\n    }\n\n    /**\n     * helper function to map resolved assets back to loaded assets\n     * @param resolveResults - the resolve results from the resolver\n     * @param onProgress - the progress callback\n     */\n    private async _mapLoadToResolve<T>(\n        resolveResults: ResolvedAsset | Record<string, ResolvedAsset>,\n        onProgress?: ProgressCallback\n    ): Promise<Record<string, T>>\n    {\n        const resolveArray = Object.values(resolveResults) as ResolvedAsset[];\n        const resolveKeys = Object.keys(resolveResults);\n\n        // pause background loader...\n        this._backgroundLoader.active = false;\n\n        const loadedAssets = await this.loader.load<T>(resolveArray, onProgress);\n\n        // resume background loader...\n        this._backgroundLoader.active = true;\n\n        // remap to the keys used..\n\n        const out: Record<string, T> = {};\n\n        resolveArray.forEach((resolveResult, i) =>\n        {\n            const asset = loadedAssets[resolveResult.src];\n\n            const keys = [resolveResult.src];\n\n            if (resolveResult.alias)\n            {\n                keys.push(...resolveResult.alias);\n            }\n\n            out[resolveKeys[i]] = asset;\n\n            Cache.set(keys, asset);\n        });\n\n        return out;\n    }\n\n    /**\n     * Unload an asset or assets. As the Assets class is responsible for creating the assets via the `load` function\n     * this will make sure to destroy any assets and release them from memory.\n     * Once unloaded, you will need to load the asset again.\n     *\n     * Use this to help manage assets if you find that you have a large app and you want to free up memory.\n     *\n     * - it's up to you as the developer to make sure that textures are not actively being used when you unload them,\n     * Pixi won't break but you will end up with missing assets. Not a good look for the user!\n     * @example\n     * import { Assets } from 'pixi.js';\n     *\n     * // Load a URL:\n     * const myImageTexture = await Assets.load('http://some.url.com/image.png'); // => returns a texture\n     *\n     * await Assets.unload('http://some.url.com/image.png')\n     *\n     * // myImageTexture will be destroyed now.\n     *\n     * // Unload multiple assets:\n     * const textures = await Assets.unload(['thumper', 'chicko']);\n     * @param urls - the urls to unload\n     */\n    public async unload(\n        urls: ArrayOr<string> | ResolvedAsset | ResolvedAsset[]\n    ): Promise<void>\n    {\n        if (!this._initialized)\n        {\n            await this.init();\n        }\n\n        const urlArray = convertToList<string | ResolvedAsset>(urls)\n            .map((url) =>\n                ((typeof url !== 'string') ? url.src : url));\n\n        // check cache first...\n        const resolveResults = this.resolver.resolve(urlArray);\n\n        await this._unloadFromResolved(resolveResults);\n    }\n\n    /**\n     * Bundles are a way to manage multiple assets at once.\n     * this will unload all files in a bundle.\n     *\n     * once a bundle has been unloaded, you need to load it again to have access to the assets.\n     * @example\n     * import { Assets } from 'pixi.js';\n     *\n     * Assets.addBundle({\n     *     'thumper': 'http://some.url.com/thumper.png',\n     * })\n     *\n     * const assets = await Assets.loadBundle('thumper');\n     *\n     * // Now to unload...\n     *\n     * await Assets.unloadBundle('thumper');\n     *\n     * // All assets in the assets object will now have been destroyed and purged from the cache\n     * @param bundleIds - the bundle id or ids to unload\n     */\n    public async unloadBundle(bundleIds: ArrayOr<string>): Promise<void>\n    {\n        if (!this._initialized)\n        {\n            await this.init();\n        }\n\n        bundleIds = convertToList<string>(bundleIds);\n\n        const resolveResults = this.resolver.resolveBundle(bundleIds);\n\n        const promises = Object.keys(resolveResults).map((bundleId) =>\n            this._unloadFromResolved(resolveResults[bundleId]));\n\n        await Promise.all(promises);\n    }\n\n    private async _unloadFromResolved(resolveResult: ResolvedAsset | Record<string, ResolvedAsset>)\n    {\n        const resolveArray = Object.values(resolveResult);\n\n        resolveArray.forEach((resolveResult) =>\n        {\n            Cache.remove(resolveResult.src);\n        });\n\n        await this.loader.unload(resolveArray);\n    }\n\n    /**\n     * Detects the supported formats for the browser, and returns an array of supported formats, respecting\n     * the users preferred formats order.\n     * @param options - the options to use when detecting formats\n     * @param options.preferredFormats - the preferred formats to use\n     * @param options.skipDetections - if we should skip the detections altogether\n     * @param options.detections - the detections to use\n     * @returns - the detected formats\n     */\n    private async _detectFormats(options: {\n        preferredFormats: string | string[],\n        skipDetections: boolean,\n        detections: FormatDetectionParser[]\n    }): Promise<string[]>\n    {\n        let formats: string[] = [];\n\n        // set preferred formats\n        if (options.preferredFormats)\n        {\n            formats = Array.isArray(options.preferredFormats)\n                ? options.preferredFormats : [options.preferredFormats];\n        }\n\n        // we should add any formats that are supported by the browser\n        for (const detection of options.detections)\n        {\n            if (options.skipDetections || await detection.test())\n            {\n                formats = await detection.add(formats);\n            }\n            else if (!options.skipDetections)\n            {\n                formats = await detection.remove(formats);\n            }\n        }\n\n        // remove any duplicates\n        formats = formats.filter((format, index) => formats.indexOf(format) === index);\n\n        return formats;\n    }\n\n    /** All the detection parsers currently added to the Assets class. */\n    public get detections(): FormatDetectionParser[]\n    {\n        return this._detections;\n    }\n\n    /**\n     * @deprecated since 7.2.0\n     * @see {@link Assets.setPreferences}\n     */\n    public get preferWorkers(): boolean\n    {\n        return loadTextures.config.preferWorkers;\n    }\n    public set preferWorkers(value: boolean)\n    {\n        if (process.env.DEBUG)\n        {\n            utils.deprecation('7.2.0', 'Assets.prefersWorkers is deprecated, '\n            + 'use Assets.setPreferences({ preferWorkers: true }) instead.');\n        }\n        this.setPreferences({ preferWorkers: value });\n    }\n\n    /**\n     * General setter for preferences. This is a helper function to set preferences on all parsers.\n     * @param preferences - the preferences to set\n     */\n    public setPreferences(preferences: Partial<AssetsPreferences>): void\n    {\n        // Find matching config keys in loaders with preferences\n        // and set the values\n        this.loader.parsers.forEach((parser) =>\n        {\n            if (!parser.config) return;\n\n            (Object.keys(parser.config) as (keyof AssetsPreferences)[])\n                .filter((key) => key in preferences)\n                .forEach((key) =>\n                {\n                    parser.config[key] = preferences[key];\n                });\n        });\n    }\n}\n\nexport const Assets = new AssetsClass();\n\n// Handle registration of extensions\nextensions\n    .handleByList(ExtensionType.LoadParser, Assets.loader.parsers)\n    .handleByList(ExtensionType.ResolveParser, Assets.resolver.parsers)\n    .handleByList(ExtensionType.CacheParser, Assets.cache.parsers)\n    .handleByList(ExtensionType.DetectionParser, Assets.detections);\n"], "mappings": ";;;;;;;;;AAmOO,MAAMA,WAAA,CACb;EAqBIC,YAAA,EACA;IALA,KAAQC,WAAA,GAAuC,IAE/C,KAAQC,YAAA,GAAe,IAId,KAAAC,QAAA,GAAW,IAAIC,QAAA,IACpB,KAAKC,MAAA,GAAS,IAAIC,MAAA,CAClB,QAAKC,KAAA,GAAQC,KAAA,EAEb,KAAKC,iBAAA,GAAoB,IAAIC,gBAAA,CAAiB,KAAKL,MAAM,GACzD,KAAKI,iBAAA,CAAkBE,MAAA,GAAS,IAEhC,KAAKC,KAAA,CAAM;EACf;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASA,MAAaC,KAAKC,OAAA,GAA4B,IAC9C;IACI,IAAI,KAAKZ,YAAA,EACT;MAGQa,OAAA,CAAQC,IAAA,CAAK,2FAA2F;MAG5G;IACJ;IAEA,SAAKd,YAAA,GAAe,IAEhBY,OAAA,CAAQG,mBAAA,IAER,KAAKd,QAAA,CAASe,sBAAA,CAAuBJ,OAAA,CAAQG,mBAAmB,GAGhEH,OAAA,CAAQK,QAAA,KAER,KAAKhB,QAAA,CAASgB,QAAA,GAAWL,OAAA,CAAQK,QAAA,GAGjCL,OAAA,CAAQM,gBAAA,IAER,KAAKjB,QAAA,CAASkB,mBAAA,CAAoBP,OAAA,CAAQM,gBAAgB,GAG1DN,OAAA,CAAQQ,QAAA,EACZ;MACI,IAAIA,QAAA,GAAWR,OAAA,CAAQQ,QAAA;MAEnB,OAAOA,QAAA,IAAa,aAEpBA,QAAA,GAAW,MAAM,KAAKC,IAAA,CAAqBD,QAAQ,IAGvD,KAAKnB,QAAA,CAASqB,WAAA,CAAYF,QAAQ;IACtC;IAEA,MAAMG,cAAA,GAAiBX,OAAA,CAAQY,iBAAA,EAAmBC,UAAA,IAAc;MAC1DA,UAAA,GAAc,OAAOF,cAAA,IAAmB,WAAY,CAACA,cAAc,IAAIA,cAAA;MAEvEG,OAAA,GAAU,MAAM,KAAKC,cAAA,CAAe;QACtCC,gBAAA,EAAkBhB,OAAA,CAAQY,iBAAA,EAAmBK,MAAA;QAC7CC,cAAA,EAAgBlB,OAAA,CAAQkB,cAAA;QACxBC,UAAA,EAAY,KAAKhC;MAAA,CACpB;IAED,KAAKE,QAAA,CAAS+B,MAAA,CAAO;MACjBC,MAAA,EAAQ;QACJJ,MAAA,EAAQH,OAAA;QACRD;MACJ;IAAA,CACH,GAEGb,OAAA,CAAQsB,WAAA,IAER,KAAKC,cAAA,CAAevB,OAAA,CAAQsB,WAAW;EAE/C;EAmDOE,IACHC,OAAA,EACAC,IAAA,EACAC,IAAA,EACAV,MAAA,EACAW,UAAA,EAEJ;IACI,KAAKvC,QAAA,CAASmC,GAAA,CAAIC,OAAA,EAA4BC,IAAA,EAAMC,IAAA,EAAMV,MAAA,EAAQW,UAAU;EAChF;EAkCA,MAAanB,KACToB,IAAA,EACAC,UAAA,EAEJ;IACS,KAAK1C,YAAA,KAEN,MAAM,KAAKW,IAAA,CAAK;IAGd,MAAAgC,WAAA,GAAcC,YAAA,CAAaH,IAAI;MAE/BI,QAAA,GAAqBC,aAAA,CAAwCL,IAAI,EAClEM,GAAA,CAAKC,GAAA,IACN;QACQ,WAAOA,GAAA,IAAQ,UACnB;UACI,MAAMX,OAAA,GAAU,KAAKpC,QAAA,CAASgD,QAAA,CAASD,GAAG;UAEtC,OAAAX,OAAA,CAAQa,IAAA,CAAMC,KAAA,IAAU,CAAC,KAAKlD,QAAA,CAASmD,MAAA,CAAOD,KAAK,CAAC,KAEpD,KAAKf,GAAA,CAAIY,GAAG,GAGTK,KAAA,CAAMC,OAAA,CAAQjB,OAAO,IAAIA,OAAA,CAAQ,CAAC,IAAIA,OAAA;QACjD;QAGA,OAAK,KAAKpC,QAAA,CAASmD,MAAA,CAAOJ,GAAG,KAAG,KAAKZ,GAAA,CAAI;UAAEe,KAAA,EAAOH,GAAA;UAAKO,GAAA,EAAKP;QAAK,IAE1DA,GAAA;MACV;MAGCQ,cAAA,GAAiB,KAAKvD,QAAA,CAASwD,OAAA,CAAQZ,QAAQ;MAG/Ca,GAAA,GAAyB,MAAM,KAAKC,iBAAA,CAAqBH,cAAA,EAAgBd,UAAU;IAEzF,OAAOC,WAAA,GAAce,GAAA,CAAIb,QAAA,CAAS,CAAC,CAAW,IAAIa,GAAA;EACtD;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAkBOE,UAAUC,QAAA,EAAkBC,MAAA,EACnC;IACS,KAAA7D,QAAA,CAAS2D,SAAA,CAAUC,QAAA,EAAUC,MAAM;EAC5C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAsDA,MAAaC,WAAWC,SAAA,EAA4BtB,UAAA,EACpD;IACS,KAAK1C,YAAA,KAEN,MAAM,KAAKW,IAAA,CAAK;IAGpB,IAAIgC,WAAA,GAAc;IAEd,OAAOqB,SAAA,IAAc,aAErBrB,WAAA,GAAc,IACdqB,SAAA,GAAY,CAACA,SAAS;IAG1B,MAAMR,cAAA,GAAiB,KAAKvD,QAAA,CAASgE,aAAA,CAAcD,SAAS;MAEtDN,GAAA,GAA2C;MAE3CQ,IAAA,GAAOC,MAAA,CAAOD,IAAA,CAAKV,cAAc;IACnC,IAAAY,KAAA,GAAQ;MACRC,KAAA,GAAQ;IACZ,MAAMC,WAAA,GAAcA,CAAA,KACpB;QACiB5B,UAAA,KAAE0B,KAAA,GAAQC,KAAK;MAE1B;MAAAE,QAAA,GAAWL,IAAA,CAAKnB,GAAA,CAAKc,QAAA,IAC3B;QACU,MAAAW,aAAA,GAAgBhB,cAAA,CAAeK,QAAQ;QAE7C,OAAAQ,KAAA,IAASF,MAAA,CAAOD,IAAA,CAAKM,aAAa,EAAEC,MAAA,EAE7B,KAAKd,iBAAA,CAAkBa,aAAA,EAAeF,WAAW,EACnDI,IAAA,CAAMC,cAAA,IACP;UACIjB,GAAA,CAAIG,QAAQ,IAAIc,cAAA;QAAA,CACnB;MAAA,CACR;IAEK,aAAAC,OAAA,CAAQC,GAAA,CAAIN,QAAQ,GAEnB5B,WAAA,GAAce,GAAA,CAAIM,SAAA,CAAU,CAAC,CAAC,IAAIN,GAAA;EAC7C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAkBA,MAAaoB,eAAerC,IAAA,EAC5B;IACS,KAAKzC,YAAA,KAEN,MAAM,KAAKW,IAAA,CAGX,WAAO8B,IAAA,IAAS,aAEhBA,IAAA,GAAO,CAACA,IAAI;IAGhB,MAAMe,cAAA,GAAiB,KAAKvD,QAAA,CAASwD,OAAA,CAAQhB,IAAI;IAEjD,KAAKlC,iBAAA,CAAkB6B,GAAA,CAAI+B,MAAA,CAAOY,MAAA,CAAOvB,cAAc,CAAC;EAC5D;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EA0BA,MAAawB,qBAAqBhB,SAAA,EAClC;IACS,KAAKhE,YAAA,KAEN,MAAM,KAAKW,IAAA,CAGX,WAAOqD,SAAA,IAAc,aAErBA,SAAA,GAAY,CAACA,SAAS;IAG1B,MAAMR,cAAA,GAAiB,KAAKvD,QAAA,CAASgE,aAAA,CAAcD,SAAS;IAE5DG,MAAA,CAAOY,MAAA,CAAOvB,cAAc,EAAEyB,OAAA,CAAST,aAAA,IACvC;MACI,KAAKjE,iBAAA,CAAkB6B,GAAA,CAAI+B,MAAA,CAAOY,MAAA,CAAOP,aAAa,CAAC;IAAA,CAC1D;EACL;EAAA;AAAA;AAAA;AAAA;AAAA;EAOO9D,MAAA,EACP;IACI,KAAKT,QAAA,CAASS,KAAA,CACd,QAAKP,MAAA,CAAOO,KAAA,IACZ,KAAKL,KAAA,CAAMK,KAAA,CAEX,QAAKV,YAAA,GAAe;EACxB;EAWOkF,IAAahB,IAAA,EACpB;IACI,IAAI,OAAOA,IAAA,IAAS,UAET,OAAA5D,KAAA,CAAM4E,GAAA,CAAIhB,IAAI;IAGzB,MAAMJ,MAAA,GAA4B;IAElC,SAASqB,CAAA,GAAI,GAAGA,CAAA,GAAIjB,IAAA,CAAKO,MAAA,EAAQU,CAAA,IAE7BrB,MAAA,CAAOqB,CAAC,IAAI7E,KAAA,CAAM4E,GAAA,CAAIhB,IAAA,CAAKiB,CAAC,CAAC;IAG1B,OAAArB,MAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,MAAcH,kBACVH,cAAA,EACAd,UAAA,EAEJ;IACU,MAAA0C,YAAA,GAAejB,MAAA,CAAOY,MAAA,CAAOvB,cAAc;MAC3C6B,WAAA,GAAclB,MAAA,CAAOD,IAAA,CAAKV,cAAc;IAG9C,KAAKjD,iBAAA,CAAkBE,MAAA,GAAS;IAEhC,MAAM6E,YAAA,GAAe,MAAM,KAAKnF,MAAA,CAAOkB,IAAA,CAAQ+D,YAAA,EAAc1C,UAAU;IAGvE,KAAKnC,iBAAA,CAAkBE,MAAA,GAAS;IAIhC,MAAMiD,GAAA,GAAyB;IAElB,OAAA0B,YAAA,CAAAH,OAAA,CAAQ,CAACT,aAAA,EAAeW,CAAA,KACrC;MACU,MAAAI,KAAA,GAAQD,YAAA,CAAad,aAAA,CAAcjB,GAAG;QAEtCW,IAAA,GAAO,CAACM,aAAA,CAAcjB,GAAG;MAE3BiB,aAAA,CAAcrB,KAAA,IAEde,IAAA,CAAKsB,IAAA,CAAK,GAAGhB,aAAA,CAAcrB,KAAK,GAGpCO,GAAA,CAAI2B,WAAA,CAAYF,CAAC,CAAC,IAAII,KAAA,EAEtBjF,KAAA,CAAMmF,GAAA,CAAIvB,IAAA,EAAMqB,KAAK;IACxB,IAEM7B,GAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAyBA,MAAagC,OACTjD,IAAA,EAEJ;IACS,KAAKzC,YAAA,KAEN,MAAM,KAAKW,IAAA,CAAK;IAGpB,MAAMkC,QAAA,GAAWC,aAAA,CAAsCL,IAAI,EACtDM,GAAA,CAAKC,GAAA,IACA,OAAOA,GAAA,IAAQ,WAAYA,GAAA,CAAIO,GAAA,GAAMP,GAAI;MAG7CQ,cAAA,GAAiB,KAAKvD,QAAA,CAASwD,OAAA,CAAQZ,QAAQ;IAE/C,WAAK8C,mBAAA,CAAoBnC,cAAc;EACjD;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAuBA,MAAaoC,aAAa5B,SAAA,EAC1B;IACS,KAAKhE,YAAA,KAEN,MAAM,KAAKW,IAAA,CAGf,IAAAqD,SAAA,GAAYlB,aAAA,CAAsBkB,SAAS;IAE3C,MAAMR,cAAA,GAAiB,KAAKvD,QAAA,CAASgE,aAAA,CAAcD,SAAS;MAEtDO,QAAA,GAAWJ,MAAA,CAAOD,IAAA,CAAKV,cAAc,EAAET,GAAA,CAAKc,QAAA,IAC9C,KAAK8B,mBAAA,CAAoBnC,cAAA,CAAeK,QAAQ,CAAC,CAAC;IAEhD,MAAAe,OAAA,CAAQC,GAAA,CAAIN,QAAQ;EAC9B;EAEA,MAAcoB,oBAAoBnB,aAAA,EAClC;IACU,MAAAY,YAAA,GAAejB,MAAA,CAAOY,MAAA,CAAOP,aAAa;IAEnCY,YAAA,CAAAH,OAAA,CAASN,cAAA,IACtB;MACUrE,KAAA,CAAAuF,MAAA,CAAOlB,cAAA,CAAcpB,GAAG;IACjC,IAED,MAAM,KAAKpD,MAAA,CAAOuF,MAAA,CAAON,YAAY;EACzC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWA,MAAczD,eAAef,OAAA,EAK7B;IACI,IAAIc,OAAA,GAAoB;IAGpBd,OAAA,CAAQgB,gBAAA,KAERF,OAAA,GAAU2B,KAAA,CAAMC,OAAA,CAAQ1C,OAAA,CAAQgB,gBAAgB,IAC1ChB,OAAA,CAAQgB,gBAAA,GAAmB,CAAChB,OAAA,CAAQgB,gBAAgB;IAI9D,WAAWkE,SAAA,IAAalF,OAAA,CAAQmB,UAAA,EAExBnB,OAAA,CAAQkB,cAAA,KAAkB,MAAMgE,SAAA,CAAUC,IAAA,CAAK,KAE/CrE,OAAA,GAAU,MAAMoE,SAAA,CAAU1D,GAAA,CAAIV,OAAO,IAE/Bd,OAAA,CAAQkB,cAAA,KAEdJ,OAAA,GAAU,MAAMoE,SAAA,CAAUD,MAAA,CAAOnE,OAAO;IAKtC,OAAAA,OAAA,GAAAA,OAAA,CAAQsE,MAAA,CAAO,CAACnE,MAAA,EAAQoE,KAAA,KAAUvE,OAAA,CAAQwE,OAAA,CAAQrE,MAAM,MAAMoE,KAAK,GAEtEvE,OAAA;EACX;EAAA;EAGA,IAAWK,WAAA,EACX;IACI,OAAO,KAAKhC,WAAA;EAChB;EAAA;AAAA;AAAA;AAAA;EAMA,IAAWoG,cAAA,EACX;IACI,OAAOC,YAAA,CAAaC,MAAA,CAAOF,aAAA;EAC/B;EACA,IAAWA,cAAcG,KAAA,EACzB;IAGcC,KAAA,CAAAC,WAAA,CAAY,SAAS,kGACoC,GAEnE,KAAKrE,cAAA,CAAe;MAAEgE,aAAA,EAAeG;IAAA,CAAO;EAChD;EAAA;AAAA;AAAA;AAAA;EAMOnE,eAAeD,WAAA,EACtB;IAGI,KAAK/B,MAAA,CAAOsG,OAAA,CAAQxB,OAAA,CAASyB,MAAA,IAC7B;MACSA,MAAA,CAAOL,MAAA,IAEXlC,MAAA,CAAOD,IAAA,CAAKwC,MAAA,CAAOL,MAAM,EACrBL,MAAA,CAAQW,GAAA,IAAQA,GAAA,IAAOzE,WAAW,EAClC+C,OAAA,CAAS0B,GAAA,IACV;QACID,MAAA,CAAOL,MAAA,CAAOM,GAAG,IAAIzE,WAAA,CAAYyE,GAAG;MAAA,CACvC;IAAA,CACR;EACL;AACJ;AAEa,MAAAC,MAAA,GAAS,IAAI/G,WAAA,CAAY;AAGtCgH,UAAA,CACKC,YAAA,CAAaC,aAAA,CAAcC,UAAA,EAAYJ,MAAA,CAAOzG,MAAA,CAAOsG,OAAO,EAC5DK,YAAA,CAAaC,aAAA,CAAcE,aAAA,EAAeL,MAAA,CAAO3G,QAAA,CAASwG,OAAO,EACjEK,YAAA,CAAaC,aAAA,CAAcG,WAAA,EAAaN,MAAA,CAAOvG,KAAA,CAAMoG,OAAO,EAC5DK,YAAA,CAAaC,aAAA,CAAcI,eAAA,EAAiBP,MAAA,CAAO7E,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}