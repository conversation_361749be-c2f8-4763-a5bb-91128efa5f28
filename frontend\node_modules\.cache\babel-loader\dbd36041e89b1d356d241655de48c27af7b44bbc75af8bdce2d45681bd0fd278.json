{"ast": null, "code": "import { GLProgram } from \"../GLProgram.mjs\";\nimport { compileShader } from \"./compileShader.mjs\";\nimport { defaultValue } from \"./defaultValue.mjs\";\nimport { getAttributeData } from \"./getAttributeData.mjs\";\nimport { getUniformData } from \"./getUniformData.mjs\";\nimport { logProgramError } from \"./logProgramError.mjs\";\nfunction generateProgram(gl, program) {\n  const glVertShader = compileShader(gl, gl.VERTEX_SHADER, program.vertexSrc),\n    glFragShader = compileShader(gl, gl.FRAGMENT_SHADER, program.fragmentSrc),\n    webGLProgram = gl.createProgram();\n  gl.attachShader(webGLProgram, glVertShader), gl.attachShader(webGLProgram, glFragShader);\n  const transformFeedbackVaryings = program.extra?.transformFeedbackVaryings;\n  if (transformFeedbackVaryings && (typeof gl.transformFeedbackVaryings != \"function\" ? console.warn(\"TransformFeedback is not supported but TransformFeedbackVaryings are given.\") : gl.transformFeedbackVaryings(webGLProgram, transformFeedbackVaryings.names, transformFeedbackVaryings.bufferMode === \"separate\" ? gl.SEPARATE_ATTRIBS : gl.INTERLEAVED_ATTRIBS)), gl.linkProgram(webGLProgram), gl.getProgramParameter(webGLProgram, gl.LINK_STATUS) || logProgramError(gl, webGLProgram, glVertShader, glFragShader), program.attributeData = getAttributeData(webGLProgram, gl), program.uniformData = getUniformData(webGLProgram, gl), !/^[ \\t]*#[ \\t]*version[ \\t]+300[ \\t]+es[ \\t]*$/m.test(program.vertexSrc)) {\n    const keys = Object.keys(program.attributeData);\n    keys.sort((a, b) => a > b ? 1 : -1);\n    for (let i = 0; i < keys.length; i++) program.attributeData[keys[i]].location = i, gl.bindAttribLocation(webGLProgram, i, keys[i]);\n    gl.linkProgram(webGLProgram);\n  }\n  gl.deleteShader(glVertShader), gl.deleteShader(glFragShader);\n  const uniformData = {};\n  for (const i in program.uniformData) {\n    const data = program.uniformData[i];\n    uniformData[i] = {\n      location: gl.getUniformLocation(webGLProgram, i),\n      value: defaultValue(data.type, data.size)\n    };\n  }\n  return new GLProgram(webGLProgram, uniformData);\n}\nexport { generateProgram };", "map": {"version": 3, "names": ["generateProgram", "gl", "program", "gl<PERSON><PERSON><PERSON><PERSON><PERSON>", "compileShader", "VERTEX_SHADER", "vertexSrc", "gl<PERSON>rag<PERSON><PERSON>er", "FRAGMENT_SHADER", "fragmentSrc", "webGLProgram", "createProgram", "<PERSON><PERSON><PERSON><PERSON>", "transformFeedbackVaryings", "extra", "console", "warn", "names", "bufferMode", "SEPARATE_ATTRIBS", "INTERLEAVED_ATTRIBS", "linkProgram", "getProgramParameter", "LINK_STATUS", "logProgramError", "attributeData", "getAttributeData", "uniformData", "getUniformData", "test", "keys", "Object", "sort", "a", "b", "i", "length", "location", "bindAttribLocation", "deleteShader", "data", "getUniformLocation", "value", "defaultValue", "type", "size", "GLProgram"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\shader\\utils\\generateProgram.ts"], "sourcesContent": ["import { GLProgram } from '../GLProgram';\nimport { compileShader } from './compileShader';\nimport { defaultValue } from './defaultValue';\nimport { getAttributeData } from './getAttributeData';\nimport { getUniformData } from './getUniformData';\nimport { logProgramError } from './logProgramError';\n\nimport type { IRenderingContext } from '../../IRenderer';\nimport type { IGLUniformData } from '../GLProgram';\nimport type { Program } from '../Program';\n\n/**\n * generates a WebGL Program object from a high level Pixi Program.\n * @param gl - a rendering context on which to generate the program\n * @param program - the high level Pixi Program.\n */\nexport function generateProgram(gl: IRenderingContext, program: Program): GLProgram\n{\n    const glVertShader = compileShader(gl, gl.VERTEX_SHADER, program.vertexSrc);\n    const glFragShader = compileShader(gl, gl.FRAGMENT_SHADER, program.fragmentSrc);\n\n    const webGLProgram = gl.createProgram();\n\n    gl.attachShader(webGLProgram, glVertShader);\n    gl.attachShader(webGLProgram, glFragShader);\n\n    const transformFeedbackVaryings = program.extra?.transformFeedbackVaryings;\n\n    if (transformFeedbackVaryings)\n    {\n        if (typeof gl.transformFeedbackVaryings !== 'function')\n        {\n            if (process.env.DEBUG)\n            {\n                console.warn(`TransformFeedback is not supported but TransformFeedbackVaryings are given.`);\n            }\n        }\n        else\n        {\n            gl.transformFeedbackVaryings(\n                webGLProgram,\n                transformFeedbackVaryings.names,\n                transformFeedbackVaryings.bufferMode === 'separate'\n                    ? gl.SEPARATE_ATTRIBS\n                    : gl.INTERLEAVED_ATTRIBS\n            );\n        }\n    }\n\n    gl.linkProgram(webGLProgram);\n\n    if (!gl.getProgramParameter(webGLProgram, gl.LINK_STATUS))\n    {\n        logProgramError(gl, webGLProgram, glVertShader, glFragShader);\n    }\n\n    program.attributeData = getAttributeData(webGLProgram, gl);\n    program.uniformData = getUniformData(webGLProgram, gl);\n\n    // GLSL 1.00: bind attributes sorted by name in ascending order\n    // GLSL 3.00: don't change the attribute locations that where chosen by the compiler\n    //            or assigned by the layout specifier in the shader source code\n    if (!(/^[ \\t]*#[ \\t]*version[ \\t]+300[ \\t]+es[ \\t]*$/m).test(program.vertexSrc))\n    {\n        const keys = Object.keys(program.attributeData);\n\n        keys.sort((a, b) => (a > b) ? 1 : -1); // eslint-disable-line no-confusing-arrow\n\n        for (let i = 0; i < keys.length; i++)\n        {\n            program.attributeData[keys[i]].location = i;\n\n            gl.bindAttribLocation(webGLProgram, i, keys[i]);\n        }\n\n        gl.linkProgram(webGLProgram);\n    }\n\n    gl.deleteShader(glVertShader);\n    gl.deleteShader(glFragShader);\n\n    const uniformData: {[key: string]: IGLUniformData} = {};\n\n    for (const i in program.uniformData)\n    {\n        const data = program.uniformData[i];\n\n        uniformData[i] = {\n            location: gl.getUniformLocation(webGLProgram, i),\n            value: defaultValue(data.type, data.size),\n        };\n    }\n\n    const glProgram = new GLProgram(webGLProgram, uniformData);\n\n    return glProgram;\n}\n"], "mappings": ";;;;;;AAgBgB,SAAAA,gBAAgBC,EAAA,EAAuBC,OAAA,EACvD;EACI,MAAMC,YAAA,GAAeC,aAAA,CAAcH,EAAA,EAAIA,EAAA,CAAGI,aAAA,EAAeH,OAAA,CAAQI,SAAS;IACpEC,YAAA,GAAeH,aAAA,CAAcH,EAAA,EAAIA,EAAA,CAAGO,eAAA,EAAiBN,OAAA,CAAQO,WAAW;IAExEC,YAAA,GAAeT,EAAA,CAAGU,aAAA;EAExBV,EAAA,CAAGW,YAAA,CAAaF,YAAA,EAAcP,YAAY,GAC1CF,EAAA,CAAGW,YAAA,CAAaF,YAAA,EAAcH,YAAY;EAEpC,MAAAM,yBAAA,GAA4BX,OAAA,CAAQY,KAAA,EAAOD,yBAAA;EAE7C,IAAAA,yBAAA,KAEI,OAAOZ,EAAA,CAAGY,yBAAA,IAA8B,aAIpCE,OAAA,CAAQC,IAAA,CAAK,6EAA6E,IAK9Ff,EAAA,CAAGY,yBAAA,CACCH,YAAA,EACAG,yBAAA,CAA0BI,KAAA,EAC1BJ,yBAAA,CAA0BK,UAAA,KAAe,aACnCjB,EAAA,CAAGkB,gBAAA,GACHlB,EAAA,CAAGmB,mBAAA,IAKrBnB,EAAA,CAAGoB,WAAA,CAAYX,YAAY,GAEtBT,EAAA,CAAGqB,mBAAA,CAAoBZ,YAAA,EAAcT,EAAA,CAAGsB,WAAW,KAEpDC,eAAA,CAAgBvB,EAAA,EAAIS,YAAA,EAAcP,YAAA,EAAcI,YAAY,GAGhEL,OAAA,CAAQuB,aAAA,GAAgBC,gBAAA,CAAiBhB,YAAA,EAAcT,EAAE,GACzDC,OAAA,CAAQyB,WAAA,GAAcC,cAAA,CAAelB,YAAA,EAAcT,EAAE,GAKjD,CAAE,iDAAkD4B,IAAA,CAAK3B,OAAA,CAAQI,SAAS,GAC9E;IACI,MAAMwB,IAAA,GAAOC,MAAA,CAAOD,IAAA,CAAK5B,OAAA,CAAQuB,aAAa;IAE9CK,IAAA,CAAKE,IAAA,CAAK,CAACC,CAAA,EAAGC,CAAA,KAAOD,CAAA,GAAIC,CAAA,GAAK,IAAI,EAAE;IAEpC,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIL,IAAA,CAAKM,MAAA,EAAQD,CAAA,IAE7BjC,OAAA,CAAQuB,aAAA,CAAcK,IAAA,CAAKK,CAAC,CAAC,EAAEE,QAAA,GAAWF,CAAA,EAE1ClC,EAAA,CAAGqC,kBAAA,CAAmB5B,YAAA,EAAcyB,CAAA,EAAGL,IAAA,CAAKK,CAAC,CAAC;IAGlDlC,EAAA,CAAGoB,WAAA,CAAYX,YAAY;EAC/B;EAEAT,EAAA,CAAGsC,YAAA,CAAapC,YAAY,GAC5BF,EAAA,CAAGsC,YAAA,CAAahC,YAAY;EAE5B,MAAMoB,WAAA,GAA+C;EAE1C,WAAAQ,CAAA,IAAKjC,OAAA,CAAQyB,WAAA,EACxB;IACU,MAAAa,IAAA,GAAOtC,OAAA,CAAQyB,WAAA,CAAYQ,CAAC;IAElCR,WAAA,CAAYQ,CAAC,IAAI;MACbE,QAAA,EAAUpC,EAAA,CAAGwC,kBAAA,CAAmB/B,YAAA,EAAcyB,CAAC;MAC/CO,KAAA,EAAOC,YAAA,CAAaH,IAAA,CAAKI,IAAA,EAAMJ,IAAA,CAAKK,IAAI;IAAA;EAEhD;EAEkB,WAAIC,SAAA,CAAUpC,YAAA,EAAciB,WAAW;AAG7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}