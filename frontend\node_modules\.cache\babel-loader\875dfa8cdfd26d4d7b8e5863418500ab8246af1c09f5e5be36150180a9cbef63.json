{"ast": null, "code": "import { GC_MODES } from \"@pixi/constants\";\nimport { ExtensionType, extensions } from \"@pixi/extensions\";\nconst _TextureGCSystem = class _TextureGCSystem2 {\n  /** @param renderer - The renderer this System works for. */\n  constructor(renderer) {\n    this.renderer = renderer, this.count = 0, this.checkCount = 0, this.maxIdle = _TextureGCSystem2.defaultMaxIdle, this.checkCountMax = _TextureGCSystem2.defaultCheckCountMax, this.mode = _TextureGCSystem2.defaultMode;\n  }\n  /**\n   * Checks to see when the last time a texture was used.\n   * If the texture has not been used for a specified amount of time, it will be removed from the GPU.\n   */\n  postrender() {\n    this.renderer.objectRenderer.renderingToScreen && (this.count++, this.mode !== GC_MODES.MANUAL && (this.checkCount++, this.checkCount > this.checkCountMax && (this.checkCount = 0, this.run())));\n  }\n  /**\n   * Checks to see when the last time a texture was used.\n   * If the texture has not been used for a specified amount of time, it will be removed from the GPU.\n   */\n  run() {\n    const tm = this.renderer.texture,\n      managedTextures = tm.managedTextures;\n    let wasRemoved = !1;\n    for (let i = 0; i < managedTextures.length; i++) {\n      const texture = managedTextures[i];\n      texture.resource && this.count - texture.touched > this.maxIdle && (tm.destroyTexture(texture, !0), managedTextures[i] = null, wasRemoved = !0);\n    }\n    if (wasRemoved) {\n      let j = 0;\n      for (let i = 0; i < managedTextures.length; i++) managedTextures[i] !== null && (managedTextures[j++] = managedTextures[i]);\n      managedTextures.length = j;\n    }\n  }\n  /**\n   * Removes all the textures within the specified displayObject and its children from the GPU.\n   * @param {PIXI.DisplayObject} displayObject - the displayObject to remove the textures from.\n   */\n  unload(displayObject) {\n    const tm = this.renderer.texture,\n      texture = displayObject._texture;\n    texture && !texture.framebuffer && tm.destroyTexture(texture);\n    for (let i = displayObject.children.length - 1; i >= 0; i--) this.unload(displayObject.children[i]);\n  }\n  destroy() {\n    this.renderer = null;\n  }\n};\n_TextureGCSystem.defaultMode = GC_MODES.AUTO,\n/**\n* Default maximum idle frames before a texture is destroyed by garbage collection.\n* @static\n* @default 3600\n* @see PIXI.TextureGCSystem#maxIdle\n*/\n_TextureGCSystem.defaultMaxIdle = 60 * 60,\n/**\n* Default frames between two garbage collections.\n* @static\n* @default 600\n* @see PIXI.TextureGCSystem#checkCountMax\n*/\n_TextureGCSystem.defaultCheckCountMax = 60 * 10, /** @ignore */\n_TextureGCSystem.extension = {\n  type: ExtensionType.RendererSystem,\n  name: \"textureGC\"\n};\nlet TextureGCSystem = _TextureGCSystem;\nextensions.add(TextureGCSystem);\nexport { TextureGCSystem };", "map": {"version": 3, "names": ["_TextureGCSystem", "_TextureGCSystem2", "constructor", "renderer", "count", "checkCount", "maxIdle", "defaultMaxIdle", "checkCountMax", "defaultCheckCountMax", "mode", "defaultMode", "postrender", "object<PERSON><PERSON><PERSON>", "renderingToScreen", "GC_MODES", "MANUAL", "run", "tm", "texture", "managedTextures", "wasRemoved", "i", "length", "resource", "touched", "destroyTexture", "j", "unload", "displayObject", "_texture", "framebuffer", "children", "destroy", "AUTO", "extension", "type", "ExtensionType", "RendererSystem", "name", "TextureGCSystem", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\textures\\TextureGCSystem.ts"], "sourcesContent": ["import { GC_MODES } from '@pixi/constants';\nimport { extensions, ExtensionType } from '@pixi/extensions';\n\nimport type { ExtensionMetadata } from '@pixi/extensions';\nimport type { Renderer } from '../Renderer';\nimport type { RenderTexture } from '../renderTexture/RenderTexture';\nimport type { ISystem } from '../system/ISystem';\nimport type { Texture } from './Texture';\n\nexport interface IUnloadableTexture\n{\n    _texture: Texture | RenderTexture;\n    children: IUnloadableTexture[];\n}\n\n/**\n * System plugin to the renderer to manage texture garbage collection on the GPU,\n * ensuring that it does not get clogged up with textures that are no longer being used.\n * @memberof PIXI\n */\nexport class TextureGCSystem implements ISystem\n{\n    /**\n     * Default garbage collection mode.\n     * @static\n     * @type {PIXI.GC_MODES}\n     * @default PIXI.GC_MODES.AUTO\n     * @see PIXI.TextureGCSystem#mode\n     */\n    public static defaultMode = GC_MODES.AUTO;\n\n    /**\n     * Default maximum idle frames before a texture is destroyed by garbage collection.\n     * @static\n     * @default 3600\n     * @see PIXI.TextureGCSystem#maxIdle\n     */\n    public static defaultMaxIdle = 60 * 60;\n\n    /**\n     * Default frames between two garbage collections.\n     * @static\n     * @default 600\n     * @see PIXI.TextureGCSystem#checkCountMax\n     */\n    public static defaultCheckCountMax = 60 * 10;\n\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        type: ExtensionType.RendererSystem,\n        name: 'textureGC',\n    };\n\n    /**\n     * Frame count since started.\n     * @readonly\n     */\n    public count: number;\n\n    /**\n     * Frame count since last garbage collection.\n     * @readonly\n     */\n    public checkCount: number;\n\n    /**\n     * Maximum idle frames before a texture is destroyed by garbage collection.\n     * @see PIXI.TextureGCSystem.defaultMaxIdle\n     */\n    public maxIdle: number;\n\n    /**\n     * Frames between two garbage collections.\n     * @see PIXI.TextureGCSystem.defaultCheckCountMax\n     */\n    public checkCountMax: number;\n\n    /**\n     * Current garbage collection mode.\n     * @see PIXI.TextureGCSystem.defaultMode\n     */\n    public mode: GC_MODES;\n    private renderer: Renderer;\n\n    /** @param renderer - The renderer this System works for. */\n    constructor(renderer: Renderer)\n    {\n        this.renderer = renderer;\n\n        this.count = 0;\n        this.checkCount = 0;\n        this.maxIdle = TextureGCSystem.defaultMaxIdle;\n        this.checkCountMax = TextureGCSystem.defaultCheckCountMax;\n        this.mode = TextureGCSystem.defaultMode;\n    }\n\n    /**\n     * Checks to see when the last time a texture was used.\n     * If the texture has not been used for a specified amount of time, it will be removed from the GPU.\n     */\n    protected postrender(): void\n    {\n        if (!this.renderer.objectRenderer.renderingToScreen)\n        {\n            return;\n        }\n\n        this.count++;\n\n        if (this.mode === GC_MODES.MANUAL)\n        {\n            return;\n        }\n\n        this.checkCount++;\n\n        if (this.checkCount > this.checkCountMax)\n        {\n            this.checkCount = 0;\n\n            this.run();\n        }\n    }\n\n    /**\n     * Checks to see when the last time a texture was used.\n     * If the texture has not been used for a specified amount of time, it will be removed from the GPU.\n     */\n    run(): void\n    {\n        const tm = this.renderer.texture;\n        const managedTextures = tm.managedTextures;\n        let wasRemoved = false;\n\n        for (let i = 0; i < managedTextures.length; i++)\n        {\n            const texture = managedTextures[i];\n\n            // Only supports non generated textures at the moment!\n            if (texture.resource && this.count - texture.touched > this.maxIdle)\n            {\n                tm.destroyTexture(texture, true);\n                managedTextures[i] = null;\n                wasRemoved = true;\n            }\n        }\n\n        if (wasRemoved)\n        {\n            let j = 0;\n\n            for (let i = 0; i < managedTextures.length; i++)\n            {\n                if (managedTextures[i] !== null)\n                {\n                    managedTextures[j++] = managedTextures[i];\n                }\n            }\n\n            managedTextures.length = j;\n        }\n    }\n\n    /**\n     * Removes all the textures within the specified displayObject and its children from the GPU.\n     * @param {PIXI.DisplayObject} displayObject - the displayObject to remove the textures from.\n     */\n    unload(displayObject: IUnloadableTexture): void\n    {\n        const tm = this.renderer.texture;\n        const texture = displayObject._texture as RenderTexture;\n\n        // only destroy non generated textures\n        if (texture && !texture.framebuffer)\n        {\n            tm.destroyTexture(texture);\n        }\n\n        for (let i = displayObject.children.length - 1; i >= 0; i--)\n        {\n            this.unload(displayObject.children[i]);\n        }\n    }\n\n    destroy(): void\n    {\n        this.renderer = null;\n    }\n}\n\nextensions.add(TextureGCSystem);\n"], "mappings": ";;AAoBO,MAAMA,gBAAA,GAAN,MAAMC,iBAAA,CACb;EAAA;EAgEIC,YAAYC,QAAA,EACZ;IACI,KAAKA,QAAA,GAAWA,QAAA,EAEhB,KAAKC,KAAA,GAAQ,GACb,KAAKC,UAAA,GAAa,GAClB,KAAKC,OAAA,GAAUL,iBAAA,CAAgBM,cAAA,EAC/B,KAAKC,aAAA,GAAgBP,iBAAA,CAAgBQ,oBAAA,EACrC,KAAKC,IAAA,GAAOT,iBAAA,CAAgBU,WAAA;EAChC;EAAA;AAAA;AAAA;AAAA;EAMUC,WAAA,EACV;IACS,KAAKT,QAAA,CAASU,cAAA,CAAeC,iBAAA,KAKlC,KAAKV,KAAA,IAED,KAAKM,IAAA,KAASK,QAAA,CAASC,MAAA,KAK3B,KAAKX,UAAA,IAED,KAAKA,UAAA,GAAa,KAAKG,aAAA,KAEvB,KAAKH,UAAA,GAAa,GAElB,KAAKY,GAAA,CAAI;EAEjB;EAAA;AAAA;AAAA;AAAA;EAMAA,IAAA,EACA;IACI,MAAMC,EAAA,GAAK,KAAKf,QAAA,CAASgB,OAAA;MACnBC,eAAA,GAAkBF,EAAA,CAAGE,eAAA;IAC3B,IAAIC,UAAA,GAAa;IAEjB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIF,eAAA,CAAgBG,MAAA,EAAQD,CAAA,IAC5C;MACU,MAAAH,OAAA,GAAUC,eAAA,CAAgBE,CAAC;MAG7BH,OAAA,CAAQK,QAAA,IAAY,KAAKpB,KAAA,GAAQe,OAAA,CAAQM,OAAA,GAAU,KAAKnB,OAAA,KAExDY,EAAA,CAAGQ,cAAA,CAAeP,OAAA,EAAS,EAAI,GAC/BC,eAAA,CAAgBE,CAAC,IAAI,MACrBD,UAAA,GAAa;IAErB;IAEA,IAAIA,UAAA,EACJ;MACI,IAAIM,CAAA,GAAI;MAER,SAASL,CAAA,GAAI,GAAGA,CAAA,GAAIF,eAAA,CAAgBG,MAAA,EAAQD,CAAA,IAEpCF,eAAA,CAAgBE,CAAC,MAAM,SAEvBF,eAAA,CAAgBO,CAAA,EAAG,IAAIP,eAAA,CAAgBE,CAAC;MAIhDF,eAAA,CAAgBG,MAAA,GAASI,CAAA;IAC7B;EACJ;EAAA;AAAA;AAAA;AAAA;EAMAC,OAAOC,aAAA,EACP;IACI,MAAMX,EAAA,GAAK,KAAKf,QAAA,CAASgB,OAAA;MACnBA,OAAA,GAAUU,aAAA,CAAcC,QAAA;IAG1BX,OAAA,IAAW,CAACA,OAAA,CAAQY,WAAA,IAEpBb,EAAA,CAAGQ,cAAA,CAAeP,OAAO;IAG7B,SAASG,CAAA,GAAIO,aAAA,CAAcG,QAAA,CAAST,MAAA,GAAS,GAAGD,CAAA,IAAK,GAAGA,CAAA,IAEpD,KAAKM,MAAA,CAAOC,aAAA,CAAcG,QAAA,CAASV,CAAC,CAAC;EAE7C;EAEAW,QAAA,EACA;IACI,KAAK9B,QAAA,GAAW;EACpB;AACJ;AAxKaH,gBAAA,CASKW,WAAA,GAAcI,QAAA,CAASmB,IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAT5BlC,gBAAA,CAiBKO,cAAA,GAAiB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAjB3BP,gBAAA,CAyBKS,oBAAA,GAAuB,KAAK;AAzBjCT,gBAAA,CA4BFmC,SAAA,GAA+B;EAClCC,IAAA,EAAMC,aAAA,CAAcC,cAAA;EACpBC,IAAA,EAAM;AACV;AA/BG,IAAMC,eAAA,GAANxC,gBAAA;AA0KPyC,UAAA,CAAWC,GAAA,CAAIF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}