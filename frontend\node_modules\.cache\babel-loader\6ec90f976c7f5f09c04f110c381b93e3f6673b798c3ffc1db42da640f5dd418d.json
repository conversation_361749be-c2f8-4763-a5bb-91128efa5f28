{"ast": null, "code": "import { Ticker } from \"@pixi/ticker\";\nimport { BaseImageResource } from \"./BaseImageResource.mjs\";\nconst _VideoResource = class _VideoResource2 extends BaseImageResource {\n  /**\n   * @param {HTMLVideoElement|object|string|Array<string|object>} source - Video element to use.\n   * @param {object} [options] - Options to use\n   * @param {boolean} [options.autoLoad=true] - Start loading the video immediately\n   * @param {boolean} [options.autoPlay=true] - Start playing video immediately\n   * @param {number} [options.updateFPS=0] - How many times a second to update the texture from the video.\n   * If 0, `requestVideoFrameCallback` is used to update the texture.\n   * If `requestVideoFrameCallback` is not available, the texture is updated every render.\n   * @param {boolean} [options.crossorigin=true] - Load image using cross origin\n   * @param {boolean} [options.loop=false] - Loops the video\n   * @param {boolean} [options.muted=false] - Mutes the video audio, useful for autoplay\n   * @param {boolean} [options.playsinline=true] - Prevents opening the video on mobile devices\n   */\n  constructor(source, options) {\n    if (options = options || {}, !(source instanceof HTMLVideoElement)) {\n      const videoElement = document.createElement(\"video\");\n      options.autoLoad !== !1 && videoElement.setAttribute(\"preload\", \"auto\"), options.playsinline !== !1 && (videoElement.setAttribute(\"webkit-playsinline\", \"\"), videoElement.setAttribute(\"playsinline\", \"\")), options.muted === !0 && (videoElement.setAttribute(\"muted\", \"\"), videoElement.muted = !0), options.loop === !0 && videoElement.setAttribute(\"loop\", \"\"), options.autoPlay !== !1 && videoElement.setAttribute(\"autoplay\", \"\"), typeof source == \"string\" && (source = [source]);\n      const firstSrc = source[0].src || source[0];\n      BaseImageResource.crossOrigin(videoElement, firstSrc, options.crossorigin);\n      for (let i = 0; i < source.length; ++i) {\n        const sourceElement = document.createElement(\"source\");\n        let {\n          src,\n          mime\n        } = source[i];\n        if (src = src || source[i], src.startsWith(\"data:\")) mime = src.slice(5, src.indexOf(\";\"));else if (!src.startsWith(\"blob:\")) {\n          const baseSrc = src.split(\"?\").shift().toLowerCase(),\n            ext = baseSrc.slice(baseSrc.lastIndexOf(\".\") + 1);\n          mime = mime || _VideoResource2.MIME_TYPES[ext] || `video/${ext}`;\n        }\n        sourceElement.src = src, mime && (sourceElement.type = mime), videoElement.appendChild(sourceElement);\n      }\n      source = videoElement;\n    }\n    super(source), this.noSubImage = !0, this._autoUpdate = !0, this._isConnectedToTicker = !1, this._updateFPS = options.updateFPS || 0, this._msToNextUpdate = 0, this.autoPlay = options.autoPlay !== !1, this._videoFrameRequestCallback = this._videoFrameRequestCallback.bind(this), this._videoFrameRequestCallbackHandle = null, this._load = null, this._resolve = null, this._reject = null, this._onCanPlay = this._onCanPlay.bind(this), this._onError = this._onError.bind(this), this._onPlayStart = this._onPlayStart.bind(this), this._onPlayStop = this._onPlayStop.bind(this), this._onSeeked = this._onSeeked.bind(this), options.autoLoad !== !1 && this.load();\n  }\n  /**\n   * Trigger updating of the texture.\n   * @param _deltaTime - time delta since last tick\n   */\n  update(_deltaTime = 0) {\n    if (!this.destroyed) {\n      if (this._updateFPS) {\n        const elapsedMS = Ticker.shared.elapsedMS * this.source.playbackRate;\n        this._msToNextUpdate = Math.floor(this._msToNextUpdate - elapsedMS);\n      }\n      (!this._updateFPS || this._msToNextUpdate <= 0) && (super.update(\n        /* deltaTime*/\n      ), this._msToNextUpdate = this._updateFPS ? Math.floor(1e3 / this._updateFPS) : 0);\n    }\n  }\n  _videoFrameRequestCallback() {\n    this.update(), this.destroyed ? this._videoFrameRequestCallbackHandle = null : this._videoFrameRequestCallbackHandle = this.source.requestVideoFrameCallback(this._videoFrameRequestCallback);\n  }\n  /**\n   * Start preloading the video resource.\n   * @returns {Promise<void>} Handle the validate event\n   */\n  load() {\n    if (this._load) return this._load;\n    const source = this.source;\n    return (source.readyState === source.HAVE_ENOUGH_DATA || source.readyState === source.HAVE_FUTURE_DATA) && source.width && source.height && (source.complete = !0), source.addEventListener(\"play\", this._onPlayStart), source.addEventListener(\"pause\", this._onPlayStop), source.addEventListener(\"seeked\", this._onSeeked), this._isSourceReady() ? this._onCanPlay() : (source.addEventListener(\"canplay\", this._onCanPlay), source.addEventListener(\"canplaythrough\", this._onCanPlay), source.addEventListener(\"error\", this._onError, !0)), this._load = new Promise((resolve, reject) => {\n      this.valid ? resolve(this) : (this._resolve = resolve, this._reject = reject, source.load());\n    }), this._load;\n  }\n  /**\n   * Handle video error events.\n   * @param event\n   */\n  _onError(event) {\n    this.source.removeEventListener(\"error\", this._onError, !0), this.onError.emit(event), this._reject && (this._reject(event), this._reject = null, this._resolve = null);\n  }\n  /**\n   * Returns true if the underlying source is playing.\n   * @returns - True if playing.\n   */\n  _isSourcePlaying() {\n    const source = this.source;\n    return !source.paused && !source.ended;\n  }\n  /**\n   * Returns true if the underlying source is ready for playing.\n   * @returns - True if ready.\n   */\n  _isSourceReady() {\n    return this.source.readyState > 2;\n  }\n  /** Runs the update loop when the video is ready to play. */\n  _onPlayStart() {\n    this.valid || this._onCanPlay(), this._configureAutoUpdate();\n  }\n  /** Fired when a pause event is triggered, stops the update loop. */\n  _onPlayStop() {\n    this._configureAutoUpdate();\n  }\n  /** Fired when the video is completed seeking to the current playback position. */\n  _onSeeked() {\n    this._autoUpdate && !this._isSourcePlaying() && (this._msToNextUpdate = 0, this.update(), this._msToNextUpdate = 0);\n  }\n  /** Fired when the video is loaded and ready to play. */\n  _onCanPlay() {\n    const source = this.source;\n    source.removeEventListener(\"canplay\", this._onCanPlay), source.removeEventListener(\"canplaythrough\", this._onCanPlay);\n    const valid = this.valid;\n    this._msToNextUpdate = 0, this.update(), this._msToNextUpdate = 0, !valid && this._resolve && (this._resolve(this), this._resolve = null, this._reject = null), this._isSourcePlaying() ? this._onPlayStart() : this.autoPlay && source.play();\n  }\n  /** Destroys this texture. */\n  dispose() {\n    this._configureAutoUpdate();\n    const source = this.source;\n    source && (source.removeEventListener(\"play\", this._onPlayStart), source.removeEventListener(\"pause\", this._onPlayStop), source.removeEventListener(\"seeked\", this._onSeeked), source.removeEventListener(\"canplay\", this._onCanPlay), source.removeEventListener(\"canplaythrough\", this._onCanPlay), source.removeEventListener(\"error\", this._onError, !0), source.pause(), source.src = \"\", source.load()), super.dispose();\n  }\n  /** Should the base texture automatically update itself, set to true by default. */\n  get autoUpdate() {\n    return this._autoUpdate;\n  }\n  set autoUpdate(value) {\n    value !== this._autoUpdate && (this._autoUpdate = value, this._configureAutoUpdate());\n  }\n  /**\n   * How many times a second to update the texture from the video. If 0, `requestVideoFrameCallback` is used to\n   * update the texture. If `requestVideoFrameCallback` is not available, the texture is updated every render.\n   * A lower fps can help performance, as updating the texture at 60fps on a 30ps video may not be efficient.\n   */\n  get updateFPS() {\n    return this._updateFPS;\n  }\n  set updateFPS(value) {\n    value !== this._updateFPS && (this._updateFPS = value, this._configureAutoUpdate());\n  }\n  _configureAutoUpdate() {\n    this._autoUpdate && this._isSourcePlaying() ? !this._updateFPS && this.source.requestVideoFrameCallback ? (this._isConnectedToTicker && (Ticker.shared.remove(this.update, this), this._isConnectedToTicker = !1, this._msToNextUpdate = 0), this._videoFrameRequestCallbackHandle === null && (this._videoFrameRequestCallbackHandle = this.source.requestVideoFrameCallback(this._videoFrameRequestCallback))) : (this._videoFrameRequestCallbackHandle !== null && (this.source.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle), this._videoFrameRequestCallbackHandle = null), this._isConnectedToTicker || (Ticker.shared.add(this.update, this), this._isConnectedToTicker = !0, this._msToNextUpdate = 0)) : (this._videoFrameRequestCallbackHandle !== null && (this.source.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle), this._videoFrameRequestCallbackHandle = null), this._isConnectedToTicker && (Ticker.shared.remove(this.update, this), this._isConnectedToTicker = !1, this._msToNextUpdate = 0));\n  }\n  /**\n   * Used to auto-detect the type of resource.\n   * @param {*} source - The source object\n   * @param {string} extension - The extension of source, if set\n   * @returns {boolean} `true` if video source\n   */\n  static test(source, extension) {\n    return globalThis.HTMLVideoElement && source instanceof HTMLVideoElement || _VideoResource2.TYPES.includes(extension);\n  }\n};\n_VideoResource.TYPES = [\"mp4\", \"m4v\", \"webm\", \"ogg\", \"ogv\", \"h264\", \"avi\", \"mov\"],\n/**\n* Map of video MIME types that can't be directly derived from file extensions.\n* @readonly\n*/\n_VideoResource.MIME_TYPES = {\n  ogv: \"video/ogg\",\n  mov: \"video/quicktime\",\n  m4v: \"video/mp4\"\n};\nlet VideoResource = _VideoResource;\nexport { VideoResource };", "map": {"version": 3, "names": ["_VideoResource", "_VideoResource2", "BaseImageResource", "constructor", "source", "options", "HTMLVideoElement", "videoElement", "document", "createElement", "autoLoad", "setAttribute", "playsinline", "muted", "loop", "autoPlay", "firstSrc", "src", "crossOrigin", "crossorigin", "i", "length", "sourceElement", "mime", "startsWith", "slice", "indexOf", "baseSrc", "split", "shift", "toLowerCase", "ext", "lastIndexOf", "MIME_TYPES", "type", "append<PERSON><PERSON><PERSON>", "noSubImage", "_autoUpdate", "_isConnectedToTicker", "_updateFPS", "updateFPS", "_msToNextUpdate", "_videoFrameRequestCallback", "bind", "_videoFrameRequestCallbackHandle", "_load", "_resolve", "_reject", "_onCanPlay", "_onError", "_onPlayStart", "_onPlayStop", "_onSeeked", "load", "update", "_deltaTime", "destroyed", "elapsedMS", "Ticker", "shared", "playbackRate", "Math", "floor", "requestVideoFrameCallback", "readyState", "HAVE_ENOUGH_DATA", "HAVE_FUTURE_DATA", "width", "height", "complete", "addEventListener", "_isSourceReady", "Promise", "resolve", "reject", "valid", "event", "removeEventListener", "onError", "emit", "_isSourcePlaying", "paused", "ended", "_configureAutoUpdate", "play", "dispose", "pause", "autoUpdate", "value", "remove", "cancelVideoFrameCallback", "add", "test", "extension", "globalThis", "TYPES", "includes", "ogv", "mov", "m4v", "VideoResource"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\textures\\resources\\VideoResource.ts"], "sourcesContent": ["import { Ticker } from '@pixi/ticker';\nimport { BaseImageResource } from './BaseImageResource';\n\nimport type { Dict } from '@pixi/utils';\n\nexport interface IVideoResourceOptions\n{\n    autoLoad?: boolean;\n    autoPlay?: boolean;\n    updateFPS?: number;\n    crossorigin?: boolean | string;\n    loop?: boolean;\n    muted?: boolean;\n    playsinline?: boolean;\n}\n\nexport interface IVideoResourceOptionsElement\n{\n    src: string;\n    mime: string;\n}\n\n/**\n * Resource type for {@link HTMLVideoElement}.\n * @memberof PIXI\n */\nexport class VideoResource extends BaseImageResource\n{\n    /** Override the source to be the video element. */\n    public source: HTMLVideoElement;\n\n    /**\n     * `true` to use Ticker.shared to auto update the base texture.\n     * @default true\n     */\n    protected _autoUpdate: boolean;\n\n    /**\n     * `true` if the instance is currently connected to PIXI.Ticker.shared to auto update the base texture.\n     * @default false\n     */\n    protected _isConnectedToTicker: boolean;\n    protected _updateFPS: number;\n    protected _msToNextUpdate: number;\n\n    private _videoFrameRequestCallbackHandle: number | null;\n\n    /**\n     * When set to true will automatically play videos used by this texture once\n     * they are loaded. If false, it will not modify the playing state.\n     * @default true\n     */\n    protected autoPlay: boolean;\n\n    /**\n     * Promise when loading.\n     * @default null\n     */\n    private _load: Promise<this>;\n\n    /** Callback when completed with load. */\n    private _resolve: (value?: this | PromiseLike<this>) => void;\n    private _reject: (error: ErrorEvent) => void;\n\n    /**\n     * @param {HTMLVideoElement|object|string|Array<string|object>} source - Video element to use.\n     * @param {object} [options] - Options to use\n     * @param {boolean} [options.autoLoad=true] - Start loading the video immediately\n     * @param {boolean} [options.autoPlay=true] - Start playing video immediately\n     * @param {number} [options.updateFPS=0] - How many times a second to update the texture from the video.\n     * If 0, `requestVideoFrameCallback` is used to update the texture.\n     * If `requestVideoFrameCallback` is not available, the texture is updated every render.\n     * @param {boolean} [options.crossorigin=true] - Load image using cross origin\n     * @param {boolean} [options.loop=false] - Loops the video\n     * @param {boolean} [options.muted=false] - Mutes the video audio, useful for autoplay\n     * @param {boolean} [options.playsinline=true] - Prevents opening the video on mobile devices\n     */\n    constructor(\n        source?: HTMLVideoElement | Array<string | IVideoResourceOptionsElement> | string, options?: IVideoResourceOptions\n    )\n    {\n        options = options || {};\n\n        if (!(source instanceof HTMLVideoElement))\n        {\n            const videoElement = document.createElement('video');\n\n            // workaround for https://github.com/pixijs/pixijs/issues/5996\n            if (options.autoLoad !== false)\n            {\n                videoElement.setAttribute('preload', 'auto');\n            }\n\n            if (options.playsinline !== false)\n            {\n                videoElement.setAttribute('webkit-playsinline', '');\n                videoElement.setAttribute('playsinline', '');\n            }\n\n            if (options.muted === true)\n            {\n                // For some reason we need to set both muted flags for chrome to autoplay\n                // https://stackoverflow.com/a/51189390\n\n                videoElement.setAttribute('muted', '');\n                videoElement.muted = true;\n            }\n\n            if (options.loop === true)\n            {\n                videoElement.setAttribute('loop', '');\n            }\n\n            if (options.autoPlay !== false)\n            {\n                videoElement.setAttribute('autoplay', '');\n            }\n\n            if (typeof source === 'string')\n            {\n                source = [source];\n            }\n\n            const firstSrc = (source[0] as IVideoResourceOptionsElement).src || source[0] as string;\n\n            BaseImageResource.crossOrigin(videoElement, firstSrc, options.crossorigin);\n\n            // array of objects or strings\n            for (let i = 0; i < source.length; ++i)\n            {\n                const sourceElement = document.createElement('source');\n\n                let { src, mime } = source[i] as IVideoResourceOptionsElement;\n\n                src = src || source[i] as string;\n\n                if (src.startsWith('data:'))\n                {\n                    mime = src.slice(5, src.indexOf(';'));\n                }\n                else if (!src.startsWith('blob:'))\n                {\n                    const baseSrc = src.split('?').shift().toLowerCase();\n                    const ext = baseSrc.slice(baseSrc.lastIndexOf('.') + 1);\n\n                    mime = mime || VideoResource.MIME_TYPES[ext] || `video/${ext}`;\n                }\n\n                sourceElement.src = src;\n\n                if (mime)\n                {\n                    sourceElement.type = mime;\n                }\n\n                videoElement.appendChild(sourceElement);\n            }\n\n            // Override the source\n            source = videoElement;\n        }\n\n        super(source);\n\n        this.noSubImage = true;\n\n        this._autoUpdate = true;\n        this._isConnectedToTicker = false;\n\n        this._updateFPS = options.updateFPS || 0;\n        this._msToNextUpdate = 0;\n        this.autoPlay = options.autoPlay !== false;\n\n        this._videoFrameRequestCallback = this._videoFrameRequestCallback.bind(this);\n        this._videoFrameRequestCallbackHandle = null;\n\n        this._load = null;\n        this._resolve = null;\n        this._reject = null;\n\n        // Bind for listeners\n        this._onCanPlay = this._onCanPlay.bind(this);\n        this._onError = this._onError.bind(this);\n        this._onPlayStart = this._onPlayStart.bind(this);\n        this._onPlayStop = this._onPlayStop.bind(this);\n        this._onSeeked = this._onSeeked.bind(this);\n\n        if (options.autoLoad !== false)\n        {\n            this.load();\n        }\n    }\n\n    /**\n     * Trigger updating of the texture.\n     * @param _deltaTime - time delta since last tick\n     */\n    update(_deltaTime = 0): void\n    {\n        if (!this.destroyed)\n        {\n            if (this._updateFPS)\n            {\n                // account for if video has had its playbackRate changed\n                const elapsedMS = Ticker.shared.elapsedMS * (this.source as HTMLVideoElement).playbackRate;\n\n                this._msToNextUpdate = Math.floor(this._msToNextUpdate - elapsedMS);\n            }\n\n            if (!this._updateFPS || this._msToNextUpdate <= 0)\n            {\n                super.update(/* deltaTime*/);\n                this._msToNextUpdate = this._updateFPS ? Math.floor(1000 / this._updateFPS) : 0;\n            }\n        }\n    }\n\n    private _videoFrameRequestCallback(): void\n    {\n        this.update();\n\n        if (!this.destroyed)\n        {\n            this._videoFrameRequestCallbackHandle = (this.source as any).requestVideoFrameCallback(\n                this._videoFrameRequestCallback);\n        }\n        else\n        {\n            this._videoFrameRequestCallbackHandle = null;\n        }\n    }\n\n    /**\n     * Start preloading the video resource.\n     * @returns {Promise<void>} Handle the validate event\n     */\n    load(): Promise<this>\n    {\n        if (this._load)\n        {\n            return this._load;\n        }\n\n        const source = this.source as HTMLVideoElement;\n\n        if ((source.readyState === source.HAVE_ENOUGH_DATA || source.readyState === source.HAVE_FUTURE_DATA)\n            && source.width && source.height)\n        {\n            (source as any).complete = true;\n        }\n\n        source.addEventListener('play', this._onPlayStart);\n        source.addEventListener('pause', this._onPlayStop);\n        source.addEventListener('seeked', this._onSeeked);\n\n        if (!this._isSourceReady())\n        {\n            source.addEventListener('canplay', this._onCanPlay);\n            source.addEventListener('canplaythrough', this._onCanPlay);\n            source.addEventListener('error', this._onError, true);\n        }\n        else\n        {\n            this._onCanPlay();\n        }\n\n        this._load = new Promise((resolve, reject): void =>\n        {\n            if (this.valid)\n            {\n                resolve(this);\n            }\n            else\n            {\n                this._resolve = resolve;\n                this._reject = reject;\n\n                source.load();\n            }\n        });\n\n        return this._load;\n    }\n\n    /**\n     * Handle video error events.\n     * @param event\n     */\n    private _onError(event: ErrorEvent): void\n    {\n        (this.source as HTMLVideoElement).removeEventListener('error', this._onError, true);\n        this.onError.emit(event);\n\n        if (this._reject)\n        {\n            this._reject(event);\n            this._reject = null;\n            this._resolve = null;\n        }\n    }\n\n    /**\n     * Returns true if the underlying source is playing.\n     * @returns - True if playing.\n     */\n    private _isSourcePlaying(): boolean\n    {\n        const source = this.source as HTMLVideoElement;\n\n        return (!source.paused && !source.ended);\n    }\n\n    /**\n     * Returns true if the underlying source is ready for playing.\n     * @returns - True if ready.\n     */\n    private _isSourceReady(): boolean\n    {\n        const source = this.source as HTMLVideoElement;\n\n        return source.readyState > 2;\n    }\n\n    /** Runs the update loop when the video is ready to play. */\n    private _onPlayStart(): void\n    {\n        // Just in case the video has not received its can play even yet..\n        if (!this.valid)\n        {\n            this._onCanPlay();\n        }\n\n        this._configureAutoUpdate();\n    }\n\n    /** Fired when a pause event is triggered, stops the update loop. */\n    private _onPlayStop(): void\n    {\n        this._configureAutoUpdate();\n    }\n\n    /** Fired when the video is completed seeking to the current playback position. */\n    private _onSeeked(): void\n    {\n        if (this._autoUpdate && !this._isSourcePlaying())\n        {\n            this._msToNextUpdate = 0;\n            this.update();\n            this._msToNextUpdate = 0;\n        }\n    }\n\n    /** Fired when the video is loaded and ready to play. */\n    private _onCanPlay(): void\n    {\n        const source = this.source as HTMLVideoElement;\n\n        source.removeEventListener('canplay', this._onCanPlay);\n        source.removeEventListener('canplaythrough', this._onCanPlay);\n\n        const valid = this.valid;\n\n        this._msToNextUpdate = 0;\n        this.update();\n        this._msToNextUpdate = 0;\n\n        // prevent multiple loaded dispatches..\n        if (!valid && this._resolve)\n        {\n            this._resolve(this);\n            this._resolve = null;\n            this._reject = null;\n        }\n\n        if (this._isSourcePlaying())\n        {\n            this._onPlayStart();\n        }\n        else if (this.autoPlay)\n        {\n            source.play();\n        }\n    }\n\n    /** Destroys this texture. */\n    dispose(): void\n    {\n        this._configureAutoUpdate();\n\n        const source = this.source as HTMLVideoElement;\n\n        if (source)\n        {\n            source.removeEventListener('play', this._onPlayStart);\n            source.removeEventListener('pause', this._onPlayStop);\n            source.removeEventListener('seeked', this._onSeeked);\n            source.removeEventListener('canplay', this._onCanPlay);\n            source.removeEventListener('canplaythrough', this._onCanPlay);\n            source.removeEventListener('error', this._onError, true);\n            source.pause();\n            source.src = '';\n            source.load();\n        }\n        super.dispose();\n    }\n\n    /** Should the base texture automatically update itself, set to true by default. */\n    get autoUpdate(): boolean\n    {\n        return this._autoUpdate;\n    }\n\n    set autoUpdate(value: boolean)\n    {\n        if (value !== this._autoUpdate)\n        {\n            this._autoUpdate = value;\n            this._configureAutoUpdate();\n        }\n    }\n\n    /**\n     * How many times a second to update the texture from the video. If 0, `requestVideoFrameCallback` is used to\n     * update the texture. If `requestVideoFrameCallback` is not available, the texture is updated every render.\n     * A lower fps can help performance, as updating the texture at 60fps on a 30ps video may not be efficient.\n     */\n    get updateFPS(): number\n    {\n        return this._updateFPS;\n    }\n\n    set updateFPS(value: number)\n    {\n        if (value !== this._updateFPS)\n        {\n            this._updateFPS = value;\n            this._configureAutoUpdate();\n        }\n    }\n\n    private _configureAutoUpdate(): void\n    {\n        if (this._autoUpdate && this._isSourcePlaying())\n        {\n            if (!this._updateFPS && (this.source as any).requestVideoFrameCallback)\n            {\n                if (this._isConnectedToTicker)\n                {\n                    Ticker.shared.remove(this.update, this);\n                    this._isConnectedToTicker = false;\n                    this._msToNextUpdate = 0;\n                }\n\n                if (this._videoFrameRequestCallbackHandle === null)\n                {\n                    this._videoFrameRequestCallbackHandle = (this.source as any).requestVideoFrameCallback(\n                        this._videoFrameRequestCallback);\n                }\n            }\n            else\n            {\n                if (this._videoFrameRequestCallbackHandle !== null)\n                {\n                    (this.source as any).cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle);\n                    this._videoFrameRequestCallbackHandle = null;\n                }\n\n                if (!this._isConnectedToTicker)\n                {\n                    Ticker.shared.add(this.update, this);\n                    this._isConnectedToTicker = true;\n                    this._msToNextUpdate = 0;\n                }\n            }\n        }\n        else\n        {\n            if (this._videoFrameRequestCallbackHandle !== null)\n            {\n                (this.source as any).cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle);\n                this._videoFrameRequestCallbackHandle = null;\n            }\n\n            if (this._isConnectedToTicker)\n            {\n                Ticker.shared.remove(this.update, this);\n                this._isConnectedToTicker = false;\n                this._msToNextUpdate = 0;\n            }\n        }\n    }\n\n    /**\n     * Used to auto-detect the type of resource.\n     * @param {*} source - The source object\n     * @param {string} extension - The extension of source, if set\n     * @returns {boolean} `true` if video source\n     */\n    static test(source: unknown, extension?: string): source is HTMLVideoElement\n    {\n        return (globalThis.HTMLVideoElement && source instanceof HTMLVideoElement)\n            || VideoResource.TYPES.includes(extension);\n    }\n\n    /**\n     * List of common video file extensions supported by VideoResource.\n     * @readonly\n     */\n    static TYPES: Array<string> = ['mp4', 'm4v', 'webm', 'ogg', 'ogv', 'h264', 'avi', 'mov'];\n\n    /**\n     * Map of video MIME types that can't be directly derived from file extensions.\n     * @readonly\n     */\n    static MIME_TYPES: Dict<string> = {\n        ogv: 'video/ogg',\n        mov: 'video/quicktime',\n        m4v: 'video/mp4',\n    };\n}\n"], "mappings": ";;AA0BO,MAAMA,cAAA,GAAN,MAAMC,eAAA,SAAsBC,iBAAA,CACnC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAkDIC,YACIC,MAAA,EAAmFC,OAAA,EAEvF;IAGI,IAFAA,OAAA,GAAUA,OAAA,IAAW,IAEjB,EAAED,MAAA,YAAkBE,gBAAA,GACxB;MACU,MAAAC,YAAA,GAAeC,QAAA,CAASC,aAAA,CAAc,OAAO;MAG/CJ,OAAA,CAAQK,QAAA,KAAa,MAErBH,YAAA,CAAaI,YAAA,CAAa,WAAW,MAAM,GAG3CN,OAAA,CAAQO,WAAA,KAAgB,OAExBL,YAAA,CAAaI,YAAA,CAAa,sBAAsB,EAAE,GAClDJ,YAAA,CAAaI,YAAA,CAAa,eAAe,EAAE,IAG3CN,OAAA,CAAQQ,KAAA,KAAU,OAKlBN,YAAA,CAAaI,YAAA,CAAa,SAAS,EAAE,GACrCJ,YAAA,CAAaM,KAAA,GAAQ,KAGrBR,OAAA,CAAQS,IAAA,KAAS,MAEjBP,YAAA,CAAaI,YAAA,CAAa,QAAQ,EAAE,GAGpCN,OAAA,CAAQU,QAAA,KAAa,MAErBR,YAAA,CAAaI,YAAA,CAAa,YAAY,EAAE,GAGxC,OAAOP,MAAA,IAAW,aAElBA,MAAA,GAAS,CAACA,MAAM;MAGpB,MAAMY,QAAA,GAAYZ,MAAA,CAAO,CAAC,EAAmCa,GAAA,IAAOb,MAAA,CAAO,CAAC;MAE5EF,iBAAA,CAAkBgB,WAAA,CAAYX,YAAA,EAAcS,QAAA,EAAUX,OAAA,CAAQc,WAAW;MAGzE,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIhB,MAAA,CAAOiB,MAAA,EAAQ,EAAED,CAAA,EACrC;QACU,MAAAE,aAAA,GAAgBd,QAAA,CAASC,aAAA,CAAc,QAAQ;QAErD,IAAI;UAAEQ,GAAA;UAAKM;QAAK,IAAInB,MAAA,CAAOgB,CAAC;QAI5B,IAFAH,GAAA,GAAMA,GAAA,IAAOb,MAAA,CAAOgB,CAAC,GAEjBH,GAAA,CAAIO,UAAA,CAAW,OAAO,GAEtBD,IAAA,GAAON,GAAA,CAAIQ,KAAA,CAAM,GAAGR,GAAA,CAAIS,OAAA,CAAQ,GAAG,CAAC,WAE/B,CAACT,GAAA,CAAIO,UAAA,CAAW,OAAO,GAChC;UACI,MAAMG,OAAA,GAAUV,GAAA,CAAIW,KAAA,CAAM,GAAG,EAAEC,KAAA,GAAQC,WAAA;YACjCC,GAAA,GAAMJ,OAAA,CAAQF,KAAA,CAAME,OAAA,CAAQK,WAAA,CAAY,GAAG,IAAI,CAAC;UAEtDT,IAAA,GAAOA,IAAA,IAAQtB,eAAA,CAAcgC,UAAA,CAAWF,GAAG,KAAK,SAASA,GAAG;QAChE;QAEcT,aAAA,CAAAL,GAAA,GAAMA,GAAA,EAEhBM,IAAA,KAEAD,aAAA,CAAcY,IAAA,GAAOX,IAAA,GAGzBhB,YAAA,CAAa4B,WAAA,CAAYb,aAAa;MAC1C;MAGSlB,MAAA,GAAAG,YAAA;IACb;IAEA,MAAMH,MAAM,GAEZ,KAAKgC,UAAA,GAAa,IAElB,KAAKC,WAAA,GAAc,IACnB,KAAKC,oBAAA,GAAuB,IAE5B,KAAKC,UAAA,GAAalC,OAAA,CAAQmC,SAAA,IAAa,GACvC,KAAKC,eAAA,GAAkB,GACvB,KAAK1B,QAAA,GAAWV,OAAA,CAAQU,QAAA,KAAa,IAErC,KAAK2B,0BAAA,GAA6B,KAAKA,0BAAA,CAA2BC,IAAA,CAAK,IAAI,GAC3E,KAAKC,gCAAA,GAAmC,MAExC,KAAKC,KAAA,GAAQ,MACb,KAAKC,QAAA,GAAW,MAChB,KAAKC,OAAA,GAAU,MAGf,KAAKC,UAAA,GAAa,KAAKA,UAAA,CAAWL,IAAA,CAAK,IAAI,GAC3C,KAAKM,QAAA,GAAW,KAAKA,QAAA,CAASN,IAAA,CAAK,IAAI,GACvC,KAAKO,YAAA,GAAe,KAAKA,YAAA,CAAaP,IAAA,CAAK,IAAI,GAC/C,KAAKQ,WAAA,GAAc,KAAKA,WAAA,CAAYR,IAAA,CAAK,IAAI,GAC7C,KAAKS,SAAA,GAAY,KAAKA,SAAA,CAAUT,IAAA,CAAK,IAAI,GAErCtC,OAAA,CAAQK,QAAA,KAAa,MAErB,KAAK2C,IAAA;EAEb;EAAA;AAAA;AAAA;AAAA;EAMAC,OAAOC,UAAA,GAAa,GACpB;IACQ,KAAC,KAAKC,SAAA,EACV;MACI,IAAI,KAAKjB,UAAA,EACT;QAEI,MAAMkB,SAAA,GAAYC,MAAA,CAAOC,MAAA,CAAOF,SAAA,GAAa,KAAKrD,MAAA,CAA4BwD,YAAA;QAE9E,KAAKnB,eAAA,GAAkBoB,IAAA,CAAKC,KAAA,CAAM,KAAKrB,eAAA,GAAkBgB,SAAS;MACtE;MAEA,CAAI,CAAC,KAAKlB,UAAA,IAAc,KAAKE,eAAA,IAAmB,OAE5C,MAAMa,MAAA;QAAA;MAAA,CAAqB,EAC3B,KAAKb,eAAA,GAAkB,KAAKF,UAAA,GAAasB,IAAA,CAAKC,KAAA,CAAM,MAAO,KAAKvB,UAAU,IAAI;IAEtF;EACJ;EAEQG,2BAAA,EACR;IACS,KAAAY,MAAA,IAEA,KAAKE,SAAA,GAON,KAAKZ,gCAAA,GAAmC,OALxC,KAAKA,gCAAA,GAAoC,KAAKxC,MAAA,CAAe2D,yBAAA,CACzD,KAAKrB,0BAAA;EAMjB;EAAA;AAAA;AAAA;AAAA;EAMAW,KAAA,EACA;IACI,IAAI,KAAKR,KAAA,EAEL,OAAO,KAAKA,KAAA;IAGhB,MAAMzC,MAAA,GAAS,KAAKA,MAAA;IAEpB,QAAKA,MAAA,CAAO4D,UAAA,KAAe5D,MAAA,CAAO6D,gBAAA,IAAoB7D,MAAA,CAAO4D,UAAA,KAAe5D,MAAA,CAAO8D,gBAAA,KAC5E9D,MAAA,CAAO+D,KAAA,IAAS/D,MAAA,CAAOgE,MAAA,KAEzBhE,MAAA,CAAeiE,QAAA,GAAW,KAG/BjE,MAAA,CAAOkE,gBAAA,CAAiB,QAAQ,KAAKpB,YAAY,GACjD9C,MAAA,CAAOkE,gBAAA,CAAiB,SAAS,KAAKnB,WAAW,GACjD/C,MAAA,CAAOkE,gBAAA,CAAiB,UAAU,KAAKlB,SAAS,GAE3C,KAAKmB,cAAA,CAAe,IAQrB,KAAKvB,UAAA,MANL5C,MAAA,CAAOkE,gBAAA,CAAiB,WAAW,KAAKtB,UAAU,GAClD5C,MAAA,CAAOkE,gBAAA,CAAiB,kBAAkB,KAAKtB,UAAU,GACzD5C,MAAA,CAAOkE,gBAAA,CAAiB,SAAS,KAAKrB,QAAA,EAAU,EAAI,IAOxD,KAAKJ,KAAA,GAAQ,IAAI2B,OAAA,CAAQ,CAACC,OAAA,EAASC,MAAA,KACnC;MACQ,KAAKC,KAAA,GAELF,OAAA,CAAQ,IAAI,KAIZ,KAAK3B,QAAA,GAAW2B,OAAA,EAChB,KAAK1B,OAAA,GAAU2B,MAAA,EAEftE,MAAA,CAAOiD,IAAA,CAAK;IAAA,CAEnB,GAEM,KAAKR,KAAA;EAChB;EAAA;AAAA;AAAA;AAAA;EAMQI,SAAS2B,KAAA,EACjB;IACK,KAAKxE,MAAA,CAA4ByE,mBAAA,CAAoB,SAAS,KAAK5B,QAAA,EAAU,EAAI,GAClF,KAAK6B,OAAA,CAAQC,IAAA,CAAKH,KAAK,GAEnB,KAAK7B,OAAA,KAEL,KAAKA,OAAA,CAAQ6B,KAAK,GAClB,KAAK7B,OAAA,GAAU,MACf,KAAKD,QAAA,GAAW;EAExB;EAAA;AAAA;AAAA;AAAA;EAMQkC,iBAAA,EACR;IACI,MAAM5E,MAAA,GAAS,KAAKA,MAAA;IAEpB,OAAQ,CAACA,MAAA,CAAO6E,MAAA,IAAU,CAAC7E,MAAA,CAAO8E,KAAA;EACtC;EAAA;AAAA;AAAA;AAAA;EAMQX,eAAA,EACR;IACmB,YAAKnE,MAAA,CAEN4D,UAAA,GAAa;EAC/B;EAAA;EAGQd,aAAA,EACR;IAES,KAAKyB,KAAA,IAEN,KAAK3B,UAAA,CAAW,GAGpB,KAAKmC,oBAAA;EACT;EAAA;EAGQhC,YAAA,EACR;IACI,KAAKgC,oBAAA,CAAqB;EAC9B;EAAA;EAGQ/B,UAAA,EACR;IACQ,KAAKf,WAAA,IAAe,CAAC,KAAK2C,gBAAA,CAAiB,MAE3C,KAAKvC,eAAA,GAAkB,GACvB,KAAKa,MAAA,CAAO,GACZ,KAAKb,eAAA,GAAkB;EAE/B;EAAA;EAGQO,WAAA,EACR;IACI,MAAM5C,MAAA,GAAS,KAAKA,MAAA;IAEbA,MAAA,CAAAyE,mBAAA,CAAoB,WAAW,KAAK7B,UAAU,GACrD5C,MAAA,CAAOyE,mBAAA,CAAoB,kBAAkB,KAAK7B,UAAU;IAE5D,MAAM2B,KAAA,GAAQ,KAAKA,KAAA;IAEnB,KAAKlC,eAAA,GAAkB,GACvB,KAAKa,MAAA,IACL,KAAKb,eAAA,GAAkB,GAGnB,CAACkC,KAAA,IAAS,KAAK7B,QAAA,KAEf,KAAKA,QAAA,CAAS,IAAI,GAClB,KAAKA,QAAA,GAAW,MAChB,KAAKC,OAAA,GAAU,OAGf,KAAKiC,gBAAA,CAEL,SAAK9B,YAAA,CAAa,IAEb,KAAKnC,QAAA,IAEVX,MAAA,CAAOgF,IAAA;EAEf;EAAA;EAGAC,QAAA,EACA;IACI,KAAKF,oBAAA,CAAqB;IAE1B,MAAM/E,MAAA,GAAS,KAAKA,MAAA;IAEhBA,MAAA,KAEAA,MAAA,CAAOyE,mBAAA,CAAoB,QAAQ,KAAK3B,YAAY,GACpD9C,MAAA,CAAOyE,mBAAA,CAAoB,SAAS,KAAK1B,WAAW,GACpD/C,MAAA,CAAOyE,mBAAA,CAAoB,UAAU,KAAKzB,SAAS,GACnDhD,MAAA,CAAOyE,mBAAA,CAAoB,WAAW,KAAK7B,UAAU,GACrD5C,MAAA,CAAOyE,mBAAA,CAAoB,kBAAkB,KAAK7B,UAAU,GAC5D5C,MAAA,CAAOyE,mBAAA,CAAoB,SAAS,KAAK5B,QAAA,EAAU,EAAI,GACvD7C,MAAA,CAAOkF,KAAA,IACPlF,MAAA,CAAOa,GAAA,GAAM,IACbb,MAAA,CAAOiD,IAAA,KAEX,MAAMgC,OAAA;EACV;EAAA;EAGA,IAAIE,WAAA,EACJ;IACI,OAAO,KAAKlD,WAAA;EAChB;EAEA,IAAIkD,WAAWC,KAAA,EACf;IACQA,KAAA,KAAU,KAAKnD,WAAA,KAEf,KAAKA,WAAA,GAAcmD,KAAA,EACnB,KAAKL,oBAAA,CAAqB;EAElC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,IAAI3C,UAAA,EACJ;IACI,OAAO,KAAKD,UAAA;EAChB;EAEA,IAAIC,UAAUgD,KAAA,EACd;IACQA,KAAA,KAAU,KAAKjD,UAAA,KAEf,KAAKA,UAAA,GAAaiD,KAAA,EAClB,KAAKL,oBAAA,CAAqB;EAElC;EAEQA,qBAAA,EACR;IACQ,KAAK9C,WAAA,IAAe,KAAK2C,gBAAA,KAErB,CAAC,KAAKzC,UAAA,IAAe,KAAKnC,MAAA,CAAe2D,yBAAA,IAErC,KAAKzB,oBAAA,KAELoB,MAAA,CAAOC,MAAA,CAAO8B,MAAA,CAAO,KAAKnC,MAAA,EAAQ,IAAI,GACtC,KAAKhB,oBAAA,GAAuB,IAC5B,KAAKG,eAAA,GAAkB,IAGvB,KAAKG,gCAAA,KAAqC,SAE1C,KAAKA,gCAAA,GAAoC,KAAKxC,MAAA,CAAe2D,yBAAA,CACzD,KAAKrB,0BAA0B,OAKnC,KAAKE,gCAAA,KAAqC,SAEzC,KAAKxC,MAAA,CAAesF,wBAAA,CAAyB,KAAK9C,gCAAgC,GACnF,KAAKA,gCAAA,GAAmC,OAGvC,KAAKN,oBAAA,KAENoB,MAAA,CAAOC,MAAA,CAAOgC,GAAA,CAAI,KAAKrC,MAAA,EAAQ,IAAI,GACnC,KAAKhB,oBAAA,GAAuB,IAC5B,KAAKG,eAAA,GAAkB,OAM3B,KAAKG,gCAAA,KAAqC,SAEzC,KAAKxC,MAAA,CAAesF,wBAAA,CAAyB,KAAK9C,gCAAgC,GACnF,KAAKA,gCAAA,GAAmC,OAGxC,KAAKN,oBAAA,KAELoB,MAAA,CAAOC,MAAA,CAAO8B,MAAA,CAAO,KAAKnC,MAAA,EAAQ,IAAI,GACtC,KAAKhB,oBAAA,GAAuB,IAC5B,KAAKG,eAAA,GAAkB;EAGnC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQA,OAAOmD,KAAKxF,MAAA,EAAiByF,SAAA,EAC7B;IACI,OAAQC,UAAA,CAAWxF,gBAAA,IAAoBF,MAAA,YAAkBE,gBAAA,IAClDL,eAAA,CAAc8F,KAAA,CAAMC,QAAA,CAASH,SAAS;EACjD;AAiBJ;AA7ea7F,cAAA,CAkeF+F,KAAA,GAAuB,CAAC,OAAO,OAAO,QAAQ,OAAO,OAAO,QAAQ,OAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAle9E/F,cAAA,CAweFiC,UAAA,GAA2B;EAC9BgE,GAAA,EAAK;EACLC,GAAA,EAAK;EACLC,GAAA,EAAK;AACT;AA5eG,IAAMC,aAAA,GAANpG,cAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}