{"ast": null, "code": "import { Filter, defaultFilterVertex, Color } from \"@pixi/core\";\nimport fragment from \"./colorMatrix.frag.mjs\";\nclass ColorMatrixFilter extends Filter {\n  constructor() {\n    const uniforms = {\n      m: new Float32Array([1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0]),\n      uAlpha: 1\n    };\n    super(defaultFilterVertex, fragment, uniforms), this.alpha = 1;\n  }\n  /**\n   * Transforms current matrix and set the new one\n   * @param {number[]} matrix - 5x4 matrix\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  _loadMatrix(matrix, multiply = !1) {\n    let newMatrix = matrix;\n    multiply && (this._multiply(newMatrix, this.uniforms.m, matrix), newMatrix = this._colorMatrix(newMatrix)), this.uniforms.m = newMatrix;\n  }\n  /**\n   * Multiplies two mat5's\n   * @private\n   * @param out - 5x4 matrix the receiving matrix\n   * @param a - 5x4 matrix the first operand\n   * @param b - 5x4 matrix the second operand\n   * @returns {number[]} 5x4 matrix\n   */\n  _multiply(out, a, b) {\n    return out[0] = a[0] * b[0] + a[1] * b[5] + a[2] * b[10] + a[3] * b[15], out[1] = a[0] * b[1] + a[1] * b[6] + a[2] * b[11] + a[3] * b[16], out[2] = a[0] * b[2] + a[1] * b[7] + a[2] * b[12] + a[3] * b[17], out[3] = a[0] * b[3] + a[1] * b[8] + a[2] * b[13] + a[3] * b[18], out[4] = a[0] * b[4] + a[1] * b[9] + a[2] * b[14] + a[3] * b[19] + a[4], out[5] = a[5] * b[0] + a[6] * b[5] + a[7] * b[10] + a[8] * b[15], out[6] = a[5] * b[1] + a[6] * b[6] + a[7] * b[11] + a[8] * b[16], out[7] = a[5] * b[2] + a[6] * b[7] + a[7] * b[12] + a[8] * b[17], out[8] = a[5] * b[3] + a[6] * b[8] + a[7] * b[13] + a[8] * b[18], out[9] = a[5] * b[4] + a[6] * b[9] + a[7] * b[14] + a[8] * b[19] + a[9], out[10] = a[10] * b[0] + a[11] * b[5] + a[12] * b[10] + a[13] * b[15], out[11] = a[10] * b[1] + a[11] * b[6] + a[12] * b[11] + a[13] * b[16], out[12] = a[10] * b[2] + a[11] * b[7] + a[12] * b[12] + a[13] * b[17], out[13] = a[10] * b[3] + a[11] * b[8] + a[12] * b[13] + a[13] * b[18], out[14] = a[10] * b[4] + a[11] * b[9] + a[12] * b[14] + a[13] * b[19] + a[14], out[15] = a[15] * b[0] + a[16] * b[5] + a[17] * b[10] + a[18] * b[15], out[16] = a[15] * b[1] + a[16] * b[6] + a[17] * b[11] + a[18] * b[16], out[17] = a[15] * b[2] + a[16] * b[7] + a[17] * b[12] + a[18] * b[17], out[18] = a[15] * b[3] + a[16] * b[8] + a[17] * b[13] + a[18] * b[18], out[19] = a[15] * b[4] + a[16] * b[9] + a[17] * b[14] + a[18] * b[19] + a[19], out;\n  }\n  /**\n   * Create a Float32 Array and normalize the offset component to 0-1\n   * @param {number[]} matrix - 5x4 matrix\n   * @returns {number[]} 5x4 matrix with all values between 0-1\n   */\n  _colorMatrix(matrix) {\n    const m = new Float32Array(matrix);\n    return m[4] /= 255, m[9] /= 255, m[14] /= 255, m[19] /= 255, m;\n  }\n  /**\n   * Adjusts brightness\n   * @param b - value of the brigthness (0-1, where 0 is black)\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  brightness(b, multiply) {\n    const matrix = [b, 0, 0, 0, 0, 0, b, 0, 0, 0, 0, 0, b, 0, 0, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * Sets each channel on the diagonal of the color matrix.\n   * This can be used to achieve a tinting effect on Containers similar to the tint field of some\n   * display objects like Sprite, Text, Graphics, and Mesh.\n   * @param color - Color of the tint. This is a hex value.\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  tint(color, multiply) {\n    const [r, g, b] = Color.shared.setValue(color).toArray(),\n      matrix = [r, 0, 0, 0, 0, 0, g, 0, 0, 0, 0, 0, b, 0, 0, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * Set the matrices in grey scales\n   * @param scale - value of the grey (0-1, where 0 is black)\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  greyscale(scale, multiply) {\n    const matrix = [scale, scale, scale, 0, 0, scale, scale, scale, 0, 0, scale, scale, scale, 0, 0, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * Set the black and white matrice.\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  blackAndWhite(multiply) {\n    const matrix = [0.3, 0.6, 0.1, 0, 0, 0.3, 0.6, 0.1, 0, 0, 0.3, 0.6, 0.1, 0, 0, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * Set the hue property of the color\n   * @param rotation - in degrees\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  hue(rotation, multiply) {\n    rotation = (rotation || 0) / 180 * Math.PI;\n    const cosR = Math.cos(rotation),\n      sinR = Math.sin(rotation),\n      sqrt = Math.sqrt,\n      w = 1 / 3,\n      sqrW = sqrt(w),\n      a00 = cosR + (1 - cosR) * w,\n      a01 = w * (1 - cosR) - sqrW * sinR,\n      a02 = w * (1 - cosR) + sqrW * sinR,\n      a10 = w * (1 - cosR) + sqrW * sinR,\n      a11 = cosR + w * (1 - cosR),\n      a12 = w * (1 - cosR) - sqrW * sinR,\n      a20 = w * (1 - cosR) - sqrW * sinR,\n      a21 = w * (1 - cosR) + sqrW * sinR,\n      a22 = cosR + w * (1 - cosR),\n      matrix = [a00, a01, a02, 0, 0, a10, a11, a12, 0, 0, a20, a21, a22, 0, 0, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * Set the contrast matrix, increase the separation between dark and bright\n   * Increase contrast : shadows darker and highlights brighter\n   * Decrease contrast : bring the shadows up and the highlights down\n   * @param amount - value of the contrast (0-1)\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  contrast(amount, multiply) {\n    const v = (amount || 0) + 1,\n      o = -0.5 * (v - 1),\n      matrix = [v, 0, 0, 0, o, 0, v, 0, 0, o, 0, 0, v, 0, o, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * Set the saturation matrix, increase the separation between colors\n   * Increase saturation : increase contrast, brightness, and sharpness\n   * @param amount - The saturation amount (0-1)\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  saturate(amount = 0, multiply) {\n    const x = amount * 2 / 3 + 1,\n      y = (x - 1) * -0.5,\n      matrix = [x, y, y, 0, 0, y, x, y, 0, 0, y, y, x, 0, 0, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /** Desaturate image (remove color) Call the saturate function */\n  desaturate() {\n    this.saturate(-1);\n  }\n  /**\n   * Negative image (inverse of classic rgb matrix)\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  negative(multiply) {\n    const matrix = [-1, 0, 0, 1, 0, 0, -1, 0, 1, 0, 0, 0, -1, 1, 0, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * Sepia image\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  sepia(multiply) {\n    const matrix = [0.393, 0.7689999, 0.18899999, 0, 0, 0.349, 0.6859999, 0.16799999, 0, 0, 0.272, 0.5339999, 0.13099999, 0, 0, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * Color motion picture process invented in 1916 (thanks Dominic Szablewski)\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  technicolor(multiply) {\n    const matrix = [1.9125277891456083, -0.8545344976951645, -0.09155508482755585, 0, 11.793603434377337, -0.3087833385928097, 1.7658908555458428, -0.10601743074722245, 0, -70.35205161461398, -0.231103377548616, -0.7501899197440212, 1.847597816108189, 0, 30.950940869491138, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * Polaroid filter\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  polaroid(multiply) {\n    const matrix = [1.438, -0.062, -0.062, 0, 0, -0.122, 1.378, -0.122, 0, 0, -0.016, -0.016, 1.483, 0, 0, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * Filter who transforms : Red -> Blue and Blue -> Red\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  toBGR(multiply) {\n    const matrix = [0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * Color reversal film introduced by Eastman Kodak in 1935. (thanks Dominic Szablewski)\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  kodachrome(multiply) {\n    const matrix = [1.1285582396593525, -0.3967382283601348, -0.03992559172921793, 0, 63.72958762196502, -0.16404339962244616, 1.0835251566291304, -0.05498805115633132, 0, 24.732407896706203, -0.16786010706155763, -0.5603416277695248, 1.6014850761964943, 0, 35.62982807460946, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * Brown delicious browni filter (thanks Dominic Szablewski)\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  browni(multiply) {\n    const matrix = [0.5997023498159715, 0.34553243048391263, -0.2708298674538042, 0, 47.43192855600873, -0.037703249837783157, 0.8609577587992641, 0.15059552388459913, 0, -36.96841498319127, 0.24113635128153335, -0.07441037908422492, 0.44972182064877153, 0, -7.562075277591283, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * Vintage filter (thanks Dominic Szablewski)\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  vintage(multiply) {\n    const matrix = [0.6279345635605994, 0.3202183420819367, -0.03965408211312453, 0, 9.651285835294123, 0.02578397704808868, 0.6441188644374771, 0.03259127616149294, 0, 7.462829176470591, 0.0466055556782719, -0.0851232987247891, 0.5241648018700465, 0, 5.159190588235296, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * We don't know exactly what it does, kind of gradient map, but funny to play with!\n   * @param desaturation - Tone values.\n   * @param toned - Tone values.\n   * @param lightColor - Tone values, example: `0xFFE580`\n   * @param darkColor - Tone values, example: `0xFFE580`\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  colorTone(desaturation, toned, lightColor, darkColor, multiply) {\n    desaturation = desaturation || 0.2, toned = toned || 0.15, lightColor = lightColor || 16770432, darkColor = darkColor || 3375104;\n    const temp = Color.shared,\n      [lR, lG, lB] = temp.setValue(lightColor).toArray(),\n      [dR, dG, dB] = temp.setValue(darkColor).toArray(),\n      matrix = [0.3, 0.59, 0.11, 0, 0, lR, lG, lB, desaturation, 0, dR, dG, dB, toned, 0, lR - dR, lG - dG, lB - dB, 0, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * Night effect\n   * @param intensity - The intensity of the night effect.\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  night(intensity, multiply) {\n    intensity = intensity || 0.1;\n    const matrix = [intensity * -2, -intensity, 0, 0, 0, -intensity, 0, intensity, 0, 0, 0, intensity, intensity * 2, 0, 0, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * Predator effect\n   *\n   * Erase the current matrix by setting a new indepent one\n   * @param amount - how much the predator feels his future victim\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  predator(amount, multiply) {\n    const matrix = [\n    // row 1\n    11.224130630493164 * amount, -4.794486999511719 * amount, -2.8746118545532227 * amount, 0 * amount, 0.40342438220977783 * amount,\n    // row 2\n    -3.6330697536468506 * amount, 9.193157196044922 * amount, -2.951810836791992 * amount, 0 * amount, -1.316135048866272 * amount,\n    // row 3\n    -3.2184197902679443 * amount, -4.2375030517578125 * amount, 7.476448059082031 * amount, 0 * amount, 0.8044459223747253 * amount,\n    // row 4\n    0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /**\n   * LSD effect\n   *\n   * Multiply the current matrix\n   * @param multiply - if true, current matrix and matrix are multiplied. If false,\n   *  just set the current matrix with @param matrix\n   */\n  lsd(multiply) {\n    const matrix = [2, -0.4, 0.5, 0, 0, -0.5, 2, -0.4, 0, 0, -0.4, -0.5, 3, 0, 0, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, multiply);\n  }\n  /** Erase the current matrix by setting the default one. */\n  reset() {\n    const matrix = [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0];\n    this._loadMatrix(matrix, !1);\n  }\n  /**\n   * The matrix of the color matrix filter\n   * @member {number[]}\n   * @default [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0]\n   */\n  get matrix() {\n    return this.uniforms.m;\n  }\n  set matrix(value) {\n    this.uniforms.m = value;\n  }\n  /**\n   * The opacity value to use when mixing the original and resultant colors.\n   *\n   * When the value is 0, the original color is used without modification.\n   * When the value is 1, the result color is used.\n   * When in the range (0, 1) the color is interpolated between the original and result by this amount.\n   * @default 1\n   */\n  get alpha() {\n    return this.uniforms.uAlpha;\n  }\n  set alpha(value) {\n    this.uniforms.uAlpha = value;\n  }\n}\nColorMatrixFilter.prototype.grayscale = ColorMatrixFilter.prototype.greyscale;\nexport { ColorMatrixFilter };", "map": {"version": 3, "names": ["ColorMatrixFilter", "Filter", "constructor", "uniforms", "m", "Float32Array", "uAlpha", "defaultFilterVertex", "fragment", "alpha", "_loadMatrix", "matrix", "multiply", "newMatrix", "_multiply", "_colorMatrix", "out", "a", "b", "brightness", "tint", "color", "r", "g", "Color", "shared", "setValue", "toArray", "greyscale", "scale", "blackAndWhite", "hue", "rotation", "Math", "PI", "cosR", "cos", "sinR", "sin", "sqrt", "w", "sqrW", "a00", "a01", "a02", "a10", "a11", "a12", "a20", "a21", "a22", "contrast", "amount", "v", "o", "saturate", "x", "y", "desaturate", "negative", "sepia", "technicolor", "polaroid", "toBGR", "kodachrome", "browni", "vintage", "colorTone", "desaturation", "toned", "lightColor", "darkColor", "temp", "lR", "lG", "lB", "dR", "dG", "dB", "night", "intensity", "predator", "lsd", "reset", "value", "prototype", "grayscale"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\filter-color-matrix\\src\\ColorMatrixFilter.ts"], "sourcesContent": ["import { Color, defaultFilterVertex, Filter } from '@pixi/core';\nimport fragment from './colorMatrix.frag';\n\nimport type { utils } from '@pixi/core';\n\nexport type ColorMatrix = utils.ArrayFixed<number, 20>;\n\n/**\n * The ColorMatrixFilter class lets you apply a 5x4 matrix transformation on the RGBA\n * color and alpha values of every pixel on your displayObject to produce a result\n * with a new set of RGBA color and alpha values. It's pretty powerful!\n * @example\n * import { filters } from 'pixi.js';\n *\n * const colorMatrix = new filters.ColorMatrixFilter();\n * container.filters = [colorMatrix];\n * colorMatrix.contrast(2);\n * <AUTHOR> Chenebault <<EMAIL>>\n * @memberof PIXI\n */\nexport class ColorMatrixFilter extends Filter\n{\n    constructor()\n    {\n        const uniforms = {\n            m: new Float32Array([1, 0, 0, 0, 0,\n                0, 1, 0, 0, 0,\n                0, 0, 1, 0, 0,\n                0, 0, 0, 1, 0]),\n            uAlpha: 1,\n        };\n\n        super(defaultFilterVertex, fragment, uniforms);\n\n        this.alpha = 1;\n    }\n\n    /**\n     * Transforms current matrix and set the new one\n     * @param {number[]} matrix - 5x4 matrix\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    private _loadMatrix(matrix: ColorMatrix, multiply = false): void\n    {\n        let newMatrix = matrix;\n\n        if (multiply)\n        {\n            this._multiply(newMatrix, this.uniforms.m, matrix);\n            newMatrix = this._colorMatrix(newMatrix) as any;\n        }\n\n        // set the new matrix\n        this.uniforms.m = newMatrix;\n    }\n\n    /**\n     * Multiplies two mat5's\n     * @private\n     * @param out - 5x4 matrix the receiving matrix\n     * @param a - 5x4 matrix the first operand\n     * @param b - 5x4 matrix the second operand\n     * @returns {number[]} 5x4 matrix\n     */\n    private _multiply(out: ColorMatrix, a: ColorMatrix, b: ColorMatrix): ColorMatrix\n    {\n        // Red Channel\n        out[0] = (a[0] * b[0]) + (a[1] * b[5]) + (a[2] * b[10]) + (a[3] * b[15]);\n        out[1] = (a[0] * b[1]) + (a[1] * b[6]) + (a[2] * b[11]) + (a[3] * b[16]);\n        out[2] = (a[0] * b[2]) + (a[1] * b[7]) + (a[2] * b[12]) + (a[3] * b[17]);\n        out[3] = (a[0] * b[3]) + (a[1] * b[8]) + (a[2] * b[13]) + (a[3] * b[18]);\n        out[4] = (a[0] * b[4]) + (a[1] * b[9]) + (a[2] * b[14]) + (a[3] * b[19]) + a[4];\n\n        // Green Channel\n        out[5] = (a[5] * b[0]) + (a[6] * b[5]) + (a[7] * b[10]) + (a[8] * b[15]);\n        out[6] = (a[5] * b[1]) + (a[6] * b[6]) + (a[7] * b[11]) + (a[8] * b[16]);\n        out[7] = (a[5] * b[2]) + (a[6] * b[7]) + (a[7] * b[12]) + (a[8] * b[17]);\n        out[8] = (a[5] * b[3]) + (a[6] * b[8]) + (a[7] * b[13]) + (a[8] * b[18]);\n        out[9] = (a[5] * b[4]) + (a[6] * b[9]) + (a[7] * b[14]) + (a[8] * b[19]) + a[9];\n\n        // Blue Channel\n        out[10] = (a[10] * b[0]) + (a[11] * b[5]) + (a[12] * b[10]) + (a[13] * b[15]);\n        out[11] = (a[10] * b[1]) + (a[11] * b[6]) + (a[12] * b[11]) + (a[13] * b[16]);\n        out[12] = (a[10] * b[2]) + (a[11] * b[7]) + (a[12] * b[12]) + (a[13] * b[17]);\n        out[13] = (a[10] * b[3]) + (a[11] * b[8]) + (a[12] * b[13]) + (a[13] * b[18]);\n        out[14] = (a[10] * b[4]) + (a[11] * b[9]) + (a[12] * b[14]) + (a[13] * b[19]) + a[14];\n\n        // Alpha Channel\n        out[15] = (a[15] * b[0]) + (a[16] * b[5]) + (a[17] * b[10]) + (a[18] * b[15]);\n        out[16] = (a[15] * b[1]) + (a[16] * b[6]) + (a[17] * b[11]) + (a[18] * b[16]);\n        out[17] = (a[15] * b[2]) + (a[16] * b[7]) + (a[17] * b[12]) + (a[18] * b[17]);\n        out[18] = (a[15] * b[3]) + (a[16] * b[8]) + (a[17] * b[13]) + (a[18] * b[18]);\n        out[19] = (a[15] * b[4]) + (a[16] * b[9]) + (a[17] * b[14]) + (a[18] * b[19]) + a[19];\n\n        return out;\n    }\n\n    /**\n     * Create a Float32 Array and normalize the offset component to 0-1\n     * @param {number[]} matrix - 5x4 matrix\n     * @returns {number[]} 5x4 matrix with all values between 0-1\n     */\n    private _colorMatrix(matrix: ColorMatrix): ColorMatrix\n    {\n        // Create a Float32 Array and normalize the offset component to 0-1\n        const m = new Float32Array(matrix);\n\n        m[4] /= 255;\n        m[9] /= 255;\n        m[14] /= 255;\n        m[19] /= 255;\n\n        return m as any;\n    }\n\n    /**\n     * Adjusts brightness\n     * @param b - value of the brigthness (0-1, where 0 is black)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public brightness(b: number, multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            b, 0, 0, 0, 0,\n            0, b, 0, 0, 0,\n            0, 0, b, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Sets each channel on the diagonal of the color matrix.\n     * This can be used to achieve a tinting effect on Containers similar to the tint field of some\n     * display objects like Sprite, Text, Graphics, and Mesh.\n     * @param color - Color of the tint. This is a hex value.\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public tint(color: number, multiply?: boolean): void\n    {\n        const [r, g, b] = Color.shared.setValue(color).toArray();\n        const matrix: ColorMatrix = [\n            r, 0, 0, 0, 0,\n            0, g, 0, 0, 0,\n            0, 0, b, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Set the matrices in grey scales\n     * @param scale - value of the grey (0-1, where 0 is black)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public greyscale(scale: number, multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            scale, scale, scale, 0, 0,\n            scale, scale, scale, 0, 0,\n            scale, scale, scale, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Americanized alias of greyscale.\n     * @method\n     * @param scale - value of the grey (0-1, where 0 is black)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     * @returns {void}\n     * @see PIXI.ColorMatrixFilter.greyscale\n     */\n    public grayscale!: (scale: number, multiply: boolean) => void;\n\n    /**\n     * Set the black and white matrice.\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public blackAndWhite(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            0.3, 0.6, 0.1, 0, 0,\n            0.3, 0.6, 0.1, 0, 0,\n            0.3, 0.6, 0.1, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Set the hue property of the color\n     * @param rotation - in degrees\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public hue(rotation: number, multiply: boolean): void\n    {\n        rotation = (rotation || 0) / 180 * Math.PI;\n\n        const cosR = Math.cos(rotation);\n        const sinR = Math.sin(rotation);\n        const sqrt = Math.sqrt;\n\n        /* a good approximation for hue rotation\n         This matrix is far better than the versions with magic luminance constants\n         formerly used here, but also used in the starling framework (flash) and known from this\n         old part of the internet: quasimondo.com/archives/000565.php\n\n         This new matrix is based on rgb cube rotation in space. Look here for a more descriptive\n         implementation as a shader not a general matrix:\n         https://github.com/evanw/glfx.js/blob/58841c23919bd59787effc0333a4897b43835412/src/filters/adjust/huesaturation.js\n\n         This is the source for the code:\n         see http://stackoverflow.com/questions/8507885/shift-hue-of-an-rgb-color/8510751#8510751\n         */\n\n        const w = 1 / 3;\n        const sqrW = sqrt(w); // weight is\n\n        const a00 = cosR + ((1.0 - cosR) * w);\n        const a01 = (w * (1.0 - cosR)) - (sqrW * sinR);\n        const a02 = (w * (1.0 - cosR)) + (sqrW * sinR);\n\n        const a10 = (w * (1.0 - cosR)) + (sqrW * sinR);\n        const a11 = cosR + (w * (1.0 - cosR));\n        const a12 = (w * (1.0 - cosR)) - (sqrW * sinR);\n\n        const a20 = (w * (1.0 - cosR)) - (sqrW * sinR);\n        const a21 = (w * (1.0 - cosR)) + (sqrW * sinR);\n        const a22 = cosR + (w * (1.0 - cosR));\n\n        const matrix: ColorMatrix = [\n            a00, a01, a02, 0, 0,\n            a10, a11, a12, 0, 0,\n            a20, a21, a22, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Set the contrast matrix, increase the separation between dark and bright\n     * Increase contrast : shadows darker and highlights brighter\n     * Decrease contrast : bring the shadows up and the highlights down\n     * @param amount - value of the contrast (0-1)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public contrast(amount: number, multiply: boolean): void\n    {\n        const v = (amount || 0) + 1;\n        const o = -0.5 * (v - 1);\n\n        const matrix: ColorMatrix = [\n            v, 0, 0, 0, o,\n            0, v, 0, 0, o,\n            0, 0, v, 0, o,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Set the saturation matrix, increase the separation between colors\n     * Increase saturation : increase contrast, brightness, and sharpness\n     * @param amount - The saturation amount (0-1)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public saturate(amount = 0, multiply?: boolean): void\n    {\n        const x = (amount * 2 / 3) + 1;\n        const y = ((x - 1) * -0.5);\n\n        const matrix: ColorMatrix = [\n            x, y, y, 0, 0,\n            y, x, y, 0, 0,\n            y, y, x, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /** Desaturate image (remove color) Call the saturate function */\n    public desaturate(): void // eslint-disable-line no-unused-vars\n    {\n        this.saturate(-1);\n    }\n\n    /**\n     * Negative image (inverse of classic rgb matrix)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public negative(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            -1, 0, 0, 1, 0,\n            0, -1, 0, 1, 0,\n            0, 0, -1, 1, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Sepia image\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public sepia(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            0.393, 0.7689999, 0.18899999, 0, 0,\n            0.349, 0.6859999, 0.16799999, 0, 0,\n            0.272, 0.5339999, 0.13099999, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Color motion picture process invented in 1916 (thanks Dominic Szablewski)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public technicolor(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            1.9125277891456083, -0.8545344976951645, -0.09155508482755585, 0, 11.793603434377337,\n            -0.3087833385928097, 1.7658908555458428, -0.10601743074722245, 0, -70.35205161461398,\n            -0.231103377548616, -0.7501899197440212, 1.847597816108189, 0, 30.950940869491138,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Polaroid filter\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public polaroid(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            1.438, -0.062, -0.062, 0, 0,\n            -0.122, 1.378, -0.122, 0, 0,\n            -0.016, -0.016, 1.483, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Filter who transforms : Red -> Blue and Blue -> Red\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public toBGR(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            0, 0, 1, 0, 0,\n            0, 1, 0, 0, 0,\n            1, 0, 0, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Color reversal film introduced by Eastman Kodak in 1935. (thanks Dominic Szablewski)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public kodachrome(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            1.1285582396593525, -0.3967382283601348, -0.03992559172921793, 0, 63.72958762196502,\n            -0.16404339962244616, 1.0835251566291304, -0.05498805115633132, 0, 24.732407896706203,\n            -0.16786010706155763, -0.5603416277695248, 1.6014850761964943, 0, 35.62982807460946,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Brown delicious browni filter (thanks Dominic Szablewski)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public browni(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            0.5997023498159715, 0.34553243048391263, -0.2708298674538042, 0, 47.43192855600873,\n            -0.037703249837783157, 0.8609577587992641, 0.15059552388459913, 0, -36.96841498319127,\n            0.24113635128153335, -0.07441037908422492, 0.44972182064877153, 0, -7.562075277591283,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Vintage filter (thanks Dominic Szablewski)\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public vintage(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            0.6279345635605994, 0.3202183420819367, -0.03965408211312453, 0, 9.651285835294123,\n            0.02578397704808868, 0.6441188644374771, 0.03259127616149294, 0, 7.462829176470591,\n            0.0466055556782719, -0.0851232987247891, 0.5241648018700465, 0, 5.159190588235296,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * We don't know exactly what it does, kind of gradient map, but funny to play with!\n     * @param desaturation - Tone values.\n     * @param toned - Tone values.\n     * @param lightColor - Tone values, example: `0xFFE580`\n     * @param darkColor - Tone values, example: `0xFFE580`\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public colorTone(desaturation: number, toned: number, lightColor: number, darkColor: number, multiply: boolean): void\n    {\n        desaturation = desaturation || 0.2;\n        toned = toned || 0.15;\n        lightColor = lightColor || 0xFFE580;\n        darkColor = darkColor || 0x338000;\n\n        const temp = Color.shared;\n        const [lR, lG, lB] = temp.setValue(lightColor).toArray();\n        const [dR, dG, dB] = temp.setValue(darkColor).toArray();\n\n        const matrix: ColorMatrix = [\n            0.3, 0.59, 0.11, 0, 0,\n            lR, lG, lB, desaturation, 0,\n            dR, dG, dB, toned, 0,\n            lR - dR, lG - dG, lB - dB, 0, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Night effect\n     * @param intensity - The intensity of the night effect.\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public night(intensity: number, multiply: boolean): void\n    {\n        intensity = intensity || 0.1;\n\n        const matrix: ColorMatrix = [\n            intensity * (-2.0), -intensity, 0, 0, 0,\n            -intensity, 0, intensity, 0, 0,\n            0, intensity, intensity * 2.0, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * Predator effect\n     *\n     * Erase the current matrix by setting a new indepent one\n     * @param amount - how much the predator feels his future victim\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public predator(amount: number, multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            // row 1\n            11.224130630493164 * amount,\n            -4.794486999511719 * amount,\n            -2.8746118545532227 * amount,\n            0 * amount,\n            0.40342438220977783 * amount,\n            // row 2\n            -3.6330697536468506 * amount,\n            9.193157196044922 * amount,\n            -2.951810836791992 * amount,\n            0 * amount,\n            -1.316135048866272 * amount,\n            // row 3\n            -3.2184197902679443 * amount,\n            -4.2375030517578125 * amount,\n            7.476448059082031 * amount,\n            0 * amount,\n            0.8044459223747253 * amount,\n            // row 4\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /**\n     * LSD effect\n     *\n     * Multiply the current matrix\n     * @param multiply - if true, current matrix and matrix are multiplied. If false,\n     *  just set the current matrix with @param matrix\n     */\n    public lsd(multiply: boolean): void\n    {\n        const matrix: ColorMatrix = [\n            2, -0.4, 0.5, 0, 0,\n            -0.5, 2, -0.4, 0, 0,\n            -0.4, -0.5, 3, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, multiply);\n    }\n\n    /** Erase the current matrix by setting the default one. */\n    public reset(): void\n    {\n        const matrix: ColorMatrix = [\n            1, 0, 0, 0, 0,\n            0, 1, 0, 0, 0,\n            0, 0, 1, 0, 0,\n            0, 0, 0, 1, 0,\n        ];\n\n        this._loadMatrix(matrix, false);\n    }\n\n    /**\n     * The matrix of the color matrix filter\n     * @member {number[]}\n     * @default [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0]\n     */\n    get matrix(): ColorMatrix\n    {\n        return this.uniforms.m;\n    }\n\n    set matrix(value: ColorMatrix)\n    {\n        this.uniforms.m = value;\n    }\n\n    /**\n     * The opacity value to use when mixing the original and resultant colors.\n     *\n     * When the value is 0, the original color is used without modification.\n     * When the value is 1, the result color is used.\n     * When in the range (0, 1) the color is interpolated between the original and result by this amount.\n     * @default 1\n     */\n    get alpha(): number\n    {\n        return this.uniforms.uAlpha;\n    }\n\n    set alpha(value: number)\n    {\n        this.uniforms.uAlpha = value;\n    }\n}\n\n// Americanized alias\nColorMatrixFilter.prototype.grayscale = ColorMatrixFilter.prototype.greyscale;\n"], "mappings": ";;AAoBO,MAAMA,iBAAA,SAA0BC,MAAA,CACvC;EACIC,YAAA,EACA;IACI,MAAMC,QAAA,GAAW;MACbC,CAAA,EAAG,IAAIC,YAAA,CAAa,CAAC,GAAG,GAAG,GAAG,GAAG,GAC7B,GAAG,GAAG,GAAG,GAAG,GACZ,GAAG,GAAG,GAAG,GAAG,GACZ,GAAG,GAAG,GAAG,GAAG,EAAE;MAClBC,MAAA,EAAQ;IAAA;IAGN,MAAAC,mBAAA,EAAqBC,QAAA,EAAUL,QAAQ,GAE7C,KAAKM,KAAA,GAAQ;EACjB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQQC,YAAYC,MAAA,EAAqBC,QAAA,GAAW,IACpD;IACI,IAAIC,SAAA,GAAYF,MAAA;IAEZC,QAAA,KAEA,KAAKE,SAAA,CAAUD,SAAA,EAAW,KAAKV,QAAA,CAASC,CAAA,EAAGO,MAAM,GACjDE,SAAA,GAAY,KAAKE,YAAA,CAAaF,SAAS,IAI3C,KAAKV,QAAA,CAASC,CAAA,GAAIS,SAAA;EACtB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUQC,UAAUE,GAAA,EAAkBC,CAAA,EAAgBC,CAAA,EACpD;IAEI,OAAAF,GAAA,CAAI,CAAC,IAAKC,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,GACtEF,GAAA,CAAI,CAAC,IAAKC,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,GACtEF,GAAA,CAAI,CAAC,IAAKC,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,GACtEF,GAAA,CAAI,CAAC,IAAKC,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,GACtEF,GAAA,CAAI,CAAC,IAAKC,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,IAAKD,CAAA,CAAE,CAAC,GAG9ED,GAAA,CAAI,CAAC,IAAKC,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,GACtEF,GAAA,CAAI,CAAC,IAAKC,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,GACtEF,GAAA,CAAI,CAAC,IAAKC,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,GACtEF,GAAA,CAAI,CAAC,IAAKC,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,GACtEF,GAAA,CAAI,CAAC,IAAKC,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,CAAC,IAAIC,CAAA,CAAE,EAAE,IAAKD,CAAA,CAAE,CAAC,GAG9ED,GAAA,CAAI,EAAE,IAAKC,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,GAC3EF,GAAA,CAAI,EAAE,IAAKC,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,GAC3EF,GAAA,CAAI,EAAE,IAAKC,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,GAC3EF,GAAA,CAAI,EAAE,IAAKC,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,GAC3EF,GAAA,CAAI,EAAE,IAAKC,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,IAAKD,CAAA,CAAE,EAAE,GAGpFD,GAAA,CAAI,EAAE,IAAKC,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,GAC3EF,GAAA,CAAI,EAAE,IAAKC,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,GAC3EF,GAAA,CAAI,EAAE,IAAKC,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,GAC3EF,GAAA,CAAI,EAAE,IAAKC,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,GAC3EF,GAAA,CAAI,EAAE,IAAKC,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,CAAC,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,IAAMD,CAAA,CAAE,EAAE,IAAIC,CAAA,CAAE,EAAE,IAAKD,CAAA,CAAE,EAAE,GAE7ED,GAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOQD,aAAaJ,MAAA,EACrB;IAEU,MAAAP,CAAA,GAAI,IAAIC,YAAA,CAAaM,MAAM;IAEjC,OAAAP,CAAA,CAAE,CAAC,KAAK,KACRA,CAAA,CAAE,CAAC,KAAK,KACRA,CAAA,CAAE,EAAE,KAAK,KACTA,CAAA,CAAE,EAAE,KAAK,KAEFA,CAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQOe,WAAWD,CAAA,EAAWN,QAAA,EAC7B;IACI,MAAMD,MAAA,GAAsB,CACxBO,CAAA,EAAG,GAAG,GAAG,GAAG,GACZ,GAAGA,CAAA,EAAG,GAAG,GAAG,GACZ,GAAG,GAAGA,CAAA,EAAG,GAAG,GACZ,GAAG,GAAG,GAAG,GAAG;IAGX,KAAAR,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUOQ,KAAKC,KAAA,EAAeT,QAAA,EAC3B;IACI,MAAM,CAACU,CAAA,EAAGC,CAAA,EAAGL,CAAC,IAAIM,KAAA,CAAMC,MAAA,CAAOC,QAAA,CAASL,KAAK,EAAEM,OAAA,CAAQ;MACjDhB,MAAA,GAAsB,CACxBW,CAAA,EAAG,GAAG,GAAG,GAAG,GACZ,GAAGC,CAAA,EAAG,GAAG,GAAG,GACZ,GAAG,GAAGL,CAAA,EAAG,GAAG,GACZ,GAAG,GAAG,GAAG,GAAG;IAGX,KAAAR,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQOgB,UAAUC,KAAA,EAAejB,QAAA,EAChC;IACI,MAAMD,MAAA,GAAsB,CACxBkB,KAAA,EAAOA,KAAA,EAAOA,KAAA,EAAO,GAAG,GACxBA,KAAA,EAAOA,KAAA,EAAOA,KAAA,EAAO,GAAG,GACxBA,KAAA,EAAOA,KAAA,EAAOA,KAAA,EAAO,GAAG,GACxB,GAAG,GAAG,GAAG,GAAG;IAGX,KAAAnB,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;EAkBOkB,cAAclB,QAAA,EACrB;IACI,MAAMD,MAAA,GAAsB,CACxB,KAAK,KAAK,KAAK,GAAG,GAClB,KAAK,KAAK,KAAK,GAAG,GAClB,KAAK,KAAK,KAAK,GAAG,GAClB,GAAG,GAAG,GAAG,GAAG;IAGX,KAAAD,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQOmB,IAAIC,QAAA,EAAkBpB,QAAA,EAC7B;IACgBoB,QAAA,IAAAA,QAAA,IAAY,KAAK,MAAMC,IAAA,CAAKC,EAAA;IAExC,MAAMC,IAAA,GAAOF,IAAA,CAAKG,GAAA,CAAIJ,QAAQ;MACxBK,IAAA,GAAOJ,IAAA,CAAKK,GAAA,CAAIN,QAAQ;MACxBO,IAAA,GAAON,IAAA,CAAKM,IAAA;MAeZC,CAAA,GAAI,IAAI;MACRC,IAAA,GAAOF,IAAA,CAAKC,CAAC;MAEbE,GAAA,GAAMP,IAAA,IAAS,IAAMA,IAAA,IAAQK,CAAA;MAC7BG,GAAA,GAAOH,CAAA,IAAK,IAAML,IAAA,IAAUM,IAAA,GAAOJ,IAAA;MACnCO,GAAA,GAAOJ,CAAA,IAAK,IAAML,IAAA,IAAUM,IAAA,GAAOJ,IAAA;MAEnCQ,GAAA,GAAOL,CAAA,IAAK,IAAML,IAAA,IAAUM,IAAA,GAAOJ,IAAA;MACnCS,GAAA,GAAMX,IAAA,GAAQK,CAAA,IAAK,IAAML,IAAA;MACzBY,GAAA,GAAOP,CAAA,IAAK,IAAML,IAAA,IAAUM,IAAA,GAAOJ,IAAA;MAEnCW,GAAA,GAAOR,CAAA,IAAK,IAAML,IAAA,IAAUM,IAAA,GAAOJ,IAAA;MACnCY,GAAA,GAAOT,CAAA,IAAK,IAAML,IAAA,IAAUM,IAAA,GAAOJ,IAAA;MACnCa,GAAA,GAAMf,IAAA,GAAQK,CAAA,IAAK,IAAML,IAAA;MAEzBxB,MAAA,GAAsB,CACxB+B,GAAA,EAAKC,GAAA,EAAKC,GAAA,EAAK,GAAG,GAClBC,GAAA,EAAKC,GAAA,EAAKC,GAAA,EAAK,GAAG,GAClBC,GAAA,EAAKC,GAAA,EAAKC,GAAA,EAAK,GAAG,GAClB,GAAG,GAAG,GAAG,GAAG;IAGX,KAAAxC,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUOuC,SAASC,MAAA,EAAgBxC,QAAA,EAChC;IACU,MAAAyC,CAAA,IAAKD,MAAA,IAAU,KAAK;MACpBE,CAAA,GAAI,QAAQD,CAAA,GAAI;MAEhB1C,MAAA,GAAsB,CACxB0C,CAAA,EAAG,GAAG,GAAG,GAAGC,CAAA,EACZ,GAAGD,CAAA,EAAG,GAAG,GAAGC,CAAA,EACZ,GAAG,GAAGD,CAAA,EAAG,GAAGC,CAAA,EACZ,GAAG,GAAG,GAAG,GAAG;IAGX,KAAA5C,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASO2C,SAASH,MAAA,GAAS,GAAGxC,QAAA,EAC5B;IACU,MAAA4C,CAAA,GAAKJ,MAAA,GAAS,IAAI,IAAK;MACvBK,CAAA,IAAMD,CAAA,GAAI,KAAK;MAEf7C,MAAA,GAAsB,CACxB6C,CAAA,EAAGC,CAAA,EAAGA,CAAA,EAAG,GAAG,GACZA,CAAA,EAAGD,CAAA,EAAGC,CAAA,EAAG,GAAG,GACZA,CAAA,EAAGA,CAAA,EAAGD,CAAA,EAAG,GAAG,GACZ,GAAG,GAAG,GAAG,GAAG;IAGX,KAAA9C,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;EAGO8C,WAAA,EACP;IACI,KAAKH,QAAA,CAAS,EAAE;EACpB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOOI,SAAS/C,QAAA,EAChB;IACI,MAAMD,MAAA,GAAsB,CACxB,IAAI,GAAG,GAAG,GAAG,GACb,GAAG,IAAI,GAAG,GAAG,GACb,GAAG,GAAG,IAAI,GAAG,GACb,GAAG,GAAG,GAAG,GAAG;IAGX,KAAAD,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOOgD,MAAMhD,QAAA,EACb;IACI,MAAMD,MAAA,GAAsB,CACxB,OAAO,WAAW,YAAY,GAAG,GACjC,OAAO,WAAW,YAAY,GAAG,GACjC,OAAO,WAAW,YAAY,GAAG,GACjC,GAAG,GAAG,GAAG,GAAG;IAGX,KAAAD,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOOiD,YAAYjD,QAAA,EACnB;IACI,MAAMD,MAAA,GAAsB,CACxB,oBAAoB,qBAAqB,sBAAsB,GAAG,oBAClE,qBAAqB,oBAAoB,sBAAsB,GAAG,oBAClE,oBAAoB,qBAAqB,mBAAmB,GAAG,oBAC/D,GAAG,GAAG,GAAG,GAAG;IAGX,KAAAD,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOOkD,SAASlD,QAAA,EAChB;IACI,MAAMD,MAAA,GAAsB,CACxB,OAAO,QAAQ,QAAQ,GAAG,GAC1B,QAAQ,OAAO,QAAQ,GAAG,GAC1B,QAAQ,QAAQ,OAAO,GAAG,GAC1B,GAAG,GAAG,GAAG,GAAG;IAGX,KAAAD,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOOmD,MAAMnD,QAAA,EACb;IACI,MAAMD,MAAA,GAAsB,CACxB,GAAG,GAAG,GAAG,GAAG,GACZ,GAAG,GAAG,GAAG,GAAG,GACZ,GAAG,GAAG,GAAG,GAAG,GACZ,GAAG,GAAG,GAAG,GAAG;IAGX,KAAAD,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOOoD,WAAWpD,QAAA,EAClB;IACI,MAAMD,MAAA,GAAsB,CACxB,oBAAoB,qBAAqB,sBAAsB,GAAG,mBAClE,sBAAsB,oBAAoB,sBAAsB,GAAG,oBACnE,sBAAsB,qBAAqB,oBAAoB,GAAG,mBAClE,GAAG,GAAG,GAAG,GAAG;IAGX,KAAAD,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOOqD,OAAOrD,QAAA,EACd;IACI,MAAMD,MAAA,GAAsB,CACxB,oBAAoB,qBAAqB,qBAAqB,GAAG,mBACjE,uBAAuB,oBAAoB,qBAAqB,GAAG,oBACnE,qBAAqB,sBAAsB,qBAAqB,GAAG,oBACnE,GAAG,GAAG,GAAG,GAAG;IAGX,KAAAD,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOOsD,QAAQtD,QAAA,EACf;IACI,MAAMD,MAAA,GAAsB,CACxB,oBAAoB,oBAAoB,sBAAsB,GAAG,mBACjE,qBAAqB,oBAAoB,qBAAqB,GAAG,mBACjE,oBAAoB,qBAAqB,oBAAoB,GAAG,mBAChE,GAAG,GAAG,GAAG,GAAG;IAGX,KAAAD,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWOuD,UAAUC,YAAA,EAAsBC,KAAA,EAAeC,UAAA,EAAoBC,SAAA,EAAmB3D,QAAA,EAC7F;IACmBwD,YAAA,GAAAA,YAAA,IAAgB,KAC/BC,KAAA,GAAQA,KAAA,IAAS,MACjBC,UAAA,GAAaA,UAAA,IAAc,UAC3BC,SAAA,GAAYA,SAAA,IAAa;IAEnB,MAAAC,IAAA,GAAOhD,KAAA,CAAMC,MAAA;MACb,CAACgD,EAAA,EAAIC,EAAA,EAAIC,EAAE,IAAIH,IAAA,CAAK9C,QAAA,CAAS4C,UAAU,EAAE3C,OAAA,CACzC;MAAA,CAACiD,EAAA,EAAIC,EAAA,EAAIC,EAAE,IAAIN,IAAA,CAAK9C,QAAA,CAAS6C,SAAS,EAAE5C,OAAA,CAAQ;MAEhDhB,MAAA,GAAsB,CACxB,KAAK,MAAM,MAAM,GAAG,GACpB8D,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIP,YAAA,EAAc,GAC1BQ,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIT,KAAA,EAAO,GACnBI,EAAA,GAAKG,EAAA,EAAIF,EAAA,GAAKG,EAAA,EAAIF,EAAA,GAAKG,EAAA,EAAI,GAAG;IAG7B,KAAApE,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQOmE,MAAMC,SAAA,EAAmBpE,QAAA,EAChC;IACIoE,SAAA,GAAYA,SAAA,IAAa;IAEzB,MAAMrE,MAAA,GAAsB,CACxBqE,SAAA,GAAa,IAAO,CAACA,SAAA,EAAW,GAAG,GAAG,GACtC,CAACA,SAAA,EAAW,GAAGA,SAAA,EAAW,GAAG,GAC7B,GAAGA,SAAA,EAAWA,SAAA,GAAY,GAAK,GAAG,GAClC,GAAG,GAAG,GAAG,GAAG;IAGX,KAAAtE,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUOqE,SAAS7B,MAAA,EAAgBxC,QAAA,EAChC;IACI,MAAMD,MAAA,GAAsB;IAAA;IAExB,qBAAqByC,MAAA,EACrB,qBAAqBA,MAAA,EACrB,sBAAsBA,MAAA,EACtB,IAAIA,MAAA,EACJ,sBAAsBA,MAAA;IAAA;IAEtB,sBAAsBA,MAAA,EACtB,oBAAoBA,MAAA,EACpB,qBAAqBA,MAAA,EACrB,IAAIA,MAAA,EACJ,qBAAqBA,MAAA;IAAA;IAErB,sBAAsBA,MAAA,EACtB,sBAAsBA,MAAA,EACtB,oBAAoBA,MAAA,EACpB,IAAIA,MAAA,EACJ,qBAAqBA,MAAA;IAAA;IAErB,GAAG,GAAG,GAAG,GAAG;IAGX,KAAA1C,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASOsE,IAAItE,QAAA,EACX;IACI,MAAMD,MAAA,GAAsB,CACxB,GAAG,MAAM,KAAK,GAAG,GACjB,MAAM,GAAG,MAAM,GAAG,GAClB,MAAM,MAAM,GAAG,GAAG,GAClB,GAAG,GAAG,GAAG,GAAG;IAGX,KAAAD,WAAA,CAAYC,MAAA,EAAQC,QAAQ;EACrC;EAAA;EAGOuE,MAAA,EACP;IACI,MAAMxE,MAAA,GAAsB,CACxB,GAAG,GAAG,GAAG,GAAG,GACZ,GAAG,GAAG,GAAG,GAAG,GACZ,GAAG,GAAG,GAAG,GAAG,GACZ,GAAG,GAAG,GAAG,GAAG;IAGX,KAAAD,WAAA,CAAYC,MAAA,EAAQ,EAAK;EAClC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,IAAIA,OAAA,EACJ;IACI,OAAO,KAAKR,QAAA,CAASC,CAAA;EACzB;EAEA,IAAIO,OAAOyE,KAAA,EACX;IACI,KAAKjF,QAAA,CAASC,CAAA,GAAIgF,KAAA;EACtB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUA,IAAI3E,MAAA,EACJ;IACI,OAAO,KAAKN,QAAA,CAASG,MAAA;EACzB;EAEA,IAAIG,MAAM2E,KAAA,EACV;IACI,KAAKjF,QAAA,CAASG,MAAA,GAAS8E,KAAA;EAC3B;AACJ;AAGApF,iBAAA,CAAkBqF,SAAA,CAAUC,SAAA,GAAYtF,iBAAA,CAAkBqF,SAAA,CAAUzD,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}