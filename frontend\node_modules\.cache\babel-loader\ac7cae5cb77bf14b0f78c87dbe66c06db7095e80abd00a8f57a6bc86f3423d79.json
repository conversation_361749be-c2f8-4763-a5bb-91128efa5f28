{"ast": null, "code": "import { ExtensionType, settings, extensions } from \"@pixi/core\";\nimport { checkDataUrl } from \"../../utils/checkDataUrl.mjs\";\nimport { checkExtension } from \"../../utils/checkExtension.mjs\";\nimport { LoaderParserPriority } from \"./LoaderParser.mjs\";\nconst validJSONExtension = \".json\",\n  validJSONMIME = \"application/json\",\n  loadJson = {\n    extension: {\n      type: ExtensionType.LoadParser,\n      priority: LoaderParserPriority.Low\n    },\n    name: \"loadJson\",\n    test(url) {\n      return checkDataUrl(url, validJSONMIME) || checkExtension(url, validJSONExtension);\n    },\n    async load(url) {\n      return await (await settings.ADAPTER.fetch(url)).json();\n    }\n  };\nextensions.add(loadJson);\nexport { loadJson };", "map": {"version": 3, "names": ["validJSONExtension", "validJSONMIME", "loadJson", "extension", "type", "ExtensionType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "priority", "LoaderParserPriority", "Low", "name", "test", "url", "checkDataUrl", "checkExtension", "load", "settings", "ADAPTER", "fetch", "json", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\assets\\src\\loader\\parsers\\loadJson.ts"], "sourcesContent": ["import { extensions, ExtensionType, settings } from '@pixi/core';\nimport { checkDataUrl } from '../../utils/checkDataUrl';\nimport { checkExtension } from '../../utils/checkExtension';\nimport { LoaderParserPriority } from './LoaderParser';\n\nimport type { LoaderParser } from './LoaderParser';\n\nconst validJSONExtension = '.json';\nconst validJSONMIME = 'application/json';\n\n/** simple loader plugin for loading json data */\nexport const loadJson = {\n    extension: {\n        type: ExtensionType.LoadParser,\n        priority: LoaderParserPriority.Low,\n    },\n\n    name: 'loadJson',\n\n    test(url: string): boolean\n    {\n        return checkDataUrl(url, validJSONMIME) || checkExtension(url, validJSONExtension);\n    },\n\n    async load<T>(url: string): Promise<T>\n    {\n        const response = await settings.ADAPTER.fetch(url);\n\n        const json = await response.json();\n\n        return json as T;\n    },\n} as LoaderParser;\n\nextensions.add(loadJson);\n"], "mappings": ";;;;AAOA,MAAMA,kBAAA,GAAqB;EACrBC,aAAA,GAAgB;EAGTC,QAAA,GAAW;IACpBC,SAAA,EAAW;MACPC,IAAA,EAAMC,aAAA,CAAcC,UAAA;MACpBC,QAAA,EAAUC,oBAAA,CAAqBC;IACnC;IAEAC,IAAA,EAAM;IAENC,KAAKC,GAAA,EACL;MACI,OAAOC,YAAA,CAAaD,GAAA,EAAKX,aAAa,KAAKa,cAAA,CAAeF,GAAA,EAAKZ,kBAAkB;IACrF;IAEA,MAAMe,KAAQH,GAAA,EACd;MAKI,OAFa,OAFI,MAAMI,QAAA,CAASC,OAAA,CAAQC,KAAA,CAAMN,GAAG,GAErBO,IAAA;IAGhC;EACJ;AAEAC,UAAA,CAAWC,GAAA,CAAInB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}