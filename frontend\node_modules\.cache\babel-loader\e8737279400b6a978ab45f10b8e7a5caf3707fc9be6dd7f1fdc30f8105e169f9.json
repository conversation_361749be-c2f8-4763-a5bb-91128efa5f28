{"ast": null, "code": "import { SHAPES } from \"../const.mjs\";\nimport { Rectangle } from \"./Rectangle.mjs\";\nclass Circle {\n  /**\n   * @param x - The X coordinate of the center of this circle\n   * @param y - The Y coordinate of the center of this circle\n   * @param radius - The radius of the circle\n   */\n  constructor(x = 0, y = 0, radius = 0) {\n    this.x = x, this.y = y, this.radius = radius, this.type = SHAPES.CIRC;\n  }\n  /**\n   * Creates a clone of this Circle instance\n   * @returns A copy of the Circle\n   */\n  clone() {\n    return new Circle(this.x, this.y, this.radius);\n  }\n  /**\n   * Checks whether the x and y coordinates given are contained within this circle\n   * @param x - The X coordinate of the point to test\n   * @param y - The Y coordinate of the point to test\n   * @returns Whether the x/y coordinates are within this Circle\n   */\n  contains(x, y) {\n    if (this.radius <= 0) return !1;\n    const r2 = this.radius * this.radius;\n    let dx = this.x - x,\n      dy = this.y - y;\n    return dx *= dx, dy *= dy, dx + dy <= r2;\n  }\n  /**\n   * Returns the framing rectangle of the circle as a Rectangle object\n   * @returns The framing rectangle\n   */\n  getBounds() {\n    return new Rectangle(this.x - this.radius, this.y - this.radius, this.radius * 2, this.radius * 2);\n  }\n}\nCircle.prototype.toString = function () {\n  return `[@pixi/math:Circle x=${this.x} y=${this.y} radius=${this.radius}]`;\n};\nexport { Circle };", "map": {"version": 3, "names": ["Circle", "constructor", "x", "y", "radius", "type", "SHAPES", "CIRC", "clone", "contains", "r2", "dx", "dy", "getBounds", "Rectangle", "prototype", "toString"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\math\\src\\shapes\\Circle.ts"], "sourcesContent": ["import { SHAPES } from './../const';\nimport { Rectangle } from './Rectangle';\n\n/**\n * The Circle object is used to help draw graphics and can also be used to specify a hit area for displayObjects.\n * @memberof PIXI\n */\nexport class Circle\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n\n    /** @default 0 */\n    public radius: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.CIRC\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.CIRC;\n\n    /**\n     * @param x - The X coordinate of the center of this circle\n     * @param y - The Y coordinate of the center of this circle\n     * @param radius - The radius of the circle\n     */\n    constructor(x = 0, y = 0, radius = 0)\n    {\n        this.x = x;\n        this.y = y;\n        this.radius = radius;\n\n        this.type = SHAPES.CIRC;\n    }\n\n    /**\n     * Creates a clone of this Circle instance\n     * @returns A copy of the Circle\n     */\n    clone(): Circle\n    {\n        return new Circle(this.x, this.y, this.radius);\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this circle\n     * @param x - The X coordinate of the point to test\n     * @param y - The Y coordinate of the point to test\n     * @returns Whether the x/y coordinates are within this Circle\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.radius <= 0)\n        {\n            return false;\n        }\n\n        const r2 = this.radius * this.radius;\n        let dx = (this.x - x);\n        let dy = (this.y - y);\n\n        dx *= dx;\n        dy *= dy;\n\n        return (dx + dy <= r2);\n    }\n\n    /**\n     * Returns the framing rectangle of the circle as a Rectangle object\n     * @returns The framing rectangle\n     */\n    getBounds(): Rectangle\n    {\n        return new Rectangle(this.x - this.radius, this.y - this.radius, this.radius * 2, this.radius * 2);\n    }\n}\n\nif (process.env.DEBUG)\n{\n    Circle.prototype.toString = function toString(): string\n    {\n        return `[@pixi/math:Circle x=${this.x} y=${this.y} radius=${this.radius}]`;\n    };\n}\n"], "mappings": ";;AAOO,MAAMA,MAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;EAsBIC,YAAYC,CAAA,GAAI,GAAGC,CAAA,GAAI,GAAGC,MAAA,GAAS,GACnC;IACS,KAAAF,CAAA,GAAIA,CAAA,EACT,KAAKC,CAAA,GAAIA,CAAA,EACT,KAAKC,MAAA,GAASA,MAAA,EAEd,KAAKC,IAAA,GAAOC,MAAA,CAAOC,IAAA;EACvB;EAAA;AAAA;AAAA;AAAA;EAMAC,MAAA,EACA;IACI,OAAO,IAAIR,MAAA,CAAO,KAAKE,CAAA,EAAG,KAAKC,CAAA,EAAG,KAAKC,MAAM;EACjD;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAK,SAASP,CAAA,EAAWC,CAAA,EACpB;IACI,IAAI,KAAKC,MAAA,IAAU,GAER;IAGL,MAAAM,EAAA,GAAK,KAAKN,MAAA,GAAS,KAAKA,MAAA;IAC9B,IAAIO,EAAA,GAAM,KAAKT,CAAA,GAAIA,CAAA;MACfU,EAAA,GAAM,KAAKT,CAAA,GAAIA,CAAA;IAEnB,OAAAQ,EAAA,IAAMA,EAAA,EACNC,EAAA,IAAMA,EAAA,EAEED,EAAA,GAAKC,EAAA,IAAMF,EAAA;EACvB;EAAA;AAAA;AAAA;AAAA;EAMAG,UAAA,EACA;IACI,OAAO,IAAIC,SAAA,CAAU,KAAKZ,CAAA,GAAI,KAAKE,MAAA,EAAQ,KAAKD,CAAA,GAAI,KAAKC,MAAA,EAAQ,KAAKA,MAAA,GAAS,GAAG,KAAKA,MAAA,GAAS,CAAC;EACrG;AACJ;AAIIJ,MAAA,CAAOe,SAAA,CAAUC,QAAA,GAAW,YAC5B;EACW,+BAAwB,KAAKd,CAAC,MAAM,KAAKC,CAAC,WAAW,KAAKC,MAAM;AAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}