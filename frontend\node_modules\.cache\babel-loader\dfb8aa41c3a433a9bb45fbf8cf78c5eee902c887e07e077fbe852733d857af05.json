{"ast": null, "code": "import { Texture, Ticker, UPDATE_PRIORITY } from \"@pixi/core\";\nimport { Sprite } from \"@pixi/sprite\";\nclass AnimatedSprite extends Sprite {\n  /**\n   * @param textures - An array of {@link PIXI.Texture} or frame\n   *  objects that make up the animation.\n   * @param {boolean} [autoUpdate=true] - Whether to use Ticker.shared to auto update animation time.\n   */\n  constructor(textures, autoUpdate = !0) {\n    super(textures[0] instanceof Texture ? textures[0] : textures[0].texture), this._textures = null, this._durations = null, this._autoUpdate = autoUpdate, this._isConnectedToTicker = !1, this.animationSpeed = 1, this.loop = !0, this.updateAnchor = !1, this.onComplete = null, this.onFrameChange = null, this.onLoop = null, this._currentTime = 0, this._playing = !1, this._previousFrame = null, this.textures = textures;\n  }\n  /** Stops the AnimatedSprite. */\n  stop() {\n    this._playing && (this._playing = !1, this._autoUpdate && this._isConnectedToTicker && (Ticker.shared.remove(this.update, this), this._isConnectedToTicker = !1));\n  }\n  /** Plays the AnimatedSprite. */\n  play() {\n    this._playing || (this._playing = !0, this._autoUpdate && !this._isConnectedToTicker && (Ticker.shared.add(this.update, this, UPDATE_PRIORITY.HIGH), this._isConnectedToTicker = !0));\n  }\n  /**\n   * Stops the AnimatedSprite and goes to a specific frame.\n   * @param frameNumber - Frame index to stop at.\n   */\n  gotoAndStop(frameNumber) {\n    this.stop(), this.currentFrame = frameNumber;\n  }\n  /**\n   * Goes to a specific frame and begins playing the AnimatedSprite.\n   * @param frameNumber - Frame index to start at.\n   */\n  gotoAndPlay(frameNumber) {\n    this.currentFrame = frameNumber, this.play();\n  }\n  /**\n   * Updates the object transform for rendering.\n   * @param deltaTime - Time since last tick.\n   */\n  update(deltaTime) {\n    if (!this._playing) return;\n    const elapsed = this.animationSpeed * deltaTime,\n      previousFrame = this.currentFrame;\n    if (this._durations !== null) {\n      let lag = this._currentTime % 1 * this._durations[this.currentFrame];\n      for (lag += elapsed / 60 * 1e3; lag < 0;) this._currentTime--, lag += this._durations[this.currentFrame];\n      const sign = Math.sign(this.animationSpeed * deltaTime);\n      for (this._currentTime = Math.floor(this._currentTime); lag >= this._durations[this.currentFrame];) lag -= this._durations[this.currentFrame] * sign, this._currentTime += sign;\n      this._currentTime += lag / this._durations[this.currentFrame];\n    } else this._currentTime += elapsed;\n    this._currentTime < 0 && !this.loop ? (this.gotoAndStop(0), this.onComplete && this.onComplete()) : this._currentTime >= this._textures.length && !this.loop ? (this.gotoAndStop(this._textures.length - 1), this.onComplete && this.onComplete()) : previousFrame !== this.currentFrame && (this.loop && this.onLoop && (this.animationSpeed > 0 && this.currentFrame < previousFrame || this.animationSpeed < 0 && this.currentFrame > previousFrame) && this.onLoop(), this.updateTexture());\n  }\n  /** Updates the displayed texture to match the current frame index. */\n  updateTexture() {\n    const currentFrame = this.currentFrame;\n    this._previousFrame !== currentFrame && (this._previousFrame = currentFrame, this._texture = this._textures[currentFrame], this._textureID = -1, this._textureTrimmedID = -1, this._cachedTint = 16777215, this.uvs = this._texture._uvs.uvsFloat32, this.updateAnchor && this._anchor.copyFrom(this._texture.defaultAnchor), this.onFrameChange && this.onFrameChange(this.currentFrame));\n  }\n  /**\n   * Stops the AnimatedSprite and destroys it.\n   * @param {object|boolean} [options] - Options parameter. A boolean will act as if all options\n   *  have been set to that value.\n   * @param {boolean} [options.children=false] - If set to true, all the children will have their destroy\n   *      method called as well. 'options' will be passed on to those calls.\n   * @param {boolean} [options.texture=false] - Should it destroy the current texture of the sprite as well.\n   * @param {boolean} [options.baseTexture=false] - Should it destroy the base texture of the sprite as well.\n   */\n  destroy(options) {\n    this.stop(), super.destroy(options), this.onComplete = null, this.onFrameChange = null, this.onLoop = null;\n  }\n  /**\n   * A short hand way of creating an AnimatedSprite from an array of frame ids.\n   * @param frames - The array of frames ids the AnimatedSprite will use as its texture frames.\n   * @returns - The new animated sprite with the specified frames.\n   */\n  static fromFrames(frames) {\n    const textures = [];\n    for (let i = 0; i < frames.length; ++i) textures.push(Texture.from(frames[i]));\n    return new AnimatedSprite(textures);\n  }\n  /**\n   * A short hand way of creating an AnimatedSprite from an array of image ids.\n   * @param images - The array of image urls the AnimatedSprite will use as its texture frames.\n   * @returns The new animate sprite with the specified images as frames.\n   */\n  static fromImages(images) {\n    const textures = [];\n    for (let i = 0; i < images.length; ++i) textures.push(Texture.from(images[i]));\n    return new AnimatedSprite(textures);\n  }\n  /**\n   * The total number of frames in the AnimatedSprite. This is the same as number of textures\n   * assigned to the AnimatedSprite.\n   * @readonly\n   * @default 0\n   */\n  get totalFrames() {\n    return this._textures.length;\n  }\n  /** The array of textures used for this AnimatedSprite. */\n  get textures() {\n    return this._textures;\n  }\n  set textures(value) {\n    if (value[0] instanceof Texture) this._textures = value, this._durations = null;else {\n      this._textures = [], this._durations = [];\n      for (let i = 0; i < value.length; i++) this._textures.push(value[i].texture), this._durations.push(value[i].time);\n    }\n    this._previousFrame = null, this.gotoAndStop(0), this.updateTexture();\n  }\n  /** The AnimatedSprite's current frame index. */\n  get currentFrame() {\n    let currentFrame = Math.floor(this._currentTime) % this._textures.length;\n    return currentFrame < 0 && (currentFrame += this._textures.length), currentFrame;\n  }\n  set currentFrame(value) {\n    if (value < 0 || value > this.totalFrames - 1) throw new Error(`[AnimatedSprite]: Invalid frame index value ${value}, expected to be between 0 and totalFrames ${this.totalFrames}.`);\n    const previousFrame = this.currentFrame;\n    this._currentTime = value, previousFrame !== this.currentFrame && this.updateTexture();\n  }\n  /**\n   * Indicates if the AnimatedSprite is currently playing.\n   * @readonly\n   */\n  get playing() {\n    return this._playing;\n  }\n  /** Whether to use Ticker.shared to auto update animation time. */\n  get autoUpdate() {\n    return this._autoUpdate;\n  }\n  set autoUpdate(value) {\n    value !== this._autoUpdate && (this._autoUpdate = value, !this._autoUpdate && this._isConnectedToTicker ? (Ticker.shared.remove(this.update, this), this._isConnectedToTicker = !1) : this._autoUpdate && !this._isConnectedToTicker && this._playing && (Ticker.shared.add(this.update, this), this._isConnectedToTicker = !0));\n  }\n}\nexport { AnimatedSprite };", "map": {"version": 3, "names": ["AnimatedSprite", "Sprite", "constructor", "textures", "autoUpdate", "Texture", "texture", "_textures", "_durations", "_autoUpdate", "_isConnectedToTicker", "animationSpeed", "loop", "updateAnchor", "onComplete", "onFrameChange", "onLoop", "_currentTime", "_playing", "_previousFrame", "stop", "Ticker", "shared", "remove", "update", "play", "add", "UPDATE_PRIORITY", "HIGH", "gotoAndStop", "frameNumber", "currentFrame", "gotoAndPlay", "deltaTime", "elapsed", "previousFrame", "lag", "sign", "Math", "floor", "length", "updateTexture", "_texture", "_textureID", "_textureTrimmedID", "_cachedTint", "uvs", "_uvs", "uvsFloat32", "_anchor", "copyFrom", "defaultAnchor", "destroy", "options", "fromFrames", "frames", "i", "push", "from", "fromImages", "images", "totalFrames", "value", "time", "Error", "playing"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\sprite-animated\\src\\AnimatedSprite.ts"], "sourcesContent": ["import { Texture, Ticker, UPDATE_PRIORITY } from '@pixi/core';\nimport { Sprite } from '@pixi/sprite';\n\nimport type { IDestroyOptions } from '@pixi/display';\n\n/**\n * An AnimatedSprite is a simple way to display an animation depicted by a list of textures.\n *\n * ```js\n * import { AnimatedSprite, Texture } from 'pixi.js';\n *\n * const alienImages = [\n *     'image_sequence_01.png',\n *     'image_sequence_02.png',\n *     'image_sequence_03.png',\n *     'image_sequence_04.png',\n * ];\n * const textureArray = [];\n *\n * for (let i = 0; i < 4; i++)\n * {\n *     const texture = Texture.from(alienImages[i]);\n *     textureArray.push(texture);\n * }\n *\n * const animatedSprite = new AnimatedSprite(textureArray);\n * ```\n *\n * The more efficient and simpler way to create an animated sprite is using a {@link PIXI.Spritesheet}\n * containing the animation definitions:\n * @example\n * import { AnimatedSprite, Assets } from 'pixi.js';\n *\n * const sheet = await Assets.load('assets/spritesheet.json');\n * animatedSprite = new AnimatedSprite(sheet.animations['image_sequence']);\n * @memberof PIXI\n */\nexport class AnimatedSprite extends Sprite\n{\n    /**\n     * The speed that the AnimatedSprite will play at. Higher is faster, lower is slower.\n     * @default 1\n     */\n    public animationSpeed: number;\n\n    /**\n     * Whether or not the animate sprite repeats after playing.\n     * @default true\n     */\n    public loop: boolean;\n\n    /**\n     * Update anchor to [Texture's defaultAnchor]{@link PIXI.Texture#defaultAnchor} when frame changes.\n     *\n     * Useful with [sprite sheet animations]{@link PIXI.Spritesheet#animations} created with tools.\n     * Changing anchor for each frame allows to pin sprite origin to certain moving feature\n     * of the frame (e.g. left foot).\n     *\n     * Note: Enabling this will override any previously set `anchor` on each frame change.\n     * @default false\n     */\n    public updateAnchor: boolean;\n\n    /**\n     * User-assigned function to call when an AnimatedSprite finishes playing.\n     * @example\n     * animation.onComplete = () => {\n     *     // Finished!\n     * };\n     */\n    public onComplete?: () => void;\n\n    /**\n     * User-assigned function to call when an AnimatedSprite changes which texture is being rendered.\n     * @example\n     * animation.onFrameChange = () => {\n     *     // Updated!\n     * };\n     */\n    public onFrameChange?: (currentFrame: number) => void;\n\n    /**\n     * User-assigned function to call when `loop` is true, and an AnimatedSprite is played and\n     * loops around to start again.\n     * @example\n     * animation.onLoop = () => {\n     *     // Looped!\n     * };\n     */\n    public onLoop?: () => void;\n\n    private _playing: boolean;\n    private _textures: Texture[];\n    private _durations: number[];\n\n    /**\n     * `true` uses PIXI.Ticker.shared to auto update animation time.\n     * @default true\n     */\n    private _autoUpdate: boolean;\n\n    /**\n     * `true` if the instance is currently connected to PIXI.Ticker.shared to auto update animation time.\n     * @default false\n     */\n    private _isConnectedToTicker: boolean;\n\n    /** Elapsed time since animation has been started, used internally to display current texture. */\n    private _currentTime: number;\n\n    /** The texture index that was displayed last time. */\n    private _previousFrame: number;\n\n    /**\n     * @param textures - An array of {@link PIXI.Texture} or frame\n     *  objects that make up the animation.\n     * @param {boolean} [autoUpdate=true] - Whether to use Ticker.shared to auto update animation time.\n     */\n    constructor(textures: Texture[] | FrameObject[], autoUpdate = true)\n    {\n        super(textures[0] instanceof Texture ? textures[0] : textures[0].texture);\n\n        this._textures = null;\n        this._durations = null;\n        this._autoUpdate = autoUpdate;\n        this._isConnectedToTicker = false;\n\n        this.animationSpeed = 1;\n        this.loop = true;\n        this.updateAnchor = false;\n        this.onComplete = null;\n        this.onFrameChange = null;\n        this.onLoop = null;\n\n        this._currentTime = 0;\n\n        this._playing = false;\n        this._previousFrame = null;\n\n        this.textures = textures;\n    }\n\n    /** Stops the AnimatedSprite. */\n    public stop(): void\n    {\n        if (!this._playing)\n        {\n            return;\n        }\n\n        this._playing = false;\n        if (this._autoUpdate && this._isConnectedToTicker)\n        {\n            Ticker.shared.remove(this.update, this);\n            this._isConnectedToTicker = false;\n        }\n    }\n\n    /** Plays the AnimatedSprite. */\n    public play(): void\n    {\n        if (this._playing)\n        {\n            return;\n        }\n\n        this._playing = true;\n        if (this._autoUpdate && !this._isConnectedToTicker)\n        {\n            Ticker.shared.add(this.update, this, UPDATE_PRIORITY.HIGH);\n            this._isConnectedToTicker = true;\n        }\n    }\n\n    /**\n     * Stops the AnimatedSprite and goes to a specific frame.\n     * @param frameNumber - Frame index to stop at.\n     */\n    public gotoAndStop(frameNumber: number): void\n    {\n        this.stop();\n        this.currentFrame = frameNumber;\n    }\n\n    /**\n     * Goes to a specific frame and begins playing the AnimatedSprite.\n     * @param frameNumber - Frame index to start at.\n     */\n    public gotoAndPlay(frameNumber: number): void\n    {\n        this.currentFrame = frameNumber;\n        this.play();\n    }\n\n    /**\n     * Updates the object transform for rendering.\n     * @param deltaTime - Time since last tick.\n     */\n    update(deltaTime: number): void\n    {\n        if (!this._playing)\n        {\n            return;\n        }\n\n        const elapsed = this.animationSpeed * deltaTime;\n        const previousFrame = this.currentFrame;\n\n        if (this._durations !== null)\n        {\n            let lag = this._currentTime % 1 * this._durations[this.currentFrame];\n\n            lag += elapsed / 60 * 1000;\n\n            while (lag < 0)\n            {\n                this._currentTime--;\n                lag += this._durations[this.currentFrame];\n            }\n\n            const sign = Math.sign(this.animationSpeed * deltaTime);\n\n            this._currentTime = Math.floor(this._currentTime);\n\n            while (lag >= this._durations[this.currentFrame])\n            {\n                lag -= this._durations[this.currentFrame] * sign;\n                this._currentTime += sign;\n            }\n\n            this._currentTime += lag / this._durations[this.currentFrame];\n        }\n        else\n        {\n            this._currentTime += elapsed;\n        }\n\n        if (this._currentTime < 0 && !this.loop)\n        {\n            this.gotoAndStop(0);\n\n            if (this.onComplete)\n            {\n                this.onComplete();\n            }\n        }\n        else if (this._currentTime >= this._textures.length && !this.loop)\n        {\n            this.gotoAndStop(this._textures.length - 1);\n\n            if (this.onComplete)\n            {\n                this.onComplete();\n            }\n        }\n        else if (previousFrame !== this.currentFrame)\n        {\n            if (this.loop && this.onLoop)\n            {\n                if ((this.animationSpeed > 0 && this.currentFrame < previousFrame)\n                    || (this.animationSpeed < 0 && this.currentFrame > previousFrame))\n                {\n                    this.onLoop();\n                }\n            }\n\n            this.updateTexture();\n        }\n    }\n\n    /** Updates the displayed texture to match the current frame index. */\n    private updateTexture(): void\n    {\n        const currentFrame = this.currentFrame;\n\n        if (this._previousFrame === currentFrame)\n        {\n            return;\n        }\n\n        this._previousFrame = currentFrame;\n\n        this._texture = this._textures[currentFrame];\n        this._textureID = -1;\n        this._textureTrimmedID = -1;\n        this._cachedTint = 0xFFFFFF;\n        this.uvs = this._texture._uvs.uvsFloat32;\n\n        if (this.updateAnchor)\n        {\n            this._anchor.copyFrom(this._texture.defaultAnchor);\n        }\n\n        if (this.onFrameChange)\n        {\n            this.onFrameChange(this.currentFrame);\n        }\n    }\n\n    /**\n     * Stops the AnimatedSprite and destroys it.\n     * @param {object|boolean} [options] - Options parameter. A boolean will act as if all options\n     *  have been set to that value.\n     * @param {boolean} [options.children=false] - If set to true, all the children will have their destroy\n     *      method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Should it destroy the current texture of the sprite as well.\n     * @param {boolean} [options.baseTexture=false] - Should it destroy the base texture of the sprite as well.\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        this.stop();\n        super.destroy(options);\n\n        this.onComplete = null;\n        this.onFrameChange = null;\n        this.onLoop = null;\n    }\n\n    /**\n     * A short hand way of creating an AnimatedSprite from an array of frame ids.\n     * @param frames - The array of frames ids the AnimatedSprite will use as its texture frames.\n     * @returns - The new animated sprite with the specified frames.\n     */\n    public static fromFrames(frames: string[]): AnimatedSprite\n    {\n        const textures = [];\n\n        for (let i = 0; i < frames.length; ++i)\n        {\n            textures.push(Texture.from(frames[i]));\n        }\n\n        return new AnimatedSprite(textures);\n    }\n\n    /**\n     * A short hand way of creating an AnimatedSprite from an array of image ids.\n     * @param images - The array of image urls the AnimatedSprite will use as its texture frames.\n     * @returns The new animate sprite with the specified images as frames.\n     */\n    public static fromImages(images: string[]): AnimatedSprite\n    {\n        const textures = [];\n\n        for (let i = 0; i < images.length; ++i)\n        {\n            textures.push(Texture.from(images[i]));\n        }\n\n        return new AnimatedSprite(textures);\n    }\n\n    /**\n     * The total number of frames in the AnimatedSprite. This is the same as number of textures\n     * assigned to the AnimatedSprite.\n     * @readonly\n     * @default 0\n     */\n    get totalFrames(): number\n    {\n        return this._textures.length;\n    }\n\n    /** The array of textures used for this AnimatedSprite. */\n    get textures(): Texture[] | FrameObject[]\n    {\n        return this._textures;\n    }\n\n    set textures(value: Texture[] | FrameObject[])\n    {\n        if (value[0] instanceof Texture)\n        {\n            this._textures = value as Texture[];\n            this._durations = null;\n        }\n        else\n        {\n            this._textures = [];\n            this._durations = [];\n\n            for (let i = 0; i < value.length; i++)\n            {\n                this._textures.push((value[i] as FrameObject).texture);\n                this._durations.push((value[i] as FrameObject).time);\n            }\n        }\n        this._previousFrame = null;\n        this.gotoAndStop(0);\n        this.updateTexture();\n    }\n\n    /** The AnimatedSprite's current frame index. */\n    get currentFrame(): number\n    {\n        let currentFrame = Math.floor(this._currentTime) % this._textures.length;\n\n        if (currentFrame < 0)\n        {\n            currentFrame += this._textures.length;\n        }\n\n        return currentFrame;\n    }\n\n    set currentFrame(value: number)\n    {\n        if (value < 0 || value > this.totalFrames - 1)\n        {\n            throw new Error(`[AnimatedSprite]: Invalid frame index value ${value}, `\n                + `expected to be between 0 and totalFrames ${this.totalFrames}.`);\n        }\n\n        const previousFrame = this.currentFrame;\n\n        this._currentTime = value;\n\n        if (previousFrame !== this.currentFrame)\n        {\n            this.updateTexture();\n        }\n    }\n\n    /**\n     * Indicates if the AnimatedSprite is currently playing.\n     * @readonly\n     */\n    get playing(): boolean\n    {\n        return this._playing;\n    }\n\n    /** Whether to use Ticker.shared to auto update animation time. */\n    get autoUpdate(): boolean\n    {\n        return this._autoUpdate;\n    }\n\n    set autoUpdate(value: boolean)\n    {\n        if (value !== this._autoUpdate)\n        {\n            this._autoUpdate = value;\n\n            if (!this._autoUpdate && this._isConnectedToTicker)\n            {\n                Ticker.shared.remove(this.update, this);\n                this._isConnectedToTicker = false;\n            }\n            else if (this._autoUpdate && !this._isConnectedToTicker && this._playing)\n            {\n                Ticker.shared.add(this.update, this);\n                this._isConnectedToTicker = true;\n            }\n        }\n    }\n}\n\n/** @memberof PIXI.AnimatedSprite */\nexport interface FrameObject\n{\n    /** The {@link PIXI.Texture} of the frame. */\n    texture: Texture;\n\n    /** The duration of the frame, in milliseconds. */\n    time: number;\n}\n"], "mappings": ";;AAqCO,MAAMA,cAAA,SAAuBC,MAAA,CACpC;EAAA;AAAA;AAAA;AAAA;AAAA;EAgFIC,YAAYC,QAAA,EAAqCC,UAAA,GAAa,IAC9D;IACU,MAAAD,QAAA,CAAS,CAAC,aAAaE,OAAA,GAAUF,QAAA,CAAS,CAAC,IAAIA,QAAA,CAAS,CAAC,EAAEG,OAAO,GAExE,KAAKC,SAAA,GAAY,MACjB,KAAKC,UAAA,GAAa,MAClB,KAAKC,WAAA,GAAcL,UAAA,EACnB,KAAKM,oBAAA,GAAuB,IAE5B,KAAKC,cAAA,GAAiB,GACtB,KAAKC,IAAA,GAAO,IACZ,KAAKC,YAAA,GAAe,IACpB,KAAKC,UAAA,GAAa,MAClB,KAAKC,aAAA,GAAgB,MACrB,KAAKC,MAAA,GAAS,MAEd,KAAKC,YAAA,GAAe,GAEpB,KAAKC,QAAA,GAAW,IAChB,KAAKC,cAAA,GAAiB,MAEtB,KAAKhB,QAAA,GAAWA,QAAA;EACpB;EAAA;EAGOiB,KAAA,EACP;IACS,KAAKF,QAAA,KAKV,KAAKA,QAAA,GAAW,IACZ,KAAKT,WAAA,IAAe,KAAKC,oBAAA,KAEzBW,MAAA,CAAOC,MAAA,CAAOC,MAAA,CAAO,KAAKC,MAAA,EAAQ,IAAI,GACtC,KAAKd,oBAAA,GAAuB;EAEpC;EAAA;EAGOe,KAAA,EACP;IACQ,KAAKP,QAAA,KAKT,KAAKA,QAAA,GAAW,IACZ,KAAKT,WAAA,IAAe,CAAC,KAAKC,oBAAA,KAE1BW,MAAA,CAAOC,MAAA,CAAOI,GAAA,CAAI,KAAKF,MAAA,EAAQ,MAAMG,eAAA,CAAgBC,IAAI,GACzD,KAAKlB,oBAAA,GAAuB;EAEpC;EAAA;AAAA;AAAA;AAAA;EAMOmB,YAAYC,WAAA,EACnB;IACS,KAAAV,IAAA,CACL,QAAKW,YAAA,GAAeD,WAAA;EACxB;EAAA;AAAA;AAAA;AAAA;EAMOE,YAAYF,WAAA,EACnB;IACS,KAAAC,YAAA,GAAeD,WAAA,EACpB,KAAKL,IAAA,CAAK;EACd;EAAA;AAAA;AAAA;AAAA;EAMAD,OAAOS,SAAA,EACP;IACI,IAAI,CAAC,KAAKf,QAAA,EAEN;IAGJ,MAAMgB,OAAA,GAAU,KAAKvB,cAAA,GAAiBsB,SAAA;MAChCE,aAAA,GAAgB,KAAKJ,YAAA;IAEvB,SAAKvB,UAAA,KAAe,MACxB;MACI,IAAI4B,GAAA,GAAM,KAAKnB,YAAA,GAAe,IAAI,KAAKT,UAAA,CAAW,KAAKuB,YAAY;MAInE,KAFAK,GAAA,IAAOF,OAAA,GAAU,KAAK,KAEfE,GAAA,GAAM,IAET,KAAKnB,YAAA,IACLmB,GAAA,IAAO,KAAK5B,UAAA,CAAW,KAAKuB,YAAY;MAG5C,MAAMM,IAAA,GAAOC,IAAA,CAAKD,IAAA,CAAK,KAAK1B,cAAA,GAAiBsB,SAAS;MAEtD,UAAKhB,YAAA,GAAeqB,IAAA,CAAKC,KAAA,CAAM,KAAKtB,YAAY,GAEzCmB,GAAA,IAAO,KAAK5B,UAAA,CAAW,KAAKuB,YAAY,IAE3CK,GAAA,IAAO,KAAK5B,UAAA,CAAW,KAAKuB,YAAY,IAAIM,IAAA,EAC5C,KAAKpB,YAAA,IAAgBoB,IAAA;MAGzB,KAAKpB,YAAA,IAAgBmB,GAAA,GAAM,KAAK5B,UAAA,CAAW,KAAKuB,YAAY;IAChE,OAGI,KAAKd,YAAA,IAAgBiB,OAAA;IAGrB,KAAKjB,YAAA,GAAe,KAAK,CAAC,KAAKL,IAAA,IAE/B,KAAKiB,WAAA,CAAY,CAAC,GAEd,KAAKf,UAAA,IAEL,KAAKA,UAAA,CAAW,KAGf,KAAKG,YAAA,IAAgB,KAAKV,SAAA,CAAUiC,MAAA,IAAU,CAAC,KAAK5B,IAAA,IAEzD,KAAKiB,WAAA,CAAY,KAAKtB,SAAA,CAAUiC,MAAA,GAAS,CAAC,GAEtC,KAAK1B,UAAA,IAEL,KAAKA,UAAA,MAGJqB,aAAA,KAAkB,KAAKJ,YAAA,KAExB,KAAKnB,IAAA,IAAQ,KAAKI,MAAA,KAEb,KAAKL,cAAA,GAAiB,KAAK,KAAKoB,YAAA,GAAeI,aAAA,IAC5C,KAAKxB,cAAA,GAAiB,KAAK,KAAKoB,YAAA,GAAeI,aAAA,KAEnD,KAAKnB,MAAA,IAIb,KAAKyB,aAAA,CAAc;EAE3B;EAAA;EAGQA,cAAA,EACR;IACI,MAAMV,YAAA,GAAe,KAAKA,YAAA;IAEtB,KAAKZ,cAAA,KAAmBY,YAAA,KAK5B,KAAKZ,cAAA,GAAiBY,YAAA,EAEtB,KAAKW,QAAA,GAAW,KAAKnC,SAAA,CAAUwB,YAAY,GAC3C,KAAKY,UAAA,GAAa,IAClB,KAAKC,iBAAA,GAAoB,IACzB,KAAKC,WAAA,GAAc,UACnB,KAAKC,GAAA,GAAM,KAAKJ,QAAA,CAASK,IAAA,CAAKC,UAAA,EAE1B,KAAKnC,YAAA,IAEL,KAAKoC,OAAA,CAAQC,QAAA,CAAS,KAAKR,QAAA,CAASS,aAAa,GAGjD,KAAKpC,aAAA,IAEL,KAAKA,aAAA,CAAc,KAAKgB,YAAY;EAE5C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWOqB,QAAQC,OAAA,EACf;IACI,KAAKjC,IAAA,CAAK,GACV,MAAMgC,OAAA,CAAQC,OAAO,GAErB,KAAKvC,UAAA,GAAa,MAClB,KAAKC,aAAA,GAAgB,MACrB,KAAKC,MAAA,GAAS;EAClB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,OAAcsC,WAAWC,MAAA,EACzB;IACI,MAAMpD,QAAA,GAAW;IAEjB,SAASqD,CAAA,GAAI,GAAGA,CAAA,GAAID,MAAA,CAAOf,MAAA,EAAQ,EAAEgB,CAAA,EAEjCrD,QAAA,CAASsD,IAAA,CAAKpD,OAAA,CAAQqD,IAAA,CAAKH,MAAA,CAAOC,CAAC,CAAC,CAAC;IAGlC,WAAIxD,cAAA,CAAeG,QAAQ;EACtC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,OAAcwD,WAAWC,MAAA,EACzB;IACI,MAAMzD,QAAA,GAAW;IAEjB,SAASqD,CAAA,GAAI,GAAGA,CAAA,GAAII,MAAA,CAAOpB,MAAA,EAAQ,EAAEgB,CAAA,EAEjCrD,QAAA,CAASsD,IAAA,CAAKpD,OAAA,CAAQqD,IAAA,CAAKE,MAAA,CAAOJ,CAAC,CAAC,CAAC;IAGlC,WAAIxD,cAAA,CAAeG,QAAQ;EACtC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQA,IAAI0D,YAAA,EACJ;IACI,OAAO,KAAKtD,SAAA,CAAUiC,MAAA;EAC1B;EAAA;EAGA,IAAIrC,SAAA,EACJ;IACI,OAAO,KAAKI,SAAA;EAChB;EAEA,IAAIJ,SAAS2D,KAAA,EACb;IACQ,IAAAA,KAAA,CAAM,CAAC,aAAazD,OAAA,EAEf,KAAAE,SAAA,GAAYuD,KAAA,EACjB,KAAKtD,UAAA,GAAa,UAGtB;MACI,KAAKD,SAAA,GAAY,IACjB,KAAKC,UAAA,GAAa;MAElB,SAASgD,CAAA,GAAI,GAAGA,CAAA,GAAIM,KAAA,CAAMtB,MAAA,EAAQgB,CAAA,IAE9B,KAAKjD,SAAA,CAAUkD,IAAA,CAAMK,KAAA,CAAMN,CAAC,EAAkBlD,OAAO,GACrD,KAAKE,UAAA,CAAWiD,IAAA,CAAMK,KAAA,CAAMN,CAAC,EAAkBO,IAAI;IAE3D;IACA,KAAK5C,cAAA,GAAiB,MACtB,KAAKU,WAAA,CAAY,CAAC,GAClB,KAAKY,aAAA;EACT;EAAA;EAGA,IAAIV,aAAA,EACJ;IACI,IAAIA,YAAA,GAAeO,IAAA,CAAKC,KAAA,CAAM,KAAKtB,YAAY,IAAI,KAAKV,SAAA,CAAUiC,MAAA;IAElE,OAAIT,YAAA,GAAe,MAEfA,YAAA,IAAgB,KAAKxB,SAAA,CAAUiC,MAAA,GAG5BT,YAAA;EACX;EAEA,IAAIA,aAAa+B,KAAA,EACjB;IACI,IAAIA,KAAA,GAAQ,KAAKA,KAAA,GAAQ,KAAKD,WAAA,GAAc,GAExC,MAAM,IAAIG,KAAA,CAAM,+CAA+CF,KAAK,8CAClB,KAAKD,WAAW,GAAG;IAGzE,MAAM1B,aAAA,GAAgB,KAAKJ,YAAA;IAE3B,KAAKd,YAAA,GAAe6C,KAAA,EAEhB3B,aAAA,KAAkB,KAAKJ,YAAA,IAEvB,KAAKU,aAAA;EAEb;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIwB,QAAA,EACJ;IACI,OAAO,KAAK/C,QAAA;EAChB;EAAA;EAGA,IAAId,WAAA,EACJ;IACI,OAAO,KAAKK,WAAA;EAChB;EAEA,IAAIL,WAAW0D,KAAA,EACf;IACQA,KAAA,KAAU,KAAKrD,WAAA,KAEf,KAAKA,WAAA,GAAcqD,KAAA,EAEf,CAAC,KAAKrD,WAAA,IAAe,KAAKC,oBAAA,IAE1BW,MAAA,CAAOC,MAAA,CAAOC,MAAA,CAAO,KAAKC,MAAA,EAAQ,IAAI,GACtC,KAAKd,oBAAA,GAAuB,MAEvB,KAAKD,WAAA,IAAe,CAAC,KAAKC,oBAAA,IAAwB,KAAKQ,QAAA,KAE5DG,MAAA,CAAOC,MAAA,CAAOI,GAAA,CAAI,KAAKF,MAAA,EAAQ,IAAI,GACnC,KAAKd,oBAAA,GAAuB;EAGxC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}