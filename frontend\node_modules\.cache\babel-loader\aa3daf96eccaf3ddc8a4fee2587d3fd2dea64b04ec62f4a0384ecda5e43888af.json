{"ast": null, "code": "import { ExtensionType, extensions } from \"@pixi/extensions\";\nimport { Matrix } from \"@pixi/math\";\nclass ProjectionSystem {\n  /** @param renderer - The renderer this System works for. */\n  constructor(renderer) {\n    this.renderer = renderer, this.destinationFrame = null, this.sourceFrame = null, this.defaultFrame = null, this.projectionMatrix = new Matrix(), this.transform = null;\n  }\n  /**\n   * Updates the projection-matrix based on the sourceFrame → destinationFrame mapping provided.\n   *\n   * NOTE: It is expected you call `renderer.framebuffer.setViewport(destinationFrame)` after this. This is because\n   * the framebuffer viewport converts shader vertex output in normalized device coordinates to window coordinates.\n   *\n   * NOTE-2: {@link PIXI.RenderTextureSystem#bind} updates the projection-matrix when you bind a render-texture.\n   * It is expected\n   * that you dirty the current bindings when calling this manually.\n   * @param destinationFrame - The rectangle in the render-target to render the contents into. If rendering to the canvas,\n   *  the origin is on the top-left; if rendering to a render-texture, the origin is on the bottom-left.\n   * @param sourceFrame - The rectangle in world space that contains the contents being rendered.\n   * @param resolution - The resolution of the render-target, which is the ratio of\n   *  world-space (or CSS) pixels to physical pixels.\n   * @param root - Whether the render-target is the screen. This is required because rendering to textures\n   *  is y-flipped (i.e. upside down relative to the screen).\n   */\n  update(destinationFrame, sourceFrame, resolution, root) {\n    this.destinationFrame = destinationFrame || this.destinationFrame || this.defaultFrame, this.sourceFrame = sourceFrame || this.sourceFrame || destinationFrame, this.calculateProjection(this.destinationFrame, this.sourceFrame, resolution, root), this.transform && this.projectionMatrix.append(this.transform);\n    const renderer = this.renderer;\n    renderer.globalUniforms.uniforms.projectionMatrix = this.projectionMatrix, renderer.globalUniforms.update(), renderer.shader.shader && renderer.shader.syncUniformGroup(renderer.shader.shader.uniforms.globals);\n  }\n  /**\n   * Calculates the `projectionMatrix` to map points inside `sourceFrame` to inside `destinationFrame`.\n   * @param _destinationFrame - The destination frame in the render-target.\n   * @param sourceFrame - The source frame in world space.\n   * @param _resolution - The render-target's resolution, i.e. ratio of CSS to physical pixels.\n   * @param root - Whether rendering into the screen. Otherwise, if rendering to a framebuffer, the projection\n   *  is y-flipped.\n   */\n  calculateProjection(_destinationFrame, sourceFrame, _resolution, root) {\n    const pm = this.projectionMatrix,\n      sign = root ? -1 : 1;\n    pm.identity(), pm.a = 1 / sourceFrame.width * 2, pm.d = sign * (1 / sourceFrame.height * 2), pm.tx = -1 - sourceFrame.x * pm.a, pm.ty = -sign - sourceFrame.y * pm.d;\n  }\n  /**\n   * Sets the transform of the active render target to the given matrix.\n   * @param _matrix - The transformation matrix\n   */\n  setTransform(_matrix) {}\n  destroy() {\n    this.renderer = null;\n  }\n}\nProjectionSystem.extension = {\n  type: ExtensionType.RendererSystem,\n  name: \"projection\"\n};\nextensions.add(ProjectionSystem);\nexport { ProjectionSystem };", "map": {"version": 3, "names": ["ProjectionSystem", "constructor", "renderer", "destinationFrame", "sourceFrame", "defaultFrame", "projectionMatrix", "Matrix", "transform", "update", "resolution", "root", "calculateProjection", "append", "globalUniforms", "uniforms", "shader", "syncUniformGroup", "globals", "_destinationFrame", "_resolution", "pm", "sign", "identity", "a", "width", "d", "height", "tx", "x", "ty", "y", "setTransform", "_matrix", "destroy", "extension", "type", "ExtensionType", "RendererSystem", "name", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\projection\\ProjectionSystem.ts"], "sourcesContent": ["import { extensions, ExtensionType } from '@pixi/extensions';\nimport { Matrix } from '@pixi/math';\n\nimport type { ExtensionMetadata } from '@pixi/extensions';\nimport type { Rectangle } from '@pixi/math';\nimport type { Renderer } from '../Renderer';\nimport type { ISystem } from '../system/ISystem';\n\n/**\n * System plugin to the renderer to manage the projection matrix.\n *\n * The `projectionMatrix` is a global uniform provided to all shaders. It is used to transform points in world space to\n * normalized device coordinates.\n * @memberof PIXI\n */\nexport class ProjectionSystem implements ISystem\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        type: ExtensionType.RendererSystem,\n        name: 'projection',\n    };\n\n    /**\n     * The destination frame used to calculate the current projection matrix.\n     *\n     * The destination frame is the rectangle in the render-target into which contents are rendered. If rendering\n     * to the screen, the origin is on the top-left. If rendering to a framebuffer, the origin is on the\n     * bottom-left. This \"flipping\" phenomenon is because of WebGL convention for (shader) texture coordinates, where\n     * the bottom-left corner is (0,0). It allows display-objects to map their (0,0) position in local-space (top-left)\n     * to (0,0) in texture space (bottom-left). In other words, a sprite's top-left corner actually renders the\n     * texture's bottom-left corner. You will also notice this when using a tool like SpectorJS to view your textures\n     * at runtime.\n     *\n     * The destination frame's dimensions (width,height) should be equal to the source frame. This is because,\n     * otherwise, the contents will be scaled to fill the destination frame. Similarly, the destination frame's (x,y)\n     * coordinates are (0,0) unless you know what you're doing.\n     * @readonly\n     */\n    public destinationFrame: Rectangle;\n\n    /**\n     * The source frame used to calculate the current projection matrix.\n     *\n     * The source frame is the rectangle in world space containing the contents to be rendered.\n     * @readonly\n     */\n    public sourceFrame: Rectangle;\n\n    /**\n     * Default destination frame\n     *\n     * This is not used internally. It is not advised to use this feature specifically unless you know what\n     * you're doing. The `update` method will default to this frame if you do not pass the destination frame.\n     * @readonly\n     */\n    public defaultFrame: Rectangle;\n\n    /**\n     * Projection matrix\n     *\n     * This matrix can be used to transform points from world space to normalized device coordinates, and is calculated\n     * from the sourceFrame → destinationFrame mapping provided.\n     *\n     * The renderer's `globalUniforms` keeps a reference to this, and so it is available for all shaders to use as a\n     * uniform.\n     * @readonly\n     */\n    public projectionMatrix: Matrix;\n\n    /**\n     * A transform to be appended to the projection matrix.\n     *\n     * This can be used to transform points in world-space one last time before they are outputted by the shader. You can\n     * use to rotate the whole scene, for example. Remember to clear it once you've rendered everything.\n     * @member {PIXI.Matrix}\n     */\n    public transform: Matrix;\n\n    private renderer: Renderer;\n\n    /** @param renderer - The renderer this System works for. */\n    constructor(renderer: Renderer)\n    {\n        this.renderer = renderer;\n\n        this.destinationFrame = null;\n        this.sourceFrame = null;\n        this.defaultFrame = null;\n        this.projectionMatrix = new Matrix();\n        this.transform = null;\n    }\n\n    /**\n     * Updates the projection-matrix based on the sourceFrame → destinationFrame mapping provided.\n     *\n     * NOTE: It is expected you call `renderer.framebuffer.setViewport(destinationFrame)` after this. This is because\n     * the framebuffer viewport converts shader vertex output in normalized device coordinates to window coordinates.\n     *\n     * NOTE-2: {@link PIXI.RenderTextureSystem#bind} updates the projection-matrix when you bind a render-texture.\n     * It is expected\n     * that you dirty the current bindings when calling this manually.\n     * @param destinationFrame - The rectangle in the render-target to render the contents into. If rendering to the canvas,\n     *  the origin is on the top-left; if rendering to a render-texture, the origin is on the bottom-left.\n     * @param sourceFrame - The rectangle in world space that contains the contents being rendered.\n     * @param resolution - The resolution of the render-target, which is the ratio of\n     *  world-space (or CSS) pixels to physical pixels.\n     * @param root - Whether the render-target is the screen. This is required because rendering to textures\n     *  is y-flipped (i.e. upside down relative to the screen).\n     */\n    update(destinationFrame: Rectangle, sourceFrame: Rectangle, resolution: number, root: boolean): void\n    {\n        this.destinationFrame = destinationFrame || this.destinationFrame || this.defaultFrame;\n        this.sourceFrame = sourceFrame || this.sourceFrame || destinationFrame;\n\n        // Calculate object-space to clip-space projection\n        this.calculateProjection(this.destinationFrame, this.sourceFrame, resolution, root);\n\n        if (this.transform)\n        {\n            this.projectionMatrix.append(this.transform);\n        }\n\n        const renderer = this.renderer;\n\n        renderer.globalUniforms.uniforms.projectionMatrix = this.projectionMatrix;\n        renderer.globalUniforms.update();\n\n        // this will work for now\n        // but would be sweet to stick and even on the global uniforms..\n        if (renderer.shader.shader)\n        {\n            renderer.shader.syncUniformGroup(renderer.shader.shader.uniforms.globals);\n        }\n    }\n\n    /**\n     * Calculates the `projectionMatrix` to map points inside `sourceFrame` to inside `destinationFrame`.\n     * @param _destinationFrame - The destination frame in the render-target.\n     * @param sourceFrame - The source frame in world space.\n     * @param _resolution - The render-target's resolution, i.e. ratio of CSS to physical pixels.\n     * @param root - Whether rendering into the screen. Otherwise, if rendering to a framebuffer, the projection\n     *  is y-flipped.\n     */\n    calculateProjection(_destinationFrame: Rectangle, sourceFrame: Rectangle, _resolution: number, root: boolean): void\n    {\n        const pm = this.projectionMatrix;\n        const sign = !root ? 1 : -1;\n\n        pm.identity();\n\n        pm.a = (1 / sourceFrame.width * 2);\n        pm.d = sign * (1 / sourceFrame.height * 2);\n\n        pm.tx = -1 - (sourceFrame.x * pm.a);\n        pm.ty = -sign - (sourceFrame.y * pm.d);\n    }\n\n    /**\n     * Sets the transform of the active render target to the given matrix.\n     * @param _matrix - The transformation matrix\n     */\n    setTransform(_matrix: Matrix): void\n    {\n        // this._activeRenderTarget.transform = matrix;\n    }\n\n    destroy(): void\n    {\n        this.renderer = null;\n    }\n}\n\nextensions.add(ProjectionSystem);\n"], "mappings": ";;AAeO,MAAMA,gBAAA,CACb;EAAA;EAkEIC,YAAYC,QAAA,EACZ;IACI,KAAKA,QAAA,GAAWA,QAAA,EAEhB,KAAKC,gBAAA,GAAmB,MACxB,KAAKC,WAAA,GAAc,MACnB,KAAKC,YAAA,GAAe,MACpB,KAAKC,gBAAA,GAAmB,IAAIC,MAAA,IAC5B,KAAKC,SAAA,GAAY;EACrB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAmBAC,OAAON,gBAAA,EAA6BC,WAAA,EAAwBM,UAAA,EAAoBC,IAAA,EAChF;IACS,KAAAR,gBAAA,GAAmBA,gBAAA,IAAoB,KAAKA,gBAAA,IAAoB,KAAKE,YAAA,EAC1E,KAAKD,WAAA,GAAcA,WAAA,IAAe,KAAKA,WAAA,IAAeD,gBAAA,EAGtD,KAAKS,mBAAA,CAAoB,KAAKT,gBAAA,EAAkB,KAAKC,WAAA,EAAaM,UAAA,EAAYC,IAAI,GAE9E,KAAKH,SAAA,IAEL,KAAKF,gBAAA,CAAiBO,MAAA,CAAO,KAAKL,SAAS;IAG/C,MAAMN,QAAA,GAAW,KAAKA,QAAA;IAEtBA,QAAA,CAASY,cAAA,CAAeC,QAAA,CAAST,gBAAA,GAAmB,KAAKA,gBAAA,EACzDJ,QAAA,CAASY,cAAA,CAAeL,MAAA,IAIpBP,QAAA,CAASc,MAAA,CAAOA,MAAA,IAEhBd,QAAA,CAASc,MAAA,CAAOC,gBAAA,CAAiBf,QAAA,CAASc,MAAA,CAAOA,MAAA,CAAOD,QAAA,CAASG,OAAO;EAEhF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUAN,oBAAoBO,iBAAA,EAA8Bf,WAAA,EAAwBgB,WAAA,EAAqBT,IAAA,EAC/F;IACI,MAAMU,EAAA,GAAK,KAAKf,gBAAA;MACVgB,IAAA,GAAQX,IAAA,GAAW,KAAJ;IAErBU,EAAA,CAAGE,QAAA,CAAS,GAEZF,EAAA,CAAGG,CAAA,GAAK,IAAIpB,WAAA,CAAYqB,KAAA,GAAQ,GAChCJ,EAAA,CAAGK,CAAA,GAAIJ,IAAA,IAAQ,IAAIlB,WAAA,CAAYuB,MAAA,GAAS,IAExCN,EAAA,CAAGO,EAAA,GAAK,KAAMxB,WAAA,CAAYyB,CAAA,GAAIR,EAAA,CAAGG,CAAA,EACjCH,EAAA,CAAGS,EAAA,GAAK,CAACR,IAAA,GAAQlB,WAAA,CAAY2B,CAAA,GAAIV,EAAA,CAAGK,CAAA;EACxC;EAAA;AAAA;AAAA;AAAA;EAMAM,aAAaC,OAAA,EACb,CAEA;EAEAC,QAAA,EACA;IACI,KAAKhC,QAAA,GAAW;EACpB;AACJ;AA5JaF,gBAAA,CAGFmC,SAAA,GAA+B;EAClCC,IAAA,EAAMC,aAAA,CAAcC,cAAA;EACpBC,IAAA,EAAM;AACV;AAwJJC,UAAA,CAAWC,GAAA,CAAIzC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}