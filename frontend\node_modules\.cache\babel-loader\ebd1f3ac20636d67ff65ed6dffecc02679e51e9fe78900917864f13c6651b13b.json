{"ast": null, "code": "const isSingleItem = item => !Array.isArray(item);\nexport { isSingleItem };", "map": {"version": 3, "names": ["isSingleItem", "item", "Array", "isArray"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\assets\\src\\utils\\isSingleItem.ts"], "sourcesContent": ["/**\n * Checks if the given value is an array.\n * @param item - The item to test\n */\nexport const isSingleItem = (item: unknown): boolean => (!Array.isArray(item));\n"], "mappings": "AAIO,MAAMA,YAAA,GAAgBC,IAAA,IAA4B,CAACC,KAAA,CAAMC,OAAA,CAAQF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}