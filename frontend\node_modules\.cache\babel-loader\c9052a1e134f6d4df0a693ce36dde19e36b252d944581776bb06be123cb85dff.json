{"ast": null, "code": "import \"./settings.mjs\";\nimport { UPDATE_PRIORITY } from \"./const.mjs\";\nimport { Ticker } from \"./Ticker.mjs\";\nimport { TickerPlugin } from \"./TickerPlugin.mjs\";\nexport { Ticker, TickerPlugin, UPDATE_PRIORITY };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import \"./settings.mjs\";\nimport { UPDATE_PRIORITY } from \"./const.mjs\";\nimport { Ticker } from \"./Ticker.mjs\";\nimport { TickerPlugin } from \"./TickerPlugin.mjs\";\nexport {\n  Ticker,\n  TickerPlugin,\n  UPDATE_PRIORITY\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}