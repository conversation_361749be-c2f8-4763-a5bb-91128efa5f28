{"ast": null, "code": "import { ExtensionType, extensions } from \"@pixi/extensions\";\nimport { Transform, Rectangle, Matrix } from \"@pixi/math\";\nimport { RenderTexture } from \"./RenderTexture.mjs\";\nconst tempTransform = new Transform(),\n  tempRect = new Rectangle();\nclass GenerateTextureSystem {\n  constructor(renderer) {\n    this.renderer = renderer, this._tempMatrix = new Matrix();\n  }\n  /**\n   * A Useful function that returns a texture of the display object that can then be used to create sprites\n   * This can be quite useful if your displayObject is complicated and needs to be reused multiple times.\n   * @param displayObject - The displayObject the object will be generated from.\n   * @param {IGenerateTextureOptions} options - Generate texture options.\n   * @param {PIXI.Rectangle} options.region - The region of the displayObject, that shall be rendered,\n   *        if no region is specified, defaults to the local bounds of the displayObject.\n   * @param {number} [options.resolution] - If not given, the renderer's resolution is used.\n   * @param {PIXI.MSAA_QUALITY} [options.multisample] - If not given, the renderer's multisample is used.\n   * @returns a shiny new texture of the display object passed in\n   */\n  generateTexture(displayObject, options) {\n    const {\n        region: manualRegion,\n        ...textureOptions\n      } = options || {},\n      region = manualRegion?.copyTo(tempRect) || displayObject.getLocalBounds(tempRect, !0),\n      resolution = textureOptions.resolution || this.renderer.resolution;\n    region.width = Math.max(region.width, 1 / resolution), region.height = Math.max(region.height, 1 / resolution), textureOptions.width = region.width, textureOptions.height = region.height, textureOptions.resolution = resolution, textureOptions.multisample ?? (textureOptions.multisample = this.renderer.multisample);\n    const renderTexture = RenderTexture.create(textureOptions);\n    this._tempMatrix.tx = -region.x, this._tempMatrix.ty = -region.y;\n    const transform = displayObject.transform;\n    return displayObject.transform = tempTransform, this.renderer.render(displayObject, {\n      renderTexture,\n      transform: this._tempMatrix,\n      skipUpdateTransform: !!displayObject.parent,\n      blit: !0\n    }), displayObject.transform = transform, renderTexture;\n  }\n  destroy() {}\n}\nGenerateTextureSystem.extension = {\n  type: [ExtensionType.RendererSystem, ExtensionType.CanvasRendererSystem],\n  name: \"textureGenerator\"\n};\nextensions.add(GenerateTextureSystem);\nexport { GenerateTextureSystem };", "map": {"version": 3, "names": ["tempTransform", "Transform", "tempRect", "Rectangle", "GenerateTextureSystem", "constructor", "renderer", "_tempMatrix", "Matrix", "generateTexture", "displayObject", "options", "region", "manualRegion", "textureOptions", "copyTo", "getLocalBounds", "resolution", "width", "Math", "max", "height", "multisample", "renderTexture", "RenderTexture", "create", "tx", "x", "ty", "y", "transform", "render", "skipUpdateTransform", "parent", "blit", "destroy", "extension", "type", "ExtensionType", "RendererSystem", "CanvasRendererSystem", "name", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\renderTexture\\GenerateTextureSystem.ts"], "sourcesContent": ["import { extensions, ExtensionType } from '@pixi/extensions';\nimport { Matrix, Rectangle, Transform } from '@pixi/math';\nimport { RenderTexture } from './RenderTexture';\n\nimport type { MSAA_QUALITY } from '@pixi/constants';\nimport type { ExtensionMetadata } from '@pixi/extensions';\nimport type { IRenderableContainer, IRenderableObject, IRenderer } from '../IRenderer';\nimport type { ISystem } from '../system/ISystem';\nimport type { IBaseTextureOptions } from '../textures/BaseTexture';\n\nconst tempTransform = new Transform();\nconst tempRect = new Rectangle();\n\n// TODO could this just be part of extract?\nexport interface IGenerateTextureOptions extends IBaseTextureOptions\n{\n    /**\n     * The region of the displayObject, that shall be rendered,\n     * if no region is specified, defaults to the local bounds of the displayObject.\n     */\n    region?: Rectangle;\n    /** The resolution / device pixel ratio of the texture being generated. The default is the renderer's resolution. */\n    resolution?: number;\n    /** The number of samples of the frame buffer. The default is the renderer's multisample. */\n    multisample?: MSAA_QUALITY;\n}\n\n/**\n * System that manages the generation of textures from the renderer.\n * @memberof PIXI\n */\nexport class GenerateTextureSystem implements ISystem\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        type: [\n            ExtensionType.RendererSystem,\n            ExtensionType.CanvasRendererSystem\n        ],\n        name: 'textureGenerator',\n    };\n\n    renderer: IRenderer;\n\n    private readonly _tempMatrix: Matrix;\n\n    constructor(renderer: IRenderer)\n    {\n        this.renderer = renderer;\n\n        this._tempMatrix = new Matrix();\n    }\n\n    /**\n     * A Useful function that returns a texture of the display object that can then be used to create sprites\n     * This can be quite useful if your displayObject is complicated and needs to be reused multiple times.\n     * @param displayObject - The displayObject the object will be generated from.\n     * @param {IGenerateTextureOptions} options - Generate texture options.\n     * @param {PIXI.Rectangle} options.region - The region of the displayObject, that shall be rendered,\n     *        if no region is specified, defaults to the local bounds of the displayObject.\n     * @param {number} [options.resolution] - If not given, the renderer's resolution is used.\n     * @param {PIXI.MSAA_QUALITY} [options.multisample] - If not given, the renderer's multisample is used.\n     * @returns a shiny new texture of the display object passed in\n     */\n    generateTexture(displayObject: IRenderableObject, options?: IGenerateTextureOptions): RenderTexture\n    {\n        const { region: manualRegion, ...textureOptions } = options || {};\n\n        const region = manualRegion?.copyTo(tempRect)\n            || (displayObject as IRenderableContainer).getLocalBounds(tempRect, true);\n        const resolution = textureOptions.resolution || this.renderer.resolution;\n\n        region.width = Math.max(region.width, 1 / resolution);\n        region.height = Math.max(region.height, 1 / resolution);\n\n        textureOptions.width = region.width;\n        textureOptions.height = region.height;\n        textureOptions.resolution = resolution;\n        textureOptions.multisample ??= this.renderer.multisample;\n\n        const renderTexture = RenderTexture.create(textureOptions);\n\n        this._tempMatrix.tx = -region.x;\n        this._tempMatrix.ty = -region.y;\n\n        const transform = displayObject.transform;\n\n        displayObject.transform = tempTransform;\n\n        this.renderer.render(displayObject, {\n            renderTexture,\n            transform: this._tempMatrix,\n            skipUpdateTransform: !!displayObject.parent,\n            blit: true,\n        });\n\n        displayObject.transform = transform;\n\n        return renderTexture;\n    }\n\n    destroy(): void\n    {\n        // ka boom!\n    }\n}\n\nextensions.add(GenerateTextureSystem);\n"], "mappings": ";;;AAUA,MAAMA,aAAA,GAAgB,IAAIC,SAAA;EACpBC,QAAA,GAAW,IAAIC,SAAA,CAAU;AAoBxB,MAAMC,qBAAA,CACb;EAcIC,YAAYC,QAAA,EACZ;IACI,KAAKA,QAAA,GAAWA,QAAA,EAEhB,KAAKC,WAAA,GAAc,IAAIC,MAAA;EAC3B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAaAC,gBAAgBC,aAAA,EAAkCC,OAAA,EAClD;IACU;QAAEC,MAAA,EAAQC,YAAA;QAAc,GAAGC;MAAmB,IAAAH,OAAA,IAAW;MAEzDC,MAAA,GAASC,YAAA,EAAcE,MAAA,CAAOb,QAAQ,KACpCQ,aAAA,CAAuCM,cAAA,CAAed,QAAA,EAAU,EAAI;MACtEe,UAAA,GAAaH,cAAA,CAAeG,UAAA,IAAc,KAAKX,QAAA,CAASW,UAAA;IAE9DL,MAAA,CAAOM,KAAA,GAAQC,IAAA,CAAKC,GAAA,CAAIR,MAAA,CAAOM,KAAA,EAAO,IAAID,UAAU,GACpDL,MAAA,CAAOS,MAAA,GAASF,IAAA,CAAKC,GAAA,CAAIR,MAAA,CAAOS,MAAA,EAAQ,IAAIJ,UAAU,GAEtDH,cAAA,CAAeI,KAAA,GAAQN,MAAA,CAAOM,KAAA,EAC9BJ,cAAA,CAAeO,MAAA,GAAST,MAAA,CAAOS,MAAA,EAC/BP,cAAA,CAAeG,UAAA,GAAaA,UAAA,EAC5BH,cAAA,CAAeQ,WAAA,KAAfR,cAAA,CAAeQ,WAAA,GAAgB,KAAKhB,QAAA,CAASgB,WAAA;IAEvC,MAAAC,aAAA,GAAgBC,aAAA,CAAcC,MAAA,CAAOX,cAAc;IAEpD,KAAAP,WAAA,CAAYmB,EAAA,GAAK,CAACd,MAAA,CAAOe,CAAA,EAC9B,KAAKpB,WAAA,CAAYqB,EAAA,GAAK,CAAChB,MAAA,CAAOiB,CAAA;IAE9B,MAAMC,SAAA,GAAYpB,aAAA,CAAcoB,SAAA;IAEhC,OAAApB,aAAA,CAAcoB,SAAA,GAAY9B,aAAA,EAE1B,KAAKM,QAAA,CAASyB,MAAA,CAAOrB,aAAA,EAAe;MAChCa,aAAA;MACAO,SAAA,EAAW,KAAKvB,WAAA;MAChByB,mBAAA,EAAqB,CAAC,CAACtB,aAAA,CAAcuB,MAAA;MACrCC,IAAA,EAAM;IACT,IAEDxB,aAAA,CAAcoB,SAAA,GAAYA,SAAA,EAEnBP,aAAA;EACX;EAEAY,QAAA,EACA,CAEA;AACJ;AA1Ea/B,qBAAA,CAGFgC,SAAA,GAA+B;EAClCC,IAAA,EAAM,CACFC,aAAA,CAAcC,cAAA,EACdD,aAAA,CAAcE,oBAAA,CAClB;EACAC,IAAA,EAAM;AACV;AAmEJC,UAAA,CAAWC,GAAA,CAAIvC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}