{"ast": null, "code": "import { BlobResource } from \"./BlobResource.mjs\";\nimport { CompressedTextureResource } from \"./CompressedTextureResource.mjs\";\nexport { BlobResource, CompressedTextureResource };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import { BlobResource } from \"./BlobResource.mjs\";\nimport { CompressedTextureResource } from \"./CompressedTextureResource.mjs\";\nexport {\n  BlobResource,\n  CompressedTextureResource\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}