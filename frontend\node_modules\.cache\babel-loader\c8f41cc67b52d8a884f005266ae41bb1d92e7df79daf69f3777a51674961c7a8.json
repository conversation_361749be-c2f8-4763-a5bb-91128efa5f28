{"ast": null, "code": "import { checkMaxIfStatementsInShader } from \"./checkMaxIfStatementsInShader.mjs\";\nimport { compileShader } from \"./compileShader.mjs\";\nimport { defaultValue } from \"./defaultValue.mjs\";\nimport { generateUniformsSync } from \"./generateUniformsSync.mjs\";\nimport { getMaxFragmentPrecision } from \"./getMaxFragmentPrecision.mjs\";\nimport { getTestContext } from \"./getTestContext.mjs\";\nimport { logProgramError } from \"./logProgramError.mjs\";\nimport { mapSize } from \"./mapSize.mjs\";\nimport { mapType } from \"./mapType.mjs\";\nimport { setPrecision } from \"./setPrecision.mjs\";\nimport { uniformParsers } from \"./uniformParsers.mjs\";\nimport { unsafeEvalSupported } from \"./unsafeEvalSupported.mjs\";\nexport { checkMaxIfStatementsInShader, compileShader, defaultValue, generateUniformsSync, getMaxFragmentPrecision, getTestContext, logProgramError, mapSize, mapType, setPrecision, uniformParsers, unsafeEvalSupported };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import { checkMaxIfStatementsInShader } from \"./checkMaxIfStatementsInShader.mjs\";\nimport { compileShader } from \"./compileShader.mjs\";\nimport { defaultValue } from \"./defaultValue.mjs\";\nimport { generateUniformsSync } from \"./generateUniformsSync.mjs\";\nimport { getMaxFragmentPrecision } from \"./getMaxFragmentPrecision.mjs\";\nimport { getTestContext } from \"./getTestContext.mjs\";\nimport { logProgramError } from \"./logProgramError.mjs\";\nimport { mapSize } from \"./mapSize.mjs\";\nimport { mapType } from \"./mapType.mjs\";\nimport { setPrecision } from \"./setPrecision.mjs\";\nimport { uniformParsers } from \"./uniformParsers.mjs\";\nimport { unsafeEvalSupported } from \"./unsafeEvalSupported.mjs\";\nexport {\n  checkMaxIfStatementsInShader,\n  compileShader,\n  defaultValue,\n  generateUniformsSync,\n  getMaxFragmentPrecision,\n  getTestContext,\n  logProgramError,\n  mapSize,\n  mapType,\n  setPrecision,\n  uniformParsers,\n  unsafeEvalSupported\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}