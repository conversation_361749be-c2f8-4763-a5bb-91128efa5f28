{"ast": null, "code": "import \"../settings.mjs\";\nimport { settings } from \"@pixi/settings\";\nfunction getResolutionOfUrl(url, defaultValue = 1) {\n  const resolution = settings.RETINA_PREFIX?.exec(url);\n  return resolution ? parseFloat(resolution[1]) : defaultValue;\n}\nexport { getResolutionOfUrl };", "map": {"version": 3, "names": ["getResolutionOfUrl", "url", "defaultValue", "resolution", "settings", "RETINA_PREFIX", "exec", "parseFloat"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\utils\\src\\network\\getResolutionOfUrl.ts"], "sourcesContent": ["import { settings } from '../settings';\n\n/**\n * get the resolution / device pixel ratio of an asset by looking for the prefix\n * used by spritesheets and image urls\n * @memberof PIXI.utils\n * @function getResolutionOfUrl\n * @param {string} url - the image path\n * @param {number} [defaultValue=1] - the defaultValue if no filename prefix is set.\n * @returns {number} resolution / device pixel ratio of an asset\n */\nexport function getResolutionOfUrl(url: string, defaultValue = 1): number\n{\n    const resolution = settings.RETINA_PREFIX?.exec(url);\n\n    if (resolution)\n    {\n        return parseFloat(resolution[1]);\n    }\n\n    return defaultValue;\n}\n"], "mappings": ";;AAWgB,SAAAA,mBAAmBC,GAAA,EAAaC,YAAA,GAAe,GAC/D;EACI,MAAMC,UAAA,GAAaC,QAAA,CAASC,aAAA,EAAeC,IAAA,CAAKL,GAAG;EAEnD,OAAIE,UAAA,GAEOI,UAAA,CAAWJ,UAAA,CAAW,CAAC,CAAC,IAG5BD,YAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}