using UnityEngine;
using System.Collections.Generic;

[System.Serializable]
public class PopGroup
{
    public string culture;
    public string profession;
    public int size;
    public float happiness;
    public float militancy;
}

public class PopSystem : MonoBehaviour
{
    [Header("Population Settings")]
    public float baseGrowthRate = 0.02f; // 2% per turn
    public float happinessGrowthBonus = 0.01f;
    public float militancyGrowthPenalty = 0.005f;

    private Dictionary<string, List<PopGroup>> provincePopulations = new Dictionary<string, List<PopGroup>>();

    void Start()
    {
        InitializePopulations();
    }

    void InitializePopulations()
    {
        // Initialize basic population groups for each province
        var mapManager = FindObjectOfType<MapManager>();
        if (mapManager != null)
        {
            var provinces = mapManager.GetAllProvinces();
            foreach (var province in provinces)
            {
                InitializeProvincePopulation(province);
            }
        }

        Debug.Log("PopSystem initialized with population groups.");
    }

    void InitializeProvincePopulation(Province province)
    {
        if (province == null) return;

        var popGroups = new List<PopGroup>();

        // Create basic population groups based on province population
        int totalPop = province.Population;

        // Farmers (60% of population)
        popGroups.Add(new PopGroup
        {
            culture = "Local",
            profession = "Farmer",
            size = Mathf.RoundToInt(totalPop * 0.6f),
            happiness = Random.Range(0.4f, 0.8f),
            militancy = Random.Range(0.1f, 0.3f)
        });

        // Craftsmen (25% of population)
        popGroups.Add(new PopGroup
        {
            culture = "Local",
            profession = "Craftsman",
            size = Mathf.RoundToInt(totalPop * 0.25f),
            happiness = Random.Range(0.5f, 0.7f),
            militancy = Random.Range(0.2f, 0.4f)
        });

        // Aristocrats (15% of population)
        popGroups.Add(new PopGroup
        {
            culture = "Local",
            profession = "Aristocrat",
            size = Mathf.RoundToInt(totalPop * 0.15f),
            happiness = Random.Range(0.7f, 0.9f),
            militancy = Random.Range(0.0f, 0.2f)
        });

        provincePopulations[province.Name] = popGroups;
    }

    public void UpdateSystem()
    {
        foreach (var kvp in provincePopulations)
        {
            UpdateProvincePopulation(kvp.Key, kvp.Value);
        }

        Debug.Log("PopSystem updated for all provinces.");
    }

    void UpdateProvincePopulation(string provinceName, List<PopGroup> popGroups)
    {
        foreach (var popGroup in popGroups)
        {
            // Calculate growth rate based on happiness and militancy
            float growthRate = baseGrowthRate;
            growthRate += (popGroup.happiness - 0.5f) * happinessGrowthBonus;
            growthRate -= popGroup.militancy * militancyGrowthPenalty;

            // Apply growth
            popGroup.size = Mathf.RoundToInt(popGroup.size * (1 + growthRate));

            // Update happiness and militancy (simplified)
            popGroup.happiness += Random.Range(-0.05f, 0.05f);
            popGroup.happiness = Mathf.Clamp01(popGroup.happiness);

            popGroup.militancy += Random.Range(-0.02f, 0.02f);
            popGroup.militancy = Mathf.Clamp01(popGroup.militancy);
        }
    }

    public List<PopGroup> GetProvincePopulation(string provinceName)
    {
        if (provincePopulations.ContainsKey(provinceName))
        {
            return new List<PopGroup>(provincePopulations[provinceName]);
        }
        return new List<PopGroup>();
    }

    public int GetTotalPopulation(string provinceName)
    {
        if (provincePopulations.ContainsKey(provinceName))
        {
            int total = 0;
            foreach (var popGroup in provincePopulations[provinceName])
            {
                total += popGroup.size;
            }
            return total;
        }
        return 0;
    }

    public float GetAverageHappiness(string provinceName)
    {
        if (provincePopulations.ContainsKey(provinceName))
        {
            float totalHappiness = 0f;
            int totalPops = 0;

            foreach (var popGroup in provincePopulations[provinceName])
            {
                totalHappiness += popGroup.happiness * popGroup.size;
                totalPops += popGroup.size;
            }

            return totalPops > 0 ? totalHappiness / totalPops : 0f;
        }
        return 0f;
    }
}
