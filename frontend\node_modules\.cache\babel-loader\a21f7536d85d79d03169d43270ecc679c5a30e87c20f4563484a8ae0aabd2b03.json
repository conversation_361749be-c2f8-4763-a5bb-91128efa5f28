{"ast": null, "code": "import { settings } from \"@pixi/core\";\nimport { XMLFormat } from \"./XMLFormat.mjs\";\nclass XMLStringFormat {\n  /**\n   * Check if resource refers to text xml font data.\n   * @param data\n   * @returns - True if resource could be treated as font data, false otherwise.\n   */\n  static test(data) {\n    return typeof data == \"string\" && data.includes(\"<font>\") ? XMLFormat.test(settings.ADAPTER.parseXML(data)) : !1;\n  }\n  /**\n   * Convert the text XML into BitmapFontData that we can use.\n   * @param xmlTxt\n   * @returns - Data to use for BitmapFont\n   */\n  static parse(xmlTxt) {\n    return XMLFormat.parse(settings.ADAPTER.parseXML(xmlTxt));\n  }\n}\nexport { XMLStringFormat };", "map": {"version": 3, "names": ["XMLStringFormat", "test", "data", "includes", "XMLFormat", "settings", "ADAPTER", "parseXML", "parse", "xmlTxt"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\text-bitmap\\src\\formats\\XMLStringFormat.ts"], "sourcesContent": ["import { settings } from '@pixi/core';\nimport { XMLFormat } from './XMLFormat';\n\nimport type { BitmapFontData } from '../BitmapFontData';\n\n/**\n * BitmapFont format that's XML-based.\n * @private\n */\nexport class XMLStringFormat\n{\n    /**\n     * Check if resource refers to text xml font data.\n     * @param data\n     * @returns - True if resource could be treated as font data, false otherwise.\n     */\n    static test(data: string | XMLDocument | BitmapFontData): boolean\n    {\n        if (typeof data === 'string' && data.includes('<font>'))\n        {\n            return XMLFormat.test(settings.ADAPTER.parseXML(data));\n        }\n\n        return false;\n    }\n\n    /**\n     * Convert the text XML into BitmapFontData that we can use.\n     * @param xmlTxt\n     * @returns - Data to use for BitmapFont\n     */\n    static parse(xmlTxt: string): BitmapFontData\n    {\n        return XMLFormat.parse(settings.ADAPTER.parseXML(xmlTxt));\n    }\n}\n"], "mappings": ";;AASO,MAAMA,eAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;EAMI,OAAOC,KAAKC,IAAA,EACZ;IACI,OAAI,OAAOA,IAAA,IAAS,YAAYA,IAAA,CAAKC,QAAA,CAAS,QAAQ,IAE3CC,SAAA,CAAUH,IAAA,CAAKI,QAAA,CAASC,OAAA,CAAQC,QAAA,CAASL,IAAI,CAAC,IAGlD;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,OAAOM,MAAMC,MAAA,EACb;IACI,OAAOL,SAAA,CAAUI,KAAA,CAAMH,QAAA,CAASC,OAAA,CAAQC,QAAA,CAASE,MAAM,CAAC;EAC5D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}