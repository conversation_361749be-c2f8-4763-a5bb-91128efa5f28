{"ast": null, "code": "import { FederatedMouseEvent } from \"./FederatedMouseEvent.mjs\";\nclass FederatedPointerEvent extends FederatedMouseEvent {\n  constructor() {\n    super(...arguments), this.width = 0, this.height = 0, this.isPrimary = !1;\n  }\n  // Only included for completeness for now\n  getCoalescedEvents() {\n    return this.type === \"pointermove\" || this.type === \"mousemove\" || this.type === \"touchmove\" ? [this] : [];\n  }\n  // Only included for completeness for now\n  getPredictedEvents() {\n    throw new Error(\"getPredictedEvents is not supported!\");\n  }\n}\nexport { FederatedPointerEvent };", "map": {"version": 3, "names": ["FederatedPointerEvent", "FederatedMouseEvent", "constructor", "arguments", "width", "height", "isPrimary", "getCoalescedEvents", "type", "getPredictedEvents", "Error"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\events\\src\\FederatedPointerEvent.ts"], "sourcesContent": ["import { FederatedMouseEvent } from './FederatedMouseEvent';\n\n/**\n * A {@link PIXI.FederatedEvent} for pointer events.\n * @memberof PIXI\n */\nexport class FederatedPointerEvent extends FederatedMouseEvent implements PointerEvent\n{\n    /**\n     * The unique identifier of the pointer.\n     * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/pointerId}\n     */\n    public pointerId: number;\n\n    /**\n     * The width of the pointer's contact along the x-axis, measured in CSS pixels.\n     * radiusX of TouchEvents will be represented by this value.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/width\n     */\n    public width = 0;\n\n    /**\n     * The height of the pointer's contact along the y-axis, measured in CSS pixels.\n     * radiusY of TouchEvents will be represented by this value.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/height\n     */\n    public height = 0;\n\n    /**\n     * Indicates whether or not the pointer device that created the event is the primary pointer.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/isPrimary\n     */\n    public isPrimary = false;\n\n    /**\n     * The type of pointer that triggered the event.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/pointerType\n     */\n    public pointerType: string;\n\n    /**\n     * Pressure applied by the pointing device during the event.\n     *s\n     * A Touch's force property will be represented by this value.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/pressure\n     */\n    public pressure: number;\n\n    /**\n     * Barrel pressure on a stylus pointer.\n     * @see https://w3c.github.io/pointerevents/#pointerevent-interface\n     */\n    public tangentialPressure: number;\n\n    /**\n     * The angle, in degrees, between the pointer device and the screen.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/tiltX\n     */\n    public tiltX: number;\n\n    /**\n     * The angle, in degrees, between the pointer device and the screen.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/tiltY\n     */\n    public tiltY: number;\n\n    /**\n     * Twist of a stylus pointer.\n     * @see https://w3c.github.io/pointerevents/#pointerevent-interface\n     */\n    public twist: number;\n\n    /**\n     * The angle in radians of a pointer or stylus measuring the vertical angle between\n     * the device's surface to the pointer or stylus.\n     * A stylus at 0 degrees would be directly parallel whereas at π/2 degrees it would be perpendicular.\n     * @see https://developer.mozilla.org/docs/Web/API/PointerEvent/altitudeAngle)\n     */\n    public altitudeAngle: number;\n\n    /**\n     * The angle in radians of a pointer or stylus measuring an arc from the X axis of the device to\n     * the pointer or stylus projected onto the screen's plane.\n     * A stylus at 0 degrees would be pointing to the \"0 o'clock\" whereas at π/2 degrees it would be pointing at \"6 o'clock\".\n     * @see https://developer.mozilla.org/docs/Web/API/PointerEvent/azimuthAngle)\n     */\n    public azimuthAngle: number;\n\n    /** This is the number of clicks that occurs in 200ms/click of each other. */\n    public detail: number;\n\n    // Only included for completeness for now\n    getCoalescedEvents(): PointerEvent[]\n    {\n        if (this.type === 'pointermove' || this.type === 'mousemove' || this.type === 'touchmove')\n        {\n            return [this];\n        }\n\n        return [];\n    }\n\n    // Only included for completeness for now\n    getPredictedEvents(): PointerEvent[]\n    {\n        throw new Error('getPredictedEvents is not supported!');\n    }\n}\n"], "mappings": ";AAMO,MAAMA,qBAAA,SAA8BC,mBAAA,CAC3C;EADOC,YAAA;IAAA,SAAAC,SAAA,GAaH,KAAOC,KAAA,GAAQ,GAOf,KAAOC,MAAA,GAAS,GAMhB,KAAOC,SAAA,GAAY;EAAA;EAAA;EA4DnBC,mBAAA,EACA;IACI,OAAI,KAAKC,IAAA,KAAS,iBAAiB,KAAKA,IAAA,KAAS,eAAe,KAAKA,IAAA,KAAS,cAEnE,CAAC,IAAI,IAGT;EACX;EAAA;EAGAC,mBAAA,EACA;IACU,UAAIC,KAAA,CAAM,sCAAsC;EAC1D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}