{"ast": null, "code": "import { Texture } from \"@pixi/core\";\nclass FillStyle {\n  constructor() {\n    this.color = 16777215, this.alpha = 1, this.texture = Texture.WHITE, this.matrix = null, this.visible = !1, this.reset();\n  }\n  /** Clones the object */\n  clone() {\n    const obj = new FillStyle();\n    return obj.color = this.color, obj.alpha = this.alpha, obj.texture = this.texture, obj.matrix = this.matrix, obj.visible = this.visible, obj;\n  }\n  /** Reset */\n  reset() {\n    this.color = 16777215, this.alpha = 1, this.texture = Texture.WHITE, this.matrix = null, this.visible = !1;\n  }\n  /** Destroy and don't use after this. */\n  destroy() {\n    this.texture = null, this.matrix = null;\n  }\n}\nexport { FillStyle };", "map": {"version": 3, "names": ["FillStyle", "constructor", "color", "alpha", "texture", "Texture", "WHITE", "matrix", "visible", "reset", "clone", "obj", "destroy"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\graphics\\src\\styles\\FillStyle.ts"], "sourcesContent": ["import { Texture } from '@pixi/core';\n\nimport type { Matrix } from '@pixi/core';\n\n/**\n * Fill style object for Graphics.\n * @memberof PIXI\n */\nexport class FillStyle\n{\n    /**\n     * The hex color value used when coloring the Graphics object.\n     * @default 0xFFFFFF\n     */\n    public color = 0xFFFFFF;\n\n    /** The alpha value used when filling the Graphics object. */\n    public alpha = 1.0;\n\n    /**\n     * The texture to be used for the fill.\n     * @default 0\n     */\n    public texture: Texture = Texture.WHITE;\n\n    /**\n     * The transform applied to the texture.\n     * @default null\n     */\n    public matrix: Matrix = null;\n\n    /** If the current fill is visible. */\n    public visible = false;\n\n    constructor()\n    {\n        this.reset();\n    }\n\n    /** Clones the object */\n    public clone(): FillStyle\n    {\n        const obj = new FillStyle();\n\n        obj.color = this.color;\n        obj.alpha = this.alpha;\n        obj.texture = this.texture;\n        obj.matrix = this.matrix;\n        obj.visible = this.visible;\n\n        return obj;\n    }\n\n    /** Reset */\n    public reset(): void\n    {\n        this.color = 0xFFFFFF;\n        this.alpha = 1;\n        this.texture = Texture.WHITE;\n        this.matrix = null;\n        this.visible = false;\n    }\n\n    /** Destroy and don't use after this. */\n    public destroy(): void\n    {\n        this.texture = null;\n        this.matrix = null;\n    }\n}\n"], "mappings": ";AAQO,MAAMA,SAAA,CACb;EAyBIC,YAAA,EACA;IArBA,KAAOC,KAAA,GAAQ,UAGf,KAAOC,KAAA,GAAQ,GAMf,KAAOC,OAAA,GAAmBC,OAAA,CAAQC,KAAA,EAMlC,KAAOC,MAAA,GAAiB,MAGxB,KAAOC,OAAA,GAAU,IAIb,KAAKC,KAAA,CAAM;EACf;EAAA;EAGOC,MAAA,EACP;IACU,MAAAC,GAAA,GAAM,IAAIX,SAAA;IAEhB,OAAAW,GAAA,CAAIT,KAAA,GAAQ,KAAKA,KAAA,EACjBS,GAAA,CAAIR,KAAA,GAAQ,KAAKA,KAAA,EACjBQ,GAAA,CAAIP,OAAA,GAAU,KAAKA,OAAA,EACnBO,GAAA,CAAIJ,MAAA,GAAS,KAAKA,MAAA,EAClBI,GAAA,CAAIH,OAAA,GAAU,KAAKA,OAAA,EAEZG,GAAA;EACX;EAAA;EAGOF,MAAA,EACP;IACI,KAAKP,KAAA,GAAQ,UACb,KAAKC,KAAA,GAAQ,GACb,KAAKC,OAAA,GAAUC,OAAA,CAAQC,KAAA,EACvB,KAAKC,MAAA,GAAS,MACd,KAAKC,OAAA,GAAU;EACnB;EAAA;EAGOI,QAAA,EACP;IACS,KAAAR,OAAA,GAAU,MACf,KAAKG,MAAA,GAAS;EAClB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}