{"ast": null, "code": "import { TYPES, FORMATS } from \"@pixi/constants\";\nfunction mapTypeAndFormatToInternalFormat(gl) {\n  let table;\n  return \"WebGL2RenderingContext\" in globalThis && gl instanceof globalThis.WebGL2RenderingContext ? table = {\n    [TYPES.UNSIGNED_BYTE]: {\n      [FORMATS.RGBA]: gl.RGBA8,\n      [FORMATS.RGB]: gl.RGB8,\n      [FORMATS.RG]: gl.RG8,\n      [FORMATS.RED]: gl.R8,\n      [FORMATS.RGBA_INTEGER]: gl.RGBA8UI,\n      [FORMATS.RGB_INTEGER]: gl.RGB8UI,\n      [FORMATS.RG_INTEGER]: gl.RG8UI,\n      [FORMATS.RED_INTEGER]: gl.R8UI,\n      [FORMATS.ALPHA]: gl.ALPHA,\n      [FORMATS.LUMINANCE]: gl.LUMINANCE,\n      [FORMATS.LUMINANCE_ALPHA]: gl.LUMINANCE_ALPHA\n    },\n    [TYPES.BYTE]: {\n      [FORMATS.RGBA]: gl.RGBA8_SNORM,\n      [FORMATS.RGB]: gl.RGB8_SNORM,\n      [FORMATS.RG]: gl.RG8_SNORM,\n      [FORMATS.RED]: gl.R8_SNORM,\n      [FORMATS.RGBA_INTEGER]: gl.RGBA8I,\n      [FORMATS.RGB_INTEGER]: gl.RGB8I,\n      [FORMATS.RG_INTEGER]: gl.RG8I,\n      [FORMATS.RED_INTEGER]: gl.R8I\n    },\n    [TYPES.UNSIGNED_SHORT]: {\n      [FORMATS.RGBA_INTEGER]: gl.RGBA16UI,\n      [FORMATS.RGB_INTEGER]: gl.RGB16UI,\n      [FORMATS.RG_INTEGER]: gl.RG16UI,\n      [FORMATS.RED_INTEGER]: gl.R16UI,\n      [FORMATS.DEPTH_COMPONENT]: gl.DEPTH_COMPONENT16\n    },\n    [TYPES.SHORT]: {\n      [FORMATS.RGBA_INTEGER]: gl.RGBA16I,\n      [FORMATS.RGB_INTEGER]: gl.RGB16I,\n      [FORMATS.RG_INTEGER]: gl.RG16I,\n      [FORMATS.RED_INTEGER]: gl.R16I\n    },\n    [TYPES.UNSIGNED_INT]: {\n      [FORMATS.RGBA_INTEGER]: gl.RGBA32UI,\n      [FORMATS.RGB_INTEGER]: gl.RGB32UI,\n      [FORMATS.RG_INTEGER]: gl.RG32UI,\n      [FORMATS.RED_INTEGER]: gl.R32UI,\n      [FORMATS.DEPTH_COMPONENT]: gl.DEPTH_COMPONENT24\n    },\n    [TYPES.INT]: {\n      [FORMATS.RGBA_INTEGER]: gl.RGBA32I,\n      [FORMATS.RGB_INTEGER]: gl.RGB32I,\n      [FORMATS.RG_INTEGER]: gl.RG32I,\n      [FORMATS.RED_INTEGER]: gl.R32I\n    },\n    [TYPES.FLOAT]: {\n      [FORMATS.RGBA]: gl.RGBA32F,\n      [FORMATS.RGB]: gl.RGB32F,\n      [FORMATS.RG]: gl.RG32F,\n      [FORMATS.RED]: gl.R32F,\n      [FORMATS.DEPTH_COMPONENT]: gl.DEPTH_COMPONENT32F\n    },\n    [TYPES.HALF_FLOAT]: {\n      [FORMATS.RGBA]: gl.RGBA16F,\n      [FORMATS.RGB]: gl.RGB16F,\n      [FORMATS.RG]: gl.RG16F,\n      [FORMATS.RED]: gl.R16F\n    },\n    [TYPES.UNSIGNED_SHORT_5_6_5]: {\n      [FORMATS.RGB]: gl.RGB565\n    },\n    [TYPES.UNSIGNED_SHORT_4_4_4_4]: {\n      [FORMATS.RGBA]: gl.RGBA4\n    },\n    [TYPES.UNSIGNED_SHORT_5_5_5_1]: {\n      [FORMATS.RGBA]: gl.RGB5_A1\n    },\n    [TYPES.UNSIGNED_INT_2_10_10_10_REV]: {\n      [FORMATS.RGBA]: gl.RGB10_A2,\n      [FORMATS.RGBA_INTEGER]: gl.RGB10_A2UI\n    },\n    [TYPES.UNSIGNED_INT_10F_11F_11F_REV]: {\n      [FORMATS.RGB]: gl.R11F_G11F_B10F\n    },\n    [TYPES.UNSIGNED_INT_5_9_9_9_REV]: {\n      [FORMATS.RGB]: gl.RGB9_E5\n    },\n    [TYPES.UNSIGNED_INT_24_8]: {\n      [FORMATS.DEPTH_STENCIL]: gl.DEPTH24_STENCIL8\n    },\n    [TYPES.FLOAT_32_UNSIGNED_INT_24_8_REV]: {\n      [FORMATS.DEPTH_STENCIL]: gl.DEPTH32F_STENCIL8\n    }\n  } : table = {\n    [TYPES.UNSIGNED_BYTE]: {\n      [FORMATS.RGBA]: gl.RGBA,\n      [FORMATS.RGB]: gl.RGB,\n      [FORMATS.ALPHA]: gl.ALPHA,\n      [FORMATS.LUMINANCE]: gl.LUMINANCE,\n      [FORMATS.LUMINANCE_ALPHA]: gl.LUMINANCE_ALPHA\n    },\n    [TYPES.UNSIGNED_SHORT_5_6_5]: {\n      [FORMATS.RGB]: gl.RGB\n    },\n    [TYPES.UNSIGNED_SHORT_4_4_4_4]: {\n      [FORMATS.RGBA]: gl.RGBA\n    },\n    [TYPES.UNSIGNED_SHORT_5_5_5_1]: {\n      [FORMATS.RGBA]: gl.RGBA\n    }\n  }, table;\n}\nexport { mapTypeAndFormatToInternalFormat };", "map": {"version": 3, "names": ["mapTypeAndFormatToInternalFormat", "gl", "table", "globalThis", "WebGL2RenderingContext", "TYPES", "UNSIGNED_BYTE", "FORMATS", "RGBA", "RGBA8", "RGB", "RGB8", "RG", "RG8", "RED", "R8", "RGBA_INTEGER", "RGBA8UI", "RGB_INTEGER", "RGB8UI", "RG_INTEGER", "RG8UI", "RED_INTEGER", "R8UI", "ALPHA", "LUMINANCE", "LUMINANCE_ALPHA", "BYTE", "RGBA8_SNORM", "RGB8_SNORM", "RG8_SNORM", "R8_SNORM", "RGBA8I", "RGB8I", "RG8I", "R8I", "UNSIGNED_SHORT", "RGBA16UI", "RGB16UI", "RG16UI", "R16UI", "DEPTH_COMPONENT", "DEPTH_COMPONENT16", "SHORT", "RGBA16I", "RGB16I", "RG16I", "R16I", "UNSIGNED_INT", "RGBA32UI", "RGB32UI", "RG32UI", "R32UI", "DEPTH_COMPONENT24", "INT", "RGBA32I", "RGB32I", "RG32I", "R32I", "FLOAT", "RGBA32F", "RGB32F", "RG32F", "R32F", "DEPTH_COMPONENT32F", "HALF_FLOAT", "RGBA16F", "RGB16F", "RG16F", "R16F", "UNSIGNED_SHORT_5_6_5", "RGB565", "UNSIGNED_SHORT_4_4_4_4", "RGBA4", "UNSIGNED_SHORT_5_5_5_1", "RGB5_A1", "UNSIGNED_INT_2_10_10_10_REV", "RGB10_A2", "RGB10_A2UI", "UNSIGNED_INT_10F_11F_11F_REV", "R11F_G11F_B10F", "UNSIGNED_INT_5_9_9_9_REV", "RGB9_E5", "UNSIGNED_INT_24_8", "DEPTH_STENCIL", "DEPTH24_STENCIL8", "FLOAT_32_UNSIGNED_INT_24_8_REV", "DEPTH32F_STENCIL8"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\textures\\utils\\mapTypeAndFormatToInternalFormat.ts"], "sourcesContent": ["import { FORMATS, TYPES } from '@pixi/constants';\n\n/**\n * Returns a lookup table that maps each type-format pair to a compatible internal format.\n * @memberof PIXI\n * @function mapTypeAndFormatToInternalFormat\n * @private\n * @param {WebGLRenderingContext} gl - The rendering context.\n * @returns Lookup table.\n */\nexport function mapTypeAndFormatToInternalFormat(gl: WebGLRenderingContextBase):\n{ [type: number]: { [format: number]: number } }\n{\n    let table;\n\n    if ('WebGL2RenderingContext' in globalThis && gl instanceof globalThis.WebGL2RenderingContext)\n    {\n        table = {\n            [TYPES.UNSIGNED_BYTE]: {\n                [FORMATS.RGBA]: gl.RGBA8,\n                [FORMATS.RGB]: gl.RGB8,\n                [FORMATS.RG]: gl.RG8,\n                [FORMATS.RED]: gl.R8,\n                [FORMATS.RGBA_INTEGER]: gl.RGBA8UI,\n                [FORMATS.RGB_INTEGER]: gl.RGB8UI,\n                [FORMATS.RG_INTEGER]: gl.RG8UI,\n                [FORMATS.RED_INTEGER]: gl.R8UI,\n                [FORMATS.ALPHA]: gl.ALPHA,\n                [FORMATS.LUMINANCE]: gl.LUMINANCE,\n                [FORMATS.LUMINANCE_ALPHA]: gl.LUMINANCE_ALPHA,\n            },\n            [TYPES.BYTE]: {\n                [FORMATS.RGBA]: gl.RGBA8_SNORM,\n                [FORMATS.RGB]: gl.RGB8_SNORM,\n                [FORMATS.RG]: gl.RG8_SNORM,\n                [FORMATS.RED]: gl.R8_SNORM,\n                [FORMATS.RGBA_INTEGER]: gl.RGBA8I,\n                [FORMATS.RGB_INTEGER]: gl.RGB8I,\n                [FORMATS.RG_INTEGER]: gl.RG8I,\n                [FORMATS.RED_INTEGER]: gl.R8I,\n            },\n            [TYPES.UNSIGNED_SHORT]: {\n                [FORMATS.RGBA_INTEGER]: gl.RGBA16UI,\n                [FORMATS.RGB_INTEGER]: gl.RGB16UI,\n                [FORMATS.RG_INTEGER]: gl.RG16UI,\n                [FORMATS.RED_INTEGER]: gl.R16UI,\n                [FORMATS.DEPTH_COMPONENT]: gl.DEPTH_COMPONENT16,\n            },\n            [TYPES.SHORT]: {\n                [FORMATS.RGBA_INTEGER]: gl.RGBA16I,\n                [FORMATS.RGB_INTEGER]: gl.RGB16I,\n                [FORMATS.RG_INTEGER]: gl.RG16I,\n                [FORMATS.RED_INTEGER]: gl.R16I,\n            },\n            [TYPES.UNSIGNED_INT]: {\n                [FORMATS.RGBA_INTEGER]: gl.RGBA32UI,\n                [FORMATS.RGB_INTEGER]: gl.RGB32UI,\n                [FORMATS.RG_INTEGER]: gl.RG32UI,\n                [FORMATS.RED_INTEGER]: gl.R32UI,\n                [FORMATS.DEPTH_COMPONENT]: gl.DEPTH_COMPONENT24,\n            },\n            [TYPES.INT]: {\n                [FORMATS.RGBA_INTEGER]: gl.RGBA32I,\n                [FORMATS.RGB_INTEGER]: gl.RGB32I,\n                [FORMATS.RG_INTEGER]: gl.RG32I,\n                [FORMATS.RED_INTEGER]: gl.R32I,\n            },\n            [TYPES.FLOAT]: {\n                [FORMATS.RGBA]: gl.RGBA32F,\n                [FORMATS.RGB]: gl.RGB32F,\n                [FORMATS.RG]: gl.RG32F,\n                [FORMATS.RED]: gl.R32F,\n                [FORMATS.DEPTH_COMPONENT]: gl.DEPTH_COMPONENT32F,\n            },\n            [TYPES.HALF_FLOAT]: {\n                [FORMATS.RGBA]: gl.RGBA16F,\n                [FORMATS.RGB]: gl.RGB16F,\n                [FORMATS.RG]: gl.RG16F,\n                [FORMATS.RED]: gl.R16F,\n            },\n            [TYPES.UNSIGNED_SHORT_5_6_5]: {\n                [FORMATS.RGB]: gl.RGB565,\n            },\n            [TYPES.UNSIGNED_SHORT_4_4_4_4]: {\n                [FORMATS.RGBA]: gl.RGBA4,\n            },\n            [TYPES.UNSIGNED_SHORT_5_5_5_1]: {\n                [FORMATS.RGBA]: gl.RGB5_A1,\n            },\n            [TYPES.UNSIGNED_INT_2_10_10_10_REV]: {\n                [FORMATS.RGBA]: gl.RGB10_A2,\n                [FORMATS.RGBA_INTEGER]: gl.RGB10_A2UI,\n            },\n            [TYPES.UNSIGNED_INT_10F_11F_11F_REV]: {\n                [FORMATS.RGB]: gl.R11F_G11F_B10F,\n            },\n            [TYPES.UNSIGNED_INT_5_9_9_9_REV]: {\n                [FORMATS.RGB]: gl.RGB9_E5,\n            },\n            [TYPES.UNSIGNED_INT_24_8]: {\n                [FORMATS.DEPTH_STENCIL]: gl.DEPTH24_STENCIL8,\n            },\n            [TYPES.FLOAT_32_UNSIGNED_INT_24_8_REV]: {\n                [FORMATS.DEPTH_STENCIL]: gl.DEPTH32F_STENCIL8,\n            },\n        };\n    }\n    else\n    {\n        table = {\n            [TYPES.UNSIGNED_BYTE]: {\n                [FORMATS.RGBA]: gl.RGBA,\n                [FORMATS.RGB]: gl.RGB,\n                [FORMATS.ALPHA]: gl.ALPHA,\n                [FORMATS.LUMINANCE]: gl.LUMINANCE,\n                [FORMATS.LUMINANCE_ALPHA]: gl.LUMINANCE_ALPHA,\n            },\n            [TYPES.UNSIGNED_SHORT_5_6_5]: {\n                [FORMATS.RGB]: gl.RGB,\n            },\n            [TYPES.UNSIGNED_SHORT_4_4_4_4]: {\n                [FORMATS.RGBA]: gl.RGBA,\n            },\n            [TYPES.UNSIGNED_SHORT_5_5_5_1]: {\n                [FORMATS.RGBA]: gl.RGBA,\n            },\n        };\n    }\n\n    return table;\n}\n"], "mappings": ";AAUO,SAASA,iCAAiCC,EAAA,EAEjD;EACQ,IAAAC,KAAA;EAEJ,OAAI,4BAA4BC,UAAA,IAAcF,EAAA,YAAcE,UAAA,CAAWC,sBAAA,GAEnEF,KAAA,GAAQ;IACJ,CAACG,KAAA,CAAMC,aAAa,GAAG;MACnB,CAACC,OAAA,CAAQC,IAAI,GAAGP,EAAA,CAAGQ,KAAA;MACnB,CAACF,OAAA,CAAQG,GAAG,GAAGT,EAAA,CAAGU,IAAA;MAClB,CAACJ,OAAA,CAAQK,EAAE,GAAGX,EAAA,CAAGY,GAAA;MACjB,CAACN,OAAA,CAAQO,GAAG,GAAGb,EAAA,CAAGc,EAAA;MAClB,CAACR,OAAA,CAAQS,YAAY,GAAGf,EAAA,CAAGgB,OAAA;MAC3B,CAACV,OAAA,CAAQW,WAAW,GAAGjB,EAAA,CAAGkB,MAAA;MAC1B,CAACZ,OAAA,CAAQa,UAAU,GAAGnB,EAAA,CAAGoB,KAAA;MACzB,CAACd,OAAA,CAAQe,WAAW,GAAGrB,EAAA,CAAGsB,IAAA;MAC1B,CAAChB,OAAA,CAAQiB,KAAK,GAAGvB,EAAA,CAAGuB,KAAA;MACpB,CAACjB,OAAA,CAAQkB,SAAS,GAAGxB,EAAA,CAAGwB,SAAA;MACxB,CAAClB,OAAA,CAAQmB,eAAe,GAAGzB,EAAA,CAAGyB;IAClC;IACA,CAACrB,KAAA,CAAMsB,IAAI,GAAG;MACV,CAACpB,OAAA,CAAQC,IAAI,GAAGP,EAAA,CAAG2B,WAAA;MACnB,CAACrB,OAAA,CAAQG,GAAG,GAAGT,EAAA,CAAG4B,UAAA;MAClB,CAACtB,OAAA,CAAQK,EAAE,GAAGX,EAAA,CAAG6B,SAAA;MACjB,CAACvB,OAAA,CAAQO,GAAG,GAAGb,EAAA,CAAG8B,QAAA;MAClB,CAACxB,OAAA,CAAQS,YAAY,GAAGf,EAAA,CAAG+B,MAAA;MAC3B,CAACzB,OAAA,CAAQW,WAAW,GAAGjB,EAAA,CAAGgC,KAAA;MAC1B,CAAC1B,OAAA,CAAQa,UAAU,GAAGnB,EAAA,CAAGiC,IAAA;MACzB,CAAC3B,OAAA,CAAQe,WAAW,GAAGrB,EAAA,CAAGkC;IAC9B;IACA,CAAC9B,KAAA,CAAM+B,cAAc,GAAG;MACpB,CAAC7B,OAAA,CAAQS,YAAY,GAAGf,EAAA,CAAGoC,QAAA;MAC3B,CAAC9B,OAAA,CAAQW,WAAW,GAAGjB,EAAA,CAAGqC,OAAA;MAC1B,CAAC/B,OAAA,CAAQa,UAAU,GAAGnB,EAAA,CAAGsC,MAAA;MACzB,CAAChC,OAAA,CAAQe,WAAW,GAAGrB,EAAA,CAAGuC,KAAA;MAC1B,CAACjC,OAAA,CAAQkC,eAAe,GAAGxC,EAAA,CAAGyC;IAClC;IACA,CAACrC,KAAA,CAAMsC,KAAK,GAAG;MACX,CAACpC,OAAA,CAAQS,YAAY,GAAGf,EAAA,CAAG2C,OAAA;MAC3B,CAACrC,OAAA,CAAQW,WAAW,GAAGjB,EAAA,CAAG4C,MAAA;MAC1B,CAACtC,OAAA,CAAQa,UAAU,GAAGnB,EAAA,CAAG6C,KAAA;MACzB,CAACvC,OAAA,CAAQe,WAAW,GAAGrB,EAAA,CAAG8C;IAC9B;IACA,CAAC1C,KAAA,CAAM2C,YAAY,GAAG;MAClB,CAACzC,OAAA,CAAQS,YAAY,GAAGf,EAAA,CAAGgD,QAAA;MAC3B,CAAC1C,OAAA,CAAQW,WAAW,GAAGjB,EAAA,CAAGiD,OAAA;MAC1B,CAAC3C,OAAA,CAAQa,UAAU,GAAGnB,EAAA,CAAGkD,MAAA;MACzB,CAAC5C,OAAA,CAAQe,WAAW,GAAGrB,EAAA,CAAGmD,KAAA;MAC1B,CAAC7C,OAAA,CAAQkC,eAAe,GAAGxC,EAAA,CAAGoD;IAClC;IACA,CAAChD,KAAA,CAAMiD,GAAG,GAAG;MACT,CAAC/C,OAAA,CAAQS,YAAY,GAAGf,EAAA,CAAGsD,OAAA;MAC3B,CAAChD,OAAA,CAAQW,WAAW,GAAGjB,EAAA,CAAGuD,MAAA;MAC1B,CAACjD,OAAA,CAAQa,UAAU,GAAGnB,EAAA,CAAGwD,KAAA;MACzB,CAAClD,OAAA,CAAQe,WAAW,GAAGrB,EAAA,CAAGyD;IAC9B;IACA,CAACrD,KAAA,CAAMsD,KAAK,GAAG;MACX,CAACpD,OAAA,CAAQC,IAAI,GAAGP,EAAA,CAAG2D,OAAA;MACnB,CAACrD,OAAA,CAAQG,GAAG,GAAGT,EAAA,CAAG4D,MAAA;MAClB,CAACtD,OAAA,CAAQK,EAAE,GAAGX,EAAA,CAAG6D,KAAA;MACjB,CAACvD,OAAA,CAAQO,GAAG,GAAGb,EAAA,CAAG8D,IAAA;MAClB,CAACxD,OAAA,CAAQkC,eAAe,GAAGxC,EAAA,CAAG+D;IAClC;IACA,CAAC3D,KAAA,CAAM4D,UAAU,GAAG;MAChB,CAAC1D,OAAA,CAAQC,IAAI,GAAGP,EAAA,CAAGiE,OAAA;MACnB,CAAC3D,OAAA,CAAQG,GAAG,GAAGT,EAAA,CAAGkE,MAAA;MAClB,CAAC5D,OAAA,CAAQK,EAAE,GAAGX,EAAA,CAAGmE,KAAA;MACjB,CAAC7D,OAAA,CAAQO,GAAG,GAAGb,EAAA,CAAGoE;IACtB;IACA,CAAChE,KAAA,CAAMiE,oBAAoB,GAAG;MAC1B,CAAC/D,OAAA,CAAQG,GAAG,GAAGT,EAAA,CAAGsE;IACtB;IACA,CAAClE,KAAA,CAAMmE,sBAAsB,GAAG;MAC5B,CAACjE,OAAA,CAAQC,IAAI,GAAGP,EAAA,CAAGwE;IACvB;IACA,CAACpE,KAAA,CAAMqE,sBAAsB,GAAG;MAC5B,CAACnE,OAAA,CAAQC,IAAI,GAAGP,EAAA,CAAG0E;IACvB;IACA,CAACtE,KAAA,CAAMuE,2BAA2B,GAAG;MACjC,CAACrE,OAAA,CAAQC,IAAI,GAAGP,EAAA,CAAG4E,QAAA;MACnB,CAACtE,OAAA,CAAQS,YAAY,GAAGf,EAAA,CAAG6E;IAC/B;IACA,CAACzE,KAAA,CAAM0E,4BAA4B,GAAG;MAClC,CAACxE,OAAA,CAAQG,GAAG,GAAGT,EAAA,CAAG+E;IACtB;IACA,CAAC3E,KAAA,CAAM4E,wBAAwB,GAAG;MAC9B,CAAC1E,OAAA,CAAQG,GAAG,GAAGT,EAAA,CAAGiF;IACtB;IACA,CAAC7E,KAAA,CAAM8E,iBAAiB,GAAG;MACvB,CAAC5E,OAAA,CAAQ6E,aAAa,GAAGnF,EAAA,CAAGoF;IAChC;IACA,CAAChF,KAAA,CAAMiF,8BAA8B,GAAG;MACpC,CAAC/E,OAAA,CAAQ6E,aAAa,GAAGnF,EAAA,CAAGsF;IAChC;EAAA,IAKJrF,KAAA,GAAQ;IACJ,CAACG,KAAA,CAAMC,aAAa,GAAG;MACnB,CAACC,OAAA,CAAQC,IAAI,GAAGP,EAAA,CAAGO,IAAA;MACnB,CAACD,OAAA,CAAQG,GAAG,GAAGT,EAAA,CAAGS,GAAA;MAClB,CAACH,OAAA,CAAQiB,KAAK,GAAGvB,EAAA,CAAGuB,KAAA;MACpB,CAACjB,OAAA,CAAQkB,SAAS,GAAGxB,EAAA,CAAGwB,SAAA;MACxB,CAAClB,OAAA,CAAQmB,eAAe,GAAGzB,EAAA,CAAGyB;IAClC;IACA,CAACrB,KAAA,CAAMiE,oBAAoB,GAAG;MAC1B,CAAC/D,OAAA,CAAQG,GAAG,GAAGT,EAAA,CAAGS;IACtB;IACA,CAACL,KAAA,CAAMmE,sBAAsB,GAAG;MAC5B,CAACjE,OAAA,CAAQC,IAAI,GAAGP,EAAA,CAAGO;IACvB;IACA,CAACH,KAAA,CAAMqE,sBAAsB,GAAG;MAC5B,CAACnE,OAAA,CAAQC,IAAI,GAAGP,EAAA,CAAGO;IACvB;EAID,GAAAN,KAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}