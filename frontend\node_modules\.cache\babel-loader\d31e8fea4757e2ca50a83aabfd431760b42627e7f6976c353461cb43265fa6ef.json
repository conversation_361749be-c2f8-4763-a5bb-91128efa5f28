{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Projects\\\\Python\\\\EU4\\\\frontend\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport axios from \"axios\";\nimport WorldMap from './WorldMap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst INITIAL_STATS = {\n  population: 1000000,\n  resources: 100,\n  stability: 100\n};\nconst WIN_RESOURCES = 1000;\nconst LOSE_STABILITY = 0;\nexport default function App() {\n  _s();\n  var _playerCountry$proper2;\n  const [countries, setCountries] = useState([]);\n  const [playerCountry, setPlayerCountry] = useState(null);\n  const [countryStats, setCountryStats] = useState({});\n  const [turn, setTurn] = useState(1);\n  const [message, setMessage] = useState(\"\");\n  const [actionTaken, setActionTaken] = useState(false);\n  const [event, setEvent] = useState(null);\n  const [diplomacy, setDiplomacy] = useState([]);\n\n  // Load countries on mount\n  useEffect(() => {\n    axios.get(\"http://localhost:8000/countries\").then(r => r.data).then(data => {\n      console.log(\"Fetched countries:\", data); // DEBUG LOG\n      setCountries(data);\n      // Initialize stats for all countries with a valid NAME\n      const stats = {};\n      data.forEach(c => {\n        var _c$properties;\n        const name = c.name || ((_c$properties = c.properties) === null || _c$properties === void 0 ? void 0 : _c$properties.NAME);\n        if (name) {\n          stats[name] = {\n            ...INITIAL_STATS\n          };\n        }\n      });\n      setCountryStats(stats);\n    });\n  }, []);\n\n  // Fetch event and diplomacy at start and on turn change\n  useEffect(() => {\n    var _playerCountry$proper;\n    const countryName = (playerCountry === null || playerCountry === void 0 ? void 0 : playerCountry.name) || (playerCountry === null || playerCountry === void 0 ? void 0 : (_playerCountry$proper = playerCountry.properties) === null || _playerCountry$proper === void 0 ? void 0 : _playerCountry$proper.NAME);\n    if (!countryName) return;\n    axios.get(`http://localhost:8000/event?country=${encodeURIComponent(countryName)}`).then(r => setEvent(r.data));\n    axios.get(`http://localhost:8000/diplomacy?country=${encodeURIComponent(countryName)}`).then(r => setDiplomacy(r.data));\n  }, [turn, playerCountry]);\n\n  // Player picks a country at start\n  if (!countries.length || !Object.keys(countryStats).length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading world data...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 12\n    }, this);\n  }\n  const playerCountryName = (playerCountry === null || playerCountry === void 0 ? void 0 : playerCountry.name) || (playerCountry === null || playerCountry === void 0 ? void 0 : (_playerCountry$proper2 = playerCountry.properties) === null || _playerCountry$proper2 === void 0 ? void 0 : _playerCountry$proper2.NAME);\n  if (!playerCountryName) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Choose Your Country\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(WorldMap, {\n        onSelectCountry: c => {\n          var _c$properties2;\n          const name = (c === null || c === void 0 ? void 0 : c.name) || (c === null || c === void 0 ? void 0 : (_c$properties2 = c.properties) === null || _c$properties2 === void 0 ? void 0 : _c$properties2.NAME);\n          if (name) setPlayerCountry(c);\n        },\n        selectedCountry: null\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Click a country to play as it.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this);\n  }\n  const stats = countryStats[playerCountryName];\n  if (!stats) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading country stats...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Player actions\n  const invest = () => {\n    if (actionTaken) return;\n    setCountryStats(stats => ({\n      ...stats,\n      [playerCountryName]: {\n        ...stats[playerCountryName],\n        resources: stats[playerCountryName].resources + 50,\n        stability: stats[playerCountryName].stability - 2\n      }\n    }));\n    setMessage(\"You invested in your economy! Resources +50, Stability -2\");\n    setActionTaken(true);\n  };\n  const build = () => {\n    if (actionTaken) return;\n    setCountryStats(stats => ({\n      ...stats,\n      [playerCountryName]: {\n        ...stats[playerCountryName],\n        population: Math.floor(stats[playerCountryName].population * 1.02),\n        resources: stats[playerCountryName].resources - 20,\n        stability: stats[playerCountryName].stability + 1\n      }\n    }));\n    setMessage(\"You built infrastructure! Population +2%, Resources -20, Stability +1\");\n    setActionTaken(true);\n  };\n  const propaganda = () => {\n    if (actionTaken) return;\n    setCountryStats(stats => ({\n      ...stats,\n      [playerCountryName]: {\n        ...stats[playerCountryName],\n        stability: stats[playerCountryName].stability + 10,\n        resources: stats[playerCountryName].resources - 10\n      }\n    }));\n    setMessage(\"You ran propaganda! Stability +10, Resources -10\");\n    setActionTaken(true);\n  };\n\n  // Apply event effect to player country\n  const applyEvent = () => {\n    if (!event) return;\n    axios.post(\"http://localhost:8000/event/apply\", {\n      country: playerCountryName,\n      effect: event.effect\n    }).then(res => {\n      if (res.data.success && res.data.country) {\n        setCountryStats(stats => ({\n          ...stats,\n          [playerCountryName]: {\n            ...stats[playerCountryName],\n            ...res.data.country\n          }\n        }));\n        setMessage(`Event applied: ${event.effect}`);\n      } else {\n        setMessage(\"Failed to apply event.\");\n      }\n    });\n  };\n\n  // Perform diplomacy action\n  const performDiplomacy = (action, target) => {\n    axios.post(\"http://localhost:8000/diplomacy/perform\", {\n      country: playerCountryName,\n      action,\n      target\n    }).then(res => {\n      setMessage(res.data.result || \"Diplomacy action performed.\");\n    });\n  };\n\n  // End turn: AI and player stats update\n  const nextTurn = () => {\n    setTurn(t => t + 1);\n    setActionTaken(false);\n    setMessage(\"\");\n    setCountryStats(stats => {\n      const newStats = {\n        ...stats\n      };\n      Object.keys(newStats).forEach(name => {\n        // AI: grow pop/resources, random stability change\n        if (name !== playerCountryName) {\n          newStats[name] = {\n            ...newStats[name],\n            population: Math.floor(newStats[name].population * (1.01 + Math.random() * 0.01)),\n            resources: newStats[name].resources + Math.floor(Math.random() * 20),\n            stability: Math.max(0, Math.min(100, newStats[name].stability + Math.floor(Math.random() * 5 - 2)))\n          };\n        } else {\n          // Player: small passive growth\n          newStats[name] = {\n            ...newStats[name],\n            population: Math.floor(newStats[name].population * 1.01),\n            resources: newStats[name].resources + 5,\n            stability: Math.max(0, Math.min(100, newStats[name].stability))\n          };\n        }\n      });\n      return newStats;\n    });\n  };\n\n  // Win/Lose conditions\n  if (stats.resources >= WIN_RESOURCES) {\n    return /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Victory! You built a prosperous nation.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 12\n    }, this);\n  }\n  if (stats.stability <= LOSE_STABILITY) {\n    return /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Your country collapsed due to instability. Game Over.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Empires & Revolutions\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [\"Turn: \", turn]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(WorldMap, {\n      onSelectCountry: () => {},\n      selectedCountry: playerCountry\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this), event && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#333',\n        color: '#fff',\n        padding: 10,\n        margin: 10,\n        borderRadius: 8\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: [\"Event: \", event.title]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: event.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: 'lightgreen'\n        },\n        children: event.effect\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: applyEvent,\n        children: \"Apply Event Effect\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#222',\n        color: '#fff',\n        padding: 10,\n        margin: 10,\n        borderRadius: 8\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: playerCountryName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Population: \", stats.population.toLocaleString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Resources: \", stats.resources]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Stability: \", stats.stability]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: invest,\n        disabled: actionTaken,\n        children: \"Invest\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: build,\n        disabled: actionTaken,\n        style: {\n          marginLeft: 8\n        },\n        children: \"Build\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: propaganda,\n        disabled: actionTaken,\n        style: {\n          marginLeft: 8\n        },\n        children: \"Propaganda\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#223',\n        color: '#fff',\n        padding: 10,\n        margin: 10,\n        borderRadius: 8\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Diplomacy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), diplomacy.length === 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No diplomatic actions available.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 36\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: diplomacy.map((d, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: [d.action, \" \", d.target && /*#__PURE__*/_jsxDEV(\"b\", {\n            children: [\"with \", d.target]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 39\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              marginLeft: 8\n            },\n            onClick: () => performDiplomacy(d.action, d.target),\n            children: \"Do\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this)]\n        }, i, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: nextTurn,\n      children: \"End Turn\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        color: 'yellow',\n        margin: 10\n      },\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 19\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 185,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"c760sk3bkS0WjL4Xygqt/Hrnv/Y=\");\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "WorldMap", "jsxDEV", "_jsxDEV", "INITIAL_STATS", "population", "resources", "stability", "WIN_RESOURCES", "LOSE_STABILITY", "App", "_s", "_playerCountry$proper2", "countries", "setCountries", "playerCountry", "setPlayerCountry", "countryStats", "setCountryStats", "turn", "setTurn", "message", "setMessage", "actionTaken", "setActionTaken", "event", "setEvent", "diplomacy", "setDiplomacy", "get", "then", "r", "data", "console", "log", "stats", "for<PERSON>ach", "c", "_c$properties", "name", "properties", "NAME", "_playerCountry$proper", "countryName", "encodeURIComponent", "length", "Object", "keys", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "player<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onSelectCountry", "_c$properties2", "selectedCountry", "invest", "build", "Math", "floor", "propaganda", "applyEvent", "post", "country", "effect", "res", "success", "performDiplomacy", "action", "target", "result", "nextTurn", "t", "newStats", "random", "max", "min", "style", "background", "color", "padding", "margin", "borderRadius", "title", "description", "onClick", "toLocaleString", "disabled", "marginLeft", "map", "d", "i", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Projects/Python/EU4/frontend/src/App.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport axios from \"axios\";\r\nimport WorldMap from './WorldMap';\r\n\r\nconst INITIAL_STATS = { population: 1000000, resources: 100, stability: 100 };\r\nconst WIN_RESOURCES = 1000;\r\nconst LOSE_STABILITY = 0;\r\n\r\nexport default function App() {\r\n  const [countries, setCountries] = useState([]);\r\n  const [playerCountry, setPlayerCountry] = useState(null);\r\n  const [countryStats, setCountryStats] = useState({});\r\n  const [turn, setTurn] = useState(1);\r\n  const [message, setMessage] = useState(\"\");\r\n  const [actionTaken, setActionTaken] = useState(false);\r\n  const [event, setEvent] = useState(null);\r\n  const [diplomacy, setDiplomacy] = useState([]);\r\n\r\n  // Load countries on mount\r\n  useEffect(() => {\r\n    axios.get(\"http://localhost:8000/countries\")\r\n      .then(r => r.data)\r\n      .then(data => {\r\n        console.log(\"Fetched countries:\", data); // DEBUG LOG\r\n        setCountries(data);\r\n        // Initialize stats for all countries with a valid NAME\r\n        const stats = {};\r\n        data.forEach(c => {\r\n          const name = c.name || c.properties?.NAME;\r\n          if (name) {\r\n            stats[name] = { ...INITIAL_STATS };\r\n          }\r\n        });\r\n        setCountryStats(stats);\r\n      });\r\n  }, []);\r\n\r\n  // Fetch event and diplomacy at start and on turn change\r\n  useEffect(() => {\r\n    const countryName = playerCountry?.name || playerCountry?.properties?.NAME;\r\n    if (!countryName) return;\r\n    axios.get(`http://localhost:8000/event?country=${encodeURIComponent(countryName)}`)\r\n      .then(r => setEvent(r.data));\r\n    axios.get(`http://localhost:8000/diplomacy?country=${encodeURIComponent(countryName)}`)\r\n      .then(r => setDiplomacy(r.data));\r\n  }, [turn, playerCountry]);\r\n\r\n  // Player picks a country at start\r\n  if (!countries.length || !Object.keys(countryStats).length) {\r\n    return <div>Loading world data...</div>;\r\n  }\r\n  const playerCountryName = playerCountry?.name || playerCountry?.properties?.NAME;\r\n  if (!playerCountryName) {\r\n    return (\r\n      <div>\r\n        <h1>Choose Your Country</h1>\r\n        <WorldMap onSelectCountry={c => {\r\n          const name = c?.name || c?.properties?.NAME;\r\n          if (name) setPlayerCountry(c);\r\n        }} selectedCountry={null} />\r\n        <p>Click a country to play as it.</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const stats = countryStats[playerCountryName];\r\n  if (!stats) {\r\n    return <div>Loading country stats...</div>;\r\n  }\r\n\r\n  // Player actions\r\n  const invest = () => {\r\n    if (actionTaken) return;\r\n    setCountryStats(stats => ({\r\n      ...stats,\r\n      [playerCountryName]: {\r\n        ...stats[playerCountryName],\r\n        resources: stats[playerCountryName].resources + 50,\r\n        stability: stats[playerCountryName].stability - 2\r\n      }\r\n    }));\r\n    setMessage(\"You invested in your economy! Resources +50, Stability -2\");\r\n    setActionTaken(true);\r\n  };\r\n  const build = () => {\r\n    if (actionTaken) return;\r\n    setCountryStats(stats => ({\r\n      ...stats,\r\n      [playerCountryName]: {\r\n        ...stats[playerCountryName],\r\n        population: Math.floor(stats[playerCountryName].population * 1.02),\r\n        resources: stats[playerCountryName].resources - 20,\r\n        stability: stats[playerCountryName].stability + 1\r\n      }\r\n    }));\r\n    setMessage(\"You built infrastructure! Population +2%, Resources -20, Stability +1\");\r\n    setActionTaken(true);\r\n  };\r\n  const propaganda = () => {\r\n    if (actionTaken) return;\r\n    setCountryStats(stats => ({\r\n      ...stats,\r\n      [playerCountryName]: {\r\n        ...stats[playerCountryName],\r\n        stability: stats[playerCountryName].stability + 10,\r\n        resources: stats[playerCountryName].resources - 10\r\n      }\r\n    }));\r\n    setMessage(\"You ran propaganda! Stability +10, Resources -10\");\r\n    setActionTaken(true);\r\n  };\r\n\r\n  // Apply event effect to player country\r\n  const applyEvent = () => {\r\n    if (!event) return;\r\n    axios.post(\"http://localhost:8000/event/apply\", {\r\n      country: playerCountryName,\r\n      effect: event.effect\r\n    }).then(res => {\r\n      if (res.data.success && res.data.country) {\r\n        setCountryStats(stats => ({\r\n          ...stats,\r\n          [playerCountryName]: {\r\n            ...stats[playerCountryName],\r\n            ...res.data.country\r\n          }\r\n        }));\r\n        setMessage(`Event applied: ${event.effect}`);\r\n      } else {\r\n        setMessage(\"Failed to apply event.\");\r\n      }\r\n    });\r\n  };\r\n\r\n  // Perform diplomacy action\r\n  const performDiplomacy = (action, target) => {\r\n    axios.post(\"http://localhost:8000/diplomacy/perform\", {\r\n      country: playerCountryName,\r\n      action,\r\n      target\r\n    }).then(res => {\r\n      setMessage(res.data.result || \"Diplomacy action performed.\");\r\n    });\r\n  };\r\n\r\n  // End turn: AI and player stats update\r\n  const nextTurn = () => {\r\n    setTurn(t => t + 1);\r\n    setActionTaken(false);\r\n    setMessage(\"\");\r\n    setCountryStats(stats => {\r\n      const newStats = { ...stats };\r\n      Object.keys(newStats).forEach(name => {\r\n        // AI: grow pop/resources, random stability change\r\n        if (name !== playerCountryName) {\r\n          newStats[name] = {\r\n            ...newStats[name],\r\n            population: Math.floor(newStats[name].population * (1.01 + Math.random() * 0.01)),\r\n            resources: newStats[name].resources + Math.floor(Math.random() * 20),\r\n            stability: Math.max(0, Math.min(100, newStats[name].stability + Math.floor(Math.random() * 5 - 2)))\r\n          };\r\n        } else {\r\n          // Player: small passive growth\r\n          newStats[name] = {\r\n            ...newStats[name],\r\n            population: Math.floor(newStats[name].population * 1.01),\r\n            resources: newStats[name].resources + 5,\r\n            stability: Math.max(0, Math.min(100, newStats[name].stability))\r\n          };\r\n        }\r\n      });\r\n      return newStats;\r\n    });\r\n  };\r\n\r\n  // Win/Lose conditions\r\n  if (stats.resources >= WIN_RESOURCES) {\r\n    return <h1>Victory! You built a prosperous nation.</h1>;\r\n  }\r\n  if (stats.stability <= LOSE_STABILITY) {\r\n    return <h1>Your country collapsed due to instability. Game Over.</h1>;\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <h1>Empires & Revolutions</h1>\r\n      <div>Turn: {turn}</div>\r\n      <WorldMap onSelectCountry={() => {}} selectedCountry={playerCountry} />\r\n      {/* Event UI */}\r\n      {event && (\r\n        <div style={{ background: '#333', color: '#fff', padding: 10, margin: 10, borderRadius: 8 }}>\r\n          <h3>Event: {event.title}</h3>\r\n          <p>{event.description}</p>\r\n          <p style={{ color: 'lightgreen' }}>{event.effect}</p>\r\n          <button onClick={applyEvent}>Apply Event Effect</button>\r\n        </div>\r\n      )}\r\n      {/* Country stats and actions */}\r\n      <div style={{ background: '#222', color: '#fff', padding: 10, margin: 10, borderRadius: 8 }}>\r\n        <h2>{playerCountryName}</h2>\r\n        <p>Population: {stats.population.toLocaleString()}</p>\r\n        <p>Resources: {stats.resources}</p>\r\n        <p>Stability: {stats.stability}</p>\r\n        <button onClick={invest} disabled={actionTaken}>Invest</button>\r\n        <button onClick={build} disabled={actionTaken} style={{marginLeft: 8}}>Build</button>\r\n        <button onClick={propaganda} disabled={actionTaken} style={{marginLeft: 8}}>Propaganda</button>\r\n      </div>\r\n      {/* Diplomacy UI */}\r\n      <div style={{ background: '#223', color: '#fff', padding: 10, margin: 10, borderRadius: 8 }}>\r\n        <h3>Diplomacy</h3>\r\n        {diplomacy.length === 0 && <p>No diplomatic actions available.</p>}\r\n        <ul>\r\n          {diplomacy.map((d, i) => (\r\n            <li key={i}>\r\n              {d.action} {d.target && <b>with {d.target}</b>}\r\n              <button style={{marginLeft: 8}} onClick={() => performDiplomacy(d.action, d.target)}>Do</button>\r\n            </li>\r\n          ))}\r\n        </ul>\r\n      </div>\r\n      <button onClick={nextTurn}>End Turn</button>\r\n      {message && <div style={{ color: 'yellow', margin: 10 }}>{message}</div>}\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,aAAa,GAAG;EAAEC,UAAU,EAAE,OAAO;EAAEC,SAAS,EAAE,GAAG;EAAEC,SAAS,EAAE;AAAI,CAAC;AAC7E,MAAMC,aAAa,GAAG,IAAI;AAC1B,MAAMC,cAAc,GAAG,CAAC;AAExB,eAAe,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,sBAAA;EAC5B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACoB,IAAI,EAAEC,OAAO,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACAD,SAAS,CAAC,MAAM;IACdE,KAAK,CAAC6B,GAAG,CAAC,iCAAiC,CAAC,CACzCC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CACjBF,IAAI,CAACE,IAAI,IAAI;MACZC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,IAAI,CAAC,CAAC,CAAC;MACzClB,YAAY,CAACkB,IAAI,CAAC;MAClB;MACA,MAAMG,KAAK,GAAG,CAAC,CAAC;MAChBH,IAAI,CAACI,OAAO,CAACC,CAAC,IAAI;QAAA,IAAAC,aAAA;QAChB,MAAMC,IAAI,GAAGF,CAAC,CAACE,IAAI,MAAAD,aAAA,GAAID,CAAC,CAACG,UAAU,cAAAF,aAAA,uBAAZA,aAAA,CAAcG,IAAI;QACzC,IAAIF,IAAI,EAAE;UACRJ,KAAK,CAACI,IAAI,CAAC,GAAG;YAAE,GAAGnC;UAAc,CAAC;QACpC;MACF,CAAC,CAAC;MACFc,eAAe,CAACiB,KAAK,CAAC;IACxB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArC,SAAS,CAAC,MAAM;IAAA,IAAA4C,qBAAA;IACd,MAAMC,WAAW,GAAG,CAAA5B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwB,IAAI,MAAIxB,aAAa,aAAbA,aAAa,wBAAA2B,qBAAA,GAAb3B,aAAa,CAAEyB,UAAU,cAAAE,qBAAA,uBAAzBA,qBAAA,CAA2BD,IAAI;IAC1E,IAAI,CAACE,WAAW,EAAE;IAClB3C,KAAK,CAAC6B,GAAG,CAAC,uCAAuCe,kBAAkB,CAACD,WAAW,CAAC,EAAE,CAAC,CAChFb,IAAI,CAACC,CAAC,IAAIL,QAAQ,CAACK,CAAC,CAACC,IAAI,CAAC,CAAC;IAC9BhC,KAAK,CAAC6B,GAAG,CAAC,2CAA2Ce,kBAAkB,CAACD,WAAW,CAAC,EAAE,CAAC,CACpFb,IAAI,CAACC,CAAC,IAAIH,YAAY,CAACG,CAAC,CAACC,IAAI,CAAC,CAAC;EACpC,CAAC,EAAE,CAACb,IAAI,EAAEJ,aAAa,CAAC,CAAC;;EAEzB;EACA,IAAI,CAACF,SAAS,CAACgC,MAAM,IAAI,CAACC,MAAM,CAACC,IAAI,CAAC9B,YAAY,CAAC,CAAC4B,MAAM,EAAE;IAC1D,oBAAO1C,OAAA;MAAA6C,QAAA,EAAK;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACzC;EACA,MAAMC,iBAAiB,GAAG,CAAAtC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwB,IAAI,MAAIxB,aAAa,aAAbA,aAAa,wBAAAH,sBAAA,GAAbG,aAAa,CAAEyB,UAAU,cAAA5B,sBAAA,uBAAzBA,sBAAA,CAA2B6B,IAAI;EAChF,IAAI,CAACY,iBAAiB,EAAE;IACtB,oBACElD,OAAA;MAAA6C,QAAA,gBACE7C,OAAA;QAAA6C,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BjD,OAAA,CAACF,QAAQ;QAACqD,eAAe,EAAEjB,CAAC,IAAI;UAAA,IAAAkB,cAAA;UAC9B,MAAMhB,IAAI,GAAG,CAAAF,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEE,IAAI,MAAIF,CAAC,aAADA,CAAC,wBAAAkB,cAAA,GAADlB,CAAC,CAAEG,UAAU,cAAAe,cAAA,uBAAbA,cAAA,CAAed,IAAI;UAC3C,IAAIF,IAAI,EAAEvB,gBAAgB,CAACqB,CAAC,CAAC;QAC/B,CAAE;QAACmB,eAAe,EAAE;MAAK;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5BjD,OAAA;QAAA6C,QAAA,EAAG;MAA8B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAEV;EAEA,MAAMjB,KAAK,GAAGlB,YAAY,CAACoC,iBAAiB,CAAC;EAC7C,IAAI,CAAClB,KAAK,EAAE;IACV,oBAAOhC,OAAA;MAAA6C,QAAA,EAAK;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC5C;;EAEA;EACA,MAAMK,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAIlC,WAAW,EAAE;IACjBL,eAAe,CAACiB,KAAK,KAAK;MACxB,GAAGA,KAAK;MACR,CAACkB,iBAAiB,GAAG;QACnB,GAAGlB,KAAK,CAACkB,iBAAiB,CAAC;QAC3B/C,SAAS,EAAE6B,KAAK,CAACkB,iBAAiB,CAAC,CAAC/C,SAAS,GAAG,EAAE;QAClDC,SAAS,EAAE4B,KAAK,CAACkB,iBAAiB,CAAC,CAAC9C,SAAS,GAAG;MAClD;IACF,CAAC,CAAC,CAAC;IACHe,UAAU,CAAC,2DAA2D,CAAC;IACvEE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAMkC,KAAK,GAAGA,CAAA,KAAM;IAClB,IAAInC,WAAW,EAAE;IACjBL,eAAe,CAACiB,KAAK,KAAK;MACxB,GAAGA,KAAK;MACR,CAACkB,iBAAiB,GAAG;QACnB,GAAGlB,KAAK,CAACkB,iBAAiB,CAAC;QAC3BhD,UAAU,EAAEsD,IAAI,CAACC,KAAK,CAACzB,KAAK,CAACkB,iBAAiB,CAAC,CAAChD,UAAU,GAAG,IAAI,CAAC;QAClEC,SAAS,EAAE6B,KAAK,CAACkB,iBAAiB,CAAC,CAAC/C,SAAS,GAAG,EAAE;QAClDC,SAAS,EAAE4B,KAAK,CAACkB,iBAAiB,CAAC,CAAC9C,SAAS,GAAG;MAClD;IACF,CAAC,CAAC,CAAC;IACHe,UAAU,CAAC,uEAAuE,CAAC;IACnFE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAMqC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAItC,WAAW,EAAE;IACjBL,eAAe,CAACiB,KAAK,KAAK;MACxB,GAAGA,KAAK;MACR,CAACkB,iBAAiB,GAAG;QACnB,GAAGlB,KAAK,CAACkB,iBAAiB,CAAC;QAC3B9C,SAAS,EAAE4B,KAAK,CAACkB,iBAAiB,CAAC,CAAC9C,SAAS,GAAG,EAAE;QAClDD,SAAS,EAAE6B,KAAK,CAACkB,iBAAiB,CAAC,CAAC/C,SAAS,GAAG;MAClD;IACF,CAAC,CAAC,CAAC;IACHgB,UAAU,CAAC,kDAAkD,CAAC;IAC9DE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMsC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACrC,KAAK,EAAE;IACZzB,KAAK,CAAC+D,IAAI,CAAC,mCAAmC,EAAE;MAC9CC,OAAO,EAAEX,iBAAiB;MAC1BY,MAAM,EAAExC,KAAK,CAACwC;IAChB,CAAC,CAAC,CAACnC,IAAI,CAACoC,GAAG,IAAI;MACb,IAAIA,GAAG,CAAClC,IAAI,CAACmC,OAAO,IAAID,GAAG,CAAClC,IAAI,CAACgC,OAAO,EAAE;QACxC9C,eAAe,CAACiB,KAAK,KAAK;UACxB,GAAGA,KAAK;UACR,CAACkB,iBAAiB,GAAG;YACnB,GAAGlB,KAAK,CAACkB,iBAAiB,CAAC;YAC3B,GAAGa,GAAG,CAAClC,IAAI,CAACgC;UACd;QACF,CAAC,CAAC,CAAC;QACH1C,UAAU,CAAC,kBAAkBG,KAAK,CAACwC,MAAM,EAAE,CAAC;MAC9C,CAAC,MAAM;QACL3C,UAAU,CAAC,wBAAwB,CAAC;MACtC;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM8C,gBAAgB,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;IAC3CtE,KAAK,CAAC+D,IAAI,CAAC,yCAAyC,EAAE;MACpDC,OAAO,EAAEX,iBAAiB;MAC1BgB,MAAM;MACNC;IACF,CAAC,CAAC,CAACxC,IAAI,CAACoC,GAAG,IAAI;MACb5C,UAAU,CAAC4C,GAAG,CAAClC,IAAI,CAACuC,MAAM,IAAI,6BAA6B,CAAC;IAC9D,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrBpD,OAAO,CAACqD,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC;IACnBjD,cAAc,CAAC,KAAK,CAAC;IACrBF,UAAU,CAAC,EAAE,CAAC;IACdJ,eAAe,CAACiB,KAAK,IAAI;MACvB,MAAMuC,QAAQ,GAAG;QAAE,GAAGvC;MAAM,CAAC;MAC7BW,MAAM,CAACC,IAAI,CAAC2B,QAAQ,CAAC,CAACtC,OAAO,CAACG,IAAI,IAAI;QACpC;QACA,IAAIA,IAAI,KAAKc,iBAAiB,EAAE;UAC9BqB,QAAQ,CAACnC,IAAI,CAAC,GAAG;YACf,GAAGmC,QAAQ,CAACnC,IAAI,CAAC;YACjBlC,UAAU,EAAEsD,IAAI,CAACC,KAAK,CAACc,QAAQ,CAACnC,IAAI,CAAC,CAAClC,UAAU,IAAI,IAAI,GAAGsD,IAAI,CAACgB,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YACjFrE,SAAS,EAAEoE,QAAQ,CAACnC,IAAI,CAAC,CAACjC,SAAS,GAAGqD,IAAI,CAACC,KAAK,CAACD,IAAI,CAACgB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;YACpEpE,SAAS,EAAEoD,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAEjB,IAAI,CAACkB,GAAG,CAAC,GAAG,EAAEH,QAAQ,CAACnC,IAAI,CAAC,CAAChC,SAAS,GAAGoD,IAAI,CAACC,KAAK,CAACD,IAAI,CAACgB,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;UACpG,CAAC;QACH,CAAC,MAAM;UACL;UACAD,QAAQ,CAACnC,IAAI,CAAC,GAAG;YACf,GAAGmC,QAAQ,CAACnC,IAAI,CAAC;YACjBlC,UAAU,EAAEsD,IAAI,CAACC,KAAK,CAACc,QAAQ,CAACnC,IAAI,CAAC,CAAClC,UAAU,GAAG,IAAI,CAAC;YACxDC,SAAS,EAAEoE,QAAQ,CAACnC,IAAI,CAAC,CAACjC,SAAS,GAAG,CAAC;YACvCC,SAAS,EAAEoD,IAAI,CAACiB,GAAG,CAAC,CAAC,EAAEjB,IAAI,CAACkB,GAAG,CAAC,GAAG,EAAEH,QAAQ,CAACnC,IAAI,CAAC,CAAChC,SAAS,CAAC;UAChE,CAAC;QACH;MACF,CAAC,CAAC;MACF,OAAOmE,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAIvC,KAAK,CAAC7B,SAAS,IAAIE,aAAa,EAAE;IACpC,oBAAOL,OAAA;MAAA6C,QAAA,EAAI;IAAuC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EACzD;EACA,IAAIjB,KAAK,CAAC5B,SAAS,IAAIE,cAAc,EAAE;IACrC,oBAAON,OAAA;MAAA6C,QAAA,EAAI;IAAqD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EACvE;EAEA,oBACEjD,OAAA;IAAA6C,QAAA,gBACE7C,OAAA;MAAA6C,QAAA,EAAI;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC9BjD,OAAA;MAAA6C,QAAA,GAAK,QAAM,EAAC7B,IAAI;IAAA;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACvBjD,OAAA,CAACF,QAAQ;MAACqD,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAE;MAACE,eAAe,EAAEzC;IAAc;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAEtE3B,KAAK,iBACJtB,OAAA;MAAK2E,KAAK,EAAE;QAAEC,UAAU,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,YAAY,EAAE;MAAE,CAAE;MAAAnC,QAAA,gBAC1F7C,OAAA;QAAA6C,QAAA,GAAI,SAAO,EAACvB,KAAK,CAAC2D,KAAK;MAAA;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7BjD,OAAA;QAAA6C,QAAA,EAAIvB,KAAK,CAAC4D;MAAW;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BjD,OAAA;QAAG2E,KAAK,EAAE;UAAEE,KAAK,EAAE;QAAa,CAAE;QAAAhC,QAAA,EAAEvB,KAAK,CAACwC;MAAM;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrDjD,OAAA;QAAQmF,OAAO,EAAExB,UAAW;QAAAd,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CACN,eAEDjD,OAAA;MAAK2E,KAAK,EAAE;QAAEC,UAAU,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,YAAY,EAAE;MAAE,CAAE;MAAAnC,QAAA,gBAC1F7C,OAAA;QAAA6C,QAAA,EAAKK;MAAiB;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5BjD,OAAA;QAAA6C,QAAA,GAAG,cAAY,EAACb,KAAK,CAAC9B,UAAU,CAACkF,cAAc,CAAC,CAAC;MAAA;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtDjD,OAAA;QAAA6C,QAAA,GAAG,aAAW,EAACb,KAAK,CAAC7B,SAAS;MAAA;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCjD,OAAA;QAAA6C,QAAA,GAAG,aAAW,EAACb,KAAK,CAAC5B,SAAS;MAAA;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCjD,OAAA;QAAQmF,OAAO,EAAE7B,MAAO;QAAC+B,QAAQ,EAAEjE,WAAY;QAAAyB,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC/DjD,OAAA;QAAQmF,OAAO,EAAE5B,KAAM;QAAC8B,QAAQ,EAAEjE,WAAY;QAACuD,KAAK,EAAE;UAACW,UAAU,EAAE;QAAC,CAAE;QAAAzC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACrFjD,OAAA;QAAQmF,OAAO,EAAEzB,UAAW;QAAC2B,QAAQ,EAAEjE,WAAY;QAACuD,KAAK,EAAE;UAACW,UAAU,EAAE;QAAC,CAAE;QAAAzC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,eAENjD,OAAA;MAAK2E,KAAK,EAAE;QAAEC,UAAU,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,YAAY,EAAE;MAAE,CAAE;MAAAnC,QAAA,gBAC1F7C,OAAA;QAAA6C,QAAA,EAAI;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACjBzB,SAAS,CAACkB,MAAM,KAAK,CAAC,iBAAI1C,OAAA;QAAA6C,QAAA,EAAG;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAClEjD,OAAA;QAAA6C,QAAA,EACGrB,SAAS,CAAC+D,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBAClBzF,OAAA;UAAA6C,QAAA,GACG2C,CAAC,CAACtB,MAAM,EAAC,GAAC,EAACsB,CAAC,CAACrB,MAAM,iBAAInE,OAAA;YAAA6C,QAAA,GAAG,OAAK,EAAC2C,CAAC,CAACrB,MAAM;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CjD,OAAA;YAAQ2E,KAAK,EAAE;cAACW,UAAU,EAAE;YAAC,CAAE;YAACH,OAAO,EAAEA,CAAA,KAAMlB,gBAAgB,CAACuB,CAAC,CAACtB,MAAM,EAAEsB,CAAC,CAACrB,MAAM,CAAE;YAAAtB,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,GAFzFwC,CAAC;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGN,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACNjD,OAAA;MAAQmF,OAAO,EAAEd,QAAS;MAAAxB,QAAA,EAAC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAC3C/B,OAAO,iBAAIlB,OAAA;MAAK2E,KAAK,EAAE;QAAEE,KAAK,EAAE,QAAQ;QAAEE,MAAM,EAAE;MAAG,CAAE;MAAAlC,QAAA,EAAE3B;IAAO;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrE,CAAC;AAEV;AAACzC,EAAA,CAxNuBD,GAAG;AAAAmF,EAAA,GAAHnF,GAAG;AAAA,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}