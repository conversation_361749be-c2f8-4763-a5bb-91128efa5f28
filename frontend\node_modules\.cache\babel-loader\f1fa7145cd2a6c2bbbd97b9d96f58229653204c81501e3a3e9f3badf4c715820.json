{"ast": null, "code": "import { LoaderParserPriority, copySearchParams } from \"@pixi/assets\";\nimport { ExtensionType, utils, settings, extensions } from \"@pixi/core\";\nimport { BitmapFont } from \"./BitmapFont.mjs\";\nimport \"./formats/index.mjs\";\nimport { TextFormat } from \"./formats/TextFormat.mjs\";\nimport { XMLStringFormat } from \"./formats/XMLStringFormat.mjs\";\nconst validExtensions = [\".xml\", \".fnt\"],\n  loadBitmapFont = {\n    extension: {\n      type: ExtensionType.LoadParser,\n      priority: LoaderParserPriority.Normal\n    },\n    name: \"loadBitmapFont\",\n    test(url) {\n      return validExtensions.includes(utils.path.extname(url).toLowerCase());\n    },\n    async testParse(data) {\n      return TextFormat.test(data) || XMLStringFormat.test(data);\n    },\n    async parse(asset, data, loader) {\n      const fontData = TextFormat.test(asset) ? TextFormat.parse(asset) : XMLStringFormat.parse(asset),\n        {\n          src\n        } = data,\n        {\n          page: pages\n        } = fontData,\n        textureUrls = [];\n      for (let i = 0; i < pages.length; ++i) {\n        const pageFile = pages[i].file;\n        let imagePath = utils.path.join(utils.path.dirname(src), pageFile);\n        imagePath = copySearchParams(imagePath, src), textureUrls.push(imagePath);\n      }\n      const loadedTextures = await loader.load(textureUrls),\n        textures = textureUrls.map(url => loadedTextures[url]);\n      return BitmapFont.install(fontData, textures, !0);\n    },\n    async load(url, _options) {\n      return (await settings.ADAPTER.fetch(url)).text();\n    },\n    unload(bitmapFont) {\n      bitmapFont.destroy();\n    }\n  };\nextensions.add(loadBitmapFont);\nexport { loadBitmapFont };", "map": {"version": 3, "names": ["validExtensions", "loadBitmapFont", "extension", "type", "ExtensionType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "priority", "LoaderParserPriority", "Normal", "name", "test", "url", "includes", "utils", "path", "extname", "toLowerCase", "test<PERSON><PERSON>e", "data", "TextFormat", "XMLStringFormat", "parse", "asset", "loader", "fontData", "src", "page", "pages", "textureUrls", "i", "length", "pageFile", "file", "imagePath", "join", "dirname", "copySearchParams", "push", "loadedTextures", "load", "textures", "map", "BitmapFont", "install", "_options", "settings", "ADAPTER", "fetch", "text", "unload", "bitmapFont", "destroy", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\text-bitmap\\src\\loadBitmapFont.ts"], "sourcesContent": ["import { copySearchParams, LoaderParserPriority } from '@pixi/assets';\nimport { extensions, ExtensionType, settings, utils } from '@pixi/core';\nimport { BitmapFont } from './BitmapFont';\nimport { TextFormat, XMLStringFormat } from './formats';\n\nimport type { Loader, LoaderParser, ResolvedAsset } from '@pixi/assets';\nimport type { Texture } from '@pixi/core';\nimport type { BitmapFontData } from './BitmapFontData';\n\nconst validExtensions = ['.xml', '.fnt'];\n\n/** simple loader plugin for loading in bitmap fonts! */\nexport const loadBitmapFont = {\n    extension: {\n        type: ExtensionType.LoadParser,\n        priority: LoaderParserPriority.Normal,\n    },\n\n    name: 'loadBitmapFont',\n\n    test(url: string): boolean\n    {\n        return validExtensions.includes(utils.path.extname(url).toLowerCase());\n    },\n\n    async testParse(data: string): Promise<boolean>\n    {\n        return TextFormat.test(data) || XMLStringFormat.test(data);\n    },\n\n    async parse(asset: string, data: ResolvedAsset, loader: Loader): Promise<BitmapFont>\n    {\n        const fontData: BitmapFontData = TextFormat.test(asset)\n            ? TextFormat.parse(asset)\n            : XMLStringFormat.parse(asset);\n\n        const { src } = data;\n        const { page: pages } = fontData;\n        const textureUrls = [];\n\n        for (let i = 0; i < pages.length; ++i)\n        {\n            const pageFile = pages[i].file;\n            let imagePath = utils.path.join(utils.path.dirname(src), pageFile);\n\n            imagePath = copySearchParams(imagePath, src);\n\n            textureUrls.push(imagePath);\n        }\n\n        const loadedTextures = await loader.load<Texture>(textureUrls);\n        const textures = textureUrls.map((url) => loadedTextures[url]);\n\n        return BitmapFont.install(fontData, textures, true);\n    },\n\n    async load(url: string, _options: ResolvedAsset): Promise<string>\n    {\n        const response = await settings.ADAPTER.fetch(url);\n\n        return response.text();\n    },\n\n    unload(bitmapFont: BitmapFont): void\n    {\n        bitmapFont.destroy();\n    }\n} as LoaderParser<BitmapFont | string>;\n\nextensions.add(loadBitmapFont);\n"], "mappings": ";;;;;;AASA,MAAMA,eAAA,GAAkB,CAAC,QAAQ,MAAM;EAG1BC,cAAA,GAAiB;IAC1BC,SAAA,EAAW;MACPC,IAAA,EAAMC,aAAA,CAAcC,UAAA;MACpBC,QAAA,EAAUC,oBAAA,CAAqBC;IACnC;IAEAC,IAAA,EAAM;IAENC,KAAKC,GAAA,EACL;MACW,OAAAX,eAAA,CAAgBY,QAAA,CAASC,KAAA,CAAMC,IAAA,CAAKC,OAAA,CAAQJ,GAAG,EAAEK,WAAA,EAAa;IACzE;IAEA,MAAMC,UAAUC,IAAA,EAChB;MACI,OAAOC,UAAA,CAAWT,IAAA,CAAKQ,IAAI,KAAKE,eAAA,CAAgBV,IAAA,CAAKQ,IAAI;IAC7D;IAEA,MAAMG,MAAMC,KAAA,EAAeJ,IAAA,EAAqBK,MAAA,EAChD;MACU,MAAAC,QAAA,GAA2BL,UAAA,CAAWT,IAAA,CAAKY,KAAK,IAChDH,UAAA,CAAWE,KAAA,CAAMC,KAAK,IACtBF,eAAA,CAAgBC,KAAA,CAAMC,KAAK;QAE3B;UAAEG;QAAI,IAAIP,IAAA;QACV;UAAEQ,IAAA,EAAMC;QAAA,IAAUH,QAAA;QAClBI,WAAA,GAAc;MAEpB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIF,KAAA,CAAMG,MAAA,EAAQ,EAAED,CAAA,EACpC;QACU,MAAAE,QAAA,GAAWJ,KAAA,CAAME,CAAC,EAAEG,IAAA;QACtB,IAAAC,SAAA,GAAYpB,KAAA,CAAMC,IAAA,CAAKoB,IAAA,CAAKrB,KAAA,CAAMC,IAAA,CAAKqB,OAAA,CAAQV,GAAG,GAAGM,QAAQ;QAEjEE,SAAA,GAAYG,gBAAA,CAAiBH,SAAA,EAAWR,GAAG,GAE3CG,WAAA,CAAYS,IAAA,CAAKJ,SAAS;MAC9B;MAEA,MAAMK,cAAA,GAAiB,MAAMf,MAAA,CAAOgB,IAAA,CAAcX,WAAW;QACvDY,QAAA,GAAWZ,WAAA,CAAYa,GAAA,CAAK9B,GAAA,IAAQ2B,cAAA,CAAe3B,GAAG,CAAC;MAE7D,OAAO+B,UAAA,CAAWC,OAAA,CAAQnB,QAAA,EAAUgB,QAAA,EAAU,EAAI;IACtD;IAEA,MAAMD,KAAK5B,GAAA,EAAaiC,QAAA,EACxB;MAGI,QAFiB,MAAMC,QAAA,CAASC,OAAA,CAAQC,KAAA,CAAMpC,GAAG,GAEjCqC,IAAA;IACpB;IAEAC,OAAOC,UAAA,EACP;MACIA,UAAA,CAAWC,OAAA,CAAQ;IACvB;EACJ;AAEAC,UAAA,CAAWC,GAAA,CAAIpD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}