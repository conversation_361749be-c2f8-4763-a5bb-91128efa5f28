{"ast": null, "code": "import { defaultValue } from \"./defaultValue.mjs\";\nimport { mapType } from \"./mapType.mjs\";\nfunction getUniformData(program, gl) {\n  const uniforms = {},\n    totalUniforms = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);\n  for (let i = 0; i < totalUniforms; i++) {\n    const uniformData = gl.getActiveUniform(program, i),\n      name = uniformData.name.replace(/\\[.*?\\]$/, \"\"),\n      isArray = !!uniformData.name.match(/\\[.*?\\]$/),\n      type = mapType(gl, uniformData.type);\n    uniforms[name] = {\n      name,\n      index: i,\n      type,\n      size: uniformData.size,\n      isArray,\n      value: defaultValue(type, uniformData.size)\n    };\n  }\n  return uniforms;\n}\nexport { getUniformData };", "map": {"version": 3, "names": ["getUniformData", "program", "gl", "uniforms", "totalUniforms", "getProgramParameter", "ACTIVE_UNIFORMS", "i", "uniformData", "getActiveUniform", "name", "replace", "isArray", "match", "type", "mapType", "index", "size", "value", "defaultValue"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\shader\\utils\\getUniformData.ts"], "sourcesContent": ["import { defaultValue } from './defaultValue';\nimport { mapType } from './mapType';\n\nimport type { IUniformData } from '../Program';\n\n/**\n * returns the uniform data from the program\n * @private\n * @param program - the webgl program\n * @param gl - the WebGL context\n * @returns {object} the uniform data for this program\n */\nexport function getUniformData(program: WebGLProgram, gl: WebGLRenderingContextBase): {[key: string]: IUniformData}\n{\n    const uniforms: {[key: string]: IUniformData} = {};\n\n    const totalUniforms = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);\n\n    for (let i = 0; i < totalUniforms; i++)\n    {\n        const uniformData = gl.getActiveUniform(program, i);\n        const name = uniformData.name.replace(/\\[.*?\\]$/, '');\n\n        const isArray = !!(uniformData.name.match(/\\[.*?\\]$/));\n\n        const type = mapType(gl, uniformData.type);\n\n        uniforms[name] = {\n            name,\n            index: i,\n            type,\n            size: uniformData.size,\n            isArray,\n            value: defaultValue(type, uniformData.size),\n        };\n    }\n\n    return uniforms;\n}\n"], "mappings": ";;AAYgB,SAAAA,eAAeC,OAAA,EAAuBC,EAAA,EACtD;EACU,MAAAC,QAAA,GAA0C,CAE1C;IAAAC,aAAA,GAAgBF,EAAA,CAAGG,mBAAA,CAAoBJ,OAAA,EAASC,EAAA,CAAGI,eAAe;EAExE,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIH,aAAA,EAAeG,CAAA,IACnC;IACU,MAAAC,WAAA,GAAcN,EAAA,CAAGO,gBAAA,CAAiBR,OAAA,EAASM,CAAC;MAC5CG,IAAA,GAAOF,WAAA,CAAYE,IAAA,CAAKC,OAAA,CAAQ,YAAY,EAAE;MAE9CC,OAAA,GAAU,CAAC,CAAEJ,WAAA,CAAYE,IAAA,CAAKG,KAAA,CAAM,UAAU;MAE9CC,IAAA,GAAOC,OAAA,CAAQb,EAAA,EAAIM,WAAA,CAAYM,IAAI;IAEzCX,QAAA,CAASO,IAAI,IAAI;MACbA,IAAA;MACAM,KAAA,EAAOT,CAAA;MACPO,IAAA;MACAG,IAAA,EAAMT,WAAA,CAAYS,IAAA;MAClBL,OAAA;MACAM,KAAA,EAAOC,YAAA,CAAaL,IAAA,EAAMN,WAAA,CAAYS,IAAI;IAAA;EAElD;EAEO,OAAAd,QAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}