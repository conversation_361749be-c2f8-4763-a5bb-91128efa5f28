{"ast": null, "code": "import { Geometry } from \"../geometry/Geometry.mjs\";\nclass Quad extends Geometry {\n  constructor() {\n    super(), this.addAttribute(\"aVertexPosition\", new Float32Array([0, 0, 1, 0, 1, 1, 0, 1])).addIndex([0, 1, 3, 2]);\n  }\n}\nexport { Quad };", "map": {"version": 3, "names": ["Quad", "Geometry", "constructor", "addAttribute", "Float32Array", "addIndex"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\utils\\Quad.ts"], "sourcesContent": ["import { Geometry } from '../geometry/Geometry';\n\n/**\n * Helper class to create a quad\n * @memberof PIXI\n */\nexport class Quad extends Geometry\n{\n    constructor()\n    {\n        super();\n\n        this.addAttribute('aVertexPosition', new Float32Array([\n            0, 0,\n            1, 0,\n            1, 1,\n            0, 1,\n        ]))\n            .addIndex([0, 1, 3, 2]);\n    }\n}\n"], "mappings": ";AAMO,MAAMA,IAAA,SAAaC,QAAA,CAC1B;EACIC,YAAA,EACA;IACI,MAEA,QAAKC,YAAA,CAAa,mBAAmB,IAAIC,YAAA,CAAa,CAClD,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,EACN,CAAC,EACGC,QAAA,CAAS,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}