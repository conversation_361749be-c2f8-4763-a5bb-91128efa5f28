{"ast": null, "code": "import { Color } from \"@pixi/color\";\nimport { ENV } from \"@pixi/constants\";\nimport { ExtensionType, extensions } from \"@pixi/extensions\";\nimport { settings } from \"@pixi/settings\";\nimport { deprecation, premultiplyBlendMode, nextPow2, log2 } from \"@pixi/utils\";\nimport { ViewableBuffer } from \"../geometry/ViewableBuffer.mjs\";\nimport { checkMaxIfStatementsInShader } from \"../shader/utils/checkMaxIfStatementsInShader.mjs\";\nimport { State } from \"../state/State.mjs\";\nimport { BaseTexture } from \"../textures/BaseTexture.mjs\";\nimport { BatchDrawCall } from \"./BatchDrawCall.mjs\";\nimport { BatchGeometry } from \"./BatchGeometry.mjs\";\nimport { BatchShaderGenerator } from \"./BatchShaderGenerator.mjs\";\nimport { BatchTextureArray } from \"./BatchTextureArray.mjs\";\nimport { canUploadSameBuffer } from \"./canUploadSameBuffer.mjs\";\nimport { maxRecommendedTextures } from \"./maxRecommendedTextures.mjs\";\nimport { ObjectRenderer } from \"./ObjectRenderer.mjs\";\nimport defaultFragment from \"./texture.frag.mjs\";\nimport defaultVertex from \"./texture.vert.mjs\";\nconst _BatchRenderer = class _BatchRenderer2 extends ObjectRenderer {\n  /**\n   * This will hook onto the renderer's `contextChange`\n   * and `prerender` signals.\n   * @param {PIXI.Renderer} renderer - The renderer this works for.\n   */\n  constructor(renderer) {\n    super(renderer), this.setShaderGenerator(), this.geometryClass = BatchGeometry, this.vertexSize = 6, this.state = State.for2d(), this.size = _BatchRenderer2.defaultBatchSize * 4, this._vertexCount = 0, this._indexCount = 0, this._bufferedElements = [], this._bufferedTextures = [], this._bufferSize = 0, this._shader = null, this._packedGeometries = [], this._packedGeometryPoolSize = 2, this._flushId = 0, this._aBuffers = {}, this._iBuffers = {}, this.maxTextures = 1, this.renderer.on(\"prerender\", this.onPrerender, this), renderer.runners.contextChange.add(this), this._dcIndex = 0, this._aIndex = 0, this._iIndex = 0, this._attributeBuffer = null, this._indexBuffer = null, this._tempBoundTextures = [];\n  }\n  /**\n   * The maximum textures that this device supports.\n   * @static\n   * @default 32\n   */\n  static get defaultMaxTextures() {\n    return this._defaultMaxTextures = this._defaultMaxTextures ?? maxRecommendedTextures(32), this._defaultMaxTextures;\n  }\n  static set defaultMaxTextures(value) {\n    this._defaultMaxTextures = value;\n  }\n  /**\n   * Can we upload the same buffer in a single frame?\n   * @static\n   */\n  static get canUploadSameBuffer() {\n    return this._canUploadSameBuffer = this._canUploadSameBuffer ?? canUploadSameBuffer(), this._canUploadSameBuffer;\n  }\n  static set canUploadSameBuffer(value) {\n    this._canUploadSameBuffer = value;\n  }\n  /**\n   * @see PIXI.BatchRenderer#maxTextures\n   * @deprecated since 7.1.0\n   * @readonly\n   */\n  get MAX_TEXTURES() {\n    return deprecation(\"7.1.0\", \"BatchRenderer#MAX_TEXTURES renamed to BatchRenderer#maxTextures\"), this.maxTextures;\n  }\n  /**\n   * The default vertex shader source\n   * @readonly\n   */\n  static get defaultVertexSrc() {\n    return defaultVertex;\n  }\n  /**\n   * The default fragment shader source\n   * @readonly\n   */\n  static get defaultFragmentTemplate() {\n    return defaultFragment;\n  }\n  /**\n   * Set the shader generator.\n   * @param {object} [options]\n   * @param {string} [options.vertex=PIXI.BatchRenderer.defaultVertexSrc] - Vertex shader source\n   * @param {string} [options.fragment=PIXI.BatchRenderer.defaultFragmentTemplate] - Fragment shader template\n   */\n  setShaderGenerator({\n    vertex = _BatchRenderer2.defaultVertexSrc,\n    fragment = _BatchRenderer2.defaultFragmentTemplate\n  } = {}) {\n    this.shaderGenerator = new BatchShaderGenerator(vertex, fragment);\n  }\n  /**\n   * Handles the `contextChange` signal.\n   *\n   * It calculates `this.maxTextures` and allocating the packed-geometry object pool.\n   */\n  contextChange() {\n    const gl = this.renderer.gl;\n    settings.PREFER_ENV === ENV.WEBGL_LEGACY ? this.maxTextures = 1 : (this.maxTextures = Math.min(gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS), _BatchRenderer2.defaultMaxTextures), this.maxTextures = checkMaxIfStatementsInShader(this.maxTextures, gl)), this._shader = this.shaderGenerator.generateShader(this.maxTextures);\n    for (let i = 0; i < this._packedGeometryPoolSize; i++) this._packedGeometries[i] = new this.geometryClass();\n    this.initFlushBuffers();\n  }\n  /** Makes sure that static and dynamic flush pooled objects have correct dimensions. */\n  initFlushBuffers() {\n    const {\n        _drawCallPool,\n        _textureArrayPool\n      } = _BatchRenderer2,\n      MAX_SPRITES = this.size / 4,\n      MAX_TA = Math.floor(MAX_SPRITES / this.maxTextures) + 1;\n    for (; _drawCallPool.length < MAX_SPRITES;) _drawCallPool.push(new BatchDrawCall());\n    for (; _textureArrayPool.length < MAX_TA;) _textureArrayPool.push(new BatchTextureArray());\n    for (let i = 0; i < this.maxTextures; i++) this._tempBoundTextures[i] = null;\n  }\n  /** Handles the `prerender` signal. It ensures that flushes start from the first geometry object again. */\n  onPrerender() {\n    this._flushId = 0;\n  }\n  /**\n   * Buffers the \"batchable\" object. It need not be rendered immediately.\n   * @param {PIXI.DisplayObject} element - the element to render when\n   *    using this renderer\n   */\n  render(element) {\n    element._texture.valid && (this._vertexCount + element.vertexData.length / 2 > this.size && this.flush(), this._vertexCount += element.vertexData.length / 2, this._indexCount += element.indices.length, this._bufferedTextures[this._bufferSize] = element._texture.baseTexture, this._bufferedElements[this._bufferSize++] = element);\n  }\n  buildTexturesAndDrawCalls() {\n    const {\n        _bufferedTextures: textures,\n        maxTextures\n      } = this,\n      textureArrays = _BatchRenderer2._textureArrayPool,\n      batch = this.renderer.batch,\n      boundTextures = this._tempBoundTextures,\n      touch = this.renderer.textureGC.count;\n    let TICK = ++BaseTexture._globalBatch,\n      countTexArrays = 0,\n      texArray = textureArrays[0],\n      start = 0;\n    batch.copyBoundTextures(boundTextures, maxTextures);\n    for (let i = 0; i < this._bufferSize; ++i) {\n      const tex = textures[i];\n      textures[i] = null, tex._batchEnabled !== TICK && (texArray.count >= maxTextures && (batch.boundArray(texArray, boundTextures, TICK, maxTextures), this.buildDrawCalls(texArray, start, i), start = i, texArray = textureArrays[++countTexArrays], ++TICK), tex._batchEnabled = TICK, tex.touched = touch, texArray.elements[texArray.count++] = tex);\n    }\n    texArray.count > 0 && (batch.boundArray(texArray, boundTextures, TICK, maxTextures), this.buildDrawCalls(texArray, start, this._bufferSize), ++countTexArrays, ++TICK);\n    for (let i = 0; i < boundTextures.length; i++) boundTextures[i] = null;\n    BaseTexture._globalBatch = TICK;\n  }\n  /**\n   * Populating drawcalls for rendering\n   * @param texArray\n   * @param start\n   * @param finish\n   */\n  buildDrawCalls(texArray, start, finish) {\n    const {\n        _bufferedElements: elements,\n        _attributeBuffer,\n        _indexBuffer,\n        vertexSize\n      } = this,\n      drawCalls = _BatchRenderer2._drawCallPool;\n    let dcIndex = this._dcIndex,\n      aIndex = this._aIndex,\n      iIndex = this._iIndex,\n      drawCall = drawCalls[dcIndex];\n    drawCall.start = this._iIndex, drawCall.texArray = texArray;\n    for (let i = start; i < finish; ++i) {\n      const sprite = elements[i],\n        tex = sprite._texture.baseTexture,\n        spriteBlendMode = premultiplyBlendMode[tex.alphaMode ? 1 : 0][sprite.blendMode];\n      elements[i] = null, start < i && drawCall.blend !== spriteBlendMode && (drawCall.size = iIndex - drawCall.start, start = i, drawCall = drawCalls[++dcIndex], drawCall.texArray = texArray, drawCall.start = iIndex), this.packInterleavedGeometry(sprite, _attributeBuffer, _indexBuffer, aIndex, iIndex), aIndex += sprite.vertexData.length / 2 * vertexSize, iIndex += sprite.indices.length, drawCall.blend = spriteBlendMode;\n    }\n    start < finish && (drawCall.size = iIndex - drawCall.start, ++dcIndex), this._dcIndex = dcIndex, this._aIndex = aIndex, this._iIndex = iIndex;\n  }\n  /**\n   * Bind textures for current rendering\n   * @param texArray\n   */\n  bindAndClearTexArray(texArray) {\n    const textureSystem = this.renderer.texture;\n    for (let j = 0; j < texArray.count; j++) textureSystem.bind(texArray.elements[j], texArray.ids[j]), texArray.elements[j] = null;\n    texArray.count = 0;\n  }\n  updateGeometry() {\n    const {\n      _packedGeometries: packedGeometries,\n      _attributeBuffer: attributeBuffer,\n      _indexBuffer: indexBuffer\n    } = this;\n    _BatchRenderer2.canUploadSameBuffer ? (packedGeometries[this._flushId]._buffer.update(attributeBuffer.rawBinaryData), packedGeometries[this._flushId]._indexBuffer.update(indexBuffer), this.renderer.geometry.updateBuffers()) : (this._packedGeometryPoolSize <= this._flushId && (this._packedGeometryPoolSize++, packedGeometries[this._flushId] = new this.geometryClass()), packedGeometries[this._flushId]._buffer.update(attributeBuffer.rawBinaryData), packedGeometries[this._flushId]._indexBuffer.update(indexBuffer), this.renderer.geometry.bind(packedGeometries[this._flushId]), this.renderer.geometry.updateBuffers(), this._flushId++);\n  }\n  drawBatches() {\n    const dcCount = this._dcIndex,\n      {\n        gl,\n        state: stateSystem\n      } = this.renderer,\n      drawCalls = _BatchRenderer2._drawCallPool;\n    let curTexArray = null;\n    for (let i = 0; i < dcCount; i++) {\n      const {\n        texArray,\n        type,\n        size,\n        start,\n        blend\n      } = drawCalls[i];\n      curTexArray !== texArray && (curTexArray = texArray, this.bindAndClearTexArray(texArray)), this.state.blendMode = blend, stateSystem.set(this.state), gl.drawElements(type, size, gl.UNSIGNED_SHORT, start * 2);\n    }\n  }\n  /** Renders the content _now_ and empties the current batch. */\n  flush() {\n    this._vertexCount !== 0 && (this._attributeBuffer = this.getAttributeBuffer(this._vertexCount), this._indexBuffer = this.getIndexBuffer(this._indexCount), this._aIndex = 0, this._iIndex = 0, this._dcIndex = 0, this.buildTexturesAndDrawCalls(), this.updateGeometry(), this.drawBatches(), this._bufferSize = 0, this._vertexCount = 0, this._indexCount = 0);\n  }\n  /** Starts a new sprite batch. */\n  start() {\n    this.renderer.state.set(this.state), this.renderer.texture.ensureSamplerType(this.maxTextures), this.renderer.shader.bind(this._shader), _BatchRenderer2.canUploadSameBuffer && this.renderer.geometry.bind(this._packedGeometries[this._flushId]);\n  }\n  /** Stops and flushes the current batch. */\n  stop() {\n    this.flush();\n  }\n  /** Destroys this `BatchRenderer`. It cannot be used again. */\n  destroy() {\n    for (let i = 0; i < this._packedGeometryPoolSize; i++) this._packedGeometries[i] && this._packedGeometries[i].destroy();\n    this.renderer.off(\"prerender\", this.onPrerender, this), this._aBuffers = null, this._iBuffers = null, this._packedGeometries = null, this._attributeBuffer = null, this._indexBuffer = null, this._shader && (this._shader.destroy(), this._shader = null), super.destroy();\n  }\n  /**\n   * Fetches an attribute buffer from `this._aBuffers` that can hold atleast `size` floats.\n   * @param size - minimum capacity required\n   * @returns - buffer than can hold atleast `size` floats\n   */\n  getAttributeBuffer(size) {\n    const roundedP2 = nextPow2(Math.ceil(size / 8)),\n      roundedSizeIndex = log2(roundedP2),\n      roundedSize = roundedP2 * 8;\n    this._aBuffers.length <= roundedSizeIndex && (this._iBuffers.length = roundedSizeIndex + 1);\n    let buffer = this._aBuffers[roundedSize];\n    return buffer || (this._aBuffers[roundedSize] = buffer = new ViewableBuffer(roundedSize * this.vertexSize * 4)), buffer;\n  }\n  /**\n   * Fetches an index buffer from `this._iBuffers` that can\n   * have at least `size` capacity.\n   * @param size - minimum required capacity\n   * @returns - buffer that can fit `size` indices.\n   */\n  getIndexBuffer(size) {\n    const roundedP2 = nextPow2(Math.ceil(size / 12)),\n      roundedSizeIndex = log2(roundedP2),\n      roundedSize = roundedP2 * 12;\n    this._iBuffers.length <= roundedSizeIndex && (this._iBuffers.length = roundedSizeIndex + 1);\n    let buffer = this._iBuffers[roundedSizeIndex];\n    return buffer || (this._iBuffers[roundedSizeIndex] = buffer = new Uint16Array(roundedSize)), buffer;\n  }\n  /**\n   * Takes the four batching parameters of `element`, interleaves\n   * and pushes them into the batching attribute/index buffers given.\n   *\n   * It uses these properties: `vertexData` `uvs`, `textureId` and\n   * `indicies`. It also uses the \"tint\" of the base-texture, if\n   * present.\n   * @param {PIXI.DisplayObject} element - element being rendered\n   * @param attributeBuffer - attribute buffer.\n   * @param indexBuffer - index buffer\n   * @param aIndex - number of floats already in the attribute buffer\n   * @param iIndex - number of indices already in `indexBuffer`\n   */\n  packInterleavedGeometry(element, attributeBuffer, indexBuffer, aIndex, iIndex) {\n    const {\n        uint32View,\n        float32View\n      } = attributeBuffer,\n      packedVertices = aIndex / this.vertexSize,\n      uvs = element.uvs,\n      indicies = element.indices,\n      vertexData = element.vertexData,\n      textureId = element._texture.baseTexture._batchLocation,\n      alpha = Math.min(element.worldAlpha, 1),\n      argb = Color.shared.setValue(element._tintRGB).toPremultiplied(alpha, element._texture.baseTexture.alphaMode > 0);\n    for (let i = 0; i < vertexData.length; i += 2) float32View[aIndex++] = vertexData[i], float32View[aIndex++] = vertexData[i + 1], float32View[aIndex++] = uvs[i], float32View[aIndex++] = uvs[i + 1], uint32View[aIndex++] = argb, float32View[aIndex++] = textureId;\n    for (let i = 0; i < indicies.length; i++) indexBuffer[iIndex++] = packedVertices + indicies[i];\n  }\n};\n_BatchRenderer.defaultBatchSize = 4096, /** @ignore */\n_BatchRenderer.extension = {\n  name: \"batch\",\n  type: ExtensionType.RendererPlugin\n},\n/**\n* Pool of `BatchDrawCall` objects that `flush` used\n* to create \"batches\" of the objects being rendered.\n*\n* These are never re-allocated again.\n* Shared between all batch renderers because it can be only one \"flush\" working at the moment.\n* @member {PIXI.BatchDrawCall[]}\n*/\n_BatchRenderer._drawCallPool = [],\n/**\n* Pool of `BatchDrawCall` objects that `flush` used\n* to create \"batches\" of the objects being rendered.\n*\n* These are never re-allocated again.\n* Shared between all batch renderers because it can be only one \"flush\" working at the moment.\n* @member {PIXI.BatchTextureArray[]}\n*/\n_BatchRenderer._textureArrayPool = [];\nlet BatchRenderer = _BatchRenderer;\nextensions.add(BatchRenderer);\nexport { BatchRenderer };", "map": {"version": 3, "names": ["_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_BatchRenderer2", "O<PERSON><PERSON><PERSON><PERSON>", "constructor", "renderer", "setShaderGenerator", "geometryClass", "BatchGeometry", "vertexSize", "state", "State", "for2d", "size", "defaultBatchSize", "_vertexCount", "_indexCount", "_bufferedElements", "_bufferedTextures", "_bufferSize", "_shader", "_packedGeometries", "_packedGeometryPoolSize", "_flushId", "_aBuffers", "_iBuffers", "maxTextures", "on", "onP<PERSON>ender", "runners", "contextChange", "add", "_dcIndex", "_aIndex", "_iIndex", "_attributeBuffer", "_indexBuffer", "_tempBoundTextures", "defaultMaxTextures", "_defaultMaxTextures", "maxRecommendedTextures", "value", "canUploadSameBuffer", "_canUploadSameBuffer", "MAX_TEXTURES", "deprecation", "defaultVertexSrc", "defaultVertex", "defaultFragmentTemplate", "defaultFragment", "vertex", "fragment", "shaderGenerator", "BatchShaderGenerator", "gl", "settings", "PREFER_ENV", "ENV", "WEBGL_LEGACY", "Math", "min", "getParameter", "MAX_TEXTURE_IMAGE_UNITS", "checkMaxIfStatementsInShader", "generateShader", "i", "initFlushBuffers", "_drawCallPool", "_textureArrayPool", "MAX_SPRITES", "MAX_TA", "floor", "length", "push", "BatchDrawCall", "BatchTextureArray", "render", "element", "_texture", "valid", "vertexData", "flush", "indices", "baseTexture", "buildTexturesAndDrawCalls", "textures", "textureArrays", "batch", "boundTextures", "touch", "textureGC", "count", "TICK", "BaseTexture", "_globalBatch", "countTexArrays", "texArray", "start", "copyBoundTextures", "tex", "_batchEnabled", "boundArray", "buildDrawCalls", "touched", "elements", "finish", "drawCalls", "dcIndex", "aIndex", "iIndex", "drawCall", "sprite", "spriteBlendMode", "premultiplyBlendMode", "alphaMode", "blendMode", "blend", "packInterleavedGeometry", "bindAndClearTexArray", "textureSystem", "texture", "j", "bind", "ids", "updateGeometry", "packedGeometries", "attribute<PERSON>uffer", "indexBuffer", "_buffer", "update", "rawBinaryData", "geometry", "updateBuffers", "drawBatches", "dcCount", "stateSystem", "curTexArray", "type", "set", "drawElements", "UNSIGNED_SHORT", "getAttributeBuffer", "getIndexBuffer", "ensureSamplerType", "shader", "stop", "destroy", "off", "roundedP2", "nextPow2", "ceil", "roundedSizeIndex", "log2", "roundedSize", "buffer", "ViewableBuffer", "Uint16Array", "uint32View", "float32View", "packedVertices", "uvs", "indicies", "textureId", "_batchLocation", "alpha", "worldAlpha", "argb", "Color", "shared", "setValue", "_tintRGB", "toPremultiplied", "extension", "name", "ExtensionType", "RendererPlugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extensions"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\batch\\BatchRenderer.ts"], "sourcesContent": ["import { Color } from '@pixi/color';\nimport { ENV } from '@pixi/constants';\nimport { extensions, ExtensionType } from '@pixi/extensions';\nimport { settings } from '@pixi/settings';\nimport { deprecation, log2, nextPow2, premultiplyBlendMode } from '@pixi/utils';\nimport { ViewableBuffer } from '../geometry/ViewableBuffer';\nimport { checkMaxIfStatementsInShader } from '../shader/utils/checkMaxIfStatementsInShader';\nimport { State } from '../state/State';\nimport { BaseTexture } from '../textures/BaseTexture';\nimport { BatchDrawCall } from './BatchDrawCall';\nimport { BatchGeometry } from './BatchGeometry';\nimport { BatchShaderGenerator } from './BatchShaderGenerator';\nimport { BatchTextureArray } from './BatchTextureArray';\nimport { canUploadSameBuffer } from './canUploadSameBuffer';\nimport { maxRecommendedTextures } from './maxRecommendedTextures';\nimport { ObjectRenderer } from './ObjectRenderer';\nimport defaultFragment from './texture.frag';\nimport defaultVertex from './texture.vert';\n\nimport type { BLEND_MODES } from '@pixi/constants';\nimport type { ExtensionMetadata } from '@pixi/extensions';\nimport type { Renderer } from '../Renderer';\nimport type { Shader } from '../shader/Shader';\nimport type { Texture } from '../textures/Texture';\n\n/**\n * Interface for elements like Sprite, Mesh etc. for batching.\n * @memberof PIXI\n */\nexport interface IBatchableElement\n{\n    _texture: Texture;\n    vertexData: Float32Array;\n    indices: Uint16Array | Uint32Array | Array<number>;\n    uvs: Float32Array;\n    worldAlpha: number;\n    _tintRGB: number;\n    blendMode: BLEND_MODES;\n}\n\n/**\n * Renderer dedicated to drawing and batching sprites.\n *\n * This is the default batch renderer. It buffers objects\n * with texture-based geometries and renders them in\n * batches. It uploads multiple textures to the GPU to\n * reduce to the number of draw calls.\n * @memberof PIXI\n */\nexport class BatchRenderer extends ObjectRenderer\n{\n    /**\n     * The maximum textures that this device supports.\n     * @static\n     * @default 32\n     */\n    public static get defaultMaxTextures(): number\n    {\n        this._defaultMaxTextures = this._defaultMaxTextures ?? maxRecommendedTextures(32);\n\n        return this._defaultMaxTextures;\n    }\n    public static set defaultMaxTextures(value: number)\n    {\n        this._defaultMaxTextures = value;\n    }\n\n    /** @ignore */\n    private static _defaultMaxTextures: number;\n\n    /**\n     * The default sprite batch size.\n     *\n     * The default aims to balance desktop and mobile devices.\n     * @static\n     */\n    public static defaultBatchSize = 4096;\n\n    /**\n     * Can we upload the same buffer in a single frame?\n     * @static\n     */\n    public static get canUploadSameBuffer(): boolean\n    {\n        this._canUploadSameBuffer = this._canUploadSameBuffer ?? canUploadSameBuffer();\n\n        return this._canUploadSameBuffer;\n    }\n    public static set canUploadSameBuffer(value: boolean)\n    {\n        this._canUploadSameBuffer = value;\n    }\n\n    /** @ignore */\n    private static _canUploadSameBuffer: boolean;\n\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'batch',\n        type: ExtensionType.RendererPlugin,\n    };\n\n    /** The WebGL state in which this renderer will work. */\n    public readonly state: State;\n\n    /**\n     * The number of bufferable objects before a flush\n     * occurs automatically.\n     * @default PIXI.BatchRenderer.defaultBatchSize * 4\n     */\n    public size: number;\n\n    /**\n     * Maximum number of textures that can be uploaded to\n     * the GPU under the current context. It is initialized\n     * properly in `this.contextChange`.\n     * @see PIXI.BatchRenderer#contextChange\n     * @readonly\n     */\n    public maxTextures: number;\n\n    /**\n     * This is used to generate a shader that can\n     * color each vertex based on a `aTextureId`\n     * attribute that points to an texture in `uSampler`.\n     *\n     * This enables the objects with different textures\n     * to be drawn in the same draw call.\n     *\n     * You can customize your shader by creating your\n     * custom shader generator.\n     */\n    protected shaderGenerator: BatchShaderGenerator;\n\n    /**\n     * The class that represents the geometry of objects\n     * that are going to be batched with this.\n     * @member {object}\n     * @default PIXI.BatchGeometry\n     */\n    protected geometryClass: typeof BatchGeometry;\n\n    /**\n     * Size of data being buffered per vertex in the\n     * attribute buffers (in floats). By default, the\n     * batch-renderer plugin uses 6:\n     *\n     * | aVertexPosition | 2 |\n     * |-----------------|---|\n     * | aTextureCoords  | 2 |\n     * | aColor          | 1 |\n     * | aTextureId      | 1 |\n     * @default 6\n     */\n    protected vertexSize: number;\n\n    /** Total count of all vertices used by the currently buffered objects. */\n    protected _vertexCount: number;\n\n    /** Total count of all indices used by the currently buffered objects. */\n    protected _indexCount: number;\n\n    /**\n     * Buffer of objects that are yet to be rendered.\n     * @member {PIXI.DisplayObject[]}\n     */\n    protected _bufferedElements: Array<IBatchableElement>;\n\n    /**\n     * Data for texture batch builder, helps to save a bit of CPU on a pass.\n     * @member {PIXI.BaseTexture[]}\n     */\n    protected _bufferedTextures: Array<BaseTexture>;\n\n    /** Number of elements that are buffered and are waiting to be flushed. */\n    protected _bufferSize: number;\n\n    /**\n     * This shader is generated by `this.shaderGenerator`.\n     *\n     * It is generated specifically to handle the required\n     * number of textures being batched together.\n     */\n    protected _shader: Shader;\n\n    /**\n     * A flush may occur multiple times in a single\n     * frame. On iOS devices or when\n     * `BatchRenderer.canUploadSameBuffer` is false, the\n     * batch renderer does not upload data to the same\n     * `WebGLBuffer` for performance reasons.\n     *\n     * This is the index into `packedGeometries` that points to\n     * geometry holding the most recent buffers.\n     */\n    protected _flushId: number;\n\n    /**\n     * Pool of `ViewableBuffer` objects that are sorted in\n     * order of increasing size. The flush method uses\n     * the buffer with the least size above the amount\n     * it requires. These are used for passing attributes.\n     *\n     * The first buffer has a size of 8; each subsequent\n     * buffer has double capacity of its previous.\n     * @member {PIXI.ViewableBuffer[]}\n     * @see PIXI.BatchRenderer#getAttributeBuffer\n     */\n    protected _aBuffers: Array<ViewableBuffer>;\n\n    /**\n     * Pool of `Uint16Array` objects that are sorted in\n     * order of increasing size. The flush method uses\n     * the buffer with the least size above the amount\n     * it requires. These are used for passing indices.\n     *\n     * The first buffer has a size of 12; each subsequent\n     * buffer has double capacity of its previous.\n     * @member {Uint16Array[]}\n     * @see PIXI.BatchRenderer#getIndexBuffer\n     */\n    protected _iBuffers: Array<Uint16Array>;\n    protected _dcIndex: number;\n    protected _aIndex: number;\n    protected _iIndex: number;\n    protected _attributeBuffer: ViewableBuffer;\n    protected _indexBuffer: Uint16Array;\n    protected _tempBoundTextures: BaseTexture[];\n\n    /**\n     * Pool of `this.geometryClass` geometry objects\n     * that store buffers. They are used to pass data\n     * to the shader on each draw call.\n     *\n     * These are never re-allocated again, unless a\n     * context change occurs; however, the pool may\n     * be expanded if required.\n     * @member {PIXI.Geometry[]}\n     * @see PIXI.BatchRenderer.contextChange\n     */\n    private _packedGeometries: Array<BatchGeometry>;\n\n    /**\n     * Size of `this._packedGeometries`. It can be expanded\n     * if more than `this._packedGeometryPoolSize` flushes\n     * occur in a single frame.\n     */\n    private _packedGeometryPoolSize: number;\n\n    /**\n     * This will hook onto the renderer's `contextChange`\n     * and `prerender` signals.\n     * @param {PIXI.Renderer} renderer - The renderer this works for.\n     */\n    constructor(renderer: Renderer)\n    {\n        super(renderer);\n\n        this.setShaderGenerator();\n        this.geometryClass = BatchGeometry;\n        this.vertexSize = 6;\n        this.state = State.for2d();\n        this.size = BatchRenderer.defaultBatchSize * 4;\n        this._vertexCount = 0;\n        this._indexCount = 0;\n        this._bufferedElements = [];\n        this._bufferedTextures = [];\n        this._bufferSize = 0;\n        this._shader = null;\n        this._packedGeometries = [];\n        this._packedGeometryPoolSize = 2;\n        this._flushId = 0;\n        this._aBuffers = {} as any;\n        this._iBuffers = {} as any;\n\n        this.maxTextures = 1;\n\n        this.renderer.on('prerender', this.onPrerender, this);\n        renderer.runners.contextChange.add(this);\n\n        this._dcIndex = 0;\n        this._aIndex = 0;\n        this._iIndex = 0;\n        this._attributeBuffer = null;\n        this._indexBuffer = null;\n        this._tempBoundTextures = [];\n    }\n\n    /**\n     * @see PIXI.BatchRenderer#maxTextures\n     * @deprecated since 7.1.0\n     * @readonly\n     */\n    get MAX_TEXTURES(): number\n    {\n        if (process.env.DEBUG)\n        {\n            deprecation('7.1.0', 'BatchRenderer#MAX_TEXTURES renamed to BatchRenderer#maxTextures');\n        }\n\n        return this.maxTextures;\n    }\n\n    /**\n     * The default vertex shader source\n     * @readonly\n     */\n    static get defaultVertexSrc(): string\n    {\n        return defaultVertex;\n    }\n\n    /**\n     * The default fragment shader source\n     * @readonly\n     */\n    static get defaultFragmentTemplate(): string\n    {\n        return defaultFragment;\n    }\n\n    /**\n     * Set the shader generator.\n     * @param {object} [options]\n     * @param {string} [options.vertex=PIXI.BatchRenderer.defaultVertexSrc] - Vertex shader source\n     * @param {string} [options.fragment=PIXI.BatchRenderer.defaultFragmentTemplate] - Fragment shader template\n     */\n    public setShaderGenerator({\n        vertex = BatchRenderer.defaultVertexSrc,\n        fragment = BatchRenderer.defaultFragmentTemplate\n    }: { vertex?: string, fragment?: string } = {}): void\n    {\n        this.shaderGenerator = new BatchShaderGenerator(vertex, fragment);\n    }\n\n    /**\n     * Handles the `contextChange` signal.\n     *\n     * It calculates `this.maxTextures` and allocating the packed-geometry object pool.\n     */\n    contextChange(): void\n    {\n        const gl = this.renderer.gl;\n\n        if (settings.PREFER_ENV === ENV.WEBGL_LEGACY)\n        {\n            this.maxTextures = 1;\n        }\n        else\n        {\n            // step 1: first check max textures the GPU can handle.\n            this.maxTextures = Math.min(\n                gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS),\n                BatchRenderer.defaultMaxTextures);\n\n            // step 2: check the maximum number of if statements the shader can have too..\n            this.maxTextures = checkMaxIfStatementsInShader(\n                this.maxTextures, gl);\n        }\n\n        this._shader = this.shaderGenerator.generateShader(this.maxTextures);\n\n        // we use the second shader as the first one depending on your browser\n        // may omit aTextureId as it is not used by the shader so is optimized out.\n        for (let i = 0; i < this._packedGeometryPoolSize; i++)\n        {\n            /* eslint-disable max-len */\n            this._packedGeometries[i] = new (this.geometryClass)();\n        }\n\n        this.initFlushBuffers();\n    }\n\n    /** Makes sure that static and dynamic flush pooled objects have correct dimensions. */\n    initFlushBuffers(): void\n    {\n        const {\n            _drawCallPool,\n            _textureArrayPool,\n        } = BatchRenderer;\n        // max draw calls\n        const MAX_SPRITES = this.size / 4;\n        // max texture arrays\n        const MAX_TA = Math.floor(MAX_SPRITES / this.maxTextures) + 1;\n\n        while (_drawCallPool.length < MAX_SPRITES)\n        {\n            _drawCallPool.push(new BatchDrawCall());\n        }\n        while (_textureArrayPool.length < MAX_TA)\n        {\n            _textureArrayPool.push(new BatchTextureArray());\n        }\n        for (let i = 0; i < this.maxTextures; i++)\n        {\n            this._tempBoundTextures[i] = null;\n        }\n    }\n\n    /** Handles the `prerender` signal. It ensures that flushes start from the first geometry object again. */\n    onPrerender(): void\n    {\n        this._flushId = 0;\n    }\n\n    /**\n     * Buffers the \"batchable\" object. It need not be rendered immediately.\n     * @param {PIXI.DisplayObject} element - the element to render when\n     *    using this renderer\n     */\n    render(element: IBatchableElement): void\n    {\n        if (!element._texture.valid)\n        {\n            return;\n        }\n\n        if (this._vertexCount + (element.vertexData.length / 2) > this.size)\n        {\n            this.flush();\n        }\n\n        this._vertexCount += element.vertexData.length / 2;\n        this._indexCount += element.indices.length;\n        this._bufferedTextures[this._bufferSize] = element._texture.baseTexture;\n        this._bufferedElements[this._bufferSize++] = element;\n    }\n\n    buildTexturesAndDrawCalls(): void\n    {\n        const {\n            _bufferedTextures: textures,\n            maxTextures,\n        } = this;\n        const textureArrays = BatchRenderer._textureArrayPool;\n        const batch = this.renderer.batch;\n        const boundTextures = this._tempBoundTextures;\n        const touch = this.renderer.textureGC.count;\n\n        let TICK = ++BaseTexture._globalBatch;\n        let countTexArrays = 0;\n        let texArray = textureArrays[0];\n        let start = 0;\n\n        batch.copyBoundTextures(boundTextures, maxTextures);\n\n        for (let i = 0; i < this._bufferSize; ++i)\n        {\n            const tex = textures[i];\n\n            textures[i] = null;\n            if (tex._batchEnabled === TICK)\n            {\n                continue;\n            }\n\n            if (texArray.count >= maxTextures)\n            {\n                batch.boundArray(texArray, boundTextures, TICK, maxTextures);\n                this.buildDrawCalls(texArray, start, i);\n                start = i;\n                texArray = textureArrays[++countTexArrays];\n                ++TICK;\n            }\n\n            tex._batchEnabled = TICK;\n            tex.touched = touch;\n            texArray.elements[texArray.count++] = tex;\n        }\n\n        if (texArray.count > 0)\n        {\n            batch.boundArray(texArray, boundTextures, TICK, maxTextures);\n            this.buildDrawCalls(texArray, start, this._bufferSize);\n            ++countTexArrays;\n            ++TICK;\n        }\n\n        // Clean-up\n\n        for (let i = 0; i < boundTextures.length; i++)\n        {\n            boundTextures[i] = null;\n        }\n        BaseTexture._globalBatch = TICK;\n    }\n\n    /**\n     * Populating drawcalls for rendering\n     * @param texArray\n     * @param start\n     * @param finish\n     */\n    buildDrawCalls(texArray: BatchTextureArray, start: number, finish: number): void\n    {\n        const {\n            _bufferedElements: elements,\n            _attributeBuffer,\n            _indexBuffer,\n            vertexSize,\n        } = this;\n        const drawCalls = BatchRenderer._drawCallPool;\n\n        let dcIndex = this._dcIndex;\n        let aIndex = this._aIndex;\n        let iIndex = this._iIndex;\n\n        let drawCall = drawCalls[dcIndex];\n\n        drawCall.start = this._iIndex;\n        drawCall.texArray = texArray;\n\n        for (let i = start; i < finish; ++i)\n        {\n            const sprite = elements[i];\n            const tex = sprite._texture.baseTexture;\n            const spriteBlendMode = premultiplyBlendMode[\n                tex.alphaMode ? 1 : 0][sprite.blendMode];\n\n            elements[i] = null;\n\n            if (start < i && drawCall.blend !== spriteBlendMode)\n            {\n                drawCall.size = iIndex - drawCall.start;\n                start = i;\n                drawCall = drawCalls[++dcIndex];\n                drawCall.texArray = texArray;\n                drawCall.start = iIndex;\n            }\n\n            this.packInterleavedGeometry(sprite, _attributeBuffer, _indexBuffer, aIndex, iIndex);\n            aIndex += sprite.vertexData.length / 2 * vertexSize;\n            iIndex += sprite.indices.length;\n\n            drawCall.blend = spriteBlendMode;\n        }\n\n        if (start < finish)\n        {\n            drawCall.size = iIndex - drawCall.start;\n            ++dcIndex;\n        }\n\n        this._dcIndex = dcIndex;\n        this._aIndex = aIndex;\n        this._iIndex = iIndex;\n    }\n\n    /**\n     * Bind textures for current rendering\n     * @param texArray\n     */\n    bindAndClearTexArray(texArray: BatchTextureArray): void\n    {\n        const textureSystem = this.renderer.texture;\n\n        for (let j = 0; j < texArray.count; j++)\n        {\n            textureSystem.bind(texArray.elements[j], texArray.ids[j]);\n            texArray.elements[j] = null;\n        }\n        texArray.count = 0;\n    }\n\n    updateGeometry(): void\n    {\n        const {\n            _packedGeometries: packedGeometries,\n            _attributeBuffer: attributeBuffer,\n            _indexBuffer: indexBuffer,\n        } = this;\n\n        if (!BatchRenderer.canUploadSameBuffer)\n        { /* Usually on iOS devices, where the browser doesn't\n            like uploads to the same buffer in a single frame. */\n            if (this._packedGeometryPoolSize <= this._flushId)\n            {\n                this._packedGeometryPoolSize++;\n                packedGeometries[this._flushId] = new (this.geometryClass)();\n            }\n\n            packedGeometries[this._flushId]._buffer.update(attributeBuffer.rawBinaryData);\n            packedGeometries[this._flushId]._indexBuffer.update(indexBuffer);\n\n            this.renderer.geometry.bind(packedGeometries[this._flushId]);\n            this.renderer.geometry.updateBuffers();\n            this._flushId++;\n        }\n        else\n        {\n            // lets use the faster option, always use buffer number 0\n            packedGeometries[this._flushId]._buffer.update(attributeBuffer.rawBinaryData);\n            packedGeometries[this._flushId]._indexBuffer.update(indexBuffer);\n\n            this.renderer.geometry.updateBuffers();\n        }\n    }\n\n    drawBatches(): void\n    {\n        const dcCount = this._dcIndex;\n        const { gl, state: stateSystem } = this.renderer;\n        const drawCalls = BatchRenderer._drawCallPool;\n\n        let curTexArray = null;\n\n        // Upload textures and do the draw calls\n        for (let i = 0; i < dcCount; i++)\n        {\n            const { texArray, type, size, start, blend } = drawCalls[i];\n\n            if (curTexArray !== texArray)\n            {\n                curTexArray = texArray;\n                this.bindAndClearTexArray(texArray);\n            }\n\n            this.state.blendMode = blend;\n            stateSystem.set(this.state);\n            gl.drawElements(type, size, gl.UNSIGNED_SHORT, start * 2);\n        }\n    }\n\n    /** Renders the content _now_ and empties the current batch. */\n    flush(): void\n    {\n        if (this._vertexCount === 0)\n        {\n            return;\n        }\n\n        this._attributeBuffer = this.getAttributeBuffer(this._vertexCount);\n        this._indexBuffer = this.getIndexBuffer(this._indexCount);\n        this._aIndex = 0;\n        this._iIndex = 0;\n        this._dcIndex = 0;\n\n        this.buildTexturesAndDrawCalls();\n        this.updateGeometry();\n        this.drawBatches();\n\n        // reset elements buffer for the next flush\n        this._bufferSize = 0;\n        this._vertexCount = 0;\n        this._indexCount = 0;\n    }\n\n    /** Starts a new sprite batch. */\n    start(): void\n    {\n        this.renderer.state.set(this.state);\n\n        this.renderer.texture.ensureSamplerType(this.maxTextures);\n\n        this.renderer.shader.bind(this._shader);\n\n        if (BatchRenderer.canUploadSameBuffer)\n        {\n            // bind buffer #0, we don't need others\n            this.renderer.geometry.bind(this._packedGeometries[this._flushId]);\n        }\n    }\n\n    /** Stops and flushes the current batch. */\n    stop(): void\n    {\n        this.flush();\n    }\n\n    /** Destroys this `BatchRenderer`. It cannot be used again. */\n    destroy(): void\n    {\n        for (let i = 0; i < this._packedGeometryPoolSize; i++)\n        {\n            if (this._packedGeometries[i])\n            {\n                this._packedGeometries[i].destroy();\n            }\n        }\n\n        this.renderer.off('prerender', this.onPrerender, this);\n\n        this._aBuffers = null;\n        this._iBuffers = null;\n        this._packedGeometries = null;\n        this._attributeBuffer = null;\n        this._indexBuffer = null;\n\n        if (this._shader)\n        {\n            this._shader.destroy();\n            this._shader = null;\n        }\n\n        super.destroy();\n    }\n\n    /**\n     * Fetches an attribute buffer from `this._aBuffers` that can hold atleast `size` floats.\n     * @param size - minimum capacity required\n     * @returns - buffer than can hold atleast `size` floats\n     */\n    getAttributeBuffer(size: number): ViewableBuffer\n    {\n        // 8 vertices is enough for 2 quads\n        const roundedP2 = nextPow2(Math.ceil(size / 8));\n        const roundedSizeIndex = log2(roundedP2);\n        const roundedSize = roundedP2 * 8;\n\n        if (this._aBuffers.length <= roundedSizeIndex)\n        {\n            this._iBuffers.length = roundedSizeIndex + 1;\n        }\n\n        let buffer = this._aBuffers[roundedSize];\n\n        if (!buffer)\n        {\n            this._aBuffers[roundedSize] = buffer = new ViewableBuffer(roundedSize * this.vertexSize * 4);\n        }\n\n        return buffer;\n    }\n\n    /**\n     * Fetches an index buffer from `this._iBuffers` that can\n     * have at least `size` capacity.\n     * @param size - minimum required capacity\n     * @returns - buffer that can fit `size` indices.\n     */\n    getIndexBuffer(size: number): Uint16Array\n    {\n        // 12 indices is enough for 2 quads\n        const roundedP2 = nextPow2(Math.ceil(size / 12));\n        const roundedSizeIndex = log2(roundedP2);\n        const roundedSize = roundedP2 * 12;\n\n        if (this._iBuffers.length <= roundedSizeIndex)\n        {\n            this._iBuffers.length = roundedSizeIndex + 1;\n        }\n\n        let buffer = this._iBuffers[roundedSizeIndex];\n\n        if (!buffer)\n        {\n            this._iBuffers[roundedSizeIndex] = buffer = new Uint16Array(roundedSize);\n        }\n\n        return buffer;\n    }\n\n    /**\n     * Takes the four batching parameters of `element`, interleaves\n     * and pushes them into the batching attribute/index buffers given.\n     *\n     * It uses these properties: `vertexData` `uvs`, `textureId` and\n     * `indicies`. It also uses the \"tint\" of the base-texture, if\n     * present.\n     * @param {PIXI.DisplayObject} element - element being rendered\n     * @param attributeBuffer - attribute buffer.\n     * @param indexBuffer - index buffer\n     * @param aIndex - number of floats already in the attribute buffer\n     * @param iIndex - number of indices already in `indexBuffer`\n     */\n    packInterleavedGeometry(element: IBatchableElement, attributeBuffer: ViewableBuffer, indexBuffer: Uint16Array,\n        aIndex: number, iIndex: number): void\n    {\n        const {\n            uint32View,\n            float32View,\n        } = attributeBuffer;\n\n        const packedVertices = aIndex / this.vertexSize;\n        const uvs = element.uvs;\n        const indicies = element.indices;\n        const vertexData = element.vertexData;\n        const textureId = element._texture.baseTexture._batchLocation;\n\n        const alpha = Math.min(element.worldAlpha, 1.0);\n        const argb = Color.shared\n            .setValue(element._tintRGB)\n            .toPremultiplied(alpha, element._texture.baseTexture.alphaMode > 0);\n\n        // lets not worry about tint! for now..\n        for (let i = 0; i < vertexData.length; i += 2)\n        {\n            float32View[aIndex++] = vertexData[i];\n            float32View[aIndex++] = vertexData[i + 1];\n            float32View[aIndex++] = uvs[i];\n            float32View[aIndex++] = uvs[i + 1];\n            uint32View[aIndex++] = argb;\n            float32View[aIndex++] = textureId;\n        }\n\n        for (let i = 0; i < indicies.length; i++)\n        {\n            indexBuffer[iIndex++] = packedVertices + indicies[i];\n        }\n    }\n\n    /**\n     * Pool of `BatchDrawCall` objects that `flush` used\n     * to create \"batches\" of the objects being rendered.\n     *\n     * These are never re-allocated again.\n     * Shared between all batch renderers because it can be only one \"flush\" working at the moment.\n     * @member {PIXI.BatchDrawCall[]}\n     */\n    static _drawCallPool: Array<BatchDrawCall> = [];\n\n    /**\n     * Pool of `BatchDrawCall` objects that `flush` used\n     * to create \"batches\" of the objects being rendered.\n     *\n     * These are never re-allocated again.\n     * Shared between all batch renderers because it can be only one \"flush\" working at the moment.\n     * @member {PIXI.BatchTextureArray[]}\n     */\n    static _textureArrayPool: Array<BatchTextureArray> = [];\n}\n\n// Install BatchRenderer as default\nextensions.add(BatchRenderer);\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAiDO,MAAMA,cAAA,GAAN,MAAMC,eAAA,SAAsBC,cAAA,CACnC;EAAA;AAAA;AAAA;AAAA;AAAA;EA4MIC,YAAYC,QAAA,EACZ;IACI,MAAMA,QAAQ,GAEd,KAAKC,kBAAA,CACL,QAAKC,aAAA,GAAgBC,aAAA,EACrB,KAAKC,UAAA,GAAa,GAClB,KAAKC,KAAA,GAAQC,KAAA,CAAMC,KAAA,IACnB,KAAKC,IAAA,GAAOX,eAAA,CAAcY,gBAAA,GAAmB,GAC7C,KAAKC,YAAA,GAAe,GACpB,KAAKC,WAAA,GAAc,GACnB,KAAKC,iBAAA,GAAoB,IACzB,KAAKC,iBAAA,GAAoB,IACzB,KAAKC,WAAA,GAAc,GACnB,KAAKC,OAAA,GAAU,MACf,KAAKC,iBAAA,GAAoB,EAAC,EAC1B,KAAKC,uBAAA,GAA0B,GAC/B,KAAKC,QAAA,GAAW,GAChB,KAAKC,SAAA,GAAY,CACjB,QAAKC,SAAA,GAAY,CAAC,GAElB,KAAKC,WAAA,GAAc,GAEnB,KAAKrB,QAAA,CAASsB,EAAA,CAAG,aAAa,KAAKC,WAAA,EAAa,IAAI,GACpDvB,QAAA,CAASwB,OAAA,CAAQC,aAAA,CAAcC,GAAA,CAAI,IAAI,GAEvC,KAAKC,QAAA,GAAW,GAChB,KAAKC,OAAA,GAAU,GACf,KAAKC,OAAA,GAAU,GACf,KAAKC,gBAAA,GAAmB,MACxB,KAAKC,YAAA,GAAe,MACpB,KAAKC,kBAAA,GAAqB;EAC9B;EAAA;AAAA;AAAA;AAAA;AAAA;EAtOA,WAAkBC,mBAAA,EAClB;IACI,YAAKC,mBAAA,GAAsB,KAAKA,mBAAA,IAAuBC,sBAAA,CAAuB,EAAE,GAEzE,KAAKD,mBAAA;EAChB;EACA,WAAkBD,mBAAmBG,KAAA,EACrC;IACI,KAAKF,mBAAA,GAAsBE,KAAA;EAC/B;EAAA;AAAA;AAAA;AAAA;EAiBA,WAAkBC,oBAAA,EAClB;IACI,YAAKC,oBAAA,GAAuB,KAAKA,oBAAA,IAAwBD,mBAAA,IAElD,KAAKC,oBAAA;EAChB;EACA,WAAkBD,oBAAoBD,KAAA,EACtC;IACI,KAAKE,oBAAA,GAAuBF,KAAA;EAChC;EAAA;AAAA;AAAA;AAAA;AAAA;EA0MA,IAAIG,aAAA,EACJ;IAGoB,OAAAC,WAAA,UAAS,iEAAiE,GAGnF,KAAKnB,WAAA;EAChB;EAAA;AAAA;AAAA;AAAA;EAMA,WAAWoB,iBAAA,EACX;IACW,OAAAC,aAAA;EACX;EAAA;AAAA;AAAA;AAAA;EAMA,WAAWC,wBAAA,EACX;IACW,OAAAC,eAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQO3C,mBAAmB;IACtB4C,MAAA,GAAShD,eAAA,CAAc4C,gBAAA;IACvBK,QAAA,GAAWjD,eAAA,CAAc8C;EAC7B,IAA4C,IAC5C;IACI,KAAKI,eAAA,GAAkB,IAAIC,oBAAA,CAAqBH,MAAA,EAAQC,QAAQ;EACpE;EAAA;AAAA;AAAA;AAAA;AAAA;EAOArB,cAAA,EACA;IACU,MAAAwB,EAAA,GAAK,KAAKjD,QAAA,CAASiD,EAAA;IAErBC,QAAA,CAASC,UAAA,KAAeC,GAAA,CAAIC,YAAA,GAE5B,KAAKhC,WAAA,GAAc,KAKnB,KAAKA,WAAA,GAAciC,IAAA,CAAKC,GAAA,CACpBN,EAAA,CAAGO,YAAA,CAAaP,EAAA,CAAGQ,uBAAuB,GAC1C5D,eAAA,CAAcoC,kBAAA,GAGlB,KAAKZ,WAAA,GAAcqC,4BAAA,CACf,KAAKrC,WAAA,EAAa4B,EAAA,IAG1B,KAAKlC,OAAA,GAAU,KAAKgC,eAAA,CAAgBY,cAAA,CAAe,KAAKtC,WAAW;IAInE,SAASuC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK3C,uBAAA,EAAyB2C,CAAA,IAG9C,KAAK5C,iBAAA,CAAkB4C,CAAC,IAAI,IAAK,KAAK1D,aAAA,CAAe;IAGzD,KAAK2D,gBAAA,CAAiB;EAC1B;EAAA;EAGAA,iBAAA,EACA;IACU;QACFC,aAAA;QACAC;MACA,IAAAlE,eAAA;MAEEmE,WAAA,GAAc,KAAKxD,IAAA,GAAO;MAE1ByD,MAAA,GAASX,IAAA,CAAKY,KAAA,CAAMF,WAAA,GAAc,KAAK3C,WAAW,IAAI;IAE5D,OAAOyC,aAAA,CAAcK,MAAA,GAASH,WAAA,GAEZF,aAAA,CAAAM,IAAA,CAAK,IAAIC,aAAA,EAAe;IAE1C,OAAON,iBAAA,CAAkBI,MAAA,GAASF,MAAA,GAEZF,iBAAA,CAAAK,IAAA,CAAK,IAAIE,iBAAA,EAAmB;IAElD,SAASV,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKvC,WAAA,EAAauC,CAAA,IAE7B,KAAA5B,kBAAA,CAAmB4B,CAAC,IAAI;EAErC;EAAA;EAGArC,YAAA,EACA;IACI,KAAKL,QAAA,GAAW;EACpB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAqD,OAAOC,OAAA,EACP;IACSA,OAAA,CAAQC,QAAA,CAASC,KAAA,KAKlB,KAAKhE,YAAA,GAAgB8D,OAAA,CAAQG,UAAA,CAAWR,MAAA,GAAS,IAAK,KAAK3D,IAAA,IAE3D,KAAKoE,KAAA,IAGT,KAAKlE,YAAA,IAAgB8D,OAAA,CAAQG,UAAA,CAAWR,MAAA,GAAS,GACjD,KAAKxD,WAAA,IAAe6D,OAAA,CAAQK,OAAA,CAAQV,MAAA,EACpC,KAAKtD,iBAAA,CAAkB,KAAKC,WAAW,IAAI0D,OAAA,CAAQC,QAAA,CAASK,WAAA,EAC5D,KAAKlE,iBAAA,CAAkB,KAAKE,WAAA,EAAa,IAAI0D,OAAA;EACjD;EAEAO,0BAAA,EACA;IACU;QACFlE,iBAAA,EAAmBmE,QAAA;QACnB3D;MAAA,IACA;MACE4D,aAAA,GAAgBpF,eAAA,CAAckE,iBAAA;MAC9BmB,KAAA,GAAQ,KAAKlF,QAAA,CAASkF,KAAA;MACtBC,aAAA,GAAgB,KAAKnD,kBAAA;MACrBoD,KAAA,GAAQ,KAAKpF,QAAA,CAASqF,SAAA,CAAUC,KAAA;IAElC,IAAAC,IAAA,GAAO,EAAEC,WAAA,CAAYC,YAAA;MACrBC,cAAA,GAAiB;MACjBC,QAAA,GAAWV,aAAA,CAAc,CAAC;MAC1BW,KAAA,GAAQ;IAENV,KAAA,CAAAW,iBAAA,CAAkBV,aAAA,EAAe9D,WAAW;IAElD,SAASuC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK9C,WAAA,EAAa,EAAE8C,CAAA,EACxC;MACU,MAAAkC,GAAA,GAAMd,QAAA,CAASpB,CAAC;MAEtBoB,QAAA,CAASpB,CAAC,IAAI,MACVkC,GAAA,CAAIC,aAAA,KAAkBR,IAAA,KAKtBI,QAAA,CAASL,KAAA,IAASjE,WAAA,KAElB6D,KAAA,CAAMc,UAAA,CAAWL,QAAA,EAAUR,aAAA,EAAeI,IAAA,EAAMlE,WAAW,GAC3D,KAAK4E,cAAA,CAAeN,QAAA,EAAUC,KAAA,EAAOhC,CAAC,GACtCgC,KAAA,GAAQhC,CAAA,EACR+B,QAAA,GAAWV,aAAA,CAAc,EAAES,cAAc,GACzC,EAAEH,IAAA,GAGNO,GAAA,CAAIC,aAAA,GAAgBR,IAAA,EACpBO,GAAA,CAAII,OAAA,GAAUd,KAAA,EACdO,QAAA,CAASQ,QAAA,CAASR,QAAA,CAASL,KAAA,EAAO,IAAIQ,GAAA;IAC1C;IAEIH,QAAA,CAASL,KAAA,GAAQ,MAEjBJ,KAAA,CAAMc,UAAA,CAAWL,QAAA,EAAUR,aAAA,EAAeI,IAAA,EAAMlE,WAAW,GAC3D,KAAK4E,cAAA,CAAeN,QAAA,EAAUC,KAAA,EAAO,KAAK9E,WAAW,GACrD,EAAE4E,cAAA,EACF,EAAEH,IAAA;IAKN,SAAS3B,CAAA,GAAI,GAAGA,CAAA,GAAIuB,aAAA,CAAchB,MAAA,EAAQP,CAAA,IAEtCuB,aAAA,CAAcvB,CAAC,IAAI;IAEvB4B,WAAA,CAAYC,YAAA,GAAeF,IAAA;EAC/B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAU,eAAeN,QAAA,EAA6BC,KAAA,EAAeQ,MAAA,EAC3D;IACU;QACFxF,iBAAA,EAAmBuF,QAAA;QACnBrE,gBAAA;QACAC,YAAA;QACA3B;MACJ,IAAI;MACEiG,SAAA,GAAYxG,eAAA,CAAciE,aAAA;IAE5B,IAAAwC,OAAA,GAAU,KAAK3E,QAAA;MACf4E,MAAA,GAAS,KAAK3E,OAAA;MACd4E,MAAA,GAAS,KAAK3E,OAAA;MAEd4E,QAAA,GAAWJ,SAAA,CAAUC,OAAO;IAEhCG,QAAA,CAASb,KAAA,GAAQ,KAAK/D,OAAA,EACtB4E,QAAA,CAASd,QAAA,GAAWA,QAAA;IAEpB,SAAS/B,CAAA,GAAIgC,KAAA,EAAOhC,CAAA,GAAIwC,MAAA,EAAQ,EAAExC,CAAA,EAClC;MACI,MAAM8C,MAAA,GAASP,QAAA,CAASvC,CAAC;QACnBkC,GAAA,GAAMY,MAAA,CAAOjC,QAAA,CAASK,WAAA;QACtB6B,eAAA,GAAkBC,oBAAA,CACpBd,GAAA,CAAIe,SAAA,GAAY,IAAI,CAAC,EAAEH,MAAA,CAAOI,SAAS;MAElCX,QAAA,CAAAvC,CAAC,IAAI,MAEVgC,KAAA,GAAQhC,CAAA,IAAK6C,QAAA,CAASM,KAAA,KAAUJ,eAAA,KAEhCF,QAAA,CAASjG,IAAA,GAAOgG,MAAA,GAASC,QAAA,CAASb,KAAA,EAClCA,KAAA,GAAQhC,CAAA,EACR6C,QAAA,GAAWJ,SAAA,CAAU,EAAEC,OAAO,GAC9BG,QAAA,CAASd,QAAA,GAAWA,QAAA,EACpBc,QAAA,CAASb,KAAA,GAAQY,MAAA,GAGrB,KAAKQ,uBAAA,CAAwBN,MAAA,EAAQ5E,gBAAA,EAAkBC,YAAA,EAAcwE,MAAA,EAAQC,MAAM,GACnFD,MAAA,IAAUG,MAAA,CAAO/B,UAAA,CAAWR,MAAA,GAAS,IAAI/D,UAAA,EACzCoG,MAAA,IAAUE,MAAA,CAAO7B,OAAA,CAAQV,MAAA,EAEzBsC,QAAA,CAASM,KAAA,GAAQJ,eAAA;IACrB;IAEIf,KAAA,GAAQQ,MAAA,KAERK,QAAA,CAASjG,IAAA,GAAOgG,MAAA,GAASC,QAAA,CAASb,KAAA,EAClC,EAAEU,OAAA,GAGN,KAAK3E,QAAA,GAAW2E,OAAA,EAChB,KAAK1E,OAAA,GAAU2E,MAAA,EACf,KAAK1E,OAAA,GAAU2E,MAAA;EACnB;EAAA;AAAA;AAAA;AAAA;EAMAS,qBAAqBtB,QAAA,EACrB;IACU,MAAAuB,aAAA,GAAgB,KAAKlH,QAAA,CAASmH,OAAA;IAEpC,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIzB,QAAA,CAASL,KAAA,EAAO8B,CAAA,IAEhCF,aAAA,CAAcG,IAAA,CAAK1B,QAAA,CAASQ,QAAA,CAASiB,CAAC,GAAGzB,QAAA,CAAS2B,GAAA,CAAIF,CAAC,CAAC,GACxDzB,QAAA,CAASQ,QAAA,CAASiB,CAAC,IAAI;IAE3BzB,QAAA,CAASL,KAAA,GAAQ;EACrB;EAEAiC,eAAA,EACA;IACU;MACFvG,iBAAA,EAAmBwG,gBAAA;MACnB1F,gBAAA,EAAkB2F,eAAA;MAClB1F,YAAA,EAAc2F;IACd;IAEC7H,eAAA,CAAcwC,mBAAA,IAmBfmF,gBAAA,CAAiB,KAAKtG,QAAQ,EAAEyG,OAAA,CAAQC,MAAA,CAAOH,eAAA,CAAgBI,aAAa,GAC5EL,gBAAA,CAAiB,KAAKtG,QAAQ,EAAEa,YAAA,CAAa6F,MAAA,CAAOF,WAAW,GAE/D,KAAK1H,QAAA,CAAS8H,QAAA,CAASC,aAAA,CAnBnB,WAAK9G,uBAAA,IAA2B,KAAKC,QAAA,KAErC,KAAKD,uBAAA,IACLuG,gBAAA,CAAiB,KAAKtG,QAAQ,IAAI,IAAK,KAAKhB,aAAA,CAAe,IAG/DsH,gBAAA,CAAiB,KAAKtG,QAAQ,EAAEyG,OAAA,CAAQC,MAAA,CAAOH,eAAA,CAAgBI,aAAa,GAC5EL,gBAAA,CAAiB,KAAKtG,QAAQ,EAAEa,YAAA,CAAa6F,MAAA,CAAOF,WAAW,GAE/D,KAAK1H,QAAA,CAAS8H,QAAA,CAAST,IAAA,CAAKG,gBAAA,CAAiB,KAAKtG,QAAQ,CAAC,GAC3D,KAAKlB,QAAA,CAAS8H,QAAA,CAASC,aAAA,IACvB,KAAK7G,QAAA;EAUb;EAEA8G,YAAA,EACA;IACU,MAAAC,OAAA,GAAU,KAAKtG,QAAA;MACf;QAAEsB,EAAA;QAAI5C,KAAA,EAAO6H;MAAgB,SAAKlI,QAAA;MAClCqG,SAAA,GAAYxG,eAAA,CAAciE,aAAA;IAEhC,IAAIqE,WAAA,GAAc;IAGlB,SAASvE,CAAA,GAAI,GAAGA,CAAA,GAAIqE,OAAA,EAASrE,CAAA,IAC7B;MACU;QAAE+B,QAAA;QAAUyC,IAAA;QAAM5H,IAAA;QAAMoF,KAAA;QAAOmB;MAAM,IAAIV,SAAA,CAAUzC,CAAC;MAEtDuE,WAAA,KAAgBxC,QAAA,KAEhBwC,WAAA,GAAcxC,QAAA,EACd,KAAKsB,oBAAA,CAAqBtB,QAAQ,IAGtC,KAAKtF,KAAA,CAAMyG,SAAA,GAAYC,KAAA,EACvBmB,WAAA,CAAYG,GAAA,CAAI,KAAKhI,KAAK,GAC1B4C,EAAA,CAAGqF,YAAA,CAAaF,IAAA,EAAM5H,IAAA,EAAMyC,EAAA,CAAGsF,cAAA,EAAgB3C,KAAA,GAAQ,CAAC;IAC5D;EACJ;EAAA;EAGAhB,MAAA,EACA;IACQ,KAAKlE,YAAA,KAAiB,MAK1B,KAAKoB,gBAAA,GAAmB,KAAK0G,kBAAA,CAAmB,KAAK9H,YAAY,GACjE,KAAKqB,YAAA,GAAe,KAAK0G,cAAA,CAAe,KAAK9H,WAAW,GACxD,KAAKiB,OAAA,GAAU,GACf,KAAKC,OAAA,GAAU,GACf,KAAKF,QAAA,GAAW,GAEhB,KAAKoD,yBAAA,CAA0B,GAC/B,KAAKwC,cAAA,CAAe,GACpB,KAAKS,WAAA,CAAY,GAGjB,KAAKlH,WAAA,GAAc,GACnB,KAAKJ,YAAA,GAAe,GACpB,KAAKC,WAAA,GAAc;EACvB;EAAA;EAGAiF,MAAA,EACA;IACI,KAAK5F,QAAA,CAASK,KAAA,CAAMgI,GAAA,CAAI,KAAKhI,KAAK,GAElC,KAAKL,QAAA,CAASmH,OAAA,CAAQuB,iBAAA,CAAkB,KAAKrH,WAAW,GAExD,KAAKrB,QAAA,CAAS2I,MAAA,CAAOtB,IAAA,CAAK,KAAKtG,OAAO,GAElClB,eAAA,CAAcwC,mBAAA,IAGd,KAAKrC,QAAA,CAAS8H,QAAA,CAAST,IAAA,CAAK,KAAKrG,iBAAA,CAAkB,KAAKE,QAAQ,CAAC;EAEzE;EAAA;EAGA0H,KAAA,EACA;IACI,KAAKhE,KAAA,CAAM;EACf;EAAA;EAGAiE,QAAA,EACA;IACI,SAASjF,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK3C,uBAAA,EAAyB2C,CAAA,IAE1C,KAAK5C,iBAAA,CAAkB4C,CAAC,KAExB,KAAK5C,iBAAA,CAAkB4C,CAAC,EAAEiF,OAAA;IAIlC,KAAK7I,QAAA,CAAS8I,GAAA,CAAI,aAAa,KAAKvH,WAAA,EAAa,IAAI,GAErD,KAAKJ,SAAA,GAAY,MACjB,KAAKC,SAAA,GAAY,MACjB,KAAKJ,iBAAA,GAAoB,MACzB,KAAKc,gBAAA,GAAmB,MACxB,KAAKC,YAAA,GAAe,MAEhB,KAAKhB,OAAA,KAEL,KAAKA,OAAA,CAAQ8H,OAAA,IACb,KAAK9H,OAAA,GAAU,OAGnB,MAAM8H,OAAA;EACV;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAL,mBAAmBhI,IAAA,EACnB;IAEI,MAAMuI,SAAA,GAAYC,QAAA,CAAS1F,IAAA,CAAK2F,IAAA,CAAKzI,IAAA,GAAO,CAAC,CAAC;MACxC0I,gBAAA,GAAmBC,IAAA,CAAKJ,SAAS;MACjCK,WAAA,GAAcL,SAAA,GAAY;IAE5B,KAAK5H,SAAA,CAAUgD,MAAA,IAAU+E,gBAAA,KAEzB,KAAK9H,SAAA,CAAU+C,MAAA,GAAS+E,gBAAA,GAAmB;IAG3C,IAAAG,MAAA,GAAS,KAAKlI,SAAA,CAAUiI,WAAW;IAEvC,OAAKC,MAAA,KAED,KAAKlI,SAAA,CAAUiI,WAAW,IAAIC,MAAA,GAAS,IAAIC,cAAA,CAAeF,WAAA,GAAc,KAAKhJ,UAAA,GAAa,CAAC,IAGxFiJ,MAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAZ,eAAejI,IAAA,EACf;IAEI,MAAMuI,SAAA,GAAYC,QAAA,CAAS1F,IAAA,CAAK2F,IAAA,CAAKzI,IAAA,GAAO,EAAE,CAAC;MACzC0I,gBAAA,GAAmBC,IAAA,CAAKJ,SAAS;MACjCK,WAAA,GAAcL,SAAA,GAAY;IAE5B,KAAK3H,SAAA,CAAU+C,MAAA,IAAU+E,gBAAA,KAEzB,KAAK9H,SAAA,CAAU+C,MAAA,GAAS+E,gBAAA,GAAmB;IAG3C,IAAAG,MAAA,GAAS,KAAKjI,SAAA,CAAU8H,gBAAgB;IAEvC,OAAAG,MAAA,KAED,KAAKjI,SAAA,CAAU8H,gBAAgB,IAAIG,MAAA,GAAS,IAAIE,WAAA,CAAYH,WAAW,IAGpEC,MAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAeArC,wBAAwBxC,OAAA,EAA4BiD,eAAA,EAAiCC,WAAA,EACjFnB,MAAA,EAAgBC,MAAA,EACpB;IACU;QACFgD,UAAA;QACAC;MAAA,IACAhC,eAAA;MAEEiC,cAAA,GAAiBnD,MAAA,GAAS,KAAKnG,UAAA;MAC/BuJ,GAAA,GAAMnF,OAAA,CAAQmF,GAAA;MACdC,QAAA,GAAWpF,OAAA,CAAQK,OAAA;MACnBF,UAAA,GAAaH,OAAA,CAAQG,UAAA;MACrBkF,SAAA,GAAYrF,OAAA,CAAQC,QAAA,CAASK,WAAA,CAAYgF,cAAA;MAEzCC,KAAA,GAAQzG,IAAA,CAAKC,GAAA,CAAIiB,OAAA,CAAQwF,UAAA,EAAY,CAAG;MACxCC,IAAA,GAAOC,KAAA,CAAMC,MAAA,CACdC,QAAA,CAAS5F,OAAA,CAAQ6F,QAAQ,EACzBC,eAAA,CAAgBP,KAAA,EAAOvF,OAAA,CAAQC,QAAA,CAASK,WAAA,CAAY+B,SAAA,GAAY,CAAC;IAGtE,SAASjD,CAAA,GAAI,GAAGA,CAAA,GAAIe,UAAA,CAAWR,MAAA,EAAQP,CAAA,IAAK,GAExC6F,WAAA,CAAYlD,MAAA,EAAQ,IAAI5B,UAAA,CAAWf,CAAC,GACpC6F,WAAA,CAAYlD,MAAA,EAAQ,IAAI5B,UAAA,CAAWf,CAAA,GAAI,CAAC,GACxC6F,WAAA,CAAYlD,MAAA,EAAQ,IAAIoD,GAAA,CAAI/F,CAAC,GAC7B6F,WAAA,CAAYlD,MAAA,EAAQ,IAAIoD,GAAA,CAAI/F,CAAA,GAAI,CAAC,GACjC4F,UAAA,CAAWjD,MAAA,EAAQ,IAAI0D,IAAA,EACvBR,WAAA,CAAYlD,MAAA,EAAQ,IAAIsD,SAAA;IAG5B,SAASjG,CAAA,GAAI,GAAGA,CAAA,GAAIgG,QAAA,CAASzF,MAAA,EAAQP,CAAA,IAEjC8D,WAAA,CAAYlB,MAAA,EAAQ,IAAIkD,cAAA,GAAiBE,QAAA,CAAShG,CAAC;EAE3D;AAqBJ;AAnwBahE,cAAA,CA2BKa,gBAAA,GAAmB;AA3BxBb,cAAA,CAgDF2K,SAAA,GAA+B;EAClCC,IAAA,EAAM;EACNpC,IAAA,EAAMqC,aAAA,CAAcC;AACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAnDS9K,cAAA,CAwvBFkE,aAAA,GAAsC,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAxvBrClE,cAAA,CAkwBFmE,iBAAA,GAA8C;AAlwBlD,IAAM4G,aAAA,GAAN/K,cAAA;AAswBPgL,UAAA,CAAWlJ,GAAA,CAAIiJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}