{"ast": null, "code": "import { Matrix } from \"@pixi/math\";\nimport { Program } from \"../shader/Program.mjs\";\nimport { Shader } from \"../shader/Shader.mjs\";\nimport { UniformGroup } from \"../shader/UniformGroup.mjs\";\nclass BatchShaderGenerator {\n  /**\n   * @param vertexSrc - Vertex shader\n   * @param fragTemplate - Fragment shader template\n   */\n  constructor(vertexSrc, fragTemplate) {\n    if (this.vertexSrc = vertexSrc, this.fragTemplate = fragTemplate, this.programCache = {}, this.defaultGroupCache = {}, !fragTemplate.includes(\"%count%\")) throw new Error('Fragment template must contain \"%count%\".');\n    if (!fragTemplate.includes(\"%forloop%\")) throw new Error('Fragment template must contain \"%forloop%\".');\n  }\n  generateShader(maxTextures) {\n    if (!this.programCache[maxTextures]) {\n      const sampleValues = new Int32Array(maxTextures);\n      for (let i = 0; i < maxTextures; i++) sampleValues[i] = i;\n      this.defaultGroupCache[maxTextures] = UniformGroup.from({\n        uSamplers: sampleValues\n      }, !0);\n      let fragmentSrc = this.fragTemplate;\n      fragmentSrc = fragmentSrc.replace(/%count%/gi, `${maxTextures}`), fragmentSrc = fragmentSrc.replace(/%forloop%/gi, this.generateSampleSrc(maxTextures)), this.programCache[maxTextures] = new Program(this.vertexSrc, fragmentSrc);\n    }\n    const uniforms = {\n      tint: new Float32Array([1, 1, 1, 1]),\n      translationMatrix: new Matrix(),\n      default: this.defaultGroupCache[maxTextures]\n    };\n    return new Shader(this.programCache[maxTextures], uniforms);\n  }\n  generateSampleSrc(maxTextures) {\n    let src = \"\";\n    src += `\n`, src += `\n`;\n    for (let i = 0; i < maxTextures; i++) i > 0 && (src += `\nelse `), i < maxTextures - 1 && (src += `if(vTextureId < ${i}.5)`), src += `\n{`, src += `\n\tcolor = texture2D(uSamplers[${i}], vTextureCoord);`, src += `\n}`;\n    return src += `\n`, src += `\n`, src;\n  }\n}\nexport { BatchShaderGenerator };", "map": {"version": 3, "names": ["BatchShaderGenerator", "constructor", "vertexSrc", "fragTemplate", "programCache", "defaultGroupCache", "includes", "Error", "generateShader", "maxTextures", "sampleValues", "Int32Array", "i", "UniformGroup", "from", "uSamplers", "fragmentSrc", "replace", "generateSampleSrc", "Program", "uniforms", "tint", "Float32Array", "translationMatrix", "Matrix", "default", "Shader", "src"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\batch\\BatchShaderGenerator.ts"], "sourcesContent": ["import { Matrix } from '@pixi/math';\nimport { Program } from '../shader/Program';\nimport { Shader } from '../shader/Shader';\nimport { UniformGroup } from '../shader/UniformGroup';\n\n/**\n * Helper that generates batching multi-texture shader. Use it with your new BatchRenderer\n * @memberof PIXI\n */\nexport class BatchShaderGenerator\n{\n    /** Reference to the vertex shader source. */\n    public vertexSrc: string;\n\n    /** Reference to the fragment shader template. Must contain \"%count%\" and \"%forloop%\". */\n    public fragTemplate: string;\n\n    programCache: {[key: number]: Program};\n    defaultGroupCache: {[key: number]: UniformGroup};\n\n    /**\n     * @param vertexSrc - Vertex shader\n     * @param fragTemplate - Fragment shader template\n     */\n    constructor(vertexSrc: string, fragTemplate: string)\n    {\n        this.vertexSrc = vertexSrc;\n        this.fragTemplate = fragTemplate;\n\n        this.programCache = {};\n        this.defaultGroupCache = {};\n\n        if (!fragTemplate.includes('%count%'))\n        {\n            throw new Error('Fragment template must contain \"%count%\".');\n        }\n\n        if (!fragTemplate.includes('%forloop%'))\n        {\n            throw new Error('Fragment template must contain \"%forloop%\".');\n        }\n    }\n\n    generateShader(maxTextures: number): Shader\n    {\n        if (!this.programCache[maxTextures])\n        {\n            const sampleValues = new Int32Array(maxTextures);\n\n            for (let i = 0; i < maxTextures; i++)\n            {\n                sampleValues[i] = i;\n            }\n\n            this.defaultGroupCache[maxTextures] = UniformGroup.from({ uSamplers: sampleValues }, true);\n\n            let fragmentSrc = this.fragTemplate;\n\n            fragmentSrc = fragmentSrc.replace(/%count%/gi, `${maxTextures}`);\n            fragmentSrc = fragmentSrc.replace(/%forloop%/gi, this.generateSampleSrc(maxTextures));\n\n            this.programCache[maxTextures] = new Program(this.vertexSrc, fragmentSrc);\n        }\n\n        const uniforms = {\n            tint: new Float32Array([1, 1, 1, 1]),\n            translationMatrix: new Matrix(),\n            default: this.defaultGroupCache[maxTextures],\n        };\n\n        return new Shader(this.programCache[maxTextures], uniforms);\n    }\n\n    generateSampleSrc(maxTextures: number): string\n    {\n        let src = '';\n\n        src += '\\n';\n        src += '\\n';\n\n        for (let i = 0; i < maxTextures; i++)\n        {\n            if (i > 0)\n            {\n                src += '\\nelse ';\n            }\n\n            if (i < maxTextures - 1)\n            {\n                src += `if(vTextureId < ${i}.5)`;\n            }\n\n            src += '\\n{';\n            src += `\\n\\tcolor = texture2D(uSamplers[${i}], vTextureCoord);`;\n            src += '\\n}';\n        }\n\n        src += '\\n';\n        src += '\\n';\n\n        return src;\n    }\n}\n"], "mappings": ";;;;AASO,MAAMA,oBAAA,CACb;EAAA;AAAA;AAAA;AAAA;EAcIC,YAAYC,SAAA,EAAmBC,YAAA,EAC/B;IAOI,IANA,KAAKD,SAAA,GAAYA,SAAA,EACjB,KAAKC,YAAA,GAAeA,YAAA,EAEpB,KAAKC,YAAA,GAAe,CAAC,GACrB,KAAKC,iBAAA,GAAoB,IAErB,CAACF,YAAA,CAAaG,QAAA,CAAS,SAAS,GAE1B,UAAIC,KAAA,CAAM,2CAA2C;IAG3D,KAACJ,YAAA,CAAaG,QAAA,CAAS,WAAW,GAE5B,UAAIC,KAAA,CAAM,6CAA6C;EAErE;EAEAC,eAAeC,WAAA,EACf;IACI,IAAI,CAAC,KAAKL,YAAA,CAAaK,WAAW,GAClC;MACU,MAAAC,YAAA,GAAe,IAAIC,UAAA,CAAWF,WAAW;MAEtC,SAAAG,CAAA,GAAI,GAAGA,CAAA,GAAIH,WAAA,EAAaG,CAAA,IAE7BF,YAAA,CAAaE,CAAC,IAAIA,CAAA;MAGjB,KAAAP,iBAAA,CAAkBI,WAAW,IAAII,YAAA,CAAaC,IAAA,CAAK;QAAEC,SAAA,EAAWL;MAAa,GAAG,EAAI;MAEzF,IAAIM,WAAA,GAAc,KAAKb,YAAA;MAETa,WAAA,GAAAA,WAAA,CAAYC,OAAA,CAAQ,aAAa,GAAGR,WAAW,EAAE,GAC/DO,WAAA,GAAcA,WAAA,CAAYC,OAAA,CAAQ,eAAe,KAAKC,iBAAA,CAAkBT,WAAW,CAAC,GAEpF,KAAKL,YAAA,CAAaK,WAAW,IAAI,IAAIU,OAAA,CAAQ,KAAKjB,SAAA,EAAWc,WAAW;IAC5E;IAEA,MAAMI,QAAA,GAAW;MACbC,IAAA,EAAM,IAAIC,YAAA,CAAa,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;MACnCC,iBAAA,EAAmB,IAAIC,MAAA,CAAO;MAC9BC,OAAA,EAAS,KAAKpB,iBAAA,CAAkBI,WAAW;IAAA;IAG/C,OAAO,IAAIiB,MAAA,CAAO,KAAKtB,YAAA,CAAaK,WAAW,GAAGW,QAAQ;EAC9D;EAEAF,kBAAkBT,WAAA,EAClB;IACI,IAAIkB,GAAA,GAAM;IAEHA,GAAA;AAAA,GACPA,GAAA,IAAO;AAAA;IAEE,SAAAf,CAAA,GAAI,GAAGA,CAAA,GAAIH,WAAA,EAAaG,CAAA,IAEzBA,CAAA,GAAI,MAEJe,GAAA,IAAO;AAAA,SAGPf,CAAA,GAAIH,WAAA,GAAc,MAElBkB,GAAA,IAAO,mBAAmBf,CAAC,QAG/Be,GAAA,IAAO;AAAA,IACPA,GAAA,IAAO;AAAA,+BAAmCf,CAAC,sBAC3Ce,GAAA,IAAO;AAAA;IAGJ,OAAAA,GAAA;AAAA,GACPA,GAAA,IAAO;AAAA,GAEAA,GAAA;EACX;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}