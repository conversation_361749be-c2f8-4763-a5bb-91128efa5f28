{"ast": null, "code": "import { ALPHA_MODES } from \"@pixi/constants\";\nimport { Resource } from \"./Resource.mjs\";\nclass BufferResource extends Resource {\n  /**\n   * @param source - Source buffer\n   * @param options - Options\n   * @param {number} options.width - Width of the texture\n   * @param {number} options.height - Height of the texture\n   * @param {1|2|4|8} [options.unpackAlignment=4] - The alignment of the pixel rows.\n   */\n  constructor(source, options) {\n    const {\n      width,\n      height\n    } = options || {};\n    if (!width || !height) throw new Error(\"BufferResource width or height invalid\");\n    super(width, height), this.data = source, this.unpackAlignment = options.unpackAlignment ?? 4;\n  }\n  /**\n   * Upload the texture to the GPU.\n   * @param renderer - Upload to the renderer\n   * @param baseTexture - Reference to parent texture\n   * @param glTexture - glTexture\n   * @returns - true is success\n   */\n  upload(renderer, baseTexture, glTexture) {\n    const gl = renderer.gl;\n    gl.pixelStorei(gl.UNPACK_ALIGNMENT, this.unpackAlignment), gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, baseTexture.alphaMode === ALPHA_MODES.UNPACK);\n    const width = baseTexture.realWidth,\n      height = baseTexture.realHeight;\n    return glTexture.width === width && glTexture.height === height ? gl.texSubImage2D(baseTexture.target, 0, 0, 0, width, height, baseTexture.format, glTexture.type, this.data) : (glTexture.width = width, glTexture.height = height, gl.texImage2D(baseTexture.target, 0, glTexture.internalFormat, width, height, 0, baseTexture.format, glTexture.type, this.data)), !0;\n  }\n  /** Destroy and don't use after this. */\n  dispose() {\n    this.data = null;\n  }\n  /**\n   * Used to auto-detect the type of resource.\n   * @param {*} source - The source object\n   * @returns {boolean} `true` if buffer source\n   */\n  static test(source) {\n    return source === null || source instanceof Int8Array || source instanceof Uint8Array || source instanceof Uint8ClampedArray || source instanceof Int16Array || source instanceof Uint16Array || source instanceof Int32Array || source instanceof Uint32Array || source instanceof Float32Array;\n  }\n}\nexport { BufferResource };", "map": {"version": 3, "names": ["BufferResource", "Resource", "constructor", "source", "options", "width", "height", "Error", "data", "unpackAlignment", "upload", "renderer", "baseTexture", "glTexture", "gl", "pixelStorei", "UNPACK_ALIGNMENT", "UNPACK_PREMULTIPLY_ALPHA_WEBGL", "alphaMode", "ALPHA_MODES", "UNPACK", "realWidth", "realHeight", "texSubImage2D", "target", "format", "type", "texImage2D", "internalFormat", "dispose", "test", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\textures\\resources\\BufferResource.ts"], "sourcesContent": ["import { ALPHA_MODES } from '@pixi/constants';\nimport { Resource } from './Resource';\n\nimport type { ISize } from '@pixi/math';\nimport type { Renderer } from '../../Renderer';\nimport type { BaseTexture } from '../BaseTexture';\nimport type { GLTexture } from '../GLTexture';\n\nexport type BufferType = null | Int8Array | Uint8Array | Uint8ClampedArray\n| Int16Array | Uint16Array | Int32Array | Uint32Array | Float32Array;\n\n/**\n * Constructor options for BufferResource.\n * @memberof PIXI\n */\nexport interface IBufferResourceOptions extends ISize\n{\n    unpackAlignment?: 1 | 2 | 4 | 8\n}\n\n/**\n * Buffer resource with data of typed array.\n * @memberof PIXI\n */\nexport class BufferResource extends Resource\n{\n    /** The data of this resource. */\n    public data: BufferType;\n\n    /** The alignment of the rows in the data. */\n    public unpackAlignment: 1 | 2 | 4 | 8;\n\n    /**\n     * @param source - Source buffer\n     * @param options - Options\n     * @param {number} options.width - Width of the texture\n     * @param {number} options.height - Height of the texture\n     * @param {1|2|4|8} [options.unpackAlignment=4] - The alignment of the pixel rows.\n     */\n    constructor(source: BufferType, options: IBufferResourceOptions)\n    {\n        const { width, height } = options || {};\n\n        if (!width || !height)\n        {\n            throw new Error('BufferResource width or height invalid');\n        }\n\n        super(width, height);\n\n        this.data = source;\n        this.unpackAlignment = options.unpackAlignment ?? 4;\n    }\n\n    /**\n     * Upload the texture to the GPU.\n     * @param renderer - Upload to the renderer\n     * @param baseTexture - Reference to parent texture\n     * @param glTexture - glTexture\n     * @returns - true is success\n     */\n    upload(renderer: Renderer, baseTexture: BaseTexture, glTexture: GLTexture): boolean\n    {\n        const gl = renderer.gl;\n\n        gl.pixelStorei(gl.UNPACK_ALIGNMENT, this.unpackAlignment);\n        gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, baseTexture.alphaMode === ALPHA_MODES.UNPACK);\n\n        const width = baseTexture.realWidth;\n        const height = baseTexture.realHeight;\n\n        if (glTexture.width === width && glTexture.height === height)\n        {\n            gl.texSubImage2D(\n                baseTexture.target,\n                0,\n                0,\n                0,\n                width,\n                height,\n                baseTexture.format,\n                glTexture.type,\n                this.data\n            );\n        }\n        else\n        {\n            glTexture.width = width;\n            glTexture.height = height;\n\n            gl.texImage2D(\n                baseTexture.target,\n                0,\n                glTexture.internalFormat,\n                width,\n                height,\n                0,\n                baseTexture.format,\n                glTexture.type,\n                this.data\n            );\n        }\n\n        return true;\n    }\n\n    /** Destroy and don't use after this. */\n    dispose(): void\n    {\n        this.data = null;\n    }\n\n    /**\n     * Used to auto-detect the type of resource.\n     * @param {*} source - The source object\n     * @returns {boolean} `true` if buffer source\n     */\n    static test(source: unknown): source is BufferType\n    {\n        return source === null\n            || source instanceof Int8Array\n            || source instanceof Uint8Array\n            || source instanceof Uint8ClampedArray\n            || source instanceof Int16Array\n            || source instanceof Uint16Array\n            || source instanceof Int32Array\n            || source instanceof Uint32Array\n            || source instanceof Float32Array;\n    }\n}\n"], "mappings": ";;AAwBO,MAAMA,cAAA,SAAuBC,QAAA,CACpC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAcIC,YAAYC,MAAA,EAAoBC,OAAA,EAChC;IACI,MAAM;MAAEC,KAAA;MAAOC;IAAA,IAAWF,OAAA,IAAW;IAEjC,KAACC,KAAA,IAAS,CAACC,MAAA,EAEL,UAAIC,KAAA,CAAM,wCAAwC;IAG5D,MAAMF,KAAA,EAAOC,MAAM,GAEnB,KAAKE,IAAA,GAAOL,MAAA,EACZ,KAAKM,eAAA,GAAkBL,OAAA,CAAQK,eAAA,IAAmB;EACtD;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAC,OAAOC,QAAA,EAAoBC,WAAA,EAA0BC,SAAA,EACrD;IACI,MAAMC,EAAA,GAAKH,QAAA,CAASG,EAAA;IAEpBA,EAAA,CAAGC,WAAA,CAAYD,EAAA,CAAGE,gBAAA,EAAkB,KAAKP,eAAe,GACxDK,EAAA,CAAGC,WAAA,CAAYD,EAAA,CAAGG,8BAAA,EAAgCL,WAAA,CAAYM,SAAA,KAAcC,WAAA,CAAYC,MAAM;IAE9F,MAAMf,KAAA,GAAQO,WAAA,CAAYS,SAAA;MACpBf,MAAA,GAASM,WAAA,CAAYU,UAAA;IAE3B,OAAIT,SAAA,CAAUR,KAAA,KAAUA,KAAA,IAASQ,SAAA,CAAUP,MAAA,KAAWA,MAAA,GAElDQ,EAAA,CAAGS,aAAA,CACCX,WAAA,CAAYY,MAAA,EACZ,GACA,GACA,GACAnB,KAAA,EACAC,MAAA,EACAM,WAAA,CAAYa,MAAA,EACZZ,SAAA,CAAUa,IAAA,EACV,KAAKlB,IAAA,KAKTK,SAAA,CAAUR,KAAA,GAAQA,KAAA,EAClBQ,SAAA,CAAUP,MAAA,GAASA,MAAA,EAEnBQ,EAAA,CAAGa,UAAA,CACCf,WAAA,CAAYY,MAAA,EACZ,GACAX,SAAA,CAAUe,cAAA,EACVvB,KAAA,EACAC,MAAA,EACA,GACAM,WAAA,CAAYa,MAAA,EACZZ,SAAA,CAAUa,IAAA,EACV,KAAKlB,IAAA,IAIN;EACX;EAAA;EAGAqB,QAAA,EACA;IACI,KAAKrB,IAAA,GAAO;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,OAAOsB,KAAK3B,MAAA,EACZ;IACI,OAAOA,MAAA,KAAW,QACXA,MAAA,YAAkB4B,SAAA,IAClB5B,MAAA,YAAkB6B,UAAA,IAClB7B,MAAA,YAAkB8B,iBAAA,IAClB9B,MAAA,YAAkB+B,UAAA,IAClB/B,MAAA,YAAkBgC,WAAA,IAClBhC,MAAA,YAAkBiC,UAAA,IAClBjC,MAAA,YAAkBkC,WAAA,IAClBlC,MAAA,YAAkBmC,YAAA;EAC7B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}