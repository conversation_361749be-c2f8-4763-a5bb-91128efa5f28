{"ast": null, "code": "import { SHAPES } from \"../const.mjs\";\nimport { Rectangle } from \"./Rectangle.mjs\";\nclass Ellipse {\n  /**\n   * @param x - The X coordinate of the center of this ellipse\n   * @param y - The Y coordinate of the center of this ellipse\n   * @param halfWidth - The half width of this ellipse\n   * @param halfHeight - The half height of this ellipse\n   */\n  constructor(x = 0, y = 0, halfWidth = 0, halfHeight = 0) {\n    this.x = x, this.y = y, this.width = halfWidth, this.height = halfHeight, this.type = SHAPES.ELIP;\n  }\n  /**\n   * Creates a clone of this Ellipse instance\n   * @returns {PIXI.Ellipse} A copy of the ellipse\n   */\n  clone() {\n    return new Ellipse(this.x, this.y, this.width, this.height);\n  }\n  /**\n   * Checks whether the x and y coordinates given are contained within this ellipse\n   * @param x - The X coordinate of the point to test\n   * @param y - The Y coordinate of the point to test\n   * @returns Whether the x/y coords are within this ellipse\n   */\n  contains(x, y) {\n    if (this.width <= 0 || this.height <= 0) return !1;\n    let normx = (x - this.x) / this.width,\n      normy = (y - this.y) / this.height;\n    return normx *= normx, normy *= normy, normx + normy <= 1;\n  }\n  /**\n   * Returns the framing rectangle of the ellipse as a Rectangle object\n   * @returns The framing rectangle\n   */\n  getBounds() {\n    return new Rectangle(this.x - this.width, this.y - this.height, this.width, this.height);\n  }\n}\nEllipse.prototype.toString = function () {\n  return `[@pixi/math:Ellipse x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`;\n};\nexport { Ellipse };", "map": {"version": 3, "names": ["Ellipse", "constructor", "x", "y", "halfWidth", "halfHeight", "width", "height", "type", "SHAPES", "ELIP", "clone", "contains", "normx", "normy", "getBounds", "Rectangle", "prototype", "toString"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\math\\src\\shapes\\Ellipse.ts"], "sourcesContent": ["import { SHAPES } from '../const';\nimport { Rectangle } from './Rectangle';\n\n/**\n * The Ellipse object is used to help draw graphics and can also be used to specify a hit area for displayObjects.\n * @memberof PIXI\n */\nexport class Ellipse\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n\n    /** @default 0 */\n    public width: number;\n\n    /** @default 0 */\n    public height: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.ELIP\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.ELIP;\n\n    /**\n     * @param x - The X coordinate of the center of this ellipse\n     * @param y - The Y coordinate of the center of this ellipse\n     * @param halfWidth - The half width of this ellipse\n     * @param halfHeight - The half height of this ellipse\n     */\n    constructor(x = 0, y = 0, halfWidth = 0, halfHeight = 0)\n    {\n        this.x = x;\n        this.y = y;\n        this.width = halfWidth;\n        this.height = halfHeight;\n\n        this.type = SHAPES.ELIP;\n    }\n\n    /**\n     * Creates a clone of this Ellipse instance\n     * @returns {PIXI.Ellipse} A copy of the ellipse\n     */\n    clone(): Ellipse\n    {\n        return new Ellipse(this.x, this.y, this.width, this.height);\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this ellipse\n     * @param x - The X coordinate of the point to test\n     * @param y - The Y coordinate of the point to test\n     * @returns Whether the x/y coords are within this ellipse\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.width <= 0 || this.height <= 0)\n        {\n            return false;\n        }\n\n        // normalize the coords to an ellipse with center 0,0\n        let normx = ((x - this.x) / this.width);\n        let normy = ((y - this.y) / this.height);\n\n        normx *= normx;\n        normy *= normy;\n\n        return (normx + normy <= 1);\n    }\n\n    /**\n     * Returns the framing rectangle of the ellipse as a Rectangle object\n     * @returns The framing rectangle\n     */\n    getBounds(): Rectangle\n    {\n        return new Rectangle(this.x - this.width, this.y - this.height, this.width, this.height);\n    }\n}\n\nif (process.env.DEBUG)\n{\n    Ellipse.prototype.toString = function toString(): string\n    {\n        return `[@pixi/math:Ellipse x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`;\n    };\n}\n"], "mappings": ";;AAOO,MAAMA,OAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EA0BIC,YAAYC,CAAA,GAAI,GAAGC,CAAA,GAAI,GAAGC,SAAA,GAAY,GAAGC,UAAA,GAAa,GACtD;IACI,KAAKH,CAAA,GAAIA,CAAA,EACT,KAAKC,CAAA,GAAIA,CAAA,EACT,KAAKG,KAAA,GAAQF,SAAA,EACb,KAAKG,MAAA,GAASF,UAAA,EAEd,KAAKG,IAAA,GAAOC,MAAA,CAAOC,IAAA;EACvB;EAAA;AAAA;AAAA;AAAA;EAMAC,MAAA,EACA;IACW,WAAIX,OAAA,CAAQ,KAAKE,CAAA,EAAG,KAAKC,CAAA,EAAG,KAAKG,KAAA,EAAO,KAAKC,MAAM;EAC9D;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAK,SAASV,CAAA,EAAWC,CAAA,EACpB;IACI,IAAI,KAAKG,KAAA,IAAS,KAAK,KAAKC,MAAA,IAAU,GAE3B;IAIP,IAAAM,KAAA,IAAUX,CAAA,GAAI,KAAKA,CAAA,IAAK,KAAKI,KAAA;MAC7BQ,KAAA,IAAUX,CAAA,GAAI,KAAKA,CAAA,IAAK,KAAKI,MAAA;IAEjC,OAAAM,KAAA,IAASA,KAAA,EACTC,KAAA,IAASA,KAAA,EAEDD,KAAA,GAAQC,KAAA,IAAS;EAC7B;EAAA;AAAA;AAAA;AAAA;EAMAC,UAAA,EACA;IACI,OAAO,IAAIC,SAAA,CAAU,KAAKd,CAAA,GAAI,KAAKI,KAAA,EAAO,KAAKH,CAAA,GAAI,KAAKI,MAAA,EAAQ,KAAKD,KAAA,EAAO,KAAKC,MAAM;EAC3F;AACJ;AAIIP,OAAA,CAAQiB,SAAA,CAAUC,QAAA,GAAW,YAC7B;EACW,gCAAyB,KAAKhB,CAAC,MAAM,KAAKC,CAAC,UAAU,KAAKG,KAAK,WAAW,KAAKC,MAAM;AAChG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}