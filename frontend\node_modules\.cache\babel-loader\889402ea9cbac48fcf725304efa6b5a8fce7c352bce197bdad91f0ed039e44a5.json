{"ast": null, "code": "import { ExtensionType, utils, settings, extensions } from \"@pixi/core\";\nconst knownFormats = [\"s3tc\", \"s3tc_sRGB\", \"etc\", \"etc1\", \"pvrtc\", \"atc\", \"astc\", \"bptc\"],\n  resolveCompressedTextureUrl = {\n    extension: ExtensionType.ResolveParser,\n    test: value => {\n      const extension = utils.path.extname(value).slice(1);\n      return [\"basis\", \"ktx\", \"dds\"].includes(extension);\n    },\n    parse: value => {\n      const parts = value.split(\".\"),\n        extension = parts.pop();\n      if ([\"ktx\", \"dds\"].includes(extension)) {\n        const textureFormat = parts.pop();\n        if (knownFormats.includes(textureFormat)) return {\n          resolution: parseFloat(settings.RETINA_PREFIX.exec(value)?.[1] ?? \"1\"),\n          format: textureFormat,\n          src: value\n        };\n      }\n      return {\n        resolution: parseFloat(settings.RETINA_PREFIX.exec(value)?.[1] ?? \"1\"),\n        format: extension,\n        src: value\n      };\n    }\n  };\nextensions.add(resolveCompressedTextureUrl);\nexport { resolveCompressedTextureUrl };", "map": {"version": 3, "names": ["knownFormats", "resolveCompressedTextureUrl", "extension", "ExtensionType", "Resolve<PERSON><PERSON>er", "test", "value", "utils", "path", "extname", "slice", "includes", "parse", "parts", "split", "pop", "textureFormat", "resolution", "parseFloat", "settings", "RETINA_PREFIX", "exec", "format", "src", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\compressed-textures\\src\\loaders\\resolveCompressedTextureUrl.ts"], "sourcesContent": ["import { extensions, ExtensionType, settings, utils } from '@pixi/core';\n\nimport type { ResolveURLParser, UnresolvedAsset } from '@pixi/assets';\n\nconst knownFormats = ['s3tc', 's3tc_sRGB', 'etc', 'etc1', 'pvrtc', 'atc', 'astc', 'bptc'];\n\nexport const resolveCompressedTextureUrl = {\n    extension: ExtensionType.ResolveParser,\n    test: (value: string) =>\n    {\n        const extension = utils.path.extname(value).slice(1);\n\n        return ['basis', 'ktx', 'dds'].includes(extension);\n    },\n    parse: (value: string): UnresolvedAsset =>\n    {\n        // value expected in format: {name}{resolution}.{format}.{extension} - <EMAIL>\n        const parts = value.split('.');\n        const extension = parts.pop();\n\n        if (['ktx', 'dds'].includes(extension))\n        {\n            const textureFormat = parts.pop();\n\n            if (knownFormats.includes(textureFormat))\n            {\n                return {\n                    resolution: parseFloat(settings.RETINA_PREFIX.exec(value)?.[1] ?? '1'),\n                    format: textureFormat,\n                    src: value,\n                };\n            }\n        }\n\n        return {\n            resolution: parseFloat(settings.RETINA_PREFIX.exec(value)?.[1] ?? '1'),\n            format: extension,\n            src: value,\n        };\n    },\n} as ResolveURLParser;\n\nextensions.add(resolveCompressedTextureUrl);\n"], "mappings": ";AAIA,MAAMA,YAAA,GAAe,CAAC,QAAQ,aAAa,OAAO,QAAQ,SAAS,OAAO,QAAQ,MAAM;EAE3EC,2BAAA,GAA8B;IACvCC,SAAA,EAAWC,aAAA,CAAcC,aAAA;IACzBC,IAAA,EAAOC,KAAA,IACP;MACI,MAAMJ,SAAA,GAAYK,KAAA,CAAMC,IAAA,CAAKC,OAAA,CAAQH,KAAK,EAAEI,KAAA,CAAM,CAAC;MAEnD,OAAO,CAAC,SAAS,OAAO,KAAK,EAAEC,QAAA,CAAST,SAAS;IACrD;IACAU,KAAA,EAAQN,KAAA,IACR;MAEI,MAAMO,KAAA,GAAQP,KAAA,CAAMQ,KAAA,CAAM,GAAG;QACvBZ,SAAA,GAAYW,KAAA,CAAME,GAAA;MAExB,IAAI,CAAC,OAAO,KAAK,EAAEJ,QAAA,CAAST,SAAS,GACrC;QACU,MAAAc,aAAA,GAAgBH,KAAA,CAAME,GAAA;QAExB,IAAAf,YAAA,CAAaW,QAAA,CAASK,aAAa,GAE5B;UACHC,UAAA,EAAYC,UAAA,CAAWC,QAAA,CAASC,aAAA,CAAcC,IAAA,CAAKf,KAAK,IAAI,CAAC,KAAK,GAAG;UACrEgB,MAAA,EAAQN,aAAA;UACRO,GAAA,EAAKjB;QAAA;MAGjB;MAEO;QACHW,UAAA,EAAYC,UAAA,CAAWC,QAAA,CAASC,aAAA,CAAcC,IAAA,CAAKf,KAAK,IAAI,CAAC,KAAK,GAAG;QACrEgB,MAAA,EAAQpB,SAAA;QACRqB,GAAA,EAAKjB;MAAA;IAEb;EACJ;AAEAkB,UAAA,CAAWC,GAAA,CAAIxB,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}