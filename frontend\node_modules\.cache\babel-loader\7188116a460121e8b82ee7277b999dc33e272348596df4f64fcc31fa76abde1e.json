{"ast": null, "code": "class CountLimiter {\n  /**\n   * @param maxItemsPerFrame - The maximum number of items that can be prepared each frame.\n   */\n  constructor(maxItemsPerFrame) {\n    this.maxItemsPerFrame = maxItemsPerFrame, this.itemsLeft = 0;\n  }\n  /** Resets any counting properties to start fresh on a new frame. */\n  beginFrame() {\n    this.itemsLeft = this.maxItemsPerFrame;\n  }\n  /**\n   * Checks to see if another item can be uploaded. This should only be called once per item.\n   * @returns If the item is allowed to be uploaded.\n   */\n  allowedToUpload() {\n    return this.itemsLeft-- > 0;\n  }\n}\nexport { CountLimiter };", "map": {"version": 3, "names": ["CountLimiter", "constructor", "maxItemsPerFrame", "itemsLeft", "beginFrame", "allowedToUpload"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\prepare\\src\\CountLimiter.ts"], "sourcesContent": ["/**\n * CountLimiter limits the number of items handled by a {@link PIXI.BasePrepare} to a specified\n * number of items per frame.\n * @memberof PIXI\n */\nexport class CountLimiter\n{\n    /** The maximum number of items that can be prepared each frame. */\n    public maxItemsPerFrame: number;\n\n    /** The number of items that can be prepared in the current frame. */\n    public itemsLeft: number;\n\n    /**\n     * @param maxItemsPerFrame - The maximum number of items that can be prepared each frame.\n     */\n    constructor(maxItemsPerFrame: number)\n    {\n        this.maxItemsPerFrame = maxItemsPerFrame;\n        this.itemsLeft = 0;\n    }\n\n    /** Resets any counting properties to start fresh on a new frame. */\n    beginFrame(): void\n    {\n        this.itemsLeft = this.maxItemsPerFrame;\n    }\n\n    /**\n     * Checks to see if another item can be uploaded. This should only be called once per item.\n     * @returns If the item is allowed to be uploaded.\n     */\n    allowedToUpload(): boolean\n    {\n        return this.itemsLeft-- > 0;\n    }\n}\n"], "mappings": "AAKO,MAAMA,YAAA,CACb;EAAA;AAAA;AAAA;EAUIC,YAAYC,gBAAA,EACZ;IACS,KAAAA,gBAAA,GAAmBA,gBAAA,EACxB,KAAKC,SAAA,GAAY;EACrB;EAAA;EAGAC,WAAA,EACA;IACI,KAAKD,SAAA,GAAY,KAAKD,gBAAA;EAC1B;EAAA;AAAA;AAAA;AAAA;EAMAG,gBAAA,EACA;IACI,OAAO,KAAKF,SAAA,KAAc;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}