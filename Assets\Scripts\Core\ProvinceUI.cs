using UnityEngine;
using TMPro;

public class ProvinceUI : MonoBehaviour
{
    [Header("UI References")]
    public TextMeshProUGUI nameText;
    public TextMeshProUGUI ownerText;
    public TextMeshProUGUI populationText;
    public TextMeshProUGUI resourcesText;

    public void SetProvince(Province province)
    {
        if (province == null)
        {
            Debug.LogWarning("Province is null in ProvinceUI.SetProvince");
            return;
        }

        if (nameText != null) nameText.text = province.Name ?? "Unknown";
        if (ownerText != null) ownerText.text = "Owner: " + (province.Owner ?? "None");
        if (populationText != null) populationText.text = "Pop: " + province.Population.ToString("N0");
        if (resourcesText != null) resourcesText.text = "Res: " + province.Resources.ToString();
    }

    public void ClearUI()
    {
        if (nameText != null) nameText.text = "";
        if (ownerText != null) ownerText.text = "";
        if (populationText != null) populationText.text = "";
        if (resourcesText != null) resourcesText.text = "";
    }
}
