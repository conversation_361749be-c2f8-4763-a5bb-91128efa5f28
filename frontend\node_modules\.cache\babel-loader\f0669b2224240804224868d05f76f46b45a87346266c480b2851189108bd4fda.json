{"ast": null, "code": "import \"./settings.mjs\";\nexport * from \"@pixi/color\";\nexport * from \"@pixi/constants\";\nexport * from \"@pixi/extensions\";\nexport * from \"@pixi/math\";\nexport * from \"@pixi/runner\";\nexport * from \"@pixi/settings\";\nexport * from \"@pixi/ticker\";\nimport * as utils$1 from \"@pixi/utils\";\nimport { autoDetectRenderer } from \"./autoDetectRenderer.mjs\";\nimport { BackgroundSystem } from \"./background/BackgroundSystem.mjs\";\nimport { BatchDrawCall } from \"./batch/BatchDrawCall.mjs\";\nimport { BatchGeometry } from \"./batch/BatchGeometry.mjs\";\nimport { BatchRenderer } from \"./batch/BatchRenderer.mjs\";\nimport { BatchShaderGenerator } from \"./batch/BatchShaderGenerator.mjs\";\nimport { BatchSystem } from \"./batch/BatchSystem.mjs\";\nimport { BatchTextureArray } from \"./batch/BatchTextureArray.mjs\";\nimport { ObjectRenderer } from \"./batch/ObjectRenderer.mjs\";\nimport { ContextSystem } from \"./context/ContextSystem.mjs\";\nimport { Filter } from \"./filters/Filter.mjs\";\nimport { FilterState } from \"./filters/FilterState.mjs\";\nimport { FilterSystem } from \"./filters/FilterSystem.mjs\";\nimport \"./filters/IFilterTarget.mjs\";\nimport { SpriteMaskFilter } from \"./filters/spriteMask/SpriteMaskFilter.mjs\";\nimport { defaultFilterVertex, defaultVertex } from \"./fragments/index.mjs\";\nimport { Framebuffer } from \"./framebuffer/Framebuffer.mjs\";\nimport { FramebufferSystem } from \"./framebuffer/FramebufferSystem.mjs\";\nimport { GLFramebuffer } from \"./framebuffer/GLFramebuffer.mjs\";\nimport { MultisampleSystem } from \"./framebuffer/MultisampleSystem.mjs\";\nimport { Attribute } from \"./geometry/Attribute.mjs\";\nimport { Buffer } from \"./geometry/Buffer.mjs\";\nimport { BufferSystem } from \"./geometry/BufferSystem.mjs\";\nimport { Geometry } from \"./geometry/Geometry.mjs\";\nimport { GeometrySystem } from \"./geometry/GeometrySystem.mjs\";\nimport { ViewableBuffer } from \"./geometry/ViewableBuffer.mjs\";\nimport \"./IRenderer.mjs\";\nimport { MaskData } from \"./mask/MaskData.mjs\";\nimport { MaskSystem } from \"./mask/MaskSystem.mjs\";\nimport { ScissorSystem } from \"./mask/ScissorSystem.mjs\";\nimport { StencilSystem } from \"./mask/StencilSystem.mjs\";\nimport { PluginSystem } from \"./plugin/PluginSystem.mjs\";\nimport { ProjectionSystem } from \"./projection/ProjectionSystem.mjs\";\nimport { ObjectRendererSystem } from \"./render/ObjectRendererSystem.mjs\";\nimport { Renderer } from \"./Renderer.mjs\";\nimport { BaseRenderTexture } from \"./renderTexture/BaseRenderTexture.mjs\";\nimport { GenerateTextureSystem } from \"./renderTexture/GenerateTextureSystem.mjs\";\nimport { RenderTexture } from \"./renderTexture/RenderTexture.mjs\";\nimport { RenderTexturePool } from \"./renderTexture/RenderTexturePool.mjs\";\nimport { RenderTextureSystem } from \"./renderTexture/RenderTextureSystem.mjs\";\nimport { GLProgram, IGLUniformData } from \"./shader/GLProgram.mjs\";\nimport { Program } from \"./shader/Program.mjs\";\nimport { Shader } from \"./shader/Shader.mjs\";\nimport { ShaderSystem } from \"./shader/ShaderSystem.mjs\";\nimport { UniformGroup } from \"./shader/UniformGroup.mjs\";\nimport { checkMaxIfStatementsInShader } from \"./shader/utils/checkMaxIfStatementsInShader.mjs\";\nimport { generateProgram } from \"./shader/utils/generateProgram.mjs\";\nimport { createUBOElements, generateUniformBufferSync, getUBOData } from \"./shader/utils/generateUniformBufferSync.mjs\";\nimport { getTestContext } from \"./shader/utils/getTestContext.mjs\";\nimport { uniformParsers } from \"./shader/utils/uniformParsers.mjs\";\nimport { unsafeEvalSupported } from \"./shader/utils/unsafeEvalSupported.mjs\";\nimport { StartupSystem } from \"./startup/StartupSystem.mjs\";\nimport { State } from \"./state/State.mjs\";\nimport { StateSystem } from \"./state/StateSystem.mjs\";\nimport \"./system/ISystem.mjs\";\nimport \"./systems.mjs\";\nimport { BaseTexture } from \"./textures/BaseTexture.mjs\";\nimport { GLTexture } from \"./textures/GLTexture.mjs\";\nimport \"./textures/resources/index.mjs\";\nimport { Texture } from \"./textures/Texture.mjs\";\nimport { TextureGCSystem } from \"./textures/TextureGCSystem.mjs\";\nimport { TextureMatrix } from \"./textures/TextureMatrix.mjs\";\nimport { TextureSystem } from \"./textures/TextureSystem.mjs\";\nimport { TextureUvs } from \"./textures/TextureUvs.mjs\";\nimport { TransformFeedback } from \"./transformFeedback/TransformFeedback.mjs\";\nimport { TransformFeedbackSystem } from \"./transformFeedback/TransformFeedbackSystem.mjs\";\nimport { Quad } from \"./utils/Quad.mjs\";\nimport { QuadUv } from \"./utils/QuadUv.mjs\";\nimport { ViewSystem } from \"./view/ViewSystem.mjs\";\nimport { SystemManager } from \"./system/SystemManager.mjs\";\nimport { BaseImageResource } from \"./textures/resources/BaseImageResource.mjs\";\nimport { Resource } from \"./textures/resources/Resource.mjs\";\nimport { AbstractMultiResource } from \"./textures/resources/AbstractMultiResource.mjs\";\nimport { ArrayResource } from \"./textures/resources/ArrayResource.mjs\";\nimport { INSTALLED, autoDetectResource } from \"./textures/resources/autoDetectResource.mjs\";\nimport { BufferResource } from \"./textures/resources/BufferResource.mjs\";\nimport { CanvasResource } from \"./textures/resources/CanvasResource.mjs\";\nimport { CubeResource } from \"./textures/resources/CubeResource.mjs\";\nimport { ImageBitmapResource } from \"./textures/resources/ImageBitmapResource.mjs\";\nimport { ImageResource } from \"./textures/resources/ImageResource.mjs\";\nimport { SVGResource } from \"./textures/resources/SVGResource.mjs\";\nimport { VideoResource } from \"./textures/resources/VideoResource.mjs\";\nconst VERSION = \"7.4.3\";\nexport { AbstractMultiResource, ArrayResource, Attribute, BackgroundSystem, BaseImageResource, BaseRenderTexture, BaseTexture, BatchDrawCall, BatchGeometry, BatchRenderer, BatchShaderGenerator, BatchSystem, BatchTextureArray, Buffer, BufferResource, BufferSystem, CanvasResource, ContextSystem, CubeResource, Filter, FilterState, FilterSystem, Framebuffer, FramebufferSystem, GLFramebuffer, GLProgram, GLTexture, GenerateTextureSystem, Geometry, GeometrySystem, IGLUniformData, INSTALLED, ImageBitmapResource, ImageResource, MaskData, MaskSystem, MultisampleSystem, ObjectRenderer, ObjectRendererSystem, PluginSystem, Program, ProjectionSystem, Quad, QuadUv, RenderTexture, RenderTexturePool, RenderTextureSystem, Renderer, Resource, SVGResource, ScissorSystem, Shader, ShaderSystem, SpriteMaskFilter, StartupSystem, State, StateSystem, StencilSystem, SystemManager, Texture, TextureGCSystem, TextureMatrix, TextureSystem, TextureUvs, TransformFeedback, TransformFeedbackSystem, UniformGroup, VERSION, VideoResource, ViewSystem, ViewableBuffer, autoDetectRenderer, autoDetectResource, checkMaxIfStatementsInShader, createUBOElements, defaultFilterVertex, defaultVertex, generateProgram, generateUniformBufferSync, getTestContext, getUBOData, uniformParsers, unsafeEvalSupported, utils$1 as utils };", "map": {"version": 3, "names": ["VERSION"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\index.ts"], "sourcesContent": ["/// <reference path=\"../global.d.ts\" />\nimport './settings';\n\n/**\n * @namespace PIXI\n */\n\n/**\n * String of the current PIXI version.\n * @memberof PIXI\n */\nexport const VERSION = process.env.VERSION;\n\n// Export dependencies\nexport * from '@pixi/color';\nexport * from '@pixi/constants';\nexport * from '@pixi/extensions';\nexport * from '@pixi/math';\nexport * from '@pixi/runner';\nexport * from '@pixi/settings';\nexport * from '@pixi/ticker';\nexport * as utils from '@pixi/utils';\n\n// Export core\nexport * from './autoDetectRenderer';\nexport * from './background/BackgroundSystem';\nexport * from './batch/BatchDrawCall';\nexport * from './batch/BatchGeometry';\nexport * from './batch/BatchRenderer';\nexport * from './batch/BatchShaderGenerator';\nexport * from './batch/BatchSystem';\nexport * from './batch/BatchTextureArray';\nexport * from './batch/ObjectRenderer';\nexport * from './context/ContextSystem';\nexport * from './filters/Filter';\nexport * from './filters/FilterState';\nexport * from './filters/FilterSystem';\nexport * from './filters/IFilterTarget';\nexport * from './filters/spriteMask/SpriteMaskFilter';\nexport * from './fragments';\nexport * from './framebuffer/Framebuffer';\nexport * from './framebuffer/FramebufferSystem';\nexport * from './framebuffer/GLFramebuffer';\nexport * from './framebuffer/MultisampleSystem';\nexport * from './geometry/Attribute';\nexport * from './geometry/Buffer';\nexport * from './geometry/BufferSystem';\nexport * from './geometry/Geometry';\nexport * from './geometry/GeometrySystem';\nexport * from './geometry/ViewableBuffer';\nexport * from './IRenderer';\nexport * from './IRenderer';\nexport * from './mask/MaskData';\nexport * from './mask/MaskSystem';\nexport * from './mask/ScissorSystem';\nexport * from './mask/StencilSystem';\nexport * from './plugin/PluginSystem';\nexport * from './plugin/PluginSystem';\nexport * from './projection/ProjectionSystem';\nexport * from './render/ObjectRendererSystem';\nexport * from './Renderer';\nexport * from './renderTexture/BaseRenderTexture';\nexport * from './renderTexture/GenerateTextureSystem';\nexport * from './renderTexture/GenerateTextureSystem';\nexport * from './renderTexture/RenderTexture';\nexport * from './renderTexture/RenderTexturePool';\nexport * from './renderTexture/RenderTextureSystem';\nexport * from './shader/GLProgram';\nexport * from './shader/Program';\nexport * from './shader/Shader';\nexport * from './shader/ShaderSystem';\nexport * from './shader/UniformGroup';\nexport * from './shader/utils/checkMaxIfStatementsInShader';\nexport * from './shader/utils/generateProgram';\nexport * from './shader/utils/generateUniformBufferSync';\nexport * from './shader/utils/getTestContext';\nexport * from './shader/utils/uniformParsers';\nexport * from './shader/utils/unsafeEvalSupported';\nexport * from './startup/StartupSystem';\nexport * from './state/State';\nexport * from './state/StateSystem';\nexport * from './system/ISystem';\nexport * from './systems';\nexport * from './textures/BaseTexture';\nexport * from './textures/GLTexture';\nexport * from './textures/resources';\nexport * from './textures/Texture';\nexport * from './textures/TextureGCSystem';\nexport * from './textures/TextureMatrix';\nexport * from './textures/TextureSystem';\nexport * from './textures/TextureUvs';\nexport * from './transformFeedback/TransformFeedback';\nexport * from './transformFeedback/TransformFeedbackSystem';\nexport * from './utils/Quad';\nexport * from './utils/QuadUv';\nexport * from './view/ViewSystem';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWO,MAAMA,OAAA,GAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}