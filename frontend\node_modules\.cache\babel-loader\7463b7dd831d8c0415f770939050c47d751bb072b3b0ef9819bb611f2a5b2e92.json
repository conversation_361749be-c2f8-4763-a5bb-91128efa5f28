{"ast": null, "code": "import { BUFFER_TYPE } from \"@pixi/constants\";\nimport { <PERSON> } from \"@pixi/runner\";\nlet UID = 0;\nclass Buffer {\n  /**\n   * @param {PIXI.IArrayBuffer} data - the data to store in the buffer.\n   * @param _static - `true` for static buffer\n   * @param index - `true` for index buffer\n   */\n  constructor(data, _static = !0, index = !1) {\n    this.data = data || new Float32Array(1), this._glBuffers = {}, this._updateID = 0, this.index = index, this.static = _static, this.id = UID++, this.disposeRunner = new Runner(\"disposeBuffer\");\n  }\n  // TODO could explore flagging only a partial upload?\n  /**\n   * Flags this buffer as requiring an upload to the GPU.\n   * @param {PIXI.IArrayBuffer|number[]} [data] - the data to update in the buffer.\n   */\n  update(data) {\n    data instanceof Array && (data = new Float32Array(data)), this.data = data || this.data, this._updateID++;\n  }\n  /** Disposes WebGL resources that are connected to this geometry. */\n  dispose() {\n    this.disposeRunner.emit(this, !1);\n  }\n  /** Destroys the buffer. */\n  destroy() {\n    this.dispose(), this.data = null;\n  }\n  /**\n   * Flags whether this is an index buffer.\n   *\n   * Index buffers are of type `ELEMENT_ARRAY_BUFFER`. Note that setting this property to false will make\n   * the buffer of type `ARRAY_BUFFER`.\n   *\n   * For backwards compatibility.\n   */\n  set index(value) {\n    this.type = value ? BUFFER_TYPE.ELEMENT_ARRAY_BUFFER : BUFFER_TYPE.ARRAY_BUFFER;\n  }\n  get index() {\n    return this.type === BUFFER_TYPE.ELEMENT_ARRAY_BUFFER;\n  }\n  /**\n   * Helper function that creates a buffer based on an array or TypedArray\n   * @param {ArrayBufferView | number[]} data - the TypedArray that the buffer will store. If this is a regular Array it will be converted to a Float32Array.\n   * @returns - A new Buffer based on the data provided.\n   */\n  static from(data) {\n    return data instanceof Array && (data = new Float32Array(data)), new Buffer(data);\n  }\n}\nexport { Buffer };", "map": {"version": 3, "names": ["UID", "<PERSON><PERSON><PERSON>", "constructor", "data", "_static", "index", "Float32Array", "_glBuffers", "_updateID", "static", "id", "dispose<PERSON><PERSON><PERSON>", "Runner", "update", "Array", "dispose", "emit", "destroy", "value", "type", "BUFFER_TYPE", "ELEMENT_ARRAY_BUFFER", "ARRAY_BUFFER", "from"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\geometry\\Buffer.ts"], "sourcesContent": ["import { BUFFER_TYPE } from '@pixi/constants';\nimport { Runner } from '@pixi/runner';\n\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './G<PERSON><PERSON>uffer';\n\nlet UID = 0;\n/* eslint-disable max-len */\n\n/**\n * Marks places in PixiJS where you can pass Float32Array, UInt32Array, any typed arrays, and ArrayBuffer.\n *\n * Same as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in typescript lib, defined here just for documentation.\n * @memberof PIXI\n */\nexport interface IArrayBuffer extends ArrayBuffer // eslint-disable-line @typescript-eslint/no-empty-interface\n{\n}\n\n/**\n * PixiJS classes use this type instead of <PERSON><PERSON>yBuffer and typed arrays\n * to support expressions like `geometry.buffers[0].data[0] = position.x`.\n *\n * Gives access to indexing and `length` field.\n * - @popelyshev: If data is actually <PERSON><PERSON><PERSON><PERSON>uffer and throws Exception on indexing - its user problem :)\n * @memberof PIXI\n */\nexport interface ITypedArray extends IArrayBuffer\n{\n    readonly length: number;\n    [index: number]: number;\n    readonly BYTES_PER_ELEMENT: number;\n}\n\n/**\n * A wrapper for data so that it can be used and uploaded by WebGL\n * @memberof PIXI\n */\nexport class Buffer\n{\n    /**\n     * The data in the buffer, as a typed array\n     * @type {PIXI.IArrayBuffer}\n     */\n    public data: ITypedArray;\n\n    /**\n     * The type of buffer this is, one of:\n     * + ELEMENT_ARRAY_BUFFER - used as an index buffer\n     * + ARRAY_BUFFER - used as an attribute buffer\n     * + UNIFORM_BUFFER - used as a uniform buffer (if available)\n     */\n    public type: BUFFER_TYPE;\n\n    public static: boolean;\n    public id: number;\n    disposeRunner: Runner;\n\n    /**\n     * A map of renderer IDs to webgl buffer\n     * @private\n     * @type {Record<number, GLBuffer>}\n     */\n    _glBuffers: {[key: number]: GLBuffer};\n    _updateID: number;\n\n    /**\n     * @param {PIXI.IArrayBuffer} data - the data to store in the buffer.\n     * @param _static - `true` for static buffer\n     * @param index - `true` for index buffer\n     */\n    constructor(data?: IArrayBuffer, _static = true, index = false)\n    {\n        this.data = (data || new Float32Array(1)) as ITypedArray;\n\n        this._glBuffers = {};\n        this._updateID = 0;\n\n        this.index = index;\n        this.static = _static;\n        this.id = UID++;\n\n        this.disposeRunner = new Runner('disposeBuffer');\n    }\n\n    // TODO could explore flagging only a partial upload?\n    /**\n     * Flags this buffer as requiring an upload to the GPU.\n     * @param {PIXI.IArrayBuffer|number[]} [data] - the data to update in the buffer.\n     */\n    update(data?: IArrayBuffer | Array<number>): void\n    {\n        if (data instanceof Array)\n        {\n            data = new Float32Array(data);\n        }\n        this.data = (data as ITypedArray) || this.data;\n        this._updateID++;\n    }\n\n    /** Disposes WebGL resources that are connected to this geometry. */\n    dispose(): void\n    {\n        this.disposeRunner.emit(this, false);\n    }\n\n    /** Destroys the buffer. */\n    destroy(): void\n    {\n        this.dispose();\n\n        this.data = null;\n    }\n\n    /**\n     * Flags whether this is an index buffer.\n     *\n     * Index buffers are of type `ELEMENT_ARRAY_BUFFER`. Note that setting this property to false will make\n     * the buffer of type `ARRAY_BUFFER`.\n     *\n     * For backwards compatibility.\n     */\n    set index(value: boolean)\n    {\n        this.type = value ? BUFFER_TYPE.ELEMENT_ARRAY_BUFFER : BUFFER_TYPE.ARRAY_BUFFER;\n    }\n\n    get index(): boolean\n    {\n        return this.type === BUFFER_TYPE.ELEMENT_ARRAY_BUFFER;\n    }\n\n    /**\n     * Helper function that creates a buffer based on an array or TypedArray\n     * @param {ArrayBufferView | number[]} data - the TypedArray that the buffer will store. If this is a regular Array it will be converted to a Float32Array.\n     * @returns - A new Buffer based on the data provided.\n     */\n    static from(data: IArrayBuffer | number[]): Buffer\n    {\n        if (data instanceof Array)\n        {\n            data = new Float32Array(data);\n        }\n\n        return new Buffer(data);\n    }\n}\n"], "mappings": ";;AAKA,IAAIA,GAAA,GAAM;AAgCH,MAAMC,MAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;EAgCIC,YAAYC,IAAA,EAAqBC,OAAA,GAAU,IAAMC,KAAA,GAAQ,IACzD;IACS,KAAAF,IAAA,GAAQA,IAAA,IAAQ,IAAIG,YAAA,CAAa,CAAC,GAEvC,KAAKC,UAAA,GAAa,IAClB,KAAKC,SAAA,GAAY,GAEjB,KAAKH,KAAA,GAAQA,KAAA,EACb,KAAKI,MAAA,GAASL,OAAA,EACd,KAAKM,EAAA,GAAKV,GAAA,IAEV,KAAKW,aAAA,GAAgB,IAAIC,MAAA,CAAO,eAAe;EACnD;EAAA;EAAA;AAAA;AAAA;AAAA;EAOAC,OAAOV,IAAA,EACP;IACQA,IAAA,YAAgBW,KAAA,KAEhBX,IAAA,GAAO,IAAIG,YAAA,CAAaH,IAAI,IAEhC,KAAKA,IAAA,GAAQA,IAAA,IAAwB,KAAKA,IAAA,EAC1C,KAAKK,SAAA;EACT;EAAA;EAGAO,QAAA,EACA;IACS,KAAAJ,aAAA,CAAcK,IAAA,CAAK,MAAM,EAAK;EACvC;EAAA;EAGAC,QAAA,EACA;IACS,KAAAF,OAAA,CAEL,QAAKZ,IAAA,GAAO;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUA,IAAIE,MAAMa,KAAA,EACV;IACI,KAAKC,IAAA,GAAOD,KAAA,GAAQE,WAAA,CAAYC,oBAAA,GAAuBD,WAAA,CAAYE,YAAA;EACvE;EAEA,IAAIjB,MAAA,EACJ;IACW,YAAKc,IAAA,KAASC,WAAA,CAAYC,oBAAA;EACrC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,OAAOE,KAAKpB,IAAA,EACZ;IACQ,OAAAA,IAAA,YAAgBW,KAAA,KAEhBX,IAAA,GAAO,IAAIG,YAAA,CAAaH,IAAI,IAGzB,IAAIF,MAAA,CAAOE,IAAI;EAC1B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}