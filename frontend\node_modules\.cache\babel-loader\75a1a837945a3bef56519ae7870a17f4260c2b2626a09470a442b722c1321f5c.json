{"ast": null, "code": "import { Filter, CLEAR_MODES } from \"@pixi/core\";\nimport { generateBlurFragSource } from \"./generateBlurFragSource.mjs\";\nimport { generateBlurVertSource } from \"./generateBlurVertSource.mjs\";\nclass BlurFilterPass extends Filter {\n  /**\n   * @param horizontal - Do pass along the x-axis (`true`) or y-axis (`false`).\n   * @param strength - The strength of the blur filter.\n   * @param quality - The quality of the blur filter.\n   * @param {number|null} [resolution=PIXI.Filter.defaultResolution] - The resolution of the blur filter.\n   * @param kernelSize - The kernelSize of the blur filter.Options: 5, 7, 9, 11, 13, 15.\n   */\n  constructor(horizontal, strength = 8, quality = 4, resolution = Filter.defaultResolution, kernelSize = 5) {\n    const vertSrc = generateBlurVertSource(kernelSize, horizontal),\n      fragSrc = generateBlurFragSource(kernelSize);\n    super(\n    // vertex shader\n    vertSrc,\n    // fragment shader\n    fragSrc), this.horizontal = horizontal, this.resolution = resolution, this._quality = 0, this.quality = quality, this.blur = strength;\n  }\n  /**\n   * Applies the filter.\n   * @param filterManager - The manager.\n   * @param input - The input target.\n   * @param output - The output target.\n   * @param clearMode - How to clear\n   */\n  apply(filterManager, input, output, clearMode) {\n    if (output ? this.horizontal ? this.uniforms.strength = 1 / output.width * (output.width / input.width) : this.uniforms.strength = 1 / output.height * (output.height / input.height) : this.horizontal ? this.uniforms.strength = 1 / filterManager.renderer.width * (filterManager.renderer.width / input.width) : this.uniforms.strength = 1 / filterManager.renderer.height * (filterManager.renderer.height / input.height), this.uniforms.strength *= this.strength, this.uniforms.strength /= this.passes, this.passes === 1) filterManager.applyFilter(this, input, output, clearMode);else {\n      const renderTarget = filterManager.getFilterTexture(),\n        renderer = filterManager.renderer;\n      let flip = input,\n        flop = renderTarget;\n      this.state.blend = !1, filterManager.applyFilter(this, flip, flop, CLEAR_MODES.CLEAR);\n      for (let i = 1; i < this.passes - 1; i++) {\n        filterManager.bindAndClear(flip, CLEAR_MODES.BLIT), this.uniforms.uSampler = flop;\n        const temp = flop;\n        flop = flip, flip = temp, renderer.shader.bind(this), renderer.geometry.draw(5);\n      }\n      this.state.blend = !0, filterManager.applyFilter(this, flop, output, clearMode), filterManager.returnFilterTexture(renderTarget);\n    }\n  }\n  /**\n   * Sets the strength of both the blur.\n   * @default 16\n   */\n  get blur() {\n    return this.strength;\n  }\n  set blur(value) {\n    this.padding = 1 + Math.abs(value) * 2, this.strength = value;\n  }\n  /**\n   * Sets the quality of the blur by modifying the number of passes. More passes means higher\n   * quality bluring but the lower the performance.\n   * @default 4\n   */\n  get quality() {\n    return this._quality;\n  }\n  set quality(value) {\n    this._quality = value, this.passes = value;\n  }\n}\nexport { BlurFilterPass };", "map": {"version": 3, "names": ["Blur<PERSON>ilterPass", "Filter", "constructor", "horizontal", "strength", "quality", "resolution", "defaultResolution", "kernelSize", "vertSrc", "generateBlurVertSource", "fragSrc", "generateBlurFragSource", "_quality", "blur", "apply", "filterManager", "input", "output", "clearMode", "uniforms", "width", "height", "renderer", "passes", "applyFilter", "renderTarget", "getFilterTexture", "flip", "flop", "state", "blend", "CLEAR_MODES", "CLEAR", "i", "bindAndClear", "BLIT", "uSampler", "temp", "shader", "bind", "geometry", "draw", "returnFilterTexture", "value", "padding", "Math", "abs"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\filter-blur\\src\\BlurFilterPass.ts"], "sourcesContent": ["import { CLEAR_MODES, Filter } from '@pixi/core';\nimport { generateBlurFragSource } from './generateBlurFragSource';\nimport { generateBlurVertSource } from './generateBlurVertSource';\n\nimport type { FilterSystem, RenderTexture } from '@pixi/core';\n\n/**\n * The BlurFilterPass applies a horizontal or vertical Gaussian blur to an object.\n * @memberof PIXI\n */\nexport class BlurFilterPass extends Filter\n{\n    public horizontal: boolean;\n    public strength!: number;\n    public passes!: number;\n\n    private _quality: number;\n\n    /**\n     * @param horizontal - Do pass along the x-axis (`true`) or y-axis (`false`).\n     * @param strength - The strength of the blur filter.\n     * @param quality - The quality of the blur filter.\n     * @param {number|null} [resolution=PIXI.Filter.defaultResolution] - The resolution of the blur filter.\n     * @param kernelSize - The kernelSize of the blur filter.Options: 5, 7, 9, 11, 13, 15.\n     */\n    constructor(horizontal: boolean, strength = 8, quality = 4, resolution = Filter.defaultResolution, kernelSize = 5)\n    {\n        const vertSrc = generateBlurVertSource(kernelSize, horizontal);\n        const fragSrc = generateBlurFragSource(kernelSize);\n\n        super(\n            // vertex shader\n            vertSrc,\n            // fragment shader\n            fragSrc\n        );\n\n        this.horizontal = horizontal;\n\n        this.resolution = resolution;\n\n        this._quality = 0;\n\n        this.quality = quality;\n\n        this.blur = strength;\n    }\n\n    /**\n     * Applies the filter.\n     * @param filterManager - The manager.\n     * @param input - The input target.\n     * @param output - The output target.\n     * @param clearMode - How to clear\n     */\n    public apply(\n        filterManager: FilterSystem, input: RenderTexture, output: RenderTexture, clearMode: CLEAR_MODES\n    ): void\n    {\n        if (output)\n        {\n            if (this.horizontal)\n            {\n                this.uniforms.strength = (1 / output.width) * (output.width / input.width);\n            }\n            else\n            {\n                this.uniforms.strength = (1 / output.height) * (output.height / input.height);\n            }\n        }\n        else\n        {\n            if (this.horizontal) // eslint-disable-line\n            {\n                this.uniforms.strength = (1 / filterManager.renderer.width) * (filterManager.renderer.width / input.width);\n            }\n            else\n            {\n                this.uniforms.strength = (1 / filterManager.renderer.height) * (filterManager.renderer.height / input.height); // eslint-disable-line\n            }\n        }\n\n        // screen space!\n        this.uniforms.strength *= this.strength;\n        this.uniforms.strength /= this.passes;\n\n        if (this.passes === 1)\n        {\n            filterManager.applyFilter(this, input, output, clearMode);\n        }\n        else\n        {\n            const renderTarget = filterManager.getFilterTexture();\n            const renderer = filterManager.renderer;\n\n            let flip = input;\n            let flop = renderTarget;\n\n            this.state.blend = false;\n            filterManager.applyFilter(this, flip, flop, CLEAR_MODES.CLEAR);\n\n            for (let i = 1; i < this.passes - 1; i++)\n            {\n                filterManager.bindAndClear(flip, CLEAR_MODES.BLIT);\n\n                this.uniforms.uSampler = flop;\n\n                const temp = flop;\n\n                flop = flip;\n                flip = temp;\n\n                renderer.shader.bind(this);\n                renderer.geometry.draw(5);\n            }\n\n            this.state.blend = true;\n            filterManager.applyFilter(this, flop, output, clearMode);\n            filterManager.returnFilterTexture(renderTarget);\n        }\n    }\n    /**\n     * Sets the strength of both the blur.\n     * @default 16\n     */\n    get blur(): number\n    {\n        return this.strength;\n    }\n\n    set blur(value: number)\n    {\n        this.padding = 1 + (Math.abs(value) * 2);\n        this.strength = value;\n    }\n\n    /**\n     * Sets the quality of the blur by modifying the number of passes. More passes means higher\n     * quality bluring but the lower the performance.\n     * @default 4\n     */\n    get quality(): number\n    {\n        return this._quality;\n    }\n\n    set quality(value: number)\n    {\n        this._quality = value;\n        this.passes = value;\n    }\n}\n"], "mappings": ";;;AAUO,MAAMA,cAAA,SAAuBC,MAAA,CACpC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAcIC,YAAYC,UAAA,EAAqBC,QAAA,GAAW,GAAGC,OAAA,GAAU,GAAGC,UAAA,GAAaL,MAAA,CAAOM,iBAAA,EAAmBC,UAAA,GAAa,GAChH;IACI,MAAMC,OAAA,GAAUC,sBAAA,CAAuBF,UAAA,EAAYL,UAAU;MACvDQ,OAAA,GAAUC,sBAAA,CAAuBJ,UAAU;IAEjD;IAAA;IAEIC,OAAA;IAAA;IAEAE,OAAA,GAGJ,KAAKR,UAAA,GAAaA,UAAA,EAElB,KAAKG,UAAA,GAAaA,UAAA,EAElB,KAAKO,QAAA,GAAW,GAEhB,KAAKR,OAAA,GAAUA,OAAA,EAEf,KAAKS,IAAA,GAAOV,QAAA;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASOW,MACHC,aAAA,EAA6BC,KAAA,EAAsBC,MAAA,EAAuBC,SAAA,EAE9E;IA4BI,IA3BID,MAAA,GAEI,KAAKf,UAAA,GAEL,KAAKiB,QAAA,CAAShB,QAAA,GAAY,IAAIc,MAAA,CAAOG,KAAA,IAAUH,MAAA,CAAOG,KAAA,GAAQJ,KAAA,CAAMI,KAAA,IAIpE,KAAKD,QAAA,CAAShB,QAAA,GAAY,IAAIc,MAAA,CAAOI,MAAA,IAAWJ,MAAA,CAAOI,MAAA,GAASL,KAAA,CAAMK,MAAA,IAKtE,KAAKnB,UAAA,GAEL,KAAKiB,QAAA,CAAShB,QAAA,GAAY,IAAIY,aAAA,CAAcO,QAAA,CAASF,KAAA,IAAUL,aAAA,CAAcO,QAAA,CAASF,KAAA,GAAQJ,KAAA,CAAMI,KAAA,IAIpG,KAAKD,QAAA,CAAShB,QAAA,GAAY,IAAIY,aAAA,CAAcO,QAAA,CAASD,MAAA,IAAWN,aAAA,CAAcO,QAAA,CAASD,MAAA,GAASL,KAAA,CAAMK,MAAA,GAK9G,KAAKF,QAAA,CAAShB,QAAA,IAAY,KAAKA,QAAA,EAC/B,KAAKgB,QAAA,CAAShB,QAAA,IAAY,KAAKoB,MAAA,EAE3B,KAAKA,MAAA,KAAW,GAEhBR,aAAA,CAAcS,WAAA,CAAY,MAAMR,KAAA,EAAOC,MAAA,EAAQC,SAAS,OAG5D;MACI,MAAMO,YAAA,GAAeV,aAAA,CAAcW,gBAAA,CAAiB;QAC9CJ,QAAA,GAAWP,aAAA,CAAcO,QAAA;MAE3B,IAAAK,IAAA,GAAOX,KAAA;QACPY,IAAA,GAAOH,YAAA;MAEN,KAAAI,KAAA,CAAMC,KAAA,GAAQ,IACnBf,aAAA,CAAcS,WAAA,CAAY,MAAMG,IAAA,EAAMC,IAAA,EAAMG,WAAA,CAAYC,KAAK;MAE7D,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKV,MAAA,GAAS,GAAGU,CAAA,IACrC;QACIlB,aAAA,CAAcmB,YAAA,CAAaP,IAAA,EAAMI,WAAA,CAAYI,IAAI,GAEjD,KAAKhB,QAAA,CAASiB,QAAA,GAAWR,IAAA;QAEzB,MAAMS,IAAA,GAAOT,IAAA;QAENA,IAAA,GAAAD,IAAA,EACPA,IAAA,GAAOU,IAAA,EAEPf,QAAA,CAASgB,MAAA,CAAOC,IAAA,CAAK,IAAI,GACzBjB,QAAA,CAASkB,QAAA,CAASC,IAAA,CAAK,CAAC;MAC5B;MAEA,KAAKZ,KAAA,CAAMC,KAAA,GAAQ,IACnBf,aAAA,CAAcS,WAAA,CAAY,MAAMI,IAAA,EAAMX,MAAA,EAAQC,SAAS,GACvDH,aAAA,CAAc2B,mBAAA,CAAoBjB,YAAY;IAClD;EACJ;EAAA;AAAA;AAAA;AAAA;EAKA,IAAIZ,KAAA,EACJ;IACI,OAAO,KAAKV,QAAA;EAChB;EAEA,IAAIU,KAAK8B,KAAA,EACT;IACS,KAAAC,OAAA,GAAU,IAAKC,IAAA,CAAKC,GAAA,CAAIH,KAAK,IAAI,GACtC,KAAKxC,QAAA,GAAWwC,KAAA;EACpB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,IAAIvC,QAAA,EACJ;IACI,OAAO,KAAKQ,QAAA;EAChB;EAEA,IAAIR,QAAQuC,KAAA,EACZ;IACS,KAAA/B,QAAA,GAAW+B,KAAA,EAChB,KAAKpB,MAAA,GAASoB,KAAA;EAClB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}