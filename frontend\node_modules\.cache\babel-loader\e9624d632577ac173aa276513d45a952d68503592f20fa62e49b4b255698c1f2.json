{"ast": null, "code": "import { ExtensionType, extensions } from \"@pixi/extensions\";\nimport { UPDATE_PRIORITY } from \"./const.mjs\";\nimport { Ticker } from \"./Ticker.mjs\";\nclass TickerPlugin {\n  /**\n   * Initialize the plugin with scope of application instance\n   * @static\n   * @private\n   * @param {object} [options] - See application options\n   */\n  static init(options) {\n    options = Object.assign({\n      autoStart: !0,\n      sharedTicker: !1\n    }, options), Object.defineProperty(this, \"ticker\", {\n      set(ticker) {\n        this._ticker && this._ticker.remove(this.render, this), this._ticker = ticker, ticker && ticker.add(this.render, this, UPDATE_PRIORITY.LOW);\n      },\n      get() {\n        return this._ticker;\n      }\n    }), this.stop = () => {\n      this._ticker.stop();\n    }, this.start = () => {\n      this._ticker.start();\n    }, this._ticker = null, this.ticker = options.sharedTicker ? Ticker.shared : new Ticker(), options.autoStart && this.start();\n  }\n  /**\n   * Clean up the ticker, scoped to application.\n   * @static\n   * @private\n   */\n  static destroy() {\n    if (this._ticker) {\n      const oldTicker = this._ticker;\n      this.ticker = null, oldTicker.destroy();\n    }\n  }\n}\nTickerPlugin.extension = ExtensionType.Application;\nextensions.add(TickerPlugin);\nexport { TickerPlugin };", "map": {"version": 3, "names": ["TickerPlugin", "init", "options", "Object", "assign", "autoStart", "sharedTicker", "defineProperty", "set", "ticker", "_ticker", "remove", "render", "add", "UPDATE_PRIORITY", "LOW", "get", "stop", "start", "Ticker", "shared", "destroy", "oldTicker", "extension", "ExtensionType", "Application", "extensions"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\ticker\\src\\TickerPlugin.ts"], "sourcesContent": ["import { extensions, ExtensionType } from '@pixi/extensions';\nimport { UPDATE_PRIORITY } from './const';\nimport { Ticker } from './Ticker';\n\nimport type { ExtensionMetadata } from '@pixi/extensions';\n\nexport interface TickerPluginOptions\n{\n    /**\n     * Automatically starts the rendering after the construction.\n     *  **Note**: Setting this parameter to `false` does NOT stop the shared ticker even if you set\n     *  `options.sharedTicker` to `true` in case that it is already started. Stop it by your own.\n     * @memberof PIXI.IApplicationOptions\n     * @default true\n     */\n    autoStart?: boolean;\n    /**\n     * Set`true` to use `Ticker.shared`, `false` to create new ticker.\n     *  If set to `false`, you cannot register a handler to occur before anything that runs on the shared ticker.\n     *  The system ticker will always run before both the shared ticker and the app ticker.\n     * @memberof PIXI.IApplicationOptions\n     * @default false\n     */\n    sharedTicker?: boolean;\n}\n\n/**\n * Middleware for for Application Ticker.\n * @class\n * @memberof PIXI\n */\nexport class TickerPlugin\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Application;\n\n    static start: () => void;\n    static stop: () => void;\n    static _ticker: Ticker;\n    static ticker: Ticker;\n\n    /**\n     * Initialize the plugin with scope of application instance\n     * @static\n     * @private\n     * @param {object} [options] - See application options\n     */\n    static init(options?: GlobalMixins.IApplicationOptions): void\n    {\n        // Set default\n        options = Object.assign({\n            autoStart: true,\n            sharedTicker: false,\n        }, options);\n\n        // Create ticker setter\n        Object.defineProperty(this, 'ticker',\n            {\n                set(ticker)\n                {\n                    if (this._ticker)\n                    {\n                        this._ticker.remove(this.render, this);\n                    }\n                    this._ticker = ticker;\n                    if (ticker)\n                    {\n                        ticker.add(this.render, this, UPDATE_PRIORITY.LOW);\n                    }\n                },\n                get()\n                {\n                    return this._ticker;\n                },\n            });\n\n        /**\n         * Convenience method for stopping the render.\n         * @method\n         * @memberof PIXI.Application\n         * @instance\n         */\n        this.stop = (): void =>\n        {\n            this._ticker.stop();\n        };\n\n        /**\n         * Convenience method for starting the render.\n         * @method\n         * @memberof PIXI.Application\n         * @instance\n         */\n        this.start = (): void =>\n        {\n            this._ticker.start();\n        };\n\n        /**\n         * Internal reference to the ticker.\n         * @type {PIXI.Ticker}\n         * @name _ticker\n         * @memberof PIXI.Application#\n         * @private\n         */\n        this._ticker = null;\n\n        /**\n         * Ticker for doing render updates.\n         * @type {PIXI.Ticker}\n         * @name ticker\n         * @memberof PIXI.Application#\n         * @default PIXI.Ticker.shared\n         */\n        this.ticker = options.sharedTicker ? Ticker.shared : new Ticker();\n\n        // Start the rendering\n        if (options.autoStart)\n        {\n            this.start();\n        }\n    }\n\n    /**\n     * Clean up the ticker, scoped to application.\n     * @static\n     * @private\n     */\n    static destroy(): void\n    {\n        if (this._ticker)\n        {\n            const oldTicker = this._ticker;\n\n            this.ticker = null;\n            oldTicker.destroy();\n        }\n    }\n}\n\nextensions.add(TickerPlugin);\n"], "mappings": ";;;AA+BO,MAAMA,YAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAeI,OAAOC,KAAKC,OAAA,EACZ;IAEIA,OAAA,GAAUC,MAAA,CAAOC,MAAA,CAAO;MACpBC,SAAA,EAAW;MACXC,YAAA,EAAc;IAAA,GACfJ,OAAO,GAGVC,MAAA,CAAOI,cAAA,CAAe,MAAM,UACxB;MACIC,IAAIC,MAAA,EACJ;QACQ,KAAKC,OAAA,IAEL,KAAKA,OAAA,CAAQC,MAAA,CAAO,KAAKC,MAAA,EAAQ,IAAI,GAEzC,KAAKF,OAAA,GAAUD,MAAA,EACXA,MAAA,IAEAA,MAAA,CAAOI,GAAA,CAAI,KAAKD,MAAA,EAAQ,MAAME,eAAA,CAAgBC,GAAG;MAEzD;MACAC,IAAA,EACA;QACI,OAAO,KAAKN,OAAA;MAChB;IACJ,IAQJ,KAAKO,IAAA,GAAO,MACZ;MACI,KAAKP,OAAA,CAAQO,IAAA;IAAK,GAStB,KAAKC,KAAA,GAAQ,MACb;MACI,KAAKR,OAAA,CAAQQ,KAAA;IAAM,GAUvB,KAAKR,OAAA,GAAU,MASf,KAAKD,MAAA,GAASP,OAAA,CAAQI,YAAA,GAAea,MAAA,CAAOC,MAAA,GAAS,IAAID,MAAA,IAGrDjB,OAAA,CAAQG,SAAA,IAER,KAAKa,KAAA;EAEb;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,OAAOG,QAAA,EACP;IACI,IAAI,KAAKX,OAAA,EACT;MACI,MAAMY,SAAA,GAAY,KAAKZ,OAAA;MAElB,KAAAD,MAAA,GAAS,MACda,SAAA,CAAUD,OAAA,CAAQ;IACtB;EACJ;AACJ;AA3GarB,YAAA,CAGFuB,SAAA,GAA+BC,aAAA,CAAcC,WAAA;AA0GxDC,UAAA,CAAWb,GAAA,CAAIb,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}