{"ast": null, "code": "import { ExtensionType, extensions } from \"@pixi/extensions\";\nimport { <PERSON><PERSON><PERSON>uffer } from \"./GLBuffer.mjs\";\nclass BufferSystem {\n  /**\n   * @param {PIXI.Renderer} renderer - The renderer this System works for.\n   */\n  constructor(renderer) {\n    this.renderer = renderer, this.managedBuffers = {}, this.boundBufferBases = {};\n  }\n  /**\n   * @ignore\n   */\n  destroy() {\n    this.renderer = null;\n  }\n  /** Sets up the renderer context and necessary buffers. */\n  contextChange() {\n    this.disposeAll(!0), this.gl = this.renderer.gl, this.CONTEXT_UID = this.renderer.CONTEXT_UID;\n  }\n  /**\n   * This binds specified buffer. On first run, it will create the webGL buffers for the context too\n   * @param buffer - the buffer to bind to the renderer\n   */\n  bind(buffer) {\n    const {\n        gl,\n        CONTEXT_UID\n      } = this,\n      glBuffer = buffer._glBuffers[CONTEXT_UID] || this.createGLBuffer(buffer);\n    gl.bindBuffer(buffer.type, glBuffer.buffer);\n  }\n  unbind(type) {\n    const {\n      gl\n    } = this;\n    gl.bindBuffer(type, null);\n  }\n  /**\n   * Binds an uniform buffer to at the given index.\n   *\n   * A cache is used so a buffer will not be bound again if already bound.\n   * @param buffer - the buffer to bind\n   * @param index - the base index to bind it to.\n   */\n  bindBufferBase(buffer, index) {\n    const {\n      gl,\n      CONTEXT_UID\n    } = this;\n    if (this.boundBufferBases[index] !== buffer) {\n      const glBuffer = buffer._glBuffers[CONTEXT_UID] || this.createGLBuffer(buffer);\n      this.boundBufferBases[index] = buffer, gl.bindBufferBase(gl.UNIFORM_BUFFER, index, glBuffer.buffer);\n    }\n  }\n  /**\n   * Binds a buffer whilst also binding its range.\n   * This will make the buffer start from the offset supplied rather than 0 when it is read.\n   * @param buffer - the buffer to bind\n   * @param index - the base index to bind at, defaults to 0\n   * @param offset - the offset to bind at (this is blocks of 256). 0 = 0, 1 = 256, 2 = 512 etc\n   */\n  bindBufferRange(buffer, index, offset) {\n    const {\n      gl,\n      CONTEXT_UID\n    } = this;\n    offset = offset || 0;\n    const glBuffer = buffer._glBuffers[CONTEXT_UID] || this.createGLBuffer(buffer);\n    gl.bindBufferRange(gl.UNIFORM_BUFFER, index || 0, glBuffer.buffer, offset * 256, 256);\n  }\n  /**\n   * Will ensure the data in the buffer is uploaded to the GPU.\n   * @param {PIXI.Buffer} buffer - the buffer to update\n   */\n  update(buffer) {\n    const {\n        gl,\n        CONTEXT_UID\n      } = this,\n      glBuffer = buffer._glBuffers[CONTEXT_UID] || this.createGLBuffer(buffer);\n    if (buffer._updateID !== glBuffer.updateID) if (glBuffer.updateID = buffer._updateID, gl.bindBuffer(buffer.type, glBuffer.buffer), glBuffer.byteLength >= buffer.data.byteLength) gl.bufferSubData(buffer.type, 0, buffer.data);else {\n      const drawType = buffer.static ? gl.STATIC_DRAW : gl.DYNAMIC_DRAW;\n      glBuffer.byteLength = buffer.data.byteLength, gl.bufferData(buffer.type, buffer.data, drawType);\n    }\n  }\n  /**\n   * Disposes buffer\n   * @param {PIXI.Buffer} buffer - buffer with data\n   * @param {boolean} [contextLost=false] - If context was lost, we suppress deleteVertexArray\n   */\n  dispose(buffer, contextLost) {\n    if (!this.managedBuffers[buffer.id]) return;\n    delete this.managedBuffers[buffer.id];\n    const glBuffer = buffer._glBuffers[this.CONTEXT_UID],\n      gl = this.gl;\n    buffer.disposeRunner.remove(this), glBuffer && (contextLost || gl.deleteBuffer(glBuffer.buffer), delete buffer._glBuffers[this.CONTEXT_UID]);\n  }\n  /**\n   * dispose all WebGL resources of all managed buffers\n   * @param {boolean} [contextLost=false] - If context was lost, we suppress `gl.delete` calls\n   */\n  disposeAll(contextLost) {\n    const all = Object.keys(this.managedBuffers);\n    for (let i = 0; i < all.length; i++) this.dispose(this.managedBuffers[all[i]], contextLost);\n  }\n  /**\n   * creates and attaches a GLBuffer object tied to the current context.\n   * @param buffer\n   * @protected\n   */\n  createGLBuffer(buffer) {\n    const {\n      CONTEXT_UID,\n      gl\n    } = this;\n    return buffer._glBuffers[CONTEXT_UID] = new GLBuffer(gl.createBuffer()), this.managedBuffers[buffer.id] = buffer, buffer.disposeRunner.add(this), buffer._glBuffers[CONTEXT_UID];\n  }\n}\nBufferSystem.extension = {\n  type: ExtensionType.RendererSystem,\n  name: \"buffer\"\n};\nextensions.add(BufferSystem);\nexport { BufferSystem };", "map": {"version": 3, "names": ["BufferSystem", "constructor", "renderer", "managedBuffers", "boundBufferBases", "destroy", "contextChange", "disposeAll", "gl", "CONTEXT_UID", "bind", "buffer", "gl<PERSON>uffer", "_glBuffers", "createGLBuffer", "<PERSON><PERSON><PERSON><PERSON>", "type", "unbind", "bindBufferBase", "index", "UNIFORM_BUFFER", "bindBufferRange", "offset", "update", "_updateID", "updateID", "byteLength", "data", "bufferSubData", "drawType", "static", "STATIC_DRAW", "DYNAMIC_DRAW", "bufferData", "dispose", "contextLost", "id", "dispose<PERSON><PERSON><PERSON>", "remove", "deleteBuffer", "all", "Object", "keys", "i", "length", "GL<PERSON>uffer", "createBuffer", "add", "extension", "ExtensionType", "RendererSystem", "name", "extensions"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\geometry\\BufferSystem.ts"], "sourcesContent": ["import { extensions, ExtensionType } from '@pixi/extensions';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './G<PERSON><PERSON>uffer';\n\nimport type { BUFFER_TYPE } from '@pixi/constants';\nimport type { ExtensionMetadata } from '@pixi/extensions';\nimport type { IRenderingContext } from '../IRenderer';\nimport type { <PERSON>derer } from '../Renderer';\nimport type { ISystem } from '../system/ISystem';\nimport type { <PERSON>uffer } from './Buffer';\n\n/**\n * System plugin to the renderer to manage buffers.\n *\n * WebGL uses Buffers as a way to store objects to the GPU.\n * This system makes working with them a lot easier.\n *\n * Buffers are used in three main places in WebGL\n * - geometry information\n * - Uniform information (via uniform buffer objects - a WebGL 2 only feature)\n * - Transform feedback information. (WebGL 2 only feature)\n *\n * This system will handle the binding of buffers to the GPU as well as uploading\n * them. With this system, you never need to work directly with GPU buffers, but instead work with\n * the PIXI.Buffer class.\n * @class\n * @memberof PIXI\n */\nexport class BufferSystem implements ISystem\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        type: ExtensionType.RendererSystem,\n        name: 'buffer',\n    };\n\n    CONTEXT_UID: number;\n    gl: IRenderingContext;\n\n    /** Cache for all buffers by id, used in case renderer gets destroyed or for profiling */\n    readonly managedBuffers: {[key: number]: Buffer};\n\n    /** Cache keeping track of the base bound buffer bases */\n    readonly boundBufferBases: {[key: number]: Buffer};\n\n    private renderer: Renderer;\n\n    /**\n     * @param {PIXI.Renderer} renderer - The renderer this System works for.\n     */\n    constructor(renderer: Renderer)\n    {\n        this.renderer = renderer;\n        this.managedBuffers = {};\n        this.boundBufferBases = {};\n    }\n\n    /**\n     * @ignore\n     */\n    destroy(): void\n    {\n        this.renderer = null;\n    }\n\n    /** Sets up the renderer context and necessary buffers. */\n    protected contextChange(): void\n    {\n        this.disposeAll(true);\n\n        this.gl = this.renderer.gl;\n\n        // TODO fill out...\n        this.CONTEXT_UID = this.renderer.CONTEXT_UID;\n    }\n\n    /**\n     * This binds specified buffer. On first run, it will create the webGL buffers for the context too\n     * @param buffer - the buffer to bind to the renderer\n     */\n    bind(buffer: Buffer): void\n    {\n        const { gl, CONTEXT_UID } = this;\n\n        const glBuffer = buffer._glBuffers[CONTEXT_UID] || this.createGLBuffer(buffer);\n\n        gl.bindBuffer(buffer.type, glBuffer.buffer);\n    }\n\n    unbind(type: BUFFER_TYPE): void\n    {\n        const { gl } = this;\n\n        gl.bindBuffer(type, null);\n    }\n\n    /**\n     * Binds an uniform buffer to at the given index.\n     *\n     * A cache is used so a buffer will not be bound again if already bound.\n     * @param buffer - the buffer to bind\n     * @param index - the base index to bind it to.\n     */\n    bindBufferBase(buffer: Buffer, index: number): void\n    {\n        const { gl, CONTEXT_UID } = this;\n\n        if (this.boundBufferBases[index] !== buffer)\n        {\n            const glBuffer = buffer._glBuffers[CONTEXT_UID] || this.createGLBuffer(buffer);\n\n            this.boundBufferBases[index] = buffer;\n\n            gl.bindBufferBase(gl.UNIFORM_BUFFER, index, glBuffer.buffer);\n        }\n    }\n\n    /**\n     * Binds a buffer whilst also binding its range.\n     * This will make the buffer start from the offset supplied rather than 0 when it is read.\n     * @param buffer - the buffer to bind\n     * @param index - the base index to bind at, defaults to 0\n     * @param offset - the offset to bind at (this is blocks of 256). 0 = 0, 1 = 256, 2 = 512 etc\n     */\n    bindBufferRange(buffer: Buffer, index?: number, offset?: number): void\n    {\n        const { gl, CONTEXT_UID } = this;\n\n        offset = offset || 0;\n\n        const glBuffer = buffer._glBuffers[CONTEXT_UID] || this.createGLBuffer(buffer);\n\n        gl.bindBufferRange(gl.UNIFORM_BUFFER, index || 0, glBuffer.buffer, offset * 256, 256);\n    }\n\n    /**\n     * Will ensure the data in the buffer is uploaded to the GPU.\n     * @param {PIXI.Buffer} buffer - the buffer to update\n     */\n    update(buffer: Buffer): void\n    {\n        const { gl, CONTEXT_UID } = this;\n\n        const glBuffer = buffer._glBuffers[CONTEXT_UID] || this.createGLBuffer(buffer);\n\n        if (buffer._updateID === glBuffer.updateID)\n        {\n            return;\n        }\n\n        glBuffer.updateID = buffer._updateID;\n\n        gl.bindBuffer(buffer.type, glBuffer.buffer);\n\n        if (glBuffer.byteLength >= buffer.data.byteLength)\n        {\n            // offset is always zero for now!\n            gl.bufferSubData(buffer.type, 0, buffer.data);\n        }\n        else\n        {\n            const drawType = buffer.static ? gl.STATIC_DRAW : gl.DYNAMIC_DRAW;\n\n            glBuffer.byteLength = buffer.data.byteLength;\n            gl.bufferData(buffer.type, buffer.data, drawType);\n        }\n    }\n\n    /**\n     * Disposes buffer\n     * @param {PIXI.Buffer} buffer - buffer with data\n     * @param {boolean} [contextLost=false] - If context was lost, we suppress deleteVertexArray\n     */\n    dispose(buffer: Buffer, contextLost?: boolean): void\n    {\n        if (!this.managedBuffers[buffer.id])\n        {\n            return;\n        }\n\n        delete this.managedBuffers[buffer.id];\n\n        const glBuffer = buffer._glBuffers[this.CONTEXT_UID];\n        const gl = this.gl;\n\n        buffer.disposeRunner.remove(this);\n\n        if (!glBuffer)\n        {\n            return;\n        }\n\n        if (!contextLost)\n        {\n            gl.deleteBuffer(glBuffer.buffer);\n        }\n\n        delete buffer._glBuffers[this.CONTEXT_UID];\n    }\n\n    /**\n     * dispose all WebGL resources of all managed buffers\n     * @param {boolean} [contextLost=false] - If context was lost, we suppress `gl.delete` calls\n     */\n    disposeAll(contextLost?: boolean): void\n    {\n        const all: Array<any> = Object.keys(this.managedBuffers);\n\n        for (let i = 0; i < all.length; i++)\n        {\n            this.dispose(this.managedBuffers[all[i]], contextLost);\n        }\n    }\n\n    /**\n     * creates and attaches a GLBuffer object tied to the current context.\n     * @param buffer\n     * @protected\n     */\n    protected createGLBuffer(buffer: Buffer): GLBuffer\n    {\n        const { CONTEXT_UID, gl } = this;\n\n        buffer._glBuffers[CONTEXT_UID] = new GLBuffer(gl.createBuffer());\n\n        this.managedBuffers[buffer.id] = buffer;\n\n        buffer.disposeRunner.add(this);\n\n        return buffer._glBuffers[CONTEXT_UID];\n    }\n}\n\nextensions.add(BufferSystem);\n"], "mappings": ";;AA2BO,MAAMA,YAAA,CACb;EAAA;AAAA;AAAA;EAqBIC,YAAYC,QAAA,EACZ;IACS,KAAAA,QAAA,GAAWA,QAAA,EAChB,KAAKC,cAAA,GAAiB,IACtB,KAAKC,gBAAA,GAAmB;EAC5B;EAAA;AAAA;AAAA;EAKAC,QAAA,EACA;IACI,KAAKH,QAAA,GAAW;EACpB;EAAA;EAGUI,cAAA,EACV;IACS,KAAAC,UAAA,CAAW,EAAI,GAEpB,KAAKC,EAAA,GAAK,KAAKN,QAAA,CAASM,EAAA,EAGxB,KAAKC,WAAA,GAAc,KAAKP,QAAA,CAASO,WAAA;EACrC;EAAA;AAAA;AAAA;AAAA;EAMAC,KAAKC,MAAA,EACL;IACI,MAAM;QAAEH,EAAA;QAAIC;MAAA,IAAgB;MAEtBG,QAAA,GAAWD,MAAA,CAAOE,UAAA,CAAWJ,WAAW,KAAK,KAAKK,cAAA,CAAeH,MAAM;IAE7EH,EAAA,CAAGO,UAAA,CAAWJ,MAAA,CAAOK,IAAA,EAAMJ,QAAA,CAASD,MAAM;EAC9C;EAEAM,OAAOD,IAAA,EACP;IACU;MAAER;IAAO;IAEZA,EAAA,CAAAO,UAAA,CAAWC,IAAA,EAAM,IAAI;EAC5B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAE,eAAeP,MAAA,EAAgBQ,KAAA,EAC/B;IACU;MAAEX,EAAA;MAAIC;IAAgB;IAE5B,IAAI,KAAKL,gBAAA,CAAiBe,KAAK,MAAMR,MAAA,EACrC;MACI,MAAMC,QAAA,GAAWD,MAAA,CAAOE,UAAA,CAAWJ,WAAW,KAAK,KAAKK,cAAA,CAAeH,MAAM;MAExE,KAAAP,gBAAA,CAAiBe,KAAK,IAAIR,MAAA,EAE/BH,EAAA,CAAGU,cAAA,CAAeV,EAAA,CAAGY,cAAA,EAAgBD,KAAA,EAAOP,QAAA,CAASD,MAAM;IAC/D;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAU,gBAAgBV,MAAA,EAAgBQ,KAAA,EAAgBG,MAAA,EAChD;IACU;MAAEd,EAAA;MAAIC;IAAgB;IAE5Ba,MAAA,GAASA,MAAA,IAAU;IAEnB,MAAMV,QAAA,GAAWD,MAAA,CAAOE,UAAA,CAAWJ,WAAW,KAAK,KAAKK,cAAA,CAAeH,MAAM;IAE1EH,EAAA,CAAAa,eAAA,CAAgBb,EAAA,CAAGY,cAAA,EAAgBD,KAAA,IAAS,GAAGP,QAAA,CAASD,MAAA,EAAQW,MAAA,GAAS,KAAK,GAAG;EACxF;EAAA;AAAA;AAAA;AAAA;EAMAC,OAAOZ,MAAA,EACP;IACI,MAAM;QAAEH,EAAA;QAAIC;MAAA,IAAgB;MAEtBG,QAAA,GAAWD,MAAA,CAAOE,UAAA,CAAWJ,WAAW,KAAK,KAAKK,cAAA,CAAeH,MAAM;IAEzE,IAAAA,MAAA,CAAOa,SAAA,KAAcZ,QAAA,CAASa,QAAA,EASlC,IAJAb,QAAA,CAASa,QAAA,GAAWd,MAAA,CAAOa,SAAA,EAE3BhB,EAAA,CAAGO,UAAA,CAAWJ,MAAA,CAAOK,IAAA,EAAMJ,QAAA,CAASD,MAAM,GAEtCC,QAAA,CAASc,UAAA,IAAcf,MAAA,CAAOgB,IAAA,CAAKD,UAAA,EAGnClB,EAAA,CAAGoB,aAAA,CAAcjB,MAAA,CAAOK,IAAA,EAAM,GAAGL,MAAA,CAAOgB,IAAI,OAGhD;MACI,MAAME,QAAA,GAAWlB,MAAA,CAAOmB,MAAA,GAAStB,EAAA,CAAGuB,WAAA,GAAcvB,EAAA,CAAGwB,YAAA;MAE5CpB,QAAA,CAAAc,UAAA,GAAaf,MAAA,CAAOgB,IAAA,CAAKD,UAAA,EAClClB,EAAA,CAAGyB,UAAA,CAAWtB,MAAA,CAAOK,IAAA,EAAML,MAAA,CAAOgB,IAAA,EAAME,QAAQ;IACpD;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAK,QAAQvB,MAAA,EAAgBwB,WAAA,EACxB;IACI,IAAI,CAAC,KAAKhC,cAAA,CAAeQ,MAAA,CAAOyB,EAAE,GAE9B;IAGG,YAAKjC,cAAA,CAAeQ,MAAA,CAAOyB,EAAE;IAEpC,MAAMxB,QAAA,GAAWD,MAAA,CAAOE,UAAA,CAAW,KAAKJ,WAAW;MAC7CD,EAAA,GAAK,KAAKA,EAAA;IAEhBG,MAAA,CAAO0B,aAAA,CAAcC,MAAA,CAAO,IAAI,GAE3B1B,QAAA,KAKAuB,WAAA,IAED3B,EAAA,CAAG+B,YAAA,CAAa3B,QAAA,CAASD,MAAM,GAGnC,OAAOA,MAAA,CAAOE,UAAA,CAAW,KAAKJ,WAAW;EAC7C;EAAA;AAAA;AAAA;AAAA;EAMAF,WAAW4B,WAAA,EACX;IACI,MAAMK,GAAA,GAAkBC,MAAA,CAAOC,IAAA,CAAK,KAAKvC,cAAc;IAEvD,SAASwC,CAAA,GAAI,GAAGA,CAAA,GAAIH,GAAA,CAAII,MAAA,EAAQD,CAAA,IAE5B,KAAKT,OAAA,CAAQ,KAAK/B,cAAA,CAAeqC,GAAA,CAAIG,CAAC,CAAC,GAAGR,WAAW;EAE7D;EAAA;AAAA;AAAA;AAAA;AAAA;EAOUrB,eAAeH,MAAA,EACzB;IACU;MAAEF,WAAA;MAAaD;IAAO;IAErB,OAAAG,MAAA,CAAAE,UAAA,CAAWJ,WAAW,IAAI,IAAIoC,QAAA,CAASrC,EAAA,CAAGsC,YAAA,EAAc,GAE/D,KAAK3C,cAAA,CAAeQ,MAAA,CAAOyB,EAAE,IAAIzB,MAAA,EAEjCA,MAAA,CAAO0B,aAAA,CAAcU,GAAA,CAAI,IAAI,GAEtBpC,MAAA,CAAOE,UAAA,CAAWJ,WAAW;EACxC;AACJ;AA3MaT,YAAA,CAGFgD,SAAA,GAA+B;EAClChC,IAAA,EAAMiC,aAAA,CAAcC,cAAA;EACpBC,IAAA,EAAM;AACV;AAuMJC,UAAA,CAAWL,GAAA,CAAI/C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}