{"ast": null, "code": "import { SAMPLER_TYPES } from \"@pixi/constants\";\nfunction mapInternalFormatToSamplerType(gl) {\n  let table;\n  return \"WebGL2RenderingContext\" in globalThis && gl instanceof globalThis.WebGL2RenderingContext ? table = {\n    [gl.RGB]: SAMPLER_TYPES.FLOAT,\n    [gl.RGBA]: SAMPLER_TYPES.FLOAT,\n    [gl.ALPHA]: SAMPLER_TYPES.FLOAT,\n    [gl.LUMINANCE]: SAMPLER_TYPES.FLOAT,\n    [gl.LUMINANCE_ALPHA]: SAMPLER_TYPES.FLOAT,\n    [gl.R8]: SAMPLER_TYPES.FLOAT,\n    [gl.R8_SNORM]: SAMPLER_TYPES.FLOAT,\n    [gl.RG8]: SAMPLER_TYPES.FLOAT,\n    [gl.RG8_SNORM]: SAMPLER_TYPES.FLOAT,\n    [gl.RGB8]: SAMPLER_TYPES.FLOAT,\n    [gl.RGB8_SNORM]: SAMPLER_TYPES.FLOAT,\n    [gl.RGB565]: SAMPLER_TYPES.FLOAT,\n    [gl.RGBA4]: SAMPLER_TYPES.FLOAT,\n    [gl.RGB5_A1]: SAMPLER_TYPES.FLOAT,\n    [gl.RGBA8]: SAMPLER_TYPES.FLOAT,\n    [gl.RGBA8_SNORM]: SAMPLER_TYPES.FLOAT,\n    [gl.RGB10_A2]: SAMPLER_TYPES.FLOAT,\n    [gl.RGB10_A2UI]: SAMPLER_TYPES.FLOAT,\n    [gl.SRGB8]: SAMPLER_TYPES.FLOAT,\n    [gl.SRGB8_ALPHA8]: SAMPLER_TYPES.FLOAT,\n    [gl.R16F]: SAMPLER_TYPES.FLOAT,\n    [gl.RG16F]: SAMPLER_TYPES.FLOAT,\n    [gl.RGB16F]: SAMPLER_TYPES.FLOAT,\n    [gl.RGBA16F]: SAMPLER_TYPES.FLOAT,\n    [gl.R32F]: SAMPLER_TYPES.FLOAT,\n    [gl.RG32F]: SAMPLER_TYPES.FLOAT,\n    [gl.RGB32F]: SAMPLER_TYPES.FLOAT,\n    [gl.RGBA32F]: SAMPLER_TYPES.FLOAT,\n    [gl.R11F_G11F_B10F]: SAMPLER_TYPES.FLOAT,\n    [gl.RGB9_E5]: SAMPLER_TYPES.FLOAT,\n    [gl.R8I]: SAMPLER_TYPES.INT,\n    [gl.R8UI]: SAMPLER_TYPES.UINT,\n    [gl.R16I]: SAMPLER_TYPES.INT,\n    [gl.R16UI]: SAMPLER_TYPES.UINT,\n    [gl.R32I]: SAMPLER_TYPES.INT,\n    [gl.R32UI]: SAMPLER_TYPES.UINT,\n    [gl.RG8I]: SAMPLER_TYPES.INT,\n    [gl.RG8UI]: SAMPLER_TYPES.UINT,\n    [gl.RG16I]: SAMPLER_TYPES.INT,\n    [gl.RG16UI]: SAMPLER_TYPES.UINT,\n    [gl.RG32I]: SAMPLER_TYPES.INT,\n    [gl.RG32UI]: SAMPLER_TYPES.UINT,\n    [gl.RGB8I]: SAMPLER_TYPES.INT,\n    [gl.RGB8UI]: SAMPLER_TYPES.UINT,\n    [gl.RGB16I]: SAMPLER_TYPES.INT,\n    [gl.RGB16UI]: SAMPLER_TYPES.UINT,\n    [gl.RGB32I]: SAMPLER_TYPES.INT,\n    [gl.RGB32UI]: SAMPLER_TYPES.UINT,\n    [gl.RGBA8I]: SAMPLER_TYPES.INT,\n    [gl.RGBA8UI]: SAMPLER_TYPES.UINT,\n    [gl.RGBA16I]: SAMPLER_TYPES.INT,\n    [gl.RGBA16UI]: SAMPLER_TYPES.UINT,\n    [gl.RGBA32I]: SAMPLER_TYPES.INT,\n    [gl.RGBA32UI]: SAMPLER_TYPES.UINT,\n    [gl.DEPTH_COMPONENT16]: SAMPLER_TYPES.FLOAT,\n    [gl.DEPTH_COMPONENT24]: SAMPLER_TYPES.FLOAT,\n    [gl.DEPTH_COMPONENT32F]: SAMPLER_TYPES.FLOAT,\n    [gl.DEPTH_STENCIL]: SAMPLER_TYPES.FLOAT,\n    [gl.DEPTH24_STENCIL8]: SAMPLER_TYPES.FLOAT,\n    [gl.DEPTH32F_STENCIL8]: SAMPLER_TYPES.FLOAT\n  } : table = {\n    [gl.RGB]: SAMPLER_TYPES.FLOAT,\n    [gl.RGBA]: SAMPLER_TYPES.FLOAT,\n    [gl.ALPHA]: SAMPLER_TYPES.FLOAT,\n    [gl.LUMINANCE]: SAMPLER_TYPES.FLOAT,\n    [gl.LUMINANCE_ALPHA]: SAMPLER_TYPES.FLOAT,\n    [gl.DEPTH_STENCIL]: SAMPLER_TYPES.FLOAT\n  }, table;\n}\nexport { mapInternalFormatToSamplerType };", "map": {"version": 3, "names": ["mapInternalFormatToSamplerType", "gl", "table", "globalThis", "WebGL2RenderingContext", "RGB", "SAMPLER_TYPES", "FLOAT", "RGBA", "ALPHA", "LUMINANCE", "LUMINANCE_ALPHA", "R8", "R8_SNORM", "RG8", "RG8_SNORM", "RGB8", "RGB8_SNORM", "RGB565", "RGBA4", "RGB5_A1", "RGBA8", "RGBA8_SNORM", "RGB10_A2", "RGB10_A2UI", "SRGB8", "SRGB8_ALPHA8", "R16F", "RG16F", "RGB16F", "RGBA16F", "R32F", "RG32F", "RGB32F", "RGBA32F", "R11F_G11F_B10F", "RGB9_E5", "R8I", "INT", "R8UI", "UINT", "R16I", "R16UI", "R32I", "R32UI", "RG8I", "RG8UI", "RG16I", "RG16UI", "RG32I", "RG32UI", "RGB8I", "RGB8UI", "RGB16I", "RGB16UI", "RGB32I", "RGB32UI", "RGBA8I", "RGBA8UI", "RGBA16I", "RGBA16UI", "RGBA32I", "RGBA32UI", "DEPTH_COMPONENT16", "DEPTH_COMPONENT24", "DEPTH_COMPONENT32F", "DEPTH_STENCIL", "DEPTH24_STENCIL8", "DEPTH32F_STENCIL8"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\textures\\utils\\mapInternalFormatToSamplerType.ts"], "sourcesContent": ["import { SAMPLER_TYPES } from '@pixi/constants';\n\n/**\n * Returns a lookup table that maps each internal format to the compatible sampler type.\n * @memberof PIXI\n * @function mapInternalFormatToSamplerType\n * @private\n * @param {WebGLRenderingContext} gl - The rendering context.\n * @returns Lookup table.\n */\nexport function mapInternalFormatToSamplerType(gl: WebGLRenderingContextBase):\nRecord<number, SAMPLER_TYPES>\n{\n    let table;\n\n    if ('WebGL2RenderingContext' in globalThis && gl instanceof globalThis.WebGL2RenderingContext)\n    {\n        table = {\n            [gl.RGB]: SAMPLER_TYPES.FLOAT,\n            [gl.RGBA]: SAMPLER_TYPES.FLOAT,\n            [gl.ALPHA]: SAMPLER_TYPES.FLOAT,\n            [gl.LUMINANCE]: SAMPLER_TYPES.FLOAT,\n            [gl.LUMINANCE_ALPHA]: SAMPLER_TYPES.FLOAT,\n            [gl.R8]: SAMPLER_TYPES.FLOAT,\n            [gl.R8_SNORM]: SAMPLER_TYPES.FLOAT,\n            [gl.RG8]: SAMPLER_TYPES.FLOAT,\n            [gl.RG8_SNORM]: SAMPLER_TYPES.FLOAT,\n            [gl.RGB8]: SAMPLER_TYPES.FLOAT,\n            [gl.RGB8_SNORM]: SAMPLER_TYPES.FLOAT,\n            [gl.RGB565]: SAMPLER_TYPES.FLOAT,\n            [gl.RGBA4]: SAMPLER_TYPES.FLOAT,\n            [gl.RGB5_A1]: SAMPLER_TYPES.FLOAT,\n            [gl.RGBA8]: SAMPLER_TYPES.FLOAT,\n            [gl.RGBA8_SNORM]: SAMPLER_TYPES.FLOAT,\n            [gl.RGB10_A2]: SAMPLER_TYPES.FLOAT,\n            [gl.RGB10_A2UI]: SAMPLER_TYPES.FLOAT,\n            [gl.SRGB8]: SAMPLER_TYPES.FLOAT,\n            [gl.SRGB8_ALPHA8]: SAMPLER_TYPES.FLOAT,\n            [gl.R16F]: SAMPLER_TYPES.FLOAT,\n            [gl.RG16F]: SAMPLER_TYPES.FLOAT,\n            [gl.RGB16F]: SAMPLER_TYPES.FLOAT,\n            [gl.RGBA16F]: SAMPLER_TYPES.FLOAT,\n            [gl.R32F]: SAMPLER_TYPES.FLOAT,\n            [gl.RG32F]: SAMPLER_TYPES.FLOAT,\n            [gl.RGB32F]: SAMPLER_TYPES.FLOAT,\n            [gl.RGBA32F]: SAMPLER_TYPES.FLOAT,\n            [gl.R11F_G11F_B10F]: SAMPLER_TYPES.FLOAT,\n            [gl.RGB9_E5]: SAMPLER_TYPES.FLOAT,\n            [gl.R8I]: SAMPLER_TYPES.INT,\n            [gl.R8UI]: SAMPLER_TYPES.UINT,\n            [gl.R16I]: SAMPLER_TYPES.INT,\n            [gl.R16UI]: SAMPLER_TYPES.UINT,\n            [gl.R32I]: SAMPLER_TYPES.INT,\n            [gl.R32UI]: SAMPLER_TYPES.UINT,\n            [gl.RG8I]: SAMPLER_TYPES.INT,\n            [gl.RG8UI]: SAMPLER_TYPES.UINT,\n            [gl.RG16I]: SAMPLER_TYPES.INT,\n            [gl.RG16UI]: SAMPLER_TYPES.UINT,\n            [gl.RG32I]: SAMPLER_TYPES.INT,\n            [gl.RG32UI]: SAMPLER_TYPES.UINT,\n            [gl.RGB8I]: SAMPLER_TYPES.INT,\n            [gl.RGB8UI]: SAMPLER_TYPES.UINT,\n            [gl.RGB16I]: SAMPLER_TYPES.INT,\n            [gl.RGB16UI]: SAMPLER_TYPES.UINT,\n            [gl.RGB32I]: SAMPLER_TYPES.INT,\n            [gl.RGB32UI]: SAMPLER_TYPES.UINT,\n            [gl.RGBA8I]: SAMPLER_TYPES.INT,\n            [gl.RGBA8UI]: SAMPLER_TYPES.UINT,\n            [gl.RGBA16I]: SAMPLER_TYPES.INT,\n            [gl.RGBA16UI]: SAMPLER_TYPES.UINT,\n            [gl.RGBA32I]: SAMPLER_TYPES.INT,\n            [gl.RGBA32UI]: SAMPLER_TYPES.UINT,\n            [gl.DEPTH_COMPONENT16]: SAMPLER_TYPES.FLOAT,\n            [gl.DEPTH_COMPONENT24]: SAMPLER_TYPES.FLOAT,\n            [gl.DEPTH_COMPONENT32F]: SAMPLER_TYPES.FLOAT,\n            [gl.DEPTH_STENCIL]: SAMPLER_TYPES.FLOAT,\n            [gl.DEPTH24_STENCIL8]: SAMPLER_TYPES.FLOAT,\n            [gl.DEPTH32F_STENCIL8]: SAMPLER_TYPES.FLOAT,\n        };\n    }\n    else\n    {\n        table = {\n            [gl.RGB]: SAMPLER_TYPES.FLOAT,\n            [gl.RGBA]: SAMPLER_TYPES.FLOAT,\n            [gl.ALPHA]: SAMPLER_TYPES.FLOAT,\n            [gl.LUMINANCE]: SAMPLER_TYPES.FLOAT,\n            [gl.LUMINANCE_ALPHA]: SAMPLER_TYPES.FLOAT,\n            [gl.DEPTH_STENCIL]: SAMPLER_TYPES.FLOAT,\n        };\n    }\n\n    return table;\n}\n"], "mappings": ";AAUO,SAASA,+BAA+<PERSON>,EAAA,EAE/C;EACQ,IAAAC,KAAA;EAEJ,OAAI,4BAA4BC,UAAA,IAAcF,EAAA,YAAcE,UAAA,CAAWC,sBAAA,GAEnEF,KAAA,GAAQ;IACJ,CAACD,EAAA,CAAGI,GAAG,GAAGC,aAAA,CAAcC,KAAA;IACxB,CAACN,EAAA,CAAGO,IAAI,GAAGF,aAAA,CAAcC,KAAA;IACzB,CAACN,EAAA,CAAGQ,KAAK,GAAGH,aAAA,CAAcC,KAAA;IAC1B,CAACN,EAAA,CAAGS,SAAS,GAAGJ,aAAA,CAAcC,KAAA;IAC9B,CAACN,EAAA,CAAGU,eAAe,GAAGL,aAAA,CAAcC,KAAA;IACpC,CAACN,EAAA,CAAGW,EAAE,GAAGN,aAAA,CAAcC,KAAA;IACvB,CAACN,EAAA,CAAGY,QAAQ,GAAGP,aAAA,CAAcC,KAAA;IAC7B,CAACN,EAAA,CAAGa,GAAG,GAAGR,aAAA,CAAcC,KAAA;IACxB,CAACN,EAAA,CAAGc,SAAS,GAAGT,aAAA,CAAcC,KAAA;IAC9B,CAACN,EAAA,CAAGe,IAAI,GAAGV,aAAA,CAAcC,KAAA;IACzB,CAACN,EAAA,CAAGgB,UAAU,GAAGX,aAAA,CAAcC,KAAA;IAC/B,CAACN,EAAA,CAAGiB,MAAM,GAAGZ,aAAA,CAAcC,KAAA;IAC3B,CAACN,EAAA,CAAGkB,KAAK,GAAGb,aAAA,CAAcC,KAAA;IAC1B,CAACN,EAAA,CAAGmB,OAAO,GAAGd,aAAA,CAAcC,KAAA;IAC5B,CAACN,EAAA,CAAGoB,KAAK,GAAGf,aAAA,CAAcC,KAAA;IAC1B,CAACN,EAAA,CAAGqB,WAAW,GAAGhB,aAAA,CAAcC,KAAA;IAChC,CAACN,EAAA,CAAGsB,QAAQ,GAAGjB,aAAA,CAAcC,KAAA;IAC7B,CAACN,EAAA,CAAGuB,UAAU,GAAGlB,aAAA,CAAcC,KAAA;IAC/B,CAACN,EAAA,CAAGwB,KAAK,GAAGnB,aAAA,CAAcC,KAAA;IAC1B,CAACN,EAAA,CAAGyB,YAAY,GAAGpB,aAAA,CAAcC,KAAA;IACjC,CAACN,EAAA,CAAG0B,IAAI,GAAGrB,aAAA,CAAcC,KAAA;IACzB,CAACN,EAAA,CAAG2B,KAAK,GAAGtB,aAAA,CAAcC,KAAA;IAC1B,CAACN,EAAA,CAAG4B,MAAM,GAAGvB,aAAA,CAAcC,KAAA;IAC3B,CAACN,EAAA,CAAG6B,OAAO,GAAGxB,aAAA,CAAcC,KAAA;IAC5B,CAACN,EAAA,CAAG8B,IAAI,GAAGzB,aAAA,CAAcC,KAAA;IACzB,CAACN,EAAA,CAAG+B,KAAK,GAAG1B,aAAA,CAAcC,KAAA;IAC1B,CAACN,EAAA,CAAGgC,MAAM,GAAG3B,aAAA,CAAcC,KAAA;IAC3B,CAACN,EAAA,CAAGiC,OAAO,GAAG5B,aAAA,CAAcC,KAAA;IAC5B,CAACN,EAAA,CAAGkC,cAAc,GAAG7B,aAAA,CAAcC,KAAA;IACnC,CAACN,EAAA,CAAGmC,OAAO,GAAG9B,aAAA,CAAcC,KAAA;IAC5B,CAACN,EAAA,CAAGoC,GAAG,GAAG/B,aAAA,CAAcgC,GAAA;IACxB,CAACrC,EAAA,CAAGsC,IAAI,GAAGjC,aAAA,CAAckC,IAAA;IACzB,CAACvC,EAAA,CAAGwC,IAAI,GAAGnC,aAAA,CAAcgC,GAAA;IACzB,CAACrC,EAAA,CAAGyC,KAAK,GAAGpC,aAAA,CAAckC,IAAA;IAC1B,CAACvC,EAAA,CAAG0C,IAAI,GAAGrC,aAAA,CAAcgC,GAAA;IACzB,CAACrC,EAAA,CAAG2C,KAAK,GAAGtC,aAAA,CAAckC,IAAA;IAC1B,CAACvC,EAAA,CAAG4C,IAAI,GAAGvC,aAAA,CAAcgC,GAAA;IACzB,CAACrC,EAAA,CAAG6C,KAAK,GAAGxC,aAAA,CAAckC,IAAA;IAC1B,CAACvC,EAAA,CAAG8C,KAAK,GAAGzC,aAAA,CAAcgC,GAAA;IAC1B,CAACrC,EAAA,CAAG+C,MAAM,GAAG1C,aAAA,CAAckC,IAAA;IAC3B,CAACvC,EAAA,CAAGgD,KAAK,GAAG3C,aAAA,CAAcgC,GAAA;IAC1B,CAACrC,EAAA,CAAGiD,MAAM,GAAG5C,aAAA,CAAckC,IAAA;IAC3B,CAACvC,EAAA,CAAGkD,KAAK,GAAG7C,aAAA,CAAcgC,GAAA;IAC1B,CAACrC,EAAA,CAAGmD,MAAM,GAAG9C,aAAA,CAAckC,IAAA;IAC3B,CAACvC,EAAA,CAAGoD,MAAM,GAAG/C,aAAA,CAAcgC,GAAA;IAC3B,CAACrC,EAAA,CAAGqD,OAAO,GAAGhD,aAAA,CAAckC,IAAA;IAC5B,CAACvC,EAAA,CAAGsD,MAAM,GAAGjD,aAAA,CAAcgC,GAAA;IAC3B,CAACrC,EAAA,CAAGuD,OAAO,GAAGlD,aAAA,CAAckC,IAAA;IAC5B,CAACvC,EAAA,CAAGwD,MAAM,GAAGnD,aAAA,CAAcgC,GAAA;IAC3B,CAACrC,EAAA,CAAGyD,OAAO,GAAGpD,aAAA,CAAckC,IAAA;IAC5B,CAACvC,EAAA,CAAG0D,OAAO,GAAGrD,aAAA,CAAcgC,GAAA;IAC5B,CAACrC,EAAA,CAAG2D,QAAQ,GAAGtD,aAAA,CAAckC,IAAA;IAC7B,CAACvC,EAAA,CAAG4D,OAAO,GAAGvD,aAAA,CAAcgC,GAAA;IAC5B,CAACrC,EAAA,CAAG6D,QAAQ,GAAGxD,aAAA,CAAckC,IAAA;IAC7B,CAACvC,EAAA,CAAG8D,iBAAiB,GAAGzD,aAAA,CAAcC,KAAA;IACtC,CAACN,EAAA,CAAG+D,iBAAiB,GAAG1D,aAAA,CAAcC,KAAA;IACtC,CAACN,EAAA,CAAGgE,kBAAkB,GAAG3D,aAAA,CAAcC,KAAA;IACvC,CAACN,EAAA,CAAGiE,aAAa,GAAG5D,aAAA,CAAcC,KAAA;IAClC,CAACN,EAAA,CAAGkE,gBAAgB,GAAG7D,aAAA,CAAcC,KAAA;IACrC,CAACN,EAAA,CAAGmE,iBAAiB,GAAG9D,aAAA,CAAcC;EAAA,IAK1CL,KAAA,GAAQ;IACJ,CAACD,EAAA,CAAGI,GAAG,GAAGC,aAAA,CAAcC,KAAA;IACxB,CAACN,EAAA,CAAGO,IAAI,GAAGF,aAAA,CAAcC,KAAA;IACzB,CAACN,EAAA,CAAGQ,KAAK,GAAGH,aAAA,CAAcC,KAAA;IAC1B,CAACN,EAAA,CAAGS,SAAS,GAAGJ,aAAA,CAAcC,KAAA;IAC9B,CAACN,EAAA,CAAGU,eAAe,GAAGL,aAAA,CAAcC,KAAA;IACpC,CAACN,EAAA,CAAGiE,aAAa,GAAG5D,aAAA,CAAcC;EAInC,GAAAL,KAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}