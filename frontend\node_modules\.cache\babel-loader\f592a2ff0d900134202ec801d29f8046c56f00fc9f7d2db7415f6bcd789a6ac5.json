{"ast": null, "code": "import { getCanvasBoundingBox } from \"./getCanvasBoundingBox.mjs\";\nfunction trimCanvas(canvas) {\n  const boundingBox = getCanvasBoundingBox(canvas),\n    {\n      width,\n      height\n    } = boundingBox;\n  let data = null;\n  if (!boundingBox.isEmpty()) {\n    const context = canvas.getContext(\"2d\");\n    if (context === null) throw new TypeError(\"Failed to get canvas 2D context\");\n    data = context.getImageData(boundingBox.left, boundingBox.top, width, height);\n  }\n  return {\n    width,\n    height,\n    data\n  };\n}\nexport { trimCanvas };", "map": {"version": 3, "names": ["trimCanvas", "canvas", "boundingBox", "getCanvasBoundingBox", "width", "height", "data", "isEmpty", "context", "getContext", "TypeError", "getImageData", "left", "top"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\utils\\src\\media\\trimCanvas.ts"], "sourcesContent": ["import { getCanvasBoundingBox } from './getCanvasBoundingBox';\n\nimport type { ICanvas } from '@pixi/settings';\n\n/**\n * Trim transparent borders from a canvas.\n * @memberof PIXI.utils\n * @param {PIXI.ICanvas} canvas - The canvas to trim.\n * @returns The trimmed canvas data.\n */\nexport function trimCanvas(canvas: ICanvas): { width: number, height: number, data: ImageData | null }\n{\n    const boundingBox = getCanvasBoundingBox(canvas);\n    const { width, height } = boundingBox;\n    let data = null;\n\n    if (!boundingBox.isEmpty())\n    {\n        const context = canvas.getContext('2d');\n\n        if (context === null)\n        {\n            throw new TypeError('Failed to get canvas 2D context');\n        }\n\n        data = context.getImageData(\n            boundingBox.left,\n            boundingBox.top,\n            width,\n            height\n        );\n    }\n\n    return { width, height, data };\n}\n"], "mappings": ";AAUO,SAASA,WAAWC,MAAA,EAC3B;EACI,MAAMC,WAAA,GAAcC,oBAAA,CAAqBF,MAAM;IACzC;MAAEG,KAAA;MAAOC;IAAW,IAAAH,WAAA;EAC1B,IAAII,IAAA,GAAO;EAEP,KAACJ,WAAA,CAAYK,OAAA,IACjB;IACU,MAAAC,OAAA,GAAUP,MAAA,CAAOQ,UAAA,CAAW,IAAI;IAEtC,IAAID,OAAA,KAAY,MAEN,UAAIE,SAAA,CAAU,iCAAiC;IAGzDJ,IAAA,GAAOE,OAAA,CAAQG,YAAA,CACXT,WAAA,CAAYU,IAAA,EACZV,WAAA,CAAYW,GAAA,EACZT,KAAA,EACAC,MAAA;EAER;EAEO;IAAED,KAAA;IAAOC,MAAA;IAAQC;EAAA;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}