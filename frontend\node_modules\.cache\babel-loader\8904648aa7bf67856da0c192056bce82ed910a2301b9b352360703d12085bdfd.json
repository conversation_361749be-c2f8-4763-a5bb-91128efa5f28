{"ast": null, "code": "const vertTemplate = `\n    attribute vec2 aVertexPosition;\n\n    uniform mat3 projectionMatrix;\n\n    uniform float strength;\n\n    varying vec2 vBlurTexCoords[%size%];\n\n    uniform vec4 inputSize;\n    uniform vec4 outputFrame;\n\n    vec4 filterVertexPosition( void )\n    {\n        vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;\n\n        return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);\n    }\n\n    vec2 filterTextureCoord( void )\n    {\n        return aVertexPosition * (outputFrame.zw * inputSize.zw);\n    }\n\n    void main(void)\n    {\n        gl_Position = filterVertexPosition();\n\n        vec2 textureCoord = filterTextureCoord();\n        %blur%\n    }`;\nfunction generateBlurVertSource(kernelSize, x) {\n  const halfLength = Math.ceil(kernelSize / 2);\n  let vertSource = vertTemplate,\n    blurLoop = \"\",\n    template;\n  x ? template = \"vBlurTexCoords[%index%] =  textureCoord + vec2(%sampleIndex% * strength, 0.0);\" : template = \"vBlurTexCoords[%index%] =  textureCoord + vec2(0.0, %sampleIndex% * strength);\";\n  for (let i = 0; i < kernelSize; i++) {\n    let blur = template.replace(\"%index%\", i.toString());\n    blur = blur.replace(\"%sampleIndex%\", `${i - (halfLength - 1)}.0`), blurLoop += blur, blurLoop += `\n`;\n  }\n  return vertSource = vertSource.replace(\"%blur%\", blurLoop), vertSource = vertSource.replace(\"%size%\", kernelSize.toString()), vertSource;\n}\nexport { generateBlurVertSource };", "map": {"version": 3, "names": ["vertTemplate", "generateBlurVertSource", "kernelSize", "x", "<PERSON><PERSON><PERSON><PERSON>", "Math", "ceil", "vertSource", "blurLoop", "template", "i", "blur", "replace", "toString"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\filter-blur\\src\\generateBlurVertSource.ts"], "sourcesContent": ["const vertTemplate = `\n    attribute vec2 aVertexPosition;\n\n    uniform mat3 projectionMatrix;\n\n    uniform float strength;\n\n    varying vec2 vBlurTexCoords[%size%];\n\n    uniform vec4 inputSize;\n    uniform vec4 outputFrame;\n\n    vec4 filterVertexPosition( void )\n    {\n        vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;\n\n        return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);\n    }\n\n    vec2 filterTextureCoord( void )\n    {\n        return aVertexPosition * (outputFrame.zw * inputSize.zw);\n    }\n\n    void main(void)\n    {\n        gl_Position = filterVertexPosition();\n\n        vec2 textureCoord = filterTextureCoord();\n        %blur%\n    }`;\n\nexport function generateBlurVertSource(kernelSize: number, x: boolean): string\n{\n    const halfLength = Math.ceil(kernelSize / 2);\n\n    let vertSource = vertTemplate;\n\n    let blurLoop = '';\n    let template;\n\n    if (x)\n    {\n        template = 'vBlurTexCoords[%index%] =  textureCoord + vec2(%sampleIndex% * strength, 0.0);';\n    }\n    else\n    {\n        template = 'vBlurTexCoords[%index%] =  textureCoord + vec2(0.0, %sampleIndex% * strength);';\n    }\n\n    for (let i = 0; i < kernelSize; i++)\n    {\n        let blur = template.replace('%index%', i.toString());\n\n        blur = blur.replace('%sampleIndex%', `${i - (halfLength - 1)}.0`);\n\n        blurLoop += blur;\n        blurLoop += '\\n';\n    }\n\n    vertSource = vertSource.replace('%blur%', blurLoop);\n    vertSource = vertSource.replace('%size%', kernelSize.toString());\n\n    return vertSource;\n}\n"], "mappings": "AAAA,MAAMA,YAAA,GAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgCL,SAAAC,uBAAuBC,UAAA,EAAoBC,CAAA,EAC3D;EACI,MAAMC,UAAA,GAAaC,IAAA,CAAKC,IAAA,CAAKJ,UAAA,GAAa,CAAC;EAEvC,IAAAK,UAAA,GAAaP,YAAA;IAEbQ,QAAA,GAAW;IACXC,QAAA;EAEAN,CAAA,GAEAM,QAAA,GAAW,mFAIXA,QAAA,GAAW;EAGf,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIR,UAAA,EAAYQ,CAAA,IAChC;IACI,IAAIC,IAAA,GAAOF,QAAA,CAASG,OAAA,CAAQ,WAAWF,CAAA,CAAEG,QAAA,EAAU;IAE5CF,IAAA,GAAAA,IAAA,CAAKC,OAAA,CAAQ,iBAAiB,GAAGF,CAAA,IAAKN,UAAA,GAAa,EAAE,IAAI,GAEhEI,QAAA,IAAYG,IAAA,EACZH,QAAA,IAAY;AAAA;EAChB;EAEA,OAAAD,UAAA,GAAaA,UAAA,CAAWK,OAAA,CAAQ,UAAUJ,QAAQ,GAClDD,UAAA,GAAaA,UAAA,CAAWK,OAAA,CAAQ,UAAUV,UAAA,CAAWW,QAAA,CAAU,IAExDN,UAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}