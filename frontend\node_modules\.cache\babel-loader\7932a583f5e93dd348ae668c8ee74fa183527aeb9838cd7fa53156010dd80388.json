{"ast": null, "code": "import { ExtensionType, extensions } from \"@pixi/core\";\nclass ResizePlugin {\n  /**\n   * Initialize the plugin with scope of application instance\n   * @static\n   * @private\n   * @param {object} [options] - See application options\n   */\n  static init(options) {\n    Object.defineProperty(this, \"resizeTo\",\n    /**\n     * The HTML element or window to automatically resize the\n     * renderer's view element to match width and height.\n     * @member {Window|HTMLElement}\n     * @name resizeTo\n     * @memberof PIXI.Application#\n     */\n    {\n      set(dom) {\n        globalThis.removeEventListener(\"resize\", this.queueResize), this._resizeTo = dom, dom && (globalThis.addEventListener(\"resize\", this.queueResize), this.resize());\n      },\n      get() {\n        return this._resizeTo;\n      }\n    }), this.queueResize = () => {\n      this._resizeTo && (this.cancelResize(), this._resizeId = requestAnimationFrame(() => this.resize()));\n    }, this.cancelResize = () => {\n      this._resizeId && (cancelAnimationFrame(this._resizeId), this._resizeId = null);\n    }, this.resize = () => {\n      if (!this._resizeTo) return;\n      this.cancelResize();\n      let width, height;\n      if (this._resizeTo === globalThis.window) width = globalThis.innerWidth, height = globalThis.innerHeight;else {\n        const {\n          clientWidth,\n          clientHeight\n        } = this._resizeTo;\n        width = clientWidth, height = clientHeight;\n      }\n      this.renderer.resize(width, height), this.render();\n    }, this._resizeId = null, this._resizeTo = null, this.resizeTo = options.resizeTo || null;\n  }\n  /**\n   * Clean up the ticker, scoped to application\n   * @static\n   * @private\n   */\n  static destroy() {\n    globalThis.removeEventListener(\"resize\", this.queueResize), this.cancelResize(), this.cancelResize = null, this.queueResize = null, this.resizeTo = null, this.resize = null;\n  }\n}\nResizePlugin.extension = ExtensionType.Application;\nextensions.add(ResizePlugin);\nexport { ResizePlugin };", "map": {"version": 3, "names": ["ResizePlugin", "init", "options", "Object", "defineProperty", "set", "dom", "globalThis", "removeEventListener", "queueResize", "_resizeTo", "addEventListener", "resize", "get", "cancelResize", "_resizeId", "requestAnimationFrame", "cancelAnimationFrame", "width", "height", "window", "innerWidth", "innerHeight", "clientWidth", "clientHeight", "renderer", "render", "resizeTo", "destroy", "extension", "ExtensionType", "Application", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\app\\src\\ResizePlugin.ts"], "sourcesContent": ["import { extensions, ExtensionType } from '@pixi/core';\n\nimport type { ExtensionMetadata, Renderer } from '@pixi/core';\n\ntype ResizeableRenderer = Pick<Renderer, 'resize'>;\n\nexport interface ResizePluginOptions\n{\n    /**\n     * Element to automatically resize stage to.\n     * @memberof PIXI.IApplicationOptions\n     */\n    resizeTo?: Window | HTMLElement;\n}\n\n/**\n * Middleware for for Application's resize functionality\n * @private\n * @class\n */\nexport class ResizePlugin\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = ExtensionType.Application;\n\n    public static resizeTo: Window | HTMLElement;\n    public static resize: () => void;\n    public static renderer: ResizeableRenderer;\n    public static queueResize: () => void;\n    public static render: () => void;\n    private static _resizeId: number;\n    private static _resizeTo: Window | HTMLElement;\n    private static cancelResize: () => void;\n\n    /**\n     * Initialize the plugin with scope of application instance\n     * @static\n     * @private\n     * @param {object} [options] - See application options\n     */\n    static init(options: ResizePluginOptions): void\n    {\n        Object.defineProperty(this, 'resizeTo',\n            /**\n             * The HTML element or window to automatically resize the\n             * renderer's view element to match width and height.\n             * @member {Window|HTMLElement}\n             * @name resizeTo\n             * @memberof PIXI.Application#\n             */\n            {\n                set(dom: Window | HTMLElement)\n                {\n                    globalThis.removeEventListener('resize', this.queueResize);\n                    this._resizeTo = dom;\n                    if (dom)\n                    {\n                        globalThis.addEventListener('resize', this.queueResize);\n                        this.resize();\n                    }\n                },\n                get()\n                {\n                    return this._resizeTo;\n                },\n            });\n\n        /**\n         * Resize is throttled, so it's safe to call this multiple times per frame and it'll\n         * only be called once.\n         * @memberof PIXI.Application#\n         * @method queueResize\n         * @private\n         */\n        this.queueResize = (): void =>\n        {\n            if (!this._resizeTo)\n            {\n                return;\n            }\n\n            this.cancelResize();\n\n            // // Throttle resize events per raf\n            this._resizeId = requestAnimationFrame(() => this.resize());\n        };\n\n        /**\n         * Cancel the resize queue.\n         * @memberof PIXI.Application#\n         * @method cancelResize\n         * @private\n         */\n        this.cancelResize = (): void =>\n        {\n            if (this._resizeId)\n            {\n                cancelAnimationFrame(this._resizeId);\n                this._resizeId = null;\n            }\n        };\n\n        /**\n         * Execute an immediate resize on the renderer, this is not\n         * throttled and can be expensive to call many times in a row.\n         * Will resize only if `resizeTo` property is set.\n         * @memberof PIXI.Application#\n         * @method resize\n         */\n        this.resize = (): void =>\n        {\n            if (!this._resizeTo)\n            {\n                return;\n            }\n\n            // clear queue resize\n            this.cancelResize();\n\n            let width: number;\n            let height: number;\n\n            // Resize to the window\n            if (this._resizeTo === globalThis.window)\n            {\n                width = globalThis.innerWidth;\n                height = globalThis.innerHeight;\n            }\n            // Resize to other HTML entities\n            else\n            {\n                const { clientWidth, clientHeight } = this._resizeTo as HTMLElement;\n\n                width = clientWidth;\n                height = clientHeight;\n            }\n\n            this.renderer.resize(width, height);\n            this.render();\n        };\n\n        // On resize\n        this._resizeId = null;\n        this._resizeTo = null;\n        this.resizeTo = options.resizeTo || null;\n    }\n\n    /**\n     * Clean up the ticker, scoped to application\n     * @static\n     * @private\n     */\n    static destroy(): void\n    {\n        globalThis.removeEventListener('resize', this.queueResize);\n        this.cancelResize();\n        this.cancelResize = null;\n        this.queueResize = null;\n        this.resizeTo = null;\n        this.resize = null;\n    }\n}\n\nextensions.add(ResizePlugin);\n"], "mappings": ";AAoBO,MAAMA,YAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAmBI,OAAOC,KAAKC,OAAA,EACZ;IACWC,MAAA,CAAAC,cAAA,CAAe,MAAM;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAQxB;MACIC,IAAIC,GAAA,EACJ;QACIC,UAAA,CAAWC,mBAAA,CAAoB,UAAU,KAAKC,WAAW,GACzD,KAAKC,SAAA,GAAYJ,GAAA,EACbA,GAAA,KAEAC,UAAA,CAAWI,gBAAA,CAAiB,UAAU,KAAKF,WAAW,GACtD,KAAKG,MAAA,CAAO;MAEpB;MACAC,IAAA,EACA;QACI,OAAO,KAAKH,SAAA;MAChB;IACJ,IASJ,KAAKD,WAAA,GAAc,MACnB;MACS,KAAKC,SAAA,KAKV,KAAKI,YAAA,CAAa,GAGlB,KAAKC,SAAA,GAAYC,qBAAA,CAAsB,MAAM,KAAKJ,MAAA,EAAQ;IAAA,GAS9D,KAAKE,YAAA,GAAe,MACpB;MACQ,KAAKC,SAAA,KAELE,oBAAA,CAAqB,KAAKF,SAAS,GACnC,KAAKA,SAAA,GAAY;IAAA,GAWzB,KAAKH,MAAA,GAAS,MACd;MACI,IAAI,CAAC,KAAKF,SAAA,EAEN;MAIJ,KAAKI,YAAA,CAAa;MAElB,IAAII,KAAA,EACAC,MAAA;MAGA,SAAKT,SAAA,KAAcH,UAAA,CAAWa,MAAA,EAEtBF,KAAA,GAAAX,UAAA,CAAWc,UAAA,EACnBF,MAAA,GAASZ,UAAA,CAAWe,WAAA,MAIxB;QACI,MAAM;UAAEC,WAAA;UAAaC;QAAA,IAAiB,KAAKd,SAAA;QAE3CQ,KAAA,GAAQK,WAAA,EACRJ,MAAA,GAASK,YAAA;MACb;MAEA,KAAKC,QAAA,CAASb,MAAA,CAAOM,KAAA,EAAOC,MAAM,GAClC,KAAKO,MAAA;IACT,GAGA,KAAKX,SAAA,GAAY,MACjB,KAAKL,SAAA,GAAY,MACjB,KAAKiB,QAAA,GAAWzB,OAAA,CAAQyB,QAAA,IAAY;EACxC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,OAAOC,QAAA,EACP;IACIrB,UAAA,CAAWC,mBAAA,CAAoB,UAAU,KAAKC,WAAW,GACzD,KAAKK,YAAA,CACL,QAAKA,YAAA,GAAe,MACpB,KAAKL,WAAA,GAAc,MACnB,KAAKkB,QAAA,GAAW,MAChB,KAAKf,MAAA,GAAS;EAClB;AACJ;AA7IaZ,YAAA,CAGF6B,SAAA,GAA+BC,aAAA,CAAcC,WAAA;AA4IxDC,UAAA,CAAWC,GAAA,CAAIjC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}