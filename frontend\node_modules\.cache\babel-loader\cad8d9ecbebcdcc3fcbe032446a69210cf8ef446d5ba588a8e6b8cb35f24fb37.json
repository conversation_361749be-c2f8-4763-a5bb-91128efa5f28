{"ast": null, "code": "import { utils, ALPHA_MODES, MIPMAP_MODES, Rectangle, Texture, settings, BaseTexture } from \"@pixi/core\";\nimport { TextStyle, TextMetrics } from \"@pixi/text\";\nimport { BitmapFontData } from \"./BitmapFontData.mjs\";\nimport { autoDetectFormat } from \"./formats/index.mjs\";\nimport \"./utils/index.mjs\";\nimport { resolveCharacters } from \"./utils/resolveCharacters.mjs\";\nimport { drawGlyph } from \"./utils/drawGlyph.mjs\";\nimport { extractCharCode } from \"./utils/extractCharCode.mjs\";\nconst _BitmapFont = class _BitmapFont2 {\n  /**\n   * @param data\n   * @param textures\n   * @param ownsTextures - Setting to `true` will destroy page textures\n   *        when the font is uninstalled.\n   */\n  constructor(data, textures, ownsTextures) {\n    const [info] = data.info,\n      [common] = data.common,\n      [page] = data.page,\n      [distanceField] = data.distanceField,\n      res = utils.getResolutionOfUrl(page.file),\n      pageTextures = {};\n    this._ownsTextures = ownsTextures, this.font = info.face, this.size = info.size, this.lineHeight = common.lineHeight / res, this.chars = {}, this.pageTextures = pageTextures;\n    for (let i = 0; i < data.page.length; i++) {\n      const {\n        id,\n        file\n      } = data.page[i];\n      pageTextures[id] = textures instanceof Array ? textures[i] : textures[file], distanceField?.fieldType && distanceField.fieldType !== \"none\" && (pageTextures[id].baseTexture.alphaMode = ALPHA_MODES.NO_PREMULTIPLIED_ALPHA, pageTextures[id].baseTexture.mipmap = MIPMAP_MODES.OFF);\n    }\n    for (let i = 0; i < data.char.length; i++) {\n      const {\n        id,\n        page: page2\n      } = data.char[i];\n      let {\n        x,\n        y,\n        width,\n        height,\n        xoffset,\n        yoffset,\n        xadvance\n      } = data.char[i];\n      x /= res, y /= res, width /= res, height /= res, xoffset /= res, yoffset /= res, xadvance /= res;\n      const rect = new Rectangle(x + pageTextures[page2].frame.x / res, y + pageTextures[page2].frame.y / res, width, height);\n      this.chars[id] = {\n        xOffset: xoffset,\n        yOffset: yoffset,\n        xAdvance: xadvance,\n        kerning: {},\n        texture: new Texture(pageTextures[page2].baseTexture, rect),\n        page: page2\n      };\n    }\n    for (let i = 0; i < data.kerning.length; i++) {\n      let {\n        first,\n        second,\n        amount\n      } = data.kerning[i];\n      first /= res, second /= res, amount /= res, this.chars[second] && (this.chars[second].kerning[first] = amount);\n    }\n    this.distanceFieldRange = distanceField?.distanceRange, this.distanceFieldType = distanceField?.fieldType?.toLowerCase() ?? \"none\";\n  }\n  /** Remove references to created glyph textures. */\n  destroy() {\n    for (const id in this.chars) this.chars[id].texture.destroy(), this.chars[id].texture = null;\n    for (const id in this.pageTextures) this._ownsTextures && this.pageTextures[id].destroy(!0), this.pageTextures[id] = null;\n    this.chars = null, this.pageTextures = null;\n  }\n  /**\n   * Register a new bitmap font.\n   * @param data - The\n   *        characters map that could be provided as xml or raw string.\n   * @param textures - List of textures for each page.\n   * @param ownsTextures - Set to `true` to destroy page textures\n   *        when the font is uninstalled. By default fonts created with\n   *        `BitmapFont.from` or from the `BitmapFontLoader` are `true`.\n   * @returns {PIXI.BitmapFont} Result font object with font, size, lineHeight\n   *         and char fields.\n   */\n  static install(data, textures, ownsTextures) {\n    let fontData;\n    if (data instanceof BitmapFontData) fontData = data;else {\n      const format = autoDetectFormat(data);\n      if (!format) throw new Error(\"Unrecognized data format for font.\");\n      fontData = format.parse(data);\n    }\n    textures instanceof Texture && (textures = [textures]);\n    const font = new _BitmapFont2(fontData, textures, ownsTextures);\n    return _BitmapFont2.available[font.font] = font, font;\n  }\n  /**\n   * Remove bitmap font by name.\n   * @param name - Name of the font to uninstall.\n   */\n  static uninstall(name) {\n    const font = _BitmapFont2.available[name];\n    if (!font) throw new Error(`No font found named '${name}'`);\n    font.destroy(), delete _BitmapFont2.available[name];\n  }\n  /**\n   * Generates a bitmap-font for the given style and character set. This does not support\n   * kernings yet. With `style` properties, only the following non-layout properties are used:\n   *\n   * - {@link PIXI.TextStyle#dropShadow|dropShadow}\n   * - {@link PIXI.TextStyle#dropShadowDistance|dropShadowDistance}\n   * - {@link PIXI.TextStyle#dropShadowColor|dropShadowColor}\n   * - {@link PIXI.TextStyle#dropShadowBlur|dropShadowBlur}\n   * - {@link PIXI.TextStyle#dropShadowAngle|dropShadowAngle}\n   * - {@link PIXI.TextStyle#fill|fill}\n   * - {@link PIXI.TextStyle#fillGradientStops|fillGradientStops}\n   * - {@link PIXI.TextStyle#fillGradientType|fillGradientType}\n   * - {@link PIXI.TextStyle#fontFamily|fontFamily}\n   * - {@link PIXI.TextStyle#fontSize|fontSize}\n   * - {@link PIXI.TextStyle#fontVariant|fontVariant}\n   * - {@link PIXI.TextStyle#fontWeight|fontWeight}\n   * - {@link PIXI.TextStyle#lineJoin|lineJoin}\n   * - {@link PIXI.TextStyle#miterLimit|miterLimit}\n   * - {@link PIXI.TextStyle#stroke|stroke}\n   * - {@link PIXI.TextStyle#strokeThickness|strokeThickness}\n   * - {@link PIXI.TextStyle#textBaseline|textBaseline}\n   * @param name - The name of the custom font to use with BitmapText.\n   * @param textStyle - Style options to render with BitmapFont.\n   * @param options - Setup options for font or name of the font.\n   * @returns Font generated by style options.\n   * @example\n   * import { BitmapFont, BitmapText } from 'pixi.js';\n   *\n   * BitmapFont.from('TitleFont', {\n   *     fontFamily: 'Arial',\n   *     fontSize: 12,\n   *     strokeThickness: 2,\n   *     fill: 'purple',\n   * });\n   *\n   * const title = new BitmapText('This is the title', { fontName: 'TitleFont' });\n   */\n  static from(name, textStyle, options) {\n    if (!name) throw new Error(\"[BitmapFont] Property `name` is required.\");\n    const {\n        chars,\n        padding,\n        resolution,\n        textureWidth,\n        textureHeight,\n        ...baseOptions\n      } = Object.assign({}, _BitmapFont2.defaultOptions, options),\n      charsList = resolveCharacters(chars),\n      style = textStyle instanceof TextStyle ? textStyle : new TextStyle(textStyle),\n      lineWidth = textureWidth,\n      fontData = new BitmapFontData();\n    fontData.info[0] = {\n      face: style.fontFamily,\n      size: style.fontSize\n    }, fontData.common[0] = {\n      lineHeight: style.fontSize\n    };\n    let positionX = 0,\n      positionY = 0,\n      canvas,\n      context,\n      baseTexture,\n      maxCharHeight = 0;\n    const baseTextures = [],\n      textures = [];\n    for (let i = 0; i < charsList.length; i++) {\n      canvas || (canvas = settings.ADAPTER.createCanvas(), canvas.width = textureWidth, canvas.height = textureHeight, context = canvas.getContext(\"2d\"), baseTexture = new BaseTexture(canvas, {\n        resolution,\n        ...baseOptions\n      }), baseTextures.push(baseTexture), textures.push(new Texture(baseTexture)), fontData.page.push({\n        id: textures.length - 1,\n        file: \"\"\n      }));\n      const character = charsList[i],\n        metrics = TextMetrics.measureText(character, style, !1, canvas),\n        width = metrics.width,\n        height = Math.ceil(metrics.height),\n        textureGlyphWidth = Math.ceil((style.fontStyle === \"italic\" ? 2 : 1) * width);\n      if (positionY >= textureHeight - height * resolution) {\n        if (positionY === 0) throw new Error(`[BitmapFont] textureHeight ${textureHeight}px is too small (fontFamily: '${style.fontFamily}', fontSize: ${style.fontSize}px, char: '${character}')`);\n        --i, canvas = null, context = null, baseTexture = null, positionY = 0, positionX = 0, maxCharHeight = 0;\n        continue;\n      }\n      if (maxCharHeight = Math.max(height + metrics.fontProperties.descent, maxCharHeight), textureGlyphWidth * resolution + positionX >= lineWidth) {\n        if (positionX === 0) throw new Error(`[BitmapFont] textureWidth ${textureWidth}px is too small (fontFamily: '${style.fontFamily}', fontSize: ${style.fontSize}px, char: '${character}')`);\n        --i, positionY += maxCharHeight * resolution, positionY = Math.ceil(positionY), positionX = 0, maxCharHeight = 0;\n        continue;\n      }\n      drawGlyph(canvas, context, metrics, positionX, positionY, resolution, style);\n      const id = extractCharCode(metrics.text);\n      fontData.char.push({\n        id,\n        page: textures.length - 1,\n        x: positionX / resolution,\n        y: positionY / resolution,\n        width: textureGlyphWidth,\n        height,\n        xoffset: 0,\n        yoffset: 0,\n        xadvance: width - (style.dropShadow ? style.dropShadowDistance : 0) - (style.stroke ? style.strokeThickness : 0)\n      }), positionX += (textureGlyphWidth + 2 * padding) * resolution, positionX = Math.ceil(positionX);\n    }\n    if (!options?.skipKerning) for (let i = 0, len = charsList.length; i < len; i++) {\n      const first = charsList[i];\n      for (let j = 0; j < len; j++) {\n        const second = charsList[j],\n          c1 = context.measureText(first).width,\n          c2 = context.measureText(second).width,\n          amount = context.measureText(first + second).width - (c1 + c2);\n        amount && fontData.kerning.push({\n          first: extractCharCode(first),\n          second: extractCharCode(second),\n          amount\n        });\n      }\n    }\n    const font = new _BitmapFont2(fontData, textures, !0);\n    return _BitmapFont2.available[name] !== void 0 && _BitmapFont2.uninstall(name), _BitmapFont2.available[name] = font, font;\n  }\n};\n_BitmapFont.ALPHA = [[\"a\", \"z\"], [\"A\", \"Z\"], \" \"],\n/**\n* This character set includes all decimal digits (from 0 to 9).\n* @type {string[][]}\n* @example\n* BitmapFont.from('ExampleFont', style, { chars: BitmapFont.NUMERIC })\n*/\n_BitmapFont.NUMERIC = [[\"0\", \"9\"]],\n/**\n* This character set is the union of `BitmapFont.ALPHA` and `BitmapFont.NUMERIC`.\n* @type {string[][]}\n*/\n_BitmapFont.ALPHANUMERIC = [[\"a\", \"z\"], [\"A\", \"Z\"], [\"0\", \"9\"], \" \"],\n/**\n* This character set consists of all the ASCII table.\n* @member {string[][]}\n* @see http://www.asciitable.com/\n*/\n_BitmapFont.ASCII = [[\" \", \"~\"]],\n/**\n* Collection of default options when using `BitmapFont.from`.\n* @property {number} [resolution=1] -\n* @property {number} [textureWidth=512] -\n* @property {number} [textureHeight=512] -\n* @property {number} [padding=4] -\n* @property {string|string[]|string[][]} chars = PIXI.BitmapFont.ALPHANUMERIC\n*/\n_BitmapFont.defaultOptions = {\n  resolution: 1,\n  textureWidth: 512,\n  textureHeight: 512,\n  padding: 4,\n  chars: _BitmapFont.ALPHANUMERIC\n}, /** Collection of available/installed fonts. */\n_BitmapFont.available = {};\nlet BitmapFont = _BitmapFont;\nexport { BitmapFont };", "map": {"version": 3, "names": ["_BitmapFont", "_BitmapFont2", "constructor", "data", "textures", "ownsTextures", "info", "common", "page", "distanceField", "res", "utils", "getResolutionOfUrl", "file", "pageTextures", "_ownsTextures", "font", "face", "size", "lineHeight", "chars", "i", "length", "id", "Array", "fieldType", "baseTexture", "alphaMode", "ALPHA_MODES", "NO_PREMULTIPLIED_ALPHA", "mipmap", "MIPMAP_MODES", "OFF", "char", "page2", "x", "y", "width", "height", "xoffset", "yoffset", "xadvance", "rect", "Rectangle", "frame", "xOffset", "yOffset", "xAdvance", "kerning", "texture", "Texture", "first", "second", "amount", "distanceFieldRange", "distanceRange", "distanceFieldType", "toLowerCase", "destroy", "install", "fontData", "BitmapFontData", "format", "autoDetectFormat", "Error", "parse", "available", "uninstall", "name", "from", "textStyle", "options", "padding", "resolution", "textureWidth", "textureHeight", "baseOptions", "Object", "assign", "defaultOptions", "charsList", "resolveCharacters", "style", "TextStyle", "lineWidth", "fontFamily", "fontSize", "positionX", "positionY", "canvas", "context", "maxCharHeight", "baseTextures", "settings", "ADAPTER", "createCanvas", "getContext", "BaseTexture", "push", "character", "metrics", "TextMetrics", "measureText", "Math", "ceil", "textureGlyphWidth", "fontStyle", "max", "fontProperties", "descent", "drawGlyph", "extractCharCode", "text", "dropShadow", "dropShadowDistance", "stroke", "strokeThickness", "skip<PERSON><PERSON>ing", "len", "j", "c1", "c2", "ALPHA", "NUMERIC", "ALPHANUMERIC", "ASCII", "BitmapFont"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\text-bitmap\\src\\BitmapFont.ts"], "sourcesContent": ["import { ALPHA_MODES, BaseTexture, MIPMAP_MODES, Rectangle, settings, Texture, utils } from '@pixi/core';\nimport { TextMetrics, TextStyle } from '@pixi/text';\nimport { BitmapFontData } from './BitmapFontData';\nimport { autoDetectFormat } from './formats';\nimport { drawGlyph, extractCharCode, resolveCharacters } from './utils';\n\nimport type { IBaseTextureOptions, ICanvas, ICanvasRenderingContext2D, SCALE_MODES } from '@pixi/core';\nimport type { ITextStyle } from '@pixi/text';\n\nexport interface IBitmapFontCharacter\n{\n    xOffset: number;\n    yOffset: number;\n    xAdvance: number;\n    texture: Texture;\n    page: number;\n    kerning: utils.Dict<number>;\n}\n\ntype BaseOptions = Pick<IBaseTextureOptions, 'scaleMode' | 'mipmap' | 'anisotropicLevel' | 'alphaMode'>;\n\n/** @memberof PIXI */\nexport interface IBitmapFontOptions extends BaseOptions\n{\n    /**\n     * Characters included in the font set. You can also use ranges.\n     * For example, `[['a', 'z'], ['A', 'Z'], \"!@#$%^&*()~{}[] \"]`.\n     * Don't forget to include spaces ' ' in your character set!\n     * @default PIXI.BitmapFont.ALPHANUMERIC\n     */\n    chars?: string | (string | string[])[];\n\n    /**\n     * Render resolution for glyphs.\n     * @default 1\n     */\n    resolution?: number;\n\n    /**\n     * Padding between glyphs on texture atlas. Lower values could mean more visual artifacts\n     * and bleeding from other glyphs, larger values increase the space required on the texture.\n     * @default 4\n     */\n    padding?: number;\n\n    /**\n     * Optional width of atlas, smaller values to reduce memory.\n     * @default 512\n     */\n    textureWidth?: number;\n\n    /**\n     * Optional height of atlas, smaller values to reduce memory.\n     * @default 512\n     */\n    textureHeight?: number;\n\n    /**\n     * If mipmapping is enabled for texture. For instance, by default PixiJS only enables mipmapping\n     * on Power-of-Two textures. If your textureWidth or textureHeight are not power-of-two, you\n     * may consider enabling mipmapping to get better quality with lower font sizes. Note:\n     * for MSDF/SDF fonts, mipmapping is not supported.\n     * @default PIXI.BaseTexture.defaultOptions.mipmap\n     */\n    mipmap?: MIPMAP_MODES;\n\n    /**\n     * Anisotropic filtering level of texture.\n     * @default PIXI.BaseTexture.defaultOptions.anisotropicLevel\n     */\n    anisotropicLevel?: number;\n\n    /**\n     * Default scale mode, linear, nearest. Nearest can be helpful for bitmap-style fonts.\n     * @default PIXI.BaseTexture.defaultOptions.scaleMode\n     */\n    scaleMode?: SCALE_MODES;\n\n    /**\n     * Pre multiply the image alpha.  Note: for MSDF/SDF fonts, alphaMode is not supported.\n     * @default PIXI.BaseTexture.defaultOptions.alphaMode\n     */\n    alphaMode?: ALPHA_MODES;\n\n    /**\n     * Skip generation of kerning information for the BitmapFont.\n     * If true, this could potentially increase the performance, but may impact the rendered text appearance.\n     * @default false\n     */\n    skipKerning?: boolean;\n}\n\n/**\n * BitmapFont represents a typeface available for use with the BitmapText class. Use the `install`\n * method for adding a font to be used.\n * @memberof PIXI\n */\nexport class BitmapFont\n{\n    /**\n     * This character set includes all the letters in the alphabet (both lower- and upper- case).\n     * @type {string[][]}\n     * @example\n     * BitmapFont.from('ExampleFont', style, { chars: BitmapFont.ALPHA })\n     */\n    public static readonly ALPHA = [['a', 'z'], ['A', 'Z'], ' '];\n\n    /**\n     * This character set includes all decimal digits (from 0 to 9).\n     * @type {string[][]}\n     * @example\n     * BitmapFont.from('ExampleFont', style, { chars: BitmapFont.NUMERIC })\n     */\n    public static readonly NUMERIC = [['0', '9']];\n\n    /**\n     * This character set is the union of `BitmapFont.ALPHA` and `BitmapFont.NUMERIC`.\n     * @type {string[][]}\n     */\n    public static readonly ALPHANUMERIC = [['a', 'z'], ['A', 'Z'], ['0', '9'], ' '];\n\n    /**\n     * This character set consists of all the ASCII table.\n     * @member {string[][]}\n     * @see http://www.asciitable.com/\n     */\n    public static readonly ASCII = [[' ', '~']];\n\n    /**\n     * Collection of default options when using `BitmapFont.from`.\n     * @property {number} [resolution=1] -\n     * @property {number} [textureWidth=512] -\n     * @property {number} [textureHeight=512] -\n     * @property {number} [padding=4] -\n     * @property {string|string[]|string[][]} chars = PIXI.BitmapFont.ALPHANUMERIC\n     */\n    public static readonly defaultOptions: IBitmapFontOptions = {\n        resolution: 1,\n        textureWidth: 512,\n        textureHeight: 512,\n        padding: 4,\n        chars: BitmapFont.ALPHANUMERIC,\n    };\n\n    /** Collection of available/installed fonts. */\n    public static readonly available: utils.Dict<BitmapFont> = {};\n\n    /** The name of the font face. */\n    public readonly font: string;\n\n    /** The size of the font face in pixels. */\n    public readonly size: number;\n\n    /** The line-height of the font face in pixels. */\n    public readonly lineHeight: number;\n\n    /** The map of characters by character code. */\n    public readonly chars: utils.Dict<IBitmapFontCharacter>;\n\n    /** The map of base page textures (i.e., sheets of glyphs). */\n    public readonly pageTextures: utils.Dict<Texture>;\n\n    /** The range of the distance field in pixels. */\n    public readonly distanceFieldRange: number;\n\n    /** The kind of distance field for this font or \"none\". */\n    public readonly distanceFieldType: string;\n\n    private _ownsTextures: boolean;\n\n    /**\n     * @param data\n     * @param textures\n     * @param ownsTextures - Setting to `true` will destroy page textures\n     *        when the font is uninstalled.\n     */\n    constructor(data: BitmapFontData, textures: Texture[] | utils.Dict<Texture>, ownsTextures?: boolean)\n    {\n        const [info] = data.info;\n        const [common] = data.common;\n        const [page] = data.page;\n        const [distanceField] = data.distanceField;\n        const res = utils.getResolutionOfUrl(page.file);\n        const pageTextures: utils.Dict<Texture> = {};\n\n        this._ownsTextures = ownsTextures;\n        this.font = info.face;\n        this.size = info.size;\n        this.lineHeight = common.lineHeight / res;\n        this.chars = {};\n        this.pageTextures = pageTextures;\n\n        // Convert the input Texture, Textures or object\n        // into a page Texture lookup by \"id\"\n        for (let i = 0; i < data.page.length; i++)\n        {\n            const { id, file } = data.page[i];\n\n            pageTextures[id] = textures instanceof Array\n                ? textures[i] : textures[file];\n\n            // only MSDF and SDF fonts need no-premultiplied-alpha\n            if (distanceField?.fieldType && distanceField.fieldType !== 'none')\n            {\n                pageTextures[id].baseTexture.alphaMode = ALPHA_MODES.NO_PREMULTIPLIED_ALPHA;\n                pageTextures[id].baseTexture.mipmap = MIPMAP_MODES.OFF;\n            }\n        }\n\n        // parse letters\n        for (let i = 0; i < data.char.length; i++)\n        {\n            const { id, page } = data.char[i];\n            let { x, y, width, height, xoffset, yoffset, xadvance } = data.char[i];\n\n            x /= res;\n            y /= res;\n            width /= res;\n            height /= res;\n            xoffset /= res;\n            yoffset /= res;\n            xadvance /= res;\n\n            const rect = new Rectangle(\n                x + (pageTextures[page].frame.x / res),\n                y + (pageTextures[page].frame.y / res),\n                width,\n                height\n            );\n\n            this.chars[id] = {\n                xOffset: xoffset,\n                yOffset: yoffset,\n                xAdvance: xadvance,\n                kerning: {},\n                texture: new Texture(\n                    pageTextures[page].baseTexture,\n                    rect\n                ),\n                page,\n            };\n        }\n\n        // parse kernings\n        for (let i = 0; i < data.kerning.length; i++)\n        {\n            let { first, second, amount } = data.kerning[i];\n\n            first /= res;\n            second /= res;\n            amount /= res;\n\n            if (this.chars[second])\n            {\n                this.chars[second].kerning[first] = amount;\n            }\n        }\n\n        // Store distance field information\n        this.distanceFieldRange = distanceField?.distanceRange;\n        this.distanceFieldType = distanceField?.fieldType?.toLowerCase() ?? 'none';\n    }\n\n    /** Remove references to created glyph textures. */\n    public destroy(): void\n    {\n        for (const id in this.chars)\n        {\n            this.chars[id].texture.destroy();\n            this.chars[id].texture = null;\n        }\n\n        for (const id in this.pageTextures)\n        {\n            if (this._ownsTextures)\n            {\n                this.pageTextures[id].destroy(true);\n            }\n\n            this.pageTextures[id] = null;\n        }\n\n        // Set readonly null.\n        (this as any).chars = null;\n        (this as any).pageTextures = null;\n    }\n\n    /**\n     * Register a new bitmap font.\n     * @param data - The\n     *        characters map that could be provided as xml or raw string.\n     * @param textures - List of textures for each page.\n     * @param ownsTextures - Set to `true` to destroy page textures\n     *        when the font is uninstalled. By default fonts created with\n     *        `BitmapFont.from` or from the `BitmapFontLoader` are `true`.\n     * @returns {PIXI.BitmapFont} Result font object with font, size, lineHeight\n     *         and char fields.\n     */\n    public static install(\n        data: string | XMLDocument | BitmapFontData,\n        textures: Texture | Texture[] | utils.Dict<Texture>,\n        ownsTextures?: boolean\n    ): BitmapFont\n    {\n        let fontData;\n\n        if (data instanceof BitmapFontData)\n        {\n            fontData = data;\n        }\n        else\n        {\n            const format = autoDetectFormat(data);\n\n            if (!format)\n            {\n                throw new Error('Unrecognized data format for font.');\n            }\n\n            fontData = format.parse(data as any);\n        }\n\n        // Single texture, convert to list\n        if (textures instanceof Texture)\n        {\n            textures = [textures];\n        }\n\n        const font = new BitmapFont(fontData, textures, ownsTextures);\n\n        BitmapFont.available[font.font] = font;\n\n        return font;\n    }\n\n    /**\n     * Remove bitmap font by name.\n     * @param name - Name of the font to uninstall.\n     */\n    public static uninstall(name: string): void\n    {\n        const font = BitmapFont.available[name];\n\n        if (!font)\n        {\n            throw new Error(`No font found named '${name}'`);\n        }\n\n        font.destroy();\n        delete BitmapFont.available[name];\n    }\n\n    /**\n     * Generates a bitmap-font for the given style and character set. This does not support\n     * kernings yet. With `style` properties, only the following non-layout properties are used:\n     *\n     * - {@link PIXI.TextStyle#dropShadow|dropShadow}\n     * - {@link PIXI.TextStyle#dropShadowDistance|dropShadowDistance}\n     * - {@link PIXI.TextStyle#dropShadowColor|dropShadowColor}\n     * - {@link PIXI.TextStyle#dropShadowBlur|dropShadowBlur}\n     * - {@link PIXI.TextStyle#dropShadowAngle|dropShadowAngle}\n     * - {@link PIXI.TextStyle#fill|fill}\n     * - {@link PIXI.TextStyle#fillGradientStops|fillGradientStops}\n     * - {@link PIXI.TextStyle#fillGradientType|fillGradientType}\n     * - {@link PIXI.TextStyle#fontFamily|fontFamily}\n     * - {@link PIXI.TextStyle#fontSize|fontSize}\n     * - {@link PIXI.TextStyle#fontVariant|fontVariant}\n     * - {@link PIXI.TextStyle#fontWeight|fontWeight}\n     * - {@link PIXI.TextStyle#lineJoin|lineJoin}\n     * - {@link PIXI.TextStyle#miterLimit|miterLimit}\n     * - {@link PIXI.TextStyle#stroke|stroke}\n     * - {@link PIXI.TextStyle#strokeThickness|strokeThickness}\n     * - {@link PIXI.TextStyle#textBaseline|textBaseline}\n     * @param name - The name of the custom font to use with BitmapText.\n     * @param textStyle - Style options to render with BitmapFont.\n     * @param options - Setup options for font or name of the font.\n     * @returns Font generated by style options.\n     * @example\n     * import { BitmapFont, BitmapText } from 'pixi.js';\n     *\n     * BitmapFont.from('TitleFont', {\n     *     fontFamily: 'Arial',\n     *     fontSize: 12,\n     *     strokeThickness: 2,\n     *     fill: 'purple',\n     * });\n     *\n     * const title = new BitmapText('This is the title', { fontName: 'TitleFont' });\n     */\n    public static from(name: string, textStyle?: TextStyle | Partial<ITextStyle>, options?: IBitmapFontOptions): BitmapFont\n    {\n        if (!name)\n        {\n            throw new Error('[BitmapFont] Property `name` is required.');\n        }\n\n        const {\n            chars,\n            padding,\n            resolution,\n            textureWidth,\n            textureHeight,\n            ...baseOptions\n        } = Object.assign({}, BitmapFont.defaultOptions, options);\n\n        const charsList = resolveCharacters(chars);\n        const style = textStyle instanceof TextStyle ? textStyle : new TextStyle(textStyle);\n        const lineWidth = textureWidth;\n        const fontData = new BitmapFontData();\n\n        fontData.info[0] = {\n            face: style.fontFamily as string,\n            size: style.fontSize as number,\n        };\n        fontData.common[0] = {\n            lineHeight: style.fontSize as number,\n        };\n\n        let positionX = 0;\n        let positionY = 0;\n\n        let canvas: ICanvas;\n        let context: ICanvasRenderingContext2D;\n        let baseTexture: BaseTexture;\n        let maxCharHeight = 0;\n        const baseTextures: BaseTexture[] = [];\n        const textures: Texture[] = [];\n\n        for (let i = 0; i < charsList.length; i++)\n        {\n            if (!canvas)\n            {\n                canvas = settings.ADAPTER.createCanvas();\n                canvas.width = textureWidth;\n                canvas.height = textureHeight;\n\n                context = canvas.getContext('2d');\n                baseTexture = new BaseTexture(canvas, { resolution, ...baseOptions });\n\n                baseTextures.push(baseTexture);\n                textures.push(new Texture(baseTexture));\n\n                fontData.page.push({\n                    id: textures.length - 1,\n                    file: '',\n                });\n            }\n\n            // Measure glyph dimensions\n            const character = charsList[i];\n            const metrics = TextMetrics.measureText(character, style, false, canvas);\n            const width = metrics.width;\n            const height = Math.ceil(metrics.height);\n\n            // This is ugly - but italics are given more space so they don't overlap\n            const textureGlyphWidth = Math.ceil((style.fontStyle === 'italic' ? 2 : 1) * width);\n\n            // Can't fit char anymore: next canvas please!\n            if (positionY >= textureHeight - (height * resolution))\n            {\n                if (positionY === 0)\n                {\n                    // We don't want user debugging an infinite loop (or do we? :)\n                    throw new Error(`[BitmapFont] textureHeight ${textureHeight}px is too small `\n                        + `(fontFamily: '${style.fontFamily}', fontSize: ${style.fontSize}px, char: '${character}')`);\n                }\n\n                --i;\n\n                // Create new atlas once current has filled up\n                canvas = null;\n                context = null;\n                baseTexture = null;\n                positionY = 0;\n                positionX = 0;\n                maxCharHeight = 0;\n\n                continue;\n            }\n\n            maxCharHeight = Math.max(height + metrics.fontProperties.descent, maxCharHeight);\n\n            // Wrap line once full row has been rendered\n            if ((textureGlyphWidth * resolution) + positionX >= lineWidth)\n            {\n                if (positionX === 0)\n                {\n                    // Avoid infinite loop (There can be some very wide char like '\\uFDFD'!)\n                    throw new Error(`[BitmapFont] textureWidth ${textureWidth}px is too small `\n                        + `(fontFamily: '${style.fontFamily}', fontSize: ${style.fontSize}px, char: '${character}')`);\n                }\n\n                --i;\n                positionY += maxCharHeight * resolution;\n                positionY = Math.ceil(positionY);\n                positionX = 0;\n                maxCharHeight = 0;\n\n                continue;\n            }\n\n            drawGlyph(canvas, context, metrics, positionX, positionY, resolution, style);\n\n            // Unique (numeric) ID mapping to this glyph\n            const id = extractCharCode(metrics.text);\n\n            // Create a texture holding just the glyph\n            fontData.char.push({\n                id,\n                page: textures.length - 1,\n                x: positionX / resolution,\n                y: positionY / resolution,\n                width: textureGlyphWidth,\n                height,\n                xoffset: 0,\n                yoffset: 0,\n                xadvance: width\n                        - (style.dropShadow ? style.dropShadowDistance : 0)\n                        - (style.stroke ? style.strokeThickness : 0),\n            });\n\n            positionX += (textureGlyphWidth + (2 * padding)) * resolution;\n            positionX = Math.ceil(positionX);\n        }\n\n        if (!options?.skipKerning)\n        {\n            // Brute-force kerning info, this can be expensive b/c it's an O(n²),\n            // but we're using measureText which is native and fast.\n            for (let i = 0, len = charsList.length; i < len; i++)\n            {\n                const first = charsList[i];\n\n                for (let j = 0; j < len; j++)\n                {\n                    const second = charsList[j];\n                    const c1 = context.measureText(first).width;\n                    const c2 = context.measureText(second).width;\n                    const total = context.measureText(first + second).width;\n                    const amount = total - (c1 + c2);\n\n                    if (amount)\n                    {\n                        fontData.kerning.push({\n                            first: extractCharCode(first),\n                            second: extractCharCode(second),\n                            amount,\n                        });\n                    }\n                }\n            }\n        }\n\n        const font = new BitmapFont(fontData, textures, true);\n\n        // Make it easier to replace a font\n        if (BitmapFont.available[name] !== undefined)\n        {\n            BitmapFont.uninstall(name);\n        }\n\n        BitmapFont.available[name] = font;\n\n        return font;\n    }\n}\n"], "mappings": ";;;;;;;;AAiGO,MAAMA,WAAA,GAAN,MAAMC,YAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EA8EIC,YAAYC,IAAA,EAAsBC,QAAA,EAA2CC,YAAA,EAC7E;IACU,OAACC,IAAI,IAAIH,IAAA,CAAKG,IAAA;MACd,CAACC,MAAM,IAAIJ,IAAA,CAAKI,MAAA;MAChB,CAACC,IAAI,IAAIL,IAAA,CAAKK,IAAA;MACd,CAACC,aAAa,IAAIN,IAAA,CAAKM,aAAA;MACvBC,GAAA,GAAMC,KAAA,CAAMC,kBAAA,CAAmBJ,IAAA,CAAKK,IAAI;MACxCC,YAAA,GAAoC;IAErC,KAAAC,aAAA,GAAgBV,YAAA,EACrB,KAAKW,IAAA,GAAOV,IAAA,CAAKW,IAAA,EACjB,KAAKC,IAAA,GAAOZ,IAAA,CAAKY,IAAA,EACjB,KAAKC,UAAA,GAAaZ,MAAA,CAAOY,UAAA,GAAaT,GAAA,EACtC,KAAKU,KAAA,GAAQ,IACb,KAAKN,YAAA,GAAeA,YAAA;IAIpB,SAASO,CAAA,GAAI,GAAGA,CAAA,GAAIlB,IAAA,CAAKK,IAAA,CAAKc,MAAA,EAAQD,CAAA,IACtC;MACI,MAAM;QAAEE,EAAA;QAAIV;MAAA,IAASV,IAAA,CAAKK,IAAA,CAAKa,CAAC;MAEnBP,YAAA,CAAAS,EAAE,IAAInB,QAAA,YAAoBoB,KAAA,GACjCpB,QAAA,CAASiB,CAAC,IAAIjB,QAAA,CAASS,IAAI,GAG7BJ,aAAA,EAAegB,SAAA,IAAahB,aAAA,CAAcgB,SAAA,KAAc,WAExDX,YAAA,CAAaS,EAAE,EAAEG,WAAA,CAAYC,SAAA,GAAYC,WAAA,CAAYC,sBAAA,EACrDf,YAAA,CAAaS,EAAE,EAAEG,WAAA,CAAYI,MAAA,GAASC,YAAA,CAAaC,GAAA;IAE3D;IAGA,SAASX,CAAA,GAAI,GAAGA,CAAA,GAAIlB,IAAA,CAAK8B,IAAA,CAAKX,MAAA,EAAQD,CAAA,IACtC;MACI,MAAM;QAAEE,EAAA;QAAIf,IAAA,EAAA0B;MAAS,IAAA/B,IAAA,CAAK8B,IAAA,CAAKZ,CAAC;MAC5B;QAAEc,CAAA;QAAGC,CAAA;QAAGC,KAAA;QAAOC,MAAA;QAAQC,OAAA;QAASC,OAAA;QAASC;MAAS,IAAItC,IAAA,CAAK8B,IAAA,CAAKZ,CAAC;MAEhEc,CAAA,IAAAzB,GAAA,EACL0B,CAAA,IAAK1B,GAAA,EACL2B,KAAA,IAAS3B,GAAA,EACT4B,MAAA,IAAU5B,GAAA,EACV6B,OAAA,IAAW7B,GAAA,EACX8B,OAAA,IAAW9B,GAAA,EACX+B,QAAA,IAAY/B,GAAA;MAEZ,MAAMgC,IAAA,GAAO,IAAIC,SAAA,CACbR,CAAA,GAAKrB,YAAA,CAAaoB,KAAI,EAAEU,KAAA,CAAMT,CAAA,GAAIzB,GAAA,EAClC0B,CAAA,GAAKtB,YAAA,CAAaoB,KAAI,EAAEU,KAAA,CAAMR,CAAA,GAAI1B,GAAA,EAClC2B,KAAA,EACAC,MAAA;MAGC,KAAAlB,KAAA,CAAMG,EAAE,IAAI;QACbsB,OAAA,EAASN,OAAA;QACTO,OAAA,EAASN,OAAA;QACTO,QAAA,EAAUN,QAAA;QACVO,OAAA,EAAS,CAAC;QACVC,OAAA,EAAS,IAAIC,OAAA,CACTpC,YAAA,CAAaoB,KAAI,EAAER,WAAA,EACnBgB,IACJ;QACAlC,IAAA,EAAA0B;MAAA;IAER;IAGA,SAASb,CAAA,GAAI,GAAGA,CAAA,GAAIlB,IAAA,CAAK6C,OAAA,CAAQ1B,MAAA,EAAQD,CAAA,IACzC;MACI,IAAI;QAAE8B,KAAA;QAAOC,MAAA;QAAQC;MAAW,IAAAlD,IAAA,CAAK6C,OAAA,CAAQ3B,CAAC;MAE9C8B,KAAA,IAASzC,GAAA,EACT0C,MAAA,IAAU1C,GAAA,EACV2C,MAAA,IAAU3C,GAAA,EAEN,KAAKU,KAAA,CAAMgC,MAAM,MAEjB,KAAKhC,KAAA,CAAMgC,MAAM,EAAEJ,OAAA,CAAQG,KAAK,IAAIE,MAAA;IAE5C;IAGK,KAAAC,kBAAA,GAAqB7C,aAAA,EAAe8C,aAAA,EACzC,KAAKC,iBAAA,GAAoB/C,aAAA,EAAegB,SAAA,EAAWgC,WAAA,CAAiB;EACxE;EAAA;EAGOC,QAAA,EACP;IACI,WAAWnC,EAAA,IAAM,KAAKH,KAAA,EAEb,KAAAA,KAAA,CAAMG,EAAE,EAAE0B,OAAA,CAAQS,OAAA,IACvB,KAAKtC,KAAA,CAAMG,EAAE,EAAE0B,OAAA,GAAU;IAG7B,WAAW1B,EAAA,IAAM,KAAKT,YAAA,EAEd,KAAKC,aAAA,IAEL,KAAKD,YAAA,CAAaS,EAAE,EAAEmC,OAAA,CAAQ,EAAI,GAGtC,KAAK5C,YAAA,CAAaS,EAAE,IAAI;IAI3B,KAAaH,KAAA,GAAQ,MACrB,KAAaN,YAAA,GAAe;EACjC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAaA,OAAc6C,QACVxD,IAAA,EACAC,QAAA,EACAC,YAAA,EAEJ;IACQ,IAAAuD,QAAA;IAEJ,IAAIzD,IAAA,YAAgB0D,cAAA,EAELD,QAAA,GAAAzD,IAAA,MAGf;MACU,MAAA2D,MAAA,GAASC,gBAAA,CAAiB5D,IAAI;MAEpC,IAAI,CAAC2D,MAAA,EAEK,UAAIE,KAAA,CAAM,oCAAoC;MAG7CJ,QAAA,GAAAE,MAAA,CAAOG,KAAA,CAAM9D,IAAW;IACvC;IAGIC,QAAA,YAAoB8C,OAAA,KAEpB9C,QAAA,GAAW,CAACA,QAAQ;IAGxB,MAAMY,IAAA,GAAO,IAAIf,YAAA,CAAW2D,QAAA,EAAUxD,QAAA,EAAUC,YAAY;IAE5D,OAAAJ,YAAA,CAAWiE,SAAA,CAAUlD,IAAA,CAAKA,IAAI,IAAIA,IAAA,EAE3BA,IAAA;EACX;EAAA;AAAA;AAAA;AAAA;EAMA,OAAcmD,UAAUC,IAAA,EACxB;IACU,MAAApD,IAAA,GAAOf,YAAA,CAAWiE,SAAA,CAAUE,IAAI;IAEtC,IAAI,CAACpD,IAAA,EAED,MAAM,IAAIgD,KAAA,CAAM,wBAAwBI,IAAI,GAAG;IAGnDpD,IAAA,CAAK0C,OAAA,CAAQ,GACb,OAAOzD,YAAA,CAAWiE,SAAA,CAAUE,IAAI;EACpC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAuCA,OAAcC,KAAKD,IAAA,EAAcE,SAAA,EAA6CC,OAAA,EAC9E;IACI,IAAI,CAACH,IAAA,EAEK,UAAIJ,KAAA,CAAM,2CAA2C;IAGzD;QACF5C,KAAA;QACAoD,OAAA;QACAC,UAAA;QACAC,YAAA;QACAC,aAAA;QACA,GAAGC;MACP,IAAIC,MAAA,CAAOC,MAAA,CAAO,CAAC,GAAG7E,YAAA,CAAW8E,cAAA,EAAgBR,OAAO;MAElDS,SAAA,GAAYC,iBAAA,CAAkB7D,KAAK;MACnC8D,KAAA,GAAQZ,SAAA,YAAqBa,SAAA,GAAYb,SAAA,GAAY,IAAIa,SAAA,CAAUb,SAAS;MAC5Ec,SAAA,GAAYV,YAAA;MACZd,QAAA,GAAW,IAAIC,cAAA;IAEZD,QAAA,CAAAtD,IAAA,CAAK,CAAC,IAAI;MACfW,IAAA,EAAMiE,KAAA,CAAMG,UAAA;MACZnE,IAAA,EAAMgE,KAAA,CAAMI;IAAA,GAEhB1B,QAAA,CAASrD,MAAA,CAAO,CAAC,IAAI;MACjBY,UAAA,EAAY+D,KAAA,CAAMI;IAAA;IAGtB,IAAIC,SAAA,GAAY;MACZC,SAAA,GAAY;MAEZC,MAAA;MACAC,OAAA;MACAhE,WAAA;MACAiE,aAAA,GAAgB;IACpB,MAAMC,YAAA,GAA8B;MAC9BxF,QAAA,GAAsB;IAE5B,SAASiB,CAAA,GAAI,GAAGA,CAAA,GAAI2D,SAAA,CAAU1D,MAAA,EAAQD,CAAA,IACtC;MACSoE,MAAA,KAEDA,MAAA,GAASI,QAAA,CAASC,OAAA,CAAQC,YAAA,CAAa,GACvCN,MAAA,CAAOpD,KAAA,GAAQqC,YAAA,EACfe,MAAA,CAAOnD,MAAA,GAASqC,aAAA,EAEhBe,OAAA,GAAUD,MAAA,CAAOO,UAAA,CAAW,IAAI,GAChCtE,WAAA,GAAc,IAAIuE,WAAA,CAAYR,MAAA,EAAQ;QAAEhB,UAAA;QAAY,GAAGG;MAAa,IAEpEgB,YAAA,CAAaM,IAAA,CAAKxE,WAAW,GAC7BtB,QAAA,CAAS8F,IAAA,CAAK,IAAIhD,OAAA,CAAQxB,WAAW,CAAC,GAEtCkC,QAAA,CAASpD,IAAA,CAAK0F,IAAA,CAAK;QACf3E,EAAA,EAAInB,QAAA,CAASkB,MAAA,GAAS;QACtBT,IAAA,EAAM;MACT;MAIL,MAAMsF,SAAA,GAAYnB,SAAA,CAAU3D,CAAC;QACvB+E,OAAA,GAAUC,WAAA,CAAYC,WAAA,CAAYH,SAAA,EAAWjB,KAAA,EAAO,IAAOO,MAAM;QACjEpD,KAAA,GAAQ+D,OAAA,CAAQ/D,KAAA;QAChBC,MAAA,GAASiE,IAAA,CAAKC,IAAA,CAAKJ,OAAA,CAAQ9D,MAAM;QAGjCmE,iBAAA,GAAoBF,IAAA,CAAKC,IAAA,EAAMtB,KAAA,CAAMwB,SAAA,KAAc,WAAW,IAAI,KAAKrE,KAAK;MAG9E,IAAAmD,SAAA,IAAab,aAAA,GAAiBrC,MAAA,GAASmC,UAAA,EAC3C;QACI,IAAIe,SAAA,KAAc,GAGd,MAAM,IAAIxB,KAAA,CAAM,8BAA8BW,aAAa,iCACpCO,KAAA,CAAMG,UAAU,gBAAgBH,KAAA,CAAMI,QAAQ,cAAca,SAAS,IAAI;QAGlG,EAAA9E,CAAA,EAGFoE,MAAA,GAAS,MACTC,OAAA,GAAU,MACVhE,WAAA,GAAc,MACd8D,SAAA,GAAY,GACZD,SAAA,GAAY,GACZI,aAAA,GAAgB;QAEhB;MACJ;MAKA,IAHAA,aAAA,GAAgBY,IAAA,CAAKI,GAAA,CAAIrE,MAAA,GAAS8D,OAAA,CAAQQ,cAAA,CAAeC,OAAA,EAASlB,aAAa,GAG1Ec,iBAAA,GAAoBhC,UAAA,GAAcc,SAAA,IAAaH,SAAA,EACpD;QACI,IAAIG,SAAA,KAAc,GAGd,MAAM,IAAIvB,KAAA,CAAM,6BAA6BU,YAAY,iCAClCQ,KAAA,CAAMG,UAAU,gBAAgBH,KAAA,CAAMI,QAAQ,cAAca,SAAS,IAAI;QAGlG,EAAA9E,CAAA,EACFmE,SAAA,IAAaG,aAAA,GAAgBlB,UAAA,EAC7Be,SAAA,GAAYe,IAAA,CAAKC,IAAA,CAAKhB,SAAS,GAC/BD,SAAA,GAAY,GACZI,aAAA,GAAgB;QAEhB;MACJ;MAEAmB,SAAA,CAAUrB,MAAA,EAAQC,OAAA,EAASU,OAAA,EAASb,SAAA,EAAWC,SAAA,EAAWf,UAAA,EAAYS,KAAK;MAGrE,MAAA3D,EAAA,GAAKwF,eAAA,CAAgBX,OAAA,CAAQY,IAAI;MAGvCpD,QAAA,CAAS3B,IAAA,CAAKiE,IAAA,CAAK;QACf3E,EAAA;QACAf,IAAA,EAAMJ,QAAA,CAASkB,MAAA,GAAS;QACxBa,CAAA,EAAGoD,SAAA,GAAYd,UAAA;QACfrC,CAAA,EAAGoD,SAAA,GAAYf,UAAA;QACfpC,KAAA,EAAOoE,iBAAA;QACPnE,MAAA;QACAC,OAAA,EAAS;QACTC,OAAA,EAAS;QACTC,QAAA,EAAUJ,KAAA,IACC6C,KAAA,CAAM+B,UAAA,GAAa/B,KAAA,CAAMgC,kBAAA,GAAqB,MAC9ChC,KAAA,CAAMiC,MAAA,GAASjC,KAAA,CAAMkC,eAAA,GAAkB;MACrD,IAED7B,SAAA,KAAckB,iBAAA,GAAqB,IAAIjC,OAAA,IAAYC,UAAA,EACnDc,SAAA,GAAYgB,IAAA,CAAKC,IAAA,CAAKjB,SAAS;IACnC;IAEA,IAAI,CAAChB,OAAA,EAAS8C,WAAA,EAIV,SAAShG,CAAA,GAAI,GAAGiG,GAAA,GAAMtC,SAAA,CAAU1D,MAAA,EAAQD,CAAA,GAAIiG,GAAA,EAAKjG,CAAA,IACjD;MACU,MAAA8B,KAAA,GAAQ6B,SAAA,CAAU3D,CAAC;MAEzB,SAASkG,CAAA,GAAI,GAAGA,CAAA,GAAID,GAAA,EAAKC,CAAA,IACzB;QACU,MAAAnE,MAAA,GAAS4B,SAAA,CAAUuC,CAAC;UACpBC,EAAA,GAAK9B,OAAA,CAAQY,WAAA,CAAYnD,KAAK,EAAEd,KAAA;UAChCoF,EAAA,GAAK/B,OAAA,CAAQY,WAAA,CAAYlD,MAAM,EAAEf,KAAA;UAEjCgB,MAAA,GADQqC,OAAA,CAAQY,WAAA,CAAYnD,KAAA,GAAQC,MAAM,EAAEf,KAAA,IAC1BmF,EAAA,GAAKC,EAAA;QAEzBpE,MAAA,IAEAO,QAAA,CAASZ,OAAA,CAAQkD,IAAA,CAAK;UAClB/C,KAAA,EAAO4D,eAAA,CAAgB5D,KAAK;UAC5BC,MAAA,EAAQ2D,eAAA,CAAgB3D,MAAM;UAC9BC;QAAA,CACH;MAET;IACJ;IAGJ,MAAMrC,IAAA,GAAO,IAAIf,YAAA,CAAW2D,QAAA,EAAUxD,QAAA,EAAU,EAAI;IAGpD,OAAIH,YAAA,CAAWiE,SAAA,CAAUE,IAAI,MAAM,UAE/BnE,YAAA,CAAWkE,SAAA,CAAUC,IAAI,GAG7BnE,YAAA,CAAWiE,SAAA,CAAUE,IAAI,IAAIpD,IAAA,EAEtBA,IAAA;EACX;AACJ;AApdahB,WAAA,CAQc0H,KAAA,GAAQ,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARlD1H,WAAA,CAgBc2H,OAAA,GAAU,CAAC,CAAC,KAAK,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAhBnC3H,WAAA,CAsBc4H,YAAA,GAAe,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAtBrE5H,WAAA,CA6Bc6H,KAAA,GAAQ,CAAC,CAAC,KAAK,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA7BjC7H,WAAA,CAuCc+E,cAAA,GAAqC;EACxDN,UAAA,EAAY;EACZC,YAAA,EAAc;EACdC,aAAA,EAAe;EACfH,OAAA,EAAS;EACTpD,KAAA,EAAOpB,WAAA,CAAW4H;AACtB;AA7CS5H,WAAA,CAgDckE,SAAA,GAAoC;AAhDxD,IAAM4D,UAAA,GAAN9H,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}