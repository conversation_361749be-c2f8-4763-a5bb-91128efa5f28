{"ast": null, "code": "import { uniformParsers } from \"./uniformParsers.mjs\";\nconst GLSL_TO_SINGLE_SETTERS_CACHED = {\n    float: `\n    if (cv !== v)\n    {\n        cu.value = v;\n        gl.uniform1f(location, v);\n    }`,\n    vec2: `\n    if (cv[0] !== v[0] || cv[1] !== v[1])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n\n        gl.uniform2f(location, v[0], v[1])\n    }`,\n    vec3: `\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n\n        gl.uniform3f(location, v[0], v[1], v[2])\n    }`,\n    vec4: `\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n        cv[3] = v[3];\n\n        gl.uniform4f(location, v[0], v[1], v[2], v[3]);\n    }`,\n    int: `\n    if (cv !== v)\n    {\n        cu.value = v;\n\n        gl.uniform1i(location, v);\n    }`,\n    ivec2: `\n    if (cv[0] !== v[0] || cv[1] !== v[1])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n\n        gl.uniform2i(location, v[0], v[1]);\n    }`,\n    ivec3: `\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n\n        gl.uniform3i(location, v[0], v[1], v[2]);\n    }`,\n    ivec4: `\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n        cv[3] = v[3];\n\n        gl.uniform4i(location, v[0], v[1], v[2], v[3]);\n    }`,\n    uint: `\n    if (cv !== v)\n    {\n        cu.value = v;\n\n        gl.uniform1ui(location, v);\n    }`,\n    uvec2: `\n    if (cv[0] !== v[0] || cv[1] !== v[1])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n\n        gl.uniform2ui(location, v[0], v[1]);\n    }`,\n    uvec3: `\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n\n        gl.uniform3ui(location, v[0], v[1], v[2]);\n    }`,\n    uvec4: `\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n        cv[3] = v[3];\n\n        gl.uniform4ui(location, v[0], v[1], v[2], v[3]);\n    }`,\n    bool: `\n    if (cv !== v)\n    {\n        cu.value = v;\n        gl.uniform1i(location, v);\n    }`,\n    bvec2: `\n    if (cv[0] != v[0] || cv[1] != v[1])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n\n        gl.uniform2i(location, v[0], v[1]);\n    }`,\n    bvec3: `\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n\n        gl.uniform3i(location, v[0], v[1], v[2]);\n    }`,\n    bvec4: `\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n        cv[3] = v[3];\n\n        gl.uniform4i(location, v[0], v[1], v[2], v[3]);\n    }`,\n    mat2: \"gl.uniformMatrix2fv(location, false, v)\",\n    mat3: \"gl.uniformMatrix3fv(location, false, v)\",\n    mat4: \"gl.uniformMatrix4fv(location, false, v)\",\n    sampler2D: `\n    if (cv !== v)\n    {\n        cu.value = v;\n\n        gl.uniform1i(location, v);\n    }`,\n    samplerCube: `\n    if (cv !== v)\n    {\n        cu.value = v;\n\n        gl.uniform1i(location, v);\n    }`,\n    sampler2DArray: `\n    if (cv !== v)\n    {\n        cu.value = v;\n\n        gl.uniform1i(location, v);\n    }`\n  },\n  GLSL_TO_ARRAY_SETTERS = {\n    float: \"gl.uniform1fv(location, v)\",\n    vec2: \"gl.uniform2fv(location, v)\",\n    vec3: \"gl.uniform3fv(location, v)\",\n    vec4: \"gl.uniform4fv(location, v)\",\n    mat4: \"gl.uniformMatrix4fv(location, false, v)\",\n    mat3: \"gl.uniformMatrix3fv(location, false, v)\",\n    mat2: \"gl.uniformMatrix2fv(location, false, v)\",\n    int: \"gl.uniform1iv(location, v)\",\n    ivec2: \"gl.uniform2iv(location, v)\",\n    ivec3: \"gl.uniform3iv(location, v)\",\n    ivec4: \"gl.uniform4iv(location, v)\",\n    uint: \"gl.uniform1uiv(location, v)\",\n    uvec2: \"gl.uniform2uiv(location, v)\",\n    uvec3: \"gl.uniform3uiv(location, v)\",\n    uvec4: \"gl.uniform4uiv(location, v)\",\n    bool: \"gl.uniform1iv(location, v)\",\n    bvec2: \"gl.uniform2iv(location, v)\",\n    bvec3: \"gl.uniform3iv(location, v)\",\n    bvec4: \"gl.uniform4iv(location, v)\",\n    sampler2D: \"gl.uniform1iv(location, v)\",\n    samplerCube: \"gl.uniform1iv(location, v)\",\n    sampler2DArray: \"gl.uniform1iv(location, v)\"\n  };\nfunction generateUniformsSync(group, uniformData) {\n  const funcFragments = [`\n        var v = null;\n        var cv = null;\n        var cu = null;\n        var t = 0;\n        var gl = renderer.gl;\n    `];\n  for (const i in group.uniforms) {\n    const data = uniformData[i];\n    if (!data) {\n      group.uniforms[i]?.group === !0 && (group.uniforms[i].ubo ? funcFragments.push(`\n                        renderer.shader.syncUniformBufferGroup(uv.${i}, '${i}');\n                    `) : funcFragments.push(`\n                        renderer.shader.syncUniformGroup(uv.${i}, syncData);\n                    `));\n      continue;\n    }\n    const uniform = group.uniforms[i];\n    let parsed = !1;\n    for (let j = 0; j < uniformParsers.length; j++) if (uniformParsers[j].test(data, uniform)) {\n      funcFragments.push(uniformParsers[j].code(i, uniform)), parsed = !0;\n      break;\n    }\n    if (!parsed) {\n      const template = (data.size === 1 && !data.isArray ? GLSL_TO_SINGLE_SETTERS_CACHED : GLSL_TO_ARRAY_SETTERS)[data.type].replace(\"location\", `ud[\"${i}\"].location`);\n      funcFragments.push(`\n            cu = ud[\"${i}\"];\n            cv = cu.value;\n            v = uv[\"${i}\"];\n            ${template};`);\n    }\n  }\n  return new Function(\"ud\", \"uv\", \"renderer\", \"syncData\", funcFragments.join(`\n`));\n}\nexport { generateUniformsSync };", "map": {"version": 3, "names": ["GLSL_TO_SINGLE_SETTERS_CACHED", "float", "vec2", "vec3", "vec4", "int", "ivec2", "ivec3", "ivec4", "uint", "uvec2", "uvec3", "uvec4", "bool", "bvec2", "bvec3", "bvec4", "mat2", "mat3", "mat4", "sampler2D", "samplerCube", "sampler2DArray", "GLSL_TO_ARRAY_SETTERS", "generateUniformsSync", "group", "uniformData", "funcFragments", "i", "uniforms", "data", "ubo", "push", "uniform", "parsed", "j", "uniformParsers", "length", "test", "code", "template", "size", "isArray", "type", "replace", "Function", "join"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\shader\\utils\\generateUniformsSync.ts"], "sourcesContent": ["import { uniformParsers } from './uniformParsers';\n\nimport type { Dict } from '@pixi/utils';\nimport type { UniformGroup } from '../UniformGroup';\n\nexport type UniformsSyncCallback = (...args: any[]) => void;\n\n// cu = Cached value's uniform data field\n// cv = Cached value\n// v = value to upload\n// ud = uniformData\n// uv = uniformValue\n// l = location\nconst GLSL_TO_SINGLE_SETTERS_CACHED: Dict<string> = {\n\n    float: `\n    if (cv !== v)\n    {\n        cu.value = v;\n        gl.uniform1f(location, v);\n    }`,\n\n    vec2: `\n    if (cv[0] !== v[0] || cv[1] !== v[1])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n\n        gl.uniform2f(location, v[0], v[1])\n    }`,\n\n    vec3: `\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n\n        gl.uniform3f(location, v[0], v[1], v[2])\n    }`,\n\n    vec4: `\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n        cv[3] = v[3];\n\n        gl.uniform4f(location, v[0], v[1], v[2], v[3]);\n    }`,\n\n    int: `\n    if (cv !== v)\n    {\n        cu.value = v;\n\n        gl.uniform1i(location, v);\n    }`,\n    ivec2: `\n    if (cv[0] !== v[0] || cv[1] !== v[1])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n\n        gl.uniform2i(location, v[0], v[1]);\n    }`,\n    ivec3: `\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n\n        gl.uniform3i(location, v[0], v[1], v[2]);\n    }`,\n    ivec4: `\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n        cv[3] = v[3];\n\n        gl.uniform4i(location, v[0], v[1], v[2], v[3]);\n    }`,\n\n    uint: `\n    if (cv !== v)\n    {\n        cu.value = v;\n\n        gl.uniform1ui(location, v);\n    }`,\n    uvec2: `\n    if (cv[0] !== v[0] || cv[1] !== v[1])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n\n        gl.uniform2ui(location, v[0], v[1]);\n    }`,\n    uvec3: `\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n\n        gl.uniform3ui(location, v[0], v[1], v[2]);\n    }`,\n    uvec4: `\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n        cv[3] = v[3];\n\n        gl.uniform4ui(location, v[0], v[1], v[2], v[3]);\n    }`,\n\n    bool: `\n    if (cv !== v)\n    {\n        cu.value = v;\n        gl.uniform1i(location, v);\n    }`,\n    bvec2: `\n    if (cv[0] != v[0] || cv[1] != v[1])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n\n        gl.uniform2i(location, v[0], v[1]);\n    }`,\n    bvec3: `\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n\n        gl.uniform3i(location, v[0], v[1], v[2]);\n    }`,\n    bvec4: `\n    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])\n    {\n        cv[0] = v[0];\n        cv[1] = v[1];\n        cv[2] = v[2];\n        cv[3] = v[3];\n\n        gl.uniform4i(location, v[0], v[1], v[2], v[3]);\n    }`,\n\n    mat2:     'gl.uniformMatrix2fv(location, false, v)',\n    mat3:     'gl.uniformMatrix3fv(location, false, v)',\n    mat4:     'gl.uniformMatrix4fv(location, false, v)',\n\n    sampler2D: `\n    if (cv !== v)\n    {\n        cu.value = v;\n\n        gl.uniform1i(location, v);\n    }`,\n    samplerCube: `\n    if (cv !== v)\n    {\n        cu.value = v;\n\n        gl.uniform1i(location, v);\n    }`,\n    sampler2DArray: `\n    if (cv !== v)\n    {\n        cu.value = v;\n\n        gl.uniform1i(location, v);\n    }`,\n};\n\nconst GLSL_TO_ARRAY_SETTERS: Dict<string> = {\n\n    float:    `gl.uniform1fv(location, v)`,\n\n    vec2:     `gl.uniform2fv(location, v)`,\n    vec3:     `gl.uniform3fv(location, v)`,\n    vec4:     'gl.uniform4fv(location, v)',\n\n    mat4:     'gl.uniformMatrix4fv(location, false, v)',\n    mat3:     'gl.uniformMatrix3fv(location, false, v)',\n    mat2:     'gl.uniformMatrix2fv(location, false, v)',\n\n    int:      'gl.uniform1iv(location, v)',\n    ivec2:    'gl.uniform2iv(location, v)',\n    ivec3:    'gl.uniform3iv(location, v)',\n    ivec4:    'gl.uniform4iv(location, v)',\n\n    uint:     'gl.uniform1uiv(location, v)',\n    uvec2:    'gl.uniform2uiv(location, v)',\n    uvec3:    'gl.uniform3uiv(location, v)',\n    uvec4:    'gl.uniform4uiv(location, v)',\n\n    bool:     'gl.uniform1iv(location, v)',\n    bvec2:    'gl.uniform2iv(location, v)',\n    bvec3:    'gl.uniform3iv(location, v)',\n    bvec4:    'gl.uniform4iv(location, v)',\n\n    sampler2D:      'gl.uniform1iv(location, v)',\n    samplerCube:    'gl.uniform1iv(location, v)',\n    sampler2DArray: 'gl.uniform1iv(location, v)',\n};\n\nexport function generateUniformsSync(group: UniformGroup, uniformData: Dict<any>): UniformsSyncCallback\n{\n    const funcFragments = [`\n        var v = null;\n        var cv = null;\n        var cu = null;\n        var t = 0;\n        var gl = renderer.gl;\n    `];\n\n    for (const i in group.uniforms)\n    {\n        const data = uniformData[i];\n\n        if (!data)\n        {\n            if (group.uniforms[i]?.group === true) // strict check to desambiguate from Array.group\n            {\n                if (group.uniforms[i].ubo)\n                {\n                    funcFragments.push(`\n                        renderer.shader.syncUniformBufferGroup(uv.${i}, '${i}');\n                    `);\n                }\n                else\n                {\n                    funcFragments.push(`\n                        renderer.shader.syncUniformGroup(uv.${i}, syncData);\n                    `);\n                }\n            }\n\n            continue;\n        }\n\n        const uniform = group.uniforms[i];\n\n        let parsed = false;\n\n        for (let j = 0; j < uniformParsers.length; j++)\n        {\n            if (uniformParsers[j].test(data, uniform))\n            {\n                funcFragments.push(uniformParsers[j].code(i, uniform));\n                parsed = true;\n\n                break;\n            }\n        }\n\n        if (!parsed)\n        {\n            const templateType = data.size === 1 && !data.isArray ? GLSL_TO_SINGLE_SETTERS_CACHED : GLSL_TO_ARRAY_SETTERS;\n            const template = templateType[data.type].replace('location', `ud[\"${i}\"].location`);\n\n            funcFragments.push(`\n            cu = ud[\"${i}\"];\n            cv = cu.value;\n            v = uv[\"${i}\"];\n            ${template};`);\n        }\n    }\n\n    /*\n     * the introduction of syncData is to solve an issue where textures in uniform groups are not set correctly\n     * the texture count was always starting from 0 in each group. This needs to increment each time a texture is used\n     * no matter which group is being used\n     *\n     */\n    // eslint-disable-next-line no-new-func\n    return new Function('ud', 'uv', 'renderer', 'syncData', funcFragments.join('\\n')) as UniformsSyncCallback;\n}\n"], "mappings": ";AAaA,MAAMA,6BAAA,GAA8C;IAEhDC,KAAA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;IAOPC,IAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IASNC,IAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAUNC,IAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAWNC,GAAA,EAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAOLC,KAAA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAQPC,KAAA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IASPC,KAAA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAWPC,IAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAONC,KAAA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAQPC,KAAA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IASPC,KAAA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAWPC,IAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA;IAMNC,KAAA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAQPC,KAAA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IASPC,KAAA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAWPC,IAAA,EAAU;IACVC,IAAA,EAAU;IACVC,IAAA,EAAU;IAEVC,SAAA,EAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAOXC,WAAA,EAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAObC,cAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOpB;EAEMC,qBAAA,GAAsC;IAExCtB,KAAA,EAAU;IAEVC,IAAA,EAAU;IACVC,IAAA,EAAU;IACVC,IAAA,EAAU;IAEVe,IAAA,EAAU;IACVD,IAAA,EAAU;IACVD,IAAA,EAAU;IAEVZ,GAAA,EAAU;IACVC,KAAA,EAAU;IACVC,KAAA,EAAU;IACVC,KAAA,EAAU;IAEVC,IAAA,EAAU;IACVC,KAAA,EAAU;IACVC,KAAA,EAAU;IACVC,KAAA,EAAU;IAEVC,IAAA,EAAU;IACVC,KAAA,EAAU;IACVC,KAAA,EAAU;IACVC,KAAA,EAAU;IAEVI,SAAA,EAAgB;IAChBC,WAAA,EAAgB;IAChBC,cAAA,EAAgB;EACpB;AAEgB,SAAAE,qBAAqBC,KAAA,EAAqBC,WAAA,EAC1D;EACI,MAAMC,aAAA,GAAgB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAMtB;EAEU,WAAAC,CAAA,IAAKH,KAAA,CAAMI,QAAA,EACtB;IACU,MAAAC,IAAA,GAAOJ,WAAA,CAAYE,CAAC;IAE1B,IAAI,CAACE,IAAA,EACL;MACQL,KAAA,CAAMI,QAAA,CAASD,CAAC,GAAGH,KAAA,KAAU,OAEzBA,KAAA,CAAMI,QAAA,CAASD,CAAC,EAAEG,GAAA,GAElBJ,aAAA,CAAcK,IAAA,CAAK;AAAA,oEAC6BJ,CAAC,MAAMA,CAAC;AAAA,qBACvD,IAIDD,aAAA,CAAcK,IAAA,CAAK;AAAA,8DACuBJ,CAAC;AAAA,qBAC1C;MAIT;IACJ;IAEM,MAAAK,OAAA,GAAUR,KAAA,CAAMI,QAAA,CAASD,CAAC;IAEhC,IAAIM,MAAA,GAAS;IAEb,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIC,cAAA,CAAeC,MAAA,EAAQF,CAAA,IAEvC,IAAIC,cAAA,CAAeD,CAAC,EAAEG,IAAA,CAAKR,IAAA,EAAMG,OAAO,GACxC;MACkBN,aAAA,CAAAK,IAAA,CAAKI,cAAA,CAAeD,CAAC,EAAEI,IAAA,CAAKX,CAAA,EAAGK,OAAO,CAAC,GACrDC,MAAA,GAAS;MAET;IACJ;IAGJ,IAAI,CAACA,MAAA,EACL;MAEI,MAAMM,QAAA,IADeV,IAAA,CAAKW,IAAA,KAAS,KAAK,CAACX,IAAA,CAAKY,OAAA,GAAU1C,6BAAA,GAAgCuB,qBAAA,EAC1DO,IAAA,CAAKa,IAAI,EAAEC,OAAA,CAAQ,YAAY,OAAOhB,CAAC,aAAa;MAElFD,aAAA,CAAcK,IAAA,CAAK;AAAA,uBACRJ,CAAC;AAAA;AAAA,sBAEFA,CAAC;AAAA,cACTY,QAAQ,GAAG;IACjB;EACJ;EASA,OAAO,IAAIK,QAAA,CAAS,MAAM,MAAM,YAAY,YAAYlB,aAAA,CAAcmB,IAAA,CAAK;AAAA,CAAI,CAAC;AACpF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}