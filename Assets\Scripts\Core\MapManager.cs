using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;

public class MapManager : MonoBehaviour
{
    [Header("Map Configuration")]
    public ProvinceData provinceData;
    public GameObject provincePrefab;
    public Transform mapParent;

    [Header("API Configuration")]
    public string backendUrl = "http://localhost:8000";

    [Header("Map Settings")]
    public float provinceSpacing = 2f;
    public int provincesPerRow = 10;

    private List<GameObject> provinceObjects = new List<GameObject>();
    private List<Province> loadedProvinces = new List<Province>();

    void Start()
    {
        StartCoroutine(LoadMapFromAPI());
    }

    IEnumerator LoadMapFromAPI()
    {
        Debug.Log("Loading provinces from backend API...");

        using (UnityWebRequest request = UnityWebRequest.Get($"{backendUrl}/provinces"))
        {
            yield return request.SendWebRequest();

            if (request.result == UnityWebRequest.Result.Success)
            {
                try
                {
                    string jsonResponse = request.downloadHandler.text;
                    Debug.Log($"Received provinces data: {jsonResponse}");

                    // Parse JSON response (simplified - in real project use JsonUtility or Newtonsoft.Json)
                    // For now, fall back to ScriptableObject data
                    LoadMapFromScriptableObject();
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to parse provinces data: {e.Message}");
                    LoadMapFromScriptableObject();
                }
            }
            else
            {
                Debug.LogWarning($"Failed to load provinces from API: {request.error}. Using ScriptableObject data.");
                LoadMapFromScriptableObject();
            }
        }
    }

    void LoadMapFromScriptableObject()
    {
        if (provinceData == null)
        {
            Debug.LogError("ProvinceData is null! Please assign a ProvinceData ScriptableObject.");
            return;
        }

        if (provincePrefab == null)
        {
            Debug.LogError("ProvincePrefab is null! Please assign a province prefab.");
            return;
        }

        if (mapParent == null)
        {
            Debug.LogError("MapParent is null! Please assign a transform for the map parent.");
            return;
        }

        if (provinceData.Provinces == null || provinceData.Provinces.Count == 0)
        {
            Debug.LogWarning("No provinces found in ProvinceData.");
            return;
        }

        Debug.Log($"Loading {provinceData.Provinces.Count} provinces from ScriptableObject...");

        ClearExistingProvinces();

        for (int i = 0; i < provinceData.Provinces.Count; i++)
        {
            var province = provinceData.Provinces[i];
            CreateProvinceObject(province, i);
        }

        Debug.Log($"Successfully loaded {provinceObjects.Count} province objects.");
    }

    void CreateProvinceObject(Province province, int index)
    {
        if (province == null)
        {
            Debug.LogWarning($"Province at index {index} is null, skipping.");
            return;
        }

        var obj = Instantiate(provincePrefab, mapParent);
        obj.name = province.Name ?? $"Province_{index}";

        // Position provinces in a grid layout
        int row = index / provincesPerRow;
        int col = index % provincesPerRow;
        obj.transform.localPosition = new Vector3(col * provinceSpacing, -row * provinceSpacing, 0);

        // Set up province UI if available
        var provinceUI = obj.GetComponent<ProvinceUI>();
        if (provinceUI != null)
        {
            provinceUI.SetProvince(province);
        }

        // Add click handler if available
        var collider = obj.GetComponent<Collider>();
        if (collider != null)
        {
            var clickHandler = obj.GetComponent<ProvinceClickHandler>();
            if (clickHandler == null)
            {
                clickHandler = obj.AddComponent<ProvinceClickHandler>();
            }
            clickHandler.SetProvince(province);
        }

        provinceObjects.Add(obj);
        loadedProvinces.Add(province);
    }

    void ClearExistingProvinces()
    {
        foreach (var obj in provinceObjects)
        {
            if (obj != null)
            {
                DestroyImmediate(obj);
            }
        }
        provinceObjects.Clear();
        loadedProvinces.Clear();
    }

    public Province GetProvinceByName(string name)
    {
        return loadedProvinces.Find(p => p.Name == name);
    }

    public List<Province> GetAllProvinces()
    {
        return new List<Province>(loadedProvinces);
    }

    public void RefreshMap()
    {
        StartCoroutine(LoadMapFromAPI());
    }
}
