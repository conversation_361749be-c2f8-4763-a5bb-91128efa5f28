{"ast": null, "code": "import { ENV, BUFFER_TYPE } from \"@pixi/constants\";\nimport { ExtensionType, extensions } from \"@pixi/extensions\";\nimport { settings } from \"@pixi/settings\";\nconst byteSizeMap = {\n  5126: 4,\n  5123: 2,\n  5121: 1\n};\nclass GeometrySystem {\n  /** @param renderer - The renderer this System works for. */\n  constructor(renderer) {\n    this.renderer = renderer, this._activeGeometry = null, this._activeVao = null, this.hasVao = !0, this.hasInstance = !0, this.canUseUInt32ElementIndex = !1, this.managedGeometries = {};\n  }\n  /** Sets up the renderer context and necessary buffers. */\n  contextChange() {\n    this.disposeAll(!0);\n    const gl = this.gl = this.renderer.gl,\n      context = this.renderer.context;\n    if (this.CONTEXT_UID = this.renderer.CONTEXT_UID, context.webGLVersion !== 2) {\n      let nativeVaoExtension = this.renderer.context.extensions.vertexArrayObject;\n      settings.PREFER_ENV === ENV.WEBGL_LEGACY && (nativeVaoExtension = null), nativeVaoExtension ? (gl.createVertexArray = () => nativeVaoExtension.createVertexArrayOES(), gl.bindVertexArray = vao => nativeVaoExtension.bindVertexArrayOES(vao), gl.deleteVertexArray = vao => nativeVaoExtension.deleteVertexArrayOES(vao)) : (this.hasVao = !1, gl.createVertexArray = () => null, gl.bindVertexArray = () => null, gl.deleteVertexArray = () => null);\n    }\n    if (context.webGLVersion !== 2) {\n      const instanceExt = gl.getExtension(\"ANGLE_instanced_arrays\");\n      instanceExt ? (gl.vertexAttribDivisor = (a, b) => instanceExt.vertexAttribDivisorANGLE(a, b), gl.drawElementsInstanced = (a, b, c, d, e) => instanceExt.drawElementsInstancedANGLE(a, b, c, d, e), gl.drawArraysInstanced = (a, b, c, d) => instanceExt.drawArraysInstancedANGLE(a, b, c, d)) : this.hasInstance = !1;\n    }\n    this.canUseUInt32ElementIndex = context.webGLVersion === 2 || !!context.extensions.uint32ElementIndex;\n  }\n  /**\n   * Binds geometry so that is can be drawn. Creating a Vao if required\n   * @param geometry - Instance of geometry to bind.\n   * @param shader - Instance of shader to use vao for.\n   */\n  bind(geometry, shader) {\n    shader = shader || this.renderer.shader.shader;\n    const {\n      gl\n    } = this;\n    let vaos = geometry.glVertexArrayObjects[this.CONTEXT_UID],\n      incRefCount = !1;\n    vaos || (this.managedGeometries[geometry.id] = geometry, geometry.disposeRunner.add(this), geometry.glVertexArrayObjects[this.CONTEXT_UID] = vaos = {}, incRefCount = !0);\n    const vao = vaos[shader.program.id] || this.initGeometryVao(geometry, shader, incRefCount);\n    this._activeGeometry = geometry, this._activeVao !== vao && (this._activeVao = vao, this.hasVao ? gl.bindVertexArray(vao) : this.activateVao(geometry, shader.program)), this.updateBuffers();\n  }\n  /** Reset and unbind any active VAO and geometry. */\n  reset() {\n    this.unbind();\n  }\n  /** Update buffers of the currently bound geometry. */\n  updateBuffers() {\n    const geometry = this._activeGeometry,\n      bufferSystem = this.renderer.buffer;\n    for (let i = 0; i < geometry.buffers.length; i++) {\n      const buffer = geometry.buffers[i];\n      bufferSystem.update(buffer);\n    }\n  }\n  /**\n   * Check compatibility between a geometry and a program\n   * @param geometry - Geometry instance.\n   * @param program - Program instance.\n   */\n  checkCompatibility(geometry, program) {\n    const geometryAttributes = geometry.attributes,\n      shaderAttributes = program.attributeData;\n    for (const j in shaderAttributes) if (!geometryAttributes[j]) throw new Error(`shader and geometry incompatible, geometry missing the \"${j}\" attribute`);\n  }\n  /**\n   * Takes a geometry and program and generates a unique signature for them.\n   * @param geometry - To get signature from.\n   * @param program - To test geometry against.\n   * @returns - Unique signature of the geometry and program\n   */\n  getSignature(geometry, program) {\n    const attribs = geometry.attributes,\n      shaderAttributes = program.attributeData,\n      strings = [\"g\", geometry.id];\n    for (const i in attribs) shaderAttributes[i] && strings.push(i, shaderAttributes[i].location);\n    return strings.join(\"-\");\n  }\n  /**\n   * Creates or gets Vao with the same structure as the geometry and stores it on the geometry.\n   * If vao is created, it is bound automatically. We use a shader to infer what and how to set up the\n   * attribute locations.\n   * @param geometry - Instance of geometry to to generate Vao for.\n   * @param shader - Instance of the shader.\n   * @param incRefCount - Increment refCount of all geometry buffers.\n   */\n  initGeometryVao(geometry, shader, incRefCount = !0) {\n    const gl = this.gl,\n      CONTEXT_UID = this.CONTEXT_UID,\n      bufferSystem = this.renderer.buffer,\n      program = shader.program;\n    program.glPrograms[CONTEXT_UID] || this.renderer.shader.generateProgram(shader), this.checkCompatibility(geometry, program);\n    const signature = this.getSignature(geometry, program),\n      vaoObjectHash = geometry.glVertexArrayObjects[this.CONTEXT_UID];\n    let vao = vaoObjectHash[signature];\n    if (vao) return vaoObjectHash[program.id] = vao, vao;\n    const buffers = geometry.buffers,\n      attributes = geometry.attributes,\n      tempStride = {},\n      tempStart = {};\n    for (const j in buffers) tempStride[j] = 0, tempStart[j] = 0;\n    for (const j in attributes) !attributes[j].size && program.attributeData[j] ? attributes[j].size = program.attributeData[j].size : attributes[j].size || console.warn(`PIXI Geometry attribute '${j}' size cannot be determined (likely the bound shader does not have the attribute)`), tempStride[attributes[j].buffer] += attributes[j].size * byteSizeMap[attributes[j].type];\n    for (const j in attributes) {\n      const attribute = attributes[j],\n        attribSize = attribute.size;\n      attribute.stride === void 0 && (tempStride[attribute.buffer] === attribSize * byteSizeMap[attribute.type] ? attribute.stride = 0 : attribute.stride = tempStride[attribute.buffer]), attribute.start === void 0 && (attribute.start = tempStart[attribute.buffer], tempStart[attribute.buffer] += attribSize * byteSizeMap[attribute.type]);\n    }\n    vao = gl.createVertexArray(), gl.bindVertexArray(vao);\n    for (let i = 0; i < buffers.length; i++) {\n      const buffer = buffers[i];\n      bufferSystem.bind(buffer), incRefCount && buffer._glBuffers[CONTEXT_UID].refCount++;\n    }\n    return this.activateVao(geometry, program), vaoObjectHash[program.id] = vao, vaoObjectHash[signature] = vao, gl.bindVertexArray(null), bufferSystem.unbind(BUFFER_TYPE.ARRAY_BUFFER), vao;\n  }\n  /**\n   * Disposes geometry.\n   * @param geometry - Geometry with buffers. Only VAO will be disposed\n   * @param [contextLost=false] - If context was lost, we suppress deleteVertexArray\n   */\n  disposeGeometry(geometry, contextLost) {\n    if (!this.managedGeometries[geometry.id]) return;\n    delete this.managedGeometries[geometry.id];\n    const vaos = geometry.glVertexArrayObjects[this.CONTEXT_UID],\n      gl = this.gl,\n      buffers = geometry.buffers,\n      bufferSystem = this.renderer?.buffer;\n    if (geometry.disposeRunner.remove(this), !!vaos) {\n      if (bufferSystem) for (let i = 0; i < buffers.length; i++) {\n        const buf = buffers[i]._glBuffers[this.CONTEXT_UID];\n        buf && (buf.refCount--, buf.refCount === 0 && !contextLost && bufferSystem.dispose(buffers[i], contextLost));\n      }\n      if (!contextLost) {\n        for (const vaoId in vaos) if (vaoId[0] === \"g\") {\n          const vao = vaos[vaoId];\n          this._activeVao === vao && this.unbind(), gl.deleteVertexArray(vao);\n        }\n      }\n      delete geometry.glVertexArrayObjects[this.CONTEXT_UID];\n    }\n  }\n  /**\n   * Dispose all WebGL resources of all managed geometries.\n   * @param [contextLost=false] - If context was lost, we suppress `gl.delete` calls\n   */\n  disposeAll(contextLost) {\n    const all = Object.keys(this.managedGeometries);\n    for (let i = 0; i < all.length; i++) this.disposeGeometry(this.managedGeometries[all[i]], contextLost);\n  }\n  /**\n   * Activate vertex array object.\n   * @param geometry - Geometry instance.\n   * @param program - Shader program instance.\n   */\n  activateVao(geometry, program) {\n    const gl = this.gl,\n      CONTEXT_UID = this.CONTEXT_UID,\n      bufferSystem = this.renderer.buffer,\n      buffers = geometry.buffers,\n      attributes = geometry.attributes;\n    geometry.indexBuffer && bufferSystem.bind(geometry.indexBuffer);\n    let lastBuffer = null;\n    for (const j in attributes) {\n      const attribute = attributes[j],\n        buffer = buffers[attribute.buffer],\n        glBuffer = buffer._glBuffers[CONTEXT_UID];\n      if (program.attributeData[j]) {\n        lastBuffer !== glBuffer && (bufferSystem.bind(buffer), lastBuffer = glBuffer);\n        const location = program.attributeData[j].location;\n        if (gl.enableVertexAttribArray(location), gl.vertexAttribPointer(location, attribute.size, attribute.type || gl.FLOAT, attribute.normalized, attribute.stride, attribute.start), attribute.instance) if (this.hasInstance) gl.vertexAttribDivisor(location, attribute.divisor);else throw new Error(\"geometry error, GPU Instancing is not supported on this device\");\n      }\n    }\n  }\n  /**\n   * Draws the currently bound geometry.\n   * @param type - The type primitive to render.\n   * @param size - The number of elements to be rendered. If not specified, all vertices after the\n   *  starting vertex will be drawn.\n   * @param start - The starting vertex in the geometry to start drawing from. If not specified,\n   *  drawing will start from the first vertex.\n   * @param instanceCount - The number of instances of the set of elements to execute. If not specified,\n   *  all instances will be drawn.\n   */\n  draw(type, size, start, instanceCount) {\n    const {\n        gl\n      } = this,\n      geometry = this._activeGeometry;\n    if (geometry.indexBuffer) {\n      const byteSize = geometry.indexBuffer.data.BYTES_PER_ELEMENT,\n        glType = byteSize === 2 ? gl.UNSIGNED_SHORT : gl.UNSIGNED_INT;\n      byteSize === 2 || byteSize === 4 && this.canUseUInt32ElementIndex ? geometry.instanced ? gl.drawElementsInstanced(type, size || geometry.indexBuffer.data.length, glType, (start || 0) * byteSize, instanceCount || 1) : gl.drawElements(type, size || geometry.indexBuffer.data.length, glType, (start || 0) * byteSize) : console.warn(\"unsupported index buffer type: uint32\");\n    } else geometry.instanced ? gl.drawArraysInstanced(type, start, size || geometry.getSize(), instanceCount || 1) : gl.drawArrays(type, start, size || geometry.getSize());\n    return this;\n  }\n  /** Unbind/reset everything. */\n  unbind() {\n    this.gl.bindVertexArray(null), this._activeVao = null, this._activeGeometry = null;\n  }\n  destroy() {\n    this.renderer = null;\n  }\n}\nGeometrySystem.extension = {\n  type: ExtensionType.RendererSystem,\n  name: \"geometry\"\n};\nextensions.add(GeometrySystem);\nexport { GeometrySystem };", "map": {"version": 3, "names": ["byteSizeMap", "GeometrySystem", "constructor", "renderer", "_activeGeometry", "_activeVao", "<PERSON><PERSON><PERSON>", "hasInstance", "canUseUInt32ElementIndex", "managedGeometries", "contextChange", "disposeAll", "gl", "context", "CONTEXT_UID", "webGLVersion", "nativeVaoExtension", "extensions", "vertexArrayObject", "settings", "PREFER_ENV", "ENV", "WEBGL_LEGACY", "createVertexArray", "createVertexArrayOES", "bindVertexArray", "vao", "bindVertexArrayOES", "deleteVertexArray", "deleteVertexArrayOES", "instanceExt", "getExtension", "vertexAttribDivisor", "a", "b", "vertexAttribDivisorANGLE", "drawElementsInstanced", "c", "d", "e", "drawElementsInstancedANGLE", "drawArraysInstanced", "drawArraysInstancedANGLE", "uint32ElementIndex", "bind", "geometry", "shader", "vaos", "glVertexArrayObjects", "incRefCount", "id", "dispose<PERSON><PERSON><PERSON>", "add", "program", "initGeometryVao", "activateVao", "updateBuffers", "reset", "unbind", "bufferSystem", "buffer", "i", "buffers", "length", "update", "checkCompatibility", "geometryAttributes", "attributes", "shaderAttributes", "attributeData", "j", "Error", "getSignature", "attribs", "strings", "push", "location", "join", "glPrograms", "generateProgram", "signature", "vaoObjectHash", "tempStride", "tempStart", "size", "console", "warn", "type", "attribute", "attribSize", "stride", "start", "_glBuffers", "refCount", "BUFFER_TYPE", "ARRAY_BUFFER", "disposeGeometry", "contextLost", "remove", "buf", "dispose", "vaoId", "all", "Object", "keys", "indexBuffer", "lastBuffer", "gl<PERSON>uffer", "enableVertexAttribArray", "vertexAttribPointer", "FLOAT", "normalized", "instance", "divisor", "draw", "instanceCount", "byteSize", "data", "BYTES_PER_ELEMENT", "glType", "UNSIGNED_SHORT", "UNSIGNED_INT", "instanced", "drawElements", "getSize", "drawArrays", "destroy", "extension", "ExtensionType", "RendererSystem", "name"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\geometry\\GeometrySystem.ts"], "sourcesContent": ["import { BUFFER_TYPE, ENV } from '@pixi/constants';\nimport { extensions, ExtensionType } from '@pixi/extensions';\nimport { settings } from '@pixi/settings';\n\nimport type { DRAW_MODES } from '@pixi/constants';\nimport type { ExtensionMetadata } from '@pixi/extensions';\nimport type { Dict } from '@pixi/utils';\nimport type { IRenderingContext } from '../IRenderer';\nimport type { Renderer } from '../Renderer';\nimport type { Program } from '../shader/Program';\nimport type { Shader } from '../shader/Shader';\nimport type { ISystem } from '../system/ISystem';\nimport type { Geometry } from './Geometry';\nimport type { <PERSON><PERSON><PERSON><PERSON>er } from './GLBuffer';\n\nconst byteSizeMap: {[key: number]: number} = { 5126: 4, 5123: 2, 5121: 1 };\n\n/**\n * System plugin to the renderer to manage geometry.\n * @memberof PIXI\n */\nexport class GeometrySystem implements ISystem\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        type: ExtensionType.RendererSystem,\n        name: 'geometry',\n    };\n\n    /**\n     * `true` if we has `*_vertex_array_object` extension.\n     * @readonly\n     */\n    public hasVao: boolean;\n\n    /**\n     * `true` if has `ANGLE_instanced_arrays` extension.\n     * @readonly\n     */\n    public hasInstance: boolean;\n\n    /**\n     * `true` if support `gl.UNSIGNED_INT` in `gl.drawElements` or `gl.drawElementsInstanced`.\n     * @readonly\n     */\n    public canUseUInt32ElementIndex: boolean;\n\n    protected CONTEXT_UID: number;\n    protected gl: IRenderingContext;\n    protected _activeGeometry: Geometry;\n    protected _activeVao: WebGLVertexArrayObject;\n    protected _boundBuffer: GLBuffer;\n\n    /** Cache for all geometries by id, used in case renderer gets destroyed or for profiling. */\n    readonly managedGeometries: {[key: number]: Geometry};\n\n    /** Renderer that owns this {@link GeometrySystem}. */\n    private renderer: Renderer;\n\n    /** @param renderer - The renderer this System works for. */\n    constructor(renderer: Renderer)\n    {\n        this.renderer = renderer;\n        this._activeGeometry = null;\n        this._activeVao = null;\n\n        this.hasVao = true;\n        this.hasInstance = true;\n        this.canUseUInt32ElementIndex = false;\n        this.managedGeometries = {};\n    }\n\n    /** Sets up the renderer context and necessary buffers. */\n    protected contextChange(): void\n    {\n        this.disposeAll(true);\n\n        const gl = this.gl = this.renderer.gl;\n        const context = this.renderer.context;\n\n        this.CONTEXT_UID = this.renderer.CONTEXT_UID;\n\n        // webgl2\n        if (context.webGLVersion !== 2)\n        {\n            // webgl 1!\n            let nativeVaoExtension = this.renderer.context.extensions.vertexArrayObject;\n\n            if (settings.PREFER_ENV === ENV.WEBGL_LEGACY)\n            {\n                nativeVaoExtension = null;\n            }\n\n            if (nativeVaoExtension)\n            {\n                gl.createVertexArray = (): WebGLVertexArrayObject =>\n                    nativeVaoExtension.createVertexArrayOES();\n\n                gl.bindVertexArray = (vao): void =>\n                    nativeVaoExtension.bindVertexArrayOES(vao);\n\n                gl.deleteVertexArray = (vao): void =>\n                    nativeVaoExtension.deleteVertexArrayOES(vao);\n            }\n            else\n            {\n                this.hasVao = false;\n                gl.createVertexArray = (): WebGLVertexArrayObject =>\n                    null;\n\n                gl.bindVertexArray = (): void =>\n                    null;\n\n                gl.deleteVertexArray = (): void =>\n                    null;\n            }\n        }\n\n        if (context.webGLVersion !== 2)\n        {\n            const instanceExt = gl.getExtension('ANGLE_instanced_arrays');\n\n            if (instanceExt)\n            {\n                gl.vertexAttribDivisor = (a, b): void =>\n                    instanceExt.vertexAttribDivisorANGLE(a, b);\n\n                gl.drawElementsInstanced = (a, b, c, d, e): void =>\n                    instanceExt.drawElementsInstancedANGLE(a, b, c, d, e);\n\n                gl.drawArraysInstanced = (a, b, c, d): void =>\n                    instanceExt.drawArraysInstancedANGLE(a, b, c, d);\n            }\n            else\n            {\n                this.hasInstance = false;\n            }\n        }\n\n        this.canUseUInt32ElementIndex = context.webGLVersion === 2 || !!context.extensions.uint32ElementIndex;\n    }\n\n    /**\n     * Binds geometry so that is can be drawn. Creating a Vao if required\n     * @param geometry - Instance of geometry to bind.\n     * @param shader - Instance of shader to use vao for.\n     */\n    bind(geometry?: Geometry, shader?: Shader): void\n    {\n        shader = shader || this.renderer.shader.shader;\n\n        const { gl } = this;\n\n        // not sure the best way to address this..\n        // currently different shaders require different VAOs for the same geometry\n        // Still mulling over the best way to solve this one..\n        // will likely need to modify the shader attribute locations at run time!\n        let vaos = geometry.glVertexArrayObjects[this.CONTEXT_UID];\n        let incRefCount = false;\n\n        if (!vaos)\n        {\n            this.managedGeometries[geometry.id] = geometry;\n            geometry.disposeRunner.add(this);\n            geometry.glVertexArrayObjects[this.CONTEXT_UID] = vaos = {};\n            incRefCount = true;\n        }\n\n        const vao = vaos[shader.program.id] || this.initGeometryVao(geometry, shader, incRefCount);\n\n        this._activeGeometry = geometry;\n\n        if (this._activeVao !== vao)\n        {\n            this._activeVao = vao;\n\n            if (this.hasVao)\n            {\n                gl.bindVertexArray(vao);\n            }\n            else\n            {\n                this.activateVao(geometry, shader.program);\n            }\n        }\n\n        // TODO - optimise later!\n        // don't need to loop through if nothing changed!\n        // maybe look to add an 'autoupdate' to geometry?\n        this.updateBuffers();\n    }\n\n    /** Reset and unbind any active VAO and geometry. */\n    reset(): void\n    {\n        this.unbind();\n    }\n\n    /** Update buffers of the currently bound geometry. */\n    updateBuffers(): void\n    {\n        const geometry = this._activeGeometry;\n\n        const bufferSystem = this.renderer.buffer;\n\n        for (let i = 0; i < geometry.buffers.length; i++)\n        {\n            const buffer = geometry.buffers[i];\n\n            bufferSystem.update(buffer);\n        }\n    }\n\n    /**\n     * Check compatibility between a geometry and a program\n     * @param geometry - Geometry instance.\n     * @param program - Program instance.\n     */\n    protected checkCompatibility(geometry: Geometry, program: Program): void\n    {\n        // geometry must have at least all the attributes that the shader requires.\n        const geometryAttributes = geometry.attributes;\n        const shaderAttributes = program.attributeData;\n\n        for (const j in shaderAttributes)\n        {\n            if (!geometryAttributes[j])\n            {\n                throw new Error(`shader and geometry incompatible, geometry missing the \"${j}\" attribute`);\n            }\n        }\n    }\n\n    /**\n     * Takes a geometry and program and generates a unique signature for them.\n     * @param geometry - To get signature from.\n     * @param program - To test geometry against.\n     * @returns - Unique signature of the geometry and program\n     */\n    protected getSignature(geometry: Geometry, program: Program): string\n    {\n        const attribs = geometry.attributes;\n        const shaderAttributes = program.attributeData;\n\n        const strings = ['g', geometry.id];\n\n        for (const i in attribs)\n        {\n            if (shaderAttributes[i])\n            {\n                strings.push(i, shaderAttributes[i].location);\n            }\n        }\n\n        return strings.join('-');\n    }\n\n    /**\n     * Creates or gets Vao with the same structure as the geometry and stores it on the geometry.\n     * If vao is created, it is bound automatically. We use a shader to infer what and how to set up the\n     * attribute locations.\n     * @param geometry - Instance of geometry to to generate Vao for.\n     * @param shader - Instance of the shader.\n     * @param incRefCount - Increment refCount of all geometry buffers.\n     */\n    protected initGeometryVao(geometry: Geometry, shader: Shader, incRefCount = true): WebGLVertexArrayObject\n    {\n        const gl = this.gl;\n        const CONTEXT_UID = this.CONTEXT_UID;\n        const bufferSystem = this.renderer.buffer;\n        const program = shader.program;\n\n        if (!program.glPrograms[CONTEXT_UID])\n        {\n            this.renderer.shader.generateProgram(shader);\n        }\n\n        this.checkCompatibility(geometry, program);\n\n        const signature = this.getSignature(geometry, program);\n\n        const vaoObjectHash = geometry.glVertexArrayObjects[this.CONTEXT_UID];\n\n        let vao = vaoObjectHash[signature];\n\n        if (vao)\n        {\n            // this will give us easy access to the vao\n            vaoObjectHash[program.id] = vao;\n\n            return vao;\n        }\n\n        const buffers = geometry.buffers;\n        const attributes = geometry.attributes;\n        const tempStride: Dict<number> = {};\n        const tempStart: Dict<number> = {};\n\n        for (const j in buffers)\n        {\n            tempStride[j] = 0;\n            tempStart[j] = 0;\n        }\n\n        for (const j in attributes)\n        {\n            if (!attributes[j].size && program.attributeData[j])\n            {\n                attributes[j].size = program.attributeData[j].size;\n            }\n            else if (!attributes[j].size)\n            {\n                console.warn(`PIXI Geometry attribute '${j}' size cannot be determined (likely the bound shader does not have the attribute)`);  // eslint-disable-line\n            }\n\n            tempStride[attributes[j].buffer] += attributes[j].size * byteSizeMap[attributes[j].type];\n        }\n\n        for (const j in attributes)\n        {\n            const attribute = attributes[j];\n            const attribSize = attribute.size;\n\n            if (attribute.stride === undefined)\n            {\n                if (tempStride[attribute.buffer] === attribSize * byteSizeMap[attribute.type])\n                {\n                    attribute.stride = 0;\n                }\n                else\n                {\n                    attribute.stride = tempStride[attribute.buffer];\n                }\n            }\n\n            if (attribute.start === undefined)\n            {\n                attribute.start = tempStart[attribute.buffer];\n\n                tempStart[attribute.buffer] += attribSize * byteSizeMap[attribute.type];\n            }\n        }\n\n        // @TODO: We don't know if VAO is supported.\n        vao = gl.createVertexArray();\n\n        gl.bindVertexArray(vao);\n\n        // first update - and create the buffers!\n        // only create a gl buffer if it actually gets\n        for (let i = 0; i < buffers.length; i++)\n        {\n            const buffer = buffers[i];\n\n            bufferSystem.bind(buffer);\n\n            if (incRefCount)\n            {\n                buffer._glBuffers[CONTEXT_UID].refCount++;\n            }\n        }\n\n        // TODO - maybe make this a data object?\n        // lets wait to see if we need to first!\n\n        this.activateVao(geometry, program);\n\n        // add it to the cache!\n        vaoObjectHash[program.id] = vao;\n        vaoObjectHash[signature] = vao;\n\n        gl.bindVertexArray(null);\n        bufferSystem.unbind(BUFFER_TYPE.ARRAY_BUFFER);\n\n        return vao;\n    }\n\n    /**\n     * Disposes geometry.\n     * @param geometry - Geometry with buffers. Only VAO will be disposed\n     * @param [contextLost=false] - If context was lost, we suppress deleteVertexArray\n     */\n    disposeGeometry(geometry: Geometry, contextLost?: boolean): void\n    {\n        if (!this.managedGeometries[geometry.id])\n        {\n            return;\n        }\n\n        delete this.managedGeometries[geometry.id];\n\n        const vaos = geometry.glVertexArrayObjects[this.CONTEXT_UID];\n        const gl = this.gl;\n        const buffers = geometry.buffers;\n        const bufferSystem = this.renderer?.buffer;\n\n        geometry.disposeRunner.remove(this);\n\n        if (!vaos)\n        {\n            return;\n        }\n\n        // bufferSystem may have already been destroyed..\n        // if this is the case, there is no need to destroy the geometry buffers...\n        // they already have been!\n        if (bufferSystem)\n        {\n            for (let i = 0; i < buffers.length; i++)\n            {\n                const buf = buffers[i]._glBuffers[this.CONTEXT_UID];\n\n                // my be null as context may have changed right before the dispose is called\n                if (buf)\n                {\n                    buf.refCount--;\n                    if (buf.refCount === 0 && !contextLost)\n                    {\n                        bufferSystem.dispose(buffers[i], contextLost);\n                    }\n                }\n            }\n        }\n\n        if (!contextLost)\n        {\n            for (const vaoId in vaos)\n            {\n                // delete only signatures, everything else are copies\n                if (vaoId[0] === 'g')\n                {\n                    const vao = vaos[vaoId];\n\n                    if (this._activeVao === vao)\n                    {\n                        this.unbind();\n                    }\n                    gl.deleteVertexArray(vao);\n                }\n            }\n        }\n\n        delete geometry.glVertexArrayObjects[this.CONTEXT_UID];\n    }\n\n    /**\n     * Dispose all WebGL resources of all managed geometries.\n     * @param [contextLost=false] - If context was lost, we suppress `gl.delete` calls\n     */\n    disposeAll(contextLost?: boolean): void\n    {\n        const all: Array<any> = Object.keys(this.managedGeometries);\n\n        for (let i = 0; i < all.length; i++)\n        {\n            this.disposeGeometry(this.managedGeometries[all[i]], contextLost);\n        }\n    }\n\n    /**\n     * Activate vertex array object.\n     * @param geometry - Geometry instance.\n     * @param program - Shader program instance.\n     */\n    protected activateVao(geometry: Geometry, program: Program): void\n    {\n        const gl = this.gl;\n        const CONTEXT_UID = this.CONTEXT_UID;\n        const bufferSystem = this.renderer.buffer;\n        const buffers = geometry.buffers;\n        const attributes = geometry.attributes;\n\n        if (geometry.indexBuffer)\n        {\n            // first update the index buffer if we have one..\n            bufferSystem.bind(geometry.indexBuffer);\n        }\n\n        let lastBuffer = null;\n\n        // add a new one!\n        for (const j in attributes)\n        {\n            const attribute = attributes[j];\n            const buffer = buffers[attribute.buffer];\n            const glBuffer = buffer._glBuffers[CONTEXT_UID];\n\n            if (program.attributeData[j])\n            {\n                if (lastBuffer !== glBuffer)\n                {\n                    bufferSystem.bind(buffer);\n\n                    lastBuffer = glBuffer;\n                }\n\n                const location = program.attributeData[j].location;\n\n                // TODO introduce state again\n                // we can optimise this for older devices that have no VAOs\n                gl.enableVertexAttribArray(location);\n\n                gl.vertexAttribPointer(location,\n                    attribute.size,\n                    attribute.type || gl.FLOAT,\n                    attribute.normalized,\n                    attribute.stride,\n                    attribute.start);\n\n                if (attribute.instance)\n                {\n                    // TODO calculate instance count based of this...\n                    if (this.hasInstance)\n                    {\n                        gl.vertexAttribDivisor(location, attribute.divisor);\n                    }\n                    else\n                    {\n                        throw new Error('geometry error, GPU Instancing is not supported on this device');\n                    }\n                }\n            }\n        }\n    }\n\n    /**\n     * Draws the currently bound geometry.\n     * @param type - The type primitive to render.\n     * @param size - The number of elements to be rendered. If not specified, all vertices after the\n     *  starting vertex will be drawn.\n     * @param start - The starting vertex in the geometry to start drawing from. If not specified,\n     *  drawing will start from the first vertex.\n     * @param instanceCount - The number of instances of the set of elements to execute. If not specified,\n     *  all instances will be drawn.\n     */\n    draw(type: DRAW_MODES, size?: number, start?: number, instanceCount?: number): this\n    {\n        const { gl } = this;\n        const geometry = this._activeGeometry;\n\n        // TODO.. this should not change so maybe cache the function?\n\n        if (geometry.indexBuffer)\n        {\n            const byteSize = geometry.indexBuffer.data.BYTES_PER_ELEMENT;\n            const glType = byteSize === 2 ? gl.UNSIGNED_SHORT : gl.UNSIGNED_INT;\n\n            if (byteSize === 2 || (byteSize === 4 && this.canUseUInt32ElementIndex))\n            {\n                if (geometry.instanced)\n                {\n                    /* eslint-disable max-len */\n                    gl.drawElementsInstanced(type, size || geometry.indexBuffer.data.length, glType, (start || 0) * byteSize, instanceCount || 1);\n                    /* eslint-enable max-len */\n                }\n                else\n                {\n                    /* eslint-disable max-len */\n                    gl.drawElements(type, size || geometry.indexBuffer.data.length, glType, (start || 0) * byteSize);\n                    /* eslint-enable max-len */\n                }\n            }\n            else\n            {\n                console.warn('unsupported index buffer type: uint32');\n            }\n        }\n        else if (geometry.instanced)\n        {\n            // TODO need a better way to calculate size..\n            gl.drawArraysInstanced(type, start, size || geometry.getSize(), instanceCount || 1);\n        }\n        else\n        {\n            gl.drawArrays(type, start, size || geometry.getSize());\n        }\n\n        return this;\n    }\n\n    /** Unbind/reset everything. */\n    protected unbind(): void\n    {\n        this.gl.bindVertexArray(null);\n        this._activeVao = null;\n        this._activeGeometry = null;\n    }\n\n    destroy(): void\n    {\n        this.renderer = null;\n    }\n}\n\nextensions.add(GeometrySystem);\n"], "mappings": ";;;AAeA,MAAMA,WAAA,GAAuC;EAAE,MAAM;EAAG,MAAM;EAAG,MAAM;AAAA;AAMhE,MAAMC,cAAA,CACb;EAAA;EAsCIC,YAAYC,QAAA,EACZ;IACI,KAAKA,QAAA,GAAWA,QAAA,EAChB,KAAKC,eAAA,GAAkB,MACvB,KAAKC,UAAA,GAAa,MAElB,KAAKC,MAAA,GAAS,IACd,KAAKC,WAAA,GAAc,IACnB,KAAKC,wBAAA,GAA2B,IAChC,KAAKC,iBAAA,GAAoB;EAC7B;EAAA;EAGUC,cAAA,EACV;IACI,KAAKC,UAAA,CAAW,EAAI;IAEd,MAAAC,EAAA,GAAK,KAAKA,EAAA,GAAK,KAAKT,QAAA,CAASS,EAAA;MAC7BC,OAAA,GAAU,KAAKV,QAAA,CAASU,OAAA;IAK9B,IAHA,KAAKC,WAAA,GAAc,KAAKX,QAAA,CAASW,WAAA,EAG7BD,OAAA,CAAQE,YAAA,KAAiB,GAC7B;MAEI,IAAIC,kBAAA,GAAqB,KAAKb,QAAA,CAASU,OAAA,CAAQI,UAAA,CAAWC,iBAAA;MAEtDC,QAAA,CAASC,UAAA,KAAeC,GAAA,CAAIC,YAAA,KAE5BN,kBAAA,GAAqB,OAGrBA,kBAAA,IAEAJ,EAAA,CAAGW,iBAAA,GAAoB,MACnBP,kBAAA,CAAmBQ,oBAAA,IAEvBZ,EAAA,CAAGa,eAAA,GAAmBC,GAAA,IAClBV,kBAAA,CAAmBW,kBAAA,CAAmBD,GAAG,GAE7Cd,EAAA,CAAGgB,iBAAA,GAAqBF,GAAA,IACpBV,kBAAA,CAAmBa,oBAAA,CAAqBH,GAAG,MAI/C,KAAKpB,MAAA,GAAS,IACdM,EAAA,CAAGW,iBAAA,GAAoB,MACnB,MAEJX,EAAA,CAAGa,eAAA,GAAkB,MACjB,MAEJb,EAAA,CAAGgB,iBAAA,GAAoB,MACnB;IAEZ;IAEI,IAAAf,OAAA,CAAQE,YAAA,KAAiB,GAC7B;MACU,MAAAe,WAAA,GAAclB,EAAA,CAAGmB,YAAA,CAAa,wBAAwB;MAExDD,WAAA,IAEAlB,EAAA,CAAGoB,mBAAA,GAAsB,CAACC,CAAA,EAAGC,CAAA,KACzBJ,WAAA,CAAYK,wBAAA,CAAyBF,CAAA,EAAGC,CAAC,GAE7CtB,EAAA,CAAGwB,qBAAA,GAAwB,CAACH,CAAA,EAAGC,CAAA,EAAGG,CAAA,EAAGC,CAAA,EAAGC,CAAA,KACpCT,WAAA,CAAYU,0BAAA,CAA2BP,CAAA,EAAGC,CAAA,EAAGG,CAAA,EAAGC,CAAA,EAAGC,CAAC,GAExD3B,EAAA,CAAG6B,mBAAA,GAAsB,CAACR,CAAA,EAAGC,CAAA,EAAGG,CAAA,EAAGC,CAAA,KAC/BR,WAAA,CAAYY,wBAAA,CAAyBT,CAAA,EAAGC,CAAA,EAAGG,CAAA,EAAGC,CAAC,KAInD,KAAK/B,WAAA,GAAc;IAE3B;IAEA,KAAKC,wBAAA,GAA2BK,OAAA,CAAQE,YAAA,KAAiB,KAAK,CAAC,CAACF,OAAA,CAAQI,UAAA,CAAW0B,kBAAA;EACvF;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAC,KAAKC,QAAA,EAAqBC,MAAA,EAC1B;IACaA,MAAA,GAAAA,MAAA,IAAU,KAAK3C,QAAA,CAAS2C,MAAA,CAAOA,MAAA;IAElC;MAAElC;IAAO;IAMf,IAAImC,IAAA,GAAOF,QAAA,CAASG,oBAAA,CAAqB,KAAKlC,WAAW;MACrDmC,WAAA,GAAc;IAEbF,IAAA,KAED,KAAKtC,iBAAA,CAAkBoC,QAAA,CAASK,EAAE,IAAIL,QAAA,EACtCA,QAAA,CAASM,aAAA,CAAcC,GAAA,CAAI,IAAI,GAC/BP,QAAA,CAASG,oBAAA,CAAqB,KAAKlC,WAAW,IAAIiC,IAAA,GAAO,IACzDE,WAAA,GAAc;IAGZ,MAAAvB,GAAA,GAAMqB,IAAA,CAAKD,MAAA,CAAOO,OAAA,CAAQH,EAAE,KAAK,KAAKI,eAAA,CAAgBT,QAAA,EAAUC,MAAA,EAAQG,WAAW;IAEpF,KAAA7C,eAAA,GAAkByC,QAAA,EAEnB,KAAKxC,UAAA,KAAeqB,GAAA,KAEpB,KAAKrB,UAAA,GAAaqB,GAAA,EAEd,KAAKpB,MAAA,GAELM,EAAA,CAAGa,eAAA,CAAgBC,GAAG,IAItB,KAAK6B,WAAA,CAAYV,QAAA,EAAUC,MAAA,CAAOO,OAAO,IAOjD,KAAKG,aAAA,CAAc;EACvB;EAAA;EAGAC,MAAA,EACA;IACI,KAAKC,MAAA,CAAO;EAChB;EAAA;EAGAF,cAAA,EACA;IACI,MAAMX,QAAA,GAAW,KAAKzC,eAAA;MAEhBuD,YAAA,GAAe,KAAKxD,QAAA,CAASyD,MAAA;IAEnC,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIhB,QAAA,CAASiB,OAAA,CAAQC,MAAA,EAAQF,CAAA,IAC7C;MACU,MAAAD,MAAA,GAASf,QAAA,CAASiB,OAAA,CAAQD,CAAC;MAEjCF,YAAA,CAAaK,MAAA,CAAOJ,MAAM;IAC9B;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;EAOUK,mBAAmBpB,QAAA,EAAoBQ,OAAA,EACjD;IAEI,MAAMa,kBAAA,GAAqBrB,QAAA,CAASsB,UAAA;MAC9BC,gBAAA,GAAmBf,OAAA,CAAQgB,aAAA;IAEjC,WAAWC,CAAA,IAAKF,gBAAA,EAER,KAACF,kBAAA,CAAmBI,CAAC,GAErB,MAAM,IAAIC,KAAA,CAAM,2DAA2DD,CAAC,aAAa;EAGrG;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQUE,aAAa3B,QAAA,EAAoBQ,OAAA,EAC3C;IACU,MAAAoB,OAAA,GAAU5B,QAAA,CAASsB,UAAA;MACnBC,gBAAA,GAAmBf,OAAA,CAAQgB,aAAA;MAE3BK,OAAA,GAAU,CAAC,KAAK7B,QAAA,CAASK,EAAE;IAEjC,WAAWW,CAAA,IAAKY,OAAA,EAERL,gBAAA,CAAiBP,CAAC,KAElBa,OAAA,CAAQC,IAAA,CAAKd,CAAA,EAAGO,gBAAA,CAAiBP,CAAC,EAAEe,QAAQ;IAI7C,OAAAF,OAAA,CAAQG,IAAA,CAAK,GAAG;EAC3B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUUvB,gBAAgBT,QAAA,EAAoBC,MAAA,EAAgBG,WAAA,GAAc,IAC5E;IACU,MAAArC,EAAA,GAAK,KAAKA,EAAA;MACVE,WAAA,GAAc,KAAKA,WAAA;MACnB6C,YAAA,GAAe,KAAKxD,QAAA,CAASyD,MAAA;MAC7BP,OAAA,GAAUP,MAAA,CAAOO,OAAA;IAElBA,OAAA,CAAQyB,UAAA,CAAWhE,WAAW,KAE/B,KAAKX,QAAA,CAAS2C,MAAA,CAAOiC,eAAA,CAAgBjC,MAAM,GAG/C,KAAKmB,kBAAA,CAAmBpB,QAAA,EAAUQ,OAAO;IAEnC,MAAA2B,SAAA,GAAY,KAAKR,YAAA,CAAa3B,QAAA,EAAUQ,OAAO;MAE/C4B,aAAA,GAAgBpC,QAAA,CAASG,oBAAA,CAAqB,KAAKlC,WAAW;IAEhE,IAAAY,GAAA,GAAMuD,aAAA,CAAcD,SAAS;IAE7B,IAAAtD,GAAA,EAGc,OAAAuD,aAAA,CAAA5B,OAAA,CAAQH,EAAE,IAAIxB,GAAA,EAErBA,GAAA;IAGL,MAAAoC,OAAA,GAAUjB,QAAA,CAASiB,OAAA;MACnBK,UAAA,GAAatB,QAAA,CAASsB,UAAA;MACtBe,UAAA,GAA2B;MAC3BC,SAAA,GAA0B;IAEhC,WAAWb,CAAA,IAAKR,OAAA,EAEZoB,UAAA,CAAWZ,CAAC,IAAI,GAChBa,SAAA,CAAUb,CAAC,IAAI;IAGnB,WAAWA,CAAA,IAAKH,UAAA,EAER,CAACA,UAAA,CAAWG,CAAC,EAAEc,IAAA,IAAQ/B,OAAA,CAAQgB,aAAA,CAAcC,CAAC,IAE9CH,UAAA,CAAWG,CAAC,EAAEc,IAAA,GAAO/B,OAAA,CAAQgB,aAAA,CAAcC,CAAC,EAAEc,IAAA,GAExCjB,UAAA,CAAWG,CAAC,EAAEc,IAAA,IAEpBC,OAAA,CAAQC,IAAA,CAAK,4BAA4BhB,CAAC,mFAAmF,GAGjIY,UAAA,CAAWf,UAAA,CAAWG,CAAC,EAAEV,MAAM,KAAKO,UAAA,CAAWG,CAAC,EAAEc,IAAA,GAAOpF,WAAA,CAAYmE,UAAA,CAAWG,CAAC,EAAEiB,IAAI;IAG3F,WAAWjB,CAAA,IAAKH,UAAA,EAChB;MACI,MAAMqB,SAAA,GAAYrB,UAAA,CAAWG,CAAC;QACxBmB,UAAA,GAAaD,SAAA,CAAUJ,IAAA;MAEzBI,SAAA,CAAUE,MAAA,KAAW,WAEjBR,UAAA,CAAWM,SAAA,CAAU5B,MAAM,MAAM6B,UAAA,GAAazF,WAAA,CAAYwF,SAAA,CAAUD,IAAI,IAExEC,SAAA,CAAUE,MAAA,GAAS,IAInBF,SAAA,CAAUE,MAAA,GAASR,UAAA,CAAWM,SAAA,CAAU5B,MAAM,IAIlD4B,SAAA,CAAUG,KAAA,KAAU,WAEpBH,SAAA,CAAUG,KAAA,GAAQR,SAAA,CAAUK,SAAA,CAAU5B,MAAM,GAE5CuB,SAAA,CAAUK,SAAA,CAAU5B,MAAM,KAAK6B,UAAA,GAAazF,WAAA,CAAYwF,SAAA,CAAUD,IAAI;IAE9E;IAGA7D,GAAA,GAAMd,EAAA,CAAGW,iBAAA,IAETX,EAAA,CAAGa,eAAA,CAAgBC,GAAG;IAItB,SAASmC,CAAA,GAAI,GAAGA,CAAA,GAAIC,OAAA,CAAQC,MAAA,EAAQF,CAAA,IACpC;MACU,MAAAD,MAAA,GAASE,OAAA,CAAQD,CAAC;MAExBF,YAAA,CAAaf,IAAA,CAAKgB,MAAM,GAEpBX,WAAA,IAEAW,MAAA,CAAOgC,UAAA,CAAW9E,WAAW,EAAE+E,QAAA;IAEvC;IAKK,YAAAtC,WAAA,CAAYV,QAAA,EAAUQ,OAAO,GAGlC4B,aAAA,CAAc5B,OAAA,CAAQH,EAAE,IAAIxB,GAAA,EAC5BuD,aAAA,CAAcD,SAAS,IAAItD,GAAA,EAE3Bd,EAAA,CAAGa,eAAA,CAAgB,IAAI,GACvBkC,YAAA,CAAaD,MAAA,CAAOoC,WAAA,CAAYC,YAAY,GAErCrE,GAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAsE,gBAAgBnD,QAAA,EAAoBoD,WAAA,EACpC;IACI,IAAI,CAAC,KAAKxF,iBAAA,CAAkBoC,QAAA,CAASK,EAAE,GAEnC;IAGG,YAAKzC,iBAAA,CAAkBoC,QAAA,CAASK,EAAE;IAEzC,MAAMH,IAAA,GAAOF,QAAA,CAASG,oBAAA,CAAqB,KAAKlC,WAAW;MACrDF,EAAA,GAAK,KAAKA,EAAA;MACVkD,OAAA,GAAUjB,QAAA,CAASiB,OAAA;MACnBH,YAAA,GAAe,KAAKxD,QAAA,EAAUyD,MAAA;IAIpC,IAFAf,QAAA,CAASM,aAAA,CAAc+C,MAAA,CAAO,IAAI,GAE9B,EAACnD,IAAA,EAQL;MAAI,IAAAY,YAAA,EAEA,SAASE,CAAA,GAAI,GAAGA,CAAA,GAAIC,OAAA,CAAQC,MAAA,EAAQF,CAAA,IACpC;QACI,MAAMsC,GAAA,GAAMrC,OAAA,CAAQD,CAAC,EAAE+B,UAAA,CAAW,KAAK9E,WAAW;QAG9CqF,GAAA,KAEAA,GAAA,CAAIN,QAAA,IACAM,GAAA,CAAIN,QAAA,KAAa,KAAK,CAACI,WAAA,IAEvBtC,YAAA,CAAayC,OAAA,CAAQtC,OAAA,CAAQD,CAAC,GAAGoC,WAAW;MAGxD;MAGJ,IAAI,CAACA,WAAA;QAED,WAAWI,KAAA,IAAStD,IAAA,EAGZ,IAAAsD,KAAA,CAAM,CAAC,MAAM,KACjB;UACU,MAAA3E,GAAA,GAAMqB,IAAA,CAAKsD,KAAK;UAElB,KAAKhG,UAAA,KAAeqB,GAAA,IAEpB,KAAKgC,MAAA,IAET9C,EAAA,CAAGgB,iBAAA,CAAkBF,GAAG;QAC5B;MAAA;MAID,OAAAmB,QAAA,CAASG,oBAAA,CAAqB,KAAKlC,WAAW;IAAA;EACzD;EAAA;AAAA;AAAA;AAAA;EAMAH,WAAWsF,WAAA,EACX;IACI,MAAMK,GAAA,GAAkBC,MAAA,CAAOC,IAAA,CAAK,KAAK/F,iBAAiB;IAE1D,SAASoD,CAAA,GAAI,GAAGA,CAAA,GAAIyC,GAAA,CAAIvC,MAAA,EAAQF,CAAA,IAE5B,KAAKmC,eAAA,CAAgB,KAAKvF,iBAAA,CAAkB6F,GAAA,CAAIzC,CAAC,CAAC,GAAGoC,WAAW;EAExE;EAAA;AAAA;AAAA;AAAA;AAAA;EAOU1C,YAAYV,QAAA,EAAoBQ,OAAA,EAC1C;IACI,MAAMzC,EAAA,GAAK,KAAKA,EAAA;MACVE,WAAA,GAAc,KAAKA,WAAA;MACnB6C,YAAA,GAAe,KAAKxD,QAAA,CAASyD,MAAA;MAC7BE,OAAA,GAAUjB,QAAA,CAASiB,OAAA;MACnBK,UAAA,GAAatB,QAAA,CAASsB,UAAA;IAExBtB,QAAA,CAAS4D,WAAA,IAGT9C,YAAA,CAAaf,IAAA,CAAKC,QAAA,CAAS4D,WAAW;IAG1C,IAAIC,UAAA,GAAa;IAGjB,WAAWpC,CAAA,IAAKH,UAAA,EAChB;MACI,MAAMqB,SAAA,GAAYrB,UAAA,CAAWG,CAAC;QACxBV,MAAA,GAASE,OAAA,CAAQ0B,SAAA,CAAU5B,MAAM;QACjC+C,QAAA,GAAW/C,MAAA,CAAOgC,UAAA,CAAW9E,WAAW;MAE1C,IAAAuC,OAAA,CAAQgB,aAAA,CAAcC,CAAC,GAC3B;QACQoC,UAAA,KAAeC,QAAA,KAEfhD,YAAA,CAAaf,IAAA,CAAKgB,MAAM,GAExB8C,UAAA,GAAaC,QAAA;QAGjB,MAAM/B,QAAA,GAAWvB,OAAA,CAAQgB,aAAA,CAAcC,CAAC,EAAEM,QAAA;QAa1C,IATAhE,EAAA,CAAGgG,uBAAA,CAAwBhC,QAAQ,GAEnChE,EAAA,CAAGiG,mBAAA,CAAoBjC,QAAA,EACnBY,SAAA,CAAUJ,IAAA,EACVI,SAAA,CAAUD,IAAA,IAAQ3E,EAAA,CAAGkG,KAAA,EACrBtB,SAAA,CAAUuB,UAAA,EACVvB,SAAA,CAAUE,MAAA,EACVF,SAAA,CAAUG,KAAA,GAEVH,SAAA,CAAUwB,QAAA,EAGV,IAAI,KAAKzG,WAAA,EAEFK,EAAA,CAAAoB,mBAAA,CAAoB4C,QAAA,EAAUY,SAAA,CAAUyB,OAAO,OAI5C,UAAI1C,KAAA,CAAM,gEAAgE;MAG5F;IACJ;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYA2C,KAAK3B,IAAA,EAAkBH,IAAA,EAAeO,KAAA,EAAgBwB,aAAA,EACtD;IACI,MAAM;QAAEvG;MAAA,IAAO;MACTiC,QAAA,GAAW,KAAKzC,eAAA;IAItB,IAAIyC,QAAA,CAAS4D,WAAA,EACb;MACU,MAAAW,QAAA,GAAWvE,QAAA,CAAS4D,WAAA,CAAYY,IAAA,CAAKC,iBAAA;QACrCC,MAAA,GAASH,QAAA,KAAa,IAAIxG,EAAA,CAAG4G,cAAA,GAAiB5G,EAAA,CAAG6G,YAAA;MAEnDL,QAAA,KAAa,KAAMA,QAAA,KAAa,KAAK,KAAK5G,wBAAA,GAEtCqC,QAAA,CAAS6E,SAAA,GAGT9G,EAAA,CAAGwB,qBAAA,CAAsBmD,IAAA,EAAMH,IAAA,IAAQvC,QAAA,CAAS4D,WAAA,CAAYY,IAAA,CAAKtD,MAAA,EAAQwD,MAAA,GAAS5B,KAAA,IAAS,KAAKyB,QAAA,EAAUD,aAAA,IAAiB,CAAC,IAM5HvG,EAAA,CAAG+G,YAAA,CAAapC,IAAA,EAAMH,IAAA,IAAQvC,QAAA,CAAS4D,WAAA,CAAYY,IAAA,CAAKtD,MAAA,EAAQwD,MAAA,GAAS5B,KAAA,IAAS,KAAKyB,QAAQ,IAMnG/B,OAAA,CAAQC,IAAA,CAAK,uCAAuC;IAE5D,OACSzC,QAAA,CAAS6E,SAAA,GAGd9G,EAAA,CAAG6B,mBAAA,CAAoB8C,IAAA,EAAMI,KAAA,EAAOP,IAAA,IAAQvC,QAAA,CAAS+E,OAAA,CAAQ,GAAGT,aAAA,IAAiB,CAAC,IAIlFvG,EAAA,CAAGiH,UAAA,CAAWtC,IAAA,EAAMI,KAAA,EAAOP,IAAA,IAAQvC,QAAA,CAAS+E,OAAA,EAAS;IAGlD;EACX;EAAA;EAGUlE,OAAA,EACV;IACS,KAAA9C,EAAA,CAAGa,eAAA,CAAgB,IAAI,GAC5B,KAAKpB,UAAA,GAAa,MAClB,KAAKD,eAAA,GAAkB;EAC3B;EAEA0H,QAAA,EACA;IACI,KAAK3H,QAAA,GAAW;EACpB;AACJ;AA3jBaF,cAAA,CAGF8H,SAAA,GAA+B;EAClCxC,IAAA,EAAMyC,aAAA,CAAcC,cAAA;EACpBC,IAAA,EAAM;AACV;AAujBJjH,UAAA,CAAWmC,GAAA,CAAInD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}