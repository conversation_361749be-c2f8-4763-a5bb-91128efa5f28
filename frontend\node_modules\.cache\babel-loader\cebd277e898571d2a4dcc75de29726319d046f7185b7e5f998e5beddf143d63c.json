{"ast": null, "code": "class GraphicsData {\n  /**\n   * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n   * @param fillStyle - the width of the line to draw\n   * @param lineStyle - the color of the line to draw\n   * @param matrix - Transform matrix\n   */\n  constructor(shape, fillStyle = null, lineStyle = null, matrix = null) {\n    this.points = [], this.holes = [], this.shape = shape, this.lineStyle = lineStyle, this.fillStyle = fillStyle, this.matrix = matrix, this.type = shape.type;\n  }\n  /**\n   * Creates a new GraphicsData object with the same values as this one.\n   * @returns - Cloned GraphicsData object\n   */\n  clone() {\n    return new GraphicsData(this.shape, this.fillStyle, this.lineStyle, this.matrix);\n  }\n  /** Destroys the Graphics data. */\n  destroy() {\n    this.shape = null, this.holes.length = 0, this.holes = null, this.points.length = 0, this.points = null, this.lineStyle = null, this.fillStyle = null;\n  }\n}\nexport { GraphicsData };", "map": {"version": 3, "names": ["GraphicsData", "constructor", "shape", "fillStyle", "lineStyle", "matrix", "points", "holes", "type", "clone", "destroy", "length"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\graphics\\src\\GraphicsData.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON>, <PERSON>, SHAPES } from '@pixi/core';\nimport type { FillStyle } from './styles/FillStyle';\nimport type { LineStyle } from './styles/LineStyle';\n\n/**\n * A class to contain data useful for Graphics objects\n * @memberof PIXI\n */\nexport class GraphicsData\n{\n    /**\n     * The shape object to draw.\n     * @member {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle}\n     */\n    shape: IShape;\n\n    /** The style of the line. */\n    lineStyle: LineStyle;\n\n    /** The style of the fill. */\n    fillStyle: FillStyle;\n\n    /** The transform matrix. */\n    matrix: Matrix;\n\n    /** The type of the shape, see the Const.Shapes file for all the existing types, */\n    type: SHAPES;\n\n    /** The collection of points. */\n    points: number[] = [];\n\n    /** The collection of holes. */\n\n    holes: Array<GraphicsData> = [];\n\n    /**\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - The shape object to draw.\n     * @param fillStyle - the width of the line to draw\n     * @param lineStyle - the color of the line to draw\n     * @param matrix - Transform matrix\n     */\n    constructor(shape: IShape, fillStyle: FillStyle = null, lineStyle: LineStyle = null, matrix: Matrix = null)\n    {\n        this.shape = shape;\n        this.lineStyle = lineStyle;\n        this.fillStyle = fillStyle;\n        this.matrix = matrix;\n        this.type = shape.type;\n    }\n\n    /**\n     * Creates a new GraphicsData object with the same values as this one.\n     * @returns - Cloned GraphicsData object\n     */\n    public clone(): GraphicsData\n    {\n        return new GraphicsData(\n            this.shape,\n            this.fillStyle,\n            this.lineStyle,\n            this.matrix\n        );\n    }\n\n    /** Destroys the Graphics data. */\n    public destroy(): void\n    {\n        this.shape = null;\n        this.holes.length = 0;\n        this.holes = null;\n        this.points.length = 0;\n        this.points = null;\n        this.lineStyle = null;\n        this.fillStyle = null;\n    }\n}\n"], "mappings": "AAQO,MAAMA,YAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAgCIC,YAAYC,KAAA,EAAeC,SAAA,GAAuB,MAAMC,SAAA,GAAuB,MAAMC,MAAA,GAAiB,MACtG;IAbA,KAAAC,MAAA,GAAmB,IAInB,KAAAC,KAAA,GAA6B,IAUzB,KAAKL,KAAA,GAAQA,KAAA,EACb,KAAKE,SAAA,GAAYA,SAAA,EACjB,KAAKD,SAAA,GAAYA,SAAA,EACjB,KAAKE,MAAA,GAASA,MAAA,EACd,KAAKG,IAAA,GAAON,KAAA,CAAMM,IAAA;EACtB;EAAA;AAAA;AAAA;AAAA;EAMOC,MAAA,EACP;IACI,OAAO,IAAIT,YAAA,CACP,KAAKE,KAAA,EACL,KAAKC,SAAA,EACL,KAAKC,SAAA,EACL,KAAKC,MAAA;EAEb;EAAA;EAGOK,QAAA,EACP;IACS,KAAAR,KAAA,GAAQ,MACb,KAAKK,KAAA,CAAMI,MAAA,GAAS,GACpB,KAAKJ,KAAA,GAAQ,MACb,KAAKD,MAAA,CAAOK,MAAA,GAAS,GACrB,KAAKL,MAAA,GAAS,MACd,KAAKF,SAAA,GAAY,MACjB,KAAKD,SAAA,GAAY;EACrB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}