{"ast": null, "code": "import { ExtensionType, settings, extensions } from \"@pixi/core\";\nimport { checkDataUrl } from \"../../utils/checkDataUrl.mjs\";\nimport { checkExtension } from \"../../utils/checkExtension.mjs\";\nimport { LoaderParserPriority } from \"./LoaderParser.mjs\";\nconst validTXTExtension = \".txt\",\n  validTXTMIME = \"text/plain\",\n  loadTxt = {\n    name: \"loadTxt\",\n    extension: {\n      type: ExtensionType.LoadParser,\n      priority: LoaderParserPriority.Low\n    },\n    test(url) {\n      return checkDataUrl(url, validTXTMIME) || checkExtension(url, validTXTExtension);\n    },\n    async load(url) {\n      return await (await settings.ADAPTER.fetch(url)).text();\n    }\n  };\nextensions.add(loadTxt);\nexport { loadTxt };", "map": {"version": 3, "names": ["validTXTExtension", "validTXTMIME", "loadTxt", "name", "extension", "type", "ExtensionType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "priority", "LoaderParserPriority", "Low", "test", "url", "checkDataUrl", "checkExtension", "load", "settings", "ADAPTER", "fetch", "text", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\assets\\src\\loader\\parsers\\loadTxt.ts"], "sourcesContent": ["import { extensions, ExtensionType, settings } from '@pixi/core';\nimport { checkDataUrl } from '../../utils/checkDataUrl';\nimport { checkExtension } from '../../utils/checkExtension';\nimport { LoaderParserPriority } from './LoaderParser';\n\nimport type { LoaderParser } from './LoaderParser';\n\nconst validTXTExtension = '.txt';\nconst validTXTMIME = 'text/plain';\n\n/** Simple loader plugin for loading text data */\nexport const loadTxt = {\n\n    name: 'loadTxt',\n\n    extension: {\n        type: ExtensionType.LoadParser,\n        priority: LoaderParserPriority.Low,\n    },\n\n    test(url: string): boolean\n    {\n        return checkDataUrl(url, validTXTMIME) || checkExtension(url, validTXTExtension);\n    },\n\n    async load(url: string): Promise<string>\n    {\n        const response = await settings.ADAPTER.fetch(url);\n\n        const txt = await response.text();\n\n        return txt;\n    },\n} as LoaderParser;\n\nextensions.add(loadTxt);\n"], "mappings": ";;;;AAOA,MAAMA,iBAAA,GAAoB;EACpBC,YAAA,GAAe;EAGRC,OAAA,GAAU;IAEnBC,IAAA,EAAM;IAENC,SAAA,EAAW;MACPC,IAAA,EAAMC,aAAA,CAAcC,UAAA;MACpBC,QAAA,EAAUC,oBAAA,CAAqBC;IACnC;IAEAC,KAAKC,GAAA,EACL;MACI,OAAOC,YAAA,CAAaD,GAAA,EAAKX,YAAY,KAAKa,cAAA,CAAeF,GAAA,EAAKZ,iBAAiB;IACnF;IAEA,MAAMe,KAAKH,GAAA,EACX;MAKI,OAFY,OAFK,MAAMI,QAAA,CAASC,OAAA,CAAQC,KAAA,CAAMN,GAAG,GAEtBO,IAAA;IAG/B;EACJ;AAEAC,UAAA,CAAWC,GAAA,CAAInB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}