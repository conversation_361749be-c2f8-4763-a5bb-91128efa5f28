{"ast": null, "code": "let nextUid = 0;\nfunction uid() {\n  return ++nextUid;\n}\nexport { uid };", "map": {"version": 3, "names": ["nextUid", "uid"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\utils\\src\\data\\uid.ts"], "sourcesContent": ["let nextUid = 0;\n\n/**\n * Gets the next unique identifier\n * @memberof PIXI.utils\n * @function uid\n * @returns {number} The next unique identifier to use.\n */\nexport function uid(): number\n{\n    return ++nextUid;\n}\n"], "mappings": "AAAA,IAAIA,OAAA,GAAU;AAQP,SAASC,IAAA,EAChB;EACI,OAAO,EAAED,OAAA;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}