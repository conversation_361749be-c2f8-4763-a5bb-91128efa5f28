{"ast": null, "code": "import { isMobile } from \"@pixi/settings\";\nfunction canUploadSameBuffer() {\n  return !isMobile.apple.device;\n}\nexport { canUploadSameBuffer };", "map": {"version": 3, "names": ["canUploadSameBuffer", "isMobile", "apple", "device"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\batch\\canUploadSameBuffer.ts"], "sourcesContent": ["import { isMobile } from '@pixi/settings';\n\n/**\n * Uploading the same buffer multiple times in a single frame can cause performance issues.\n * Apparent on iOS so only check for that at the moment\n * This check may become more complex if this issue pops up elsewhere.\n * @private\n * @returns {boolean} `true` if the same buffer may be uploaded more than once.\n */\nexport function canUploadSameBuffer(): boolean\n{\n    return !isMobile.apple.device;\n}\n"], "mappings": ";AASO,SAASA,oBAAA,EAChB;EACW,QAACC,QAAA,CAASC,KAAA,CAAMC,MAAA;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}