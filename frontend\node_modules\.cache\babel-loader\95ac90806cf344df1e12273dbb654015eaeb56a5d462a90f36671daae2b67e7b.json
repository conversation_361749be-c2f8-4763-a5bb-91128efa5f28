{"ast": null, "code": "import { Runner } from \"@pixi/runner\";\nclass TransformFeedback {\n  constructor() {\n    this._glTransformFeedbacks = {}, this.buffers = [], this.disposeRunner = new Runner(\"disposeTransformFeedback\");\n  }\n  /**\n   * Bind buffer to TransformFeedback\n   * @param index - index to bind\n   * @param buffer - buffer to bind\n   */\n  bindBuffer(index, buffer) {\n    this.buffers[index] = buffer;\n  }\n  /** Destroy WebGL resources that are connected to this TransformFeedback. */\n  destroy() {\n    this.disposeRunner.emit(this, !1);\n  }\n}\nexport { TransformFeedback };", "map": {"version": 3, "names": ["TransformFeedback", "constructor", "_glTransformFeedbacks", "buffers", "dispose<PERSON><PERSON><PERSON>", "Runner", "<PERSON><PERSON><PERSON><PERSON>", "index", "buffer", "destroy", "emit"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\transformFeedback\\TransformFeedback.ts"], "sourcesContent": ["import { Runner } from '@pixi/runner';\n\nimport type { <PERSON>uff<PERSON> } from '../geometry/Buffer';\n\n/**\n * A TransformFeedback object wrapping GLTransformFeedback object.\n *\n * For example you can use TransformFeedback object to feed-back buffer data from Shader having TransformFeedbackVaryings.\n * @memberof PIXI\n */\nexport class TransformFeedback\n{\n    _glTransformFeedbacks: {[key: number]: WebGLTransformFeedback};\n\n    buffers: Buffer[];\n\n    disposeRunner: Runner;\n\n    constructor()\n    {\n        this._glTransformFeedbacks = {};\n        this.buffers = [];\n        this.disposeRunner = new Runner('disposeTransformFeedback');\n    }\n\n    /**\n     * Bind buffer to TransformFeedback\n     * @param index - index to bind\n     * @param buffer - buffer to bind\n     */\n    bindBuffer(index: number, buffer: Buffer)\n    {\n        this.buffers[index] = buffer;\n    }\n\n    /** Destroy WebGL resources that are connected to this TransformFeedback. */\n    destroy(): void\n    {\n        this.disposeRunner.emit(this, false);\n    }\n}\n"], "mappings": ";AAUO,MAAMA,iBAAA,CACb;EAOIC,YAAA,EACA;IACS,KAAAC,qBAAA,GAAwB,IAC7B,KAAKC,OAAA,GAAU,IACf,KAAKC,aAAA,GAAgB,IAAIC,MAAA,CAAO,0BAA0B;EAC9D;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAC,WAAWC,KAAA,EAAeC,MAAA,EAC1B;IACS,KAAAL,OAAA,CAAQI,KAAK,IAAIC,MAAA;EAC1B;EAAA;EAGAC,QAAA,EACA;IACS,KAAAL,aAAA,CAAcM,IAAA,CAAK,MAAM,EAAK;EACvC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}