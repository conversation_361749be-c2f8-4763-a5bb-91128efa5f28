{"ast": null, "code": "function splitTextToCharacters(text) {\n  return Array.from ? Array.from(text) : text.split(\"\");\n}\nexport { splitTextToCharacters };", "map": {"version": 3, "names": ["splitTextToCharacters", "text", "Array", "from", "split"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\text-bitmap\\src\\utils\\splitTextToCharacters.ts"], "sourcesContent": ["/**\n * Ponyfill for IE because it doesn't support `Array.from`\n * @param text\n * @private\n */\nexport function splitTextToCharacters(text: string): string[]\n{\n    return Array.from ? Array.from(text) : text.split('');\n}\n"], "mappings": "AAKO,SAASA,sBAAsBC,IAAA,EACtC;EACW,OAAAC,KAAA,CAAMC,IAAA,GAAOD,KAAA,CAAMC,IAAA,CAAKF,IAAI,IAAIA,IAAA,CAAKG,KAAA,CAAM,EAAE;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}