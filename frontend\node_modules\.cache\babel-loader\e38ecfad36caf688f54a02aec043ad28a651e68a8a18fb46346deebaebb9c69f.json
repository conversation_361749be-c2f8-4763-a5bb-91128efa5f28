{"ast": null, "code": "import { Rectangle } from \"@pixi/core\";\nclass Bounds {\n  constructor() {\n    this.minX = 1 / 0, this.minY = 1 / 0, this.maxX = -1 / 0, this.maxY = -1 / 0, this.rect = null, this.updateID = -1;\n  }\n  /**\n   * Checks if bounds are empty.\n   * @returns - True if empty.\n   */\n  isEmpty() {\n    return this.minX > this.maxX || this.minY > this.maxY;\n  }\n  /** Clears the bounds and resets. */\n  clear() {\n    this.minX = 1 / 0, this.minY = 1 / 0, this.maxX = -1 / 0, this.maxY = -1 / 0;\n  }\n  /**\n   * Can return Rectangle.EMPTY constant, either construct new rectangle, either use your rectangle\n   * It is not guaranteed that it will return tempRect\n   * @param rect - Temporary object will be used if AABB is not empty\n   * @returns - A rectangle of the bounds\n   */\n  getRectangle(rect) {\n    return this.minX > this.maxX || this.minY > this.maxY ? Rectangle.EMPTY : (rect = rect || new Rectangle(0, 0, 1, 1), rect.x = this.minX, rect.y = this.minY, rect.width = this.maxX - this.minX, rect.height = this.maxY - this.minY, rect);\n  }\n  /**\n   * This function should be inlined when its possible.\n   * @param point - The point to add.\n   */\n  addPoint(point) {\n    this.minX = Math.min(this.minX, point.x), this.maxX = Math.max(this.maxX, point.x), this.minY = Math.min(this.minY, point.y), this.maxY = Math.max(this.maxY, point.y);\n  }\n  /**\n   * Adds a point, after transformed. This should be inlined when its possible.\n   * @param matrix\n   * @param point\n   */\n  addPointMatrix(matrix, point) {\n    const {\n        a,\n        b,\n        c,\n        d,\n        tx,\n        ty\n      } = matrix,\n      x = a * point.x + c * point.y + tx,\n      y = b * point.x + d * point.y + ty;\n    this.minX = Math.min(this.minX, x), this.maxX = Math.max(this.maxX, x), this.minY = Math.min(this.minY, y), this.maxY = Math.max(this.maxY, y);\n  }\n  /**\n   * Adds a quad, not transformed\n   * @param vertices - The verts to add.\n   */\n  addQuad(vertices) {\n    let minX = this.minX,\n      minY = this.minY,\n      maxX = this.maxX,\n      maxY = this.maxY,\n      x = vertices[0],\n      y = vertices[1];\n    minX = x < minX ? x : minX, minY = y < minY ? y : minY, maxX = x > maxX ? x : maxX, maxY = y > maxY ? y : maxY, x = vertices[2], y = vertices[3], minX = x < minX ? x : minX, minY = y < minY ? y : minY, maxX = x > maxX ? x : maxX, maxY = y > maxY ? y : maxY, x = vertices[4], y = vertices[5], minX = x < minX ? x : minX, minY = y < minY ? y : minY, maxX = x > maxX ? x : maxX, maxY = y > maxY ? y : maxY, x = vertices[6], y = vertices[7], minX = x < minX ? x : minX, minY = y < minY ? y : minY, maxX = x > maxX ? x : maxX, maxY = y > maxY ? y : maxY, this.minX = minX, this.minY = minY, this.maxX = maxX, this.maxY = maxY;\n  }\n  /**\n   * Adds sprite frame, transformed.\n   * @param transform - transform to apply\n   * @param x0 - left X of frame\n   * @param y0 - top Y of frame\n   * @param x1 - right X of frame\n   * @param y1 - bottom Y of frame\n   */\n  addFrame(transform, x0, y0, x1, y1) {\n    this.addFrameMatrix(transform.worldTransform, x0, y0, x1, y1);\n  }\n  /**\n   * Adds sprite frame, multiplied by matrix\n   * @param matrix - matrix to apply\n   * @param x0 - left X of frame\n   * @param y0 - top Y of frame\n   * @param x1 - right X of frame\n   * @param y1 - bottom Y of frame\n   */\n  addFrameMatrix(matrix, x0, y0, x1, y1) {\n    const a = matrix.a,\n      b = matrix.b,\n      c = matrix.c,\n      d = matrix.d,\n      tx = matrix.tx,\n      ty = matrix.ty;\n    let minX = this.minX,\n      minY = this.minY,\n      maxX = this.maxX,\n      maxY = this.maxY,\n      x = a * x0 + c * y0 + tx,\n      y = b * x0 + d * y0 + ty;\n    minX = x < minX ? x : minX, minY = y < minY ? y : minY, maxX = x > maxX ? x : maxX, maxY = y > maxY ? y : maxY, x = a * x1 + c * y0 + tx, y = b * x1 + d * y0 + ty, minX = x < minX ? x : minX, minY = y < minY ? y : minY, maxX = x > maxX ? x : maxX, maxY = y > maxY ? y : maxY, x = a * x0 + c * y1 + tx, y = b * x0 + d * y1 + ty, minX = x < minX ? x : minX, minY = y < minY ? y : minY, maxX = x > maxX ? x : maxX, maxY = y > maxY ? y : maxY, x = a * x1 + c * y1 + tx, y = b * x1 + d * y1 + ty, minX = x < minX ? x : minX, minY = y < minY ? y : minY, maxX = x > maxX ? x : maxX, maxY = y > maxY ? y : maxY, this.minX = minX, this.minY = minY, this.maxX = maxX, this.maxY = maxY;\n  }\n  /**\n   * Adds screen vertices from array\n   * @param vertexData - calculated vertices\n   * @param beginOffset - begin offset\n   * @param endOffset - end offset, excluded\n   */\n  addVertexData(vertexData, beginOffset, endOffset) {\n    let minX = this.minX,\n      minY = this.minY,\n      maxX = this.maxX,\n      maxY = this.maxY;\n    for (let i = beginOffset; i < endOffset; i += 2) {\n      const x = vertexData[i],\n        y = vertexData[i + 1];\n      minX = x < minX ? x : minX, minY = y < minY ? y : minY, maxX = x > maxX ? x : maxX, maxY = y > maxY ? y : maxY;\n    }\n    this.minX = minX, this.minY = minY, this.maxX = maxX, this.maxY = maxY;\n  }\n  /**\n   * Add an array of mesh vertices\n   * @param transform - mesh transform\n   * @param vertices - mesh coordinates in array\n   * @param beginOffset - begin offset\n   * @param endOffset - end offset, excluded\n   */\n  addVertices(transform, vertices, beginOffset, endOffset) {\n    this.addVerticesMatrix(transform.worldTransform, vertices, beginOffset, endOffset);\n  }\n  /**\n   * Add an array of mesh vertices.\n   * @param matrix - mesh matrix\n   * @param vertices - mesh coordinates in array\n   * @param beginOffset - begin offset\n   * @param endOffset - end offset, excluded\n   * @param padX - x padding\n   * @param padY - y padding\n   */\n  addVerticesMatrix(matrix, vertices, beginOffset, endOffset, padX = 0, padY = padX) {\n    const a = matrix.a,\n      b = matrix.b,\n      c = matrix.c,\n      d = matrix.d,\n      tx = matrix.tx,\n      ty = matrix.ty;\n    let minX = this.minX,\n      minY = this.minY,\n      maxX = this.maxX,\n      maxY = this.maxY;\n    for (let i = beginOffset; i < endOffset; i += 2) {\n      const rawX = vertices[i],\n        rawY = vertices[i + 1],\n        x = a * rawX + c * rawY + tx,\n        y = d * rawY + b * rawX + ty;\n      minX = Math.min(minX, x - padX), maxX = Math.max(maxX, x + padX), minY = Math.min(minY, y - padY), maxY = Math.max(maxY, y + padY);\n    }\n    this.minX = minX, this.minY = minY, this.maxX = maxX, this.maxY = maxY;\n  }\n  /**\n   * Adds other {@link PIXI.Bounds}.\n   * @param bounds - The Bounds to be added\n   */\n  addBounds(bounds) {\n    const minX = this.minX,\n      minY = this.minY,\n      maxX = this.maxX,\n      maxY = this.maxY;\n    this.minX = bounds.minX < minX ? bounds.minX : minX, this.minY = bounds.minY < minY ? bounds.minY : minY, this.maxX = bounds.maxX > maxX ? bounds.maxX : maxX, this.maxY = bounds.maxY > maxY ? bounds.maxY : maxY;\n  }\n  /**\n   * Adds other Bounds, masked with Bounds.\n   * @param bounds - The Bounds to be added.\n   * @param mask - TODO\n   */\n  addBoundsMask(bounds, mask) {\n    const _minX = bounds.minX > mask.minX ? bounds.minX : mask.minX,\n      _minY = bounds.minY > mask.minY ? bounds.minY : mask.minY,\n      _maxX = bounds.maxX < mask.maxX ? bounds.maxX : mask.maxX,\n      _maxY = bounds.maxY < mask.maxY ? bounds.maxY : mask.maxY;\n    if (_minX <= _maxX && _minY <= _maxY) {\n      const minX = this.minX,\n        minY = this.minY,\n        maxX = this.maxX,\n        maxY = this.maxY;\n      this.minX = _minX < minX ? _minX : minX, this.minY = _minY < minY ? _minY : minY, this.maxX = _maxX > maxX ? _maxX : maxX, this.maxY = _maxY > maxY ? _maxY : maxY;\n    }\n  }\n  /**\n   * Adds other Bounds, multiplied by matrix. Bounds shouldn't be empty.\n   * @param bounds - other bounds\n   * @param matrix - multiplicator\n   */\n  addBoundsMatrix(bounds, matrix) {\n    this.addFrameMatrix(matrix, bounds.minX, bounds.minY, bounds.maxX, bounds.maxY);\n  }\n  /**\n   * Adds other Bounds, masked with Rectangle.\n   * @param bounds - TODO\n   * @param area - TODO\n   */\n  addBoundsArea(bounds, area) {\n    const _minX = bounds.minX > area.x ? bounds.minX : area.x,\n      _minY = bounds.minY > area.y ? bounds.minY : area.y,\n      _maxX = bounds.maxX < area.x + area.width ? bounds.maxX : area.x + area.width,\n      _maxY = bounds.maxY < area.y + area.height ? bounds.maxY : area.y + area.height;\n    if (_minX <= _maxX && _minY <= _maxY) {\n      const minX = this.minX,\n        minY = this.minY,\n        maxX = this.maxX,\n        maxY = this.maxY;\n      this.minX = _minX < minX ? _minX : minX, this.minY = _minY < minY ? _minY : minY, this.maxX = _maxX > maxX ? _maxX : maxX, this.maxY = _maxY > maxY ? _maxY : maxY;\n    }\n  }\n  /**\n   * Pads bounds object, making it grow in all directions.\n   * If paddingY is omitted, both paddingX and paddingY will be set to paddingX.\n   * @param paddingX - The horizontal padding amount.\n   * @param paddingY - The vertical padding amount.\n   */\n  pad(paddingX = 0, paddingY = paddingX) {\n    this.isEmpty() || (this.minX -= paddingX, this.maxX += paddingX, this.minY -= paddingY, this.maxY += paddingY);\n  }\n  /**\n   * Adds padded frame. (x0, y0) should be strictly less than (x1, y1)\n   * @param x0 - left X of frame\n   * @param y0 - top Y of frame\n   * @param x1 - right X of frame\n   * @param y1 - bottom Y of frame\n   * @param padX - padding X\n   * @param padY - padding Y\n   */\n  addFramePad(x0, y0, x1, y1, padX, padY) {\n    x0 -= padX, y0 -= padY, x1 += padX, y1 += padY, this.minX = this.minX < x0 ? this.minX : x0, this.maxX = this.maxX > x1 ? this.maxX : x1, this.minY = this.minY < y0 ? this.minY : y0, this.maxY = this.maxY > y1 ? this.maxY : y1;\n  }\n}\nexport { Bounds };", "map": {"version": 3, "names": ["Bounds", "constructor", "minX", "minY", "maxX", "maxY", "rect", "updateID", "isEmpty", "clear", "getRectangle", "Rectangle", "EMPTY", "x", "y", "width", "height", "addPoint", "point", "Math", "min", "max", "addPointMatrix", "matrix", "a", "b", "c", "d", "tx", "ty", "addQuad", "vertices", "addFrame", "transform", "x0", "y0", "x1", "y1", "addFrameMatrix", "worldTransform", "addVertexData", "vertexData", "beginOffset", "endOffset", "i", "addVertices", "addVerticesMatrix", "padX", "padY", "rawX", "rawY", "addBounds", "bounds", "addBoundsMask", "mask", "_minX", "_minY", "_maxX", "_maxY", "addBoundsMatrix", "addBoundsArea", "area", "pad", "paddingX", "paddingY", "addFramePad"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\display\\src\\Bounds.ts"], "sourcesContent": ["import { Rectangle } from '@pixi/core';\n\nimport type { IPointData, Matrix, Transform } from '@pixi/core';\n\n/**\n * 'Builder' pattern for bounds rectangles.\n *\n * This could be called an Axis-Aligned Bounding Box.\n * It is not an actual shape. It is a mutable thing; no 'EMPTY' or those kind of problems.\n * @memberof PIXI\n */\nexport class Bounds\n{\n    /** @default Infinity */\n    public minX: number;\n\n    /** @default Infinity */\n    public minY: number;\n\n    /** @default -Infinity */\n    public maxX: number;\n\n    /** @default -Infinity */\n    public maxY: number;\n\n    public rect: Rectangle;\n\n    /**\n     * It is updated to _boundsID of corresponding object to keep bounds in sync with content.\n     * Updated from outside, thus public modifier.\n     */\n    public updateID: number;\n\n    constructor()\n    {\n        this.minX = Infinity;\n        this.minY = Infinity;\n        this.maxX = -Infinity;\n        this.maxY = -Infinity;\n\n        this.rect = null;\n        this.updateID = -1;\n    }\n\n    /**\n     * Checks if bounds are empty.\n     * @returns - True if empty.\n     */\n    isEmpty(): boolean\n    {\n        return this.minX > this.maxX || this.minY > this.maxY;\n    }\n\n    /** Clears the bounds and resets. */\n    clear(): void\n    {\n        this.minX = Infinity;\n        this.minY = Infinity;\n        this.maxX = -Infinity;\n        this.maxY = -Infinity;\n    }\n\n    /**\n     * Can return Rectangle.EMPTY constant, either construct new rectangle, either use your rectangle\n     * It is not guaranteed that it will return tempRect\n     * @param rect - Temporary object will be used if AABB is not empty\n     * @returns - A rectangle of the bounds\n     */\n    getRectangle(rect?: Rectangle): Rectangle\n    {\n        if (this.minX > this.maxX || this.minY > this.maxY)\n        {\n            return Rectangle.EMPTY;\n        }\n\n        rect = rect || new Rectangle(0, 0, 1, 1);\n\n        rect.x = this.minX;\n        rect.y = this.minY;\n        rect.width = this.maxX - this.minX;\n        rect.height = this.maxY - this.minY;\n\n        return rect;\n    }\n\n    /**\n     * This function should be inlined when its possible.\n     * @param point - The point to add.\n     */\n    addPoint(point: IPointData): void\n    {\n        this.minX = Math.min(this.minX, point.x);\n        this.maxX = Math.max(this.maxX, point.x);\n        this.minY = Math.min(this.minY, point.y);\n        this.maxY = Math.max(this.maxY, point.y);\n    }\n\n    /**\n     * Adds a point, after transformed. This should be inlined when its possible.\n     * @param matrix\n     * @param point\n     */\n    addPointMatrix(matrix: Matrix, point: IPointData): void\n    {\n        const { a, b, c, d, tx, ty } = matrix;\n\n        const x = (a * point.x) + (c * point.y) + tx;\n        const y = (b * point.x) + (d * point.y) + ty;\n\n        this.minX = Math.min(this.minX, x);\n        this.maxX = Math.max(this.maxX, x);\n        this.minY = Math.min(this.minY, y);\n        this.maxY = Math.max(this.maxY, y);\n    }\n\n    /**\n     * Adds a quad, not transformed\n     * @param vertices - The verts to add.\n     */\n    addQuad(vertices: Float32Array): void\n    {\n        let minX = this.minX;\n        let minY = this.minY;\n        let maxX = this.maxX;\n        let maxY = this.maxY;\n\n        let x = vertices[0];\n        let y = vertices[1];\n\n        minX = x < minX ? x : minX;\n        minY = y < minY ? y : minY;\n        maxX = x > maxX ? x : maxX;\n        maxY = y > maxY ? y : maxY;\n\n        x = vertices[2];\n        y = vertices[3];\n        minX = x < minX ? x : minX;\n        minY = y < minY ? y : minY;\n        maxX = x > maxX ? x : maxX;\n        maxY = y > maxY ? y : maxY;\n\n        x = vertices[4];\n        y = vertices[5];\n        minX = x < minX ? x : minX;\n        minY = y < minY ? y : minY;\n        maxX = x > maxX ? x : maxX;\n        maxY = y > maxY ? y : maxY;\n\n        x = vertices[6];\n        y = vertices[7];\n        minX = x < minX ? x : minX;\n        minY = y < minY ? y : minY;\n        maxX = x > maxX ? x : maxX;\n        maxY = y > maxY ? y : maxY;\n\n        this.minX = minX;\n        this.minY = minY;\n        this.maxX = maxX;\n        this.maxY = maxY;\n    }\n\n    /**\n     * Adds sprite frame, transformed.\n     * @param transform - transform to apply\n     * @param x0 - left X of frame\n     * @param y0 - top Y of frame\n     * @param x1 - right X of frame\n     * @param y1 - bottom Y of frame\n     */\n    addFrame(transform: Transform, x0: number, y0: number, x1: number, y1: number): void\n    {\n        this.addFrameMatrix(transform.worldTransform, x0, y0, x1, y1);\n    }\n\n    /**\n     * Adds sprite frame, multiplied by matrix\n     * @param matrix - matrix to apply\n     * @param x0 - left X of frame\n     * @param y0 - top Y of frame\n     * @param x1 - right X of frame\n     * @param y1 - bottom Y of frame\n     */\n    addFrameMatrix(matrix: Matrix, x0: number, y0: number, x1: number, y1: number): void\n    {\n        const a = matrix.a;\n        const b = matrix.b;\n        const c = matrix.c;\n        const d = matrix.d;\n        const tx = matrix.tx;\n        const ty = matrix.ty;\n\n        let minX = this.minX;\n        let minY = this.minY;\n        let maxX = this.maxX;\n        let maxY = this.maxY;\n\n        let x = (a * x0) + (c * y0) + tx;\n        let y = (b * x0) + (d * y0) + ty;\n\n        minX = x < minX ? x : minX;\n        minY = y < minY ? y : minY;\n        maxX = x > maxX ? x : maxX;\n        maxY = y > maxY ? y : maxY;\n\n        x = (a * x1) + (c * y0) + tx;\n        y = (b * x1) + (d * y0) + ty;\n        minX = x < minX ? x : minX;\n        minY = y < minY ? y : minY;\n        maxX = x > maxX ? x : maxX;\n        maxY = y > maxY ? y : maxY;\n\n        x = (a * x0) + (c * y1) + tx;\n        y = (b * x0) + (d * y1) + ty;\n        minX = x < minX ? x : minX;\n        minY = y < minY ? y : minY;\n        maxX = x > maxX ? x : maxX;\n        maxY = y > maxY ? y : maxY;\n\n        x = (a * x1) + (c * y1) + tx;\n        y = (b * x1) + (d * y1) + ty;\n        minX = x < minX ? x : minX;\n        minY = y < minY ? y : minY;\n        maxX = x > maxX ? x : maxX;\n        maxY = y > maxY ? y : maxY;\n\n        this.minX = minX;\n        this.minY = minY;\n        this.maxX = maxX;\n        this.maxY = maxY;\n    }\n\n    /**\n     * Adds screen vertices from array\n     * @param vertexData - calculated vertices\n     * @param beginOffset - begin offset\n     * @param endOffset - end offset, excluded\n     */\n    addVertexData(vertexData: Float32Array, beginOffset: number, endOffset: number): void\n    {\n        let minX = this.minX;\n        let minY = this.minY;\n        let maxX = this.maxX;\n        let maxY = this.maxY;\n\n        for (let i = beginOffset; i < endOffset; i += 2)\n        {\n            const x = vertexData[i];\n            const y = vertexData[i + 1];\n\n            minX = x < minX ? x : minX;\n            minY = y < minY ? y : minY;\n            maxX = x > maxX ? x : maxX;\n            maxY = y > maxY ? y : maxY;\n        }\n\n        this.minX = minX;\n        this.minY = minY;\n        this.maxX = maxX;\n        this.maxY = maxY;\n    }\n\n    /**\n     * Add an array of mesh vertices\n     * @param transform - mesh transform\n     * @param vertices - mesh coordinates in array\n     * @param beginOffset - begin offset\n     * @param endOffset - end offset, excluded\n     */\n    addVertices(transform: Transform, vertices: Float32Array, beginOffset: number, endOffset: number): void\n    {\n        this.addVerticesMatrix(transform.worldTransform, vertices, beginOffset, endOffset);\n    }\n\n    /**\n     * Add an array of mesh vertices.\n     * @param matrix - mesh matrix\n     * @param vertices - mesh coordinates in array\n     * @param beginOffset - begin offset\n     * @param endOffset - end offset, excluded\n     * @param padX - x padding\n     * @param padY - y padding\n     */\n    addVerticesMatrix(matrix: Matrix, vertices: Float32Array, beginOffset: number,\n        endOffset: number, padX = 0, padY = padX): void\n    {\n        const a = matrix.a;\n        const b = matrix.b;\n        const c = matrix.c;\n        const d = matrix.d;\n        const tx = matrix.tx;\n        const ty = matrix.ty;\n\n        let minX = this.minX;\n        let minY = this.minY;\n        let maxX = this.maxX;\n        let maxY = this.maxY;\n\n        for (let i = beginOffset; i < endOffset; i += 2)\n        {\n            const rawX = vertices[i];\n            const rawY = vertices[i + 1];\n            const x = (a * rawX) + (c * rawY) + tx;\n            const y = (d * rawY) + (b * rawX) + ty;\n\n            minX = Math.min(minX, x - padX);\n            maxX = Math.max(maxX, x + padX);\n            minY = Math.min(minY, y - padY);\n            maxY = Math.max(maxY, y + padY);\n        }\n\n        this.minX = minX;\n        this.minY = minY;\n        this.maxX = maxX;\n        this.maxY = maxY;\n    }\n\n    /**\n     * Adds other {@link PIXI.Bounds}.\n     * @param bounds - The Bounds to be added\n     */\n    addBounds(bounds: Bounds): void\n    {\n        const minX = this.minX;\n        const minY = this.minY;\n        const maxX = this.maxX;\n        const maxY = this.maxY;\n\n        this.minX = bounds.minX < minX ? bounds.minX : minX;\n        this.minY = bounds.minY < minY ? bounds.minY : minY;\n        this.maxX = bounds.maxX > maxX ? bounds.maxX : maxX;\n        this.maxY = bounds.maxY > maxY ? bounds.maxY : maxY;\n    }\n\n    /**\n     * Adds other Bounds, masked with Bounds.\n     * @param bounds - The Bounds to be added.\n     * @param mask - TODO\n     */\n    addBoundsMask(bounds: Bounds, mask: Bounds): void\n    {\n        const _minX = bounds.minX > mask.minX ? bounds.minX : mask.minX;\n        const _minY = bounds.minY > mask.minY ? bounds.minY : mask.minY;\n        const _maxX = bounds.maxX < mask.maxX ? bounds.maxX : mask.maxX;\n        const _maxY = bounds.maxY < mask.maxY ? bounds.maxY : mask.maxY;\n\n        if (_minX <= _maxX && _minY <= _maxY)\n        {\n            const minX = this.minX;\n            const minY = this.minY;\n            const maxX = this.maxX;\n            const maxY = this.maxY;\n\n            this.minX = _minX < minX ? _minX : minX;\n            this.minY = _minY < minY ? _minY : minY;\n            this.maxX = _maxX > maxX ? _maxX : maxX;\n            this.maxY = _maxY > maxY ? _maxY : maxY;\n        }\n    }\n\n    /**\n     * Adds other Bounds, multiplied by matrix. Bounds shouldn't be empty.\n     * @param bounds - other bounds\n     * @param matrix - multiplicator\n     */\n    addBoundsMatrix(bounds: Bounds, matrix: Matrix): void\n    {\n        this.addFrameMatrix(matrix, bounds.minX, bounds.minY, bounds.maxX, bounds.maxY);\n    }\n\n    /**\n     * Adds other Bounds, masked with Rectangle.\n     * @param bounds - TODO\n     * @param area - TODO\n     */\n    addBoundsArea(bounds: Bounds, area: Rectangle): void\n    {\n        const _minX = bounds.minX > area.x ? bounds.minX : area.x;\n        const _minY = bounds.minY > area.y ? bounds.minY : area.y;\n        const _maxX = bounds.maxX < area.x + area.width ? bounds.maxX : (area.x + area.width);\n        const _maxY = bounds.maxY < area.y + area.height ? bounds.maxY : (area.y + area.height);\n\n        if (_minX <= _maxX && _minY <= _maxY)\n        {\n            const minX = this.minX;\n            const minY = this.minY;\n            const maxX = this.maxX;\n            const maxY = this.maxY;\n\n            this.minX = _minX < minX ? _minX : minX;\n            this.minY = _minY < minY ? _minY : minY;\n            this.maxX = _maxX > maxX ? _maxX : maxX;\n            this.maxY = _maxY > maxY ? _maxY : maxY;\n        }\n    }\n\n    /**\n     * Pads bounds object, making it grow in all directions.\n     * If paddingY is omitted, both paddingX and paddingY will be set to paddingX.\n     * @param paddingX - The horizontal padding amount.\n     * @param paddingY - The vertical padding amount.\n     */\n    pad(paddingX = 0, paddingY = paddingX): void\n    {\n        if (!this.isEmpty())\n        {\n            this.minX -= paddingX;\n            this.maxX += paddingX;\n            this.minY -= paddingY;\n            this.maxY += paddingY;\n        }\n    }\n\n    /**\n     * Adds padded frame. (x0, y0) should be strictly less than (x1, y1)\n     * @param x0 - left X of frame\n     * @param y0 - top Y of frame\n     * @param x1 - right X of frame\n     * @param y1 - bottom Y of frame\n     * @param padX - padding X\n     * @param padY - padding Y\n     */\n    addFramePad(x0: number, y0: number, x1: number, y1: number, padX: number, padY: number): void\n    {\n        x0 -= padX;\n        y0 -= padY;\n        x1 += padX;\n        y1 += padY;\n\n        this.minX = this.minX < x0 ? this.minX : x0;\n        this.maxX = this.maxX > x1 ? this.maxX : x1;\n        this.minY = this.minY < y0 ? this.minY : y0;\n        this.maxY = this.maxY > y1 ? this.maxY : y1;\n    }\n}\n"], "mappings": ";AAWO,MAAMA,MAAA,CACb;EAqBIC,YAAA,EACA;IACI,KAAKC,IAAA,GAAO,OACZ,KAAKC,IAAA,GAAO,OACZ,KAAKC,IAAA,GAAO,KACZ,QAAKC,IAAA,GAAO,KAEZ,QAAKC,IAAA,GAAO,MACZ,KAAKC,QAAA,GAAW;EACpB;EAAA;AAAA;AAAA;AAAA;EAMAC,QAAA,EACA;IACI,OAAO,KAAKN,IAAA,GAAO,KAAKE,IAAA,IAAQ,KAAKD,IAAA,GAAO,KAAKE,IAAA;EACrD;EAAA;EAGAI,MAAA,EACA;IACS,KAAAP,IAAA,GAAO,OACZ,KAAKC,IAAA,GAAO,OACZ,KAAKC,IAAA,GAAO,KACZ,QAAKC,IAAA,GAAO;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAK,aAAaJ,IAAA,EACb;IACI,OAAI,KAAKJ,IAAA,GAAO,KAAKE,IAAA,IAAQ,KAAKD,IAAA,GAAO,KAAKE,IAAA,GAEnCM,SAAA,CAAUC,KAAA,IAGrBN,IAAA,GAAOA,IAAA,IAAQ,IAAIK,SAAA,CAAU,GAAG,GAAG,GAAG,CAAC,GAEvCL,IAAA,CAAKO,CAAA,GAAI,KAAKX,IAAA,EACdI,IAAA,CAAKQ,CAAA,GAAI,KAAKX,IAAA,EACdG,IAAA,CAAKS,KAAA,GAAQ,KAAKX,IAAA,GAAO,KAAKF,IAAA,EAC9BI,IAAA,CAAKU,MAAA,GAAS,KAAKX,IAAA,GAAO,KAAKF,IAAA,EAExBG,IAAA;EACX;EAAA;AAAA;AAAA;AAAA;EAMAW,SAASC,KAAA,EACT;IACI,KAAKhB,IAAA,GAAOiB,IAAA,CAAKC,GAAA,CAAI,KAAKlB,IAAA,EAAMgB,KAAA,CAAML,CAAC,GACvC,KAAKT,IAAA,GAAOe,IAAA,CAAKE,GAAA,CAAI,KAAKjB,IAAA,EAAMc,KAAA,CAAML,CAAC,GACvC,KAAKV,IAAA,GAAOgB,IAAA,CAAKC,GAAA,CAAI,KAAKjB,IAAA,EAAMe,KAAA,CAAMJ,CAAC,GACvC,KAAKT,IAAA,GAAOc,IAAA,CAAKE,GAAA,CAAI,KAAKhB,IAAA,EAAMa,KAAA,CAAMJ,CAAC;EAC3C;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAQ,eAAeC,MAAA,EAAgBL,KAAA,EAC/B;IACU;QAAEM,CAAA;QAAGC,CAAA;QAAGC,CAAA;QAAGC,CAAA;QAAGC,EAAA;QAAIC;MAAA,IAAON,MAAA;MAEzBV,CAAA,GAAKW,CAAA,GAAIN,KAAA,CAAML,CAAA,GAAMa,CAAA,GAAIR,KAAA,CAAMJ,CAAA,GAAKc,EAAA;MACpCd,CAAA,GAAKW,CAAA,GAAIP,KAAA,CAAML,CAAA,GAAMc,CAAA,GAAIT,KAAA,CAAMJ,CAAA,GAAKe,EAAA;IAE1C,KAAK3B,IAAA,GAAOiB,IAAA,CAAKC,GAAA,CAAI,KAAKlB,IAAA,EAAMW,CAAC,GACjC,KAAKT,IAAA,GAAOe,IAAA,CAAKE,GAAA,CAAI,KAAKjB,IAAA,EAAMS,CAAC,GACjC,KAAKV,IAAA,GAAOgB,IAAA,CAAKC,GAAA,CAAI,KAAKjB,IAAA,EAAMW,CAAC,GACjC,KAAKT,IAAA,GAAOc,IAAA,CAAKE,GAAA,CAAI,KAAKhB,IAAA,EAAMS,CAAC;EACrC;EAAA;AAAA;AAAA;AAAA;EAMAgB,QAAQC,QAAA,EACR;IACI,IAAI7B,IAAA,GAAO,KAAKA,IAAA;MACZC,IAAA,GAAO,KAAKA,IAAA;MACZC,IAAA,GAAO,KAAKA,IAAA;MACZC,IAAA,GAAO,KAAKA,IAAA;MAEZQ,CAAA,GAAIkB,QAAA,CAAS,CAAC;MACdjB,CAAA,GAAIiB,QAAA,CAAS,CAAC;IAEX7B,IAAA,GAAAW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EAEtBQ,CAAA,GAAIkB,QAAA,CAAS,CAAC,GACdjB,CAAA,GAAIiB,QAAA,CAAS,CAAC,GACd7B,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EAEtBQ,CAAA,GAAIkB,QAAA,CAAS,CAAC,GACdjB,CAAA,GAAIiB,QAAA,CAAS,CAAC,GACd7B,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EAEtBQ,CAAA,GAAIkB,QAAA,CAAS,CAAC,GACdjB,CAAA,GAAIiB,QAAA,CAAS,CAAC,GACd7B,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EAEtB,KAAKH,IAAA,GAAOA,IAAA,EACZ,KAAKC,IAAA,GAAOA,IAAA,EACZ,KAAKC,IAAA,GAAOA,IAAA,EACZ,KAAKC,IAAA,GAAOA,IAAA;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUA2B,SAASC,SAAA,EAAsBC,EAAA,EAAYC,EAAA,EAAYC,EAAA,EAAYC,EAAA,EACnE;IACI,KAAKC,cAAA,CAAeL,SAAA,CAAUM,cAAA,EAAgBL,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAE;EAChE;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUAC,eAAef,MAAA,EAAgBW,EAAA,EAAYC,EAAA,EAAYC,EAAA,EAAYC,EAAA,EACnE;IACI,MAAMb,CAAA,GAAID,MAAA,CAAOC,CAAA;MACXC,CAAA,GAAIF,MAAA,CAAOE,CAAA;MACXC,CAAA,GAAIH,MAAA,CAAOG,CAAA;MACXC,CAAA,GAAIJ,MAAA,CAAOI,CAAA;MACXC,EAAA,GAAKL,MAAA,CAAOK,EAAA;MACZC,EAAA,GAAKN,MAAA,CAAOM,EAAA;IAEd,IAAA3B,IAAA,GAAO,KAAKA,IAAA;MACZC,IAAA,GAAO,KAAKA,IAAA;MACZC,IAAA,GAAO,KAAKA,IAAA;MACZC,IAAA,GAAO,KAAKA,IAAA;MAEZQ,CAAA,GAAKW,CAAA,GAAIU,EAAA,GAAOR,CAAA,GAAIS,EAAA,GAAMP,EAAA;MAC1Bd,CAAA,GAAKW,CAAA,GAAIS,EAAA,GAAOP,CAAA,GAAIQ,EAAA,GAAMN,EAAA;IAE9B3B,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EAEtBQ,CAAA,GAAKW,CAAA,GAAIY,EAAA,GAAOV,CAAA,GAAIS,EAAA,GAAMP,EAAA,EAC1Bd,CAAA,GAAKW,CAAA,GAAIW,EAAA,GAAOT,CAAA,GAAIQ,EAAA,GAAMN,EAAA,EAC1B3B,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EAEtBQ,CAAA,GAAKW,CAAA,GAAIU,EAAA,GAAOR,CAAA,GAAIW,EAAA,GAAMT,EAAA,EAC1Bd,CAAA,GAAKW,CAAA,GAAIS,EAAA,GAAOP,CAAA,GAAIU,EAAA,GAAMR,EAAA,EAC1B3B,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EAEtBQ,CAAA,GAAKW,CAAA,GAAIY,EAAA,GAAOV,CAAA,GAAIW,EAAA,GAAMT,EAAA,EAC1Bd,CAAA,GAAKW,CAAA,GAAIW,EAAA,GAAOT,CAAA,GAAIU,EAAA,GAAMR,EAAA,EAC1B3B,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EAEtB,KAAKH,IAAA,GAAOA,IAAA,EACZ,KAAKC,IAAA,GAAOA,IAAA,EACZ,KAAKC,IAAA,GAAOA,IAAA,EACZ,KAAKC,IAAA,GAAOA,IAAA;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAmC,cAAcC,UAAA,EAA0BC,WAAA,EAAqBC,SAAA,EAC7D;IACQ,IAAAzC,IAAA,GAAO,KAAKA,IAAA;MACZC,IAAA,GAAO,KAAKA,IAAA;MACZC,IAAA,GAAO,KAAKA,IAAA;MACZC,IAAA,GAAO,KAAKA,IAAA;IAEhB,SAASuC,CAAA,GAAIF,WAAA,EAAaE,CAAA,GAAID,SAAA,EAAWC,CAAA,IAAK,GAC9C;MACI,MAAM/B,CAAA,GAAI4B,UAAA,CAAWG,CAAC;QAChB9B,CAAA,GAAI2B,UAAA,CAAWG,CAAA,GAAI,CAAC;MAE1B1C,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOW,CAAA,GAAIX,IAAA,GAAOW,CAAA,GAAIX,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA,EACtBC,IAAA,GAAOS,CAAA,GAAIT,IAAA,GAAOS,CAAA,GAAIT,IAAA;IAC1B;IAEK,KAAAH,IAAA,GAAOA,IAAA,EACZ,KAAKC,IAAA,GAAOA,IAAA,EACZ,KAAKC,IAAA,GAAOA,IAAA,EACZ,KAAKC,IAAA,GAAOA,IAAA;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAwC,YAAYZ,SAAA,EAAsBF,QAAA,EAAwBW,WAAA,EAAqBC,SAAA,EAC/E;IACI,KAAKG,iBAAA,CAAkBb,SAAA,CAAUM,cAAA,EAAgBR,QAAA,EAAUW,WAAA,EAAaC,SAAS;EACrF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWAG,kBAAkBvB,MAAA,EAAgBQ,QAAA,EAAwBW,WAAA,EACtDC,SAAA,EAAmBI,IAAA,GAAO,GAAGC,IAAA,GAAOD,IAAA,EACxC;IACI,MAAMvB,CAAA,GAAID,MAAA,CAAOC,CAAA;MACXC,CAAA,GAAIF,MAAA,CAAOE,CAAA;MACXC,CAAA,GAAIH,MAAA,CAAOG,CAAA;MACXC,CAAA,GAAIJ,MAAA,CAAOI,CAAA;MACXC,EAAA,GAAKL,MAAA,CAAOK,EAAA;MACZC,EAAA,GAAKN,MAAA,CAAOM,EAAA;IAEd,IAAA3B,IAAA,GAAO,KAAKA,IAAA;MACZC,IAAA,GAAO,KAAKA,IAAA;MACZC,IAAA,GAAO,KAAKA,IAAA;MACZC,IAAA,GAAO,KAAKA,IAAA;IAEhB,SAASuC,CAAA,GAAIF,WAAA,EAAaE,CAAA,GAAID,SAAA,EAAWC,CAAA,IAAK,GAC9C;MACI,MAAMK,IAAA,GAAOlB,QAAA,CAASa,CAAC;QACjBM,IAAA,GAAOnB,QAAA,CAASa,CAAA,GAAI,CAAC;QACrB/B,CAAA,GAAKW,CAAA,GAAIyB,IAAA,GAASvB,CAAA,GAAIwB,IAAA,GAAQtB,EAAA;QAC9Bd,CAAA,GAAKa,CAAA,GAAIuB,IAAA,GAASzB,CAAA,GAAIwB,IAAA,GAAQpB,EAAA;MAE7B3B,IAAA,GAAAiB,IAAA,CAAKC,GAAA,CAAIlB,IAAA,EAAMW,CAAA,GAAIkC,IAAI,GAC9B3C,IAAA,GAAOe,IAAA,CAAKE,GAAA,CAAIjB,IAAA,EAAMS,CAAA,GAAIkC,IAAI,GAC9B5C,IAAA,GAAOgB,IAAA,CAAKC,GAAA,CAAIjB,IAAA,EAAMW,CAAA,GAAIkC,IAAI,GAC9B3C,IAAA,GAAOc,IAAA,CAAKE,GAAA,CAAIhB,IAAA,EAAMS,CAAA,GAAIkC,IAAI;IAClC;IAEK,KAAA9C,IAAA,GAAOA,IAAA,EACZ,KAAKC,IAAA,GAAOA,IAAA,EACZ,KAAKC,IAAA,GAAOA,IAAA,EACZ,KAAKC,IAAA,GAAOA,IAAA;EAChB;EAAA;AAAA;AAAA;AAAA;EAMA8C,UAAUC,MAAA,EACV;IACU,MAAAlD,IAAA,GAAO,KAAKA,IAAA;MACZC,IAAA,GAAO,KAAKA,IAAA;MACZC,IAAA,GAAO,KAAKA,IAAA;MACZC,IAAA,GAAO,KAAKA,IAAA;IAElB,KAAKH,IAAA,GAAOkD,MAAA,CAAOlD,IAAA,GAAOA,IAAA,GAAOkD,MAAA,CAAOlD,IAAA,GAAOA,IAAA,EAC/C,KAAKC,IAAA,GAAOiD,MAAA,CAAOjD,IAAA,GAAOA,IAAA,GAAOiD,MAAA,CAAOjD,IAAA,GAAOA,IAAA,EAC/C,KAAKC,IAAA,GAAOgD,MAAA,CAAOhD,IAAA,GAAOA,IAAA,GAAOgD,MAAA,CAAOhD,IAAA,GAAOA,IAAA,EAC/C,KAAKC,IAAA,GAAO+C,MAAA,CAAO/C,IAAA,GAAOA,IAAA,GAAO+C,MAAA,CAAO/C,IAAA,GAAOA,IAAA;EACnD;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAgD,cAAcD,MAAA,EAAgBE,IAAA,EAC9B;IACI,MAAMC,KAAA,GAAQH,MAAA,CAAOlD,IAAA,GAAOoD,IAAA,CAAKpD,IAAA,GAAOkD,MAAA,CAAOlD,IAAA,GAAOoD,IAAA,CAAKpD,IAAA;MACrDsD,KAAA,GAAQJ,MAAA,CAAOjD,IAAA,GAAOmD,IAAA,CAAKnD,IAAA,GAAOiD,MAAA,CAAOjD,IAAA,GAAOmD,IAAA,CAAKnD,IAAA;MACrDsD,KAAA,GAAQL,MAAA,CAAOhD,IAAA,GAAOkD,IAAA,CAAKlD,IAAA,GAAOgD,MAAA,CAAOhD,IAAA,GAAOkD,IAAA,CAAKlD,IAAA;MACrDsD,KAAA,GAAQN,MAAA,CAAO/C,IAAA,GAAOiD,IAAA,CAAKjD,IAAA,GAAO+C,MAAA,CAAO/C,IAAA,GAAOiD,IAAA,CAAKjD,IAAA;IAEvD,IAAAkD,KAAA,IAASE,KAAA,IAASD,KAAA,IAASE,KAAA,EAC/B;MACU,MAAAxD,IAAA,GAAO,KAAKA,IAAA;QACZC,IAAA,GAAO,KAAKA,IAAA;QACZC,IAAA,GAAO,KAAKA,IAAA;QACZC,IAAA,GAAO,KAAKA,IAAA;MAEb,KAAAH,IAAA,GAAOqD,KAAA,GAAQrD,IAAA,GAAOqD,KAAA,GAAQrD,IAAA,EACnC,KAAKC,IAAA,GAAOqD,KAAA,GAAQrD,IAAA,GAAOqD,KAAA,GAAQrD,IAAA,EACnC,KAAKC,IAAA,GAAOqD,KAAA,GAAQrD,IAAA,GAAOqD,KAAA,GAAQrD,IAAA,EACnC,KAAKC,IAAA,GAAOqD,KAAA,GAAQrD,IAAA,GAAOqD,KAAA,GAAQrD,IAAA;IACvC;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAsD,gBAAgBP,MAAA,EAAgB7B,MAAA,EAChC;IACS,KAAAe,cAAA,CAAef,MAAA,EAAQ6B,MAAA,CAAOlD,IAAA,EAAMkD,MAAA,CAAOjD,IAAA,EAAMiD,MAAA,CAAOhD,IAAA,EAAMgD,MAAA,CAAO/C,IAAI;EAClF;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAuD,cAAcR,MAAA,EAAgBS,IAAA,EAC9B;IACI,MAAMN,KAAA,GAAQH,MAAA,CAAOlD,IAAA,GAAO2D,IAAA,CAAKhD,CAAA,GAAIuC,MAAA,CAAOlD,IAAA,GAAO2D,IAAA,CAAKhD,CAAA;MAClD2C,KAAA,GAAQJ,MAAA,CAAOjD,IAAA,GAAO0D,IAAA,CAAK/C,CAAA,GAAIsC,MAAA,CAAOjD,IAAA,GAAO0D,IAAA,CAAK/C,CAAA;MAClD2C,KAAA,GAAQL,MAAA,CAAOhD,IAAA,GAAOyD,IAAA,CAAKhD,CAAA,GAAIgD,IAAA,CAAK9C,KAAA,GAAQqC,MAAA,CAAOhD,IAAA,GAAQyD,IAAA,CAAKhD,CAAA,GAAIgD,IAAA,CAAK9C,KAAA;MACzE2C,KAAA,GAAQN,MAAA,CAAO/C,IAAA,GAAOwD,IAAA,CAAK/C,CAAA,GAAI+C,IAAA,CAAK7C,MAAA,GAASoC,MAAA,CAAO/C,IAAA,GAAQwD,IAAA,CAAK/C,CAAA,GAAI+C,IAAA,CAAK7C,MAAA;IAE5E,IAAAuC,KAAA,IAASE,KAAA,IAASD,KAAA,IAASE,KAAA,EAC/B;MACU,MAAAxD,IAAA,GAAO,KAAKA,IAAA;QACZC,IAAA,GAAO,KAAKA,IAAA;QACZC,IAAA,GAAO,KAAKA,IAAA;QACZC,IAAA,GAAO,KAAKA,IAAA;MAEb,KAAAH,IAAA,GAAOqD,KAAA,GAAQrD,IAAA,GAAOqD,KAAA,GAAQrD,IAAA,EACnC,KAAKC,IAAA,GAAOqD,KAAA,GAAQrD,IAAA,GAAOqD,KAAA,GAAQrD,IAAA,EACnC,KAAKC,IAAA,GAAOqD,KAAA,GAAQrD,IAAA,GAAOqD,KAAA,GAAQrD,IAAA,EACnC,KAAKC,IAAA,GAAOqD,KAAA,GAAQrD,IAAA,GAAOqD,KAAA,GAAQrD,IAAA;IACvC;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAyD,IAAIC,QAAA,GAAW,GAAGC,QAAA,GAAWD,QAAA,EAC7B;IACS,KAAKvD,OAAA,OAEN,KAAKN,IAAA,IAAQ6D,QAAA,EACb,KAAK3D,IAAA,IAAQ2D,QAAA,EACb,KAAK5D,IAAA,IAAQ6D,QAAA,EACb,KAAK3D,IAAA,IAAQ2D,QAAA;EAErB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWAC,YAAY/B,EAAA,EAAYC,EAAA,EAAYC,EAAA,EAAYC,EAAA,EAAYU,IAAA,EAAcC,IAAA,EAC1E;IACId,EAAA,IAAMa,IAAA,EACNZ,EAAA,IAAMa,IAAA,EACNZ,EAAA,IAAMW,IAAA,EACNV,EAAA,IAAMW,IAAA,EAEN,KAAK9C,IAAA,GAAO,KAAKA,IAAA,GAAOgC,EAAA,GAAK,KAAKhC,IAAA,GAAOgC,EAAA,EACzC,KAAK9B,IAAA,GAAO,KAAKA,IAAA,GAAOgC,EAAA,GAAK,KAAKhC,IAAA,GAAOgC,EAAA,EACzC,KAAKjC,IAAA,GAAO,KAAKA,IAAA,GAAOgC,EAAA,GAAK,KAAKhC,IAAA,GAAOgC,EAAA,EACzC,KAAK9B,IAAA,GAAO,KAAKA,IAAA,GAAOgC,EAAA,GAAK,KAAKhC,IAAA,GAAOgC,EAAA;EAC7C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}