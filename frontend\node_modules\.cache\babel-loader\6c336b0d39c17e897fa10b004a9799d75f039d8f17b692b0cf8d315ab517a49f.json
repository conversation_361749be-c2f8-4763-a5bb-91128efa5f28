{"ast": null, "code": "import { ExtensionType, extensions } from \"@pixi/extensions\";\nimport { ObjectRenderer } from \"./ObjectRenderer.mjs\";\nclass BatchSystem {\n  /**\n   * @param renderer - The renderer this System works for.\n   */\n  constructor(renderer) {\n    this.renderer = renderer, this.emptyRenderer = new ObjectRenderer(renderer), this.currentRenderer = this.emptyRenderer;\n  }\n  /**\n   * Changes the current renderer to the one given in parameter\n   * @param objectRenderer - The object renderer to use.\n   */\n  setObjectRenderer(objectRenderer) {\n    this.currentRenderer !== objectRenderer && (this.currentRenderer.stop(), this.currentRenderer = objectRenderer, this.currentRenderer.start());\n  }\n  /**\n   * This should be called if you wish to do some custom rendering\n   * It will basically render anything that may be batched up such as sprites\n   */\n  flush() {\n    this.setObjectRenderer(this.emptyRenderer);\n  }\n  /** Reset the system to an empty renderer */\n  reset() {\n    this.setObjectRenderer(this.emptyRenderer);\n  }\n  /**\n   * Handy function for batch renderers: copies bound textures in first maxTextures locations to array\n   * sets actual _batchLocation for them\n   * @param arr - arr copy destination\n   * @param maxTextures - number of copied elements\n   */\n  copyBoundTextures(arr, maxTextures) {\n    const {\n      boundTextures\n    } = this.renderer.texture;\n    for (let i = maxTextures - 1; i >= 0; --i) arr[i] = boundTextures[i] || null, arr[i] && (arr[i]._batchLocation = i);\n  }\n  /**\n   * Assigns batch locations to textures in array based on boundTextures state.\n   * All textures in texArray should have `_batchEnabled = _batchId`,\n   * and their count should be less than `maxTextures`.\n   * @param texArray - textures to bound\n   * @param boundTextures - current state of bound textures\n   * @param batchId - marker for _batchEnabled param of textures in texArray\n   * @param maxTextures - number of texture locations to manipulate\n   */\n  boundArray(texArray, boundTextures, batchId, maxTextures) {\n    const {\n      elements,\n      ids,\n      count\n    } = texArray;\n    let j = 0;\n    for (let i = 0; i < count; i++) {\n      const tex = elements[i],\n        loc = tex._batchLocation;\n      if (loc >= 0 && loc < maxTextures && boundTextures[loc] === tex) {\n        ids[i] = loc;\n        continue;\n      }\n      for (; j < maxTextures;) {\n        const bound = boundTextures[j];\n        if (bound && bound._batchEnabled === batchId && bound._batchLocation === j) {\n          j++;\n          continue;\n        }\n        ids[i] = j, tex._batchLocation = j, boundTextures[j] = tex;\n        break;\n      }\n    }\n  }\n  /**\n   * @ignore\n   */\n  destroy() {\n    this.renderer = null;\n  }\n}\nBatchSystem.extension = {\n  type: ExtensionType.RendererSystem,\n  name: \"batch\"\n};\nextensions.add(BatchSystem);\nexport { BatchSystem };", "map": {"version": 3, "names": ["BatchSystem", "constructor", "renderer", "emptyRenderer", "O<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setObjectR<PERSON><PERSON>", "object<PERSON><PERSON><PERSON>", "stop", "start", "flush", "reset", "copyBoundTextures", "arr", "maxTextures", "boundTextures", "texture", "i", "_batchLocation", "boundArray", "texArray", "batchId", "elements", "ids", "count", "j", "tex", "loc", "bound", "_batchEnabled", "destroy", "extension", "type", "ExtensionType", "RendererSystem", "name", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\batch\\BatchSystem.ts"], "sourcesContent": ["import { extensions, ExtensionType } from '@pixi/extensions';\nimport { ObjectRenderer } from './ObjectRenderer';\n\nimport type { ExtensionMetadata } from '@pixi/extensions';\nimport type { Renderer } from '../Renderer';\nimport type { ISystem } from '../system/ISystem';\nimport type { BaseTexture } from '../textures/BaseTexture';\nimport type { BatchTextureArray } from './BatchTextureArray';\n\n/**\n * System plugin to the renderer to manage batching.\n * @memberof PIXI\n */\nexport class BatchSystem implements ISystem\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        type: ExtensionType.RendererSystem,\n        name: 'batch',\n    };\n\n    /** An empty renderer. */\n    public readonly emptyRenderer: ObjectRenderer;\n\n    /** The currently active ObjectRenderer. */\n    public currentRenderer: ObjectRenderer;\n    private renderer: Renderer;\n\n    /**\n     * @param renderer - The renderer this System works for.\n     */\n    constructor(renderer: Renderer)\n    {\n        this.renderer = renderer;\n        this.emptyRenderer = new ObjectRenderer(renderer);\n        this.currentRenderer = this.emptyRenderer;\n    }\n\n    /**\n     * Changes the current renderer to the one given in parameter\n     * @param objectRenderer - The object renderer to use.\n     */\n    setObjectRenderer(objectRenderer: ObjectRenderer): void\n    {\n        if (this.currentRenderer === objectRenderer)\n        {\n            return;\n        }\n\n        this.currentRenderer.stop();\n        this.currentRenderer = objectRenderer;\n\n        this.currentRenderer.start();\n    }\n\n    /**\n     * This should be called if you wish to do some custom rendering\n     * It will basically render anything that may be batched up such as sprites\n     */\n    flush(): void\n    {\n        this.setObjectRenderer(this.emptyRenderer);\n    }\n\n    /** Reset the system to an empty renderer */\n    reset(): void\n    {\n        this.setObjectRenderer(this.emptyRenderer);\n    }\n\n    /**\n     * Handy function for batch renderers: copies bound textures in first maxTextures locations to array\n     * sets actual _batchLocation for them\n     * @param arr - arr copy destination\n     * @param maxTextures - number of copied elements\n     */\n    copyBoundTextures(arr: BaseTexture[], maxTextures: number): void\n    {\n        const { boundTextures } = this.renderer.texture;\n\n        for (let i = maxTextures - 1; i >= 0; --i)\n        {\n            arr[i] = boundTextures[i] || null;\n            if (arr[i])\n            {\n                arr[i]._batchLocation = i;\n            }\n        }\n    }\n\n    /**\n     * Assigns batch locations to textures in array based on boundTextures state.\n     * All textures in texArray should have `_batchEnabled = _batchId`,\n     * and their count should be less than `maxTextures`.\n     * @param texArray - textures to bound\n     * @param boundTextures - current state of bound textures\n     * @param batchId - marker for _batchEnabled param of textures in texArray\n     * @param maxTextures - number of texture locations to manipulate\n     */\n    boundArray(texArray: BatchTextureArray, boundTextures: Array<BaseTexture>,\n        batchId: number, maxTextures: number): void\n    {\n        const { elements, ids, count } = texArray;\n        let j = 0;\n\n        for (let i = 0; i < count; i++)\n        {\n            const tex = elements[i];\n            const loc = tex._batchLocation;\n\n            if (loc >= 0 && loc < maxTextures\n                && boundTextures[loc] === tex)\n            {\n                ids[i] = loc;\n                continue;\n            }\n\n            while (j < maxTextures)\n            {\n                const bound = boundTextures[j];\n\n                if (bound && bound._batchEnabled === batchId\n                    && bound._batchLocation === j)\n                {\n                    j++;\n                    continue;\n                }\n\n                ids[i] = j;\n                tex._batchLocation = j;\n                boundTextures[j] = tex;\n                break;\n            }\n        }\n    }\n\n    /**\n     * @ignore\n     */\n    destroy(): void\n    {\n        this.renderer = null;\n    }\n}\n\nextensions.add(BatchSystem);\n"], "mappings": ";;AAaO,MAAMA,WAAA,CACb;EAAA;AAAA;AAAA;EAiBIC,YAAYC,QAAA,EACZ;IACS,KAAAA,QAAA,GAAWA,QAAA,EAChB,KAAKC,aAAA,GAAgB,IAAIC,cAAA,CAAeF,QAAQ,GAChD,KAAKG,eAAA,GAAkB,KAAKF,aAAA;EAChC;EAAA;AAAA;AAAA;AAAA;EAMAG,kBAAkBC,cAAA,EAClB;IACQ,KAAKF,eAAA,KAAoBE,cAAA,KAK7B,KAAKF,eAAA,CAAgBG,IAAA,CACrB,QAAKH,eAAA,GAAkBE,cAAA,EAEvB,KAAKF,eAAA,CAAgBI,KAAA,CAAM;EAC/B;EAAA;AAAA;AAAA;AAAA;EAMAC,MAAA,EACA;IACS,KAAAJ,iBAAA,CAAkB,KAAKH,aAAa;EAC7C;EAAA;EAGAQ,MAAA,EACA;IACS,KAAAL,iBAAA,CAAkB,KAAKH,aAAa;EAC7C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAS,kBAAkBC,GAAA,EAAoBC,WAAA,EACtC;IACI,MAAM;MAAEC;IAAkB,SAAKb,QAAA,CAASc,OAAA;IAExC,SAASC,CAAA,GAAIH,WAAA,GAAc,GAAGG,CAAA,IAAK,GAAG,EAAEA,CAAA,EAEpCJ,GAAA,CAAII,CAAC,IAAIF,aAAA,CAAcE,CAAC,KAAK,MACzBJ,GAAA,CAAII,CAAC,MAELJ,GAAA,CAAII,CAAC,EAAEC,cAAA,GAAiBD,CAAA;EAGpC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWAE,WAAWC,QAAA,EAA6BL,aAAA,EACpCM,OAAA,EAAiBP,WAAA,EACrB;IACI,MAAM;MAAEQ,QAAA;MAAUC,GAAA;MAAKC;IAAA,IAAUJ,QAAA;IACjC,IAAIK,CAAA,GAAI;IAER,SAASR,CAAA,GAAI,GAAGA,CAAA,GAAIO,KAAA,EAAOP,CAAA,IAC3B;MACI,MAAMS,GAAA,GAAMJ,QAAA,CAASL,CAAC;QAChBU,GAAA,GAAMD,GAAA,CAAIR,cAAA;MAEhB,IAAIS,GAAA,IAAO,KAAKA,GAAA,GAAMb,WAAA,IACfC,aAAA,CAAcY,GAAG,MAAMD,GAAA,EAC9B;QACIH,GAAA,CAAIN,CAAC,IAAIU,GAAA;QACT;MACJ;MAEA,OAAOF,CAAA,GAAIX,WAAA,GACX;QACU,MAAAc,KAAA,GAAQb,aAAA,CAAcU,CAAC;QAE7B,IAAIG,KAAA,IAASA,KAAA,CAAMC,aAAA,KAAkBR,OAAA,IAC9BO,KAAA,CAAMV,cAAA,KAAmBO,CAAA,EAChC;UACIA,CAAA;UACA;QACJ;QAEIF,GAAA,CAAAN,CAAC,IAAIQ,CAAA,EACTC,GAAA,CAAIR,cAAA,GAAiBO,CAAA,EACrBV,aAAA,CAAcU,CAAC,IAAIC,GAAA;QACnB;MACJ;IACJ;EACJ;EAAA;AAAA;AAAA;EAKAI,QAAA,EACA;IACI,KAAK5B,QAAA,GAAW;EACpB;AACJ;AAlIaF,WAAA,CAGF+B,SAAA,GAA+B;EAClCC,IAAA,EAAMC,aAAA,CAAcC,cAAA;EACpBC,IAAA,EAAM;AACV;AA8HJC,UAAA,CAAWC,GAAA,CAAIrC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}