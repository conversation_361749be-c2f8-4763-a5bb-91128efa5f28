{"ast": null, "code": "import { ExtensionType, extensions } from \"@pixi/core\";\nconst imageFormats = [\"png\", \"jpg\", \"jpeg\"],\n  detectDefaults = {\n    extension: {\n      type: ExtensionType.DetectionParser,\n      priority: -1\n    },\n    test: () => Promise.resolve(!0),\n    add: async formats => [...formats, ...imageFormats],\n    remove: async formats => formats.filter(f => !imageFormats.includes(f))\n  };\nextensions.add(detectDefaults);\nexport { detectDefaults };", "map": {"version": 3, "names": ["imageFormats", "detectDefaults", "extension", "type", "ExtensionType", "DetectionParser", "priority", "test", "Promise", "resolve", "add", "formats", "remove", "filter", "f", "includes", "extensions"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\assets\\src\\detections\\parsers\\detectDefaults.ts"], "sourcesContent": ["import { extensions, ExtensionType } from '@pixi/core';\n\nimport type { FormatDetectionParser } from '..';\n\nconst imageFormats = ['png', 'jpg', 'jpeg'];\n\nexport const detectDefaults = {\n    extension: {\n        type: ExtensionType.DetectionParser,\n        priority: -1,\n    },\n    test: (): Promise<boolean> => Promise.resolve(true),\n    add: async (formats) => [...formats, ...imageFormats],\n    remove: async (formats) => formats.filter((f) => !imageFormats.includes(f)),\n} as FormatDetectionParser;\n\nextensions.add(detectDefaults);\n"], "mappings": ";AAIA,MAAMA,YAAA,GAAe,CAAC,OAAO,OAAO,MAAM;EAE7BC,cAAA,GAAiB;IAC1BC,SAAA,EAAW;MACPC,IAAA,EAAMC,aAAA,CAAcC,eAAA;MACpBC,QAAA,EAAU;IACd;IACAC,IAAA,EAAMA,CAAA,KAAwBC,OAAA,CAAQC,OAAA,CAAQ,EAAI;IAClDC,GAAA,EAAK,MAAOC,OAAA,IAAY,CAAC,GAAGA,OAAA,EAAS,GAAGX,YAAY;IACpDY,MAAA,EAAQ,MAAOD,OAAA,IAAYA,OAAA,CAAQE,MAAA,CAAQC,CAAA,IAAM,CAACd,YAAA,CAAae,QAAA,CAASD,CAAC,CAAC;EAC9E;AAEAE,UAAA,CAAWN,GAAA,CAAIT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}