# Empires & Revolutions

A grand strategy game inspired by Europa Universalis IV and Victoria 3. Built as a web application with React frontend and Python backend.

## Project Setup
1. Set up the Python backend (see backend/README.md)
2. Set up the React frontend (see frontend/README.md)
3. See DESIGN.md for system breakdowns.

## Version Control
- Recommended: Git (standard .gitignore for Python/Node.js projects)

## Core Systems
- Backend (Python/FastAPI): Game logic and data management
- Frontend (React/PixiJS): Interactive map and user interface
- MapManager: World map and provinces
- PopSystem: Population simulation
- EconomySystem: Resources and production
- DiplomacySystem: Alliances and wars
- EventSystem: Dynamic events

## MVP Goals
- Interactive web-based world map
- Basic pop, economy, diplomacy, and warfare systems
- Core event system
- Turn-based gameplay

---
