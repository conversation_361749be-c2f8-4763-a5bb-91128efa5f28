{"ast": null, "code": "import { deprecation } from \"../logging/deprecation.mjs\";\nfunction skipHello() {\n  deprecation(\"7.0.0\", \"skip<PERSON><PERSON> is deprecated, please use settings.RENDER_OPTIONS.hello\");\n}\nfunction sayHello() {\n  deprecation(\"7.0.0\", `say<PERSON><PERSON> is deprecated, please use <PERSON><PERSON><PERSON>'s \"hello\" option`);\n}\nexport { sayHello, skipHello };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "deprecation", "sayHello"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\utils\\src\\browser\\hello.ts"], "sourcesContent": ["import { deprecation } from '../logging/deprecation';\n\n/**\n * @function skipHello\n * @memberof PIXI.utils\n * @deprecated since 7.0.0\n */\nexport function skipHello(): void\n{\n    if (process.env.DEBUG)\n    {\n        deprecation('7.0.0', 'skip<PERSON><PERSON> is deprecated, please use settings.RENDER_OPTIONS.hello');\n    }\n}\n\n/**\n * @static\n * @function sayHello\n * @memberof PIXI.utils\n * @deprecated since 7.0.0\n */\nexport function sayHello(): void\n{\n    if (process.env.DEBUG)\n    {\n        deprecation('7.0.0', 'say<PERSON><PERSON> is deprecated, please use <PERSON><PERSON><PERSON>\\'s \"hello\" option');\n    }\n}\n"], "mappings": ";AAOO,SAASA,UAAA,EAChB;EAGQC,WAAA,CAAY,SAAS,mEAAmE;AAEhG;AAQO,SAASC,SAAA,EAChB;EAGQD,WAAA,CAAY,SAAS,8DAA+D;AAE5F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}