using UnityEngine;
using System.Collections.Generic;

[System.Serializable]
public class EconomicData
{
    public float taxIncome;
    public float tradeIncome;
    public float productionIncome;
    public float militaryExpenses;
    public float administrativeExpenses;
    public float totalIncome;
    public float totalExpenses;
    public float balance;
}

public class EconomySystem : MonoBehaviour
{
    [Header("Economic Settings")]
    public float baseTaxRate = 0.1f; // 10% tax rate
    public float tradeEfficiency = 0.8f;
    public float productionEfficiency = 1.0f;
    public float militaryUpkeepCost = 0.05f; // 5% of population
    public float adminUpkeepCost = 0.02f; // 2% of population

    private Dictionary<string, EconomicData> provinceEconomies = new Dictionary<string, EconomicData>();
    private PopSystem popSystem;

    void Start()
    {
        popSystem = FindObjectOfType<PopSystem>();
        InitializeEconomies();
    }

    void InitializeEconomies()
    {
        var mapManager = FindObjectOfType<MapManager>();
        if (mapManager != null)
        {
            var provinces = mapManager.GetAllProvinces();
            foreach (var province in provinces)
            {
                InitializeProvinceEconomy(province);
            }
        }

        Debug.Log("EconomySystem initialized for all provinces.");
    }

    void InitializeProvinceEconomy(Province province)
    {
        if (province == null) return;

        var economicData = new EconomicData();
        CalculateProvinceEconomy(province, economicData);
        provinceEconomies[province.Name] = economicData;
    }

    public void UpdateSystem()
    {
        var mapManager = FindObjectOfType<MapManager>();
        if (mapManager != null)
        {
            var provinces = mapManager.GetAllProvinces();
            foreach (var province in provinces)
            {
                UpdateProvinceEconomy(province);
            }
        }

        Debug.Log("EconomySystem updated for all provinces.");
    }

    void UpdateProvinceEconomy(Province province)
    {
        if (province == null || !provinceEconomies.ContainsKey(province.Name)) return;

        var economicData = provinceEconomies[province.Name];
        CalculateProvinceEconomy(province, economicData);

        // Update province resources based on economic balance
        province.Resources += Mathf.RoundToInt(economicData.balance);
        province.Resources = Mathf.Max(0, province.Resources); // Don't go negative
    }

    void CalculateProvinceEconomy(Province province, EconomicData economicData)
    {
        int population = popSystem != null ? popSystem.GetTotalPopulation(province.Name) : province.Population;

        // Calculate income
        economicData.taxIncome = population * baseTaxRate;
        economicData.tradeIncome = province.Resources * 0.1f * tradeEfficiency;
        economicData.productionIncome = population * 0.05f * productionEfficiency;
        economicData.totalIncome = economicData.taxIncome + economicData.tradeIncome + economicData.productionIncome;

        // Calculate expenses
        economicData.militaryExpenses = population * militaryUpkeepCost;
        economicData.administrativeExpenses = population * adminUpkeepCost;
        economicData.totalExpenses = economicData.militaryExpenses + economicData.administrativeExpenses;

        // Calculate balance
        economicData.balance = economicData.totalIncome - economicData.totalExpenses;
    }

    public EconomicData GetProvinceEconomy(string provinceName)
    {
        if (provinceEconomies.ContainsKey(provinceName))
        {
            return provinceEconomies[provinceName];
        }
        return new EconomicData();
    }

    public float GetTotalIncome()
    {
        float total = 0f;
        foreach (var economy in provinceEconomies.Values)
        {
            total += economy.totalIncome;
        }
        return total;
    }

    public float GetTotalExpenses()
    {
        float total = 0f;
        foreach (var economy in provinceEconomies.Values)
        {
            total += economy.totalExpenses;
        }
        return total;
    }

    public float GetTotalBalance()
    {
        return GetTotalIncome() - GetTotalExpenses();
    }

    public void SetTaxRate(float newRate)
    {
        baseTaxRate = Mathf.Clamp01(newRate);
        Debug.Log($"Tax rate set to {baseTaxRate:P1}");
    }

    public void SetTradeEfficiency(float newEfficiency)
    {
        tradeEfficiency = Mathf.Clamp01(newEfficiency);
        Debug.Log($"Trade efficiency set to {tradeEfficiency:P1}");
    }
}
