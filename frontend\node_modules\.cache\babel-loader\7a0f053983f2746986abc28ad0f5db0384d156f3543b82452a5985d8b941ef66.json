{"ast": null, "code": "import { Geo<PERSON>, Buffer, TYPES } from \"@pixi/core\";\nclass MeshGeometry extends Geometry {\n  /**\n   * @param {Float32Array|number[]} [vertices] - Positional data on geometry.\n   * @param {Float32Array|number[]} [uvs] - Texture UVs.\n   * @param {Uint16Array|number[]} [index] - IndexBuffer\n   */\n  constructor(vertices, uvs, index) {\n    super();\n    const verticesBuffer = new Buffer(vertices),\n      uvsBuffer = new Buffer(uvs, !0),\n      indexBuffer = new Buffer(index, !0, !0);\n    this.addAttribute(\"aVertexPosition\", verticesBuffer, 2, !1, TYPES.FLOAT).addAttribute(\"aTextureCoord\", uvsBuffer, 2, !1, TYPES.FLOAT).addIndex(indexBuffer), this._updateId = -1;\n  }\n  /**\n   * If the vertex position is updated.\n   * @readonly\n   * @private\n   */\n  get vertexDirtyId() {\n    return this.buffers[0]._updateID;\n  }\n}\nexport { MeshGeometry };", "map": {"version": 3, "names": ["MeshGeometry", "Geometry", "constructor", "vertices", "uvs", "index", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "uvs<PERSON><PERSON><PERSON>", "indexBuffer", "addAttribute", "TYPES", "FLOAT", "addIndex", "_updateId", "vertexDirtyId", "buffers", "_updateID"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\mesh\\src\\MeshGeometry.ts"], "sourcesContent": ["import { Buffer, Geometry, TYPES } from '@pixi/core';\n\nimport type { IArrayBuffer } from '@pixi/core';\n\n/**\n * Standard 2D geometry used in PixiJS.\n *\n * Geometry can be defined without passing in a style or data if required.\n * @example\n * import { Geometry } from 'pixi.js';\n *\n * const geometry = new Geometry();\n *\n * geometry.addAttribute('positions', [0, 0, 100, 0, 100, 100, 0, 100], 2);\n * geometry.addAttribute('uvs', [0, 0, 1, 0, 1, 1, 0, 1], 2);\n * geometry.addIndex([0, 1, 2, 1, 3, 2]);\n * @memberof PIXI\n */\nexport class MeshGeometry extends Geometry\n{\n    // Internal-only properties\n    /**\n     * Dirty flag to limit update calls on Mesh. For example,\n     * limiting updates on a single Mesh instance with a shared Geometry\n     * within the render loop.\n     * @private\n     * @default -1\n     */\n    _updateId: number;\n\n    /**\n     * @param {Float32Array|number[]} [vertices] - Positional data on geometry.\n     * @param {Float32Array|number[]} [uvs] - Texture UVs.\n     * @param {Uint16Array|number[]} [index] - IndexBuffer\n     */\n    constructor(vertices?: IArrayBuffer, uvs?: IArrayBuffer, index?: IArrayBuffer)\n    {\n        super();\n\n        const verticesBuffer = new Buffer(vertices);\n        const uvsBuffer = new Buffer(uvs, true);\n        const indexBuffer = new Buffer(index, true, true);\n\n        this.addAttribute('aVertexPosition', verticesBuffer, 2, false, TYPES.FLOAT)\n            .addAttribute('aTextureCoord', uvsBuffer, 2, false, TYPES.FLOAT)\n            .addIndex(indexBuffer);\n\n        this._updateId = -1;\n    }\n\n    /**\n     * If the vertex position is updated.\n     * @readonly\n     * @private\n     */\n    get vertexDirtyId(): number\n    {\n        return this.buffers[0]._updateID;\n    }\n}\n"], "mappings": ";AAkBO,MAAMA,YAAA,SAAqBC,QAAA,CAClC;EAAA;AAAA;AAAA;AAAA;AAAA;EAgBIC,YAAYC,QAAA,EAAyBC,GAAA,EAAoBC,KAAA,EACzD;IACU;IAEN,MAAMC,cAAA,GAAiB,IAAIC,MAAA,CAAOJ,QAAQ;MACpCK,SAAA,GAAY,IAAID,MAAA,CAAOH,GAAA,EAAK,EAAI;MAChCK,WAAA,GAAc,IAAIF,MAAA,CAAOF,KAAA,EAAO,IAAM,EAAI;IAE3C,KAAAK,YAAA,CAAa,mBAAmBJ,cAAA,EAAgB,GAAG,IAAOK,KAAA,CAAMC,KAAK,EACrEF,YAAA,CAAa,iBAAiBF,SAAA,EAAW,GAAG,IAAOG,KAAA,CAAMC,KAAK,EAC9DC,QAAA,CAASJ,WAAW,GAEzB,KAAKK,SAAA,GAAY;EACrB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,IAAIC,cAAA,EACJ;IACW,YAAKC,OAAA,CAAQ,CAAC,EAAEC,SAAA;EAC3B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}