{"ast": null, "code": "import { BUFFER_TYPE } from \"@pixi/constants\";\nimport { <PERSON> } from \"@pixi/runner\";\nimport { getBufferType } from \"@pixi/utils\";\nimport { Attribute } from \"./Attribute.mjs\";\nimport { Buffer } from \"./Buffer.mjs\";\nimport { interleaveTypedArrays } from \"./utils/interleaveTypedArrays.mjs\";\nconst byteSizeMap = {\n  5126: 4,\n  5123: 2,\n  5121: 1\n};\nlet UID = 0;\nconst map = {\n  Float32Array,\n  Uint32Array,\n  Int32Array,\n  Uint8Array,\n  Uint16Array\n};\nclass Geometry {\n  /**\n   * @param buffers - An array of buffers. optional.\n   * @param attributes - Of the geometry, optional structure of the attributes layout\n   */\n  constructor(buffers = [], attributes = {}) {\n    this.buffers = buffers, this.indexBuffer = null, this.attributes = attributes, this.glVertexArrayObjects = {}, this.id = UID++, this.instanced = !1, this.instanceCount = 1, this.disposeRunner = new Runner(\"disposeGeometry\"), this.refCount = 0;\n  }\n  /**\n   *\n   * Adds an attribute to the geometry\n   * Note: `stride` and `start` should be `undefined` if you dont know them, not 0!\n   * @param id - the name of the attribute (matching up to a shader)\n   * @param {PIXI.Buffer|number[]} buffer - the buffer that holds the data of the attribute . You can also provide an Array and a buffer will be created from it.\n   * @param size - the size of the attribute. If you have 2 floats per vertex (eg position x and y) this would be 2\n   * @param normalized - should the data be normalized.\n   * @param [type=PIXI.TYPES.FLOAT] - what type of number is the attribute. Check {@link PIXI.TYPES} to see the ones available\n   * @param [stride=0] - How far apart, in bytes, the start of each value is. (used for interleaving data)\n   * @param [start=0] - How far into the array to start reading values (used for interleaving data)\n   * @param instance - Instancing flag\n   * @returns - Returns self, useful for chaining.\n   */\n  addAttribute(id, buffer, size = 0, normalized = !1, type, stride, start, instance = !1) {\n    if (!buffer) throw new Error(\"You must pass a buffer when creating an attribute\");\n    buffer instanceof Buffer || (buffer instanceof Array && (buffer = new Float32Array(buffer)), buffer = new Buffer(buffer));\n    const ids = id.split(\"|\");\n    if (ids.length > 1) {\n      for (let i = 0; i < ids.length; i++) this.addAttribute(ids[i], buffer, size, normalized, type);\n      return this;\n    }\n    let bufferIndex = this.buffers.indexOf(buffer);\n    return bufferIndex === -1 && (this.buffers.push(buffer), bufferIndex = this.buffers.length - 1), this.attributes[id] = new Attribute(bufferIndex, size, normalized, type, stride, start, instance), this.instanced = this.instanced || instance, this;\n  }\n  /**\n   * Returns the requested attribute.\n   * @param id - The name of the attribute required\n   * @returns - The attribute requested.\n   */\n  getAttribute(id) {\n    return this.attributes[id];\n  }\n  /**\n   * Returns the requested buffer.\n   * @param id - The name of the buffer required.\n   * @returns - The buffer requested.\n   */\n  getBuffer(id) {\n    return this.buffers[this.getAttribute(id).buffer];\n  }\n  /**\n   *\n   * Adds an index buffer to the geometry\n   * The index buffer contains integers, three for each triangle in the geometry, which reference the various attribute buffers (position, colour, UV coordinates, other UV coordinates, normal, …). There is only ONE index buffer.\n   * @param {PIXI.Buffer|number[]} [buffer] - The buffer that holds the data of the index buffer. You can also provide an Array and a buffer will be created from it.\n   * @returns - Returns self, useful for chaining.\n   */\n  addIndex(buffer) {\n    return buffer instanceof Buffer || (buffer instanceof Array && (buffer = new Uint16Array(buffer)), buffer = new Buffer(buffer)), buffer.type = BUFFER_TYPE.ELEMENT_ARRAY_BUFFER, this.indexBuffer = buffer, this.buffers.includes(buffer) || this.buffers.push(buffer), this;\n  }\n  /**\n   * Returns the index buffer\n   * @returns - The index buffer.\n   */\n  getIndex() {\n    return this.indexBuffer;\n  }\n  /**\n   * This function modifies the structure so that all current attributes become interleaved into a single buffer\n   * This can be useful if your model remains static as it offers a little performance boost\n   * @returns - Returns self, useful for chaining.\n   */\n  interleave() {\n    if (this.buffers.length === 1 || this.buffers.length === 2 && this.indexBuffer) return this;\n    const arrays = [],\n      sizes = [],\n      interleavedBuffer = new Buffer();\n    let i;\n    for (i in this.attributes) {\n      const attribute = this.attributes[i],\n        buffer = this.buffers[attribute.buffer];\n      arrays.push(buffer.data), sizes.push(attribute.size * byteSizeMap[attribute.type] / 4), attribute.buffer = 0;\n    }\n    for (interleavedBuffer.data = interleaveTypedArrays(arrays, sizes), i = 0; i < this.buffers.length; i++) this.buffers[i] !== this.indexBuffer && this.buffers[i].destroy();\n    return this.buffers = [interleavedBuffer], this.indexBuffer && this.buffers.push(this.indexBuffer), this;\n  }\n  /** Get the size of the geometries, in vertices. */\n  getSize() {\n    for (const i in this.attributes) {\n      const attribute = this.attributes[i];\n      return this.buffers[attribute.buffer].data.length / (attribute.stride / 4 || attribute.size);\n    }\n    return 0;\n  }\n  /** Disposes WebGL resources that are connected to this geometry. */\n  dispose() {\n    this.disposeRunner.emit(this, !1);\n  }\n  /** Destroys the geometry. */\n  destroy() {\n    this.dispose(), this.buffers = null, this.indexBuffer = null, this.attributes = null;\n  }\n  /**\n   * Returns a clone of the geometry.\n   * @returns - A new clone of this geometry.\n   */\n  clone() {\n    const geometry = new Geometry();\n    for (let i = 0; i < this.buffers.length; i++) geometry.buffers[i] = new Buffer(this.buffers[i].data.slice(0));\n    for (const i in this.attributes) {\n      const attrib = this.attributes[i];\n      geometry.attributes[i] = new Attribute(attrib.buffer, attrib.size, attrib.normalized, attrib.type, attrib.stride, attrib.start, attrib.instance);\n    }\n    return this.indexBuffer && (geometry.indexBuffer = geometry.buffers[this.buffers.indexOf(this.indexBuffer)], geometry.indexBuffer.type = BUFFER_TYPE.ELEMENT_ARRAY_BUFFER), geometry;\n  }\n  /**\n   * Merges an array of geometries into a new single one.\n   *\n   * Geometry attribute styles must match for this operation to work.\n   * @param geometries - array of geometries to merge\n   * @returns - Shiny new geometry!\n   */\n  static merge(geometries) {\n    const geometryOut = new Geometry(),\n      arrays = [],\n      sizes = [],\n      offsets = [];\n    let geometry;\n    for (let i = 0; i < geometries.length; i++) {\n      geometry = geometries[i];\n      for (let j = 0; j < geometry.buffers.length; j++) sizes[j] = sizes[j] || 0, sizes[j] += geometry.buffers[j].data.length, offsets[j] = 0;\n    }\n    for (let i = 0; i < geometry.buffers.length; i++) arrays[i] = new map[getBufferType(geometry.buffers[i].data)](sizes[i]), geometryOut.buffers[i] = new Buffer(arrays[i]);\n    for (let i = 0; i < geometries.length; i++) {\n      geometry = geometries[i];\n      for (let j = 0; j < geometry.buffers.length; j++) arrays[j].set(geometry.buffers[j].data, offsets[j]), offsets[j] += geometry.buffers[j].data.length;\n    }\n    if (geometryOut.attributes = geometry.attributes, geometry.indexBuffer) {\n      geometryOut.indexBuffer = geometryOut.buffers[geometry.buffers.indexOf(geometry.indexBuffer)], geometryOut.indexBuffer.type = BUFFER_TYPE.ELEMENT_ARRAY_BUFFER;\n      let offset = 0,\n        stride = 0,\n        offset2 = 0,\n        bufferIndexToCount = 0;\n      for (let i = 0; i < geometry.buffers.length; i++) if (geometry.buffers[i] !== geometry.indexBuffer) {\n        bufferIndexToCount = i;\n        break;\n      }\n      for (const i in geometry.attributes) {\n        const attribute = geometry.attributes[i];\n        (attribute.buffer | 0) === bufferIndexToCount && (stride += attribute.size * byteSizeMap[attribute.type] / 4);\n      }\n      for (let i = 0; i < geometries.length; i++) {\n        const indexBufferData = geometries[i].indexBuffer.data;\n        for (let j = 0; j < indexBufferData.length; j++) geometryOut.indexBuffer.data[j + offset2] += offset;\n        offset += geometries[i].buffers[bufferIndexToCount].data.length / stride, offset2 += indexBufferData.length;\n      }\n    }\n    return geometryOut;\n  }\n}\nexport { Geometry };", "map": {"version": 3, "names": ["byteSizeMap", "UID", "map", "Float32Array", "Uint32Array", "Int32Array", "Uint8Array", "Uint16Array", "Geometry", "constructor", "buffers", "attributes", "indexBuffer", "glVertexArrayObjects", "id", "instanced", "instanceCount", "dispose<PERSON><PERSON><PERSON>", "Runner", "refCount", "addAttribute", "buffer", "size", "normalized", "type", "stride", "start", "instance", "Error", "<PERSON><PERSON><PERSON>", "Array", "ids", "split", "length", "i", "bufferIndex", "indexOf", "push", "Attribute", "getAttribute", "<PERSON><PERSON><PERSON><PERSON>", "addIndex", "BUFFER_TYPE", "ELEMENT_ARRAY_BUFFER", "includes", "getIndex", "interleave", "arrays", "sizes", "interleavedBuffer", "attribute", "data", "interleaveTypedArrays", "destroy", "getSize", "dispose", "emit", "clone", "geometry", "slice", "attrib", "merge", "geometries", "geometryOut", "offsets", "j", "getBufferType", "set", "offset", "offset2", "bufferIndexToCount", "indexBufferData"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\geometry\\Geometry.ts"], "sourcesContent": ["import { BUFFER_TYPE } from '@pixi/constants';\nimport { Runner } from '@pixi/runner';\nimport { getBufferType } from '@pixi/utils';\nimport { Attribute } from './Attribute';\nimport { Buffer } from './Buffer';\nimport { interleaveTypedArrays } from './utils/interleaveTypedArrays';\n\nimport type { TYPES } from '@pixi/constants';\nimport type { Dict } from '@pixi/utils';\nimport type { IArrayBuffer } from './Buffer';\n\nconst byteSizeMap: {[key: number]: number} = { 5126: 4, 5123: 2, 5121: 1 };\nlet UID = 0;\n\n/* eslint-disable object-shorthand */\nconst map: Dict<any> = {\n    Float32Array: Float32Array,\n    Uint32Array: Uint32Array,\n    Int32Array: Int32Array,\n    Uint8Array: Uint8Array,\n    Uint16Array: Uint16Array,\n};\n\n/* eslint-disable max-len */\n\n/**\n * The Geometry represents a model. It consists of two components:\n * - GeometryStyle - The structure of the model such as the attributes layout\n * - GeometryData - the data of the model - this consists of buffers.\n * This can include anything from positions, uvs, normals, colors etc.\n *\n * Geometry can be defined without passing in a style or data if required (thats how I prefer!)\n * @example\n * import { Geometry } from 'pixi.js';\n *\n * const geometry = new Geometry();\n *\n * geometry.addAttribute('positions', [0, 0, 100, 0, 0, 100, 100, 100], 2);\n * geometry.addAttribute('uvs', [0, 0, 1, 0, 0, 1, 1, 1], 2);\n * geometry.addIndex([0, 1, 2, 1, 3, 2]);\n * @memberof PIXI\n */\nexport class Geometry\n{\n    public buffers: Array<Buffer>;\n    public indexBuffer: Buffer;\n    public attributes: {[key: string]: Attribute};\n    public id: number;\n\n    /** Whether the geometry is instanced. */\n    public instanced: boolean;\n\n    /**\n     * Number of instances in this geometry, pass it to `GeometrySystem.draw()`.\n     * @default 1\n     */\n    public instanceCount: number;\n\n    /**\n     * A map of renderer IDs to webgl VAOs\n     * @type {object}\n     */\n    glVertexArrayObjects: {[key: number]: {[key: string]: WebGLVertexArrayObject}};\n    disposeRunner: Runner;\n\n    /** Count of existing (not destroyed) meshes that reference this geometry. */\n    refCount: number;\n\n    /**\n     * @param buffers - An array of buffers. optional.\n     * @param attributes - Of the geometry, optional structure of the attributes layout\n     */\n    constructor(buffers: Array<Buffer> = [], attributes: {[key: string]: Attribute} = {})\n    {\n        this.buffers = buffers;\n\n        this.indexBuffer = null;\n\n        this.attributes = attributes;\n\n        this.glVertexArrayObjects = {};\n\n        this.id = UID++;\n\n        this.instanced = false;\n        this.instanceCount = 1;\n\n        this.disposeRunner = new Runner('disposeGeometry');\n        this.refCount = 0;\n    }\n\n    /**\n     *\n     * Adds an attribute to the geometry\n     * Note: `stride` and `start` should be `undefined` if you dont know them, not 0!\n     * @param id - the name of the attribute (matching up to a shader)\n     * @param {PIXI.Buffer|number[]} buffer - the buffer that holds the data of the attribute . You can also provide an Array and a buffer will be created from it.\n     * @param size - the size of the attribute. If you have 2 floats per vertex (eg position x and y) this would be 2\n     * @param normalized - should the data be normalized.\n     * @param [type=PIXI.TYPES.FLOAT] - what type of number is the attribute. Check {@link PIXI.TYPES} to see the ones available\n     * @param [stride=0] - How far apart, in bytes, the start of each value is. (used for interleaving data)\n     * @param [start=0] - How far into the array to start reading values (used for interleaving data)\n     * @param instance - Instancing flag\n     * @returns - Returns self, useful for chaining.\n     */\n    addAttribute(id: string, buffer: Buffer | Float32Array | Uint32Array | Array<number>, size = 0, normalized = false,\n        type?: TYPES, stride?: number, start?: number, instance = false): this\n    {\n        if (!buffer)\n        {\n            throw new Error('You must pass a buffer when creating an attribute');\n        }\n\n        // check if this is a buffer!\n        if (!(buffer instanceof Buffer))\n        {\n            // its an array!\n            if (buffer instanceof Array)\n            {\n                buffer = new Float32Array(buffer);\n            }\n\n            buffer = new Buffer(buffer);\n        }\n\n        const ids = id.split('|');\n\n        if (ids.length > 1)\n        {\n            for (let i = 0; i < ids.length; i++)\n            {\n                this.addAttribute(ids[i], buffer, size, normalized, type);\n            }\n\n            return this;\n        }\n\n        let bufferIndex = this.buffers.indexOf(buffer);\n\n        if (bufferIndex === -1)\n        {\n            this.buffers.push(buffer);\n            bufferIndex = this.buffers.length - 1;\n        }\n\n        this.attributes[id] = new Attribute(bufferIndex, size, normalized, type, stride, start, instance);\n\n        // assuming that if there is instanced data then this will be drawn with instancing!\n        this.instanced = this.instanced || instance;\n\n        return this;\n    }\n\n    /**\n     * Returns the requested attribute.\n     * @param id - The name of the attribute required\n     * @returns - The attribute requested.\n     */\n    getAttribute(id: string): Attribute\n    {\n        return this.attributes[id];\n    }\n\n    /**\n     * Returns the requested buffer.\n     * @param id - The name of the buffer required.\n     * @returns - The buffer requested.\n     */\n    getBuffer(id: string): Buffer\n    {\n        return this.buffers[this.getAttribute(id).buffer];\n    }\n\n    /**\n     *\n     * Adds an index buffer to the geometry\n     * The index buffer contains integers, three for each triangle in the geometry, which reference the various attribute buffers (position, colour, UV coordinates, other UV coordinates, normal, …). There is only ONE index buffer.\n     * @param {PIXI.Buffer|number[]} [buffer] - The buffer that holds the data of the index buffer. You can also provide an Array and a buffer will be created from it.\n     * @returns - Returns self, useful for chaining.\n     */\n    addIndex(buffer?: Buffer | IArrayBuffer | number[]): Geometry\n    {\n        if (!(buffer instanceof Buffer))\n        {\n            // its an array!\n            if (buffer instanceof Array)\n            {\n                buffer = new Uint16Array(buffer);\n            }\n\n            buffer = new Buffer(buffer);\n        }\n\n        buffer.type = BUFFER_TYPE.ELEMENT_ARRAY_BUFFER;\n\n        this.indexBuffer = buffer;\n\n        if (!this.buffers.includes(buffer))\n        {\n            this.buffers.push(buffer);\n        }\n\n        return this;\n    }\n\n    /**\n     * Returns the index buffer\n     * @returns - The index buffer.\n     */\n    getIndex(): Buffer\n    {\n        return this.indexBuffer;\n    }\n\n    /**\n     * This function modifies the structure so that all current attributes become interleaved into a single buffer\n     * This can be useful if your model remains static as it offers a little performance boost\n     * @returns - Returns self, useful for chaining.\n     */\n    interleave(): Geometry\n    {\n        // a simple check to see if buffers are already interleaved..\n        if (this.buffers.length === 1 || (this.buffers.length === 2 && this.indexBuffer)) return this;\n\n        // assume already that no buffers are interleaved\n        const arrays = [];\n        const sizes = [];\n        const interleavedBuffer = new Buffer();\n        let i;\n\n        for (i in this.attributes)\n        {\n            const attribute = this.attributes[i];\n\n            const buffer = this.buffers[attribute.buffer];\n\n            arrays.push(buffer.data);\n\n            sizes.push((attribute.size * byteSizeMap[attribute.type]) / 4);\n\n            attribute.buffer = 0;\n        }\n\n        interleavedBuffer.data = interleaveTypedArrays(arrays, sizes);\n\n        for (i = 0; i < this.buffers.length; i++)\n        {\n            if (this.buffers[i] !== this.indexBuffer)\n            {\n                this.buffers[i].destroy();\n            }\n        }\n\n        this.buffers = [interleavedBuffer];\n\n        if (this.indexBuffer)\n        {\n            this.buffers.push(this.indexBuffer);\n        }\n\n        return this;\n    }\n\n    /** Get the size of the geometries, in vertices. */\n    getSize(): number\n    {\n        for (const i in this.attributes)\n        {\n            const attribute = this.attributes[i];\n            const buffer = this.buffers[attribute.buffer];\n\n            return (buffer.data as any).length / ((attribute.stride / 4) || attribute.size);\n        }\n\n        return 0;\n    }\n\n    /** Disposes WebGL resources that are connected to this geometry. */\n    dispose(): void\n    {\n        this.disposeRunner.emit(this, false);\n    }\n\n    /** Destroys the geometry. */\n    destroy(): void\n    {\n        this.dispose();\n\n        this.buffers = null;\n        this.indexBuffer = null;\n        this.attributes = null;\n    }\n\n    /**\n     * Returns a clone of the geometry.\n     * @returns - A new clone of this geometry.\n     */\n    clone(): Geometry\n    {\n        const geometry = new Geometry();\n\n        for (let i = 0; i < this.buffers.length; i++)\n        {\n            geometry.buffers[i] = new Buffer(this.buffers[i].data.slice(0));\n        }\n\n        for (const i in this.attributes)\n        {\n            const attrib = this.attributes[i];\n\n            geometry.attributes[i] = new Attribute(\n                attrib.buffer,\n                attrib.size,\n                attrib.normalized,\n                attrib.type,\n                attrib.stride,\n                attrib.start,\n                attrib.instance\n            );\n        }\n\n        if (this.indexBuffer)\n        {\n            geometry.indexBuffer = geometry.buffers[this.buffers.indexOf(this.indexBuffer)];\n            geometry.indexBuffer.type = BUFFER_TYPE.ELEMENT_ARRAY_BUFFER;\n        }\n\n        return geometry;\n    }\n\n    /**\n     * Merges an array of geometries into a new single one.\n     *\n     * Geometry attribute styles must match for this operation to work.\n     * @param geometries - array of geometries to merge\n     * @returns - Shiny new geometry!\n     */\n    static merge(geometries: Array<Geometry>): Geometry\n    {\n        // todo add a geometry check!\n        // also a size check.. cant be too big!]\n\n        const geometryOut = new Geometry();\n\n        const arrays = [];\n        const sizes: Array<number> = [];\n        const offsets = [];\n\n        let geometry;\n\n        // pass one.. get sizes..\n        for (let i = 0; i < geometries.length; i++)\n        {\n            geometry = geometries[i];\n\n            for (let j = 0; j < geometry.buffers.length; j++)\n            {\n                sizes[j] = sizes[j] || 0;\n                sizes[j] += geometry.buffers[j].data.length;\n                offsets[j] = 0;\n            }\n        }\n\n        // build the correct size arrays..\n        for (let i = 0; i < geometry.buffers.length; i++)\n        {\n            // TODO types!\n            arrays[i] = new map[getBufferType(geometry.buffers[i].data)](sizes[i]);\n            geometryOut.buffers[i] = new Buffer(arrays[i]);\n        }\n\n        // pass to set data..\n        for (let i = 0; i < geometries.length; i++)\n        {\n            geometry = geometries[i];\n\n            for (let j = 0; j < geometry.buffers.length; j++)\n            {\n                arrays[j].set(geometry.buffers[j].data, offsets[j]);\n                offsets[j] += geometry.buffers[j].data.length;\n            }\n        }\n\n        geometryOut.attributes = geometry.attributes;\n\n        if (geometry.indexBuffer)\n        {\n            geometryOut.indexBuffer = geometryOut.buffers[geometry.buffers.indexOf(geometry.indexBuffer)];\n            geometryOut.indexBuffer.type = BUFFER_TYPE.ELEMENT_ARRAY_BUFFER;\n\n            let offset = 0;\n            let stride = 0;\n            let offset2 = 0;\n            let bufferIndexToCount = 0;\n\n            // get a buffer\n            for (let i = 0; i < geometry.buffers.length; i++)\n            {\n                if (geometry.buffers[i] !== geometry.indexBuffer)\n                {\n                    bufferIndexToCount = i;\n                    break;\n                }\n            }\n\n            // figure out the stride of one buffer..\n            for (const i in geometry.attributes)\n            {\n                const attribute = geometry.attributes[i];\n\n                if ((attribute.buffer | 0) === bufferIndexToCount)\n                {\n                    stride += ((attribute.size * byteSizeMap[attribute.type]) / 4);\n                }\n            }\n\n            // time to off set all indexes..\n            for (let i = 0; i < geometries.length; i++)\n            {\n                const indexBufferData = geometries[i].indexBuffer.data;\n\n                for (let j = 0; j < indexBufferData.length; j++)\n                {\n                    geometryOut.indexBuffer.data[j + offset2] += offset;\n                }\n\n                offset += geometries[i].buffers[bufferIndexToCount].data.length / (stride);\n                offset2 += indexBufferData.length;\n            }\n        }\n\n        return geometryOut;\n    }\n}\n"], "mappings": ";;;;;;AAWA,MAAMA,WAAA,GAAuC;EAAE,MAAM;EAAG,MAAM;EAAG,MAAM;AAAA;AACvE,IAAIC,GAAA,GAAM;AAGV,MAAMC,GAAA,GAAiB;EACnBC,YAAA;EACAC,WAAA;EACAC,UAAA;EACAC,UAAA;EACAC;AACJ;AAqBO,MAAMC,QAAA,CACb;EAAA;AAAA;AAAA;AAAA;EA6BIC,YAAYC,OAAA,GAAyB,IAAIC,UAAA,GAAyC,IAClF;IACI,KAAKD,OAAA,GAAUA,OAAA,EAEf,KAAKE,WAAA,GAAc,MAEnB,KAAKD,UAAA,GAAaA,UAAA,EAElB,KAAKE,oBAAA,GAAuB,IAE5B,KAAKC,EAAA,GAAKb,GAAA,IAEV,KAAKc,SAAA,GAAY,IACjB,KAAKC,aAAA,GAAgB,GAErB,KAAKC,aAAA,GAAgB,IAAIC,MAAA,CAAO,iBAAiB,GACjD,KAAKC,QAAA,GAAW;EACpB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAgBAC,aAAaN,EAAA,EAAYO,MAAA,EAA6DC,IAAA,GAAO,GAAGC,UAAA,GAAa,IACzGC,IAAA,EAAcC,MAAA,EAAiBC,KAAA,EAAgBC,QAAA,GAAW,IAC9D;IACI,IAAI,CAACN,MAAA,EAEK,UAAIO,KAAA,CAAM,mDAAmD;IAIjEP,MAAA,YAAkBQ,MAAA,KAGhBR,MAAA,YAAkBS,KAAA,KAElBT,MAAA,GAAS,IAAIlB,YAAA,CAAakB,MAAM,IAGpCA,MAAA,GAAS,IAAIQ,MAAA,CAAOR,MAAM;IAGxB,MAAAU,GAAA,GAAMjB,EAAA,CAAGkB,KAAA,CAAM,GAAG;IAEpB,IAAAD,GAAA,CAAIE,MAAA,GAAS,GACjB;MACI,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIH,GAAA,CAAIE,MAAA,EAAQC,CAAA,IAE5B,KAAKd,YAAA,CAAaW,GAAA,CAAIG,CAAC,GAAGb,MAAA,EAAQC,IAAA,EAAMC,UAAA,EAAYC,IAAI;MAGrD;IACX;IAEA,IAAIW,WAAA,GAAc,KAAKzB,OAAA,CAAQ0B,OAAA,CAAQf,MAAM;IAE7C,OAAIc,WAAA,KAAgB,OAEhB,KAAKzB,OAAA,CAAQ2B,IAAA,CAAKhB,MAAM,GACxBc,WAAA,GAAc,KAAKzB,OAAA,CAAQuB,MAAA,GAAS,IAGxC,KAAKtB,UAAA,CAAWG,EAAE,IAAI,IAAIwB,SAAA,CAAUH,WAAA,EAAab,IAAA,EAAMC,UAAA,EAAYC,IAAA,EAAMC,MAAA,EAAQC,KAAA,EAAOC,QAAQ,GAGhG,KAAKZ,SAAA,GAAY,KAAKA,SAAA,IAAaY,QAAA,EAE5B;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAY,aAAazB,EAAA,EACb;IACW,YAAKH,UAAA,CAAWG,EAAE;EAC7B;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA0B,UAAU1B,EAAA,EACV;IACI,OAAO,KAAKJ,OAAA,CAAQ,KAAK6B,YAAA,CAAazB,EAAE,EAAEO,MAAM;EACpD;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAoB,SAASpB,MAAA,EACT;IACI,OAAMA,MAAA,YAAkBQ,MAAA,KAGhBR,MAAA,YAAkBS,KAAA,KAElBT,MAAA,GAAS,IAAId,WAAA,CAAYc,MAAM,IAGnCA,MAAA,GAAS,IAAIQ,MAAA,CAAOR,MAAM,IAG9BA,MAAA,CAAOG,IAAA,GAAOkB,WAAA,CAAYC,oBAAA,EAE1B,KAAK/B,WAAA,GAAcS,MAAA,EAEd,KAAKX,OAAA,CAAQkC,QAAA,CAASvB,MAAM,KAE7B,KAAKX,OAAA,CAAQ2B,IAAA,CAAKhB,MAAM,GAGrB;EACX;EAAA;AAAA;AAAA;AAAA;EAMAwB,SAAA,EACA;IACI,OAAO,KAAKjC,WAAA;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAkC,WAAA,EACA;IAEQ,SAAKpC,OAAA,CAAQuB,MAAA,KAAW,KAAM,KAAKvB,OAAA,CAAQuB,MAAA,KAAW,KAAK,KAAKrB,WAAA,EAAqB;IAGnF,MAAAmC,MAAA,GAAS;MACTC,KAAA,GAAQ,EACR;MAAAC,iBAAA,GAAoB,IAAIpB,MAAA;IAC1B,IAAAK,CAAA;IAEC,KAAAA,CAAA,IAAK,KAAKvB,UAAA,EACf;MACU,MAAAuC,SAAA,GAAY,KAAKvC,UAAA,CAAWuB,CAAC;QAE7Bb,MAAA,GAAS,KAAKX,OAAA,CAAQwC,SAAA,CAAU7B,MAAM;MAE5C0B,MAAA,CAAOV,IAAA,CAAKhB,MAAA,CAAO8B,IAAI,GAEvBH,KAAA,CAAMX,IAAA,CAAMa,SAAA,CAAU5B,IAAA,GAAOtB,WAAA,CAAYkD,SAAA,CAAU1B,IAAI,IAAK,CAAC,GAE7D0B,SAAA,CAAU7B,MAAA,GAAS;IACvB;IAEA,KAAA4B,iBAAA,CAAkBE,IAAA,GAAOC,qBAAA,CAAsBL,MAAA,EAAQC,KAAK,GAEvDd,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKxB,OAAA,CAAQuB,MAAA,EAAQC,CAAA,IAE7B,KAAKxB,OAAA,CAAQwB,CAAC,MAAM,KAAKtB,WAAA,IAEzB,KAAKF,OAAA,CAAQwB,CAAC,EAAEmB,OAAA;IAInB,YAAA3C,OAAA,GAAU,CAACuC,iBAAiB,GAE7B,KAAKrC,WAAA,IAEL,KAAKF,OAAA,CAAQ2B,IAAA,CAAK,KAAKzB,WAAW,GAG/B;EACX;EAAA;EAGA0C,QAAA,EACA;IACe,WAAApB,CAAA,IAAK,KAAKvB,UAAA,EACrB;MACU,MAAAuC,SAAA,GAAY,KAAKvC,UAAA,CAAWuB,CAAC;MACpB,YAAKxB,OAAA,CAAQwC,SAAA,CAAU7B,MAAM,EAE7B8B,IAAA,CAAalB,MAAA,IAAWiB,SAAA,CAAUzB,MAAA,GAAS,KAAMyB,SAAA,CAAU5B,IAAA;IAC9E;IAEO;EACX;EAAA;EAGAiC,QAAA,EACA;IACS,KAAAtC,aAAA,CAAcuC,IAAA,CAAK,MAAM,EAAK;EACvC;EAAA;EAGAH,QAAA,EACA;IACS,KAAAE,OAAA,IAEL,KAAK7C,OAAA,GAAU,MACf,KAAKE,WAAA,GAAc,MACnB,KAAKD,UAAA,GAAa;EACtB;EAAA;AAAA;AAAA;AAAA;EAMA8C,MAAA,EACA;IACU,MAAAC,QAAA,GAAW,IAAIlD,QAAA;IAErB,SAAS0B,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKxB,OAAA,CAAQuB,MAAA,EAAQC,CAAA,IAErCwB,QAAA,CAAShD,OAAA,CAAQwB,CAAC,IAAI,IAAIL,MAAA,CAAO,KAAKnB,OAAA,CAAQwB,CAAC,EAAEiB,IAAA,CAAKQ,KAAA,CAAM,CAAC,CAAC;IAGvD,WAAAzB,CAAA,IAAK,KAAKvB,UAAA,EACrB;MACU,MAAAiD,MAAA,GAAS,KAAKjD,UAAA,CAAWuB,CAAC;MAEvBwB,QAAA,CAAA/C,UAAA,CAAWuB,CAAC,IAAI,IAAII,SAAA,CACzBsB,MAAA,CAAOvC,MAAA,EACPuC,MAAA,CAAOtC,IAAA,EACPsC,MAAA,CAAOrC,UAAA,EACPqC,MAAA,CAAOpC,IAAA,EACPoC,MAAA,CAAOnC,MAAA,EACPmC,MAAA,CAAOlC,KAAA,EACPkC,MAAA,CAAOjC,QAAA;IAEf;IAEA,OAAI,KAAKf,WAAA,KAEL8C,QAAA,CAAS9C,WAAA,GAAc8C,QAAA,CAAShD,OAAA,CAAQ,KAAKA,OAAA,CAAQ0B,OAAA,CAAQ,KAAKxB,WAAW,CAAC,GAC9E8C,QAAA,CAAS9C,WAAA,CAAYY,IAAA,GAAOkB,WAAA,CAAYC,oBAAA,GAGrCe,QAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASA,OAAOG,MAAMC,UAAA,EACb;IAIU,MAAAC,WAAA,GAAc,IAAIvD,QAAA;MAElBuC,MAAA,GAAS,EAAC;MACVC,KAAA,GAAuB;MACvBgB,OAAA,GAAU;IAEZ,IAAAN,QAAA;IAGJ,SAASxB,CAAA,GAAI,GAAGA,CAAA,GAAI4B,UAAA,CAAW7B,MAAA,EAAQC,CAAA,IACvC;MACIwB,QAAA,GAAWI,UAAA,CAAW5B,CAAC;MAEvB,SAAS+B,CAAA,GAAI,GAAGA,CAAA,GAAIP,QAAA,CAAShD,OAAA,CAAQuB,MAAA,EAAQgC,CAAA,IAEzCjB,KAAA,CAAMiB,CAAC,IAAIjB,KAAA,CAAMiB,CAAC,KAAK,GACvBjB,KAAA,CAAMiB,CAAC,KAAKP,QAAA,CAAShD,OAAA,CAAQuD,CAAC,EAAEd,IAAA,CAAKlB,MAAA,EACrC+B,OAAA,CAAQC,CAAC,IAAI;IAErB;IAGA,SAAS/B,CAAA,GAAI,GAAGA,CAAA,GAAIwB,QAAA,CAAShD,OAAA,CAAQuB,MAAA,EAAQC,CAAA,IAGlCa,MAAA,CAAAb,CAAC,IAAI,IAAIhC,GAAA,CAAIgE,aAAA,CAAcR,QAAA,CAAShD,OAAA,CAAQwB,CAAC,EAAEiB,IAAI,CAAC,EAAEH,KAAA,CAAMd,CAAC,CAAC,GACrE6B,WAAA,CAAYrD,OAAA,CAAQwB,CAAC,IAAI,IAAIL,MAAA,CAAOkB,MAAA,CAAOb,CAAC,CAAC;IAIjD,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAI4B,UAAA,CAAW7B,MAAA,EAAQC,CAAA,IACvC;MACIwB,QAAA,GAAWI,UAAA,CAAW5B,CAAC;MAEvB,SAAS+B,CAAA,GAAI,GAAGA,CAAA,GAAIP,QAAA,CAAShD,OAAA,CAAQuB,MAAA,EAAQgC,CAAA,IAEzClB,MAAA,CAAOkB,CAAC,EAAEE,GAAA,CAAIT,QAAA,CAAShD,OAAA,CAAQuD,CAAC,EAAEd,IAAA,EAAMa,OAAA,CAAQC,CAAC,CAAC,GAClDD,OAAA,CAAQC,CAAC,KAAKP,QAAA,CAAShD,OAAA,CAAQuD,CAAC,EAAEd,IAAA,CAAKlB,MAAA;IAE/C;IAIA,IAFA8B,WAAA,CAAYpD,UAAA,GAAa+C,QAAA,CAAS/C,UAAA,EAE9B+C,QAAA,CAAS9C,WAAA,EACb;MACImD,WAAA,CAAYnD,WAAA,GAAcmD,WAAA,CAAYrD,OAAA,CAAQgD,QAAA,CAAShD,OAAA,CAAQ0B,OAAA,CAAQsB,QAAA,CAAS9C,WAAW,CAAC,GAC5FmD,WAAA,CAAYnD,WAAA,CAAYY,IAAA,GAAOkB,WAAA,CAAYC,oBAAA;MAE3C,IAAIyB,MAAA,GAAS;QACT3C,MAAA,GAAS;QACT4C,OAAA,GAAU;QACVC,kBAAA,GAAqB;MAGzB,SAASpC,CAAA,GAAI,GAAGA,CAAA,GAAIwB,QAAA,CAAShD,OAAA,CAAQuB,MAAA,EAAQC,CAAA,IAEzC,IAAIwB,QAAA,CAAShD,OAAA,CAAQwB,CAAC,MAAMwB,QAAA,CAAS9C,WAAA,EACrC;QACyB0D,kBAAA,GAAApC,CAAA;QACrB;MACJ;MAIO,WAAAA,CAAA,IAAKwB,QAAA,CAAS/C,UAAA,EACzB;QACU,MAAAuC,SAAA,GAAYQ,QAAA,CAAS/C,UAAA,CAAWuB,CAAC;QAElC,CAAAgB,SAAA,CAAU7B,MAAA,GAAS,OAAOiD,kBAAA,KAE3B7C,MAAA,IAAYyB,SAAA,CAAU5B,IAAA,GAAOtB,WAAA,CAAYkD,SAAA,CAAU1B,IAAI,IAAK;MAEpE;MAGA,SAASU,CAAA,GAAI,GAAGA,CAAA,GAAI4B,UAAA,CAAW7B,MAAA,EAAQC,CAAA,IACvC;QACI,MAAMqC,eAAA,GAAkBT,UAAA,CAAW5B,CAAC,EAAEtB,WAAA,CAAYuC,IAAA;QAElD,SAASc,CAAA,GAAI,GAAGA,CAAA,GAAIM,eAAA,CAAgBtC,MAAA,EAAQgC,CAAA,IAExCF,WAAA,CAAYnD,WAAA,CAAYuC,IAAA,CAAKc,CAAA,GAAII,OAAO,KAAKD,MAAA;QAGvCA,MAAA,IAAAN,UAAA,CAAW5B,CAAC,EAAExB,OAAA,CAAQ4D,kBAAkB,EAAEnB,IAAA,CAAKlB,MAAA,GAAUR,MAAA,EACnE4C,OAAA,IAAWE,eAAA,CAAgBtC,MAAA;MAC/B;IACJ;IAEO,OAAA8B,WAAA;EACX;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}