{"ast": null, "code": "const accessibleTarget = {\n  /**\n   *  Flag for if the object is accessible. If true AccessibilityManager will overlay a\n   *   shadow div with attributes set\n   * @member {boolean}\n   * @memberof PIXI.DisplayObject#\n   */\n  accessible: !1,\n  /**\n   * Sets the title attribute of the shadow div\n   * If accessibleTitle AND accessibleHint has not been this will default to 'displayObject [tabIndex]'\n   * @member {?string}\n   * @memberof PIXI.DisplayObject#\n   */\n  accessibleTitle: null,\n  /**\n   * Sets the aria-label attribute of the shadow div\n   * @member {string}\n   * @memberof PIXI.DisplayObject#\n   */\n  accessibleHint: null,\n  /**\n   * @member {number}\n   * @memberof PIXI.DisplayObject#\n   * @private\n   * @todo Needs docs.\n   */\n  tabIndex: 0,\n  /**\n   * @member {boolean}\n   * @memberof PIXI.DisplayObject#\n   * @todo Needs docs.\n   */\n  _accessibleActive: !1,\n  /**\n   * @member {boolean}\n   * @memberof PIXI.DisplayObject#\n   * @todo Needs docs.\n   */\n  _accessibleDiv: null,\n  /**\n   * Specify the type of div the accessible layer is. Screen readers treat the element differently\n   * depending on this type. Defaults to button.\n   * @member {string}\n   * @memberof PIXI.DisplayObject#\n   * @default 'button'\n   */\n  accessibleType: \"button\",\n  /**\n   * Specify the pointer-events the accessible div will use\n   * Defaults to auto.\n   * @member {string}\n   * @memberof PIXI.DisplayObject#\n   * @default 'auto'\n   */\n  accessiblePointerEvents: \"auto\",\n  /**\n   * Setting to false will prevent any children inside this container to\n   * be accessible. Defaults to true.\n   * @member {boolean}\n   * @memberof PIXI.DisplayObject#\n   * @default true\n   */\n  accessibleChildren: !0,\n  renderId: -1\n};\nexport { accessibleTarget };", "map": {"version": 3, "names": ["accessibleTarget", "accessible", "accessibleTitle", "accessibleHint", "tabIndex", "_accessibleActive", "_accessibleDiv", "accessibleType", "accessiblePointerEvents", "accessibleChildren", "renderId"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\accessibility\\src\\accessibleTarget.ts"], "sourcesContent": ["import type { DisplayObject } from '@pixi/display';\n\nexport type PointerEvents = 'auto'\n| 'none'\n| 'visiblePainted'\n| 'visibleFill'\n| 'visibleStroke'\n| 'visible'\n| 'painted'\n| 'fill'\n| 'stroke'\n| 'all'\n| 'inherit';\n\nexport interface IAccessibleTarget\n{\n    accessible: boolean;\n    accessibleTitle: string;\n    accessibleHint: string;\n    tabIndex: number;\n    _accessibleActive: boolean;\n    _accessibleDiv: IAccessibleHTMLElement;\n    accessibleType: string;\n    accessiblePointerEvents: PointerEvents;\n    accessibleChildren: boolean;\n    renderId: number;\n}\n\nexport interface IAccessibleHTMLElement extends HTMLElement\n{\n    type?: string;\n    displayObject?: DisplayObject;\n}\n\n/**\n * Default property values of accessible objects\n * used by {@link PIXI.AccessibilityManager}.\n * @private\n * @function accessibleTarget\n * @memberof PIXI\n * @type {object}\n * @example\n * import { accessibleTarget } from 'pixi.js';\n *\n * function MyObject() {}\n * Object.assign(MyObject.prototype, accessibleTarget);\n */\nexport const accessibleTarget: IAccessibleTarget = {\n    /**\n     *  Flag for if the object is accessible. If true AccessibilityManager will overlay a\n     *   shadow div with attributes set\n     * @member {boolean}\n     * @memberof PIXI.DisplayObject#\n     */\n    accessible: false,\n\n    /**\n     * Sets the title attribute of the shadow div\n     * If accessibleTitle AND accessibleHint has not been this will default to 'displayObject [tabIndex]'\n     * @member {?string}\n     * @memberof PIXI.DisplayObject#\n     */\n    accessibleTitle: null,\n\n    /**\n     * Sets the aria-label attribute of the shadow div\n     * @member {string}\n     * @memberof PIXI.DisplayObject#\n     */\n    accessibleHint: null,\n\n    /**\n     * @member {number}\n     * @memberof PIXI.DisplayObject#\n     * @private\n     * @todo Needs docs.\n     */\n    tabIndex: 0,\n\n    /**\n     * @member {boolean}\n     * @memberof PIXI.DisplayObject#\n     * @todo Needs docs.\n     */\n    _accessibleActive: false,\n\n    /**\n     * @member {boolean}\n     * @memberof PIXI.DisplayObject#\n     * @todo Needs docs.\n     */\n    _accessibleDiv: null,\n\n    /**\n     * Specify the type of div the accessible layer is. Screen readers treat the element differently\n     * depending on this type. Defaults to button.\n     * @member {string}\n     * @memberof PIXI.DisplayObject#\n     * @default 'button'\n     */\n    accessibleType: 'button',\n\n    /**\n     * Specify the pointer-events the accessible div will use\n     * Defaults to auto.\n     * @member {string}\n     * @memberof PIXI.DisplayObject#\n     * @default 'auto'\n     */\n    accessiblePointerEvents: 'auto',\n\n    /**\n     * Setting to false will prevent any children inside this container to\n     * be accessible. Defaults to true.\n     * @member {boolean}\n     * @memberof PIXI.DisplayObject#\n     * @default true\n     */\n    accessibleChildren: true,\n\n    renderId: -1,\n};\n"], "mappings": "AA+CO,MAAMA,gBAAA,GAAsC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAO/CC,UAAA,EAAY;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQZC,eAAA,EAAiB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOjBC,cAAA,EAAgB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQhBC,QAAA,EAAU;EAAA;AAAA;AAAA;AAAA;AAAA;EAOVC,iBAAA,EAAmB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOnBC,cAAA,EAAgB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAShBC,cAAA,EAAgB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAShBC,uBAAA,EAAyB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASzBC,kBAAA,EAAoB;EAEpBC,QAAA,EAAU;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}