{"ast": null, "code": "import $defaultVertex from \"./default.vert.mjs\";\nimport $defaultFilterVertex from \"./defaultFilter.vert.mjs\";\nconst defaultVertex = $defaultVertex,\n  defaultFilterVertex = $defaultFilterVertex;\nexport { defaultFilterVertex, defaultVertex };", "map": {"version": 3, "names": ["defaultVertex", "$defaultVertex", "defaultFilterVertex", "$defaultFilterVertex"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\fragments\\index.ts"], "sourcesContent": ["import $defaultVertex from './default.vert';\nimport $defaultFilterVertex from './defaultFilter.vert';\n\n/**\n * Default vertex shader\n * @memberof PIXI\n * @member {string} defaultVertex\n */\n\n/**\n * Default filter vertex shader\n * @memberof PIXI\n * @member {string} defaultFilterVertex\n */\n\n// NOTE: This black magic is so that @microsoft/api-extractor does not complain! This explicitly specifies the types\n// of defaultVertex, defaultFilterVertex.\nconst defaultVertex: string = $defaultVertex;\nconst defaultFilterVertex: string = $defaultFilterVertex;\n\nexport { defaultFilterVertex, defaultVertex };\n"], "mappings": ";;AAiBM,MAAAA,aAAA,GAAwBC,cAAA;EACxBC,mBAAA,GAA8BC,oBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}