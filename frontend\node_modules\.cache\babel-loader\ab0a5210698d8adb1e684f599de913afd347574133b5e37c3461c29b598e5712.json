{"ast": null, "code": "const inWorker = \"WorkerGlobalScope\" in globalThis && globalThis instanceof globalThis.WorkerGlobalScope;\nfunction testVideoFormat(mimeType) {\n  return inWorker ? !1 : document.createElement(\"video\").canPlayType(mimeType) !== \"\";\n}\nexport { testVideoFormat };", "map": {"version": 3, "names": ["inWorker", "globalThis", "WorkerGlobalScope", "testVideoFormat", "mimeType", "document", "createElement", "canPlayType"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\assets\\src\\detections\\utils\\testVideoFormat.ts"], "sourcesContent": ["const inWorker = 'WorkerGlobalScope' in globalThis\n    && globalThis instanceof (globalThis as any).WorkerGlobalScope;\n\nexport function testVideoFormat(mimeType: string): boolean\n{\n    if (inWorker)\n    {\n        return false;\n    }\n\n    const video = document.createElement('video');\n\n    return video.canPlayType(mimeType) !== '';\n}\n"], "mappings": "AAAA,MAAMA,QAAA,GAAW,uBAAuBC,UAAA,IACjCA,UAAA,YAAuBA,UAAA,CAAmBC,iBAAA;AAE1C,SAASC,gBAAgBC,QAAA,EAChC;EACQ,OAAAJ,QAAA,GAEO,KAGGK,QAAA,CAASC,aAAA,CAAc,OAAO,EAE/BC,WAAA,CAAYH,QAAQ,MAAM;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}