{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Projects\\\\Python\\\\EU4\\\\frontend\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport axios from \"axios\";\nimport WorldMap from './WorldMap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function App() {\n  _s();\n  var _selected$properties;\n  const [countries, setCountries] = useState([]);\n  const [selected, setSelected] = useState(null);\n  const [turn, setTurn] = useState(1);\n  const [playerStats, setPlayerStats] = useState({\n    resources: 100,\n    population: 1000000\n  });\n  const [message, setMessage] = useState(\"\");\n  useEffect(() => {\n    axios.get(\"http://localhost:8000/countries\").then(r => setCountries(r.data));\n  }, [turn]);\n  const nextTurn = () => {\n    axios.post(\"http://localhost:8000/turn\").then(r => {\n      setTurn(r.data.turn);\n      setCountries(r.data.countries);\n    });\n    setTurn(t => t + 1);\n    setPlayerStats(stats => ({\n      ...stats,\n      population: Math.floor(stats.population * 1.01)\n    }));\n    setMessage(\"A new turn begins. Population grows.\");\n  };\n  const invest = () => {\n    setPlayerStats(stats => ({\n      ...stats,\n      resources: stats.resources + 10\n    }));\n    setMessage(\"You invested in your economy! Resources +10\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Empires & Revolutions (Earth Edition)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: nextTurn,\n      children: \"Next Turn\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: [\" Turn: \", turn, \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(WorldMap, {\n      onSelectCountry: setSelected,\n      selectedCountry: selected\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), selected && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#222',\n        color: '#fff',\n        padding: 10,\n        margin: 10,\n        borderRadius: 8\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: selected.name || ((_selected$properties = selected.properties) === null || _selected$properties === void 0 ? void 0 : _selected$properties.NAME)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Population: \", playerStats.population.toLocaleString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Resources: \", playerStats.resources]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: invest,\n        children: \"Invest in Economy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 9\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        color: 'yellow',\n        margin: 10\n      },\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 19\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"K/sUyVnOzQYxEaHb/USKWuM9aLg=\");\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "WorldMap", "jsxDEV", "_jsxDEV", "App", "_s", "_selected$properties", "countries", "setCountries", "selected", "setSelected", "turn", "setTurn", "playerStats", "setPlayerStats", "resources", "population", "message", "setMessage", "get", "then", "r", "data", "nextTurn", "post", "t", "stats", "Math", "floor", "invest", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSelectCountry", "selectedCountry", "style", "background", "color", "padding", "margin", "borderRadius", "name", "properties", "NAME", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Projects/Python/EU4/frontend/src/App.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport axios from \"axios\";\r\nimport WorldMap from './WorldMap';\r\n\r\nexport default function App() {\r\n  const [countries, setCountries] = useState([]);\r\n  const [selected, setSelected] = useState(null);\r\n  const [turn, setTurn] = useState(1);\r\n  const [playerStats, setPlayerStats] = useState({ resources: 100, population: 1000000 });\r\n  const [message, setMessage] = useState(\"\");\r\n\r\n  useEffect(() => {\r\n    axios.get(\"http://localhost:8000/countries\").then(r => setCountries(r.data));\r\n  }, [turn]);\r\n\r\n  const nextTurn = () => {\r\n    axios.post(\"http://localhost:8000/turn\").then(r => {\r\n      setTurn(r.data.turn);\r\n      setCountries(r.data.countries);\r\n    });\r\n    setTurn(t => t + 1);\r\n    setPlayerStats(stats => ({ ...stats, population: Math.floor(stats.population * 1.01) }));\r\n    setMessage(\"A new turn begins. Population grows.\");\r\n  };\r\n\r\n  const invest = () => {\r\n    setPlayerStats(stats => ({ ...stats, resources: stats.resources + 10 }));\r\n    setMessage(\"You invested in your economy! Resources +10\");\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <h1>Empires & Revolutions (Earth Edition)</h1>\r\n      <button onClick={nextTurn}>Next Turn</button>\r\n      <span> Turn: {turn} </span>\r\n      <WorldMap onSelectCountry={setSelected} selectedCountry={selected} />\r\n      {selected && (\r\n        <div style={{ background: '#222', color: '#fff', padding: 10, margin: 10, borderRadius: 8 }}>\r\n          <h2>{selected.name || selected.properties?.NAME}</h2>\r\n          <p>Population: {playerStats.population.toLocaleString()}</p>\r\n          <p>Resources: {playerStats.resources}</p>\r\n          <button onClick={invest}>Invest in Economy</button>\r\n        </div>\r\n      )}\r\n      {message && <div style={{ color: 'yellow', margin: 10 }}>{message}</div>}\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,eAAe,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,oBAAA;EAC5B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACY,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC;IAAEgB,SAAS,EAAE,GAAG;IAAEC,UAAU,EAAE;EAAQ,CAAC,CAAC;EACvF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAE1CD,SAAS,CAAC,MAAM;IACdE,KAAK,CAACmB,GAAG,CAAC,iCAAiC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIb,YAAY,CAACa,CAAC,CAACC,IAAI,CAAC,CAAC;EAC9E,CAAC,EAAE,CAACX,IAAI,CAAC,CAAC;EAEV,MAAMY,QAAQ,GAAGA,CAAA,KAAM;IACrBvB,KAAK,CAACwB,IAAI,CAAC,4BAA4B,CAAC,CAACJ,IAAI,CAACC,CAAC,IAAI;MACjDT,OAAO,CAACS,CAAC,CAACC,IAAI,CAACX,IAAI,CAAC;MACpBH,YAAY,CAACa,CAAC,CAACC,IAAI,CAACf,SAAS,CAAC;IAChC,CAAC,CAAC;IACFK,OAAO,CAACa,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC;IACnBX,cAAc,CAACY,KAAK,KAAK;MAAE,GAAGA,KAAK;MAAEV,UAAU,EAAEW,IAAI,CAACC,KAAK,CAACF,KAAK,CAACV,UAAU,GAAG,IAAI;IAAE,CAAC,CAAC,CAAC;IACxFE,UAAU,CAAC,sCAAsC,CAAC;EACpD,CAAC;EAED,MAAMW,MAAM,GAAGA,CAAA,KAAM;IACnBf,cAAc,CAACY,KAAK,KAAK;MAAE,GAAGA,KAAK;MAAEX,SAAS,EAAEW,KAAK,CAACX,SAAS,GAAG;IAAG,CAAC,CAAC,CAAC;IACxEG,UAAU,CAAC,6CAA6C,CAAC;EAC3D,CAAC;EAED,oBACEf,OAAA;IAAA2B,QAAA,gBACE3B,OAAA;MAAA2B,QAAA,EAAI;IAAqC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC9C/B,OAAA;MAAQgC,OAAO,EAAEZ,QAAS;MAAAO,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAC7C/B,OAAA;MAAA2B,QAAA,GAAM,SAAO,EAACnB,IAAI,EAAC,GAAC;IAAA;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC3B/B,OAAA,CAACF,QAAQ;MAACmC,eAAe,EAAE1B,WAAY;MAAC2B,eAAe,EAAE5B;IAAS;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACpEzB,QAAQ,iBACPN,OAAA;MAAKmC,KAAK,EAAE;QAAEC,UAAU,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,YAAY,EAAE;MAAE,CAAE;MAAAb,QAAA,gBAC1F3B,OAAA;QAAA2B,QAAA,EAAKrB,QAAQ,CAACmC,IAAI,MAAAtC,oBAAA,GAAIG,QAAQ,CAACoC,UAAU,cAAAvC,oBAAA,uBAAnBA,oBAAA,CAAqBwC,IAAI;MAAA;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrD/B,OAAA;QAAA2B,QAAA,GAAG,cAAY,EAACjB,WAAW,CAACG,UAAU,CAAC+B,cAAc,CAAC,CAAC;MAAA;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5D/B,OAAA;QAAA2B,QAAA,GAAG,aAAW,EAACjB,WAAW,CAACE,SAAS;MAAA;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzC/B,OAAA;QAAQgC,OAAO,EAAEN,MAAO;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CACN,EACAjB,OAAO,iBAAId,OAAA;MAAKmC,KAAK,EAAE;QAAEE,KAAK,EAAE,QAAQ;QAAEE,MAAM,EAAE;MAAG,CAAE;MAAAZ,QAAA,EAAEb;IAAO;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrE,CAAC;AAEV;AAAC7B,EAAA,CA3CuBD,GAAG;AAAA4C,EAAA,GAAH5C,GAAG;AAAA,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}