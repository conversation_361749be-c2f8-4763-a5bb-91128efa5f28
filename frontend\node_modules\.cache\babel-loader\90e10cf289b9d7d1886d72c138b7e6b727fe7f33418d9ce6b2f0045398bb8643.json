{"ast": null, "code": "import { Filter, defaultVertex } from \"@pixi/core\";\nimport fragment from \"./alpha.frag.mjs\";\nclass AlphaFilter extends Filter {\n  /**\n   * @param alpha - Amount of alpha from 0 to 1, where 0 is transparent\n   */\n  constructor(alpha = 1) {\n    super(defaultVertex, fragment, {\n      uAlpha: 1\n    }), this.alpha = alpha;\n  }\n  /**\n   * Coefficient for alpha multiplication\n   * @default 1\n   */\n  get alpha() {\n    return this.uniforms.uAlpha;\n  }\n  set alpha(value) {\n    this.uniforms.uAlpha = value;\n  }\n}\nexport { AlphaFilter };", "map": {"version": 3, "names": ["AlphaFilter", "Filter", "constructor", "alpha", "defaultVertex", "fragment", "uAlpha", "uniforms", "value"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\filter-alpha\\src\\AlphaFilter.ts"], "sourcesContent": ["import { defaultVertex, Filter } from '@pixi/core';\nimport fragment from './alpha.frag';\n\n/**\n * Simplest filter - applies alpha.\n *\n * Use this instead of Container's alpha property to avoid visual layering of individual elements.\n * AlphaFilter applies alpha evenly across the entire display object and any opaque elements it contains.\n * If elements are not opaque, they will blend with each other anyway.\n *\n * Very handy if you want to use common features of all filters:\n *\n * 1. Assign a blendMode to this filter, blend all elements inside display object with background.\n *\n * 2. To use clipping in display coordinates, assign a filterArea to the same container that has this filter.\n * @memberof PIXI\n */\nexport class AlphaFilter extends Filter\n{\n    /**\n     * @param alpha - Amount of alpha from 0 to 1, where 0 is transparent\n     */\n    constructor(alpha = 1.0)\n    {\n        super(defaultVertex, fragment, { uAlpha: 1 });\n\n        this.alpha = alpha;\n    }\n\n    /**\n     * Coefficient for alpha multiplication\n     * @default 1\n     */\n    get alpha(): number\n    {\n        return this.uniforms.uAlpha;\n    }\n\n    set alpha(value: number)\n    {\n        this.uniforms.uAlpha = value;\n    }\n}\n"], "mappings": ";;AAiBO,MAAMA,WAAA,SAAoBC,MAAA,CACjC;EAAA;AAAA;AAAA;EAIIC,YAAYC,KAAA,GAAQ,GACpB;IACU,MAAAC,aAAA,EAAeC,QAAA,EAAU;MAAEC,MAAA,EAAQ;IAAA,CAAG,GAE5C,KAAKH,KAAA,GAAQA,KAAA;EACjB;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIA,MAAA,EACJ;IACI,OAAO,KAAKI,QAAA,CAASD,MAAA;EACzB;EAEA,IAAIH,MAAMK,KAAA,EACV;IACI,KAAKD,QAAA,CAASD,MAAA,GAASE,KAAA;EAC3B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}