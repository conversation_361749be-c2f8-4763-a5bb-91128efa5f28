{"ast": null, "code": "import { MeshGeometry } from \"@pixi/mesh\";\nclass RopeGeometry extends MeshGeometry {\n  /**\n   * @param width - The width (i.e., thickness) of the rope.\n   * @param points - An array of {@link PIXI.Point} objects to construct this rope.\n   * @param textureScale - By default the rope texture will be stretched to match\n   *     rope length. If textureScale is positive this value will be treated as a scaling\n   *     factor and the texture will preserve its aspect ratio instead. To create a tiling rope\n   *     set baseTexture.wrapMode to {@link PIXI.WRAP_MODES.REPEAT} and use a power of two texture,\n   *     then set textureScale=1 to keep the original texture pixel size.\n   *     In order to reduce alpha channel artifacts provide a larger texture and downsample -\n   *     i.e. set textureScale=0.5 to scale it down twice.\n   */\n  constructor(width = 200, points, textureScale = 0) {\n    super(new Float32Array(points.length * 4), new Float32Array(points.length * 4), new Uint16Array((points.length - 1) * 6)), this.points = points, this._width = width, this.textureScale = textureScale, this.build();\n  }\n  /**\n   * The width (i.e., thickness) of the rope.\n   * @readonly\n   */\n  get width() {\n    return this._width;\n  }\n  /** Refreshes Rope indices and uvs */\n  build() {\n    const points = this.points;\n    if (!points) return;\n    const vertexBuffer = this.getBuffer(\"aVertexPosition\"),\n      uvBuffer = this.getBuffer(\"aTextureCoord\"),\n      indexBuffer = this.getIndex();\n    if (points.length < 1) return;\n    vertexBuffer.data.length / 4 !== points.length && (vertexBuffer.data = new Float32Array(points.length * 4), uvBuffer.data = new Float32Array(points.length * 4), indexBuffer.data = new Uint16Array((points.length - 1) * 6));\n    const uvs = uvBuffer.data,\n      indices = indexBuffer.data;\n    uvs[0] = 0, uvs[1] = 0, uvs[2] = 0, uvs[3] = 1;\n    let amount = 0,\n      prev = points[0];\n    const textureWidth = this._width * this.textureScale,\n      total = points.length;\n    for (let i = 0; i < total; i++) {\n      const index = i * 4;\n      if (this.textureScale > 0) {\n        const dx = prev.x - points[i].x,\n          dy = prev.y - points[i].y,\n          distance = Math.sqrt(dx * dx + dy * dy);\n        prev = points[i], amount += distance / textureWidth;\n      } else amount = i / (total - 1);\n      uvs[index] = amount, uvs[index + 1] = 0, uvs[index + 2] = amount, uvs[index + 3] = 1;\n    }\n    let indexCount = 0;\n    for (let i = 0; i < total - 1; i++) {\n      const index = i * 2;\n      indices[indexCount++] = index, indices[indexCount++] = index + 1, indices[indexCount++] = index + 2, indices[indexCount++] = index + 2, indices[indexCount++] = index + 1, indices[indexCount++] = index + 3;\n    }\n    uvBuffer.update(), indexBuffer.update(), this.updateVertices();\n  }\n  /** refreshes vertices of Rope mesh */\n  updateVertices() {\n    const points = this.points;\n    if (points.length < 1) return;\n    let lastPoint = points[0],\n      nextPoint,\n      perpX = 0,\n      perpY = 0;\n    const vertices = this.buffers[0].data,\n      total = points.length,\n      halfWidth = this.textureScale > 0 ? this.textureScale * this._width / 2 : this._width / 2;\n    for (let i = 0; i < total; i++) {\n      const point = points[i],\n        index = i * 4;\n      i < points.length - 1 ? nextPoint = points[i + 1] : nextPoint = point, perpY = -(nextPoint.x - lastPoint.x), perpX = nextPoint.y - lastPoint.y;\n      let ratio = (1 - i / (total - 1)) * 10;\n      ratio > 1 && (ratio = 1);\n      const perpLength = Math.sqrt(perpX * perpX + perpY * perpY);\n      perpLength < 1e-6 ? (perpX = 0, perpY = 0) : (perpX /= perpLength, perpY /= perpLength, perpX *= halfWidth, perpY *= halfWidth), vertices[index] = point.x + perpX, vertices[index + 1] = point.y + perpY, vertices[index + 2] = point.x - perpX, vertices[index + 3] = point.y - perpY, lastPoint = point;\n    }\n    this.buffers[0].update();\n  }\n  update() {\n    this.textureScale > 0 ? this.build() : this.updateVertices();\n  }\n}\nexport { RopeGeometry };", "map": {"version": 3, "names": ["RopeGeometry", "MeshGeometry", "constructor", "width", "points", "textureScale", "Float32Array", "length", "Uint16Array", "_width", "build", "vertexBuffer", "<PERSON><PERSON><PERSON><PERSON>", "uv<PERSON><PERSON><PERSON>", "indexBuffer", "getIndex", "data", "uvs", "indices", "amount", "prev", "textureWidth", "total", "i", "index", "dx", "x", "dy", "y", "distance", "Math", "sqrt", "indexCount", "update", "updateVertices", "lastPoint", "nextPoint", "perpX", "perpY", "vertices", "buffers", "halfWidth", "point", "ratio", "perpLength"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\mesh-extras\\src\\geometry\\RopeGeometry.ts"], "sourcesContent": ["import { MeshGeometry } from '@pixi/mesh';\n\nimport type { IPoint } from '@pixi/core';\n\n/**\n * RopeGeometry allows you to draw a geometry across several points and then manipulate these points.\n * @example\n * import { Point, RopeGeometry } from 'pixi.js';\n *\n * for (let i = 0; i < 20; i++) {\n *     points.push(new Point(i * 50, 0));\n * };\n * const rope = new RopeGeometry(100, points);\n * @memberof PIXI\n */\nexport class RopeGeometry extends MeshGeometry\n{\n    /** An array of points that determine the rope. */\n    public points: IPoint[];\n\n    /** Rope texture scale, if zero then the rope texture is stretched. */\n    public readonly textureScale: number;\n\n    /**\n     * The width (i.e., thickness) of the rope.\n     * @readonly\n     */\n    _width: number;\n\n    /**\n     * @param width - The width (i.e., thickness) of the rope.\n     * @param points - An array of {@link PIXI.Point} objects to construct this rope.\n     * @param textureScale - By default the rope texture will be stretched to match\n     *     rope length. If textureScale is positive this value will be treated as a scaling\n     *     factor and the texture will preserve its aspect ratio instead. To create a tiling rope\n     *     set baseTexture.wrapMode to {@link PIXI.WRAP_MODES.REPEAT} and use a power of two texture,\n     *     then set textureScale=1 to keep the original texture pixel size.\n     *     In order to reduce alpha channel artifacts provide a larger texture and downsample -\n     *     i.e. set textureScale=0.5 to scale it down twice.\n     */\n    constructor(width = 200, points: IPoint[], textureScale = 0)\n    {\n        super(new Float32Array(points.length * 4),\n            new Float32Array(points.length * 4),\n            new Uint16Array((points.length - 1) * 6));\n\n        this.points = points;\n        this._width = width;\n        this.textureScale = textureScale;\n\n        this.build();\n    }\n\n    /**\n     * The width (i.e., thickness) of the rope.\n     * @readonly\n     */\n    get width(): number\n    {\n        return this._width;\n    }\n\n    /** Refreshes Rope indices and uvs */\n    private build(): void\n    {\n        const points = this.points;\n\n        if (!points) return;\n\n        const vertexBuffer = this.getBuffer('aVertexPosition');\n        const uvBuffer = this.getBuffer('aTextureCoord');\n        const indexBuffer = this.getIndex();\n\n        // if too little points, or texture hasn't got UVs set yet just move on.\n        if (points.length < 1)\n        {\n            return;\n        }\n\n        // if the number of points has changed we will need to recreate the arraybuffers\n        if (vertexBuffer.data.length / 4 !== points.length)\n        {\n            vertexBuffer.data = new Float32Array(points.length * 4);\n            uvBuffer.data = new Float32Array(points.length * 4);\n            indexBuffer.data = new Uint16Array((points.length - 1) * 6);\n        }\n\n        const uvs = uvBuffer.data;\n        const indices = indexBuffer.data;\n\n        uvs[0] = 0;\n        uvs[1] = 0;\n        uvs[2] = 0;\n        uvs[3] = 1;\n\n        let amount = 0;\n        let prev = points[0];\n        const textureWidth = this._width * this.textureScale;\n        const total = points.length; // - 1;\n\n        for (let i = 0; i < total; i++)\n        {\n            // time to do some smart drawing!\n            const index = i * 4;\n\n            if (this.textureScale > 0)\n            {\n                // calculate pixel distance from previous point\n                const dx = prev.x - points[i].x;\n                const dy = prev.y - points[i].y;\n                const distance = Math.sqrt((dx * dx) + (dy * dy));\n\n                prev = points[i];\n                amount += distance / textureWidth;\n            }\n            else\n            {\n                // stretch texture\n                amount = i / (total - 1);\n            }\n\n            uvs[index] = amount;\n            uvs[index + 1] = 0;\n\n            uvs[index + 2] = amount;\n            uvs[index + 3] = 1;\n        }\n\n        let indexCount = 0;\n\n        for (let i = 0; i < total - 1; i++)\n        {\n            const index = i * 2;\n\n            indices[indexCount++] = index;\n            indices[indexCount++] = index + 1;\n            indices[indexCount++] = index + 2;\n\n            indices[indexCount++] = index + 2;\n            indices[indexCount++] = index + 1;\n            indices[indexCount++] = index + 3;\n        }\n\n        // ensure that the changes are uploaded\n        uvBuffer.update();\n        indexBuffer.update();\n\n        this.updateVertices();\n    }\n\n    /** refreshes vertices of Rope mesh */\n    public updateVertices(): void\n    {\n        const points = this.points;\n\n        if (points.length < 1)\n        {\n            return;\n        }\n\n        let lastPoint = points[0];\n        let nextPoint;\n        let perpX = 0;\n        let perpY = 0;\n\n        const vertices = this.buffers[0].data;\n        const total = points.length;\n        const halfWidth = this.textureScale > 0 ? this.textureScale * this._width / 2 : this._width / 2;\n\n        for (let i = 0; i < total; i++)\n        {\n            const point = points[i];\n            const index = i * 4;\n\n            if (i < points.length - 1)\n            {\n                nextPoint = points[i + 1];\n            }\n            else\n            {\n                nextPoint = point;\n            }\n\n            perpY = -(nextPoint.x - lastPoint.x);\n            perpX = nextPoint.y - lastPoint.y;\n\n            let ratio = (1 - (i / (total - 1))) * 10;\n\n            if (ratio > 1)\n            {\n                ratio = 1;\n            }\n\n            const perpLength = Math.sqrt((perpX * perpX) + (perpY * perpY));\n\n            if (perpLength < 1e-6)\n            {\n                perpX = 0;\n                perpY = 0;\n            }\n            else\n            {\n                perpX /= perpLength;\n                perpY /= perpLength;\n\n                perpX *= halfWidth;\n                perpY *= halfWidth;\n            }\n\n            vertices[index] = point.x + perpX;\n            vertices[index + 1] = point.y + perpY;\n            vertices[index + 2] = point.x - perpX;\n            vertices[index + 3] = point.y - perpY;\n\n            lastPoint = point;\n        }\n\n        this.buffers[0].update();\n    }\n\n    public update(): void\n    {\n        if (this.textureScale > 0)\n        {\n            this.build(); // we need to update UVs\n        }\n        else\n        {\n            this.updateVertices();\n        }\n    }\n}\n"], "mappings": ";AAeO,MAAMA,YAAA,SAAqBC,YAAA,CAClC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAwBIC,YAAYC,KAAA,GAAQ,KAAKC,MAAA,EAAkBC,YAAA,GAAe,GAC1D;IACI,MAAM,IAAIC,YAAA,CAAaF,MAAA,CAAOG,MAAA,GAAS,CAAC,GACpC,IAAID,YAAA,CAAaF,MAAA,CAAOG,MAAA,GAAS,CAAC,GAClC,IAAIC,WAAA,EAAaJ,MAAA,CAAOG,MAAA,GAAS,KAAK,CAAC,IAEtC,KAAAH,MAAA,GAASA,MAAA,EACd,KAAKK,MAAA,GAASN,KAAA,EACd,KAAKE,YAAA,GAAeA,YAAA,EAEpB,KAAKK,KAAA,CAAM;EACf;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIP,MAAA,EACJ;IACI,OAAO,KAAKM,MAAA;EAChB;EAAA;EAGQC,MAAA,EACR;IACI,MAAMN,MAAA,GAAS,KAAKA,MAAA;IAEpB,IAAI,CAACA,MAAA,EAAQ;IAEb,MAAMO,YAAA,GAAe,KAAKC,SAAA,CAAU,iBAAiB;MAC/CC,QAAA,GAAW,KAAKD,SAAA,CAAU,eAAe;MACzCE,WAAA,GAAc,KAAKC,QAAA,CAAS;IAGlC,IAAIX,MAAA,CAAOG,MAAA,GAAS,GAEhB;IAIAI,YAAA,CAAaK,IAAA,CAAKT,MAAA,GAAS,MAAMH,MAAA,CAAOG,MAAA,KAExCI,YAAA,CAAaK,IAAA,GAAO,IAAIV,YAAA,CAAaF,MAAA,CAAOG,MAAA,GAAS,CAAC,GACtDM,QAAA,CAASG,IAAA,GAAO,IAAIV,YAAA,CAAaF,MAAA,CAAOG,MAAA,GAAS,CAAC,GAClDO,WAAA,CAAYE,IAAA,GAAO,IAAIR,WAAA,EAAaJ,MAAA,CAAOG,MAAA,GAAS,KAAK,CAAC;IAG9D,MAAMU,GAAA,GAAMJ,QAAA,CAASG,IAAA;MACfE,OAAA,GAAUJ,WAAA,CAAYE,IAAA;IAE5BC,GAAA,CAAI,CAAC,IAAI,GACTA,GAAA,CAAI,CAAC,IAAI,GACTA,GAAA,CAAI,CAAC,IAAI,GACTA,GAAA,CAAI,CAAC,IAAI;IAET,IAAIE,MAAA,GAAS;MACTC,IAAA,GAAOhB,MAAA,CAAO,CAAC;IACnB,MAAMiB,YAAA,GAAe,KAAKZ,MAAA,GAAS,KAAKJ,YAAA;MAClCiB,KAAA,GAAQlB,MAAA,CAAOG,MAAA;IAErB,SAASgB,CAAA,GAAI,GAAGA,CAAA,GAAID,KAAA,EAAOC,CAAA,IAC3B;MAEI,MAAMC,KAAA,GAAQD,CAAA,GAAI;MAEd,SAAKlB,YAAA,GAAe,GACxB;QAEU,MAAAoB,EAAA,GAAKL,IAAA,CAAKM,CAAA,GAAItB,MAAA,CAAOmB,CAAC,EAAEG,CAAA;UACxBC,EAAA,GAAKP,IAAA,CAAKQ,CAAA,GAAIxB,MAAA,CAAOmB,CAAC,EAAEK,CAAA;UACxBC,QAAA,GAAWC,IAAA,CAAKC,IAAA,CAAMN,EAAA,GAAKA,EAAA,GAAOE,EAAA,GAAKA,EAAG;QAEhDP,IAAA,GAAOhB,MAAA,CAAOmB,CAAC,GACfJ,MAAA,IAAUU,QAAA,GAAWR,YAAA;MACzB,OAIIF,MAAA,GAASI,CAAA,IAAKD,KAAA,GAAQ;MAG1BL,GAAA,CAAIO,KAAK,IAAIL,MAAA,EACbF,GAAA,CAAIO,KAAA,GAAQ,CAAC,IAAI,GAEjBP,GAAA,CAAIO,KAAA,GAAQ,CAAC,IAAIL,MAAA,EACjBF,GAAA,CAAIO,KAAA,GAAQ,CAAC,IAAI;IACrB;IAEA,IAAIQ,UAAA,GAAa;IAEjB,SAAST,CAAA,GAAI,GAAGA,CAAA,GAAID,KAAA,GAAQ,GAAGC,CAAA,IAC/B;MACI,MAAMC,KAAA,GAAQD,CAAA,GAAI;MAEVL,OAAA,CAAAc,UAAA,EAAY,IAAIR,KAAA,EACxBN,OAAA,CAAQc,UAAA,EAAY,IAAIR,KAAA,GAAQ,GAChCN,OAAA,CAAQc,UAAA,EAAY,IAAIR,KAAA,GAAQ,GAEhCN,OAAA,CAAQc,UAAA,EAAY,IAAIR,KAAA,GAAQ,GAChCN,OAAA,CAAQc,UAAA,EAAY,IAAIR,KAAA,GAAQ,GAChCN,OAAA,CAAQc,UAAA,EAAY,IAAIR,KAAA,GAAQ;IACpC;IAGAX,QAAA,CAASoB,MAAA,IACTnB,WAAA,CAAYmB,MAAA,CAAO,GAEnB,KAAKC,cAAA;EACT;EAAA;EAGOA,eAAA,EACP;IACI,MAAM9B,MAAA,GAAS,KAAKA,MAAA;IAEpB,IAAIA,MAAA,CAAOG,MAAA,GAAS,GAEhB;IAGJ,IAAI4B,SAAA,GAAY/B,MAAA,CAAO,CAAC;MACpBgC,SAAA;MACAC,KAAA,GAAQ;MACRC,KAAA,GAAQ;IAEZ,MAAMC,QAAA,GAAW,KAAKC,OAAA,CAAQ,CAAC,EAAExB,IAAA;MAC3BM,KAAA,GAAQlB,MAAA,CAAOG,MAAA;MACfkC,SAAA,GAAY,KAAKpC,YAAA,GAAe,IAAI,KAAKA,YAAA,GAAe,KAAKI,MAAA,GAAS,IAAI,KAAKA,MAAA,GAAS;IAE9F,SAASc,CAAA,GAAI,GAAGA,CAAA,GAAID,KAAA,EAAOC,CAAA,IAC3B;MACI,MAAMmB,KAAA,GAAQtC,MAAA,CAAOmB,CAAC;QAChBC,KAAA,GAAQD,CAAA,GAAI;MAEdA,CAAA,GAAInB,MAAA,CAAOG,MAAA,GAAS,IAEpB6B,SAAA,GAAYhC,MAAA,CAAOmB,CAAA,GAAI,CAAC,IAIxBa,SAAA,GAAYM,KAAA,EAGhBJ,KAAA,GAAQ,EAAEF,SAAA,CAAUV,CAAA,GAAIS,SAAA,CAAUT,CAAA,GAClCW,KAAA,GAAQD,SAAA,CAAUR,CAAA,GAAIO,SAAA,CAAUP,CAAA;MAEhC,IAAIe,KAAA,IAAS,IAAKpB,CAAA,IAAKD,KAAA,GAAQ,MAAO;MAElCqB,KAAA,GAAQ,MAERA,KAAA,GAAQ;MAGZ,MAAMC,UAAA,GAAad,IAAA,CAAKC,IAAA,CAAMM,KAAA,GAAQA,KAAA,GAAUC,KAAA,GAAQA,KAAM;MAE1DM,UAAA,GAAa,QAEbP,KAAA,GAAQ,GACRC,KAAA,GAAQ,MAIRD,KAAA,IAASO,UAAA,EACTN,KAAA,IAASM,UAAA,EAETP,KAAA,IAASI,SAAA,EACTH,KAAA,IAASG,SAAA,GAGbF,QAAA,CAASf,KAAK,IAAIkB,KAAA,CAAMhB,CAAA,GAAIW,KAAA,EAC5BE,QAAA,CAASf,KAAA,GAAQ,CAAC,IAAIkB,KAAA,CAAMd,CAAA,GAAIU,KAAA,EAChCC,QAAA,CAASf,KAAA,GAAQ,CAAC,IAAIkB,KAAA,CAAMhB,CAAA,GAAIW,KAAA,EAChCE,QAAA,CAASf,KAAA,GAAQ,CAAC,IAAIkB,KAAA,CAAMd,CAAA,GAAIU,KAAA,EAEhCH,SAAA,GAAYO,KAAA;IAChB;IAEK,KAAAF,OAAA,CAAQ,CAAC,EAAEP,MAAA,CAAO;EAC3B;EAEOA,OAAA,EACP;IACQ,KAAK5B,YAAA,GAAe,IAEpB,KAAKK,KAAA,KAIL,KAAKwB,cAAA;EAEb;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}