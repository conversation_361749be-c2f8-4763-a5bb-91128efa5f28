{"ast": null, "code": "import { ExtensionType, extensions } from \"@pixi/extensions\";\nclass StartupSystem {\n  constructor(renderer) {\n    this.renderer = renderer;\n  }\n  /**\n   * It all starts here! This initiates every system, passing in the options for any system by name.\n   * @param options - the config for the renderer and all its systems\n   */\n  run(options) {\n    const {\n      renderer\n    } = this;\n    renderer.runners.init.emit(renderer.options), options.hello && console.log(`PixiJS 7.4.3 - ${renderer.rendererLogId} - https://pixijs.com`), renderer.resize(renderer.screen.width, renderer.screen.height);\n  }\n  destroy() {}\n}\nStartupSystem.defaultOptions = {\n  /**\n   * {@link PIXI.IRendererOptions.hello}\n   * @default false\n   * @memberof PIXI.settings.RENDER_OPTIONS\n   */\n  hello: !1\n}, /** @ignore */\nStartupSystem.extension = {\n  type: [ExtensionType.RendererSystem, ExtensionType.CanvasRendererSystem],\n  name: \"startup\"\n};\nextensions.add(StartupSystem);\nexport { StartupSystem };", "map": {"version": 3, "names": ["StartupSystem", "constructor", "renderer", "run", "options", "runners", "init", "emit", "hello", "console", "log", "rendererLogId", "resize", "screen", "width", "height", "destroy", "defaultOptions", "extension", "type", "ExtensionType", "RendererSystem", "CanvasRendererSystem", "name", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\startup\\StartupSystem.ts"], "sourcesContent": ["import { extensions, ExtensionType } from '@pixi/extensions';\n\nimport type { ExtensionMetadata } from '@pixi/extensions';\nimport type { <PERSON><PERSON><PERSON><PERSON> } from '../IRenderer';\nimport type { ISystem } from '../system/ISystem';\n\n/**\n * Options for the startup system.\n * @memberof PIXI\n */\nexport interface StartupSystemOptions\n{\n    /**\n     * Whether to log the version and type information of renderer to console.\n     * @memberof PIXI.IRendererOptions\n     */\n    hello: boolean;\n}\n\n/**\n * A simple system responsible for initiating the renderer.\n * @memberof PIXI\n */\nexport class StartupSystem implements ISystem<StartupSystemOptions>\n{\n    /** @ignore */\n    static defaultOptions: StartupSystemOptions = {\n        /**\n         * {@link PIXI.IRendererOptions.hello}\n         * @default false\n         * @memberof PIXI.settings.RENDER_OPTIONS\n         */\n        hello: false,\n    };\n\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        type: [\n            ExtensionType.RendererSystem,\n            ExtensionType.CanvasRendererSystem\n        ],\n        name: 'startup',\n    };\n\n    readonly renderer: <PERSON><PERSON><PERSON><PERSON>;\n\n    constructor(renderer: <PERSON><PERSON><PERSON><PERSON>)\n    {\n        this.renderer = renderer;\n    }\n\n    /**\n     * It all starts here! This initiates every system, passing in the options for any system by name.\n     * @param options - the config for the renderer and all its systems\n     */\n    run(options: StartupSystemOptions): void\n    {\n        const { renderer } = this;\n\n        renderer.runners.init.emit(renderer.options);\n\n        if (options.hello)\n        {\n            // eslint-disable-next-line no-console\n            console.log(`PixiJS ${process.env.VERSION} - ${renderer.rendererLogId} - https://pixijs.com`);\n        }\n\n        renderer.resize(renderer.screen.width, renderer.screen.height);\n    }\n\n    destroy(): void\n    {\n        // ka pow!\n    }\n}\n\nextensions.add(StartupSystem);\n"], "mappings": ";AAuBO,MAAMA,aAAA,CACb;EAsBIC,YAAYC,QAAA,EACZ;IACI,KAAKA,QAAA,GAAWA,QAAA;EACpB;EAAA;AAAA;AAAA;AAAA;EAMAC,IAAIC,OAAA,EACJ;IACU;MAAEF;IAAa;IAEZA,QAAA,CAAAG,OAAA,CAAQC,IAAA,CAAKC,IAAA,CAAKL,QAAA,CAASE,OAAO,GAEvCA,OAAA,CAAQI,KAAA,IAGRC,OAAA,CAAQC,GAAA,CAAI,kBAAmCR,QAAA,CAASS,aAAa,uBAAuB,GAGhGT,QAAA,CAASU,MAAA,CAAOV,QAAA,CAASW,MAAA,CAAOC,KAAA,EAAOZ,QAAA,CAASW,MAAA,CAAOE,MAAM;EACjE;EAEAC,QAAA,EACA,CAEA;AACJ;AAnDahB,aAAA,CAGFiB,cAAA,GAAuC;EAAA;AAAA;AAAA;AAAA;AAAA;EAM1CT,KAAA,EAAO;AACX;AAVSR,aAAA,CAaFkB,SAAA,GAA+B;EAClCC,IAAA,EAAM,CACFC,aAAA,CAAcC,cAAA,EACdD,aAAA,CAAcE,oBAAA,CAClB;EACAC,IAAA,EAAM;AACV;AAkCJC,UAAA,CAAWC,GAAA,CAAIzB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}