# Core Systems Design

## GameManager
Handles game state, turn progression, and global events.

## MapManager
Loads and manages the world map, provinces, and borders.

## PopSystem
Simulates population groups (pops) by culture, class, and profession. Handles migration, growth, and social change.

## EconomySystem
Manages resources, production, trade, and industrialization.

## DiplomacySystem
Handles alliances, wars, treaties, and international relations.

## EventSystem
Triggers dynamic and historical events, manages player choices and consequences.

---

## Data-Driven Design
- Use JSON and GeoJSON for modding and data flexibility.
- Modular systems for easy expansion.
- RESTful API design for frontend-backend communication.

## Next Steps
1. Enhance backend game systems with more complex mechanics.
2. Improve frontend UI/UX and map interactions.
3. Add save/load functionality and game persistence.
4. Implement more sophisticated AI behavior.
