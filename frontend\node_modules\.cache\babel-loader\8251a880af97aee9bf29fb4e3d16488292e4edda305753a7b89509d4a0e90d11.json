{"ast": null, "code": "import { PRECISION } from \"@pixi/constants\";\nimport { getTestContext } from \"./getTestContext.mjs\";\nlet maxFragmentPrecision;\nfunction getMaxFragmentPrecision() {\n  if (!maxFragmentPrecision) {\n    maxFragmentPrecision = PRECISION.MEDIUM;\n    const gl = getTestContext();\n    if (gl && gl.getShaderPrecisionFormat) {\n      const shaderFragment = gl.getShaderPrecisionFormat(gl.FRAGMENT_SHADER, gl.HIGH_FLOAT);\n      shaderFragment && (maxFragmentPrecision = shaderFragment.precision ? PRECISION.HIGH : PRECISION.MEDIUM);\n    }\n  }\n  return maxFragmentPrecision;\n}\nexport { getMaxFragmentPrecision };", "map": {"version": 3, "names": ["maxFragmentPrecision", "getMaxFragmentPrecision", "PRECISION", "MEDIUM", "gl", "getTestContext", "getShaderPrecisionFormat", "shaderFragment", "FRAGMENT_SHADER", "HIGH_FLOAT", "precision", "HIGH"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\shader\\utils\\getMaxFragmentPrecision.ts"], "sourcesContent": ["import { PRECISION } from '@pixi/constants';\nimport { getTestContext } from './getTestContext';\n\nlet maxFragmentPrecision: PRECISION;\n\nexport function getMaxFragmentPrecision(): PRECISION\n{\n    if (!maxFragmentPrecision)\n    {\n        maxFragmentPrecision = PRECISION.MEDIUM;\n        const gl = getTestContext();\n\n        if (gl)\n        {\n            if (gl.getShaderPrecisionFormat)\n            {\n                const shaderFragment = gl.getShaderPrecisionFormat(gl.FRAGMENT_SHADER, gl.HIGH_FLOAT);\n\n                if (shaderFragment)\n                {\n                    maxFragmentPrecision = shaderFragment.precision ? PRECISION.HIGH : PRECISION.MEDIUM;\n                }\n            }\n        }\n    }\n\n    return maxFragmentPrecision;\n}\n"], "mappings": ";;AAGA,IAAIA,oBAAA;AAEG,SAASC,wBAAA,EAChB;EACI,IAAI,CAACD,oBAAA,EACL;IACIA,oBAAA,GAAuBE,SAAA,CAAUC,MAAA;IACjC,MAAMC,EAAA,GAAKC,cAAA;IAEP,IAAAD,EAAA,IAEIA,EAAA,CAAGE,wBAAA,EACP;MACI,MAAMC,cAAA,GAAiBH,EAAA,CAAGE,wBAAA,CAAyBF,EAAA,CAAGI,eAAA,EAAiBJ,EAAA,CAAGK,UAAU;MAEhFF,cAAA,KAEAP,oBAAA,GAAuBO,cAAA,CAAeG,SAAA,GAAYR,SAAA,CAAUS,IAAA,GAAOT,SAAA,CAAUC,MAAA;IAErF;EAER;EAEO,OAAAH,oBAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}