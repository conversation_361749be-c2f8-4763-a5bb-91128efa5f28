{"ast": null, "code": "import { MSAA_QUALITY } from \"@pixi/constants\";\nclass GLFramebuffer {\n  constructor(framebuffer) {\n    this.framebuffer = framebuffer, this.stencil = null, this.dirtyId = -1, this.dirtyFormat = -1, this.dirtySize = -1, this.multisample = MSAA_QUALITY.NONE, this.msaaBuffer = null, this.blitFramebuffer = null, this.mipLevel = 0;\n  }\n}\nexport { GLFramebuffer };", "map": {"version": 3, "names": ["G<PERSON>ramebuffer", "constructor", "framebuffer", "stencil", "dirtyId", "dirtyFormat", "dirtySize", "multisample", "MSAA_QUALITY", "NONE", "msaa<PERSON><PERSON><PERSON>", "blit<PERSON><PERSON>ebuffer", "mipLevel"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\framebuffer\\GLFramebuffer.ts"], "sourcesContent": ["import { MSAA_QUALITY } from '@pixi/constants';\n\nimport type { Framebuffer } from './Framebuffer';\n\n/**\n * Internal framebuffer for WebGL context.\n * @memberof PIXI\n */\nexport class GLFramebuffer\n{\n    /** The WebGL framebuffer. */\n    public framebuffer: WebGLFramebuffer;\n\n    /** The renderbuffer for depth and/or stencil (DEPTH24_STENCIL8, DEPTH_COMPONENT24, or STENCIL_INDEX8) */\n    public stencil: WebGLRenderbuffer;\n\n    /** Detected AA samples number. */\n    public multisample: MSAA_QUALITY;\n\n    /** In case MSAA, we use this Renderbuffer instead of colorTextures[0] when we write info. */\n    public msaaBuffer: WebGLRenderbuffer;\n\n    /**\n     * In case we use MSAA, this is actual framebuffer that has colorTextures[0]\n     * The contents of that framebuffer are read when we use that renderTexture in sprites\n     */\n    public blitFramebuffer: Framebuffer;\n\n    /** Latest known version of framebuffer. */\n    dirtyId: number;\n\n    /** Latest known version of framebuffer format. */\n    dirtyFormat: number;\n\n    /** Latest known version of framebuffer size. */\n    dirtySize: number;\n\n    /** Store the current mipmap of the textures the framebuffer will write too. */\n    mipLevel: number;\n\n    constructor(framebuffer: WebGLTexture)\n    {\n        this.framebuffer = framebuffer;\n        this.stencil = null;\n        this.dirtyId = -1;\n        this.dirtyFormat = -1;\n        this.dirtySize = -1;\n        this.multisample = MSAA_QUALITY.NONE;\n        this.msaaBuffer = null;\n        this.blitFramebuffer = null;\n        this.mipLevel = 0;\n    }\n}\n"], "mappings": ";AAQO,MAAMA,aAAA,CACb;EA+BIC,YAAYC,WAAA,EACZ;IACS,KAAAA,WAAA,GAAcA,WAAA,EACnB,KAAKC,OAAA,GAAU,MACf,KAAKC,OAAA,GAAU,IACf,KAAKC,WAAA,GAAc,IACnB,KAAKC,SAAA,GAAY,IACjB,KAAKC,WAAA,GAAcC,YAAA,CAAaC,IAAA,EAChC,KAAKC,UAAA,GAAa,MAClB,KAAKC,eAAA,GAAkB,MACvB,KAAKC,QAAA,GAAW;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}