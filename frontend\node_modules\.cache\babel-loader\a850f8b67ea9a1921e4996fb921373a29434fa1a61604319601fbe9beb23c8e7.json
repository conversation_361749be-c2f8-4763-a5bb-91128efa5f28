{"ast": null, "code": "import { Ticker, UPDATE_PRIORITY } from \"@pixi/core\";\nclass EventsTickerClass {\n  constructor() {\n    this.interactionFrequency = 10, this._deltaTime = 0, this._didMove = !1, this.tickerAdded = !1, this._pauseUpdate = !0;\n  }\n  /**\n   * Initializes the event ticker.\n   * @param events - The event system.\n   */\n  init(events) {\n    this.removeTickerListener(), this.events = events, this.interactionFrequency = 10, this._deltaTime = 0, this._didMove = !1, this.tickerAdded = !1, this._pauseUpdate = !0;\n  }\n  /** Whether to pause the update checks or not. */\n  get pauseUpdate() {\n    return this._pauseUpdate;\n  }\n  set pauseUpdate(paused) {\n    this._pauseUpdate = paused;\n  }\n  /** Adds the ticker listener. */\n  addTickerListener() {\n    this.tickerAdded || !this.domElement || (Ticker.system.add(this.tickerUpdate, this, UPDATE_PRIORITY.INTERACTION), this.tickerAdded = !0);\n  }\n  /** Removes the ticker listener. */\n  removeTickerListener() {\n    this.tickerAdded && (Ticker.system.remove(this.tickerUpdate, this), this.tickerAdded = !1);\n  }\n  /** Sets flag to not fire extra events when the user has already moved there mouse */\n  pointerMoved() {\n    this._didMove = !0;\n  }\n  /** Updates the state of interactive objects. */\n  update() {\n    if (!this.domElement || this._pauseUpdate) return;\n    if (this._didMove) {\n      this._didMove = !1;\n      return;\n    }\n    const rootPointerEvent = this.events.rootPointerEvent;\n    this.events.supportsTouchEvents && rootPointerEvent.pointerType === \"touch\" || globalThis.document.dispatchEvent(new PointerEvent(\"pointermove\", {\n      clientX: rootPointerEvent.clientX,\n      clientY: rootPointerEvent.clientY\n    }));\n  }\n  /**\n   * Updates the state of interactive objects if at least {@link PIXI.EventsTicker#interactionFrequency}\n   * milliseconds have passed since the last invocation.\n   *\n   * Invoked by a throttled ticker update from {@link PIXI.Ticker.system}.\n   * @param deltaTime - time delta since the last call\n   */\n  tickerUpdate(deltaTime) {\n    this._deltaTime += deltaTime, !(this._deltaTime < this.interactionFrequency) && (this._deltaTime = 0, this.update());\n  }\n}\nconst EventsTicker = new EventsTickerClass();\nexport { EventsTicker };", "map": {"version": 3, "names": ["EventsTickerClass", "constructor", "interactionFrequency", "_deltaTime", "_did<PERSON><PERSON>", "tickerAdded", "_pauseUpdate", "init", "events", "removeTickerListener", "pauseUpdate", "paused", "addTickerListener", "dom<PERSON>lement", "Ticker", "system", "add", "tickerUpdate", "UPDATE_PRIORITY", "INTERACTION", "remove", "pointerMoved", "update", "rootPointerEvent", "supportsTouchEvents", "pointerType", "globalThis", "document", "dispatchEvent", "PointerEvent", "clientX", "clientY", "deltaTime", "EventsTicker"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\events\\src\\EventTicker.ts"], "sourcesContent": ["import { Ticker, UPDATE_PRIORITY } from '@pixi/core';\n\nimport type { EventSystem } from './EventSystem';\n\n/**\n * This class handles automatic firing of PointerEvents\n * in the case where the pointer is stationary for too long.\n * This is to ensure that hit-tests are still run on moving objects.\n * @memberof PIXI\n * @since 7.2.0\n * @see PIXI.EventsTicker\n */\nclass EventsTickerClass\n{\n    /** The event system. */\n    public events: EventSystem;\n    /** The DOM element to listen to events on. */\n    public domElement: HTMLElement;\n    /** The frequency that fake events will be fired. */\n    public interactionFrequency = 10;\n\n    private _deltaTime = 0;\n    private _didMove = false;\n    private tickerAdded = false;\n    private _pauseUpdate = true;\n\n    /**\n     * Initializes the event ticker.\n     * @param events - The event system.\n     */\n    public init(events: EventSystem): void\n    {\n        this.removeTickerListener();\n        this.events = events;\n        this.interactionFrequency = 10;\n        this._deltaTime = 0;\n        this._didMove = false;\n        this.tickerAdded = false;\n        this._pauseUpdate = true;\n    }\n\n    /** Whether to pause the update checks or not. */\n    get pauseUpdate(): boolean\n    {\n        return this._pauseUpdate;\n    }\n\n    set pauseUpdate(paused: boolean)\n    {\n        this._pauseUpdate = paused;\n    }\n\n    /** Adds the ticker listener. */\n    public addTickerListener(): void\n    {\n        if (this.tickerAdded || !this.domElement)\n        {\n            return;\n        }\n\n        Ticker.system.add(this.tickerUpdate, this, UPDATE_PRIORITY.INTERACTION);\n\n        this.tickerAdded = true;\n    }\n\n    /** Removes the ticker listener. */\n    public removeTickerListener(): void\n    {\n        if (!this.tickerAdded)\n        {\n            return;\n        }\n\n        Ticker.system.remove(this.tickerUpdate, this);\n\n        this.tickerAdded = false;\n    }\n\n    /** Sets flag to not fire extra events when the user has already moved there mouse */\n    public pointerMoved(): void\n    {\n        this._didMove = true;\n    }\n\n    /** Updates the state of interactive objects. */\n    private update(): void\n    {\n        if (!this.domElement || this._pauseUpdate)\n        {\n            return;\n        }\n\n        // if the user move the mouse this check has already been done using the mouse move!\n        if (this._didMove)\n        {\n            this._didMove = false;\n\n            return;\n        }\n\n        // eslint-disable-next-line dot-notation\n        const rootPointerEvent = this.events['rootPointerEvent'];\n\n        if (this.events.supportsTouchEvents && (rootPointerEvent as PointerEvent).pointerType === 'touch')\n        {\n            return;\n        }\n\n        globalThis.document.dispatchEvent(new PointerEvent('pointermove', {\n            clientX: rootPointerEvent.clientX,\n            clientY: rootPointerEvent.clientY,\n        }));\n    }\n\n    /**\n     * Updates the state of interactive objects if at least {@link PIXI.EventsTicker#interactionFrequency}\n     * milliseconds have passed since the last invocation.\n     *\n     * Invoked by a throttled ticker update from {@link PIXI.Ticker.system}.\n     * @param deltaTime - time delta since the last call\n     */\n    private tickerUpdate(deltaTime: number): void\n    {\n        this._deltaTime += deltaTime;\n\n        if (this._deltaTime < this.interactionFrequency)\n        {\n            return;\n        }\n\n        this._deltaTime = 0;\n\n        this.update();\n    }\n}\n\n/**\n * This class handles automatic firing of PointerEvents\n * in the case where the pointer is stationary for too long.\n * This is to ensure that hit-tests are still run on moving objects.\n * @memberof PIXI\n * @type {PIXI.EventsTickerClass}\n * @since 7.2.0\n */\nexport const EventsTicker = new EventsTickerClass();\n"], "mappings": ";AAYA,MAAMA,iBAAA,CACN;EADAC,YAAA;IAOI,KAAOC,oBAAA,GAAuB,IAE9B,KAAQC,UAAA,GAAa,GACrB,KAAQC,QAAA,GAAW,IACnB,KAAQC,WAAA,GAAc,IACtB,KAAQC,YAAA,GAAe;EAAA;EAAA;AAAA;AAAA;AAAA;EAMhBC,KAAKC,MAAA,EACZ;IACI,KAAKC,oBAAA,IACL,KAAKD,MAAA,GAASA,MAAA,EACd,KAAKN,oBAAA,GAAuB,IAC5B,KAAKC,UAAA,GAAa,GAClB,KAAKC,QAAA,GAAW,IAChB,KAAKC,WAAA,GAAc,IACnB,KAAKC,YAAA,GAAe;EACxB;EAAA;EAGA,IAAII,YAAA,EACJ;IACI,OAAO,KAAKJ,YAAA;EAChB;EAEA,IAAII,YAAYC,MAAA,EAChB;IACI,KAAKL,YAAA,GAAeK,MAAA;EACxB;EAAA;EAGOC,kBAAA,EACP;IACQ,KAAKP,WAAA,IAAe,CAAC,KAAKQ,UAAA,KAK9BC,MAAA,CAAOC,MAAA,CAAOC,GAAA,CAAI,KAAKC,YAAA,EAAc,MAAMC,eAAA,CAAgBC,WAAW,GAEtE,KAAKd,WAAA,GAAc;EACvB;EAAA;EAGOI,qBAAA,EACP;IACS,KAAKJ,WAAA,KAKVS,MAAA,CAAOC,MAAA,CAAOK,MAAA,CAAO,KAAKH,YAAA,EAAc,IAAI,GAE5C,KAAKZ,WAAA,GAAc;EACvB;EAAA;EAGOgB,aAAA,EACP;IACI,KAAKjB,QAAA,GAAW;EACpB;EAAA;EAGQkB,OAAA,EACR;IACQ,KAAC,KAAKT,UAAA,IAAc,KAAKP,YAAA,EAEzB;IAIJ,IAAI,KAAKF,QAAA,EACT;MACI,KAAKA,QAAA,GAAW;MAEhB;IACJ;IAGM,MAAAmB,gBAAA,GAAmB,KAAKf,MAAA,CAAOe,gBAAA;IAEjC,KAAKf,MAAA,CAAOgB,mBAAA,IAAwBD,gBAAA,CAAkCE,WAAA,KAAgB,WAK1FC,UAAA,CAAWC,QAAA,CAASC,aAAA,CAAc,IAAIC,YAAA,CAAa,eAAe;MAC9DC,OAAA,EAASP,gBAAA,CAAiBO,OAAA;MAC1BC,OAAA,EAASR,gBAAA,CAAiBQ;IAC7B,EAAC;EACN;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASQd,aAAae,SAAA,EACrB;IACI,KAAK7B,UAAA,IAAc6B,SAAA,EAEf,EAAK,KAAA7B,UAAA,GAAa,KAAKD,oBAAA,MAK3B,KAAKC,UAAA,GAAa,GAElB,KAAKmB,MAAA,CAAO;EAChB;AACJ;AAUa,MAAAW,YAAA,GAAe,IAAIjC,iBAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}