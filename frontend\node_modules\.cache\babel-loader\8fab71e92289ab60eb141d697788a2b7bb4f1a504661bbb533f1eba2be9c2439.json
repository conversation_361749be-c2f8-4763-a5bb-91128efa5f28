{"ast": null, "code": "import { BaseTexture, ExtensionType, extensions } from \"@pixi/core\";\nimport { Graphics } from \"@pixi/graphics\";\nimport { BasePrepare } from \"./BasePrepare.mjs\";\nfunction uploadBaseTextures(renderer, item) {\n  return item instanceof BaseTexture ? (item._glTextures[renderer.CONTEXT_UID] || renderer.texture.bind(item), !0) : !1;\n}\nfunction uploadGraphics(renderer, item) {\n  if (!(item instanceof Graphics)) return !1;\n  const {\n    geometry\n  } = item;\n  item.finishPoly(), geometry.updateBatches();\n  const {\n    batches\n  } = geometry;\n  for (let i = 0; i < batches.length; i++) {\n    const {\n      texture\n    } = batches[i].style;\n    texture && uploadBaseTextures(renderer, texture.baseTexture);\n  }\n  return geometry.batchable || renderer.geometry.bind(geometry, item._resolveDirectShader(renderer)), !0;\n}\nfunction findGraphics(item, queue) {\n  return item instanceof Graphics ? (queue.push(item), !0) : !1;\n}\nclass Prepare extends BasePrepare {\n  /**\n   * @param {PIXI.Renderer} renderer - A reference to the current renderer\n   */\n  constructor(renderer) {\n    super(renderer), this.uploadHookHelper = this.renderer, this.registerFindHook(findGraphics), this.registerUploadHook(uploadBaseTextures), this.registerUploadHook(uploadGraphics);\n  }\n}\nPrepare.extension = {\n  name: \"prepare\",\n  type: ExtensionType.RendererSystem\n};\nextensions.add(Prepare);\nexport { Prepare };", "map": {"version": 3, "names": ["uploadBaseTextures", "renderer", "item", "BaseTexture", "_glTextures", "CONTEXT_UID", "texture", "bind", "uploadGraphics", "Graphics", "geometry", "finishPoly", "updateBatches", "batches", "i", "length", "style", "baseTexture", "batchable", "_resolveDirectShader", "findGraphics", "queue", "push", "Prepare", "BasePrepare", "constructor", "uploadHookHelper", "registerFindHook", "registerUploadHook", "extension", "name", "type", "ExtensionType", "RendererSystem", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\prepare\\src\\Prepare.ts"], "sourcesContent": ["import { BaseTexture, extensions, ExtensionType } from '@pixi/core';\nimport { Graphics } from '@pixi/graphics';\nimport { BasePrepare } from './BasePrepare';\n\nimport type { ExtensionMetadata, IRenderer, ISystem, Renderer } from '@pixi/core';\nimport type { IDisplayObjectExtended } from './BasePrepare';\n\n/**\n * Built-in hook to upload PIXI.Texture objects to the GPU.\n * @private\n * @param renderer - instance of the webgl renderer\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction uploadBaseTextures(renderer: IRenderer | BasePrepare, item: IDisplayObjectExtended | BaseTexture): boolean\n{\n    if (item instanceof BaseTexture)\n    {\n        // if the texture already has a GL texture, then the texture has been prepared or rendered\n        // before now. If the texture changed, then the changer should be calling texture.update() which\n        // reuploads the texture without need for preparing it again\n        if (!item._glTextures[(renderer as Renderer).CONTEXT_UID])\n        {\n            (renderer as Render<PERSON>).texture.bind(item);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to upload PIXI.Graphics to the GPU.\n * @private\n * @param renderer - instance of the webgl renderer\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction uploadGraphics(renderer: IRenderer | BasePrepare, item: IDisplayObjectExtended): boolean\n{\n    if (!(item instanceof Graphics))\n    {\n        return false;\n    }\n\n    const { geometry } = item;\n\n    // update dirty graphics to get batches\n    item.finishPoly();\n    geometry.updateBatches();\n\n    const { batches } = geometry;\n\n    // upload all textures found in styles\n    for (let i = 0; i < batches.length; i++)\n    {\n        const { texture } = batches[i].style;\n\n        if (texture)\n        {\n            uploadBaseTextures(renderer, texture.baseTexture);\n        }\n    }\n\n    // if its not batchable - update vao for particular shader\n    if (!geometry.batchable)\n    {\n        (renderer as Renderer).geometry.bind(geometry, (item as any)._resolveDirectShader((renderer as Renderer)));\n    }\n\n    return true;\n}\n\n/**\n * Built-in hook to find graphics.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns if a PIXI.Graphics object was found.\n */\nfunction findGraphics(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    if (item instanceof Graphics)\n    {\n        queue.push(item);\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * The prepare plugin provides renderer-specific plugins for pre-rendering DisplayObjects. These plugins are useful for\n * asynchronously preparing and uploading to the GPU assets, textures, graphics waiting to be displayed.\n *\n * Do not instantiate this plugin directly. It is available from the `renderer.prepare` property.\n * @example\n * import { Application, Graphics } from 'pixi.js';\n *\n * // Create a new application (prepare will be auto-added to renderer)\n * const app = new Application();\n * document.body.appendChild(app.view);\n *\n * // Don't start rendering right away\n * app.stop();\n *\n * // Create a display object\n * const rect = new Graphics()\n *     .beginFill(0x00ff00)\n *     .drawRect(40, 40, 200, 200);\n *\n * // Add to the stage\n * app.stage.addChild(rect);\n *\n * // Don't start rendering until the graphic is uploaded to the GPU\n * app.renderer.prepare.upload(app.stage, () => {\n *     app.start();\n * });\n * @memberof PIXI\n */\nexport class Prepare extends BasePrepare implements ISystem\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'prepare',\n        type: ExtensionType.RendererSystem,\n    };\n\n    /**\n     * @param {PIXI.Renderer} renderer - A reference to the current renderer\n     */\n    constructor(renderer: Renderer)\n    {\n        super(renderer);\n\n        this.uploadHookHelper = this.renderer;\n\n        // Add textures and graphics to upload\n        this.registerFindHook(findGraphics);\n        this.registerUploadHook(uploadBaseTextures);\n        this.registerUploadHook(uploadGraphics);\n    }\n}\n\nextensions.add(Prepare);\n"], "mappings": ";;;AAcA,SAASA,mBAAmBC,QAAA,EAAmCC,IAAA,EAC/D;EACI,OAAIA,IAAA,YAAgBC,WAAA,IAKXD,IAAA,CAAKE,WAAA,CAAaH,QAAA,CAAsBI,WAAW,KAEnDJ,QAAA,CAAsBK,OAAA,CAAQC,IAAA,CAAKL,IAAI,GAGrC,MAGJ;AACX;AASA,SAASM,eAAeP,QAAA,EAAmCC,IAAA,EAC3D;EACI,IAAI,EAAEA,IAAA,YAAgBO,QAAA,GAEX;EAGL;IAAEC;EAAa,IAAAR,IAAA;EAGhBA,IAAA,CAAAS,UAAA,IACLD,QAAA,CAASE,aAAA,CAAc;EAEjB;IAAEC;EAAY,IAAAH,QAAA;EAGpB,SAASI,CAAA,GAAI,GAAGA,CAAA,GAAID,OAAA,CAAQE,MAAA,EAAQD,CAAA,IACpC;IACI,MAAM;MAAER;IAAY,IAAAO,OAAA,CAAQC,CAAC,EAAEE,KAAA;IAE3BV,OAAA,IAEAN,kBAAA,CAAmBC,QAAA,EAAUK,OAAA,CAAQW,WAAW;EAExD;EAGK,OAAAP,QAAA,CAASQ,SAAA,IAETjB,QAAA,CAAsBS,QAAA,CAASH,IAAA,CAAKG,QAAA,EAAWR,IAAA,CAAaiB,oBAAA,CAAsBlB,QAAqB,CAAC,GAGtG;AACX;AASA,SAASmB,aAAalB,IAAA,EAA8BmB,KAAA,EACpD;EACI,OAAInB,IAAA,YAAgBO,QAAA,IAEhBY,KAAA,CAAMC,IAAA,CAAKpB,IAAI,GAER,MAGJ;AACX;AA+BO,MAAMqB,OAAA,SAAgBC,WAAA,CAC7B;EAAA;AAAA;AAAA;EAUIC,YAAYxB,QAAA,EACZ;IACI,MAAMA,QAAQ,GAEd,KAAKyB,gBAAA,GAAmB,KAAKzB,QAAA,EAG7B,KAAK0B,gBAAA,CAAiBP,YAAY,GAClC,KAAKQ,kBAAA,CAAmB5B,kBAAkB,GAC1C,KAAK4B,kBAAA,CAAmBpB,cAAc;EAC1C;AACJ;AAtBae,OAAA,CAGFM,SAAA,GAA+B;EAClCC,IAAA,EAAM;EACNC,IAAA,EAAMC,aAAA,CAAcC;AACxB;AAkBJC,UAAA,CAAWC,GAAA,CAAIZ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}