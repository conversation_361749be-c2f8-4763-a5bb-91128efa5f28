{"ast": null, "code": "import { Runner } from \"@pixi/runner\";\nclass Resource {\n  /**\n   * @param width - Width of the resource\n   * @param height - Height of the resource\n   */\n  constructor(width = 0, height = 0) {\n    this._width = width, this._height = height, this.destroyed = !1, this.internal = !1, this.onResize = new Runner(\"setRealSize\"), this.onUpdate = new Runner(\"update\"), this.onError = new Runner(\"onError\");\n  }\n  /**\n   * Bind to a parent BaseTexture\n   * @param baseTexture - Parent texture\n   */\n  bind(baseTexture) {\n    this.onResize.add(baseTexture), this.onUpdate.add(baseTexture), this.onError.add(baseTexture), (this._width || this._height) && this.onResize.emit(this._width, this._height);\n  }\n  /**\n   * Unbind to a parent BaseTexture\n   * @param baseTexture - Parent texture\n   */\n  unbind(baseTexture) {\n    this.onResize.remove(baseTexture), this.onUpdate.remove(baseTexture), this.onError.remove(baseTexture);\n  }\n  /**\n   * Trigger a resize event\n   * @param width - X dimension\n   * @param height - Y dimension\n   */\n  resize(width, height) {\n    (width !== this._width || height !== this._height) && (this._width = width, this._height = height, this.onResize.emit(width, height));\n  }\n  /**\n   * Has been validated\n   * @readonly\n   */\n  get valid() {\n    return !!this._width && !!this._height;\n  }\n  /** Has been updated trigger event. */\n  update() {\n    this.destroyed || this.onUpdate.emit();\n  }\n  /**\n   * This can be overridden to start preloading a resource\n   * or do any other prepare step.\n   * @protected\n   * @returns Handle the validate event\n   */\n  load() {\n    return Promise.resolve(this);\n  }\n  /**\n   * The width of the resource.\n   * @readonly\n   */\n  get width() {\n    return this._width;\n  }\n  /**\n   * The height of the resource.\n   * @readonly\n   */\n  get height() {\n    return this._height;\n  }\n  /**\n   * Set the style, optional to override\n   * @param _renderer - yeah, renderer!\n   * @param _baseTexture - the texture\n   * @param _glTexture - texture instance for this webgl context\n   * @returns - `true` is success\n   */\n  style(_renderer, _baseTexture, _glTexture) {\n    return !1;\n  }\n  /** Clean up anything, this happens when destroying is ready. */\n  dispose() {}\n  /**\n   * Call when destroying resource, unbind any BaseTexture object\n   * before calling this method, as reference counts are maintained\n   * internally.\n   */\n  destroy() {\n    this.destroyed || (this.destroyed = !0, this.dispose(), this.onError.removeAll(), this.onError = null, this.onResize.removeAll(), this.onResize = null, this.onUpdate.removeAll(), this.onUpdate = null);\n  }\n  /**\n   * Abstract, used to auto-detect resource type.\n   * @param {*} _source - The source object\n   * @param {string} _extension - The extension of source, if set\n   */\n  static test(_source, _extension) {\n    return !1;\n  }\n}\nexport { Resource };", "map": {"version": 3, "names": ["Resource", "constructor", "width", "height", "_width", "_height", "destroyed", "internal", "onResize", "Runner", "onUpdate", "onError", "bind", "baseTexture", "add", "emit", "unbind", "remove", "resize", "valid", "update", "load", "Promise", "resolve", "style", "_renderer", "_baseTexture", "_glTexture", "dispose", "destroy", "removeAll", "test", "_source", "_extension"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\textures\\resources\\Resource.ts"], "sourcesContent": ["import { <PERSON> } from '@pixi/runner';\n\nimport type { <PERSON><PERSON><PERSON> } from '../../Renderer';\nimport type { BaseTexture } from '../BaseTexture';\nimport type { GLTexture } from '../GLTexture';\n\n/**\n * Base resource class for textures that manages validation and uploading, depending on its type.\n *\n * Uploading of a base texture to the GPU is required.\n * @memberof PIXI\n */\nexport abstract class Resource\n{\n    /** The url of the resource */\n    public src: string;\n\n    /**\n     * If resource has been destroyed.\n     * @readonly\n     * @default false\n     */\n    public destroyed: boolean;\n\n    /**\n     * `true` if resource is created by BaseTexture\n     * useful for doing cleanup with BaseTexture destroy\n     * and not cleaning up resources that were created\n     * externally.\n     */\n    public internal: boolean;\n\n    /** Internal width of the resource. */\n    protected _width: number;\n\n    /** Internal height of the resource. */\n    protected _height: number;\n\n    /**\n     * Mini-runner for handling resize events\n     * accepts 2 parameters: width, height\n     * @member {Runner}\n     * @private\n     */\n    protected onResize: Runner; // TODO: Should this be private? It doesn't seem to be used anywhere else.\n\n    /**\n     * Mini-runner for handling update events\n     * @member {Runner}\n     * @private\n     */\n    protected onUpdate: Runner;\n\n    /**\n     * Handle internal errors, such as loading errors\n     * accepts 1 param: error\n     * @member {Runner}\n     * @private\n     */\n    protected onError: Runner;\n\n    /**\n     * @param width - Width of the resource\n     * @param height - Height of the resource\n     */\n    constructor(width = 0, height = 0)\n    {\n        this._width = width;\n        this._height = height;\n\n        this.destroyed = false;\n        this.internal = false;\n\n        this.onResize = new Runner('setRealSize');\n        this.onUpdate = new Runner('update');\n        this.onError = new Runner('onError');\n    }\n\n    /**\n     * Bind to a parent BaseTexture\n     * @param baseTexture - Parent texture\n     */\n    bind(baseTexture: BaseTexture): void\n    {\n        this.onResize.add(baseTexture);\n        this.onUpdate.add(baseTexture);\n        this.onError.add(baseTexture);\n\n        // Call a resize immediate if we already\n        // have the width and height of the resource\n        if (this._width || this._height)\n        {\n            this.onResize.emit(this._width, this._height);\n        }\n    }\n\n    /**\n     * Unbind to a parent BaseTexture\n     * @param baseTexture - Parent texture\n     */\n    unbind(baseTexture: BaseTexture): void\n    {\n        this.onResize.remove(baseTexture);\n        this.onUpdate.remove(baseTexture);\n        this.onError.remove(baseTexture);\n    }\n\n    /**\n     * Trigger a resize event\n     * @param width - X dimension\n     * @param height - Y dimension\n     */\n    resize(width: number, height: number): void\n    {\n        if (width !== this._width || height !== this._height)\n        {\n            this._width = width;\n            this._height = height;\n            this.onResize.emit(width, height);\n        }\n    }\n\n    /**\n     * Has been validated\n     * @readonly\n     */\n    get valid(): boolean\n    {\n        return !!this._width && !!this._height;\n    }\n\n    /** Has been updated trigger event. */\n    update(): void\n    {\n        if (!this.destroyed)\n        {\n            this.onUpdate.emit();\n        }\n    }\n\n    /**\n     * This can be overridden to start preloading a resource\n     * or do any other prepare step.\n     * @protected\n     * @returns Handle the validate event\n     */\n    load(): Promise<this>\n    {\n        return Promise.resolve(this);\n    }\n\n    /**\n     * The width of the resource.\n     * @readonly\n     */\n    get width(): number\n    {\n        return this._width;\n    }\n\n    /**\n     * The height of the resource.\n     * @readonly\n     */\n    get height(): number\n    {\n        return this._height;\n    }\n\n    /**\n     * Uploads the texture or returns false if it cant for some reason. Override this.\n     * @param renderer - yeah, renderer!\n     * @param baseTexture - the texture\n     * @param glTexture - texture instance for this webgl context\n     * @returns - true is success\n     */\n    abstract upload(renderer: Renderer, baseTexture: BaseTexture, glTexture: GLTexture): boolean;\n\n    /**\n     * Set the style, optional to override\n     * @param _renderer - yeah, renderer!\n     * @param _baseTexture - the texture\n     * @param _glTexture - texture instance for this webgl context\n     * @returns - `true` is success\n     */\n    style(_renderer: Renderer, _baseTexture: BaseTexture, _glTexture: GLTexture): boolean\n    {\n        return false;\n    }\n\n    /** Clean up anything, this happens when destroying is ready. */\n    dispose(): void\n    {\n        // override\n    }\n\n    /**\n     * Call when destroying resource, unbind any BaseTexture object\n     * before calling this method, as reference counts are maintained\n     * internally.\n     */\n    destroy(): void\n    {\n        if (!this.destroyed)\n        {\n            this.destroyed = true;\n            this.dispose();\n            this.onError.removeAll();\n            this.onError = null;\n            this.onResize.removeAll();\n            this.onResize = null;\n            this.onUpdate.removeAll();\n            this.onUpdate = null;\n        }\n    }\n\n    /**\n     * Abstract, used to auto-detect resource type.\n     * @param {*} _source - The source object\n     * @param {string} _extension - The extension of source, if set\n     */\n    static test(_source: unknown, _extension?: string): boolean\n    {\n        return false;\n    }\n}\n"], "mappings": ";AAYO,MAAeA,QAAA,CACtB;EAAA;AAAA;AAAA;AAAA;EAoDIC,YAAYC,KAAA,GAAQ,GAAGC,MAAA,GAAS,GAChC;IACS,KAAAC,MAAA,GAASF,KAAA,EACd,KAAKG,OAAA,GAAUF,MAAA,EAEf,KAAKG,SAAA,GAAY,IACjB,KAAKC,QAAA,GAAW,IAEhB,KAAKC,QAAA,GAAW,IAAIC,MAAA,CAAO,aAAa,GACxC,KAAKC,QAAA,GAAW,IAAID,MAAA,CAAO,QAAQ,GACnC,KAAKE,OAAA,GAAU,IAAIF,MAAA,CAAO,SAAS;EACvC;EAAA;AAAA;AAAA;AAAA;EAMAG,KAAKC,WAAA,EACL;IACS,KAAAL,QAAA,CAASM,GAAA,CAAID,WAAW,GAC7B,KAAKH,QAAA,CAASI,GAAA,CAAID,WAAW,GAC7B,KAAKF,OAAA,CAAQG,GAAA,CAAID,WAAW,IAIxB,KAAKT,MAAA,IAAU,KAAKC,OAAA,KAEpB,KAAKG,QAAA,CAASO,IAAA,CAAK,KAAKX,MAAA,EAAQ,KAAKC,OAAO;EAEpD;EAAA;AAAA;AAAA;AAAA;EAMAW,OAAOH,WAAA,EACP;IACI,KAAKL,QAAA,CAASS,MAAA,CAAOJ,WAAW,GAChC,KAAKH,QAAA,CAASO,MAAA,CAAOJ,WAAW,GAChC,KAAKF,OAAA,CAAQM,MAAA,CAAOJ,WAAW;EACnC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAK,OAAOhB,KAAA,EAAeC,MAAA,EACtB;IACI,CAAID,KAAA,KAAU,KAAKE,MAAA,IAAUD,MAAA,KAAW,KAAKE,OAAA,MAEzC,KAAKD,MAAA,GAASF,KAAA,EACd,KAAKG,OAAA,GAAUF,MAAA,EACf,KAAKK,QAAA,CAASO,IAAA,CAAKb,KAAA,EAAOC,MAAM;EAExC;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIgB,MAAA,EACJ;IACI,OAAO,CAAC,CAAC,KAAKf,MAAA,IAAU,CAAC,CAAC,KAAKC,OAAA;EACnC;EAAA;EAGAe,OAAA,EACA;IACS,KAAKd,SAAA,IAEN,KAAKI,QAAA,CAASK,IAAA,CAAK;EAE3B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAM,KAAA,EACA;IACW,OAAAC,OAAA,CAAQC,OAAA,CAAQ,IAAI;EAC/B;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIrB,MAAA,EACJ;IACI,OAAO,KAAKE,MAAA;EAChB;EAAA;AAAA;AAAA;AAAA;EAMA,IAAID,OAAA,EACJ;IACI,OAAO,KAAKE,OAAA;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAkBAmB,MAAMC,SAAA,EAAqBC,YAAA,EAA2BC,UAAA,EACtD;IACW;EACX;EAAA;EAGAC,QAAA,EACA,CAEA;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAC,QAAA,EACA;IACS,KAAKvB,SAAA,KAEN,KAAKA,SAAA,GAAY,IACjB,KAAKsB,OAAA,IACL,KAAKjB,OAAA,CAAQmB,SAAA,CACb,QAAKnB,OAAA,GAAU,MACf,KAAKH,QAAA,CAASsB,SAAA,CAAU,GACxB,KAAKtB,QAAA,GAAW,MAChB,KAAKE,QAAA,CAASoB,SAAA,CACd,QAAKpB,QAAA,GAAW;EAExB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,OAAOqB,KAAKC,OAAA,EAAkBC,UAAA,EAC9B;IACW;EACX;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}