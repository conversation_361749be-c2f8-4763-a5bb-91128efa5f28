{"ast": null, "code": "import { Texture, BaseTexture, Ticker, UPDATE_PRIORITY } from \"@pixi/core\";\nimport { Container } from \"@pixi/display\";\nimport { Text, TextStyle, TextMetrics } from \"@pixi/text\";\nimport { CountLimiter } from \"./CountLimiter.mjs\";\nfunction findMultipleBaseTextures(item, queue) {\n  let result = !1;\n  if (item?._textures?.length) {\n    for (let i = 0; i < item._textures.length; i++) if (item._textures[i] instanceof Texture) {\n      const baseTexture = item._textures[i].baseTexture;\n      queue.includes(baseTexture) || (queue.push(baseTexture), result = !0);\n    }\n  }\n  return result;\n}\nfunction findBaseTexture(item, queue) {\n  if (item.baseTexture instanceof BaseTexture) {\n    const texture = item.baseTexture;\n    return queue.includes(texture) || queue.push(texture), !0;\n  }\n  return !1;\n}\nfunction findTexture(item, queue) {\n  if (item._texture && item._texture instanceof Texture) {\n    const texture = item._texture.baseTexture;\n    return queue.includes(texture) || queue.push(texture), !0;\n  }\n  return !1;\n}\nfunction drawText(_helper, item) {\n  return item instanceof Text ? (item.updateText(!0), !0) : !1;\n}\nfunction calculateTextStyle(_helper, item) {\n  if (item instanceof TextStyle) {\n    const font = item.toFontString();\n    return TextMetrics.measureFont(font), !0;\n  }\n  return !1;\n}\nfunction findText(item, queue) {\n  if (item instanceof Text) {\n    queue.includes(item.style) || queue.push(item.style), queue.includes(item) || queue.push(item);\n    const texture = item._texture.baseTexture;\n    return queue.includes(texture) || queue.push(texture), !0;\n  }\n  return !1;\n}\nfunction findTextStyle(item, queue) {\n  return item instanceof TextStyle ? (queue.includes(item) || queue.push(item), !0) : !1;\n}\nconst _BasePrepare = class _BasePrepare2 {\n  /**\n   * @param {PIXI.IRenderer} renderer - A reference to the current renderer\n   */\n  constructor(renderer) {\n    this.limiter = new CountLimiter(_BasePrepare2.uploadsPerFrame), this.renderer = renderer, this.uploadHookHelper = null, this.queue = [], this.addHooks = [], this.uploadHooks = [], this.completes = [], this.ticking = !1, this.delayedTick = () => {\n      this.queue && this.prepareItems();\n    }, this.registerFindHook(findText), this.registerFindHook(findTextStyle), this.registerFindHook(findMultipleBaseTextures), this.registerFindHook(findBaseTexture), this.registerFindHook(findTexture), this.registerUploadHook(drawText), this.registerUploadHook(calculateTextStyle);\n  }\n  /**\n   * Upload all the textures and graphics to the GPU.\n   * @method PIXI.BasePrepare#upload\n   * @param {PIXI.DisplayObject|PIXI.Container|PIXI.BaseTexture|PIXI.Texture|PIXI.Graphics|PIXI.Text} [item] -\n   *        Container or display object to search for items to upload or the items to upload themselves,\n   *        or optionally ommitted, if items have been added using {@link PIXI.BasePrepare#add `prepare.add`}.\n   */\n  upload(item) {\n    return new Promise(resolve => {\n      item && this.add(item), this.queue.length ? (this.completes.push(resolve), this.ticking || (this.ticking = !0, Ticker.system.addOnce(this.tick, this, UPDATE_PRIORITY.UTILITY))) : resolve();\n    });\n  }\n  /**\n   * Handle tick update\n   * @private\n   */\n  tick() {\n    setTimeout(this.delayedTick, 0);\n  }\n  /**\n   * Actually prepare items. This is handled outside of the tick because it will take a while\n   * and we do NOT want to block the current animation frame from rendering.\n   * @private\n   */\n  prepareItems() {\n    for (this.limiter.beginFrame(); this.queue.length && this.limiter.allowedToUpload();) {\n      const item = this.queue[0];\n      let uploaded = !1;\n      if (item && !item._destroyed) {\n        for (let i = 0, len = this.uploadHooks.length; i < len; i++) if (this.uploadHooks[i](this.uploadHookHelper, item)) {\n          this.queue.shift(), uploaded = !0;\n          break;\n        }\n      }\n      uploaded || this.queue.shift();\n    }\n    if (this.queue.length) Ticker.system.addOnce(this.tick, this, UPDATE_PRIORITY.UTILITY);else {\n      this.ticking = !1;\n      const completes = this.completes.slice(0);\n      this.completes.length = 0;\n      for (let i = 0, len = completes.length; i < len; i++) completes[i]();\n    }\n  }\n  /**\n   * Adds hooks for finding items.\n   * @param {Function} addHook - Function call that takes two parameters: `item:*, queue:Array`\n   *          function must return `true` if it was able to add item to the queue.\n   * @returns Instance of plugin for chaining.\n   */\n  registerFindHook(addHook) {\n    return addHook && this.addHooks.push(addHook), this;\n  }\n  /**\n   * Adds hooks for uploading items.\n   * @param {Function} uploadHook - Function call that takes two parameters: `prepare:CanvasPrepare, item:*` and\n   *          function must return `true` if it was able to handle upload of item.\n   * @returns Instance of plugin for chaining.\n   */\n  registerUploadHook(uploadHook) {\n    return uploadHook && this.uploadHooks.push(uploadHook), this;\n  }\n  /**\n   * Manually add an item to the uploading queue.\n   * @param {PIXI.DisplayObject|PIXI.Container|PIXI.BaseTexture|PIXI.Texture|PIXI.Graphics|PIXI.Text|*} item - Object to\n   *        add to the queue\n   * @returns Instance of plugin for chaining.\n   */\n  add(item) {\n    for (let i = 0, len = this.addHooks.length; i < len && !this.addHooks[i](item, this.queue); i++);\n    if (item instanceof Container) for (let i = item.children.length - 1; i >= 0; i--) this.add(item.children[i]);\n    return this;\n  }\n  /** Destroys the plugin, don't use after this. */\n  destroy() {\n    this.ticking && Ticker.system.remove(this.tick, this), this.ticking = !1, this.addHooks = null, this.uploadHooks = null, this.renderer = null, this.completes = null, this.queue = null, this.limiter = null, this.uploadHookHelper = null;\n  }\n};\n_BasePrepare.uploadsPerFrame = 4;\nlet BasePrepare = _BasePrepare;\nexport { BasePrepare };", "map": {"version": 3, "names": ["findMultipleBaseTextures", "item", "queue", "result", "_textures", "length", "i", "Texture", "baseTexture", "includes", "push", "findBaseTexture", "BaseTexture", "texture", "findTexture", "_texture", "drawText", "_helper", "Text", "updateText", "calculateTextStyle", "TextStyle", "font", "toFontString", "TextMetrics", "measureFont", "findText", "style", "findTextStyle", "_BasePrepare", "_BasePrepare2", "constructor", "renderer", "limiter", "CountLimiter", "uploadsPerFrame", "uploadHookHelper", "add<PERSON>ooks", "uploadHooks", "completes", "ticking", "delayedTick", "prepareItems", "registerFindHook", "registerUploadHook", "upload", "Promise", "resolve", "add", "Ticker", "system", "addOnce", "tick", "UPDATE_PRIORITY", "UTILITY", "setTimeout", "beginFrame", "allowedToUpload", "uploaded", "_destroyed", "len", "shift", "slice", "addHook", "uploadHook", "Container", "children", "destroy", "remove", "BasePrepare"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\prepare\\src\\BasePrepare.ts"], "sourcesContent": ["import { BaseTexture, Texture, Ticker, UPDATE_PRIORITY } from '@pixi/core';\nimport { Container } from '@pixi/display';\nimport { Text, TextMetrics, TextStyle } from '@pixi/text';\nimport { CountLimiter } from './CountLimiter';\n\nimport type { <PERSON><PERSON><PERSON><PERSON> } from '@pixi/core';\nimport type { DisplayObject } from '@pixi/display';\n\ninterface IArrowFunction\n{\n    (): void;\n}\ninterface IUploadHook\n{\n    (helper: I<PERSON>enderer | BasePrepare, item: IDisplayObjectExtended): boolean;\n}\n\ninterface IFindHook\n{\n    (item: any, queue: Array<any>): boolean;\n}\n\nexport interface IDisplayObjectExtended extends DisplayObject\n{\n    _textures?: Array<Texture>;\n    _texture?: Texture;\n    style?: TextStyle | Partial<TextStyle>;\n}\n\n/**\n * Built-in hook to find multiple textures from objects like AnimatedSprites.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.Texture object was found.\n */\nfunction findMultipleBaseTextures(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    let result = false;\n\n    // Objects with multiple textures\n    if (item?._textures?.length)\n    {\n        for (let i = 0; i < item._textures.length; i++)\n        {\n            if (item._textures[i] instanceof Texture)\n            {\n                const baseTexture = item._textures[i].baseTexture;\n\n                if (!queue.includes(baseTexture))\n                {\n                    queue.push(baseTexture);\n                    result = true;\n                }\n            }\n        }\n    }\n\n    return result;\n}\n\n/**\n * Built-in hook to find BaseTextures from Texture.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.Texture object was found.\n */\nfunction findBaseTexture(item: Texture, queue: Array<any>): boolean\n{\n    if (item.baseTexture instanceof BaseTexture)\n    {\n        const texture = item.baseTexture;\n\n        if (!queue.includes(texture))\n        {\n            queue.push(texture);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to find textures from objects.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.Texture object was found.\n */\nfunction findTexture(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    if (item._texture && item._texture instanceof Texture)\n    {\n        const texture = item._texture.baseTexture;\n\n        if (!queue.includes(texture))\n        {\n            queue.push(texture);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to draw PIXI.Text to its texture.\n * @private\n * @param _helper - Not used by this upload handler\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction drawText(_helper: IRenderer | BasePrepare, item: IDisplayObjectExtended): boolean\n{\n    if (item instanceof Text)\n    {\n        // updating text will return early if it is not dirty\n        item.updateText(true);\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to calculate a text style for a PIXI.Text object.\n * @private\n * @param _helper - Not used by this upload handler\n * @param item - Item to check\n * @returns If item was uploaded.\n */\nfunction calculateTextStyle(_helper: IRenderer | BasePrepare, item: IDisplayObjectExtended): boolean\n{\n    if (item instanceof TextStyle)\n    {\n        const font = item.toFontString();\n\n        TextMetrics.measureFont(font);\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to find Text objects.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns if a PIXI.Text object was found.\n */\nfunction findText(item: IDisplayObjectExtended, queue: Array<any>): boolean\n{\n    if (item instanceof Text)\n    {\n        // push the text style to prepare it - this can be really expensive\n        if (!queue.includes(item.style))\n        {\n            queue.push(item.style);\n        }\n        // also push the text object so that we can render it (to canvas/texture) if needed\n        if (!queue.includes(item))\n        {\n            queue.push(item);\n        }\n        // also push the Text's texture for upload to GPU\n        const texture = item._texture.baseTexture;\n\n        if (!queue.includes(texture))\n        {\n            queue.push(texture);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Built-in hook to find TextStyle objects.\n * @private\n * @param item - Display object to check\n * @param queue - Collection of items to upload\n * @returns If a PIXI.TextStyle object was found.\n */\nfunction findTextStyle(item: TextStyle, queue: Array<any>): boolean\n{\n    if (item instanceof TextStyle)\n    {\n        if (!queue.includes(item))\n        {\n            queue.push(item);\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * The prepare manager provides functionality to upload content to the GPU.\n *\n * BasePrepare handles basic queuing functionality and is extended by\n * {@link PIXI.Prepare} and {@link PIXI.CanvasPrepare}\n * to provide preparation capabilities specific to their respective renderers.\n * @example\n * // Create a sprite\n * const sprite = PIXI.Sprite.from('something.png');\n *\n * // Load object into GPU\n * app.renderer.prepare.upload(sprite, () => {\n *     // Texture(s) has been uploaded to GPU\n *     app.stage.addChild(sprite);\n * });\n * @abstract\n * @memberof PIXI\n */\nexport class BasePrepare\n{\n    /**\n     * The default maximum uploads per frame.\n     * @static\n     */\n    public static uploadsPerFrame = 4;\n\n    /**\n     * The limiter to be used to control how quickly items are prepared.\n     * @type {PIXI.CountLimiter|PIXI.TimeLimiter}\n     */\n    private limiter: CountLimiter;\n\n    /** Reference to the renderer. */\n    protected renderer: IRenderer;\n\n    /**\n     * The only real difference between CanvasPrepare and Prepare is what they pass\n     * to upload hooks. That different parameter is stored here.\n     */\n    protected uploadHookHelper: any;\n\n    /** Collection of items to uploads at once. */\n    protected queue: Array<any>;\n\n    /**\n     * Collection of additional hooks for finding assets.\n     * @type {Array<Function>}\n     */\n    public addHooks: Array<any>;\n\n    /**\n     * Collection of additional hooks for processing assets.\n     * @type {Array<Function>}\n     */\n    public uploadHooks: Array<any>;\n\n    /**\n     * Callback to call after completed.\n     * @type {Array<Function>}\n     */\n    public completes: Array<any>;\n\n    /**\n     * If prepare is ticking (running).\n     * @type {boolean}\n     */\n    public ticking: boolean;\n\n    /**\n     * 'bound' call for prepareItems().\n     * @type {Function}\n     */\n    private delayedTick: IArrowFunction;\n\n    /**\n     * @param {PIXI.IRenderer} renderer - A reference to the current renderer\n     */\n    constructor(renderer: IRenderer)\n    {\n        this.limiter = new CountLimiter(BasePrepare.uploadsPerFrame);\n        this.renderer = renderer;\n        this.uploadHookHelper = null;\n        this.queue = [];\n        this.addHooks = [];\n        this.uploadHooks = [];\n        this.completes = [];\n        this.ticking = false;\n        this.delayedTick = (): void =>\n        {\n            // unlikely, but in case we were destroyed between tick() and delayedTick()\n            if (!this.queue)\n            {\n                return;\n            }\n            this.prepareItems();\n        };\n\n        // hooks to find the correct texture\n        this.registerFindHook(findText);\n        this.registerFindHook(findTextStyle);\n        this.registerFindHook(findMultipleBaseTextures);\n        this.registerFindHook(findBaseTexture);\n        this.registerFindHook(findTexture);\n\n        // upload hooks\n        this.registerUploadHook(drawText);\n        this.registerUploadHook(calculateTextStyle);\n    }\n\n    /**\n     * Upload all the textures and graphics to the GPU.\n     * @method PIXI.BasePrepare#upload\n     * @param {PIXI.DisplayObject|PIXI.Container|PIXI.BaseTexture|PIXI.Texture|PIXI.Graphics|PIXI.Text} [item] -\n     *        Container or display object to search for items to upload or the items to upload themselves,\n     *        or optionally ommitted, if items have been added using {@link PIXI.BasePrepare#add `prepare.add`}.\n     */\n    upload(item?: IDisplayObjectExtended | Container | BaseTexture | Texture): Promise<void>\n    {\n        return new Promise((resolve) =>\n        {\n            // If a display object, search for items\n            // that we could upload\n            if (item)\n            {\n                this.add(item as IDisplayObjectExtended | Container | BaseTexture | Texture);\n            }\n\n            // Get the items for upload from the display\n            if (this.queue.length)\n            {\n                this.completes.push(resolve);\n\n                if (!this.ticking)\n                {\n                    this.ticking = true;\n                    Ticker.system.addOnce(this.tick, this, UPDATE_PRIORITY.UTILITY);\n                }\n            }\n            else\n            {\n                resolve();\n            }\n        });\n    }\n\n    /**\n     * Handle tick update\n     * @private\n     */\n    tick(): void\n    {\n        setTimeout(this.delayedTick, 0);\n    }\n\n    /**\n     * Actually prepare items. This is handled outside of the tick because it will take a while\n     * and we do NOT want to block the current animation frame from rendering.\n     * @private\n     */\n    prepareItems(): void\n    {\n        this.limiter.beginFrame();\n        // Upload the graphics\n        while (this.queue.length && this.limiter.allowedToUpload())\n        {\n            const item = this.queue[0];\n            let uploaded = false;\n\n            if (item && !item._destroyed)\n            {\n                for (let i = 0, len = this.uploadHooks.length; i < len; i++)\n                {\n                    if (this.uploadHooks[i](this.uploadHookHelper, item))\n                    {\n                        this.queue.shift();\n                        uploaded = true;\n                        break;\n                    }\n                }\n            }\n\n            if (!uploaded)\n            {\n                this.queue.shift();\n            }\n        }\n\n        // We're finished\n        if (!this.queue.length)\n        {\n            this.ticking = false;\n\n            const completes = this.completes.slice(0);\n\n            this.completes.length = 0;\n\n            for (let i = 0, len = completes.length; i < len; i++)\n            {\n                completes[i]();\n            }\n        }\n        else\n        {\n            // if we are not finished, on the next rAF do this again\n            Ticker.system.addOnce(this.tick, this, UPDATE_PRIORITY.UTILITY);\n        }\n    }\n\n    /**\n     * Adds hooks for finding items.\n     * @param {Function} addHook - Function call that takes two parameters: `item:*, queue:Array`\n     *          function must return `true` if it was able to add item to the queue.\n     * @returns Instance of plugin for chaining.\n     */\n    registerFindHook(addHook: IFindHook): this\n    {\n        if (addHook)\n        {\n            this.addHooks.push(addHook);\n        }\n\n        return this;\n    }\n\n    /**\n     * Adds hooks for uploading items.\n     * @param {Function} uploadHook - Function call that takes two parameters: `prepare:CanvasPrepare, item:*` and\n     *          function must return `true` if it was able to handle upload of item.\n     * @returns Instance of plugin for chaining.\n     */\n    registerUploadHook(uploadHook: IUploadHook): this\n    {\n        if (uploadHook)\n        {\n            this.uploadHooks.push(uploadHook);\n        }\n\n        return this;\n    }\n\n    /**\n     * Manually add an item to the uploading queue.\n     * @param {PIXI.DisplayObject|PIXI.Container|PIXI.BaseTexture|PIXI.Texture|PIXI.Graphics|PIXI.Text|*} item - Object to\n     *        add to the queue\n     * @returns Instance of plugin for chaining.\n     */\n    add(item: IDisplayObjectExtended | Container | BaseTexture | Texture): this\n    {\n        // Add additional hooks for finding elements on special\n        // types of objects that\n        for (let i = 0, len = this.addHooks.length; i < len; i++)\n        {\n            if (this.addHooks[i](item, this.queue))\n            {\n                break;\n            }\n        }\n\n        // Get children recursively\n        if (item instanceof Container)\n        {\n            for (let i = item.children.length - 1; i >= 0; i--)\n            {\n                this.add(item.children[i]);\n            }\n        }\n\n        return this;\n    }\n\n    /** Destroys the plugin, don't use after this. */\n    destroy(): void\n    {\n        if (this.ticking)\n        {\n            Ticker.system.remove(this.tick, this);\n        }\n        this.ticking = false;\n        this.addHooks = null;\n        this.uploadHooks = null;\n        this.renderer = null;\n        this.completes = null;\n        this.queue = null;\n        this.limiter = null;\n        this.uploadHookHelper = null;\n    }\n}\n"], "mappings": ";;;;AAoCA,SAASA,yBAAyBC,IAAA,EAA8BC,KAAA,EAChE;EACI,IAAIC,MAAA,GAAS;EAGb,IAAIF,IAAA,EAAMG,SAAA,EAAWC,MAAA;IAEjB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIL,IAAA,CAAKG,SAAA,CAAUC,MAAA,EAAQC,CAAA,IAEvC,IAAIL,IAAA,CAAKG,SAAA,CAAUE,CAAC,aAAaC,OAAA,EACjC;MACI,MAAMC,WAAA,GAAcP,IAAA,CAAKG,SAAA,CAAUE,CAAC,EAAEE,WAAA;MAEjCN,KAAA,CAAMO,QAAA,CAASD,WAAW,MAE3BN,KAAA,CAAMQ,IAAA,CAAKF,WAAW,GACtBL,MAAA,GAAS;IAEjB;EAAA;EAID,OAAAA,MAAA;AACX;AASA,SAASQ,gBAAgBV,IAAA,EAAeC,KAAA,EACxC;EACQ,IAAAD,IAAA,CAAKO,WAAA,YAAuBI,WAAA,EAChC;IACI,MAAMC,OAAA,GAAUZ,IAAA,CAAKO,WAAA;IAErB,OAAKN,KAAA,CAAMO,QAAA,CAASI,OAAO,KAEvBX,KAAA,CAAMQ,IAAA,CAAKG,OAAO,GAGf;EACX;EAEO;AACX;AASA,SAASC,YAAYb,IAAA,EAA8BC,KAAA,EACnD;EACI,IAAID,IAAA,CAAKc,QAAA,IAAYd,IAAA,CAAKc,QAAA,YAAoBR,OAAA,EAC9C;IACU,MAAAM,OAAA,GAAUZ,IAAA,CAAKc,QAAA,CAASP,WAAA;IAE9B,OAAKN,KAAA,CAAMO,QAAA,CAASI,OAAO,KAEvBX,KAAA,CAAMQ,IAAA,CAAKG,OAAO,GAGf;EACX;EAEO;AACX;AASA,SAASG,SAASC,OAAA,EAAkChB,IAAA,EACpD;EACI,OAAIA,IAAA,YAAgBiB,IAAA,IAGhBjB,IAAA,CAAKkB,UAAA,CAAW,EAAI,GAEb,MAGJ;AACX;AASA,SAASC,mBAAmBH,OAAA,EAAkChB,IAAA,EAC9D;EACI,IAAIA,IAAA,YAAgBoB,SAAA,EACpB;IACU,MAAAC,IAAA,GAAOrB,IAAA,CAAKsB,YAAA;IAEN,OAAAC,WAAA,CAAAC,WAAA,CAAYH,IAAI,GAErB;EACX;EAEO;AACX;AASA,SAASI,SAASzB,IAAA,EAA8BC,KAAA,EAChD;EACI,IAAID,IAAA,YAAgBiB,IAAA,EACpB;IAEShB,KAAA,CAAMO,QAAA,CAASR,IAAA,CAAK0B,KAAK,KAE1BzB,KAAA,CAAMQ,IAAA,CAAKT,IAAA,CAAK0B,KAAK,GAGpBzB,KAAA,CAAMO,QAAA,CAASR,IAAI,KAEpBC,KAAA,CAAMQ,IAAA,CAAKT,IAAI;IAGb,MAAAY,OAAA,GAAUZ,IAAA,CAAKc,QAAA,CAASP,WAAA;IAE9B,OAAKN,KAAA,CAAMO,QAAA,CAASI,OAAO,KAEvBX,KAAA,CAAMQ,IAAA,CAAKG,OAAO,GAGf;EACX;EAEO;AACX;AASA,SAASe,cAAc3B,IAAA,EAAiBC,KAAA,EACxC;EACQ,OAAAD,IAAA,YAAgBoB,SAAA,IAEXnB,KAAA,CAAMO,QAAA,CAASR,IAAI,KAEpBC,KAAA,CAAMQ,IAAA,CAAKT,IAAI,GAGZ,MAGJ;AACX;AAoBO,MAAM4B,YAAA,GAAN,MAAMC,aAAA,CACb;EAAA;AAAA;AAAA;EA0DIC,YAAYC,QAAA,EACZ;IACI,KAAKC,OAAA,GAAU,IAAIC,YAAA,CAAaJ,aAAA,CAAYK,eAAe,GAC3D,KAAKH,QAAA,GAAWA,QAAA,EAChB,KAAKI,gBAAA,GAAmB,MACxB,KAAKlC,KAAA,GAAQ,IACb,KAAKmC,QAAA,GAAW,IAChB,KAAKC,WAAA,GAAc,IACnB,KAAKC,SAAA,GAAY,IACjB,KAAKC,OAAA,GAAU,IACf,KAAKC,WAAA,GAAc,MACnB;MAES,KAAKvC,KAAA,IAIV,KAAKwC,YAAA;IACT,GAGA,KAAKC,gBAAA,CAAiBjB,QAAQ,GAC9B,KAAKiB,gBAAA,CAAiBf,aAAa,GACnC,KAAKe,gBAAA,CAAiB3C,wBAAwB,GAC9C,KAAK2C,gBAAA,CAAiBhC,eAAe,GACrC,KAAKgC,gBAAA,CAAiB7B,WAAW,GAGjC,KAAK8B,kBAAA,CAAmB5B,QAAQ,GAChC,KAAK4B,kBAAA,CAAmBxB,kBAAkB;EAC9C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAyB,OAAO5C,IAAA,EACP;IACW,WAAI6C,OAAA,CAASC,OAAA,IACpB;MAGQ9C,IAAA,IAEA,KAAK+C,GAAA,CAAI/C,IAAkE,GAI3E,KAAKC,KAAA,CAAMG,MAAA,IAEX,KAAKkC,SAAA,CAAU7B,IAAA,CAAKqC,OAAO,GAEtB,KAAKP,OAAA,KAEN,KAAKA,OAAA,GAAU,IACfS,MAAA,CAAOC,MAAA,CAAOC,OAAA,CAAQ,KAAKC,IAAA,EAAM,MAAMC,eAAA,CAAgBC,OAAO,MAKlEP,OAAA,CAAQ;IAAA,CAEf;EACL;EAAA;AAAA;AAAA;AAAA;EAMAK,KAAA,EACA;IACeG,UAAA,MAAKd,WAAA,EAAa,CAAC;EAClC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAC,aAAA,EACA;IACI,UAAKT,OAAA,CAAQuB,UAAA,IAEN,KAAKtD,KAAA,CAAMG,MAAA,IAAU,KAAK4B,OAAA,CAAQwB,eAAA,KACzC;MACU,MAAAxD,IAAA,GAAO,KAAKC,KAAA,CAAM,CAAC;MACzB,IAAIwD,QAAA,GAAW;MAEX,IAAAzD,IAAA,IAAQ,CAACA,IAAA,CAAK0D,UAAA;QAEd,SAASrD,CAAA,GAAI,GAAGsD,GAAA,GAAM,KAAKtB,WAAA,CAAYjC,MAAA,EAAQC,CAAA,GAAIsD,GAAA,EAAKtD,CAAA,IAEpD,IAAI,KAAKgC,WAAA,CAAYhC,CAAC,EAAE,KAAK8B,gBAAA,EAAkBnC,IAAI,GACnD;UACS,KAAAC,KAAA,CAAM2D,KAAA,CAAM,GACjBH,QAAA,GAAW;UACX;QACJ;MAAA;MAIHA,QAAA,IAED,KAAKxD,KAAA,CAAM2D,KAAA;IAEnB;IAGA,IAAK,KAAK3D,KAAA,CAAMG,MAAA,EAgBZ4C,MAAA,CAAOC,MAAA,CAAOC,OAAA,CAAQ,KAAKC,IAAA,EAAM,MAAMC,eAAA,CAAgBC,OAAO,OAflE;MACI,KAAKd,OAAA,GAAU;MAEf,MAAMD,SAAA,GAAY,KAAKA,SAAA,CAAUuB,KAAA,CAAM,CAAC;MAExC,KAAKvB,SAAA,CAAUlC,MAAA,GAAS;MAExB,SAASC,CAAA,GAAI,GAAGsD,GAAA,GAAMrB,SAAA,CAAUlC,MAAA,EAAQC,CAAA,GAAIsD,GAAA,EAAKtD,CAAA,IAE7CiC,SAAA,CAAUjC,CAAC;IAEnB;EAMJ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAqC,iBAAiBoB,OAAA,EACjB;IACI,OAAIA,OAAA,IAEA,KAAK1B,QAAA,CAAS3B,IAAA,CAAKqD,OAAO,GAGvB;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAnB,mBAAmBoB,UAAA,EACnB;IACI,OAAIA,UAAA,IAEA,KAAK1B,WAAA,CAAY5B,IAAA,CAAKsD,UAAU,GAG7B;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAhB,IAAI/C,IAAA,EACJ;IAGI,SAASK,CAAA,GAAI,GAAGsD,GAAA,GAAM,KAAKvB,QAAA,CAAShC,MAAA,EAAQC,CAAA,GAAIsD,GAAA,IAExC,MAAKvB,QAAA,CAAS/B,CAAC,EAAEL,IAAA,EAAM,KAAKC,KAAK,GAFYI,CAAA,GAEjD;IAOJ,IAAIL,IAAA,YAAgBgE,SAAA,EAEhB,SAAS3D,CAAA,GAAIL,IAAA,CAAKiE,QAAA,CAAS7D,MAAA,GAAS,GAAGC,CAAA,IAAK,GAAGA,CAAA,IAE3C,KAAK0C,GAAA,CAAI/C,IAAA,CAAKiE,QAAA,CAAS5D,CAAC,CAAC;IAI1B;EACX;EAAA;EAGA6D,QAAA,EACA;IACQ,KAAK3B,OAAA,IAELS,MAAA,CAAOC,MAAA,CAAOkB,MAAA,CAAO,KAAKhB,IAAA,EAAM,IAAI,GAExC,KAAKZ,OAAA,GAAU,IACf,KAAKH,QAAA,GAAW,MAChB,KAAKC,WAAA,GAAc,MACnB,KAAKN,QAAA,GAAW,MAChB,KAAKO,SAAA,GAAY,MACjB,KAAKrC,KAAA,GAAQ,MACb,KAAK+B,OAAA,GAAU,MACf,KAAKG,gBAAA,GAAmB;EAC5B;AACJ;AA5QaP,YAAA,CAMKM,eAAA,GAAkB;AAN7B,IAAMkC,WAAA,GAANxC,YAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}