{"ast": null, "code": "import { Point, utils } from \"@pixi/core\";\nimport { EventsTicker } from \"./EventTicker.mjs\";\nimport { FederatedMouseEvent } from \"./FederatedMouseEvent.mjs\";\nimport { FederatedPointerEvent } from \"./FederatedPointerEvent.mjs\";\nimport { FederatedWheelEvent } from \"./FederatedWheelEvent.mjs\";\nconst PROPAGATION_LIMIT = 2048,\n  tempHitLocation = new Point(),\n  tempLocalMapping = new Point();\nclass EventBoundary {\n  /**\n   * @param rootTarget - The holder of the event boundary.\n   */\n  constructor(rootTarget) {\n    this.dispatch = new utils.EventEmitter(), this.moveOnAll = !1, this.enableGlobalMoveEvents = !0, this.mappingState = {\n      trackingData: {}\n    }, this.eventPool = /* @__PURE__ */new Map(), this._allInteractiveElements = [], this._hitElements = [], this._isPointerMoveEvent = !1, this.rootTarget = rootTarget, this.hitPruneFn = this.hitPruneFn.bind(this), this.hitTestFn = this.hitTestFn.bind(this), this.mapPointerDown = this.mapPointerDown.bind(this), this.mapPointerMove = this.mapPointerMove.bind(this), this.mapPointerOut = this.mapPointerOut.bind(this), this.mapPointerOver = this.mapPointerOver.bind(this), this.mapPointerUp = this.mapPointerUp.bind(this), this.mapPointerUpOutside = this.mapPointerUpOutside.bind(this), this.mapWheel = this.mapWheel.bind(this), this.mappingTable = {}, this.addEventMapping(\"pointerdown\", this.mapPointerDown), this.addEventMapping(\"pointermove\", this.mapPointerMove), this.addEventMapping(\"pointerout\", this.mapPointerOut), this.addEventMapping(\"pointerleave\", this.mapPointerOut), this.addEventMapping(\"pointerover\", this.mapPointerOver), this.addEventMapping(\"pointerup\", this.mapPointerUp), this.addEventMapping(\"pointerupoutside\", this.mapPointerUpOutside), this.addEventMapping(\"wheel\", this.mapWheel);\n  }\n  /**\n   * Adds an event mapping for the event `type` handled by `fn`.\n   *\n   * Event mappings can be used to implement additional or custom events. They take an event\n   * coming from the upstream scene (or directly from the {@link PIXI.EventSystem}) and dispatch new downstream events\n   * generally trickling down and bubbling up to {@link PIXI.EventBoundary.rootTarget this.rootTarget}.\n   *\n   * To modify the semantics of existing events, the built-in mapping methods of EventBoundary should be overridden\n   * instead.\n   * @param type - The type of upstream event to map.\n   * @param fn - The mapping method. The context of this function must be bound manually, if desired.\n   */\n  addEventMapping(type, fn) {\n    this.mappingTable[type] || (this.mappingTable[type] = []), this.mappingTable[type].push({\n      fn,\n      priority: 0\n    }), this.mappingTable[type].sort((a, b) => a.priority - b.priority);\n  }\n  /**\n   * Dispatches the given event\n   * @param e\n   * @param type\n   */\n  dispatchEvent(e, type) {\n    e.propagationStopped = !1, e.propagationImmediatelyStopped = !1, this.propagate(e, type), this.dispatch.emit(type || e.type, e);\n  }\n  /**\n   * Maps the given upstream event through the event boundary and propagates it downstream.\n   * @param e\n   */\n  mapEvent(e) {\n    if (!this.rootTarget) return;\n    const mappers = this.mappingTable[e.type];\n    if (mappers) for (let i = 0, j = mappers.length; i < j; i++) mappers[i].fn(e);else console.warn(`[EventBoundary]: Event mapping not defined for ${e.type}`);\n  }\n  /**\n   * Finds the DisplayObject that is the target of a event at the given coordinates.\n   *\n   * The passed (x,y) coordinates are in the world space above this event boundary.\n   * @param x\n   * @param y\n   */\n  hitTest(x, y) {\n    EventsTicker.pauseUpdate = !0;\n    const fn = this._isPointerMoveEvent && this.enableGlobalMoveEvents ? \"hitTestMoveRecursive\" : \"hitTestRecursive\",\n      invertedPath = this[fn](this.rootTarget, this.rootTarget.eventMode, tempHitLocation.set(x, y), this.hitTestFn, this.hitPruneFn);\n    return invertedPath && invertedPath[0];\n  }\n  /**\n   * Propagate the passed event from from {@link PIXI.EventBoundary.rootTarget this.rootTarget} to its\n   * target {@code e.target}.\n   * @param e - The event to propagate.\n   * @param type\n   */\n  propagate(e, type) {\n    if (!e.target) return;\n    const composedPath = e.composedPath();\n    e.eventPhase = e.CAPTURING_PHASE;\n    for (let i = 0, j = composedPath.length - 1; i < j; i++) if (e.currentTarget = composedPath[i], this.notifyTarget(e, type), e.propagationStopped || e.propagationImmediatelyStopped) return;\n    if (e.eventPhase = e.AT_TARGET, e.currentTarget = e.target, this.notifyTarget(e, type), !(e.propagationStopped || e.propagationImmediatelyStopped)) {\n      e.eventPhase = e.BUBBLING_PHASE;\n      for (let i = composedPath.length - 2; i >= 0; i--) if (e.currentTarget = composedPath[i], this.notifyTarget(e, type), e.propagationStopped || e.propagationImmediatelyStopped) return;\n    }\n  }\n  /**\n   * Emits the event {@code e} to all interactive display objects. The event is propagated in the bubbling phase always.\n   *\n   * This is used in the `globalpointermove` event.\n   * @param e - The emitted event.\n   * @param type - The listeners to notify.\n   * @param targets - The targets to notify.\n   */\n  all(e, type, targets = this._allInteractiveElements) {\n    if (targets.length === 0) return;\n    e.eventPhase = e.BUBBLING_PHASE;\n    const events = Array.isArray(type) ? type : [type];\n    for (let i = targets.length - 1; i >= 0; i--) events.forEach(event => {\n      e.currentTarget = targets[i], this.notifyTarget(e, event);\n    });\n  }\n  /**\n   * Finds the propagation path from {@link PIXI.EventBoundary.rootTarget rootTarget} to the passed\n   * {@code target}. The last element in the path is {@code target}.\n   * @param target\n   */\n  propagationPath(target) {\n    const propagationPath = [target];\n    for (let i = 0; i < PROPAGATION_LIMIT && target !== this.rootTarget; i++) {\n      if (!target.parent) throw new Error(\"Cannot find propagation path to disconnected target\");\n      propagationPath.push(target.parent), target = target.parent;\n    }\n    return propagationPath.reverse(), propagationPath;\n  }\n  hitTestMoveRecursive(currentTarget, eventMode, location, testFn, pruneFn, ignore = !1) {\n    let shouldReturn = !1;\n    if (this._interactivePrune(currentTarget)) return null;\n    if ((currentTarget.eventMode === \"dynamic\" || eventMode === \"dynamic\") && (EventsTicker.pauseUpdate = !1), currentTarget.interactiveChildren && currentTarget.children) {\n      const children = currentTarget.children;\n      for (let i = children.length - 1; i >= 0; i--) {\n        const child = children[i],\n          nestedHit = this.hitTestMoveRecursive(child, this._isInteractive(eventMode) ? eventMode : child.eventMode, location, testFn, pruneFn, ignore || pruneFn(currentTarget, location));\n        if (nestedHit) {\n          if (nestedHit.length > 0 && !nestedHit[nestedHit.length - 1].parent) continue;\n          const isInteractive = currentTarget.isInteractive();\n          (nestedHit.length > 0 || isInteractive) && (isInteractive && this._allInteractiveElements.push(currentTarget), nestedHit.push(currentTarget)), this._hitElements.length === 0 && (this._hitElements = nestedHit), shouldReturn = !0;\n        }\n      }\n    }\n    const isInteractiveMode = this._isInteractive(eventMode),\n      isInteractiveTarget = currentTarget.isInteractive();\n    return isInteractiveMode && isInteractiveTarget && this._allInteractiveElements.push(currentTarget), ignore || this._hitElements.length > 0 ? null : shouldReturn ? this._hitElements : isInteractiveMode && !pruneFn(currentTarget, location) && testFn(currentTarget, location) ? isInteractiveTarget ? [currentTarget] : [] : null;\n  }\n  /**\n   * Recursive implementation for {@link PIXI.EventBoundary.hitTest hitTest}.\n   * @param currentTarget - The DisplayObject that is to be hit tested.\n   * @param eventMode - The event mode for the `currentTarget` or one of its parents.\n   * @param location - The location that is being tested for overlap.\n   * @param testFn - Callback that determines whether the target passes hit testing. This callback\n   *  can assume that `pruneFn` failed to prune the display object.\n   * @param pruneFn - Callback that determiness whether the target and all of its children\n   *  cannot pass the hit test. It is used as a preliminary optimization to prune entire subtrees\n   *  of the scene graph.\n   * @returns An array holding the hit testing target and all its ancestors in order. The first element\n   *  is the target itself and the last is {@link PIXI.EventBoundary.rootTarget rootTarget}. This is the opposite\n   *  order w.r.t. the propagation path. If no hit testing target is found, null is returned.\n   */\n  hitTestRecursive(currentTarget, eventMode, location, testFn, pruneFn) {\n    if (this._interactivePrune(currentTarget) || pruneFn(currentTarget, location)) return null;\n    if ((currentTarget.eventMode === \"dynamic\" || eventMode === \"dynamic\") && (EventsTicker.pauseUpdate = !1), currentTarget.interactiveChildren && currentTarget.children) {\n      const children = currentTarget.children;\n      for (let i = children.length - 1; i >= 0; i--) {\n        const child = children[i],\n          nestedHit = this.hitTestRecursive(child, this._isInteractive(eventMode) ? eventMode : child.eventMode, location, testFn, pruneFn);\n        if (nestedHit) {\n          if (nestedHit.length > 0 && !nestedHit[nestedHit.length - 1].parent) continue;\n          const isInteractive = currentTarget.isInteractive();\n          return (nestedHit.length > 0 || isInteractive) && nestedHit.push(currentTarget), nestedHit;\n        }\n      }\n    }\n    const isInteractiveMode = this._isInteractive(eventMode),\n      isInteractiveTarget = currentTarget.isInteractive();\n    return isInteractiveMode && testFn(currentTarget, location) ? isInteractiveTarget ? [currentTarget] : [] : null;\n  }\n  _isInteractive(int) {\n    return int === \"static\" || int === \"dynamic\";\n  }\n  _interactivePrune(displayObject) {\n    return !!(!displayObject || displayObject.isMask || !displayObject.visible || !displayObject.renderable || displayObject.eventMode === \"none\" || displayObject.eventMode === \"passive\" && !displayObject.interactiveChildren || displayObject.isMask);\n  }\n  /**\n   * Checks whether the display object or any of its children cannot pass the hit test at all.\n   *\n   * {@link PIXI.EventBoundary}'s implementation uses the {@link PIXI.DisplayObject.hitArea hitArea}\n   * and {@link PIXI.DisplayObject._mask} for pruning.\n   * @param displayObject\n   * @param location\n   */\n  hitPruneFn(displayObject, location) {\n    if (displayObject.hitArea && (displayObject.worldTransform.applyInverse(location, tempLocalMapping), !displayObject.hitArea.contains(tempLocalMapping.x, tempLocalMapping.y))) return !0;\n    if (displayObject._mask) {\n      const maskObject = displayObject._mask.isMaskData ? displayObject._mask.maskObject : displayObject._mask;\n      if (maskObject && !maskObject.containsPoint?.(location)) return !0;\n    }\n    return !1;\n  }\n  /**\n   * Checks whether the display object passes hit testing for the given location.\n   * @param displayObject\n   * @param location\n   * @returns - Whether `displayObject` passes hit testing for `location`.\n   */\n  hitTestFn(displayObject, location) {\n    return displayObject.eventMode === \"passive\" ? !1 : displayObject.hitArea ? !0 : displayObject.containsPoint ? displayObject.containsPoint(location) : !1;\n  }\n  /**\n   * Notify all the listeners to the event's `currentTarget`.\n   *\n   * If the `currentTarget` contains the property `on<type>`, then it is called here,\n   * simulating the behavior from version 6.x and prior.\n   * @param e - The event passed to the target.\n   * @param type\n   */\n  notifyTarget(e, type) {\n    type = type ?? e.type;\n    const handlerKey = `on${type}`;\n    e.currentTarget[handlerKey]?.(e);\n    const key = e.eventPhase === e.CAPTURING_PHASE || e.eventPhase === e.AT_TARGET ? `${type}capture` : type;\n    this.notifyListeners(e, key), e.eventPhase === e.AT_TARGET && this.notifyListeners(e, type);\n  }\n  /**\n   * Maps the upstream `pointerdown` events to a downstream `pointerdown` event.\n   *\n   * `touchstart`, `rightdown`, `mousedown` events are also dispatched for specific pointer types.\n   * @param from\n   */\n  mapPointerDown(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      console.warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    const e = this.createPointerEvent(from);\n    if (this.dispatchEvent(e, \"pointerdown\"), e.pointerType === \"touch\") this.dispatchEvent(e, \"touchstart\");else if (e.pointerType === \"mouse\" || e.pointerType === \"pen\") {\n      const isRightButton = e.button === 2;\n      this.dispatchEvent(e, isRightButton ? \"rightdown\" : \"mousedown\");\n    }\n    const trackingData = this.trackingData(from.pointerId);\n    trackingData.pressTargetsByButton[from.button] = e.composedPath(), this.freeEvent(e);\n  }\n  /**\n   * Maps the upstream `pointermove` to downstream `pointerout`, `pointerover`, and `pointermove` events, in that order.\n   *\n   * The tracking data for the specific pointer has an updated `overTarget`. `mouseout`, `mouseover`,\n   * `mousemove`, and `touchmove` events are fired as well for specific pointer types.\n   * @param from - The upstream `pointermove` event.\n   */\n  mapPointerMove(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      console.warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    this._allInteractiveElements.length = 0, this._hitElements.length = 0, this._isPointerMoveEvent = !0;\n    const e = this.createPointerEvent(from);\n    this._isPointerMoveEvent = !1;\n    const isMouse = e.pointerType === \"mouse\" || e.pointerType === \"pen\",\n      trackingData = this.trackingData(from.pointerId),\n      outTarget = this.findMountedTarget(trackingData.overTargets);\n    if (trackingData.overTargets?.length > 0 && outTarget !== e.target) {\n      const outType = from.type === \"mousemove\" ? \"mouseout\" : \"pointerout\",\n        outEvent = this.createPointerEvent(from, outType, outTarget);\n      if (this.dispatchEvent(outEvent, \"pointerout\"), isMouse && this.dispatchEvent(outEvent, \"mouseout\"), !e.composedPath().includes(outTarget)) {\n        const leaveEvent = this.createPointerEvent(from, \"pointerleave\", outTarget);\n        for (leaveEvent.eventPhase = leaveEvent.AT_TARGET; leaveEvent.target && !e.composedPath().includes(leaveEvent.target);) leaveEvent.currentTarget = leaveEvent.target, this.notifyTarget(leaveEvent), isMouse && this.notifyTarget(leaveEvent, \"mouseleave\"), leaveEvent.target = leaveEvent.target.parent;\n        this.freeEvent(leaveEvent);\n      }\n      this.freeEvent(outEvent);\n    }\n    if (outTarget !== e.target) {\n      const overType = from.type === \"mousemove\" ? \"mouseover\" : \"pointerover\",\n        overEvent = this.clonePointerEvent(e, overType);\n      this.dispatchEvent(overEvent, \"pointerover\"), isMouse && this.dispatchEvent(overEvent, \"mouseover\");\n      let overTargetAncestor = outTarget?.parent;\n      for (; overTargetAncestor && overTargetAncestor !== this.rootTarget.parent && overTargetAncestor !== e.target;) overTargetAncestor = overTargetAncestor.parent;\n      if (!overTargetAncestor || overTargetAncestor === this.rootTarget.parent) {\n        const enterEvent = this.clonePointerEvent(e, \"pointerenter\");\n        for (enterEvent.eventPhase = enterEvent.AT_TARGET; enterEvent.target && enterEvent.target !== outTarget && enterEvent.target !== this.rootTarget.parent;) enterEvent.currentTarget = enterEvent.target, this.notifyTarget(enterEvent), isMouse && this.notifyTarget(enterEvent, \"mouseenter\"), enterEvent.target = enterEvent.target.parent;\n        this.freeEvent(enterEvent);\n      }\n      this.freeEvent(overEvent);\n    }\n    const allMethods = [],\n      allowGlobalPointerEvents = this.enableGlobalMoveEvents ?? !0;\n    this.moveOnAll ? allMethods.push(\"pointermove\") : this.dispatchEvent(e, \"pointermove\"), allowGlobalPointerEvents && allMethods.push(\"globalpointermove\"), e.pointerType === \"touch\" && (this.moveOnAll ? allMethods.splice(1, 0, \"touchmove\") : this.dispatchEvent(e, \"touchmove\"), allowGlobalPointerEvents && allMethods.push(\"globaltouchmove\")), isMouse && (this.moveOnAll ? allMethods.splice(1, 0, \"mousemove\") : this.dispatchEvent(e, \"mousemove\"), allowGlobalPointerEvents && allMethods.push(\"globalmousemove\"), this.cursor = e.target?.cursor), allMethods.length > 0 && this.all(e, allMethods), this._allInteractiveElements.length = 0, this._hitElements.length = 0, trackingData.overTargets = e.composedPath(), this.freeEvent(e);\n  }\n  /**\n   * Maps the upstream `pointerover` to downstream `pointerover` and `pointerenter` events, in that order.\n   *\n   * The tracking data for the specific pointer gets a new `overTarget`.\n   * @param from - The upstream `pointerover` event.\n   */\n  mapPointerOver(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      console.warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    const trackingData = this.trackingData(from.pointerId),\n      e = this.createPointerEvent(from),\n      isMouse = e.pointerType === \"mouse\" || e.pointerType === \"pen\";\n    this.dispatchEvent(e, \"pointerover\"), isMouse && this.dispatchEvent(e, \"mouseover\"), e.pointerType === \"mouse\" && (this.cursor = e.target?.cursor);\n    const enterEvent = this.clonePointerEvent(e, \"pointerenter\");\n    for (enterEvent.eventPhase = enterEvent.AT_TARGET; enterEvent.target && enterEvent.target !== this.rootTarget.parent;) enterEvent.currentTarget = enterEvent.target, this.notifyTarget(enterEvent), isMouse && this.notifyTarget(enterEvent, \"mouseenter\"), enterEvent.target = enterEvent.target.parent;\n    trackingData.overTargets = e.composedPath(), this.freeEvent(e), this.freeEvent(enterEvent);\n  }\n  /**\n   * Maps the upstream `pointerout` to downstream `pointerout`, `pointerleave` events, in that order.\n   *\n   * The tracking data for the specific pointer is cleared of a `overTarget`.\n   * @param from - The upstream `pointerout` event.\n   */\n  mapPointerOut(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      console.warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    const trackingData = this.trackingData(from.pointerId);\n    if (trackingData.overTargets) {\n      const isMouse = from.pointerType === \"mouse\" || from.pointerType === \"pen\",\n        outTarget = this.findMountedTarget(trackingData.overTargets),\n        outEvent = this.createPointerEvent(from, \"pointerout\", outTarget);\n      this.dispatchEvent(outEvent), isMouse && this.dispatchEvent(outEvent, \"mouseout\");\n      const leaveEvent = this.createPointerEvent(from, \"pointerleave\", outTarget);\n      for (leaveEvent.eventPhase = leaveEvent.AT_TARGET; leaveEvent.target && leaveEvent.target !== this.rootTarget.parent;) leaveEvent.currentTarget = leaveEvent.target, this.notifyTarget(leaveEvent), isMouse && this.notifyTarget(leaveEvent, \"mouseleave\"), leaveEvent.target = leaveEvent.target.parent;\n      trackingData.overTargets = null, this.freeEvent(outEvent), this.freeEvent(leaveEvent);\n    }\n    this.cursor = null;\n  }\n  /**\n   * Maps the upstream `pointerup` event to downstream `pointerup`, `pointerupoutside`,\n   * and `click`/`rightclick`/`pointertap` events, in that order.\n   *\n   * The `pointerupoutside` event bubbles from the original `pointerdown` target to the most specific\n   * ancestor of the `pointerdown` and `pointerup` targets, which is also the `click` event's target. `touchend`,\n   * `rightup`, `mouseup`, `touchendoutside`, `rightupoutside`, `mouseupoutside`, and `tap` are fired as well for\n   * specific pointer types.\n   * @param from - The upstream `pointerup` event.\n   */\n  mapPointerUp(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      console.warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    const now = performance.now(),\n      e = this.createPointerEvent(from);\n    if (this.dispatchEvent(e, \"pointerup\"), e.pointerType === \"touch\") this.dispatchEvent(e, \"touchend\");else if (e.pointerType === \"mouse\" || e.pointerType === \"pen\") {\n      const isRightButton = e.button === 2;\n      this.dispatchEvent(e, isRightButton ? \"rightup\" : \"mouseup\");\n    }\n    const trackingData = this.trackingData(from.pointerId),\n      pressTarget = this.findMountedTarget(trackingData.pressTargetsByButton[from.button]);\n    let clickTarget = pressTarget;\n    if (pressTarget && !e.composedPath().includes(pressTarget)) {\n      let currentTarget = pressTarget;\n      for (; currentTarget && !e.composedPath().includes(currentTarget);) {\n        if (e.currentTarget = currentTarget, this.notifyTarget(e, \"pointerupoutside\"), e.pointerType === \"touch\") this.notifyTarget(e, \"touchendoutside\");else if (e.pointerType === \"mouse\" || e.pointerType === \"pen\") {\n          const isRightButton = e.button === 2;\n          this.notifyTarget(e, isRightButton ? \"rightupoutside\" : \"mouseupoutside\");\n        }\n        currentTarget = currentTarget.parent;\n      }\n      delete trackingData.pressTargetsByButton[from.button], clickTarget = currentTarget;\n    }\n    if (clickTarget) {\n      const clickEvent = this.clonePointerEvent(e, \"click\");\n      clickEvent.target = clickTarget, clickEvent.path = null, trackingData.clicksByButton[from.button] || (trackingData.clicksByButton[from.button] = {\n        clickCount: 0,\n        target: clickEvent.target,\n        timeStamp: now\n      });\n      const clickHistory = trackingData.clicksByButton[from.button];\n      if (clickHistory.target === clickEvent.target && now - clickHistory.timeStamp < 200 ? ++clickHistory.clickCount : clickHistory.clickCount = 1, clickHistory.target = clickEvent.target, clickHistory.timeStamp = now, clickEvent.detail = clickHistory.clickCount, clickEvent.pointerType === \"mouse\") {\n        const isRightButton = clickEvent.button === 2;\n        this.dispatchEvent(clickEvent, isRightButton ? \"rightclick\" : \"click\");\n      } else clickEvent.pointerType === \"touch\" && this.dispatchEvent(clickEvent, \"tap\");\n      this.dispatchEvent(clickEvent, \"pointertap\"), this.freeEvent(clickEvent);\n    }\n    this.freeEvent(e);\n  }\n  /**\n   * Maps the upstream `pointerupoutside` event to a downstream `pointerupoutside` event, bubbling from the original\n   * `pointerdown` target to `rootTarget`.\n   *\n   * (The most specific ancestor of the `pointerdown` event and the `pointerup` event must the\n   * `{@link PIXI.EventBoundary}'s root because the `pointerup` event occurred outside of the boundary.)\n   *\n   * `touchendoutside`, `mouseupoutside`, and `rightupoutside` events are fired as well for specific pointer\n   * types. The tracking data for the specific pointer is cleared of a `pressTarget`.\n   * @param from - The upstream `pointerupoutside` event.\n   */\n  mapPointerUpOutside(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      console.warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    const trackingData = this.trackingData(from.pointerId),\n      pressTarget = this.findMountedTarget(trackingData.pressTargetsByButton[from.button]),\n      e = this.createPointerEvent(from);\n    if (pressTarget) {\n      let currentTarget = pressTarget;\n      for (; currentTarget;) e.currentTarget = currentTarget, this.notifyTarget(e, \"pointerupoutside\"), e.pointerType === \"touch\" ? this.notifyTarget(e, \"touchendoutside\") : (e.pointerType === \"mouse\" || e.pointerType === \"pen\") && this.notifyTarget(e, e.button === 2 ? \"rightupoutside\" : \"mouseupoutside\"), currentTarget = currentTarget.parent;\n      delete trackingData.pressTargetsByButton[from.button];\n    }\n    this.freeEvent(e);\n  }\n  /**\n   * Maps the upstream `wheel` event to a downstream `wheel` event.\n   * @param from - The upstream `wheel` event.\n   */\n  mapWheel(from) {\n    if (!(from instanceof FederatedWheelEvent)) {\n      console.warn(\"EventBoundary cannot map a non-wheel event as a wheel event\");\n      return;\n    }\n    const wheelEvent = this.createWheelEvent(from);\n    this.dispatchEvent(wheelEvent), this.freeEvent(wheelEvent);\n  }\n  /**\n   * Finds the most specific event-target in the given propagation path that is still mounted in the scene graph.\n   *\n   * This is used to find the correct `pointerup` and `pointerout` target in the case that the original `pointerdown`\n   * or `pointerover` target was unmounted from the scene graph.\n   * @param propagationPath - The propagation path was valid in the past.\n   * @returns - The most specific event-target still mounted at the same location in the scene graph.\n   */\n  findMountedTarget(propagationPath) {\n    if (!propagationPath) return null;\n    let currentTarget = propagationPath[0];\n    for (let i = 1; i < propagationPath.length && propagationPath[i].parent === currentTarget; i++) currentTarget = propagationPath[i];\n    return currentTarget;\n  }\n  /**\n   * Creates an event whose {@code originalEvent} is {@code from}, with an optional `type` and `target` override.\n   *\n   * The event is allocated using {@link PIXI.EventBoundary#allocateEvent this.allocateEvent}.\n   * @param from - The {@code originalEvent} for the returned event.\n   * @param [type=from.type] - The type of the returned event.\n   * @param target - The target of the returned event.\n   */\n  createPointerEvent(from, type, target) {\n    const event = this.allocateEvent(FederatedPointerEvent);\n    return this.copyPointerData(from, event), this.copyMouseData(from, event), this.copyData(from, event), event.nativeEvent = from.nativeEvent, event.originalEvent = from, event.target = target ?? this.hitTest(event.global.x, event.global.y) ?? this._hitElements[0], typeof type == \"string\" && (event.type = type), event;\n  }\n  /**\n   * Creates a wheel event whose {@code originalEvent} is {@code from}.\n   *\n   * The event is allocated using {@link PIXI.EventBoundary#allocateEvent this.allocateEvent}.\n   * @param from - The upstream wheel event.\n   */\n  createWheelEvent(from) {\n    const event = this.allocateEvent(FederatedWheelEvent);\n    return this.copyWheelData(from, event), this.copyMouseData(from, event), this.copyData(from, event), event.nativeEvent = from.nativeEvent, event.originalEvent = from, event.target = this.hitTest(event.global.x, event.global.y), event;\n  }\n  /**\n   * Clones the event {@code from}, with an optional {@code type} override.\n   *\n   * The event is allocated using {@link PIXI.EventBoundary#allocateEvent this.allocateEvent}.\n   * @param from - The event to clone.\n   * @param [type=from.type] - The type of the returned event.\n   */\n  clonePointerEvent(from, type) {\n    const event = this.allocateEvent(FederatedPointerEvent);\n    return event.nativeEvent = from.nativeEvent, event.originalEvent = from.originalEvent, this.copyPointerData(from, event), this.copyMouseData(from, event), this.copyData(from, event), event.target = from.target, event.path = from.composedPath().slice(), event.type = type ?? event.type, event;\n  }\n  /**\n   * Copies wheel {@link PIXI.FederatedWheelEvent} data from {@code from} into {@code to}.\n   *\n   * The following properties are copied:\n   * + deltaMode\n   * + deltaX\n   * + deltaY\n   * + deltaZ\n   * @param from\n   * @param to\n   */\n  copyWheelData(from, to) {\n    to.deltaMode = from.deltaMode, to.deltaX = from.deltaX, to.deltaY = from.deltaY, to.deltaZ = from.deltaZ;\n  }\n  /**\n   * Copies pointer {@link PIXI.FederatedPointerEvent} data from {@code from} into {@code to}.\n   *\n   * The following properties are copied:\n   * + pointerId\n   * + width\n   * + height\n   * + isPrimary\n   * + pointerType\n   * + pressure\n   * + tangentialPressure\n   * + tiltX\n   * + tiltY\n   * @param from\n   * @param to\n   */\n  copyPointerData(from, to) {\n    from instanceof FederatedPointerEvent && to instanceof FederatedPointerEvent && (to.pointerId = from.pointerId, to.width = from.width, to.height = from.height, to.isPrimary = from.isPrimary, to.pointerType = from.pointerType, to.pressure = from.pressure, to.tangentialPressure = from.tangentialPressure, to.tiltX = from.tiltX, to.tiltY = from.tiltY, to.twist = from.twist);\n  }\n  /**\n   * Copies mouse {@link PIXI.FederatedMouseEvent} data from {@code from} to {@code to}.\n   *\n   * The following properties are copied:\n   * + altKey\n   * + button\n   * + buttons\n   * + clientX\n   * + clientY\n   * + metaKey\n   * + movementX\n   * + movementY\n   * + pageX\n   * + pageY\n   * + x\n   * + y\n   * + screen\n   * + shiftKey\n   * + global\n   * @param from\n   * @param to\n   */\n  copyMouseData(from, to) {\n    from instanceof FederatedMouseEvent && to instanceof FederatedMouseEvent && (to.altKey = from.altKey, to.button = from.button, to.buttons = from.buttons, to.client.copyFrom(from.client), to.ctrlKey = from.ctrlKey, to.metaKey = from.metaKey, to.movement.copyFrom(from.movement), to.screen.copyFrom(from.screen), to.shiftKey = from.shiftKey, to.global.copyFrom(from.global));\n  }\n  /**\n   * Copies base {@link PIXI.FederatedEvent} data from {@code from} into {@code to}.\n   *\n   * The following properties are copied:\n   * + isTrusted\n   * + srcElement\n   * + timeStamp\n   * + type\n   * @param from - The event to copy data from.\n   * @param to - The event to copy data into.\n   */\n  copyData(from, to) {\n    to.isTrusted = from.isTrusted, to.srcElement = from.srcElement, to.timeStamp = performance.now(), to.type = from.type, to.detail = from.detail, to.view = from.view, to.which = from.which, to.layer.copyFrom(from.layer), to.page.copyFrom(from.page);\n  }\n  /**\n   * @param id - The pointer ID.\n   * @returns The tracking data stored for the given pointer. If no data exists, a blank\n   *  state will be created.\n   */\n  trackingData(id) {\n    return this.mappingState.trackingData[id] || (this.mappingState.trackingData[id] = {\n      pressTargetsByButton: {},\n      clicksByButton: {},\n      overTarget: null\n    }), this.mappingState.trackingData[id];\n  }\n  /**\n   * Allocate a specific type of event from {@link PIXI.EventBoundary#eventPool this.eventPool}.\n   *\n   * This allocation is constructor-agnostic, as long as it only takes one argument - this event\n   * boundary.\n   * @param constructor - The event's constructor.\n   */\n  allocateEvent(constructor) {\n    this.eventPool.has(constructor) || this.eventPool.set(constructor, []);\n    const event = this.eventPool.get(constructor).pop() || new constructor(this);\n    return event.eventPhase = event.NONE, event.currentTarget = null, event.path = null, event.target = null, event;\n  }\n  /**\n   * Frees the event and puts it back into the event pool.\n   *\n   * It is illegal to reuse the event until it is allocated again, using `this.allocateEvent`.\n   *\n   * It is also advised that events not allocated from {@link PIXI.EventBoundary#allocateEvent this.allocateEvent}\n   * not be freed. This is because of the possibility that the same event is freed twice, which can cause\n   * it to be allocated twice & result in overwriting.\n   * @param event - The event to be freed.\n   * @throws Error if the event is managed by another event boundary.\n   */\n  freeEvent(event) {\n    if (event.manager !== this) throw new Error(\"It is illegal to free an event not managed by this EventBoundary!\");\n    const constructor = event.constructor;\n    this.eventPool.has(constructor) || this.eventPool.set(constructor, []), this.eventPool.get(constructor).push(event);\n  }\n  /**\n   * Similar to {@link PIXI.EventEmitter.emit}, except it stops if the `propagationImmediatelyStopped` flag\n   * is set on the event.\n   * @param e - The event to call each listener with.\n   * @param type - The event key.\n   */\n  notifyListeners(e, type) {\n    const listeners = e.currentTarget._events[type];\n    if (listeners && e.currentTarget.isInteractive()) if (\"fn\" in listeners) listeners.once && e.currentTarget.removeListener(type, listeners.fn, void 0, !0), listeners.fn.call(listeners.context, e);else for (let i = 0, j = listeners.length; i < j && !e.propagationImmediatelyStopped; i++) listeners[i].once && e.currentTarget.removeListener(type, listeners[i].fn, void 0, !0), listeners[i].fn.call(listeners[i].context, e);\n  }\n}\nexport { EventBoundary };", "map": {"version": 3, "names": ["PROPAGATION_LIMIT", "tempHitLocation", "Point", "tempLocalMapping", "EventBoundary", "constructor", "rootTarget", "dispatch", "utils", "EventEmitter", "moveOnAll", "enableGlobalMoveEvents", "mappingState", "trackingData", "eventPool", "Map", "_allInteractiveElements", "_hitElements", "_isPointerMoveEvent", "hitPruneFn", "bind", "hitTestFn", "mapPointerDown", "mapPointerMove", "mapPointerOut", "mapPointerOver", "mapPointerUp", "mapPointerUpOutside", "mapWheel", "mappingTable", "addEventMapping", "type", "fn", "push", "priority", "sort", "a", "b", "dispatchEvent", "e", "propagationStopped", "propagationImmediatelyStopped", "propagate", "emit", "mapEvent", "mappers", "i", "j", "length", "console", "warn", "hitTest", "x", "y", "EventsTicker", "pauseUpdate", "invertedPath", "eventMode", "set", "target", "<PERSON><PERSON><PERSON>", "eventPhase", "CAPTURING_PHASE", "currentTarget", "notify<PERSON><PERSON><PERSON>", "AT_TARGET", "BUBBLING_PHASE", "all", "targets", "events", "Array", "isArray", "for<PERSON>ach", "event", "propagationPath", "parent", "Error", "reverse", "hitTestMoveRecursive", "location", "testFn", "pruneFn", "ignore", "shouldReturn", "_interactivePrune", "interactiveChildren", "children", "child", "nestedHit", "_isInteractive", "isInteractive", "isInteractiveMode", "isInteractiveTarget", "hitTestRecursive", "int", "displayObject", "isMask", "visible", "renderable", "hitArea", "worldTransform", "applyInverse", "contains", "_mask", "maskObject", "isMaskData", "containsPoint", "handler<PERSON><PERSON>", "key", "notifyListeners", "from", "FederatedPointerEvent", "createPointerEvent", "pointerType", "isRightButton", "button", "pointerId", "pressTargetsByButton", "freeEvent", "isMouse", "outTarget", "findMountedTarget", "overTargets", "outType", "outEvent", "includes", "leaveEvent", "overType", "overEvent", "clonePointerEvent", "overTargetA<PERSON>tor", "enterEvent", "allMethods", "allowGlobalPointerEvents", "splice", "cursor", "now", "performance", "pressTarget", "clickTarget", "clickEvent", "path", "clicksByButton", "clickCount", "timeStamp", "clickHistory", "detail", "FederatedWheelEvent", "wheelEvent", "createWheelEvent", "allocateEvent", "copyPointerData", "copyMouseData", "copyData", "nativeEvent", "originalEvent", "global", "copyWheelData", "slice", "to", "deltaMode", "deltaX", "deltaY", "deltaZ", "width", "height", "isPrimary", "pressure", "tangentialPressure", "tiltX", "tiltY", "twist", "FederatedMouseEvent", "altKey", "buttons", "client", "copyFrom", "ctrl<PERSON>ey", "metaKey", "movement", "screen", "shift<PERSON>ey", "isTrusted", "srcElement", "view", "which", "layer", "page", "id", "overTarget", "has", "get", "pop", "NONE", "manager", "listeners", "_events", "once", "removeListener", "call", "context"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\events\\src\\EventBoundary.ts"], "sourcesContent": ["import { Point, utils } from '@pixi/core';\nimport { EventsTicker } from './EventTicker';\nimport { FederatedMouseEvent } from './FederatedMouseEvent';\nimport { FederatedPointerEvent } from './FederatedPointerEvent';\nimport { FederatedWheelEvent } from './FederatedWheelEvent';\n\nimport type { DisplayObject } from '@pixi/display';\nimport type { EmitterListeners, TrackingData } from './EventBoundaryTypes';\nimport type { FederatedEvent } from './FederatedEvent';\nimport type {\n    <PERSON>urs<PERSON>, EventMode, FederatedEventHandler,\n    FederatedEventTarget,\n    IFederatedDisplayObject\n} from './FederatedEventTarget';\n\n// The maximum iterations used in propagation. This prevent infinite loops.\nconst PROPAGATION_LIMIT = 2048;\n\nconst tempHitLocation = new Point();\nconst tempLocalMapping = new Point();\n\n/**\n * Event boundaries are \"barriers\" where events coming from an upstream scene are modified before downstream propagation.\n *\n * ## Root event boundary\n *\n * The {@link PIXI.EventSystem#rootBoundary rootBoundary} handles events coming from the &lt;canvas /&gt;.\n * {@link PIXI.EventSystem} handles the normalization from native {@link https://dom.spec.whatwg.org/#event Events}\n * into {@link PIXI.FederatedEvent FederatedEvents}. The rootBoundary then does the hit-testing and event dispatch\n * for the upstream normalized event.\n *\n * ## Additional event boundaries\n *\n * An additional event boundary may be desired within an application's scene graph. For example, if a portion of the scene is\n * is flat with many children at one level - a spatial hash maybe needed to accelerate hit testing. In this scenario, the\n * container can be detached from the scene and glued using a custom event boundary.\n *\n * ```ts\n * import { Container } from '@pixi/display';\n * import { EventBoundary } from '@pixi/events';\n * import { SpatialHash } from 'pixi-spatial-hash';\n *\n * class HashedHitTestingEventBoundary\n * {\n *     private spatialHash: SpatialHash;\n *\n *     constructor(scene: Container, spatialHash: SpatialHash)\n *     {\n *         super(scene);\n *         this.spatialHash = spatialHash;\n *     }\n *\n *     hitTestRecursive(...)\n *     {\n *         // TODO: If target === this.rootTarget, then use spatial hash to get a\n *         // list of possible children that match the given (x,y) coordinates.\n *     }\n * }\n *\n * class VastScene extends DisplayObject\n * {\n *     protected eventBoundary: EventBoundary;\n *     protected scene: Container;\n *     protected spatialHash: SpatialHash;\n *\n *     constructor()\n *     {\n *         this.scene = new Container();\n *         this.spatialHash = new SpatialHash();\n *         this.eventBoundary = new HashedHitTestingEventBoundary(this.scene, this.spatialHash);\n *\n *         // Populate this.scene with a ton of children, while updating this.spatialHash\n *     }\n * }\n * ```\n * @memberof PIXI\n */\nexport class EventBoundary\n{\n    /**\n     * The root event-target residing below the event boundary.\n     *\n     * All events are dispatched trickling down and bubbling up to this `rootTarget`.\n     */\n    public rootTarget: DisplayObject;\n\n    /**\n     * Emits events after they were dispatched into the scene graph.\n     *\n     * This can be used for global events listening, regardless of the scene graph being used. It should\n     * not be used by interactive libraries for normal use.\n     *\n     * Special events that do not bubble all the way to the root target are not emitted from here,\n     * e.g. pointerenter, pointerleave, click.\n     */\n    public dispatch: utils.EventEmitter = new utils.EventEmitter();\n\n    /** The cursor preferred by the event targets underneath this boundary. */\n    public cursor: Cursor | string;\n\n    /**\n     * This flag would emit `pointermove`, `touchmove`, and `mousemove` events on all DisplayObjects.\n     *\n     * The `moveOnAll` semantics mirror those of earlier versions of PixiJS. This was disabled in favor of\n     * the Pointer Event API's approach.\n     */\n    public moveOnAll = false;\n\n    /** Enables the global move events. `globalpointermove`, `globaltouchmove`, and `globalmousemove` */\n    public enableGlobalMoveEvents = true;\n\n    /**\n     * Maps event types to forwarding handles for them.\n     *\n     * {@link PIXI.EventBoundary EventBoundary} provides mapping for \"pointerdown\", \"pointermove\",\n     * \"pointerout\", \"pointerleave\", \"pointerover\", \"pointerup\", and \"pointerupoutside\" by default.\n     * @see PIXI.EventBoundary#addEventMapping\n     */\n    protected mappingTable: Record<string, Array<{\n        fn: (e: FederatedEvent) => void,\n        priority: number\n    }>>;\n\n    /**\n     * State object for mapping methods.\n     * @see PIXI.EventBoundary#trackingData\n     */\n    protected mappingState: Record<string, any> = {\n        trackingData: {}\n    };\n\n    /**\n     * The event pool maps event constructors to an free pool of instances of those specific events.\n     * @see PIXI.EventBoundary#allocateEvent\n     * @see PIXI.EventBoundary#freeEvent\n     */\n    protected eventPool: Map<typeof FederatedEvent, FederatedEvent[]> = new Map();\n\n    /** Every interactive element gathered from the scene. Only used in `pointermove` */\n    private _allInteractiveElements: FederatedEventTarget[] = [];\n    /** Every element that passed the hit test. Only used in `pointermove` */\n    private _hitElements: FederatedEventTarget[] = [];\n    /** Whether or not to collect all the interactive elements from the scene. Enabled in `pointermove` */\n    private _isPointerMoveEvent = false;\n\n    /**\n     * @param rootTarget - The holder of the event boundary.\n     */\n    constructor(rootTarget?: DisplayObject)\n    {\n        this.rootTarget = rootTarget;\n\n        this.hitPruneFn = this.hitPruneFn.bind(this);\n        this.hitTestFn = this.hitTestFn.bind(this);\n        this.mapPointerDown = this.mapPointerDown.bind(this);\n        this.mapPointerMove = this.mapPointerMove.bind(this);\n        this.mapPointerOut = this.mapPointerOut.bind(this);\n        this.mapPointerOver = this.mapPointerOver.bind(this);\n        this.mapPointerUp = this.mapPointerUp.bind(this);\n        this.mapPointerUpOutside = this.mapPointerUpOutside.bind(this);\n        this.mapWheel = this.mapWheel.bind(this);\n\n        this.mappingTable = {};\n        this.addEventMapping('pointerdown', this.mapPointerDown);\n        this.addEventMapping('pointermove', this.mapPointerMove);\n        this.addEventMapping('pointerout', this.mapPointerOut);\n        this.addEventMapping('pointerleave', this.mapPointerOut);\n        this.addEventMapping('pointerover', this.mapPointerOver);\n        this.addEventMapping('pointerup', this.mapPointerUp);\n        this.addEventMapping('pointerupoutside', this.mapPointerUpOutside);\n        this.addEventMapping('wheel', this.mapWheel);\n    }\n\n    /**\n     * Adds an event mapping for the event `type` handled by `fn`.\n     *\n     * Event mappings can be used to implement additional or custom events. They take an event\n     * coming from the upstream scene (or directly from the {@link PIXI.EventSystem}) and dispatch new downstream events\n     * generally trickling down and bubbling up to {@link PIXI.EventBoundary.rootTarget this.rootTarget}.\n     *\n     * To modify the semantics of existing events, the built-in mapping methods of EventBoundary should be overridden\n     * instead.\n     * @param type - The type of upstream event to map.\n     * @param fn - The mapping method. The context of this function must be bound manually, if desired.\n     */\n    public addEventMapping(type: string, fn: (e: FederatedEvent) => void): void\n    {\n        if (!this.mappingTable[type])\n        {\n            this.mappingTable[type] = [];\n        }\n\n        this.mappingTable[type].push({\n            fn,\n            priority: 0,\n        });\n        this.mappingTable[type].sort((a, b) => a.priority - b.priority);\n    }\n\n    /**\n     * Dispatches the given event\n     * @param e\n     * @param type\n     */\n    public dispatchEvent(e: FederatedEvent, type?: string): void\n    {\n        e.propagationStopped = false;\n        e.propagationImmediatelyStopped = false;\n\n        this.propagate(e, type);\n        this.dispatch.emit(type || e.type, e);\n    }\n\n    /**\n     * Maps the given upstream event through the event boundary and propagates it downstream.\n     * @param e\n     */\n    public mapEvent(e: FederatedEvent): void\n    {\n        if (!this.rootTarget)\n        {\n            return;\n        }\n\n        const mappers = this.mappingTable[e.type];\n\n        if (mappers)\n        {\n            for (let i = 0, j = mappers.length; i < j; i++)\n            {\n                mappers[i].fn(e);\n            }\n        }\n        else\n        {\n            console.warn(`[EventBoundary]: Event mapping not defined for ${e.type}`);\n        }\n    }\n\n    /**\n     * Finds the DisplayObject that is the target of a event at the given coordinates.\n     *\n     * The passed (x,y) coordinates are in the world space above this event boundary.\n     * @param x\n     * @param y\n     */\n    public hitTest(\n        x: number,\n        y: number,\n    ): DisplayObject\n    {\n        EventsTicker.pauseUpdate = true;\n        // if we are using global move events, we need to hit test the whole scene graph\n        const useMove = this._isPointerMoveEvent && this.enableGlobalMoveEvents;\n        const fn = useMove ? 'hitTestMoveRecursive' : 'hitTestRecursive';\n        const invertedPath = this[fn](\n            this.rootTarget,\n            this.rootTarget.eventMode,\n            tempHitLocation.set(x, y),\n            this.hitTestFn,\n            this.hitPruneFn,\n        );\n\n        return invertedPath && invertedPath[0];\n    }\n\n    /**\n     * Propagate the passed event from from {@link PIXI.EventBoundary.rootTarget this.rootTarget} to its\n     * target {@code e.target}.\n     * @param e - The event to propagate.\n     * @param type\n     */\n    public propagate(e: FederatedEvent, type?: string): void\n    {\n        if (!e.target)\n        {\n            // This usually occurs when the scene graph is not interactive.\n            return;\n        }\n\n        const composedPath = e.composedPath();\n\n        // Capturing phase\n        e.eventPhase = e.CAPTURING_PHASE;\n\n        for (let i = 0, j = composedPath.length - 1; i < j; i++)\n        {\n            e.currentTarget = composedPath[i];\n\n            this.notifyTarget(e, type);\n\n            if (e.propagationStopped || e.propagationImmediatelyStopped) return;\n        }\n\n        // At target phase\n        e.eventPhase = e.AT_TARGET;\n        e.currentTarget = e.target;\n\n        this.notifyTarget(e, type);\n\n        if (e.propagationStopped || e.propagationImmediatelyStopped) return;\n\n        // Bubbling phase\n        e.eventPhase = e.BUBBLING_PHASE;\n\n        for (let i = composedPath.length - 2; i >= 0; i--)\n        {\n            e.currentTarget = composedPath[i];\n\n            this.notifyTarget(e, type);\n\n            if (e.propagationStopped || e.propagationImmediatelyStopped) return;\n        }\n    }\n\n    /**\n     * Emits the event {@code e} to all interactive display objects. The event is propagated in the bubbling phase always.\n     *\n     * This is used in the `globalpointermove` event.\n     * @param e - The emitted event.\n     * @param type - The listeners to notify.\n     * @param targets - The targets to notify.\n     */\n    public all(e: FederatedEvent, type?: string | string[], targets = this._allInteractiveElements): void\n    {\n        if (targets.length === 0) return;\n\n        e.eventPhase = e.BUBBLING_PHASE;\n\n        const events = Array.isArray(type) ? type : [type];\n\n        // loop through all interactive elements and notify them of the event\n        // loop through targets backwards\n        for (let i = targets.length - 1; i >= 0; i--)\n        {\n            events.forEach((event) =>\n            {\n                e.currentTarget = targets[i];\n                this.notifyTarget(e, event);\n            });\n        }\n    }\n\n    /**\n     * Finds the propagation path from {@link PIXI.EventBoundary.rootTarget rootTarget} to the passed\n     * {@code target}. The last element in the path is {@code target}.\n     * @param target\n     */\n    public propagationPath(target: FederatedEventTarget): FederatedEventTarget[]\n    {\n        const propagationPath = [target];\n\n        for (let i = 0; i < PROPAGATION_LIMIT && target !== this.rootTarget; i++)\n        {\n            if (!target.parent)\n            {\n                throw new Error('Cannot find propagation path to disconnected target');\n            }\n\n            propagationPath.push(target.parent);\n\n            target = target.parent;\n        }\n\n        propagationPath.reverse();\n\n        return propagationPath;\n    }\n\n    protected hitTestMoveRecursive(\n        currentTarget: DisplayObject,\n        eventMode: EventMode,\n        location: Point,\n        testFn: (object: DisplayObject, pt: Point) => boolean,\n        pruneFn?: (object: DisplayObject, pt: Point) => boolean,\n        ignore = false\n    ): DisplayObject[]\n    {\n        let shouldReturn = false;\n\n        // only bail out early if it is not interactive\n        if (this._interactivePrune(currentTarget)) return null;\n\n        if (currentTarget.eventMode === 'dynamic' || eventMode === 'dynamic')\n        {\n            EventsTicker.pauseUpdate = false;\n        }\n\n        if (currentTarget.interactiveChildren && currentTarget.children)\n        {\n            const children = currentTarget.children;\n\n            for (let i = children.length - 1; i >= 0; i--)\n            {\n                const child = children[i] as DisplayObject;\n\n                const nestedHit = this.hitTestMoveRecursive(\n                    child,\n                    this._isInteractive(eventMode) ? eventMode : child.eventMode,\n                    location,\n                    testFn,\n                    pruneFn,\n                    ignore || pruneFn(currentTarget, location)\n                );\n\n                if (nestedHit)\n                {\n                    // Its a good idea to check if a child has lost its parent.\n                    // this means it has been removed whilst looping so its best\n                    if (nestedHit.length > 0 && !nestedHit[nestedHit.length - 1].parent)\n                    {\n                        continue;\n                    }\n\n                    // Only add the current hit-test target to the hit-test chain if the chain\n                    // has already started (i.e. the event target has been found) or if the current\n                    // target is interactive (i.e. it becomes the event target).\n                    const isInteractive = currentTarget.isInteractive();\n\n                    if (nestedHit.length > 0 || isInteractive)\n                    {\n                        if (isInteractive) this._allInteractiveElements.push(currentTarget);\n                        nestedHit.push(currentTarget);\n                    }\n\n                    // store all hit elements to be returned once we have traversed the whole tree\n                    if (this._hitElements.length === 0) this._hitElements = nestedHit;\n\n                    shouldReturn = true;\n                }\n            }\n        }\n\n        const isInteractiveMode = this._isInteractive(eventMode);\n        const isInteractiveTarget = currentTarget.isInteractive();\n\n        if (isInteractiveMode && isInteractiveTarget) this._allInteractiveElements.push(currentTarget);\n\n        // we don't carry on hit testing something once we have found a hit,\n        // now only care about gathering the interactive elements\n        if (ignore || this._hitElements.length > 0) return null;\n\n        if (shouldReturn) return this._hitElements as DisplayObject[];\n\n        // Finally, hit test this DisplayObject itself.\n        if (isInteractiveMode && (!pruneFn(currentTarget, location) && testFn(currentTarget, location)))\n        {\n            // The current hit-test target is the event's target only if it is interactive. Otherwise,\n            // the first interactive ancestor will be the event's target.\n            return isInteractiveTarget ? [currentTarget] : [];\n        }\n\n        return null;\n    }\n\n    /**\n     * Recursive implementation for {@link PIXI.EventBoundary.hitTest hitTest}.\n     * @param currentTarget - The DisplayObject that is to be hit tested.\n     * @param eventMode - The event mode for the `currentTarget` or one of its parents.\n     * @param location - The location that is being tested for overlap.\n     * @param testFn - Callback that determines whether the target passes hit testing. This callback\n     *  can assume that `pruneFn` failed to prune the display object.\n     * @param pruneFn - Callback that determiness whether the target and all of its children\n     *  cannot pass the hit test. It is used as a preliminary optimization to prune entire subtrees\n     *  of the scene graph.\n     * @returns An array holding the hit testing target and all its ancestors in order. The first element\n     *  is the target itself and the last is {@link PIXI.EventBoundary.rootTarget rootTarget}. This is the opposite\n     *  order w.r.t. the propagation path. If no hit testing target is found, null is returned.\n     */\n    protected hitTestRecursive(\n        currentTarget: DisplayObject,\n        eventMode: EventMode,\n        location: Point,\n        testFn: (object: DisplayObject, pt: Point) => boolean,\n        pruneFn?: (object: DisplayObject, pt: Point) => boolean\n    ): DisplayObject[]\n    {\n        // Attempt to prune this DisplayObject and its subtree as an optimization.\n        if (this._interactivePrune(currentTarget) || pruneFn(currentTarget, location))\n        {\n            return null;\n        }\n\n        if (currentTarget.eventMode === 'dynamic' || eventMode === 'dynamic')\n        {\n            EventsTicker.pauseUpdate = false;\n        }\n\n        // Find a child that passes the hit testing and return one, if any.\n        if (currentTarget.interactiveChildren && currentTarget.children)\n        {\n            const children = currentTarget.children;\n\n            for (let i = children.length - 1; i >= 0; i--)\n            {\n                const child = children[i] as DisplayObject;\n\n                const nestedHit = this.hitTestRecursive(\n                    child,\n                    this._isInteractive(eventMode) ? eventMode : child.eventMode,\n                    location,\n                    testFn,\n                    pruneFn\n                );\n\n                if (nestedHit)\n                {\n                    // Its a good idea to check if a child has lost its parent.\n                    // this means it has been removed whilst looping so its best\n                    if (nestedHit.length > 0 && !nestedHit[nestedHit.length - 1].parent)\n                    {\n                        continue;\n                    }\n\n                    // Only add the current hit-test target to the hit-test chain if the chain\n                    // has already started (i.e. the event target has been found) or if the current\n                    // target is interactive (i.e. it becomes the event target).\n                    const isInteractive = currentTarget.isInteractive();\n\n                    if (nestedHit.length > 0 || isInteractive) nestedHit.push(currentTarget);\n\n                    return nestedHit;\n                }\n            }\n        }\n\n        const isInteractiveMode = this._isInteractive(eventMode);\n        const isInteractiveTarget = currentTarget.isInteractive();\n\n        // Finally, hit test this DisplayObject itself.\n        if (isInteractiveMode && testFn(currentTarget, location))\n        {\n            // The current hit-test target is the event's target only if it is interactive. Otherwise,\n            // the first interactive ancestor will be the event's target.\n            return isInteractiveTarget ? [currentTarget] : [];\n        }\n\n        return null;\n    }\n\n    private _isInteractive(int: EventMode): int is 'static' | 'dynamic'\n    {\n        return int === 'static' || int === 'dynamic';\n    }\n\n    private _interactivePrune(displayObject: DisplayObject): boolean\n    {\n        // If displayObject is a mask, invisible, or not renderable then it cannot be hit directly.\n        if (!displayObject || displayObject.isMask || !displayObject.visible || !displayObject.renderable)\n        {\n            return true;\n        }\n\n        // If this DisplayObject is none then it cannot be hit by anything.\n        if (displayObject.eventMode === 'none')\n        {\n            return true;\n        }\n\n        // If this DisplayObject is passive and it has no interactive children then it cannot be hit\n        if (displayObject.eventMode === 'passive' && !displayObject.interactiveChildren)\n        {\n            return true;\n        }\n\n        // If displayObject is a mask then it cannot be hit directly.\n        if (displayObject.isMask)\n        {\n            return true;\n        }\n\n        return false;\n    }\n\n    /**\n     * Checks whether the display object or any of its children cannot pass the hit test at all.\n     *\n     * {@link PIXI.EventBoundary}'s implementation uses the {@link PIXI.DisplayObject.hitArea hitArea}\n     * and {@link PIXI.DisplayObject._mask} for pruning.\n     * @param displayObject\n     * @param location\n     */\n    protected hitPruneFn(displayObject: DisplayObject, location: Point): boolean\n    {\n        if (displayObject.hitArea)\n        {\n            displayObject.worldTransform.applyInverse(location, tempLocalMapping);\n\n            if (!displayObject.hitArea.contains(tempLocalMapping.x, tempLocalMapping.y))\n            {\n                return true;\n            }\n        }\n\n        if (displayObject._mask)\n        {\n            const maskObject = ((displayObject._mask as any).isMaskData\n                ? (displayObject._mask as any).maskObject : displayObject._mask);\n\n            if (maskObject && !maskObject.containsPoint?.(location))\n            {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Checks whether the display object passes hit testing for the given location.\n     * @param displayObject\n     * @param location\n     * @returns - Whether `displayObject` passes hit testing for `location`.\n     */\n    protected hitTestFn(displayObject: DisplayObject, location: Point): boolean\n    {\n        // If the displayObject is passive then it cannot be hit directly.\n        if (displayObject.eventMode === 'passive')\n        {\n            return false;\n        }\n\n        // If the display object failed pruning with a hitArea, then it must pass it.\n        if (displayObject.hitArea)\n        {\n            return true;\n        }\n\n        if ((displayObject as any).containsPoint)\n        {\n            return (displayObject as any).containsPoint(location) as boolean;\n        }\n\n        // TODO: Should we hit test based on bounds?\n\n        return false;\n    }\n\n    /**\n     * Notify all the listeners to the event's `currentTarget`.\n     *\n     * If the `currentTarget` contains the property `on<type>`, then it is called here,\n     * simulating the behavior from version 6.x and prior.\n     * @param e - The event passed to the target.\n     * @param type\n     */\n    protected notifyTarget(e: FederatedEvent, type?: string): void\n    {\n        type = type ?? e.type;\n\n        // call the `on${type}` for the current target if it exists\n        const handlerKey = `on${type}` as keyof IFederatedDisplayObject;\n\n        (e.currentTarget[handlerKey] as FederatedEventHandler<FederatedEvent>)?.(e);\n\n        const key = e.eventPhase === e.CAPTURING_PHASE || e.eventPhase === e.AT_TARGET ? `${type}capture` : type;\n\n        this.notifyListeners(e, key);\n\n        if (e.eventPhase === e.AT_TARGET)\n        {\n            this.notifyListeners(e, type);\n        }\n    }\n\n    /**\n     * Maps the upstream `pointerdown` events to a downstream `pointerdown` event.\n     *\n     * `touchstart`, `rightdown`, `mousedown` events are also dispatched for specific pointer types.\n     * @param from\n     */\n    protected mapPointerDown(from: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedPointerEvent))\n        {\n            console.warn('EventBoundary cannot map a non-pointer event as a pointer event');\n\n            return;\n        }\n\n        const e = this.createPointerEvent(from);\n\n        this.dispatchEvent(e, 'pointerdown');\n\n        if (e.pointerType === 'touch')\n        {\n            this.dispatchEvent(e, 'touchstart');\n        }\n        else if (e.pointerType === 'mouse' || e.pointerType === 'pen')\n        {\n            const isRightButton = e.button === 2;\n\n            this.dispatchEvent(e, isRightButton ? 'rightdown' : 'mousedown');\n        }\n\n        const trackingData = this.trackingData(from.pointerId);\n\n        trackingData.pressTargetsByButton[from.button] = e.composedPath();\n\n        this.freeEvent(e);\n    }\n\n    /**\n     * Maps the upstream `pointermove` to downstream `pointerout`, `pointerover`, and `pointermove` events, in that order.\n     *\n     * The tracking data for the specific pointer has an updated `overTarget`. `mouseout`, `mouseover`,\n     * `mousemove`, and `touchmove` events are fired as well for specific pointer types.\n     * @param from - The upstream `pointermove` event.\n     */\n    protected mapPointerMove(from: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedPointerEvent))\n        {\n            console.warn('EventBoundary cannot map a non-pointer event as a pointer event');\n\n            return;\n        }\n\n        this._allInteractiveElements.length = 0;\n        this._hitElements.length = 0;\n        this._isPointerMoveEvent = true;\n        const e = this.createPointerEvent(from);\n\n        this._isPointerMoveEvent = false;\n        const isMouse = e.pointerType === 'mouse' || e.pointerType === 'pen';\n        const trackingData = this.trackingData(from.pointerId);\n        const outTarget = this.findMountedTarget(trackingData.overTargets);\n\n        // First pointerout/pointerleave\n        if (trackingData.overTargets?.length > 0 && outTarget !== e.target)\n        {\n            // pointerout always occurs on the overTarget when the pointer hovers over another element.\n            const outType = from.type === 'mousemove' ? 'mouseout' : 'pointerout';\n            const outEvent = this.createPointerEvent(from, outType, outTarget);\n\n            this.dispatchEvent(outEvent, 'pointerout');\n            if (isMouse) this.dispatchEvent(outEvent, 'mouseout');\n\n            // If the pointer exits overTarget and its descendants, then a pointerleave event is also fired. This event\n            // is dispatched to all ancestors that no longer capture the pointer.\n            if (!e.composedPath().includes(outTarget))\n            {\n                const leaveEvent = this.createPointerEvent(from, 'pointerleave', outTarget);\n\n                leaveEvent.eventPhase = leaveEvent.AT_TARGET;\n\n                while (leaveEvent.target && !e.composedPath().includes(leaveEvent.target))\n                {\n                    leaveEvent.currentTarget = leaveEvent.target;\n\n                    this.notifyTarget(leaveEvent);\n                    if (isMouse) this.notifyTarget(leaveEvent, 'mouseleave');\n\n                    leaveEvent.target = leaveEvent.target.parent;\n                }\n\n                this.freeEvent(leaveEvent);\n            }\n\n            this.freeEvent(outEvent);\n        }\n\n        // Then pointerover\n        if (outTarget !== e.target)\n        {\n            // pointerover always occurs on the new overTarget\n            const overType = from.type === 'mousemove' ? 'mouseover' : 'pointerover';\n            const overEvent = this.clonePointerEvent(e, overType);// clone faster\n\n            this.dispatchEvent(overEvent, 'pointerover');\n            if (isMouse) this.dispatchEvent(overEvent, 'mouseover');\n\n            // Probe whether the newly hovered DisplayObject is an ancestor of the original overTarget.\n            let overTargetAncestor = outTarget?.parent;\n\n            while (overTargetAncestor && overTargetAncestor !== this.rootTarget.parent)\n            {\n                if (overTargetAncestor === e.target) break;\n\n                overTargetAncestor = overTargetAncestor.parent;\n            }\n\n            // The pointer has entered a non-ancestor of the original overTarget. This means we need a pointerentered\n            // event.\n            const didPointerEnter = !overTargetAncestor || overTargetAncestor === this.rootTarget.parent;\n\n            if (didPointerEnter)\n            {\n                const enterEvent = this.clonePointerEvent(e, 'pointerenter');\n\n                enterEvent.eventPhase = enterEvent.AT_TARGET;\n\n                while (enterEvent.target\n                        && enterEvent.target !== outTarget\n                        && enterEvent.target !== this.rootTarget.parent)\n                {\n                    enterEvent.currentTarget = enterEvent.target;\n\n                    this.notifyTarget(enterEvent);\n                    if (isMouse) this.notifyTarget(enterEvent, 'mouseenter');\n\n                    enterEvent.target = enterEvent.target.parent;\n                }\n\n                this.freeEvent(enterEvent);\n            }\n\n            this.freeEvent(overEvent);\n        }\n\n        const allMethods: string[] = [];\n        const allowGlobalPointerEvents = this.enableGlobalMoveEvents ?? true;\n\n        /* eslint-disable @typescript-eslint/no-unused-expressions */\n        this.moveOnAll ? allMethods.push('pointermove') : this.dispatchEvent(e, 'pointermove');\n        allowGlobalPointerEvents && allMethods.push('globalpointermove');\n\n        // Then pointermove\n        if (e.pointerType === 'touch')\n        {\n            this.moveOnAll ? allMethods.splice(1, 0, 'touchmove') : this.dispatchEvent(e, 'touchmove');\n            allowGlobalPointerEvents && allMethods.push('globaltouchmove');\n        }\n\n        if (isMouse)\n        {\n            this.moveOnAll ? allMethods.splice(1, 0, 'mousemove') : this.dispatchEvent(e, 'mousemove');\n            allowGlobalPointerEvents && allMethods.push('globalmousemove');\n            this.cursor = e.target?.cursor;\n        }\n\n        if (allMethods.length > 0)\n        {\n            this.all(e, allMethods);\n        }\n        this._allInteractiveElements.length = 0;\n        this._hitElements.length = 0;\n        /* eslint-enable @typescript-eslint/no-unused-expressions */\n\n        trackingData.overTargets = e.composedPath();\n\n        this.freeEvent(e);\n    }\n\n    /**\n     * Maps the upstream `pointerover` to downstream `pointerover` and `pointerenter` events, in that order.\n     *\n     * The tracking data for the specific pointer gets a new `overTarget`.\n     * @param from - The upstream `pointerover` event.\n     */\n    protected mapPointerOver(from: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedPointerEvent))\n        {\n            console.warn('EventBoundary cannot map a non-pointer event as a pointer event');\n\n            return;\n        }\n\n        const trackingData = this.trackingData(from.pointerId);\n        const e = this.createPointerEvent(from);\n        const isMouse = e.pointerType === 'mouse' || e.pointerType === 'pen';\n\n        this.dispatchEvent(e, 'pointerover');\n        if (isMouse) this.dispatchEvent(e, 'mouseover');\n        if (e.pointerType === 'mouse') this.cursor = e.target?.cursor;\n\n        // pointerenter events must be fired since the pointer entered from upstream.\n        const enterEvent = this.clonePointerEvent(e, 'pointerenter');\n\n        enterEvent.eventPhase = enterEvent.AT_TARGET;\n\n        while (enterEvent.target && enterEvent.target !== this.rootTarget.parent)\n        {\n            enterEvent.currentTarget = enterEvent.target;\n\n            this.notifyTarget(enterEvent);\n            if (isMouse) this.notifyTarget(enterEvent, 'mouseenter');\n\n            enterEvent.target = enterEvent.target.parent;\n        }\n\n        trackingData.overTargets = e.composedPath();\n\n        this.freeEvent(e);\n        this.freeEvent(enterEvent);\n    }\n\n    /**\n     * Maps the upstream `pointerout` to downstream `pointerout`, `pointerleave` events, in that order.\n     *\n     * The tracking data for the specific pointer is cleared of a `overTarget`.\n     * @param from - The upstream `pointerout` event.\n     */\n    protected mapPointerOut(from: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedPointerEvent))\n        {\n            console.warn('EventBoundary cannot map a non-pointer event as a pointer event');\n\n            return;\n        }\n\n        const trackingData = this.trackingData(from.pointerId);\n\n        if (trackingData.overTargets)\n        {\n            const isMouse = from.pointerType === 'mouse' || from.pointerType === 'pen';\n            const outTarget = this.findMountedTarget(trackingData.overTargets);\n\n            // pointerout first\n            const outEvent = this.createPointerEvent(from, 'pointerout', outTarget);\n\n            this.dispatchEvent(outEvent);\n            if (isMouse) this.dispatchEvent(outEvent, 'mouseout');\n\n            // pointerleave(s) are also dispatched b/c the pointer must've left rootTarget and its descendants to\n            // get an upstream pointerout event (upstream events do not know rootTarget has descendants).\n            const leaveEvent = this.createPointerEvent(from, 'pointerleave', outTarget);\n\n            leaveEvent.eventPhase = leaveEvent.AT_TARGET;\n\n            while (leaveEvent.target && leaveEvent.target !== this.rootTarget.parent)\n            {\n                leaveEvent.currentTarget = leaveEvent.target;\n\n                this.notifyTarget(leaveEvent);\n                if (isMouse) this.notifyTarget(leaveEvent, 'mouseleave');\n\n                leaveEvent.target = leaveEvent.target.parent;\n            }\n\n            trackingData.overTargets = null;\n\n            this.freeEvent(outEvent);\n            this.freeEvent(leaveEvent);\n        }\n\n        this.cursor = null;\n    }\n\n    /**\n     * Maps the upstream `pointerup` event to downstream `pointerup`, `pointerupoutside`,\n     * and `click`/`rightclick`/`pointertap` events, in that order.\n     *\n     * The `pointerupoutside` event bubbles from the original `pointerdown` target to the most specific\n     * ancestor of the `pointerdown` and `pointerup` targets, which is also the `click` event's target. `touchend`,\n     * `rightup`, `mouseup`, `touchendoutside`, `rightupoutside`, `mouseupoutside`, and `tap` are fired as well for\n     * specific pointer types.\n     * @param from - The upstream `pointerup` event.\n     */\n    protected mapPointerUp(from: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedPointerEvent))\n        {\n            console.warn('EventBoundary cannot map a non-pointer event as a pointer event');\n\n            return;\n        }\n\n        const now = performance.now();\n        const e = this.createPointerEvent(from);\n\n        this.dispatchEvent(e, 'pointerup');\n\n        if (e.pointerType === 'touch')\n        {\n            this.dispatchEvent(e, 'touchend');\n        }\n        else if (e.pointerType === 'mouse' || e.pointerType === 'pen')\n        {\n            const isRightButton = e.button === 2;\n\n            this.dispatchEvent(e, isRightButton ? 'rightup' : 'mouseup');\n        }\n\n        const trackingData = this.trackingData(from.pointerId);\n        const pressTarget = this.findMountedTarget(trackingData.pressTargetsByButton[from.button]);\n\n        let clickTarget = pressTarget;\n\n        // pointerupoutside only bubbles. It only bubbles upto the parent that doesn't contain\n        // the pointerup location.\n        if (pressTarget && !e.composedPath().includes(pressTarget))\n        {\n            let currentTarget = pressTarget;\n\n            while (currentTarget && !e.composedPath().includes(currentTarget))\n            {\n                e.currentTarget = currentTarget;\n\n                this.notifyTarget(e, 'pointerupoutside');\n\n                if (e.pointerType === 'touch')\n                {\n                    this.notifyTarget(e, 'touchendoutside');\n                }\n                else if (e.pointerType === 'mouse' || e.pointerType === 'pen')\n                {\n                    const isRightButton = e.button === 2;\n\n                    this.notifyTarget(e, isRightButton ? 'rightupoutside' : 'mouseupoutside');\n                }\n\n                currentTarget = currentTarget.parent;\n            }\n\n            delete trackingData.pressTargetsByButton[from.button];\n\n            // currentTarget is the most specific ancestor holding both the pointerdown and pointerup\n            // targets. That is - it's our click target!\n            clickTarget = currentTarget;\n        }\n\n        // click!\n        if (clickTarget)\n        {\n            const clickEvent = this.clonePointerEvent(e, 'click');\n\n            clickEvent.target = clickTarget;\n            clickEvent.path = null;\n\n            if (!trackingData.clicksByButton[from.button])\n            {\n                trackingData.clicksByButton[from.button] = {\n                    clickCount: 0,\n                    target: clickEvent.target,\n                    timeStamp: now,\n                };\n            }\n\n            const clickHistory = trackingData.clicksByButton[from.button];\n\n            if (clickHistory.target === clickEvent.target\n                && now - clickHistory.timeStamp < 200)\n            {\n                ++clickHistory.clickCount;\n            }\n            else\n            {\n                clickHistory.clickCount = 1;\n            }\n\n            clickHistory.target = clickEvent.target;\n            clickHistory.timeStamp = now;\n\n            clickEvent.detail = clickHistory.clickCount;\n\n            if (clickEvent.pointerType === 'mouse')\n            {\n                const isRightButton = clickEvent.button === 2;\n\n                this.dispatchEvent(clickEvent, isRightButton ? 'rightclick' : 'click');\n            }\n            else if (clickEvent.pointerType === 'touch')\n            {\n                this.dispatchEvent(clickEvent, 'tap');\n            }\n\n            this.dispatchEvent(clickEvent, 'pointertap');\n\n            this.freeEvent(clickEvent);\n        }\n\n        this.freeEvent(e);\n    }\n\n    /**\n     * Maps the upstream `pointerupoutside` event to a downstream `pointerupoutside` event, bubbling from the original\n     * `pointerdown` target to `rootTarget`.\n     *\n     * (The most specific ancestor of the `pointerdown` event and the `pointerup` event must the\n     * `{@link PIXI.EventBoundary}'s root because the `pointerup` event occurred outside of the boundary.)\n     *\n     * `touchendoutside`, `mouseupoutside`, and `rightupoutside` events are fired as well for specific pointer\n     * types. The tracking data for the specific pointer is cleared of a `pressTarget`.\n     * @param from - The upstream `pointerupoutside` event.\n     */\n    protected mapPointerUpOutside(from: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedPointerEvent))\n        {\n            console.warn('EventBoundary cannot map a non-pointer event as a pointer event');\n\n            return;\n        }\n\n        const trackingData = this.trackingData(from.pointerId);\n        const pressTarget = this.findMountedTarget(trackingData.pressTargetsByButton[from.button]);\n        const e = this.createPointerEvent(from);\n\n        if (pressTarget)\n        {\n            let currentTarget = pressTarget;\n\n            while (currentTarget)\n            {\n                e.currentTarget = currentTarget;\n\n                this.notifyTarget(e, 'pointerupoutside');\n\n                if (e.pointerType === 'touch')\n                {\n                    this.notifyTarget(e, 'touchendoutside');\n                }\n                else if (e.pointerType === 'mouse' || e.pointerType === 'pen')\n                {\n                    this.notifyTarget(e, e.button === 2 ? 'rightupoutside' : 'mouseupoutside');\n                }\n\n                currentTarget = currentTarget.parent;\n            }\n\n            delete trackingData.pressTargetsByButton[from.button];\n        }\n\n        this.freeEvent(e);\n    }\n\n    /**\n     * Maps the upstream `wheel` event to a downstream `wheel` event.\n     * @param from - The upstream `wheel` event.\n     */\n    protected mapWheel(from: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedWheelEvent))\n        {\n            console.warn('EventBoundary cannot map a non-wheel event as a wheel event');\n\n            return;\n        }\n\n        const wheelEvent = this.createWheelEvent(from);\n\n        this.dispatchEvent(wheelEvent);\n        this.freeEvent(wheelEvent);\n    }\n\n    /**\n     * Finds the most specific event-target in the given propagation path that is still mounted in the scene graph.\n     *\n     * This is used to find the correct `pointerup` and `pointerout` target in the case that the original `pointerdown`\n     * or `pointerover` target was unmounted from the scene graph.\n     * @param propagationPath - The propagation path was valid in the past.\n     * @returns - The most specific event-target still mounted at the same location in the scene graph.\n     */\n    protected findMountedTarget(propagationPath: FederatedEventTarget[]): FederatedEventTarget\n    {\n        if (!propagationPath)\n        {\n            return null;\n        }\n\n        let currentTarget = propagationPath[0];\n\n        for (let i = 1; i < propagationPath.length; i++)\n        {\n            // Set currentTarget to the next target in the path only if it is still attached to the\n            // scene graph (i.e. parent still points to the expected ancestor).\n            if (propagationPath[i].parent === currentTarget)\n            {\n                currentTarget = propagationPath[i];\n            }\n            else\n            {\n                break;\n            }\n        }\n\n        return currentTarget;\n    }\n\n    /**\n     * Creates an event whose {@code originalEvent} is {@code from}, with an optional `type` and `target` override.\n     *\n     * The event is allocated using {@link PIXI.EventBoundary#allocateEvent this.allocateEvent}.\n     * @param from - The {@code originalEvent} for the returned event.\n     * @param [type=from.type] - The type of the returned event.\n     * @param target - The target of the returned event.\n     */\n    protected createPointerEvent(\n        from: FederatedPointerEvent,\n        type?: string,\n        target?: FederatedEventTarget\n    ): FederatedPointerEvent\n    {\n        const event = this.allocateEvent(FederatedPointerEvent);\n\n        this.copyPointerData(from, event);\n        this.copyMouseData(from, event);\n        this.copyData(from, event);\n\n        event.nativeEvent = from.nativeEvent;\n        event.originalEvent = from;\n        event.target = target\n            ?? this.hitTest(event.global.x, event.global.y) as FederatedEventTarget\n            ?? this._hitElements[0];\n\n        if (typeof type === 'string')\n        {\n            event.type = type;\n        }\n\n        return event;\n    }\n\n    /**\n     * Creates a wheel event whose {@code originalEvent} is {@code from}.\n     *\n     * The event is allocated using {@link PIXI.EventBoundary#allocateEvent this.allocateEvent}.\n     * @param from - The upstream wheel event.\n     */\n    protected createWheelEvent(from: FederatedWheelEvent): FederatedWheelEvent\n    {\n        const event = this.allocateEvent(FederatedWheelEvent);\n\n        this.copyWheelData(from, event);\n        this.copyMouseData(from, event);\n        this.copyData(from, event);\n\n        event.nativeEvent = from.nativeEvent;\n        event.originalEvent = from;\n        event.target = this.hitTest(event.global.x, event.global.y);\n\n        return event;\n    }\n\n    /**\n     * Clones the event {@code from}, with an optional {@code type} override.\n     *\n     * The event is allocated using {@link PIXI.EventBoundary#allocateEvent this.allocateEvent}.\n     * @param from - The event to clone.\n     * @param [type=from.type] - The type of the returned event.\n     */\n    protected clonePointerEvent(from: FederatedPointerEvent, type?: string): FederatedPointerEvent\n    {\n        const event = this.allocateEvent(FederatedPointerEvent);\n\n        event.nativeEvent = from.nativeEvent;\n        event.originalEvent = from.originalEvent;\n\n        this.copyPointerData(from, event);\n        this.copyMouseData(from, event);\n        this.copyData(from, event);\n\n        // copy propagation path for perf\n        event.target = from.target;\n        event.path = from.composedPath().slice();\n        event.type = type ?? event.type;\n\n        return event;\n    }\n\n    /**\n     * Copies wheel {@link PIXI.FederatedWheelEvent} data from {@code from} into {@code to}.\n     *\n     * The following properties are copied:\n     * + deltaMode\n     * + deltaX\n     * + deltaY\n     * + deltaZ\n     * @param from\n     * @param to\n     */\n    protected copyWheelData(from: FederatedWheelEvent, to: FederatedWheelEvent): void\n    {\n        to.deltaMode = from.deltaMode;\n        to.deltaX = from.deltaX;\n        to.deltaY = from.deltaY;\n        to.deltaZ = from.deltaZ;\n    }\n\n    /**\n     * Copies pointer {@link PIXI.FederatedPointerEvent} data from {@code from} into {@code to}.\n     *\n     * The following properties are copied:\n     * + pointerId\n     * + width\n     * + height\n     * + isPrimary\n     * + pointerType\n     * + pressure\n     * + tangentialPressure\n     * + tiltX\n     * + tiltY\n     * @param from\n     * @param to\n     */\n    protected copyPointerData(from: FederatedEvent, to: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedPointerEvent && to instanceof FederatedPointerEvent)) return;\n\n        to.pointerId = from.pointerId;\n        to.width = from.width;\n        to.height = from.height;\n        to.isPrimary = from.isPrimary;\n        to.pointerType = from.pointerType;\n        to.pressure = from.pressure;\n        to.tangentialPressure = from.tangentialPressure;\n        to.tiltX = from.tiltX;\n        to.tiltY = from.tiltY;\n        to.twist = from.twist;\n    }\n\n    /**\n     * Copies mouse {@link PIXI.FederatedMouseEvent} data from {@code from} to {@code to}.\n     *\n     * The following properties are copied:\n     * + altKey\n     * + button\n     * + buttons\n     * + clientX\n     * + clientY\n     * + metaKey\n     * + movementX\n     * + movementY\n     * + pageX\n     * + pageY\n     * + x\n     * + y\n     * + screen\n     * + shiftKey\n     * + global\n     * @param from\n     * @param to\n     */\n    protected copyMouseData(from: FederatedEvent, to: FederatedEvent): void\n    {\n        if (!(from instanceof FederatedMouseEvent && to instanceof FederatedMouseEvent)) return;\n\n        to.altKey = from.altKey;\n        to.button = from.button;\n        to.buttons = from.buttons;\n        to.client.copyFrom(from.client);\n        to.ctrlKey = from.ctrlKey;\n        to.metaKey = from.metaKey;\n        to.movement.copyFrom(from.movement);\n        to.screen.copyFrom(from.screen);\n        to.shiftKey = from.shiftKey;\n        to.global.copyFrom(from.global);\n    }\n\n    /**\n     * Copies base {@link PIXI.FederatedEvent} data from {@code from} into {@code to}.\n     *\n     * The following properties are copied:\n     * + isTrusted\n     * + srcElement\n     * + timeStamp\n     * + type\n     * @param from - The event to copy data from.\n     * @param to - The event to copy data into.\n     */\n    protected copyData(from: FederatedEvent, to: FederatedEvent): void\n    {\n        to.isTrusted = from.isTrusted;\n        to.srcElement = from.srcElement;\n        to.timeStamp = performance.now();\n        to.type = from.type;\n        to.detail = from.detail;\n        to.view = from.view;\n        to.which = from.which;\n        to.layer.copyFrom(from.layer);\n        to.page.copyFrom(from.page);\n    }\n\n    /**\n     * @param id - The pointer ID.\n     * @returns The tracking data stored for the given pointer. If no data exists, a blank\n     *  state will be created.\n     */\n    protected trackingData(id: number): TrackingData\n    {\n        if (!this.mappingState.trackingData[id])\n        {\n            this.mappingState.trackingData[id] = {\n                pressTargetsByButton: {},\n                clicksByButton: {},\n                overTarget: null\n            };\n        }\n\n        return this.mappingState.trackingData[id];\n    }\n\n    /**\n     * Allocate a specific type of event from {@link PIXI.EventBoundary#eventPool this.eventPool}.\n     *\n     * This allocation is constructor-agnostic, as long as it only takes one argument - this event\n     * boundary.\n     * @param constructor - The event's constructor.\n     */\n    protected allocateEvent<T extends FederatedEvent>(\n        constructor: { new(boundary: EventBoundary): T }\n    ): T\n    {\n        if (!this.eventPool.has(constructor as any))\n        {\n            this.eventPool.set(constructor as any, []);\n        }\n\n        const event = this.eventPool.get(constructor as any).pop() as T\n            || new constructor(this);\n\n        event.eventPhase = event.NONE;\n        event.currentTarget = null;\n        event.path = null;\n        event.target = null;\n\n        return event;\n    }\n\n    /**\n     * Frees the event and puts it back into the event pool.\n     *\n     * It is illegal to reuse the event until it is allocated again, using `this.allocateEvent`.\n     *\n     * It is also advised that events not allocated from {@link PIXI.EventBoundary#allocateEvent this.allocateEvent}\n     * not be freed. This is because of the possibility that the same event is freed twice, which can cause\n     * it to be allocated twice & result in overwriting.\n     * @param event - The event to be freed.\n     * @throws Error if the event is managed by another event boundary.\n     */\n    protected freeEvent<T extends FederatedEvent>(event: T): void\n    {\n        if (event.manager !== this) throw new Error('It is illegal to free an event not managed by this EventBoundary!');\n\n        const constructor = event.constructor;\n\n        if (!this.eventPool.has(constructor as any))\n        {\n            this.eventPool.set(constructor as any, []);\n        }\n\n        this.eventPool.get(constructor as any).push(event);\n    }\n\n    /**\n     * Similar to {@link PIXI.EventEmitter.emit}, except it stops if the `propagationImmediatelyStopped` flag\n     * is set on the event.\n     * @param e - The event to call each listener with.\n     * @param type - The event key.\n     */\n    private notifyListeners(e: FederatedEvent, type: string): void\n    {\n        const listeners = ((e.currentTarget as any)._events as EmitterListeners)[type];\n\n        if (!listeners) return;\n        if (!e.currentTarget.isInteractive()) return;\n\n        if ('fn' in listeners)\n        {\n            if (listeners.once) e.currentTarget.removeListener(type, listeners.fn, undefined, true);\n            listeners.fn.call(listeners.context, e);\n        }\n        else\n        {\n            for (\n                let i = 0, j = listeners.length;\n                i < j && !e.propagationImmediatelyStopped;\n                i++)\n            {\n                if (listeners[i].once) e.currentTarget.removeListener(type, listeners[i].fn, undefined, true);\n                listeners[i].fn.call(listeners[i].context, e);\n            }\n        }\n    }\n}\n"], "mappings": ";;;;;AAgBA,MAAMA,iBAAA,GAAoB;EAEpBC,eAAA,GAAkB,IAAIC,KAAA;EACtBC,gBAAA,GAAmB,IAAID,KAAA;AA0DtB,MAAME,aAAA,CACb;EAAA;AAAA;AAAA;EAsEIC,YAAYC,UAAA,EACZ;IAtDO,KAAAC,QAAA,GAA+B,IAAIC,KAAA,CAAMC,YAAA,CAAa,GAW7D,KAAOC,SAAA,GAAY,IAGnB,KAAOC,sBAAA,GAAyB,IAkBhC,KAAUC,YAAA,GAAoC;MAC1CC,YAAA,EAAc,CAAC;IAAA,GAQT,KAAAC,SAAA,sBAA8DC,GAAA,IAGxE,KAAQC,uBAAA,GAAkD,IAE1D,KAAQC,YAAA,GAAuC,IAE/C,KAAQC,mBAAA,GAAsB,IAOrB,KAAAZ,UAAA,GAAaA,UAAA,EAElB,KAAKa,UAAA,GAAa,KAAKA,UAAA,CAAWC,IAAA,CAAK,IAAI,GAC3C,KAAKC,SAAA,GAAY,KAAKA,SAAA,CAAUD,IAAA,CAAK,IAAI,GACzC,KAAKE,cAAA,GAAiB,KAAKA,cAAA,CAAeF,IAAA,CAAK,IAAI,GACnD,KAAKG,cAAA,GAAiB,KAAKA,cAAA,CAAeH,IAAA,CAAK,IAAI,GACnD,KAAKI,aAAA,GAAgB,KAAKA,aAAA,CAAcJ,IAAA,CAAK,IAAI,GACjD,KAAKK,cAAA,GAAiB,KAAKA,cAAA,CAAeL,IAAA,CAAK,IAAI,GACnD,KAAKM,YAAA,GAAe,KAAKA,YAAA,CAAaN,IAAA,CAAK,IAAI,GAC/C,KAAKO,mBAAA,GAAsB,KAAKA,mBAAA,CAAoBP,IAAA,CAAK,IAAI,GAC7D,KAAKQ,QAAA,GAAW,KAAKA,QAAA,CAASR,IAAA,CAAK,IAAI,GAEvC,KAAKS,YAAA,GAAe,IACpB,KAAKC,eAAA,CAAgB,eAAe,KAAKR,cAAc,GACvD,KAAKQ,eAAA,CAAgB,eAAe,KAAKP,cAAc,GACvD,KAAKO,eAAA,CAAgB,cAAc,KAAKN,aAAa,GACrD,KAAKM,eAAA,CAAgB,gBAAgB,KAAKN,aAAa,GACvD,KAAKM,eAAA,CAAgB,eAAe,KAAKL,cAAc,GACvD,KAAKK,eAAA,CAAgB,aAAa,KAAKJ,YAAY,GACnD,KAAKI,eAAA,CAAgB,oBAAoB,KAAKH,mBAAmB,GACjE,KAAKG,eAAA,CAAgB,SAAS,KAAKF,QAAQ;EAC/C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAcOE,gBAAgBC,IAAA,EAAcC,EAAA,EACrC;IACS,KAAKH,YAAA,CAAaE,IAAI,MAEvB,KAAKF,YAAA,CAAaE,IAAI,IAAI,KAG9B,KAAKF,YAAA,CAAaE,IAAI,EAAEE,IAAA,CAAK;MACzBD,EAAA;MACAE,QAAA,EAAU;IACb,IACD,KAAKL,YAAA,CAAaE,IAAI,EAAEI,IAAA,CAAK,CAACC,CAAA,EAAGC,CAAA,KAAMD,CAAA,CAAEF,QAAA,GAAWG,CAAA,CAAEH,QAAQ;EAClE;EAAA;AAAA;AAAA;AAAA;AAAA;EAOOI,cAAcC,CAAA,EAAmBR,IAAA,EACxC;IACIQ,CAAA,CAAEC,kBAAA,GAAqB,IACvBD,CAAA,CAAEE,6BAAA,GAAgC,IAElC,KAAKC,SAAA,CAAUH,CAAA,EAAGR,IAAI,GACtB,KAAKxB,QAAA,CAASoC,IAAA,CAAKZ,IAAA,IAAQQ,CAAA,CAAER,IAAA,EAAMQ,CAAC;EACxC;EAAA;AAAA;AAAA;AAAA;EAMOK,SAASL,CAAA,EAChB;IACI,IAAI,CAAC,KAAKjC,UAAA,EAEN;IAGJ,MAAMuC,OAAA,GAAU,KAAKhB,YAAA,CAAaU,CAAA,CAAER,IAAI;IAEpC,IAAAc,OAAA,EAEA,SAASC,CAAA,GAAI,GAAGC,CAAA,GAAIF,OAAA,CAAQG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAE/BD,OAAA,CAAAC,CAAC,EAAEd,EAAA,CAAGO,CAAC,OAKnBU,OAAA,CAAQC,IAAA,CAAK,kDAAkDX,CAAA,CAAER,IAAI,EAAE;EAE/E;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASOoB,QACHC,CAAA,EACAC,CAAA,EAEJ;IACIC,YAAA,CAAaC,WAAA,GAAc;IAGrB,MAAAvB,EAAA,GADU,KAAKd,mBAAA,IAAuB,KAAKP,sBAAA,GAC5B,yBAAyB;MACxC6C,YAAA,GAAe,KAAKxB,EAAE,EACxB,KAAK1B,UAAA,EACL,KAAKA,UAAA,CAAWmD,SAAA,EAChBxD,eAAA,CAAgByD,GAAA,CAAIN,CAAA,EAAGC,CAAC,GACxB,KAAKhC,SAAA,EACL,KAAKF,UAAA;IAGF,OAAAqC,YAAA,IAAgBA,YAAA,CAAa,CAAC;EACzC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQOd,UAAUH,CAAA,EAAmBR,IAAA,EACpC;IACI,IAAI,CAACQ,CAAA,CAAEoB,MAAA,EAGH;IAGE,MAAAC,YAAA,GAAerB,CAAA,CAAEqB,YAAA;IAGvBrB,CAAA,CAAEsB,UAAA,GAAatB,CAAA,CAAEuB,eAAA;IAEjB,SAAShB,CAAA,GAAI,GAAGC,CAAA,GAAIa,YAAA,CAAaZ,MAAA,GAAS,GAAGF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAMhD,IAJAP,CAAA,CAAEwB,aAAA,GAAgBH,YAAA,CAAad,CAAC,GAEhC,KAAKkB,YAAA,CAAazB,CAAA,EAAGR,IAAI,GAErBQ,CAAA,CAAEC,kBAAA,IAAsBD,CAAA,CAAEE,6BAAA,EAA+B;IASjE,IALAF,CAAA,CAAEsB,UAAA,GAAatB,CAAA,CAAE0B,SAAA,EACjB1B,CAAA,CAAEwB,aAAA,GAAgBxB,CAAA,CAAEoB,MAAA,EAEpB,KAAKK,YAAA,CAAazB,CAAA,EAAGR,IAAI,GAErB,EAAEQ,CAAA,CAAAC,kBAAA,IAAsBD,CAAA,CAAEE,6BAAA,GAG9B;MAAAF,CAAA,CAAEsB,UAAA,GAAatB,CAAA,CAAE2B,cAAA;MAEjB,SAASpB,CAAA,GAAIc,YAAA,CAAaZ,MAAA,GAAS,GAAGF,CAAA,IAAK,GAAGA,CAAA,IAM1C,IAJAP,CAAA,CAAEwB,aAAA,GAAgBH,YAAA,CAAad,CAAC,GAEhC,KAAKkB,YAAA,CAAazB,CAAA,EAAGR,IAAI,GAErBQ,CAAA,CAAEC,kBAAA,IAAsBD,CAAA,CAAEE,6BAAA,EAA+B;IAAA;EAErE;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUO0B,IAAI5B,CAAA,EAAmBR,IAAA,EAA0BqC,OAAA,GAAU,KAAKpD,uBAAA,EACvE;IACI,IAAIoD,OAAA,CAAQpB,MAAA,KAAW,GAAG;IAE1BT,CAAA,CAAEsB,UAAA,GAAatB,CAAA,CAAE2B,cAAA;IAEjB,MAAMG,MAAA,GAASC,KAAA,CAAMC,OAAA,CAAQxC,IAAI,IAAIA,IAAA,GAAO,CAACA,IAAI;IAIjD,SAASe,CAAA,GAAIsB,OAAA,CAAQpB,MAAA,GAAS,GAAGF,CAAA,IAAK,GAAGA,CAAA,IAE9BuB,MAAA,CAAAG,OAAA,CAASC,KAAA,IAChB;MACIlC,CAAA,CAAEwB,aAAA,GAAgBK,OAAA,CAAQtB,CAAC,GAC3B,KAAKkB,YAAA,CAAazB,CAAA,EAAGkC,KAAK;IAAA,CAC7B;EAET;EAAA;AAAA;AAAA;AAAA;AAAA;EAOOC,gBAAgBf,MAAA,EACvB;IACU,MAAAe,eAAA,GAAkB,CAACf,MAAM;IAE/B,SAASb,CAAA,GAAI,GAAGA,CAAA,GAAI9C,iBAAA,IAAqB2D,MAAA,KAAW,KAAKrD,UAAA,EAAYwC,CAAA,IACrE;MACI,IAAI,CAACa,MAAA,CAAOgB,MAAA,EAEF,UAAIC,KAAA,CAAM,qDAAqD;MAGzEF,eAAA,CAAgBzC,IAAA,CAAK0B,MAAA,CAAOgB,MAAM,GAElChB,MAAA,GAASA,MAAA,CAAOgB,MAAA;IACpB;IAEA,OAAAD,eAAA,CAAgBG,OAAA,CAET,GAAAH,eAAA;EACX;EAEUI,qBACNf,aAAA,EACAN,SAAA,EACAsB,QAAA,EACAC,MAAA,EACAC,OAAA,EACAC,MAAA,GAAS,IAEb;IACI,IAAIC,YAAA,GAAe;IAGf,SAAKC,iBAAA,CAAkBrB,aAAa,GAAU;IAE9C,KAAAA,aAAA,CAAcN,SAAA,KAAc,aAAaA,SAAA,KAAc,eAEvDH,YAAA,CAAaC,WAAA,GAAc,KAG3BQ,aAAA,CAAcsB,mBAAA,IAAuBtB,aAAA,CAAcuB,QAAA,EACvD;MACI,MAAMA,QAAA,GAAWvB,aAAA,CAAcuB,QAAA;MAE/B,SAASxC,CAAA,GAAIwC,QAAA,CAAStC,MAAA,GAAS,GAAGF,CAAA,IAAK,GAAGA,CAAA,IAC1C;QACI,MAAMyC,KAAA,GAAQD,QAAA,CAASxC,CAAC;UAElB0C,SAAA,GAAY,KAAKV,oBAAA,CACnBS,KAAA,EACA,KAAKE,cAAA,CAAehC,SAAS,IAAIA,SAAA,GAAY8B,KAAA,CAAM9B,SAAA,EACnDsB,QAAA,EACAC,MAAA,EACAC,OAAA,EACAC,MAAA,IAAUD,OAAA,CAAQlB,aAAA,EAAegB,QAAQ;QAG7C,IAAIS,SAAA,EACJ;UAGQ,IAAAA,SAAA,CAAUxC,MAAA,GAAS,KAAK,CAACwC,SAAA,CAAUA,SAAA,CAAUxC,MAAA,GAAS,CAAC,EAAE2B,MAAA,EAEzD;UAME,MAAAe,aAAA,GAAgB3B,aAAA,CAAc2B,aAAA;UAEhC,CAAAF,SAAA,CAAUxC,MAAA,GAAS,KAAK0C,aAAA,MAEpBA,aAAA,IAAe,KAAK1E,uBAAA,CAAwBiB,IAAA,CAAK8B,aAAa,GAClEyB,SAAA,CAAUvD,IAAA,CAAK8B,aAAa,IAI5B,KAAK9C,YAAA,CAAa+B,MAAA,KAAW,MAAG,KAAK/B,YAAA,GAAeuE,SAAA,GAExDL,YAAA,GAAe;QACnB;MACJ;IACJ;IAEA,MAAMQ,iBAAA,GAAoB,KAAKF,cAAA,CAAehC,SAAS;MACjDmC,mBAAA,GAAsB7B,aAAA,CAAc2B,aAAA;IAM1C,OAJIC,iBAAA,IAAqBC,mBAAA,IAAqB,KAAK5E,uBAAA,CAAwBiB,IAAA,CAAK8B,aAAa,GAIzFmB,MAAA,IAAU,KAAKjE,YAAA,CAAa+B,MAAA,GAAS,IAAU,OAE/CmC,YAAA,GAAqB,KAAKlE,YAAA,GAG1B0E,iBAAA,IAAsB,CAACV,OAAA,CAAQlB,aAAA,EAAegB,QAAQ,KAAKC,MAAA,CAAOjB,aAAA,EAAegB,QAAQ,IAIlFa,mBAAA,GAAsB,CAAC7B,aAAa,IAAI,KAG5C;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAgBU8B,iBACN9B,aAAA,EACAN,SAAA,EACAsB,QAAA,EACAC,MAAA,EACAC,OAAA,EAEJ;IAEI,IAAI,KAAKG,iBAAA,CAAkBrB,aAAa,KAAKkB,OAAA,CAAQlB,aAAA,EAAegB,QAAQ,GAEjE;IAGP,KAAAhB,aAAA,CAAcN,SAAA,KAAc,aAAaA,SAAA,KAAc,eAEvDH,YAAA,CAAaC,WAAA,GAAc,KAI3BQ,aAAA,CAAcsB,mBAAA,IAAuBtB,aAAA,CAAcuB,QAAA,EACvD;MACI,MAAMA,QAAA,GAAWvB,aAAA,CAAcuB,QAAA;MAE/B,SAASxC,CAAA,GAAIwC,QAAA,CAAStC,MAAA,GAAS,GAAGF,CAAA,IAAK,GAAGA,CAAA,IAC1C;QACI,MAAMyC,KAAA,GAAQD,QAAA,CAASxC,CAAC;UAElB0C,SAAA,GAAY,KAAKK,gBAAA,CACnBN,KAAA,EACA,KAAKE,cAAA,CAAehC,SAAS,IAAIA,SAAA,GAAY8B,KAAA,CAAM9B,SAAA,EACnDsB,QAAA,EACAC,MAAA,EACAC,OAAA;QAGJ,IAAIO,SAAA,EACJ;UAGQ,IAAAA,SAAA,CAAUxC,MAAA,GAAS,KAAK,CAACwC,SAAA,CAAUA,SAAA,CAAUxC,MAAA,GAAS,CAAC,EAAE2B,MAAA,EAEzD;UAME,MAAAe,aAAA,GAAgB3B,aAAA,CAAc2B,aAAA;UAEpC,QAAIF,SAAA,CAAUxC,MAAA,GAAS,KAAK0C,aAAA,KAAeF,SAAA,CAAUvD,IAAA,CAAK8B,aAAa,GAEhEyB,SAAA;QACX;MACJ;IACJ;IAEA,MAAMG,iBAAA,GAAoB,KAAKF,cAAA,CAAehC,SAAS;MACjDmC,mBAAA,GAAsB7B,aAAA,CAAc2B,aAAA;IAGtC,OAAAC,iBAAA,IAAqBX,MAAA,CAAOjB,aAAA,EAAegB,QAAQ,IAI5Ca,mBAAA,GAAsB,CAAC7B,aAAa,IAAI,EAG5C;EACX;EAEQ0B,eAAeK,GAAA,EACvB;IACW,OAAAA,GAAA,KAAQ,YAAYA,GAAA,KAAQ;EACvC;EAEQV,kBAAkBW,aAAA,EAC1B;IAoBI,OAlBI,IAACA,aAAA,IAAiBA,aAAA,CAAcC,MAAA,IAAU,CAACD,aAAA,CAAcE,OAAA,IAAW,CAACF,aAAA,CAAcG,UAAA,IAMnFH,aAAA,CAActC,SAAA,KAAc,UAM5BsC,aAAA,CAActC,SAAA,KAAc,aAAa,CAACsC,aAAA,CAAcV,mBAAA,IAMxDU,aAAA,CAAcC,MAAA;EAMtB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUU7E,WAAW4E,aAAA,EAA8BhB,QAAA,EACnD;IACI,IAAIgB,aAAA,CAAcI,OAAA,KAEdJ,aAAA,CAAcK,cAAA,CAAeC,YAAA,CAAatB,QAAA,EAAU5E,gBAAgB,GAEhE,CAAC4F,aAAA,CAAcI,OAAA,CAAQG,QAAA,CAASnG,gBAAA,CAAiBiD,CAAA,EAAGjD,gBAAA,CAAiBkD,CAAC,IAE/D;IAIf,IAAI0C,aAAA,CAAcQ,KAAA,EAClB;MACI,MAAMC,UAAA,GAAeT,aAAA,CAAcQ,KAAA,CAAcE,UAAA,GAC1CV,aAAA,CAAcQ,KAAA,CAAcC,UAAA,GAAaT,aAAA,CAAcQ,KAAA;MAE9D,IAAIC,UAAA,IAAc,CAACA,UAAA,CAAWE,aAAA,GAAgB3B,QAAQ,GAE3C;IAEf;IAEO;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQU1D,UAAU0E,aAAA,EAA8BhB,QAAA,EAClD;IAEI,OAAIgB,aAAA,CAActC,SAAA,KAAc,YAErB,KAIPsC,aAAA,CAAcI,OAAA,GAEP,KAGNJ,aAAA,CAAsBW,aAAA,GAEfX,aAAA,CAAsBW,aAAA,CAAc3B,QAAQ,IAKjD;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUUf,aAAazB,CAAA,EAAmBR,IAAA,EAC1C;IACIA,IAAA,GAAOA,IAAA,IAAQQ,CAAA,CAAER,IAAA;IAGX,MAAA4E,UAAA,GAAa,KAAK5E,IAAI;IAE3BQ,CAAA,CAAEwB,aAAA,CAAc4C,UAAU,IAA8CpE,CAAC;IAEpE,MAAAqE,GAAA,GAAMrE,CAAA,CAAEsB,UAAA,KAAetB,CAAA,CAAEuB,eAAA,IAAmBvB,CAAA,CAAEsB,UAAA,KAAetB,CAAA,CAAE0B,SAAA,GAAY,GAAGlC,IAAI,YAAYA,IAAA;IAE/F,KAAA8E,eAAA,CAAgBtE,CAAA,EAAGqE,GAAG,GAEvBrE,CAAA,CAAEsB,UAAA,KAAetB,CAAA,CAAE0B,SAAA,IAEnB,KAAK4C,eAAA,CAAgBtE,CAAA,EAAGR,IAAI;EAEpC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQUT,eAAewF,IAAA,EACzB;IACQ,MAAEA,IAAA,YAAgBC,qBAAA,GACtB;MACI9D,OAAA,CAAQC,IAAA,CAAK,iEAAiE;MAE9E;IACJ;IAEM,MAAAX,CAAA,GAAI,KAAKyE,kBAAA,CAAmBF,IAAI;IAItC,IAFA,KAAKxE,aAAA,CAAcC,CAAA,EAAG,aAAa,GAE/BA,CAAA,CAAE0E,WAAA,KAAgB,SAEb,KAAA3E,aAAA,CAAcC,CAAA,EAAG,YAAY,WAE7BA,CAAA,CAAE0E,WAAA,KAAgB,WAAW1E,CAAA,CAAE0E,WAAA,KAAgB,OACxD;MACU,MAAAC,aAAA,GAAgB3E,CAAA,CAAE4E,MAAA,KAAW;MAEnC,KAAK7E,aAAA,CAAcC,CAAA,EAAG2E,aAAA,GAAgB,cAAc,WAAW;IACnE;IAEA,MAAMrG,YAAA,GAAe,KAAKA,YAAA,CAAaiG,IAAA,CAAKM,SAAS;IAExCvG,YAAA,CAAAwG,oBAAA,CAAqBP,IAAA,CAAKK,MAAM,IAAI5E,CAAA,CAAEqB,YAAA,CAAa,GAEhE,KAAK0D,SAAA,CAAU/E,CAAC;EACpB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASUhB,eAAeuF,IAAA,EACzB;IACQ,MAAEA,IAAA,YAAgBC,qBAAA,GACtB;MACI9D,OAAA,CAAQC,IAAA,CAAK,iEAAiE;MAE9E;IACJ;IAEK,KAAAlC,uBAAA,CAAwBgC,MAAA,GAAS,GACtC,KAAK/B,YAAA,CAAa+B,MAAA,GAAS,GAC3B,KAAK9B,mBAAA,GAAsB;IACrB,MAAAqB,CAAA,GAAI,KAAKyE,kBAAA,CAAmBF,IAAI;IAEtC,KAAK5F,mBAAA,GAAsB;IAC3B,MAAMqG,OAAA,GAAUhF,CAAA,CAAE0E,WAAA,KAAgB,WAAW1E,CAAA,CAAE0E,WAAA,KAAgB;MACzDpG,YAAA,GAAe,KAAKA,YAAA,CAAaiG,IAAA,CAAKM,SAAS;MAC/CI,SAAA,GAAY,KAAKC,iBAAA,CAAkB5G,YAAA,CAAa6G,WAAW;IAGjE,IAAI7G,YAAA,CAAa6G,WAAA,EAAa1E,MAAA,GAAS,KAAKwE,SAAA,KAAcjF,CAAA,CAAEoB,MAAA,EAC5D;MAEU,MAAAgE,OAAA,GAAUb,IAAA,CAAK/E,IAAA,KAAS,cAAc,aAAa;QACnD6F,QAAA,GAAW,KAAKZ,kBAAA,CAAmBF,IAAA,EAAMa,OAAA,EAASH,SAAS;MAOjE,IALA,KAAKlF,aAAA,CAAcsF,QAAA,EAAU,YAAY,GACrCL,OAAA,IAAS,KAAKjF,aAAA,CAAcsF,QAAA,EAAU,UAAU,GAIhD,CAACrF,CAAA,CAAEqB,YAAA,GAAeiE,QAAA,CAASL,SAAS,GACxC;QACI,MAAMM,UAAA,GAAa,KAAKd,kBAAA,CAAmBF,IAAA,EAAM,gBAAgBU,SAAS;QAI1E,KAFAM,UAAA,CAAWjE,UAAA,GAAaiE,UAAA,CAAW7D,SAAA,EAE5B6D,UAAA,CAAWnE,MAAA,IAAU,CAACpB,CAAA,CAAEqB,YAAA,CAAa,EAAEiE,QAAA,CAASC,UAAA,CAAWnE,MAAM,IAEpEmE,UAAA,CAAW/D,aAAA,GAAgB+D,UAAA,CAAWnE,MAAA,EAEtC,KAAKK,YAAA,CAAa8D,UAAU,GACxBP,OAAA,IAAS,KAAKvD,YAAA,CAAa8D,UAAA,EAAY,YAAY,GAEvDA,UAAA,CAAWnE,MAAA,GAASmE,UAAA,CAAWnE,MAAA,CAAOgB,MAAA;QAG1C,KAAK2C,SAAA,CAAUQ,UAAU;MAC7B;MAEA,KAAKR,SAAA,CAAUM,QAAQ;IAC3B;IAGI,IAAAJ,SAAA,KAAcjF,CAAA,CAAEoB,MAAA,EACpB;MAEU,MAAAoE,QAAA,GAAWjB,IAAA,CAAK/E,IAAA,KAAS,cAAc,cAAc;QACrDiG,SAAA,GAAY,KAAKC,iBAAA,CAAkB1F,CAAA,EAAGwF,QAAQ;MAE/C,KAAAzF,aAAA,CAAc0F,SAAA,EAAW,aAAa,GACvCT,OAAA,IAAS,KAAKjF,aAAA,CAAc0F,SAAA,EAAW,WAAW;MAGtD,IAAIE,kBAAA,GAAqBV,SAAA,EAAW7C,MAAA;MAEpC,OAAOuD,kBAAA,IAAsBA,kBAAA,KAAuB,KAAK5H,UAAA,CAAWqE,MAAA,IAE5DuD,kBAAA,KAAuB3F,CAAA,CAAEoB,MAAA,GAE7BuE,kBAAA,GAAqBA,kBAAA,CAAmBvD,MAAA;MAO5C,IAFwB,CAACuD,kBAAA,IAAsBA,kBAAA,KAAuB,KAAK5H,UAAA,CAAWqE,MAAA,EAGtF;QACI,MAAMwD,UAAA,GAAa,KAAKF,iBAAA,CAAkB1F,CAAA,EAAG,cAAc;QAI3D,KAFA4F,UAAA,CAAWtE,UAAA,GAAasE,UAAA,CAAWlE,SAAA,EAE5BkE,UAAA,CAAWxE,MAAA,IACPwE,UAAA,CAAWxE,MAAA,KAAW6D,SAAA,IACtBW,UAAA,CAAWxE,MAAA,KAAW,KAAKrD,UAAA,CAAWqE,MAAA,GAE7CwD,UAAA,CAAWpE,aAAA,GAAgBoE,UAAA,CAAWxE,MAAA,EAEtC,KAAKK,YAAA,CAAamE,UAAU,GACxBZ,OAAA,IAAS,KAAKvD,YAAA,CAAamE,UAAA,EAAY,YAAY,GAEvDA,UAAA,CAAWxE,MAAA,GAASwE,UAAA,CAAWxE,MAAA,CAAOgB,MAAA;QAG1C,KAAK2C,SAAA,CAAUa,UAAU;MAC7B;MAEA,KAAKb,SAAA,CAAUU,SAAS;IAC5B;IAEA,MAAMI,UAAA,GAAuB;MACvBC,wBAAA,GAA2B,KAAK1H,sBAAA,IAA0B;IAGhE,KAAKD,SAAA,GAAY0H,UAAA,CAAWnG,IAAA,CAAK,aAAa,IAAI,KAAKK,aAAA,CAAcC,CAAA,EAAG,aAAa,GACrF8F,wBAAA,IAA4BD,UAAA,CAAWnG,IAAA,CAAK,mBAAmB,GAG3DM,CAAA,CAAE0E,WAAA,KAAgB,YAElB,KAAKvG,SAAA,GAAY0H,UAAA,CAAWE,MAAA,CAAO,GAAG,GAAG,WAAW,IAAI,KAAKhG,aAAA,CAAcC,CAAA,EAAG,WAAW,GACzF8F,wBAAA,IAA4BD,UAAA,CAAWnG,IAAA,CAAK,iBAAiB,IAG7DsF,OAAA,KAEA,KAAK7G,SAAA,GAAY0H,UAAA,CAAWE,MAAA,CAAO,GAAG,GAAG,WAAW,IAAI,KAAKhG,aAAA,CAAcC,CAAA,EAAG,WAAW,GACzF8F,wBAAA,IAA4BD,UAAA,CAAWnG,IAAA,CAAK,iBAAiB,GAC7D,KAAKsG,MAAA,GAAShG,CAAA,CAAEoB,MAAA,EAAQ4E,MAAA,GAGxBH,UAAA,CAAWpF,MAAA,GAAS,KAEpB,KAAKmB,GAAA,CAAI5B,CAAA,EAAG6F,UAAU,GAE1B,KAAKpH,uBAAA,CAAwBgC,MAAA,GAAS,GACtC,KAAK/B,YAAA,CAAa+B,MAAA,GAAS,GAG3BnC,YAAA,CAAa6G,WAAA,GAAcnF,CAAA,CAAEqB,YAAA,IAE7B,KAAK0D,SAAA,CAAU/E,CAAC;EACpB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQUd,eAAeqF,IAAA,EACzB;IACQ,MAAEA,IAAA,YAAgBC,qBAAA,GACtB;MACI9D,OAAA,CAAQC,IAAA,CAAK,iEAAiE;MAE9E;IACJ;IAEA,MAAMrC,YAAA,GAAe,KAAKA,YAAA,CAAaiG,IAAA,CAAKM,SAAS;MAC/C7E,CAAA,GAAI,KAAKyE,kBAAA,CAAmBF,IAAI;MAChCS,OAAA,GAAUhF,CAAA,CAAE0E,WAAA,KAAgB,WAAW1E,CAAA,CAAE0E,WAAA,KAAgB;IAE/D,KAAK3E,aAAA,CAAcC,CAAA,EAAG,aAAa,GAC/BgF,OAAA,IAAS,KAAKjF,aAAA,CAAcC,CAAA,EAAG,WAAW,GAC1CA,CAAA,CAAE0E,WAAA,KAAgB,YAAS,KAAKsB,MAAA,GAAShG,CAAA,CAAEoB,MAAA,EAAQ4E,MAAA;IAGvD,MAAMJ,UAAA,GAAa,KAAKF,iBAAA,CAAkB1F,CAAA,EAAG,cAAc;IAE3D,KAAA4F,UAAA,CAAWtE,UAAA,GAAasE,UAAA,CAAWlE,SAAA,EAE5BkE,UAAA,CAAWxE,MAAA,IAAUwE,UAAA,CAAWxE,MAAA,KAAW,KAAKrD,UAAA,CAAWqE,MAAA,GAE9DwD,UAAA,CAAWpE,aAAA,GAAgBoE,UAAA,CAAWxE,MAAA,EAEtC,KAAKK,YAAA,CAAamE,UAAU,GACxBZ,OAAA,IAAS,KAAKvD,YAAA,CAAamE,UAAA,EAAY,YAAY,GAEvDA,UAAA,CAAWxE,MAAA,GAASwE,UAAA,CAAWxE,MAAA,CAAOgB,MAAA;IAG7B9D,YAAA,CAAA6G,WAAA,GAAcnF,CAAA,CAAEqB,YAAA,CAE7B,QAAK0D,SAAA,CAAU/E,CAAC,GAChB,KAAK+E,SAAA,CAAUa,UAAU;EAC7B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQU3G,cAAcsF,IAAA,EACxB;IACQ,MAAEA,IAAA,YAAgBC,qBAAA,GACtB;MACI9D,OAAA,CAAQC,IAAA,CAAK,iEAAiE;MAE9E;IACJ;IAEA,MAAMrC,YAAA,GAAe,KAAKA,YAAA,CAAaiG,IAAA,CAAKM,SAAS;IAErD,IAAIvG,YAAA,CAAa6G,WAAA,EACjB;MACI,MAAMH,OAAA,GAAUT,IAAA,CAAKG,WAAA,KAAgB,WAAWH,IAAA,CAAKG,WAAA,KAAgB;QAC/DO,SAAA,GAAY,KAAKC,iBAAA,CAAkB5G,YAAA,CAAa6G,WAAW;QAG3DE,QAAA,GAAW,KAAKZ,kBAAA,CAAmBF,IAAA,EAAM,cAAcU,SAAS;MAEtE,KAAKlF,aAAA,CAAcsF,QAAQ,GACvBL,OAAA,IAAS,KAAKjF,aAAA,CAAcsF,QAAA,EAAU,UAAU;MAIpD,MAAME,UAAA,GAAa,KAAKd,kBAAA,CAAmBF,IAAA,EAAM,gBAAgBU,SAAS;MAE1E,KAAAM,UAAA,CAAWjE,UAAA,GAAaiE,UAAA,CAAW7D,SAAA,EAE5B6D,UAAA,CAAWnE,MAAA,IAAUmE,UAAA,CAAWnE,MAAA,KAAW,KAAKrD,UAAA,CAAWqE,MAAA,GAE9DmD,UAAA,CAAW/D,aAAA,GAAgB+D,UAAA,CAAWnE,MAAA,EAEtC,KAAKK,YAAA,CAAa8D,UAAU,GACxBP,OAAA,IAAS,KAAKvD,YAAA,CAAa8D,UAAA,EAAY,YAAY,GAEvDA,UAAA,CAAWnE,MAAA,GAASmE,UAAA,CAAWnE,MAAA,CAAOgB,MAAA;MAG7B9D,YAAA,CAAA6G,WAAA,GAAc,MAE3B,KAAKJ,SAAA,CAAUM,QAAQ,GACvB,KAAKN,SAAA,CAAUQ,UAAU;IAC7B;IAEA,KAAKS,MAAA,GAAS;EAClB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYU7G,aAAaoF,IAAA,EACvB;IACQ,MAAEA,IAAA,YAAgBC,qBAAA,GACtB;MACI9D,OAAA,CAAQC,IAAA,CAAK,iEAAiE;MAE9E;IACJ;IAEA,MAAMsF,GAAA,GAAMC,WAAA,CAAYD,GAAA;MAClBjG,CAAA,GAAI,KAAKyE,kBAAA,CAAmBF,IAAI;IAItC,IAFA,KAAKxE,aAAA,CAAcC,CAAA,EAAG,WAAW,GAE7BA,CAAA,CAAE0E,WAAA,KAAgB,SAEb,KAAA3E,aAAA,CAAcC,CAAA,EAAG,UAAU,WAE3BA,CAAA,CAAE0E,WAAA,KAAgB,WAAW1E,CAAA,CAAE0E,WAAA,KAAgB,OACxD;MACU,MAAAC,aAAA,GAAgB3E,CAAA,CAAE4E,MAAA,KAAW;MAEnC,KAAK7E,aAAA,CAAcC,CAAA,EAAG2E,aAAA,GAAgB,YAAY,SAAS;IAC/D;IAEA,MAAMrG,YAAA,GAAe,KAAKA,YAAA,CAAaiG,IAAA,CAAKM,SAAS;MAC/CsB,WAAA,GAAc,KAAKjB,iBAAA,CAAkB5G,YAAA,CAAawG,oBAAA,CAAqBP,IAAA,CAAKK,MAAM,CAAC;IAEzF,IAAIwB,WAAA,GAAcD,WAAA;IAIlB,IAAIA,WAAA,IAAe,CAACnG,CAAA,CAAEqB,YAAA,GAAeiE,QAAA,CAASa,WAAW,GACzD;MACI,IAAI3E,aAAA,GAAgB2E,WAAA;MAEpB,OAAO3E,aAAA,IAAiB,CAACxB,CAAA,CAAEqB,YAAA,GAAeiE,QAAA,CAAS9D,aAAa,IAChE;QACI,IAAAxB,CAAA,CAAEwB,aAAA,GAAgBA,aAAA,EAElB,KAAKC,YAAA,CAAazB,CAAA,EAAG,kBAAkB,GAEnCA,CAAA,CAAE0E,WAAA,KAAgB,SAEb,KAAAjD,YAAA,CAAazB,CAAA,EAAG,iBAAiB,WAEjCA,CAAA,CAAE0E,WAAA,KAAgB,WAAW1E,CAAA,CAAE0E,WAAA,KAAgB,OACxD;UACU,MAAAC,aAAA,GAAgB3E,CAAA,CAAE4E,MAAA,KAAW;UAEnC,KAAKnD,YAAA,CAAazB,CAAA,EAAG2E,aAAA,GAAgB,mBAAmB,gBAAgB;QAC5E;QAEAnD,aAAA,GAAgBA,aAAA,CAAcY,MAAA;MAClC;MAEA,OAAO9D,YAAA,CAAawG,oBAAA,CAAqBP,IAAA,CAAKK,MAAM,GAIpDwB,WAAA,GAAc5E,aAAA;IAClB;IAGA,IAAI4E,WAAA,EACJ;MACI,MAAMC,UAAA,GAAa,KAAKX,iBAAA,CAAkB1F,CAAA,EAAG,OAAO;MAEpDqG,UAAA,CAAWjF,MAAA,GAASgF,WAAA,EACpBC,UAAA,CAAWC,IAAA,GAAO,MAEbhI,YAAA,CAAaiI,cAAA,CAAehC,IAAA,CAAKK,MAAM,MAExCtG,YAAA,CAAaiI,cAAA,CAAehC,IAAA,CAAKK,MAAM,IAAI;QACvC4B,UAAA,EAAY;QACZpF,MAAA,EAAQiF,UAAA,CAAWjF,MAAA;QACnBqF,SAAA,EAAWR;MAAA;MAInB,MAAMS,YAAA,GAAepI,YAAA,CAAaiI,cAAA,CAAehC,IAAA,CAAKK,MAAM;MAExD,IAAA8B,YAAA,CAAatF,MAAA,KAAWiF,UAAA,CAAWjF,MAAA,IAChC6E,GAAA,GAAMS,YAAA,CAAaD,SAAA,GAAY,MAElC,EAAEC,YAAA,CAAaF,UAAA,GAIfE,YAAA,CAAaF,UAAA,GAAa,GAG9BE,YAAA,CAAatF,MAAA,GAASiF,UAAA,CAAWjF,MAAA,EACjCsF,YAAA,CAAaD,SAAA,GAAYR,GAAA,EAEzBI,UAAA,CAAWM,MAAA,GAASD,YAAA,CAAaF,UAAA,EAE7BH,UAAA,CAAW3B,WAAA,KAAgB,SAC/B;QACU,MAAAC,aAAA,GAAgB0B,UAAA,CAAWzB,MAAA,KAAW;QAE5C,KAAK7E,aAAA,CAAcsG,UAAA,EAAY1B,aAAA,GAAgB,eAAe,OAAO;MACzE,OACS0B,UAAA,CAAW3B,WAAA,KAAgB,WAEhC,KAAK3E,aAAA,CAAcsG,UAAA,EAAY,KAAK;MAGxC,KAAKtG,aAAA,CAAcsG,UAAA,EAAY,YAAY,GAE3C,KAAKtB,SAAA,CAAUsB,UAAU;IAC7B;IAEA,KAAKtB,SAAA,CAAU/E,CAAC;EACpB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAaUZ,oBAAoBmF,IAAA,EAC9B;IACQ,MAAEA,IAAA,YAAgBC,qBAAA,GACtB;MACI9D,OAAA,CAAQC,IAAA,CAAK,iEAAiE;MAE9E;IACJ;IAEA,MAAMrC,YAAA,GAAe,KAAKA,YAAA,CAAaiG,IAAA,CAAKM,SAAS;MAC/CsB,WAAA,GAAc,KAAKjB,iBAAA,CAAkB5G,YAAA,CAAawG,oBAAA,CAAqBP,IAAA,CAAKK,MAAM,CAAC;MACnF5E,CAAA,GAAI,KAAKyE,kBAAA,CAAmBF,IAAI;IAEtC,IAAI4B,WAAA,EACJ;MACI,IAAI3E,aAAA,GAAgB2E,WAAA;MAEb,OAAA3E,aAAA,GAEHxB,CAAA,CAAEwB,aAAA,GAAgBA,aAAA,EAElB,KAAKC,YAAA,CAAazB,CAAA,EAAG,kBAAkB,GAEnCA,CAAA,CAAE0E,WAAA,KAAgB,UAElB,KAAKjD,YAAA,CAAazB,CAAA,EAAG,iBAAiB,KAEjCA,CAAA,CAAE0E,WAAA,KAAgB,WAAW1E,CAAA,CAAE0E,WAAA,KAAgB,UAEpD,KAAKjD,YAAA,CAAazB,CAAA,EAAGA,CAAA,CAAE4E,MAAA,KAAW,IAAI,mBAAmB,gBAAgB,GAG7EpD,aAAA,GAAgBA,aAAA,CAAcY,MAAA;MAG3B,OAAA9D,YAAA,CAAawG,oBAAA,CAAqBP,IAAA,CAAKK,MAAM;IACxD;IAEA,KAAKG,SAAA,CAAU/E,CAAC;EACpB;EAAA;AAAA;AAAA;AAAA;EAMUX,SAASkF,IAAA,EACnB;IACQ,MAAEA,IAAA,YAAgBqC,mBAAA,GACtB;MACIlG,OAAA,CAAQC,IAAA,CAAK,6DAA6D;MAE1E;IACJ;IAEM,MAAAkG,UAAA,GAAa,KAAKC,gBAAA,CAAiBvC,IAAI;IAE7C,KAAKxE,aAAA,CAAc8G,UAAU,GAC7B,KAAK9B,SAAA,CAAU8B,UAAU;EAC7B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUU3B,kBAAkB/C,eAAA,EAC5B;IACI,IAAI,CAACA,eAAA,EAEM;IAGP,IAAAX,aAAA,GAAgBW,eAAA,CAAgB,CAAC;IAE5B,SAAA5B,CAAA,GAAI,GAAGA,CAAA,GAAI4B,eAAA,CAAgB1B,MAAA,IAI5B0B,eAAA,CAAgB5B,CAAC,EAAE6B,MAAA,KAAWZ,aAAA,EAJMjB,CAAA,IAMpCiB,aAAA,GAAgBW,eAAA,CAAgB5B,CAAC;IAQlC,OAAAiB,aAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUUiD,mBACNF,IAAA,EACA/E,IAAA,EACA4B,MAAA,EAEJ;IACU,MAAAc,KAAA,GAAQ,KAAK6E,aAAA,CAAcvC,qBAAqB;IAEtD,YAAKwC,eAAA,CAAgBzC,IAAA,EAAMrC,KAAK,GAChC,KAAK+E,aAAA,CAAc1C,IAAA,EAAMrC,KAAK,GAC9B,KAAKgF,QAAA,CAAS3C,IAAA,EAAMrC,KAAK,GAEzBA,KAAA,CAAMiF,WAAA,GAAc5C,IAAA,CAAK4C,WAAA,EACzBjF,KAAA,CAAMkF,aAAA,GAAgB7C,IAAA,EACtBrC,KAAA,CAAMd,MAAA,GAASA,MAAA,IACR,KAAKR,OAAA,CAAQsB,KAAA,CAAMmF,MAAA,CAAOxG,CAAA,EAAGqB,KAAA,CAAMmF,MAAA,CAAOvG,CAAC,KAC3C,KAAKpC,YAAA,CAAa,CAAC,GAEtB,OAAOc,IAAA,IAAS,aAEhB0C,KAAA,CAAM1C,IAAA,GAAOA,IAAA,GAGV0C,KAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQU4E,iBAAiBvC,IAAA,EAC3B;IACU,MAAArC,KAAA,GAAQ,KAAK6E,aAAA,CAAcH,mBAAmB;IAEpD,YAAKU,aAAA,CAAc/C,IAAA,EAAMrC,KAAK,GAC9B,KAAK+E,aAAA,CAAc1C,IAAA,EAAMrC,KAAK,GAC9B,KAAKgF,QAAA,CAAS3C,IAAA,EAAMrC,KAAK,GAEzBA,KAAA,CAAMiF,WAAA,GAAc5C,IAAA,CAAK4C,WAAA,EACzBjF,KAAA,CAAMkF,aAAA,GAAgB7C,IAAA,EACtBrC,KAAA,CAAMd,MAAA,GAAS,KAAKR,OAAA,CAAQsB,KAAA,CAAMmF,MAAA,CAAOxG,CAAA,EAAGqB,KAAA,CAAMmF,MAAA,CAAOvG,CAAC,GAEnDoB,KAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASUwD,kBAAkBnB,IAAA,EAA6B/E,IAAA,EACzD;IACU,MAAA0C,KAAA,GAAQ,KAAK6E,aAAA,CAAcvC,qBAAqB;IAEtD,OAAAtC,KAAA,CAAMiF,WAAA,GAAc5C,IAAA,CAAK4C,WAAA,EACzBjF,KAAA,CAAMkF,aAAA,GAAgB7C,IAAA,CAAK6C,aAAA,EAE3B,KAAKJ,eAAA,CAAgBzC,IAAA,EAAMrC,KAAK,GAChC,KAAK+E,aAAA,CAAc1C,IAAA,EAAMrC,KAAK,GAC9B,KAAKgF,QAAA,CAAS3C,IAAA,EAAMrC,KAAK,GAGzBA,KAAA,CAAMd,MAAA,GAASmD,IAAA,CAAKnD,MAAA,EACpBc,KAAA,CAAMoE,IAAA,GAAO/B,IAAA,CAAKlD,YAAA,GAAekG,KAAA,CAAM,GACvCrF,KAAA,CAAM1C,IAAA,GAAOA,IAAA,IAAQ0C,KAAA,CAAM1C,IAAA,EAEpB0C,KAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAaUoF,cAAc/C,IAAA,EAA2BiD,EAAA,EACnD;IACIA,EAAA,CAAGC,SAAA,GAAYlD,IAAA,CAAKkD,SAAA,EACpBD,EAAA,CAAGE,MAAA,GAASnD,IAAA,CAAKmD,MAAA,EACjBF,EAAA,CAAGG,MAAA,GAASpD,IAAA,CAAKoD,MAAA,EACjBH,EAAA,CAAGI,MAAA,GAASrD,IAAA,CAAKqD,MAAA;EACrB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAkBUZ,gBAAgBzC,IAAA,EAAsBiD,EAAA,EAChD;IACUjD,IAAA,YAAgBC,qBAAA,IAAyBgD,EAAA,YAAchD,qBAAA,KAE7DgD,EAAA,CAAG3C,SAAA,GAAYN,IAAA,CAAKM,SAAA,EACpB2C,EAAA,CAAGK,KAAA,GAAQtD,IAAA,CAAKsD,KAAA,EAChBL,EAAA,CAAGM,MAAA,GAASvD,IAAA,CAAKuD,MAAA,EACjBN,EAAA,CAAGO,SAAA,GAAYxD,IAAA,CAAKwD,SAAA,EACpBP,EAAA,CAAG9C,WAAA,GAAcH,IAAA,CAAKG,WAAA,EACtB8C,EAAA,CAAGQ,QAAA,GAAWzD,IAAA,CAAKyD,QAAA,EACnBR,EAAA,CAAGS,kBAAA,GAAqB1D,IAAA,CAAK0D,kBAAA,EAC7BT,EAAA,CAAGU,KAAA,GAAQ3D,IAAA,CAAK2D,KAAA,EAChBV,EAAA,CAAGW,KAAA,GAAQ5D,IAAA,CAAK4D,KAAA,EAChBX,EAAA,CAAGY,KAAA,GAAQ7D,IAAA,CAAK6D,KAAA;EACpB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAwBUnB,cAAc1C,IAAA,EAAsBiD,EAAA,EAC9C;IACUjD,IAAA,YAAgB8D,mBAAA,IAAuBb,EAAA,YAAca,mBAAA,KAE3Db,EAAA,CAAGc,MAAA,GAAS/D,IAAA,CAAK+D,MAAA,EACjBd,EAAA,CAAG5C,MAAA,GAASL,IAAA,CAAKK,MAAA,EACjB4C,EAAA,CAAGe,OAAA,GAAUhE,IAAA,CAAKgE,OAAA,EAClBf,EAAA,CAAGgB,MAAA,CAAOC,QAAA,CAASlE,IAAA,CAAKiE,MAAM,GAC9BhB,EAAA,CAAGkB,OAAA,GAAUnE,IAAA,CAAKmE,OAAA,EAClBlB,EAAA,CAAGmB,OAAA,GAAUpE,IAAA,CAAKoE,OAAA,EAClBnB,EAAA,CAAGoB,QAAA,CAASH,QAAA,CAASlE,IAAA,CAAKqE,QAAQ,GAClCpB,EAAA,CAAGqB,MAAA,CAAOJ,QAAA,CAASlE,IAAA,CAAKsE,MAAM,GAC9BrB,EAAA,CAAGsB,QAAA,GAAWvE,IAAA,CAAKuE,QAAA,EACnBtB,EAAA,CAAGH,MAAA,CAAOoB,QAAA,CAASlE,IAAA,CAAK8C,MAAM;EAClC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAaUH,SAAS3C,IAAA,EAAsBiD,EAAA,EACzC;IACIA,EAAA,CAAGuB,SAAA,GAAYxE,IAAA,CAAKwE,SAAA,EACpBvB,EAAA,CAAGwB,UAAA,GAAazE,IAAA,CAAKyE,UAAA,EACrBxB,EAAA,CAAGf,SAAA,GAAYP,WAAA,CAAYD,GAAA,CAC3B,GAAAuB,EAAA,CAAGhI,IAAA,GAAO+E,IAAA,CAAK/E,IAAA,EACfgI,EAAA,CAAGb,MAAA,GAASpC,IAAA,CAAKoC,MAAA,EACjBa,EAAA,CAAGyB,IAAA,GAAO1E,IAAA,CAAK0E,IAAA,EACfzB,EAAA,CAAG0B,KAAA,GAAQ3E,IAAA,CAAK2E,KAAA,EAChB1B,EAAA,CAAG2B,KAAA,CAAMV,QAAA,CAASlE,IAAA,CAAK4E,KAAK,GAC5B3B,EAAA,CAAG4B,IAAA,CAAKX,QAAA,CAASlE,IAAA,CAAK6E,IAAI;EAC9B;EAAA;AAAA;AAAA;AAAA;AAAA;EAOU9K,aAAa+K,EAAA,EACvB;IACS,YAAKhL,YAAA,CAAaC,YAAA,CAAa+K,EAAE,MAElC,KAAKhL,YAAA,CAAaC,YAAA,CAAa+K,EAAE,IAAI;MACjCvE,oBAAA,EAAsB,CAAC;MACvByB,cAAA,EAAgB,CAAC;MACjB+C,UAAA,EAAY;IAIb,SAAKjL,YAAA,CAAaC,YAAA,CAAa+K,EAAE;EAC5C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASUtC,cACNjJ,WAAA,EAEJ;IACS,KAAKS,SAAA,CAAUgL,GAAA,CAAIzL,WAAkB,KAEtC,KAAKS,SAAA,CAAU4C,GAAA,CAAIrD,WAAA,EAAoB,EAAE;IAGvC,MAAAoE,KAAA,GAAQ,KAAK3D,SAAA,CAAUiL,GAAA,CAAI1L,WAAkB,EAAE2L,GAAA,MAC9C,IAAI3L,WAAA,CAAY,IAAI;IAErB,OAAAoE,KAAA,CAAAZ,UAAA,GAAaY,KAAA,CAAMwH,IAAA,EACzBxH,KAAA,CAAMV,aAAA,GAAgB,MACtBU,KAAA,CAAMoE,IAAA,GAAO,MACbpE,KAAA,CAAMd,MAAA,GAAS,MAERc,KAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAaU6C,UAAoC7C,KAAA,EAC9C;IACI,IAAIA,KAAA,CAAMyH,OAAA,KAAY,MAAY,UAAItH,KAAA,CAAM,mEAAmE;IAE/G,MAAMvE,WAAA,GAAcoE,KAAA,CAAMpE,WAAA;IAErB,KAAKS,SAAA,CAAUgL,GAAA,CAAIzL,WAAkB,KAEtC,KAAKS,SAAA,CAAU4C,GAAA,CAAIrD,WAAA,EAAoB,EAAE,GAG7C,KAAKS,SAAA,CAAUiL,GAAA,CAAI1L,WAAkB,EAAE4B,IAAA,CAAKwC,KAAK;EACrD;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQQoC,gBAAgBtE,CAAA,EAAmBR,IAAA,EAC3C;IACI,MAAMoK,SAAA,GAAc5J,CAAA,CAAEwB,aAAA,CAAsBqI,OAAA,CAA6BrK,IAAI;IAExE,IAAAoK,SAAA,IACA5J,CAAA,CAAEwB,aAAA,CAAc2B,aAAA,CAAc,GAEnC,IAAI,QAAQyG,SAAA,EAEJA,SAAA,CAAUE,IAAA,IAAM9J,CAAA,CAAEwB,aAAA,CAAcuI,cAAA,CAAevK,IAAA,EAAMoK,SAAA,CAAUnK,EAAA,EAAI,QAAW,EAAI,GACtFmK,SAAA,CAAUnK,EAAA,CAAGuK,IAAA,CAAKJ,SAAA,CAAUK,OAAA,EAASjK,CAAC,OAK9B,SAAAO,CAAA,GAAI,GAAGC,CAAA,GAAIoJ,SAAA,CAAUnJ,MAAA,EACzBF,CAAA,GAAIC,CAAA,IAAK,CAACR,CAAA,CAAEE,6BAAA,EACZK,CAAA,IAEIqJ,SAAA,CAAUrJ,CAAC,EAAEuJ,IAAA,IAAM9J,CAAA,CAAEwB,aAAA,CAAcuI,cAAA,CAAevK,IAAA,EAAMoK,SAAA,CAAUrJ,CAAC,EAAEd,EAAA,EAAI,QAAW,EAAI,GAC5FmK,SAAA,CAAUrJ,CAAC,EAAEd,EAAA,CAAGuK,IAAA,CAAKJ,SAAA,CAAUrJ,CAAC,EAAE0J,OAAA,EAASjK,CAAC;EAGxD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}