from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List
import json
from pathlib import Path
import logging
from fastapi import Query, Body
import random
import sys

logging.basicConfig(stream=sys.stdout, level=logging.DEBUG, format='%(levelname)s:%(message)s')

app = FastAPI()

# Enable CORS for all origins (development only)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class Province(BaseModel):
    name: str
    owner: str
    population: int
    resources: int

class Country(BaseModel):
    name: str
    population: int
    resources: int
    geometry: dict  # GeoJSON geometry

# --- Core System Skeletons ---
class MapManager:
    def __init__(self):
        logging.debug("Initializing MapManager...")
        self.provinces = [
            Province(name="Capitalia", owner="Player", population=100000, resources=50),
            Province(name="Eastmarch", owner="AI1", population=80000, resources=40),
            Province(name="Westvale", owner="AI2", population=60000, resources=30),
        ]
        logging.debug(f"Provinces initialized: {self.provinces}")
        self.countries = self.load_countries()
        logging.debug(f"Countries loaded: {self.countries}")

    def load_countries(self):
        geojson_path = Path(__file__).parent / "data" / "countries.geojson"
        logging.debug(f"Loading countries from: {geojson_path}")
        if not geojson_path.exists():
            logging.error(f"GeoJSON file not found: {geojson_path}")
            return []
        try:
            with open(geojson_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            logging.debug(f"GeoJSON data loaded: {str(data)[:500]}")
            countries = []
            features = data.get("features", [])
            logging.debug(f"Features found: {len(features)}")
            if not features:
                logging.warning("No features found in GeoJSON file.")
            for feature in features:
                properties = feature.get("properties", {})
                logging.debug(f"Feature properties: {properties}")
                name = properties.get("NAME") or properties.get("name") or properties.get("Country") or "Unknown"
                population = properties.get("population", 1000000)
                resources = properties.get("resources", 100)
                geometry = feature.get("geometry")
                if not geometry:
                    logging.warning(f"Feature missing geometry: {name}")
                    continue
                stability = properties.get("stability", 100)
                country = {
                    "name": name,
                    "population": population,
                    "resources": resources,
                    "stability": stability,
                    "geometry": geometry,
                    "properties": {"NAME": name}
                }
                logging.debug(f"Country parsed: {country}")
                countries.append(country)
            logging.info(f"Loaded {len(countries)} countries from GeoJSON.")
            return countries
        except Exception as e:
            logging.error(f"Error loading countries: {e}")
            return []

class PopSystem:
    def update(self, countries):
        # Placeholder: simple population growth
        for c in countries:
            c["population"] = int(c["population"] * 1.01)

class EconomySystem:
    def update(self, countries):
        # Placeholder: simple resource growth
        for c in countries:
            c["resources"] += 10

class EventSystem:
    def update(self, countries):
        # Placeholder: no events yet
        pass
    def get_random_event(self, country_name=None):
        # Example stub events
        events = [
            {"title": "Harvest Festival", "description": "A bountiful harvest boosts morale!", "effect": "+10 stability"},
            {"title": "Border Skirmish", "description": "Minor clashes on the border reduce stability.", "effect": "-5 stability"},
            {"title": "Scientific Breakthrough", "description": "Innovation increases resources.", "effect": "+20 resources"}
        ]
        return random.choice(events)
    def apply_event(self, countries, country_name, effect):
        # Very basic effect parser for demo purposes
        for c in countries:
            if c["name"] == country_name:
                if "+10 stability" in effect:
                    c["stability"] = c.get("stability", 100) + 10
                if "-5 stability" in effect:
                    c["stability"] = c.get("stability", 100) - 5
                if "+20 resources" in effect:
                    c["resources"] += 20
                # Clamp stability
                c["stability"] = max(0, min(100, c.get("stability", 100)))
                return c
        return None

class DiplomacySystem:
    def update(self, countries):
        # Placeholder: no diplomacy yet
        pass
    def get_actions(self, country_name=None):
        # Example stub actions
        actions = [
            {"action": "Form Alliance", "target": "Neighborland"},
            {"action": "Declare War", "target": "Rivalia"},
            {"action": "Send Trade Offer", "target": "Mercatoria"}
        ]
        if country_name:
            # Filter or customize actions per country if needed
            return actions
        return actions
    def perform_action(self, countries, country_name, action, target):
        # Stub: just return a message
        return {"result": f"{country_name} performed '{action}' with {target}. (No effect yet)"}

class GameManager:
    def __init__(self):
        logging.debug("Initializing GameManager...")
        self.turn = 1
        self.map_manager = MapManager()
        self.pop_system = PopSystem()
        self.economy_system = EconomySystem()
        self.diplomacy_system = DiplomacySystem()
        self.event_system = EventSystem()
        logging.debug("GameManager initialized.")

    def advance_turn(self):
        logging.debug(f"Advancing turn from {self.turn}")
        self.turn += 1
        self.pop_system.update(self.map_manager.countries)
        self.economy_system.update(self.map_manager.countries)
        self.diplomacy_system.update(self.map_manager.countries)
        self.event_system.update(self.map_manager.countries)
        logging.debug(f"Turn advanced to {self.turn}")
        logging.debug(f"Countries after turn: {self.map_manager.countries}")
        return {
            "turn": self.turn,
            "countries": self.map_manager.countries
        }

game = GameManager()

@app.get("/provinces", response_model=List[Province])
def get_provinces():
    logging.debug("/provinces endpoint called")
    return game.map_manager.provinces

@app.get("/countries")
def get_countries():
    logging.debug("/countries endpoint called")
    logging.debug(f"Countries to return: {game.map_manager.countries}")
    # Return countries in the format expected by frontend (with properties.NAME)
    return game.map_manager.countries

@app.post("/turn")
def advance_turn():
    logging.debug("/turn endpoint called")
    result = game.advance_turn()
    result["countries"] = [Country(**c) for c in result["countries"]]
    logging.debug(f"Turn result: {result}")
    return result

@app.get("/event")
def get_event(country: str = Query(None)):
    logging.debug(f"/event endpoint called for country: {country}")
    return game.event_system.get_random_event(country)

@app.get("/diplomacy")
def get_diplomacy(country: str = Query(None)):
    logging.debug(f"/diplomacy endpoint called for country: {country}")
    return game.diplomacy_system.get_actions(country)

@app.post("/event/apply")
def apply_event_effect(data: dict = Body(...)):
    logging.debug(f"/event/apply endpoint called with data: {data}")
    country = data.get("country")
    effect = data.get("effect")
    updated = game.event_system.apply_event(game.map_manager.countries, country, effect)
    if updated:
        logging.debug(f"Event applied to country: {updated}")
        return {"success": True, "country": updated}
    logging.debug("Event apply failed: Country not found")
    return {"success": False, "error": "Country not found"}

@app.post("/diplomacy/perform")
def perform_diplomacy_action(data: dict = Body(...)):
    logging.debug(f"/diplomacy/perform endpoint called with data: {data}")
    country = data.get("country")
    action = data.get("action")
    target = data.get("target")
    result = game.diplomacy_system.perform_action(game.map_manager.countries, country, action, target)
    logging.debug(f"Diplomacy action result: {result}")
    return result

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
