{"ast": null, "code": "function createIndicesForQuads(size, outBuffer = null) {\n  const totalIndices = size * 6;\n  if (outBuffer = outBuffer || new Uint16Array(totalIndices), outBuffer.length !== totalIndices) throw new Error(`Out buffer length is incorrect, got ${outBuffer.length} and expected ${totalIndices}`);\n  for (let i = 0, j = 0; i < totalIndices; i += 6, j += 4) outBuffer[i + 0] = j + 0, outBuffer[i + 1] = j + 1, outBuffer[i + 2] = j + 2, outBuffer[i + 3] = j + 0, outBuffer[i + 4] = j + 2, outBuffer[i + 5] = j + 3;\n  return outBuffer;\n}\nexport { createIndicesForQuads };", "map": {"version": 3, "names": ["createIndicesForQuads", "size", "outBuffer", "totalIndices", "Uint16Array", "length", "Error", "i", "j"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\utils\\src\\data\\createIndicesForQuads.ts"], "sourcesContent": ["/**\n * Generic Mask Stack data structure\n * @memberof PIXI.utils\n * @function createIndicesForQuads\n * @param {number} size - Number of quads\n * @param {Uint16Array|Uint32Array} [outBuffer] - Buffer for output, length has to be `6 * size`\n * @returns {Uint16Array|Uint32Array} - Resulting index buffer\n */\nexport function createIndicesForQuads(\n    size: number,\n    outBuffer: Uint16Array | Uint32Array | null = null\n): Uint16Array | Uint32Array\n{\n    // the total number of indices in our array, there are 6 points per quad.\n    const totalIndices = size * 6;\n\n    outBuffer = outBuffer || new Uint16Array(totalIndices);\n\n    if (outBuffer.length !== totalIndices)\n    {\n        throw new Error(`Out buffer length is incorrect, got ${outBuffer.length} and expected ${totalIndices}`);\n    }\n\n    // fill the indices with the quads to draw\n    for (let i = 0, j = 0; i < totalIndices; i += 6, j += 4)\n    {\n        outBuffer[i + 0] = j + 0;\n        outBuffer[i + 1] = j + 1;\n        outBuffer[i + 2] = j + 2;\n        outBuffer[i + 3] = j + 0;\n        outBuffer[i + 4] = j + 2;\n        outBuffer[i + 5] = j + 3;\n    }\n\n    return outBuffer;\n}\n"], "mappings": "AAQgB,SAAAA,sBACZC,IAAA,EACAC,SAAA,GAA8C,MAElD;EAEI,MAAMC,YAAA,GAAeF,IAAA,GAAO;EAI5B,IAFAC,SAAA,GAAYA,SAAA,IAAa,IAAIE,WAAA,CAAYD,YAAY,GAEjDD,SAAA,CAAUG,MAAA,KAAWF,YAAA,EAErB,MAAM,IAAIG,KAAA,CAAM,uCAAuCJ,SAAA,CAAUG,MAAM,iBAAiBF,YAAY,EAAE;EAIjG,SAAAI,CAAA,GAAI,GAAGC,CAAA,GAAI,GAAGD,CAAA,GAAIJ,YAAA,EAAcI,CAAA,IAAK,GAAGC,CAAA,IAAK,GAElDN,SAAA,CAAUK,CAAA,GAAI,CAAC,IAAIC,CAAA,GAAI,GACvBN,SAAA,CAAUK,CAAA,GAAI,CAAC,IAAIC,CAAA,GAAI,GACvBN,SAAA,CAAUK,CAAA,GAAI,CAAC,IAAIC,CAAA,GAAI,GACvBN,SAAA,CAAUK,CAAA,GAAI,CAAC,IAAIC,CAAA,GAAI,GACvBN,SAAA,CAAUK,CAAA,GAAI,CAAC,IAAIC,CAAA,GAAI,GACvBN,SAAA,CAAUK,CAAA,GAAI,CAAC,IAAIC,CAAA,GAAI;EAGpB,OAAAN,SAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}