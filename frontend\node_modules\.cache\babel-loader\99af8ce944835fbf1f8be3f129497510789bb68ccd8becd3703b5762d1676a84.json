{"ast": null, "code": "const ProgramCache = {},\n  TextureCache = /* @__PURE__ */Object.create(null),\n  BaseTextureCache = /* @__PURE__ */Object.create(null);\nfunction destroyTextureCache() {\n  let key;\n  for (key in TextureCache) TextureCache[key].destroy();\n  for (key in BaseTextureCache) BaseTextureCache[key].destroy();\n}\nfunction clearTextureCache() {\n  let key;\n  for (key in TextureCache) delete TextureCache[key];\n  for (key in BaseTextureCache) delete BaseTextureCache[key];\n}\nexport { BaseTextureCache, ProgramCache, TextureCache, clearTextureCache, destroyTextureCache };", "map": {"version": 3, "names": ["ProgramCache", "TextureCache", "Object", "create", "BaseTextureCache", "destroyTextureCache", "key", "destroy", "clearTextureCache"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\utils\\src\\media\\caches.ts"], "sourcesContent": ["import type { BaseTexture, Program, Texture } from '@pixi/core';\n\n/**\n * @todo Describe property usage\n * @static\n * @name ProgramCache\n * @memberof PIXI.utils\n * @type {Record<string, Program>}\n */\nexport const ProgramCache: {[key: string]: Program} = {};\n\n/**\n * @todo Describe property usage\n * @static\n * @name TextureCache\n * @memberof PIXI.utils\n * @type {Record<string, Texture>}\n */\nexport const TextureCache: {[key: string]: Texture} = Object.create(null);\n\n/**\n * @todo Describe property usage\n * @static\n * @name BaseTextureCache\n * @memberof PIXI.utils\n * @type {Record<string, BaseTexture>}\n */\nexport const BaseTextureCache: {[key: string]: BaseTexture} = Object.create(null);\n\n/**\n * Destroys all texture in the cache\n * @memberof PIXI.utils\n * @function destroyTextureCache\n */\nexport function destroyTextureCache(): void\n{\n    let key;\n\n    for (key in TextureCache)\n    {\n        TextureCache[key].destroy();\n    }\n    for (key in BaseTextureCache)\n    {\n        BaseTextureCache[key].destroy();\n    }\n}\n\n/**\n * Removes all textures from cache, but does not destroy them\n * @memberof PIXI.utils\n * @function clearTextureCache\n */\nexport function clearTextureCache(): void\n{\n    let key;\n\n    for (key in TextureCache)\n    {\n        delete TextureCache[key];\n    }\n    for (key in BaseTextureCache)\n    {\n        delete BaseTextureCache[key];\n    }\n}\n"], "mappings": "AASa,MAAAA,YAAA,GAAyC,CAAC;EAS1CC,YAAA,GAAyC,eAAAC,MAAA,CAAOC,MAAA,CAAO,IAAI;EAS3DC,gBAAA,GAAwD,eAAAF,MAAA,CAAAC,MAAA,CAAO,IAAI;AAOzE,SAASE,oBAAA,EAChB;EACQ,IAAAC,GAAA;EAEJ,KAAKA,GAAA,IAAOL,YAAA,EAEKA,YAAA,CAAAK,GAAG,EAAEC,OAAA;EAEtB,KAAKD,GAAA,IAAOF,gBAAA,EAESA,gBAAA,CAAAE,GAAG,EAAEC,OAAA;AAE9B;AAOO,SAASC,kBAAA,EAChB;EACQ,IAAAF,GAAA;EAEJ,KAAKA,GAAA,IAAOL,YAAA,EAER,OAAOA,YAAA,CAAaK,GAAG;EAE3B,KAAKA,GAAA,IAAOF,gBAAA,EAER,OAAOA,gBAAA,CAAiBE,GAAG;AAEnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}