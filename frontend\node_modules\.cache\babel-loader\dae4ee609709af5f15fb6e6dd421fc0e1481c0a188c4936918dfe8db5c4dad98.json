{"ast": null, "code": "const uniformParsers = [\n// a float cache layer\n{\n  test: data => data.type === \"float\" && data.size === 1 && !data.isArray,\n  code: name => `\n            if(uv[\"${name}\"] !== ud[\"${name}\"].value)\n            {\n                ud[\"${name}\"].value = uv[\"${name}\"]\n                gl.uniform1f(ud[\"${name}\"].location, uv[\"${name}\"])\n            }\n            `\n},\n// handling samplers\n{\n  test: (data, uniform) =>\n  // eslint-disable-next-line max-len,no-eq-null,eqeqeq\n  (data.type === \"sampler2D\" || data.type === \"samplerCube\" || data.type === \"sampler2DArray\") && data.size === 1 && !data.isArray && (uniform == null || uniform.castToBaseTexture !== void 0),\n  code: name => `t = syncData.textureCount++;\n\n            renderer.texture.bind(uv[\"${name}\"], t);\n\n            if(ud[\"${name}\"].value !== t)\n            {\n                ud[\"${name}\"].value = t;\n                gl.uniform1i(ud[\"${name}\"].location, t);\n; // eslint-disable-line max-len\n            }`\n},\n// uploading pixi matrix object to mat3\n{\n  test: (data, uniform) => data.type === \"mat3\" && data.size === 1 && !data.isArray && uniform.a !== void 0,\n  code: name =>\n  // TODO and some smart caching dirty ids here!\n  `\n            gl.uniformMatrix3fv(ud[\"${name}\"].location, false, uv[\"${name}\"].toArray(true));\n            `,\n  codeUbo: name => `\n                var ${name}_matrix = uv.${name}.toArray(true);\n\n                data[offset] = ${name}_matrix[0];\n                data[offset+1] = ${name}_matrix[1];\n                data[offset+2] = ${name}_matrix[2];\n        \n                data[offset + 4] = ${name}_matrix[3];\n                data[offset + 5] = ${name}_matrix[4];\n                data[offset + 6] = ${name}_matrix[5];\n        \n                data[offset + 8] = ${name}_matrix[6];\n                data[offset + 9] = ${name}_matrix[7];\n                data[offset + 10] = ${name}_matrix[8];\n            `\n},\n// uploading a pixi point as a vec2 with caching layer\n{\n  test: (data, uniform) => data.type === \"vec2\" && data.size === 1 && !data.isArray && uniform.x !== void 0,\n  code: name => `\n                cv = ud[\"${name}\"].value;\n                v = uv[\"${name}\"];\n\n                if(cv[0] !== v.x || cv[1] !== v.y)\n                {\n                    cv[0] = v.x;\n                    cv[1] = v.y;\n                    gl.uniform2f(ud[\"${name}\"].location, v.x, v.y);\n                }`,\n  codeUbo: name => `\n                v = uv.${name};\n\n                data[offset] = v.x;\n                data[offset+1] = v.y;\n            `\n},\n// caching layer for a vec2\n{\n  test: data => data.type === \"vec2\" && data.size === 1 && !data.isArray,\n  code: name => `\n                cv = ud[\"${name}\"].value;\n                v = uv[\"${name}\"];\n\n                if(cv[0] !== v[0] || cv[1] !== v[1])\n                {\n                    cv[0] = v[0];\n                    cv[1] = v[1];\n                    gl.uniform2f(ud[\"${name}\"].location, v[0], v[1]);\n                }\n            `\n},\n// upload a pixi rectangle as a vec4 with caching layer\n{\n  test: (data, uniform) => data.type === \"vec4\" && data.size === 1 && !data.isArray && uniform.width !== void 0,\n  code: name => `\n                cv = ud[\"${name}\"].value;\n                v = uv[\"${name}\"];\n\n                if(cv[0] !== v.x || cv[1] !== v.y || cv[2] !== v.width || cv[3] !== v.height)\n                {\n                    cv[0] = v.x;\n                    cv[1] = v.y;\n                    cv[2] = v.width;\n                    cv[3] = v.height;\n                    gl.uniform4f(ud[\"${name}\"].location, v.x, v.y, v.width, v.height)\n                }`,\n  codeUbo: name => `\n                    v = uv.${name};\n\n                    data[offset] = v.x;\n                    data[offset+1] = v.y;\n                    data[offset+2] = v.width;\n                    data[offset+3] = v.height;\n                `\n},\n// upload a pixi color as vec4 with caching layer\n{\n  test: (data, uniform) => data.type === \"vec4\" && data.size === 1 && !data.isArray && uniform.red !== void 0,\n  code: name => `\n                cv = ud[\"${name}\"].value;\n                v = uv[\"${name}\"];\n\n                if(cv[0] !== v.red || cv[1] !== v.green || cv[2] !== v.blue || cv[3] !== v.alpha)\n                {\n                    cv[0] = v.red;\n                    cv[1] = v.green;\n                    cv[2] = v.blue;\n                    cv[3] = v.alpha;\n                    gl.uniform4f(ud[\"${name}\"].location, v.red, v.green, v.blue, v.alpha)\n                }`,\n  codeUbo: name => `\n                    v = uv.${name};\n\n                    data[offset] = v.red;\n                    data[offset+1] = v.green;\n                    data[offset+2] = v.blue;\n                    data[offset+3] = v.alpha;\n                `\n},\n// upload a pixi color as a vec3 with caching layer\n{\n  test: (data, uniform) => data.type === \"vec3\" && data.size === 1 && !data.isArray && uniform.red !== void 0,\n  code: name => `\n                cv = ud[\"${name}\"].value;\n                v = uv[\"${name}\"];\n\n                if(cv[0] !== v.red || cv[1] !== v.green || cv[2] !== v.blue || cv[3] !== v.a)\n                {\n                    cv[0] = v.red;\n                    cv[1] = v.green;\n                    cv[2] = v.blue;\n    \n                    gl.uniform3f(ud[\"${name}\"].location, v.red, v.green, v.blue)\n                }`,\n  codeUbo: name => `\n                    v = uv.${name};\n\n                    data[offset] = v.red;\n                    data[offset+1] = v.green;\n                    data[offset+2] = v.blue;\n                `\n},\n// a caching layer for vec4 uploading\n{\n  test: data => data.type === \"vec4\" && data.size === 1 && !data.isArray,\n  code: name => `\n                cv = ud[\"${name}\"].value;\n                v = uv[\"${name}\"];\n\n                if(cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])\n                {\n                    cv[0] = v[0];\n                    cv[1] = v[1];\n                    cv[2] = v[2];\n                    cv[3] = v[3];\n\n                    gl.uniform4f(ud[\"${name}\"].location, v[0], v[1], v[2], v[3])\n                }`\n}];\nexport { uniformParsers };", "map": {"version": 3, "names": ["uniformParsers", "test", "data", "type", "size", "isArray", "code", "name", "uniform", "castToBaseTexture", "a", "codeUbo", "x", "width", "red"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\shader\\utils\\uniformParsers.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n// Parsers, each one of these will take a look at the type of shader property and uniform.\n// if they pass the test function then the code function is called that returns a the shader upload code for that uniform.\n// Shader upload code is automagically generated with these parsers.\n// If no parser is valid then the default upload functions are used.\n// exposing Parsers means that custom upload logic can be added to pixi's shaders.\n// A good example would be a pixi rectangle can be directly set on a uniform.\n// If the shader sees it it knows how to upload the rectangle structure as a vec4\n// format is as follows:\n//\n// {\n//     test: (data, uniform) => {} <--- test is this code should be used for this uniform\n//     code: (name, uniform) => {} <--- returns the string of the piece of code that uploads the uniform\n//     codeUbo: (name, uniform) => {} <--- returns the string of the piece of code that uploads the\n//                                         uniform to a uniform buffer\n// }\n\nexport interface IUniformParser\n{\n    test(data: unknown, uniform: any): boolean;\n    code(name: string, uniform: any): string;\n    codeUbo?(name: string, uniform: any): string;\n}\n\nexport const uniformParsers: IUniformParser[] = [\n\n    // a float cache layer\n    {\n        test: (data: any): boolean =>\n            data.type === 'float' && data.size === 1 && !data.isArray,\n        code: (name: string): string =>\n            `\n            if(uv[\"${name}\"] !== ud[\"${name}\"].value)\n            {\n                ud[\"${name}\"].value = uv[\"${name}\"]\n                gl.uniform1f(ud[\"${name}\"].location, uv[\"${name}\"])\n            }\n            `,\n    },\n    // handling samplers\n    {\n        test: (data: any, uniform: any): boolean =>\n            // eslint-disable-next-line max-len,no-eq-null,eqeqeq\n            (data.type === 'sampler2D' || data.type === 'samplerCube' || data.type === 'sampler2DArray') && data.size === 1 && !data.isArray && (uniform == null || uniform.castToBaseTexture !== undefined),\n        code: (name: string): string => `t = syncData.textureCount++;\n\n            renderer.texture.bind(uv[\"${name}\"], t);\n\n            if(ud[\"${name}\"].value !== t)\n            {\n                ud[\"${name}\"].value = t;\n                gl.uniform1i(ud[\"${name}\"].location, t);\\n; // eslint-disable-line max-len\n            }`,\n    },\n    // uploading pixi matrix object to mat3\n    {\n        test: (data: any, uniform: any): boolean =>\n            data.type === 'mat3' && data.size === 1 && !data.isArray && uniform.a !== undefined,\n        code: (name: string): string =>\n\n            // TODO and some smart caching dirty ids here!\n            `\n            gl.uniformMatrix3fv(ud[\"${name}\"].location, false, uv[\"${name}\"].toArray(true));\n            `,\n        codeUbo: (name: string): string =>\n            `\n                var ${name}_matrix = uv.${name}.toArray(true);\n\n                data[offset] = ${name}_matrix[0];\n                data[offset+1] = ${name}_matrix[1];\n                data[offset+2] = ${name}_matrix[2];\n        \n                data[offset + 4] = ${name}_matrix[3];\n                data[offset + 5] = ${name}_matrix[4];\n                data[offset + 6] = ${name}_matrix[5];\n        \n                data[offset + 8] = ${name}_matrix[6];\n                data[offset + 9] = ${name}_matrix[7];\n                data[offset + 10] = ${name}_matrix[8];\n            `\n        ,\n\n    },\n    // uploading a pixi point as a vec2 with caching layer\n    {\n        test: (data: any, uniform: any): boolean =>\n            data.type === 'vec2' && data.size === 1 && !data.isArray && uniform.x !== undefined,\n        code: (name: string): string =>\n            `\n                cv = ud[\"${name}\"].value;\n                v = uv[\"${name}\"];\n\n                if(cv[0] !== v.x || cv[1] !== v.y)\n                {\n                    cv[0] = v.x;\n                    cv[1] = v.y;\n                    gl.uniform2f(ud[\"${name}\"].location, v.x, v.y);\n                }`,\n        codeUbo: (name: string): string =>\n            `\n                v = uv.${name};\n\n                data[offset] = v.x;\n                data[offset+1] = v.y;\n            `\n    },\n    // caching layer for a vec2\n    {\n        test: (data: any): boolean =>\n            data.type === 'vec2' && data.size === 1 && !data.isArray,\n        code: (name: string): string =>\n            `\n                cv = ud[\"${name}\"].value;\n                v = uv[\"${name}\"];\n\n                if(cv[0] !== v[0] || cv[1] !== v[1])\n                {\n                    cv[0] = v[0];\n                    cv[1] = v[1];\n                    gl.uniform2f(ud[\"${name}\"].location, v[0], v[1]);\n                }\n            `,\n    },\n    // upload a pixi rectangle as a vec4 with caching layer\n    {\n        test: (data: any, uniform: any): boolean =>\n            data.type === 'vec4' && data.size === 1 && !data.isArray && uniform.width !== undefined,\n\n        code: (name: string): string =>\n            `\n                cv = ud[\"${name}\"].value;\n                v = uv[\"${name}\"];\n\n                if(cv[0] !== v.x || cv[1] !== v.y || cv[2] !== v.width || cv[3] !== v.height)\n                {\n                    cv[0] = v.x;\n                    cv[1] = v.y;\n                    cv[2] = v.width;\n                    cv[3] = v.height;\n                    gl.uniform4f(ud[\"${name}\"].location, v.x, v.y, v.width, v.height)\n                }`,\n        codeUbo: (name: string): string =>\n            `\n                    v = uv.${name};\n\n                    data[offset] = v.x;\n                    data[offset+1] = v.y;\n                    data[offset+2] = v.width;\n                    data[offset+3] = v.height;\n                `\n    },\n    // upload a pixi color as vec4 with caching layer\n    {\n        test: (data: any, uniform: any): boolean =>\n            data.type === 'vec4' && data.size === 1 && !data.isArray && uniform.red !== undefined,\n\n        code: (name: string): string =>\n            `\n                cv = ud[\"${name}\"].value;\n                v = uv[\"${name}\"];\n\n                if(cv[0] !== v.red || cv[1] !== v.green || cv[2] !== v.blue || cv[3] !== v.alpha)\n                {\n                    cv[0] = v.red;\n                    cv[1] = v.green;\n                    cv[2] = v.blue;\n                    cv[3] = v.alpha;\n                    gl.uniform4f(ud[\"${name}\"].location, v.red, v.green, v.blue, v.alpha)\n                }`,\n        codeUbo: (name: string): string =>\n            `\n                    v = uv.${name};\n\n                    data[offset] = v.red;\n                    data[offset+1] = v.green;\n                    data[offset+2] = v.blue;\n                    data[offset+3] = v.alpha;\n                `\n    },\n    // upload a pixi color as a vec3 with caching layer\n    {\n        test: (data: any, uniform: any): boolean =>\n            data.type === 'vec3' && data.size === 1 && !data.isArray && uniform.red !== undefined,\n\n        code: (name: string): string =>\n            `\n                cv = ud[\"${name}\"].value;\n                v = uv[\"${name}\"];\n\n                if(cv[0] !== v.red || cv[1] !== v.green || cv[2] !== v.blue || cv[3] !== v.a)\n                {\n                    cv[0] = v.red;\n                    cv[1] = v.green;\n                    cv[2] = v.blue;\n    \n                    gl.uniform3f(ud[\"${name}\"].location, v.red, v.green, v.blue)\n                }`,\n        codeUbo: (name: string): string =>\n            `\n                    v = uv.${name};\n\n                    data[offset] = v.red;\n                    data[offset+1] = v.green;\n                    data[offset+2] = v.blue;\n                `\n    },\n\n    // a caching layer for vec4 uploading\n    {\n        test: (data: any): boolean =>\n            data.type === 'vec4' && data.size === 1 && !data.isArray,\n        code: (name: string): string =>\n            `\n                cv = ud[\"${name}\"].value;\n                v = uv[\"${name}\"];\n\n                if(cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])\n                {\n                    cv[0] = v[0];\n                    cv[1] = v[1];\n                    cv[2] = v[2];\n                    cv[3] = v[3];\n\n                    gl.uniform4f(ud[\"${name}\"].location, v[0], v[1], v[2], v[3])\n                }`,\n    },\n];\n\n"], "mappings": "AAwBO,MAAMA,cAAA,GAAmC;AAAA;AAG5C;EACIC,IAAA,EAAOC,IAAA,IACHA,IAAA,CAAKC,IAAA,KAAS,WAAWD,IAAA,CAAKE,IAAA,KAAS,KAAK,CAACF,IAAA,CAAKG,OAAA;EACtDC,IAAA,EAAOC,IAAA,IACH;AAAA,qBACSA,IAAI,cAAcA,IAAI;AAAA;AAAA,sBAErBA,IAAI,kBAAkBA,IAAI;AAAA,mCACbA,IAAI,oBAAoBA,IAAI;AAAA;AAAA;AAG3D;AAAA;AAEA;EACIN,IAAA,EAAMA,CAACC,IAAA,EAAWM,OAAA;EAAA;EAAA,CAEbN,IAAA,CAAKC,IAAA,KAAS,eAAeD,IAAA,CAAKC,IAAA,KAAS,iBAAiBD,IAAA,CAAKC,IAAA,KAAS,qBAAqBD,IAAA,CAAKE,IAAA,KAAS,KAAK,CAACF,IAAA,CAAKG,OAAA,KAAYG,OAAA,IAAW,QAAQA,OAAA,CAAQC,iBAAA,KAAsB;EAC1LH,IAAA,EAAOC,IAAA,IAAyB;AAAA;AAAA,wCAEAA,IAAI;AAAA;AAAA,qBAEvBA,IAAI;AAAA;AAAA,sBAEHA,IAAI;AAAA,mCACSA,IAAI;AAAA;AAAA;AAEnC;AAAA;AAEA;EACIN,IAAA,EAAMA,CAACC,IAAA,EAAWM,OAAA,KACdN,IAAA,CAAKC,IAAA,KAAS,UAAUD,IAAA,CAAKE,IAAA,KAAS,KAAK,CAACF,IAAA,CAAKG,OAAA,IAAWG,OAAA,CAAQE,CAAA,KAAM;EAC9EJ,IAAA,EAAOC,IAAA;EAAA;EAGH;AAAA,sCAC0BA,IAAI,2BAA2BA,IAAI;AAAA;EAEjEI,OAAA,EAAUJ,IAAA,IACN;AAAA,sBACUA,IAAI,gBAAgBA,IAAI;AAAA;AAAA,iCAEbA,IAAI;AAAA,mCACFA,IAAI;AAAA,mCACJA,IAAI;AAAA;AAAA,qCAEFA,IAAI;AAAA,qCACJA,IAAI;AAAA,qCACJA,IAAI;AAAA;AAAA,qCAEJA,IAAI;AAAA,qCACJA,IAAI;AAAA,sCACHA,IAAI;AAAA;AAItC;AAAA;AAEA;EACIN,IAAA,EAAMA,CAACC,IAAA,EAAWM,OAAA,KACdN,IAAA,CAAKC,IAAA,KAAS,UAAUD,IAAA,CAAKE,IAAA,KAAS,KAAK,CAACF,IAAA,CAAKG,OAAA,IAAWG,OAAA,CAAQI,CAAA,KAAM;EAC9EN,IAAA,EAAOC,IAAA,IACH;AAAA,2BACeA,IAAI;AAAA,0BACLA,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uCAMSA,IAAI;AAAA;EAEnCI,OAAA,EAAUJ,IAAA,IACN;AAAA,yBACaA,IAAI;AAAA;AAAA;AAAA;AAAA;AAKzB;AAAA;AAEA;EACIN,IAAA,EAAOC,IAAA,IACHA,IAAA,CAAKC,IAAA,KAAS,UAAUD,IAAA,CAAKE,IAAA,KAAS,KAAK,CAACF,IAAA,CAAKG,OAAA;EACrDC,IAAA,EAAOC,IAAA,IACH;AAAA,2BACeA,IAAI;AAAA,0BACLA,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uCAMSA,IAAI;AAAA;AAAA;AAGvC;AAAA;AAEA;EACIN,IAAA,EAAMA,CAACC,IAAA,EAAWM,OAAA,KACdN,IAAA,CAAKC,IAAA,KAAS,UAAUD,IAAA,CAAKE,IAAA,KAAS,KAAK,CAACF,IAAA,CAAKG,OAAA,IAAWG,OAAA,CAAQK,KAAA,KAAU;EAElFP,IAAA,EAAOC,IAAA,IACH;AAAA,2BACeA,IAAI;AAAA,0BACLA,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uCAQSA,IAAI;AAAA;EAEnCI,OAAA,EAAUJ,IAAA,IACN;AAAA,6BACiBA,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAO7B;AAAA;AAEA;EACIN,IAAA,EAAMA,CAACC,IAAA,EAAWM,OAAA,KACdN,IAAA,CAAKC,IAAA,KAAS,UAAUD,IAAA,CAAKE,IAAA,KAAS,KAAK,CAACF,IAAA,CAAKG,OAAA,IAAWG,OAAA,CAAQM,GAAA,KAAQ;EAEhFR,IAAA,EAAOC,IAAA,IACH;AAAA,2BACeA,IAAI;AAAA,0BACLA,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uCAQSA,IAAI;AAAA;EAEnCI,OAAA,EAAUJ,IAAA,IACN;AAAA,6BACiBA,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAO7B;AAAA;AAEA;EACIN,IAAA,EAAMA,CAACC,IAAA,EAAWM,OAAA,KACdN,IAAA,CAAKC,IAAA,KAAS,UAAUD,IAAA,CAAKE,IAAA,KAAS,KAAK,CAACF,IAAA,CAAKG,OAAA,IAAWG,OAAA,CAAQM,GAAA,KAAQ;EAEhFR,IAAA,EAAOC,IAAA,IACH;AAAA,2BACeA,IAAI;AAAA,0BACLA,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uCAQSA,IAAI;AAAA;EAEnCI,OAAA,EAAUJ,IAAA,IACN;AAAA,6BACiBA,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAM7B;AAAA;AAGA;EACIN,IAAA,EAAOC,IAAA,IACHA,IAAA,CAAKC,IAAA,KAAS,UAAUD,IAAA,CAAKE,IAAA,KAAS,KAAK,CAACF,IAAA,CAAKG,OAAA;EACrDC,IAAA,EAAOC,IAAA,IACH;AAAA,2BACeA,IAAI;AAAA,0BACLA,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uCASSA,IAAI;AAAA;AAEvC,EACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}