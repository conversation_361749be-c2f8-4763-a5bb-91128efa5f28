{"ast": null, "code": "import { <PERSON><PERSON><PERSON>_MODES } from \"@pixi/constants\";\nconst BLEND = 0,\n  OFFSET = 1,\n  CULLING = 2,\n  DEPTH_TEST = 3,\n  WINDING = 4,\n  DEPTH_MASK = 5;\nclass State {\n  constructor() {\n    this.data = 0, this.blendMode = BLEND_MODES.NORMAL, this.polygonOffset = 0, this.blend = !0, this.depthMask = !0;\n  }\n  /**\n   * Activates blending of the computed fragment color values.\n   * @default true\n   */\n  get blend() {\n    return !!(this.data & 1 << BLEND);\n  }\n  set blend(value) {\n    !!(this.data & 1 << BLEND) !== value && (this.data ^= 1 << BLEND);\n  }\n  /**\n   * Activates adding an offset to depth values of polygon's fragments\n   * @default false\n   */\n  get offsets() {\n    return !!(this.data & 1 << OFFSET);\n  }\n  set offsets(value) {\n    !!(this.data & 1 << OFFSET) !== value && (this.data ^= 1 << OFFSET);\n  }\n  /**\n   * Activates culling of polygons.\n   * @default false\n   */\n  get culling() {\n    return !!(this.data & 1 << CULLING);\n  }\n  set culling(value) {\n    !!(this.data & 1 << CULLING) !== value && (this.data ^= 1 << CULLING);\n  }\n  /**\n   * Activates depth comparisons and updates to the depth buffer.\n   * @default false\n   */\n  get depthTest() {\n    return !!(this.data & 1 << DEPTH_TEST);\n  }\n  set depthTest(value) {\n    !!(this.data & 1 << DEPTH_TEST) !== value && (this.data ^= 1 << DEPTH_TEST);\n  }\n  /**\n   * Enables or disables writing to the depth buffer.\n   * @default true\n   */\n  get depthMask() {\n    return !!(this.data & 1 << DEPTH_MASK);\n  }\n  set depthMask(value) {\n    !!(this.data & 1 << DEPTH_MASK) !== value && (this.data ^= 1 << DEPTH_MASK);\n  }\n  /**\n   * Specifies whether or not front or back-facing polygons can be culled.\n   * @default false\n   */\n  get clockwiseFrontFace() {\n    return !!(this.data & 1 << WINDING);\n  }\n  set clockwiseFrontFace(value) {\n    !!(this.data & 1 << WINDING) !== value && (this.data ^= 1 << WINDING);\n  }\n  /**\n   * The blend mode to be applied when this state is set. Apply a value of `PIXI.BLEND_MODES.NORMAL` to reset the blend mode.\n   * Setting this mode to anything other than NO_BLEND will automatically switch blending on.\n   * @default PIXI.BLEND_MODES.NORMAL\n   */\n  get blendMode() {\n    return this._blendMode;\n  }\n  set blendMode(value) {\n    this.blend = value !== BLEND_MODES.NONE, this._blendMode = value;\n  }\n  /**\n   * The polygon offset. Setting this property to anything other than 0 will automatically enable polygon offset fill.\n   * @default 0\n   */\n  get polygonOffset() {\n    return this._polygonOffset;\n  }\n  set polygonOffset(value) {\n    this.offsets = !!value, this._polygonOffset = value;\n  }\n  static for2d() {\n    const state = new State();\n    return state.depthTest = !1, state.blend = !0, state;\n  }\n}\nState.prototype.toString = function () {\n  return `[@pixi/core:State blendMode=${this.blendMode} clockwiseFrontFace=${this.clockwiseFrontFace} culling=${this.culling} depthMask=${this.depthMask} polygonOffset=${this.polygonOffset}]`;\n};\nexport { State };", "map": {"version": 3, "names": ["BLEND", "OFFSET", "CULLING", "DEPTH_TEST", "WINDING", "DEPTH_MASK", "State", "constructor", "data", "blendMode", "BLEND_MODES", "NORMAL", "polygonOffset", "blend", "depthMask", "value", "offsets", "culling", "depthTest", "clockwiseFrontFace", "_blendMode", "NONE", "_polygonOffset", "for2d", "state", "prototype", "toString"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\state\\State.ts"], "sourcesContent": ["import { BLEND_MODES } from '@pixi/constants';\n\n/* eslint-disable max-len */\n\nconst BLEND = 0;\nconst OFFSET = 1;\nconst CULLING = 2;\nconst DEPTH_TEST = 3;\nconst WINDING = 4;\nconst DEPTH_MASK = 5;\n\n/**\n * This is a WebGL state, and is is passed to {@link PIXI.StateSystem}.\n *\n * Each mesh rendered may require WebGL to be in a different state.\n * For example you may want different blend mode or to enable polygon offsets\n * @memberof PIXI\n */\nexport class State\n{\n    data: number;\n    _blendMode: BLEND_MODES;\n    _polygonOffset: number;\n\n    constructor()\n    {\n        this.data = 0;\n\n        this.blendMode = BLEND_MODES.NORMAL;\n        this.polygonOffset = 0;\n\n        this.blend = true;\n        this.depthMask = true;\n        //  this.depthTest = true;\n    }\n\n    /**\n     * Activates blending of the computed fragment color values.\n     * @default true\n     */\n    get blend(): boolean\n    {\n        return !!(this.data & (1 << BLEND));\n    }\n\n    set blend(value: boolean)\n    {\n        if (!!(this.data & (1 << BLEND)) !== value)\n        {\n            this.data ^= (1 << BLEND);\n        }\n    }\n\n    /**\n     * Activates adding an offset to depth values of polygon's fragments\n     * @default false\n     */\n    get offsets(): boolean\n    {\n        return !!(this.data & (1 << OFFSET));\n    }\n\n    set offsets(value: boolean)\n    {\n        if (!!(this.data & (1 << OFFSET)) !== value)\n        {\n            this.data ^= (1 << OFFSET);\n        }\n    }\n\n    /**\n     * Activates culling of polygons.\n     * @default false\n     */\n    get culling(): boolean\n    {\n        return !!(this.data & (1 << CULLING));\n    }\n\n    set culling(value: boolean)\n    {\n        if (!!(this.data & (1 << CULLING)) !== value)\n        {\n            this.data ^= (1 << CULLING);\n        }\n    }\n\n    /**\n     * Activates depth comparisons and updates to the depth buffer.\n     * @default false\n     */\n    get depthTest(): boolean\n    {\n        return !!(this.data & (1 << DEPTH_TEST));\n    }\n\n    set depthTest(value: boolean)\n    {\n        if (!!(this.data & (1 << DEPTH_TEST)) !== value)\n        {\n            this.data ^= (1 << DEPTH_TEST);\n        }\n    }\n\n    /**\n     * Enables or disables writing to the depth buffer.\n     * @default true\n     */\n    get depthMask(): boolean\n    {\n        return !!(this.data & (1 << DEPTH_MASK));\n    }\n\n    set depthMask(value: boolean)\n    {\n        if (!!(this.data & (1 << DEPTH_MASK)) !== value)\n        {\n            this.data ^= (1 << DEPTH_MASK);\n        }\n    }\n\n    /**\n     * Specifies whether or not front or back-facing polygons can be culled.\n     * @default false\n     */\n    get clockwiseFrontFace(): boolean\n    {\n        return !!(this.data & (1 << WINDING));\n    }\n\n    set clockwiseFrontFace(value: boolean)\n    {\n        if (!!(this.data & (1 << WINDING)) !== value)\n        {\n            this.data ^= (1 << WINDING);\n        }\n    }\n\n    /**\n     * The blend mode to be applied when this state is set. Apply a value of `PIXI.BLEND_MODES.NORMAL` to reset the blend mode.\n     * Setting this mode to anything other than NO_BLEND will automatically switch blending on.\n     * @default PIXI.BLEND_MODES.NORMAL\n     */\n    get blendMode(): BLEND_MODES\n    {\n        return this._blendMode;\n    }\n\n    set blendMode(value: BLEND_MODES)\n    {\n        this.blend = (value !== BLEND_MODES.NONE);\n        this._blendMode = value;\n    }\n\n    /**\n     * The polygon offset. Setting this property to anything other than 0 will automatically enable polygon offset fill.\n     * @default 0\n     */\n    get polygonOffset(): number\n    {\n        return this._polygonOffset;\n    }\n\n    set polygonOffset(value: number)\n    {\n        this.offsets = !!value;\n        this._polygonOffset = value;\n    }\n\n    static for2d(): State\n    {\n        const state = new State();\n\n        state.depthTest = false;\n        state.blend = true;\n\n        return state;\n    }\n}\n\nif (process.env.DEBUG)\n{\n    State.prototype.toString = function toString(): string\n    {\n        return `[@pixi/core:State `\n            + `blendMode=${this.blendMode} `\n            + `clockwiseFrontFace=${this.clockwiseFrontFace} `\n            + `culling=${this.culling} `\n            + `depthMask=${this.depthMask} `\n            + `polygonOffset=${this.polygonOffset}`\n            + `]`;\n    };\n}\n\n"], "mappings": ";AAIA,MAAMA,KAAA,GAAQ;EACRC,MAAA,GAAS;EACTC,OAAA,GAAU;EACVC,UAAA,GAAa;EACbC,OAAA,GAAU;EACVC,UAAA,GAAa;AASZ,MAAMC,KAAA,CACb;EAKIC,YAAA,EACA;IACI,KAAKC,IAAA,GAAO,GAEZ,KAAKC,SAAA,GAAYC,WAAA,CAAYC,MAAA,EAC7B,KAAKC,aAAA,GAAgB,GAErB,KAAKC,KAAA,GAAQ,IACb,KAAKC,SAAA,GAAY;EAErB;EAAA;AAAA;AAAA;AAAA;EAMA,IAAID,MAAA,EACJ;IACI,OAAO,CAAC,EAAE,KAAKL,IAAA,GAAQ,KAAKR,KAAA;EAChC;EAEA,IAAIa,MAAME,KAAA,EACV;IACQ,CAAC,EAAE,KAAKP,IAAA,GAAQ,KAAKR,KAAA,MAAYe,KAAA,KAEjC,KAAKP,IAAA,IAAS,KAAKR,KAAA;EAE3B;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIgB,QAAA,EACJ;IACI,OAAO,CAAC,EAAE,KAAKR,IAAA,GAAQ,KAAKP,MAAA;EAChC;EAEA,IAAIe,QAAQD,KAAA,EACZ;IACQ,CAAC,EAAE,KAAKP,IAAA,GAAQ,KAAKP,MAAA,MAAac,KAAA,KAElC,KAAKP,IAAA,IAAS,KAAKP,MAAA;EAE3B;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIgB,QAAA,EACJ;IACI,OAAO,CAAC,EAAE,KAAKT,IAAA,GAAQ,KAAKN,OAAA;EAChC;EAEA,IAAIe,QAAQF,KAAA,EACZ;IACQ,CAAC,EAAE,KAAKP,IAAA,GAAQ,KAAKN,OAAA,MAAca,KAAA,KAEnC,KAAKP,IAAA,IAAS,KAAKN,OAAA;EAE3B;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIgB,UAAA,EACJ;IACI,OAAO,CAAC,EAAE,KAAKV,IAAA,GAAQ,KAAKL,UAAA;EAChC;EAEA,IAAIe,UAAUH,KAAA,EACd;IACQ,CAAC,EAAE,KAAKP,IAAA,GAAQ,KAAKL,UAAA,MAAiBY,KAAA,KAEtC,KAAKP,IAAA,IAAS,KAAKL,UAAA;EAE3B;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIW,UAAA,EACJ;IACI,OAAO,CAAC,EAAE,KAAKN,IAAA,GAAQ,KAAKH,UAAA;EAChC;EAEA,IAAIS,UAAUC,KAAA,EACd;IACQ,CAAC,EAAE,KAAKP,IAAA,GAAQ,KAAKH,UAAA,MAAiBU,KAAA,KAEtC,KAAKP,IAAA,IAAS,KAAKH,UAAA;EAE3B;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIc,mBAAA,EACJ;IACI,OAAO,CAAC,EAAE,KAAKX,IAAA,GAAQ,KAAKJ,OAAA;EAChC;EAEA,IAAIe,mBAAmBJ,KAAA,EACvB;IACQ,CAAC,EAAE,KAAKP,IAAA,GAAQ,KAAKJ,OAAA,MAAcW,KAAA,KAEnC,KAAKP,IAAA,IAAS,KAAKJ,OAAA;EAE3B;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,IAAIK,UAAA,EACJ;IACI,OAAO,KAAKW,UAAA;EAChB;EAEA,IAAIX,UAAUM,KAAA,EACd;IACI,KAAKF,KAAA,GAASE,KAAA,KAAUL,WAAA,CAAYW,IAAA,EACpC,KAAKD,UAAA,GAAaL,KAAA;EACtB;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIH,cAAA,EACJ;IACI,OAAO,KAAKU,cAAA;EAChB;EAEA,IAAIV,cAAcG,KAAA,EAClB;IACI,KAAKC,OAAA,GAAU,CAAC,CAACD,KAAA,EACjB,KAAKO,cAAA,GAAiBP,KAAA;EAC1B;EAEA,OAAOQ,MAAA,EACP;IACU,MAAAC,KAAA,GAAQ,IAAIlB,KAAA;IAElB,OAAAkB,KAAA,CAAMN,SAAA,GAAY,IAClBM,KAAA,CAAMX,KAAA,GAAQ,IAEPW,KAAA;EACX;AACJ;AAIIlB,KAAA,CAAMmB,SAAA,CAAUC,QAAA,GAAW,YAC3B;EACI,OAAO,+BACY,KAAKjB,SAAS,uBACL,KAAKU,kBAAkB,YAClC,KAAKF,OAAO,cACV,KAAKH,SAAS,kBACV,KAAKF,aAAa;AAE7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}