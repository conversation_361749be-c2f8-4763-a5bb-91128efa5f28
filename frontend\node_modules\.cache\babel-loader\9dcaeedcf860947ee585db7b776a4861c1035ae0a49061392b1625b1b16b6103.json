{"ast": null, "code": "const BrowserAdapter = {\n  /**\n   * Creates a canvas element of the given size.\n   * This canvas is created using the browser's native canvas element.\n   * @param width - width of the canvas\n   * @param height - height of the canvas\n   */\n  createCanvas: (width, height) => {\n    const canvas = document.createElement(\"canvas\");\n    return canvas.width = width, canvas.height = height, canvas;\n  },\n  getCanvasRenderingContext2D: () => CanvasRenderingContext2D,\n  getWebGLRenderingContext: () => WebGLRenderingContext,\n  getNavigator: () => navigator,\n  getBaseUrl: () => document.baseURI ?? window.location.href,\n  getFontFaceSet: () => document.fonts,\n  fetch: (url, options) => fetch(url, options),\n  parseXML: xml => new DOMParser().parseFromString(xml, \"text/xml\")\n};\nexport { BrowserAdapter };", "map": {"version": 3, "names": ["BrowserAdapter", "createCanvas", "width", "height", "canvas", "document", "createElement", "getCanvasRenderingContext2D", "CanvasRenderingContext2D", "getWebGLRenderingContext", "WebGLRenderingContext", "getNavigator", "navigator", "getBaseUrl", "baseURI", "window", "location", "href", "getFontFaceSet", "fonts", "fetch", "url", "options", "parseXML", "xml", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\settings\\src\\adapter.ts"], "sourcesContent": ["import type { ICanvas } from './ICanvas';\nimport type { ICanvasRenderingContext2D } from './ICanvasRenderingContext2D';\n\n/**\n * This interface describes all the DOM dependent calls that <PERSON><PERSON> makes throughout its codebase.\n * Implementations of this interface can be used to make sure <PERSON><PERSON> will work in any environment,\n * such as browser, Web Workers, and Node.js.\n * @memberof PIXI\n */\nexport interface IAdapter\n{\n    /** Returns a canvas object that can be used to create a webgl context. */\n    createCanvas: (width?: number, height?: number) => ICanvas;\n    /** Returns a 2D rendering context. */\n    getCanvasRenderingContext2D: () => { prototype: ICanvasRenderingContext2D; };\n    /** Returns a WebGL rendering context. */\n    getWebGLRenderingContext: () => typeof WebGLRenderingContext;\n    /** Returns a partial implementation of the browsers window.navigator */\n    getNavigator: () => { userAgent: string };\n    /** Returns the current base URL For browser environments this is either the document.baseURI or window.location.href */\n    getBaseUrl: () => string;\n    getFontFaceSet: () => FontFaceSet | null;\n    fetch: (url: RequestInfo, options?: RequestInit) => Promise<Response>;\n    parseXML: (xml: string) => Document;\n}\n\nexport const BrowserAdapter = {\n    /**\n     * Creates a canvas element of the given size.\n     * This canvas is created using the browser's native canvas element.\n     * @param width - width of the canvas\n     * @param height - height of the canvas\n     */\n    createCanvas: (width: number, height: number): HTMLCanvasElement =>\n    {\n        const canvas = document.createElement('canvas');\n\n        canvas.width = width;\n        canvas.height = height;\n\n        return canvas;\n    },\n    getCanvasRenderingContext2D: () => CanvasRenderingContext2D,\n    getWebGLRenderingContext: () => WebGLRenderingContext,\n    getNavigator: () => navigator,\n    getBaseUrl: () => (document.baseURI ?? window.location.href),\n    getFontFaceSet: () => document.fonts,\n    fetch: (url: RequestInfo, options?: RequestInit) => fetch(url, options),\n    parseXML: (xml: string) =>\n    {\n        const parser = new DOMParser();\n\n        return parser.parseFromString(xml, 'text/xml');\n    },\n} as IAdapter;\n"], "mappings": "AA0BO,MAAMA,cAAA,GAAiB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAO1BC,YAAA,EAAcA,CAACC,KAAA,EAAeC,MAAA,KAC9B;IACU,MAAAC,MAAA,GAASC,QAAA,CAASC,aAAA,CAAc,QAAQ;IAE9C,OAAAF,MAAA,CAAOF,KAAA,GAAQA,KAAA,EACfE,MAAA,CAAOD,MAAA,GAASA,MAAA,EAETC,MAAA;EACX;EACAG,2BAAA,EAA6BA,CAAA,KAAMC,wBAAA;EACnCC,wBAAA,EAA0BA,CAAA,KAAMC,qBAAA;EAChCC,YAAA,EAAcA,CAAA,KAAMC,SAAA;EACpBC,UAAA,EAAYA,CAAA,KAAOR,QAAA,CAASS,OAAA,IAAWC,MAAA,CAAOC,QAAA,CAASC,IAAA;EACvDC,cAAA,EAAgBA,CAAA,KAAMb,QAAA,CAASc,KAAA;EAC/BC,KAAA,EAAOA,CAACC,GAAA,EAAkBC,OAAA,KAA0BF,KAAA,CAAMC,GAAA,EAAKC,OAAO;EACtEC,QAAA,EAAWC,GAAA,IAEQ,IAAIC,SAAA,CAEL,EAAAC,eAAA,CAAgBF,GAAA,EAAK,UAAU;AAErD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}