{"ast": null, "code": "class IGLUniformData {}\nclass GLProgram {\n  /**\n   * Makes a new Pixi program.\n   * @param program - webgl program\n   * @param uniformData - uniforms\n   */\n  constructor(program, uniformData) {\n    this.program = program, this.uniformData = uniformData, this.uniformGroups = {}, this.uniformDirtyGroups = {}, this.uniformBufferBindings = {};\n  }\n  /** Destroys this program. */\n  destroy() {\n    this.uniformData = null, this.uniformGroups = null, this.uniformDirtyGroups = null, this.uniformBufferBindings = null, this.program = null;\n  }\n}\nexport { GLProgram, IGLUniformData };", "map": {"version": 3, "names": ["IGLUniformData", "GLProgram", "constructor", "program", "uniformData", "uniformGroups", "uniformDirtyGroups", "uniformBufferBindings", "destroy"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\shader\\GLProgram.ts"], "sourcesContent": ["import type { Dict } from '@pixi/utils';\n\n/**\n * @private\n */\nexport class IGLUniformData\n{\n    location: WebGLUniformLocation;\n    value: number | boolean | Float32Array | Int32Array | Uint32Array | boolean[];\n}\n\n/**\n * Helper class to create a WebGL Program\n * @memberof PIXI\n */\nexport class GLProgram\n{\n    /** The shader program. */\n    public program: WebGLProgram;\n\n    /**\n     * Holds the uniform data which contains uniform locations\n     * and current uniform values used for caching and preventing unneeded GPU commands.\n     */\n    public uniformData: Dict<any>;\n\n    /**\n     * UniformGroups holds the various upload functions for the shader. Each uniform group\n     * and program have a unique upload function generated.\n     */\n    public uniformGroups: Dict<any>;\n\n    /** A hash that stores where UBOs are bound to on the program. */\n    public uniformBufferBindings: Dict<any>;\n\n    /** A hash for lazily-generated uniform uploading functions. */\n    public uniformSync: Dict<any>;\n\n    /**\n     * A place where dirty ticks are stored for groups\n     * If a tick here does not match with the Higher level Programs tick, it means\n     * we should re upload the data.\n     */\n    public uniformDirtyGroups: Dict<any>;\n\n    /**\n     * Makes a new Pixi program.\n     * @param program - webgl program\n     * @param uniformData - uniforms\n     */\n    constructor(program: WebGLProgram, uniformData: {[key: string]: IGLUniformData})\n    {\n        this.program = program;\n        this.uniformData = uniformData;\n        this.uniformGroups = {};\n        this.uniformDirtyGroups = {};\n        this.uniformBufferBindings = {};\n    }\n\n    /** Destroys this program. */\n    destroy(): void\n    {\n        this.uniformData = null;\n        this.uniformGroups = null;\n        this.uniformDirtyGroups = null;\n        this.uniformBufferBindings = null;\n        this.program = null;\n    }\n}\n"], "mappings": "AAKO,MAAMA,cAAA,CACb;AASO,MAAMC,SAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;EAkCIC,YAAYC,OAAA,EAAuBC,WAAA,EACnC;IACI,KAAKD,OAAA,GAAUA,OAAA,EACf,KAAKC,WAAA,GAAcA,WAAA,EACnB,KAAKC,aAAA,GAAgB,CAAC,GACtB,KAAKC,kBAAA,GAAqB,CAC1B,QAAKC,qBAAA,GAAwB;EACjC;EAAA;EAGAC,QAAA,EACA;IACI,KAAKJ,WAAA,GAAc,MACnB,KAAKC,aAAA,GAAgB,MACrB,KAAKC,kBAAA,GAAqB,MAC1B,KAAKC,qBAAA,GAAwB,MAC7B,KAAKJ,OAAA,GAAU;EACnB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}