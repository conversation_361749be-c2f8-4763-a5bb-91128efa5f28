{"ast": null, "code": "import { ExtensionType, extensions } from \"@pixi/extensions\";\nimport { deprecation } from \"@pixi/utils\";\nclass PluginSystem {\n  constructor(renderer) {\n    this.renderer = renderer, this.plugins = {}, Object.defineProperties(this.plugins, {\n      extract: {\n        enumerable: !1,\n        get() {\n          return deprecation(\"7.0.0\", \"renderer.plugins.extract has moved to renderer.extract\"), renderer.extract;\n        }\n      },\n      prepare: {\n        enumerable: !1,\n        get() {\n          return deprecation(\"7.0.0\", \"renderer.plugins.prepare has moved to renderer.prepare\"), renderer.prepare;\n        }\n      },\n      interaction: {\n        enumerable: !1,\n        get() {\n          return deprecation(\"7.0.0\", \"renderer.plugins.interaction has been deprecated, use renderer.events\"), renderer.events;\n        }\n      }\n    });\n  }\n  /**\n   * Initialize the plugins.\n   * @protected\n   */\n  init() {\n    const staticMap = this.rendererPlugins;\n    for (const o in staticMap) this.plugins[o] = new staticMap[o](this.renderer);\n  }\n  destroy() {\n    for (const o in this.plugins) this.plugins[o].destroy(), this.plugins[o] = null;\n  }\n}\nPluginSystem.extension = {\n  type: [ExtensionType.RendererSystem, ExtensionType.CanvasRendererSystem],\n  name: \"_plugin\"\n};\nextensions.add(PluginSystem);\nexport { PluginSystem };", "map": {"version": 3, "names": ["PluginSystem", "constructor", "renderer", "plugins", "Object", "defineProperties", "extract", "enumerable", "get", "deprecation", "prepare", "interaction", "events", "init", "staticMap", "rendererPlugins", "o", "destroy", "extension", "type", "ExtensionType", "RendererSystem", "CanvasRendererSystem", "name", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\plugin\\PluginSystem.ts"], "sourcesContent": ["import { extensions, ExtensionType } from '@pixi/extensions';\nimport { deprecation } from '@pixi/utils';\n\nimport type { ExtensionMetadata } from '@pixi/extensions';\nimport type { IRenderer } from '../IRenderer';\nimport type { ISystem } from '../system/ISystem';\n\nexport interface IRendererPlugins extends GlobalMixins.IRendererPlugins\n{\n    [key: string]: any;\n}\n\n/**\n * Manages the functionality that allows users to extend pixi functionality via additional plugins.\n * @memberof PIXI\n */\nexport class PluginSystem implements ISystem\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        type: [\n            ExtensionType.RendererSystem,\n            ExtensionType.CanvasRendererSystem\n        ],\n        name: '_plugin',\n    };\n\n    /** @ignore */\n    public rendererPlugins: IRendererPlugins;\n\n    /**\n     * Collection of plugins.\n     * @readonly\n     * @member {object}\n     */\n    public readonly plugins: IRendererPlugins;\n    private renderer: IRenderer;\n\n    constructor(renderer: IRenderer)\n    {\n        this.renderer = renderer;\n\n        /**\n         * Collection of plugins.\n         * @readonly\n         * @member {object}\n         */\n        this.plugins = {};\n\n        if (process.env.DEBUG)\n        {\n            Object.defineProperties(this.plugins, {\n                extract: {\n                    enumerable: false,\n                    get()\n                    {\n                        deprecation('7.0.0', 'renderer.plugins.extract has moved to renderer.extract');\n\n                        return (renderer as any).extract;\n                    },\n                },\n                prepare: {\n                    enumerable: false,\n                    get()\n                    {\n                        deprecation('7.0.0', 'renderer.plugins.prepare has moved to renderer.prepare');\n\n                        return (renderer as any).prepare;\n                    },\n                },\n                interaction: {\n                    enumerable: false,\n                    get()\n                    {\n                        deprecation('7.0.0', 'renderer.plugins.interaction has been deprecated, use renderer.events');\n\n                        return (renderer as any).events;\n                    },\n                },\n            });\n        }\n    }\n\n    /**\n     * Initialize the plugins.\n     * @protected\n     */\n    init(): void\n    {\n        const staticMap = this.rendererPlugins;\n\n        for (const o in staticMap)\n        {\n            this.plugins[o] = new (staticMap[o])(this.renderer);\n        }\n    }\n\n    destroy(): void\n    {\n        for (const o in this.plugins)\n        {\n            this.plugins[o].destroy();\n            this.plugins[o] = null;\n        }\n    }\n}\n\nextensions.add(PluginSystem);\n"], "mappings": ";;AAgBO,MAAMA,YAAA,CACb;EAqBIC,YAAYC,QAAA,EACZ;IACS,KAAAA,QAAA,GAAWA,QAAA,EAOhB,KAAKC,OAAA,GAAU,CAIX,GAAAC,MAAA,CAAOC,gBAAA,CAAiB,KAAKF,OAAA,EAAS;MAClCG,OAAA,EAAS;QACLC,UAAA,EAAY;QACZC,IAAA,EACA;UACgB,OAAAC,WAAA,UAAS,wDAAwD,GAErEP,QAAA,CAAiBI,OAAA;QAC7B;MACJ;MACAI,OAAA,EAAS;QACLH,UAAA,EAAY;QACZC,IAAA,EACA;UACgB,OAAAC,WAAA,UAAS,wDAAwD,GAErEP,QAAA,CAAiBQ,OAAA;QAC7B;MACJ;MACAC,WAAA,EAAa;QACTJ,UAAA,EAAY;QACZC,IAAA,EACA;UACgB,OAAAC,WAAA,UAAS,uEAAuE,GAEpFP,QAAA,CAAiBU,MAAA;QAC7B;MACJ;IAAA,CACH;EAET;EAAA;AAAA;AAAA;AAAA;EAMAC,KAAA,EACA;IACI,MAAMC,SAAA,GAAY,KAAKC,eAAA;IAEvB,WAAWC,CAAA,IAAKF,SAAA,EAEP,KAAAX,OAAA,CAAQa,CAAC,IAAI,IAAKF,SAAA,CAAUE,CAAC,EAAG,KAAKd,QAAQ;EAE1D;EAEAe,QAAA,EACA;IACI,WAAWD,CAAA,IAAK,KAAKb,OAAA,EAEZ,KAAAA,OAAA,CAAQa,CAAC,EAAEC,OAAA,IAChB,KAAKd,OAAA,CAAQa,CAAC,IAAI;EAE1B;AACJ;AAzFahB,YAAA,CAGFkB,SAAA,GAA+B;EAClCC,IAAA,EAAM,CACFC,aAAA,CAAcC,cAAA,EACdD,aAAA,CAAcE,oBAAA,CAClB;EACAC,IAAA,EAAM;AACV;AAkFJC,UAAA,CAAWC,GAAA,CAAIzB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}