{"ast": null, "code": "import { RENDERER_TYPE } from \"@pixi/constants\";\nimport { ExtensionType, extensions } from \"@pixi/extensions\";\nimport { Matrix } from \"@pixi/math\";\nimport { settings } from \"@pixi/settings\";\nimport { deprecation, isWebGLSupported } from \"@pixi/utils\";\nimport { UniformGroup } from \"./shader/UniformGroup.mjs\";\nimport { SystemManager } from \"./system/SystemManager.mjs\";\nconst _Renderer = class _Renderer2 extends SystemManager {\n  /**\n   * @param {PIXI.IRendererOptions} [options] - See {@link PIXI.settings.RENDER_OPTIONS} for defaults.\n   */\n  constructor(options) {\n    super(), this.type = RENDERER_TYPE.WEBGL, options = Object.assign({}, settings.RENDER_OPTIONS, options), this.gl = null, this.CONTEXT_UID = 0, this.globalUniforms = new UniformGroup({\n      projectionMatrix: new Matrix()\n    }, !0);\n    const systemConfig = {\n      runners: [\"init\", \"destroy\", \"contextChange\", \"resolutionChange\", \"reset\", \"update\", \"postrender\", \"prerender\", \"resize\"],\n      systems: _Renderer2.__systems,\n      priority: [\"_view\", \"textureGenerator\", \"background\", \"_plugin\", \"startup\",\n      // low level WebGL systems\n      \"context\", \"state\", \"texture\", \"buffer\", \"geometry\", \"framebuffer\", \"transformFeedback\",\n      // high level pixi specific rendering\n      \"mask\", \"scissor\", \"stencil\", \"projection\", \"textureGC\", \"filter\", \"renderTexture\", \"batch\", \"objectRenderer\", \"_multisample\"]\n    };\n    this.setup(systemConfig), \"useContextAlpha\" in options && (deprecation(\"7.0.0\", \"options.useContextAlpha is deprecated, use options.premultipliedAlpha and options.backgroundAlpha instead\"), options.premultipliedAlpha = options.useContextAlpha && options.useContextAlpha !== \"notMultiplied\", options.backgroundAlpha = options.useContextAlpha === !1 ? 1 : options.backgroundAlpha), this._plugin.rendererPlugins = _Renderer2.__plugins, this.options = options, this.startup.run(this.options);\n  }\n  /**\n   * Create renderer if WebGL is available. Overrideable\n   * by the **@pixi/canvas-renderer** package to allow fallback.\n   * throws error if WebGL is not available.\n   * @param options\n   * @private\n   */\n  static test(options) {\n    return options?.forceCanvas ? !1 : isWebGLSupported();\n  }\n  /**\n   * Renders the object to its WebGL view.\n   * @param displayObject - The object to be rendered.\n   * @param {object} [options] - Object to use for render options.\n   * @param {PIXI.RenderTexture} [options.renderTexture] - The render texture to render to.\n   * @param {boolean} [options.clear=true] - Should the canvas be cleared before the new render.\n   * @param {PIXI.Matrix} [options.transform] - A transform to apply to the render texture before rendering.\n   * @param {boolean} [options.skipUpdateTransform=false] - Should we skip the update transform pass?\n   */\n  render(displayObject, options) {\n    this.objectRenderer.render(displayObject, options);\n  }\n  /**\n   * Resizes the WebGL view to the specified width and height.\n   * @param desiredScreenWidth - The desired width of the screen.\n   * @param desiredScreenHeight - The desired height of the screen.\n   */\n  resize(desiredScreenWidth, desiredScreenHeight) {\n    this._view.resizeView(desiredScreenWidth, desiredScreenHeight);\n  }\n  /**\n   * Resets the WebGL state so you can render things however you fancy!\n   * @returns Returns itself.\n   */\n  reset() {\n    return this.runners.reset.emit(), this;\n  }\n  /** Clear the frame buffer. */\n  clear() {\n    this.renderTexture.bind(), this.renderTexture.clear();\n  }\n  /**\n   * Removes everything from the renderer (event listeners, spritebatch, etc...)\n   * @param [removeView=false] - Removes the Canvas element from the DOM.\n   *  See: https://github.com/pixijs/pixijs/issues/2233\n   */\n  destroy(removeView = !1) {\n    this.runners.destroy.items.reverse(), this.emitWithCustomOptions(this.runners.destroy, {\n      _view: removeView\n    }), super.destroy();\n  }\n  /** Collection of plugins */\n  get plugins() {\n    return this._plugin.plugins;\n  }\n  /** The number of msaa samples of the canvas. */\n  get multisample() {\n    return this._multisample.multisample;\n  }\n  /**\n   * Same as view.width, actual number of pixels in the canvas by horizontal.\n   * @member {number}\n   * @readonly\n   * @default 800\n   */\n  get width() {\n    return this._view.element.width;\n  }\n  /**\n   * Same as view.height, actual number of pixels in the canvas by vertical.\n   * @default 600\n   */\n  get height() {\n    return this._view.element.height;\n  }\n  /** The resolution / device pixel ratio of the renderer. */\n  get resolution() {\n    return this._view.resolution;\n  }\n  set resolution(value) {\n    this._view.resolution = value, this.runners.resolutionChange.emit(value);\n  }\n  /** Whether CSS dimensions of canvas view should be resized to screen dimensions automatically. */\n  get autoDensity() {\n    return this._view.autoDensity;\n  }\n  /** The canvas element that everything is drawn to.*/\n  get view() {\n    return this._view.element;\n  }\n  /**\n   * Measurements of the screen. (0, 0, screenWidth, screenHeight).\n   *\n   * Its safe to use as filterArea or hitArea for the whole stage.\n   * @member {PIXI.Rectangle}\n   */\n  get screen() {\n    return this._view.screen;\n  }\n  /** the last object rendered by the renderer. Useful for other plugins like interaction managers */\n  get lastObjectRendered() {\n    return this.objectRenderer.lastObjectRendered;\n  }\n  /** Flag if we are rendering to the screen vs renderTexture */\n  get renderingToScreen() {\n    return this.objectRenderer.renderingToScreen;\n  }\n  /** When logging Pixi to the console, this is the name we will show */\n  get rendererLogId() {\n    return `WebGL ${this.context.webGLVersion}`;\n  }\n  /**\n   * This sets weather the screen is totally cleared between each frame withthe background color and alpha\n   * @deprecated since 7.0.0\n   */\n  get clearBeforeRender() {\n    return deprecation(\"7.0.0\", \"renderer.clearBeforeRender has been deprecated, please use renderer.background.clearBeforeRender instead.\"), this.background.clearBeforeRender;\n  }\n  /**\n   * Pass-thru setting for the canvas' context `alpha` property. This is typically\n   * not something you need to fiddle with. If you want transparency, use `backgroundAlpha`.\n   * @deprecated since 7.0.0\n   * @member {boolean}\n   */\n  get useContextAlpha() {\n    return deprecation(\"7.0.0\", \"renderer.useContextAlpha has been deprecated, please use renderer.context.premultipliedAlpha instead.\"), this.context.useContextAlpha;\n  }\n  /**\n   * readonly drawing buffer preservation\n   * we can only know this if Pixi created the context\n   * @deprecated since 7.0.0\n   */\n  get preserveDrawingBuffer() {\n    return deprecation(\"7.0.0\", \"renderer.preserveDrawingBuffer has been deprecated, we cannot truly know this unless pixi created the context\"), this.context.preserveDrawingBuffer;\n  }\n  /**\n   * The background color to fill if not transparent\n   * @member {number}\n   * @deprecated since 7.0.0\n   */\n  get backgroundColor() {\n    return deprecation(\"7.0.0\", \"renderer.backgroundColor has been deprecated, use renderer.background.color instead.\"), this.background.color;\n  }\n  set backgroundColor(value) {\n    deprecation(\"7.0.0\", \"renderer.backgroundColor has been deprecated, use renderer.background.color instead.\"), this.background.color = value;\n  }\n  /**\n   * The background color alpha. Setting this to 0 will make the canvas transparent.\n   * @member {number}\n   * @deprecated since 7.0.0\n   */\n  get backgroundAlpha() {\n    return deprecation(\"7.0.0\", \"renderer.backgroundAlpha has been deprecated, use renderer.background.alpha instead.\"), this.background.alpha;\n  }\n  /**\n   * @deprecated since 7.0.0\n   */\n  set backgroundAlpha(value) {\n    deprecation(\"7.0.0\", \"renderer.backgroundAlpha has been deprecated, use renderer.background.alpha instead.\"), this.background.alpha = value;\n  }\n  /**\n   * @deprecated since 7.0.0\n   */\n  get powerPreference() {\n    return deprecation(\"7.0.0\", \"renderer.powerPreference has been deprecated, we can only know this if pixi creates the context\"), this.context.powerPreference;\n  }\n  /**\n   * Useful function that returns a texture of the display object that can then be used to create sprites\n   * This can be quite useful if your displayObject is complicated and needs to be reused multiple times.\n   * @param displayObject - The displayObject the object will be generated from.\n   * @param {IGenerateTextureOptions} options - Generate texture options.\n   * @param {PIXI.Rectangle} options.region - The region of the displayObject, that shall be rendered,\n   *        if no region is specified, defaults to the local bounds of the displayObject.\n   * @param {number} [options.resolution] - If not given, the renderer's resolution is used.\n   * @param {PIXI.MSAA_QUALITY} [options.multisample] - If not given, the renderer's multisample is used.\n   * @returns A texture of the graphics object.\n   */\n  generateTexture(displayObject, options) {\n    return this.textureGenerator.generateTexture(displayObject, options);\n  }\n};\n_Renderer.extension = {\n  type: ExtensionType.Renderer,\n  priority: 1\n},\n/**\n* Collection of installed plugins. These are included by default in PIXI, but can be excluded\n* by creating a custom build. Consult the README for more information about creating custom\n* builds and excluding plugins.\n* @private\n*/\n_Renderer.__plugins = {},\n/**\n* The collection of installed systems.\n* @private\n*/\n_Renderer.__systems = {};\nlet Renderer = _Renderer;\nextensions.handleByMap(ExtensionType.RendererPlugin, Renderer.__plugins);\nextensions.handleByMap(ExtensionType.RendererSystem, Renderer.__systems);\nextensions.add(Renderer);\nexport { Renderer };", "map": {"version": 3, "names": ["_Renderer", "_Renderer2", "SystemManager", "constructor", "options", "type", "RENDERER_TYPE", "WEBGL", "Object", "assign", "settings", "RENDER_OPTIONS", "gl", "CONTEXT_UID", "globalUniforms", "UniformGroup", "projectionMatrix", "Matrix", "systemConfig", "runners", "systems", "__systems", "priority", "setup", "deprecation", "premultipliedAlpha", "useContextAlpha", "backgroundAlpha", "_plugin", "rendererPlugins", "__plugins", "startup", "run", "test", "forceCanvas", "isWebGLSupported", "render", "displayObject", "object<PERSON><PERSON><PERSON>", "resize", "desiredScreenWidth", "desiredScreenHeight", "_view", "resizeView", "reset", "emit", "clear", "renderTexture", "bind", "destroy", "<PERSON><PERSON><PERSON><PERSON>", "items", "reverse", "emitWithCustomOptions", "plugins", "multisample", "_multisample", "width", "element", "height", "resolution", "value", "resolutionChange", "autoDensity", "view", "screen", "lastObjectRendered", "renderingToScreen", "rendererLogId", "context", "webGLVersion", "clearBeforeRender", "background", "preserveDrawingBuffer", "backgroundColor", "color", "alpha", "powerPreference", "generateTexture", "textureGenerator", "extension", "ExtensionType", "<PERSON><PERSON><PERSON>", "extensions", "handleByMap", "RendererPlugin", "RendererSystem", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\Renderer.ts"], "sourcesContent": ["import { RENDERER_TYPE } from '@pixi/constants';\nimport { extensions, ExtensionType } from '@pixi/extensions';\nimport { Matrix } from '@pixi/math';\nimport { settings } from '@pixi/settings';\nimport { deprecation, isWebGLSupported } from '@pixi/utils';\nimport { UniformGroup } from './shader/UniformGroup';\nimport { SystemManager } from './system/SystemManager';\n\nimport type { ColorSource } from '@pixi/color';\nimport type { MSAA_QUALITY } from '@pixi/constants';\nimport type { ExtensionMetadata } from '@pixi/extensions';\nimport type { Rectangle } from '@pixi/math';\nimport type { ICanvas } from '@pixi/settings';\nimport type { BackgroundSystem } from './background/BackgroundSystem';\nimport type { BatchSystem } from './batch/BatchSystem';\nimport type { ContextSystem } from './context/ContextSystem';\nimport type { FilterSystem } from './filters/FilterSystem';\nimport type { FramebufferSystem } from './framebuffer/FramebufferSystem';\nimport type { MultisampleSystem } from './framebuffer/MultisampleSystem';\nimport type { BufferSystem } from './geometry/BufferSystem';\nimport type { GeometrySystem } from './geometry/GeometrySystem';\nimport type { IRenderableObject, IRenderer, IRendererOptions, IRendererRenderOptions, IRenderingContext } from './IRenderer';\nimport type { MaskSystem } from './mask/MaskSystem';\nimport type { ScissorSystem } from './mask/ScissorSystem';\nimport type { StencilSystem } from './mask/StencilSystem';\nimport type { IRendererPlugins, PluginSystem } from './plugin/PluginSystem';\nimport type { ProjectionSystem } from './projection/ProjectionSystem';\nimport type { ObjectRendererSystem } from './render/ObjectRendererSystem';\nimport type { GenerateTextureSystem, IGenerateTextureOptions } from './renderTexture/GenerateTextureSystem';\nimport type { RenderTexture } from './renderTexture/RenderTexture';\nimport type { RenderTextureSystem } from './renderTexture/RenderTextureSystem';\nimport type { ShaderSystem } from './shader/ShaderSystem';\nimport type { StartupSystem } from './startup/StartupSystem';\nimport type { StateSystem } from './state/StateSystem';\nimport type { TextureGCSystem } from './textures/TextureGCSystem';\nimport type { TextureSystem } from './textures/TextureSystem';\nimport type { TransformFeedbackSystem } from './transformFeedback/TransformFeedbackSystem';\nimport type { ViewSystem } from './view/ViewSystem';\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface Renderer extends GlobalMixins.Renderer {}\n\n/**\n * The Renderer draws the scene and all its content onto a WebGL enabled canvas.\n *\n * This renderer should be used for browsers that support WebGL.\n *\n * This renderer works by automatically managing WebGLBatches, so no need for Sprite Batches or Sprite Clouds.\n * Don't forget to add the view to your DOM or you will not see anything!\n *\n * Renderer is composed of systems that manage specific tasks. The following systems are added by default\n * whenever you create a renderer:\n *\n * | System                               | Description                                                                   |\n * | ------------------------------------ | ----------------------------------------------------------------------------- |\n *\n * | Generic Systems                      | Systems that manage functionality that all renderer types share               |\n * | ------------------------------------ | ----------------------------------------------------------------------------- |\n * | {@link PIXI.ViewSystem}              | This manages the main view of the renderer usually a Canvas                   |\n * | {@link PIXI.PluginSystem}            | This manages plugins for the renderer                                         |\n * | {@link PIXI.BackgroundSystem}        | This manages the main views background color and alpha                        |\n * | {@link PIXI.StartupSystem}           | Boots up a renderer and initiatives all the systems                           |\n * | {@link PIXI.EventSystem}             | This manages UI events.                                                       |\n *\n * | WebGL Core Systems                   | Provide an optimised, easy to use API to work with WebGL                      |\n * | ------------------------------------ | ----------------------------------------------------------------------------- |\n * | {@link PIXI.ContextSystem}           | This manages the WebGL context and extensions.                                |\n * | {@link PIXI.FramebufferSystem}       | This manages framebuffers, which are used for offscreen rendering.            |\n * | {@link PIXI.GeometrySystem}          | This manages geometries & buffers, which are used to draw object meshes.      |\n * | {@link PIXI.ShaderSystem}            | This manages shaders, programs that run on the GPU to calculate 'em pixels.   |\n * | {@link PIXI.StateSystem}             | This manages the WebGL state variables like blend mode, depth testing, etc.   |\n * | {@link PIXI.TextureSystem}           | This manages textures and their resources on the GPU.                         |\n * | {@link PIXI.TextureGCSystem}         | This will automatically remove textures from the GPU if they are not used.    |\n * | {@link PIXI.MultisampleSystem}       | This manages the multisample const on the WEbGL Renderer                      |\n *\n * | PixiJS High-Level Systems            | Set of specific systems designed to work with PixiJS objects                  |\n * | ------------------------------------ | ----------------------------------------------------------------------------- |\n * | {@link PIXI.GenerateTextureSystem}   | This adds the ability to generate textures from any PIXI.DisplayObject        |\n * | {@link PIXI.ProjectionSystem}        | This manages the `projectionMatrix`, used by shaders to get NDC coordinates.  |\n * | {@link PIXI.RenderTextureSystem}     | This manages render-textures, which are an abstraction over framebuffers.     |\n * | {@link PIXI.MaskSystem}              | This manages masking operations.                                              |\n * | {@link PIXI.ScissorSystem}           | This handles scissor masking, and is used internally by {@link PIXI.MaskSystem} |\n * | {@link PIXI.StencilSystem}           | This handles stencil masking, and is used internally by {@link PIXI.MaskSystem} |\n * | {@link PIXI.FilterSystem}            | This manages the filtering pipeline for post-processing effects.              |\n * | {@link PIXI.BatchSystem}             | This manages object renderers that defer rendering until a flush.             |\n * | {@link PIXI.Prepare}                 | This manages uploading assets to the GPU.                                     |\n * | {@link PIXI.Extract}                 | This extracts image data from display objects.                                |\n *\n * The breadth of the API surface provided by the renderer is contained within these systems.\n * @memberof PIXI\n */\nexport class Renderer extends SystemManager<Renderer> implements IRenderer\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        type: ExtensionType.Renderer,\n        priority: 1,\n    };\n\n    /**\n     * The type of the renderer. will be PIXI.RENDERER_TYPE.CANVAS\n     * @member {number}\n     * @see PIXI.RENDERER_TYPE\n     */\n    public readonly type = RENDERER_TYPE.WEBGL;\n\n    /**\n     * Options passed to the constructor.\n     * @type {PIXI.IRendererOptions}\n     */\n    public readonly options: IRendererOptions;\n\n    /**\n     * WebGL context, set by {@link PIXI.ContextSystem this.context}.\n     * @readonly\n     * @member {WebGLRenderingContext}\n     */\n    public gl: IRenderingContext;\n\n    /**\n     * Global uniforms\n     * Add any uniforms you want shared across your shaders.\n     * the must be added before the scene is rendered for the first time\n     * as we dynamically buildcode to handle all global var per shader\n     *\n     */\n    public globalUniforms: UniformGroup;\n\n    /** Unique UID assigned to the renderer's WebGL context. */\n    public CONTEXT_UID: number;\n\n    // systems\n\n    /**\n     * Mask system instance\n     * @readonly\n     */\n    public readonly mask: MaskSystem;\n\n    /**\n     * Context system instance\n     * @readonly\n     */\n    public readonly context: ContextSystem;\n\n    /**\n     * State system instance\n     * @readonly\n     */\n    public readonly state: StateSystem;\n\n    /**\n     * Shader system instance\n     * @readonly\n     */\n    public readonly shader: ShaderSystem;\n\n    /**\n     * Texture system instance\n     * @readonly\n     */\n    public readonly texture: TextureSystem;\n\n    /**\n     * Buffer system instance\n     * @readonly\n     */\n    public readonly buffer: BufferSystem;\n\n    /**\n     * TransformFeedback system instance\n     * @readonly\n     */\n    public transformFeedback: TransformFeedbackSystem;\n\n    /**\n     * Geometry system instance\n     * @readonly\n     */\n    public readonly geometry: GeometrySystem;\n\n    /**\n     * Framebuffer system instance\n     * @readonly\n     */\n    public readonly framebuffer: FramebufferSystem;\n\n    /**\n     * Scissor system instance\n     * @readonly\n     */\n    public readonly scissor: ScissorSystem;\n\n    /**\n     * Stencil system instance\n     * @readonly\n     */\n    public readonly stencil: StencilSystem;\n\n    /**\n     * Projection system instance\n     * @readonly\n     */\n    public readonly projection: ProjectionSystem;\n\n    /**\n     * Texture garbage collector system instance\n     * @readonly\n     */\n    public readonly textureGC: TextureGCSystem;\n\n    /**\n     * Filter system instance\n     * @readonly\n     */\n    public readonly filter: FilterSystem;\n\n    /**\n     * RenderTexture system instance\n     * @readonly\n     */\n    public readonly renderTexture: RenderTextureSystem;\n\n    /**\n     * Batch system instance\n     * @readonly\n     */\n    public readonly batch: BatchSystem;\n\n    /**\n     * plugin system instance\n     * @readonly\n     */\n    public readonly _plugin: PluginSystem;\n\n    /**\n     * _multisample system instance\n     * @readonly\n     */\n    public readonly _multisample: MultisampleSystem;\n\n    /**\n     * textureGenerator system instance\n     * @readonly\n     */\n    public readonly textureGenerator: GenerateTextureSystem;\n\n    /**\n     * background system instance\n     * @readonly\n     */\n    public readonly background: BackgroundSystem;\n\n    /**\n     * _view system instance\n     * @readonly\n     */\n    public readonly _view: ViewSystem;\n\n    /**\n     * _render system instance\n     * @readonly\n     */\n    public readonly objectRenderer: ObjectRendererSystem;\n\n    /**\n     * startup system instance\n     * @readonly\n     */\n    public readonly startup: StartupSystem;\n\n    /**\n     * Create renderer if WebGL is available. Overrideable\n     * by the **@pixi/canvas-renderer** package to allow fallback.\n     * throws error if WebGL is not available.\n     * @param options\n     * @private\n     */\n    static test(options?: Partial<IRendererOptions>): boolean\n    {\n        if (options?.forceCanvas)\n        {\n            return false;\n        }\n\n        return isWebGLSupported();\n    }\n\n    /**\n     * @param {PIXI.IRendererOptions} [options] - See {@link PIXI.settings.RENDER_OPTIONS} for defaults.\n     */\n    constructor(options?: Partial<IRendererOptions>)\n    {\n        super();\n\n        // Add the default render options\n        options = Object.assign({}, settings.RENDER_OPTIONS, options);\n\n        this.gl = null;\n\n        this.CONTEXT_UID = 0;\n\n        this.globalUniforms = new UniformGroup({\n            projectionMatrix: new Matrix(),\n        }, true);\n\n        const systemConfig = {\n            runners: [\n                'init',\n                'destroy',\n                'contextChange',\n                'resolutionChange',\n                'reset',\n                'update',\n                'postrender',\n                'prerender',\n                'resize'\n            ],\n            systems: Renderer.__systems,\n            priority: [\n                '_view',\n                'textureGenerator',\n                'background',\n                '_plugin',\n                'startup',\n                // low level WebGL systems\n                'context',\n                'state',\n                'texture',\n                'buffer',\n                'geometry',\n                'framebuffer',\n                'transformFeedback',\n                // high level pixi specific rendering\n                'mask',\n                'scissor',\n                'stencil',\n                'projection',\n                'textureGC',\n                'filter',\n                'renderTexture',\n                'batch',\n                'objectRenderer',\n                '_multisample'\n            ],\n        };\n\n        this.setup(systemConfig);\n\n        if ('useContextAlpha' in options)\n        {\n            if (process.env.DEBUG)\n            {\n                // eslint-disable-next-line max-len\n                deprecation('7.0.0', 'options.useContextAlpha is deprecated, use options.premultipliedAlpha and options.backgroundAlpha instead');\n            }\n            options.premultipliedAlpha = options.useContextAlpha && options.useContextAlpha !== 'notMultiplied';\n            options.backgroundAlpha = options.useContextAlpha === false ? 1 : options.backgroundAlpha;\n        }\n\n        this._plugin.rendererPlugins = Renderer.__plugins;\n        this.options = options as IRendererOptions;\n        this.startup.run(this.options);\n    }\n\n    /**\n     * Renders the object to its WebGL view.\n     * @param displayObject - The object to be rendered.\n     * @param {object} [options] - Object to use for render options.\n     * @param {PIXI.RenderTexture} [options.renderTexture] - The render texture to render to.\n     * @param {boolean} [options.clear=true] - Should the canvas be cleared before the new render.\n     * @param {PIXI.Matrix} [options.transform] - A transform to apply to the render texture before rendering.\n     * @param {boolean} [options.skipUpdateTransform=false] - Should we skip the update transform pass?\n     */\n    render(displayObject: IRenderableObject, options?: IRendererRenderOptions): void\n    {\n        this.objectRenderer.render(displayObject, options);\n    }\n\n    /**\n     * Resizes the WebGL view to the specified width and height.\n     * @param desiredScreenWidth - The desired width of the screen.\n     * @param desiredScreenHeight - The desired height of the screen.\n     */\n    resize(desiredScreenWidth: number, desiredScreenHeight: number): void\n    {\n        this._view.resizeView(desiredScreenWidth, desiredScreenHeight);\n    }\n\n    /**\n     * Resets the WebGL state so you can render things however you fancy!\n     * @returns Returns itself.\n     */\n    reset(): this\n    {\n        this.runners.reset.emit();\n\n        return this;\n    }\n\n    /** Clear the frame buffer. */\n    clear(): void\n    {\n        this.renderTexture.bind();\n        this.renderTexture.clear();\n    }\n\n    /**\n     * Removes everything from the renderer (event listeners, spritebatch, etc...)\n     * @param [removeView=false] - Removes the Canvas element from the DOM.\n     *  See: https://github.com/pixijs/pixijs/issues/2233\n     */\n    destroy(removeView = false): void\n    {\n        this.runners.destroy.items.reverse();\n\n        this.emitWithCustomOptions(this.runners.destroy, {\n            _view: removeView,\n        });\n\n        super.destroy();\n    }\n\n    /** Collection of plugins */\n    get plugins(): IRendererPlugins\n    {\n        return this._plugin.plugins;\n    }\n\n    /** The number of msaa samples of the canvas. */\n    get multisample(): MSAA_QUALITY\n    {\n        return this._multisample.multisample;\n    }\n\n    /**\n     * Same as view.width, actual number of pixels in the canvas by horizontal.\n     * @member {number}\n     * @readonly\n     * @default 800\n     */\n    get width(): number\n    {\n        return this._view.element.width;\n    }\n\n    /**\n     * Same as view.height, actual number of pixels in the canvas by vertical.\n     * @default 600\n     */\n    get height(): number\n    {\n        return this._view.element.height;\n    }\n\n    /** The resolution / device pixel ratio of the renderer. */\n    get resolution(): number\n    {\n        return this._view.resolution;\n    }\n    set resolution(value: number)\n    {\n        this._view.resolution = value;\n        this.runners.resolutionChange.emit(value);\n    }\n\n    /** Whether CSS dimensions of canvas view should be resized to screen dimensions automatically. */\n    get autoDensity(): boolean\n    {\n        return this._view.autoDensity;\n    }\n\n    /** The canvas element that everything is drawn to.*/\n    get view(): ICanvas\n    {\n        return this._view.element;\n    }\n\n    /**\n     * Measurements of the screen. (0, 0, screenWidth, screenHeight).\n     *\n     * Its safe to use as filterArea or hitArea for the whole stage.\n     * @member {PIXI.Rectangle}\n     */\n    get screen(): Rectangle\n    {\n        return this._view.screen;\n    }\n\n    /** the last object rendered by the renderer. Useful for other plugins like interaction managers */\n    get lastObjectRendered(): IRenderableObject\n    {\n        return this.objectRenderer.lastObjectRendered;\n    }\n\n    /** Flag if we are rendering to the screen vs renderTexture */\n    get renderingToScreen(): boolean\n    {\n        return this.objectRenderer.renderingToScreen;\n    }\n\n    /** When logging Pixi to the console, this is the name we will show */\n    get rendererLogId(): string\n    {\n        return `WebGL ${this.context.webGLVersion}`;\n    }\n\n    /**\n     * This sets weather the screen is totally cleared between each frame withthe background color and alpha\n     * @deprecated since 7.0.0\n     */\n    get clearBeforeRender(): boolean\n    {\n        if (process.env.DEBUG)\n        {\n            // eslint-disable-next-line max-len\n            deprecation('7.0.0', 'renderer.clearBeforeRender has been deprecated, please use renderer.background.clearBeforeRender instead.');\n        }\n\n        return this.background.clearBeforeRender;\n    }\n\n    /**\n     * Pass-thru setting for the canvas' context `alpha` property. This is typically\n     * not something you need to fiddle with. If you want transparency, use `backgroundAlpha`.\n     * @deprecated since 7.0.0\n     * @member {boolean}\n     */\n    get useContextAlpha(): boolean | 'notMultiplied'\n    {\n        if (process.env.DEBUG)\n        {\n            // eslint-disable-next-line max-len\n            deprecation('7.0.0', 'renderer.useContextAlpha has been deprecated, please use renderer.context.premultipliedAlpha instead.');\n        }\n\n        return this.context.useContextAlpha;\n    }\n\n    /**\n     * readonly drawing buffer preservation\n     * we can only know this if Pixi created the context\n     * @deprecated since 7.0.0\n     */\n    get preserveDrawingBuffer(): boolean\n    {\n        if (process.env.DEBUG)\n        {\n            // eslint-disable-next-line max-len\n            deprecation('7.0.0', 'renderer.preserveDrawingBuffer has been deprecated, we cannot truly know this unless pixi created the context');\n        }\n\n        return this.context.preserveDrawingBuffer;\n    }\n\n    /**\n     * The background color to fill if not transparent\n     * @member {number}\n     * @deprecated since 7.0.0\n     */\n    get backgroundColor(): ColorSource\n    {\n        if (process.env.DEBUG)\n        {\n            // eslint-disable-next-line max-len\n            deprecation('7.0.0', 'renderer.backgroundColor has been deprecated, use renderer.background.color instead.');\n        }\n\n        return this.background.color;\n    }\n\n    set backgroundColor(value: ColorSource)\n    {\n        if (process.env.DEBUG)\n        {\n            deprecation('7.0.0', 'renderer.backgroundColor has been deprecated, use renderer.background.color instead.');\n        }\n\n        this.background.color = value;\n    }\n\n    /**\n     * The background color alpha. Setting this to 0 will make the canvas transparent.\n     * @member {number}\n     * @deprecated since 7.0.0\n     */\n    get backgroundAlpha(): number\n    {\n        if (process.env.DEBUG)\n        {\n            // eslint-disable-next-line max-len\n            deprecation('7.0.0', 'renderer.backgroundAlpha has been deprecated, use renderer.background.alpha instead.');\n        }\n\n        return this.background.alpha;\n    }\n\n    /**\n     * @deprecated since 7.0.0\n     */\n    set backgroundAlpha(value: number)\n    {\n        if (process.env.DEBUG)\n        {\n            // eslint-disable-next-line max-len\n            deprecation('7.0.0', 'renderer.backgroundAlpha has been deprecated, use renderer.background.alpha instead.');\n        }\n\n        this.background.alpha = value;\n    }\n\n    /**\n     * @deprecated since 7.0.0\n     */\n    get powerPreference(): WebGLPowerPreference\n    {\n        if (process.env.DEBUG)\n        {\n            // eslint-disable-next-line max-len\n            deprecation('7.0.0', 'renderer.powerPreference has been deprecated, we can only know this if pixi creates the context');\n        }\n\n        return this.context.powerPreference;\n    }\n\n    /**\n     * Useful function that returns a texture of the display object that can then be used to create sprites\n     * This can be quite useful if your displayObject is complicated and needs to be reused multiple times.\n     * @param displayObject - The displayObject the object will be generated from.\n     * @param {IGenerateTextureOptions} options - Generate texture options.\n     * @param {PIXI.Rectangle} options.region - The region of the displayObject, that shall be rendered,\n     *        if no region is specified, defaults to the local bounds of the displayObject.\n     * @param {number} [options.resolution] - If not given, the renderer's resolution is used.\n     * @param {PIXI.MSAA_QUALITY} [options.multisample] - If not given, the renderer's multisample is used.\n     * @returns A texture of the graphics object.\n     */\n    generateTexture(displayObject: IRenderableObject, options?: IGenerateTextureOptions): RenderTexture\n    {\n        return this.textureGenerator.generateTexture(displayObject, options);\n    }\n\n    /**\n     * Collection of installed plugins. These are included by default in PIXI, but can be excluded\n     * by creating a custom build. Consult the README for more information about creating custom\n     * builds and excluding plugins.\n     * @private\n     */\n    static readonly __plugins: IRendererPlugins = {};\n\n    /**\n     * The collection of installed systems.\n     * @private\n     */\n    static readonly __systems: Record<string, any> = {};\n}\n\n// Handle registration of extensions\nextensions.handleByMap(ExtensionType.RendererPlugin, Renderer.__plugins);\nextensions.handleByMap(ExtensionType.RendererSystem, Renderer.__systems);\nextensions.add(Renderer);\n"], "mappings": ";;;;;;;AA2FO,MAAMA,SAAA,GAAN,MAAMC,UAAA,SAAiBC,aAAA,CAC9B;EAAA;AAAA;AAAA;EAuMIC,YAAYC,OAAA,EACZ;IACU,SA7LV,KAAgBC,IAAA,GAAOC,aAAA,CAAcC,KAAA,EAgMjCH,OAAA,GAAUI,MAAA,CAAOC,MAAA,CAAO,IAAIC,QAAA,CAASC,cAAA,EAAgBP,OAAO,GAE5D,KAAKQ,EAAA,GAAK,MAEV,KAAKC,WAAA,GAAc,GAEnB,KAAKC,cAAA,GAAiB,IAAIC,YAAA,CAAa;MACnCC,gBAAA,EAAkB,IAAIC,MAAA,CAAO;IAAA,GAC9B,EAAI;IAEP,MAAMC,YAAA,GAAe;MACjBC,OAAA,EAAS,CACL,QACA,WACA,iBACA,oBACA,SACA,UACA,cACA,aACA,SACJ;MACAC,OAAA,EAASnB,UAAA,CAASoB,SAAA;MAClBC,QAAA,EAAU,CACN,SACA,oBACA,cACA,WACA;MAAA;MAEA,WACA,SACA,WACA,UACA,YACA,eACA;MAAA;MAEA,QACA,WACA,WACA,cACA,aACA,UACA,iBACA,SACA,kBACA;IACJ;IAGJ,KAAKC,KAAA,CAAML,YAAY,GAEnB,qBAAqBd,OAAA,KAKjBoB,WAAA,CAAY,SAAS,2GAA2G,GAEpIpB,OAAA,CAAQqB,kBAAA,GAAqBrB,OAAA,CAAQsB,eAAA,IAAmBtB,OAAA,CAAQsB,eAAA,KAAoB,iBACpFtB,OAAA,CAAQuB,eAAA,GAAkBvB,OAAA,CAAQsB,eAAA,KAAoB,KAAQ,IAAItB,OAAA,CAAQuB,eAAA,GAG9E,KAAKC,OAAA,CAAQC,eAAA,GAAkB5B,UAAA,CAAS6B,SAAA,EACxC,KAAK1B,OAAA,GAAUA,OAAA,EACf,KAAK2B,OAAA,CAAQC,GAAA,CAAI,KAAK5B,OAAO;EACjC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EArFA,OAAO6B,KAAK7B,OAAA,EACZ;IACQ,OAAAA,OAAA,EAAS8B,WAAA,GAEF,KAGJC,gBAAA,CAAiB;EAC5B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAwFAC,OAAOC,aAAA,EAAkCjC,OAAA,EACzC;IACS,KAAAkC,cAAA,CAAeF,MAAA,CAAOC,aAAA,EAAejC,OAAO;EACrD;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAmC,OAAOC,kBAAA,EAA4BC,mBAAA,EACnC;IACS,KAAAC,KAAA,CAAMC,UAAA,CAAWH,kBAAA,EAAoBC,mBAAmB;EACjE;EAAA;AAAA;AAAA;AAAA;EAMAG,MAAA,EACA;IACS,YAAAzB,OAAA,CAAQyB,KAAA,CAAMC,IAAA,IAEZ;EACX;EAAA;EAGAC,MAAA,EACA;IACI,KAAKC,aAAA,CAAcC,IAAA,IACnB,KAAKD,aAAA,CAAcD,KAAA;EACvB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAG,QAAQC,UAAA,GAAa,IACrB;IACS,KAAA/B,OAAA,CAAQ8B,OAAA,CAAQE,KAAA,CAAMC,OAAA,IAE3B,KAAKC,qBAAA,CAAsB,KAAKlC,OAAA,CAAQ8B,OAAA,EAAS;MAC7CP,KAAA,EAAOQ;IAAA,CACV,GAED,MAAMD,OAAA;EACV;EAAA;EAGA,IAAIK,QAAA,EACJ;IACI,OAAO,KAAK1B,OAAA,CAAQ0B,OAAA;EACxB;EAAA;EAGA,IAAIC,YAAA,EACJ;IACI,OAAO,KAAKC,YAAA,CAAaD,WAAA;EAC7B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQA,IAAIE,MAAA,EACJ;IACW,YAAKf,KAAA,CAAMgB,OAAA,CAAQD,KAAA;EAC9B;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIE,OAAA,EACJ;IACW,YAAKjB,KAAA,CAAMgB,OAAA,CAAQC,MAAA;EAC9B;EAAA;EAGA,IAAIC,WAAA,EACJ;IACI,OAAO,KAAKlB,KAAA,CAAMkB,UAAA;EACtB;EACA,IAAIA,WAAWC,KAAA,EACf;IACI,KAAKnB,KAAA,CAAMkB,UAAA,GAAaC,KAAA,EACxB,KAAK1C,OAAA,CAAQ2C,gBAAA,CAAiBjB,IAAA,CAAKgB,KAAK;EAC5C;EAAA;EAGA,IAAIE,YAAA,EACJ;IACI,OAAO,KAAKrB,KAAA,CAAMqB,WAAA;EACtB;EAAA;EAGA,IAAIC,KAAA,EACJ;IACI,OAAO,KAAKtB,KAAA,CAAMgB,OAAA;EACtB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQA,IAAIO,OAAA,EACJ;IACI,OAAO,KAAKvB,KAAA,CAAMuB,MAAA;EACtB;EAAA;EAGA,IAAIC,mBAAA,EACJ;IACI,OAAO,KAAK5B,cAAA,CAAe4B,kBAAA;EAC/B;EAAA;EAGA,IAAIC,kBAAA,EACJ;IACI,OAAO,KAAK7B,cAAA,CAAe6B,iBAAA;EAC/B;EAAA;EAGA,IAAIC,cAAA,EACJ;IACW,gBAAS,KAAKC,OAAA,CAAQC,YAAY;EAC7C;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIC,kBAAA,EACJ;IAIQ,OAAA/C,WAAA,CAAY,SAAS,2GAA2G,GAG7H,KAAKgD,UAAA,CAAWD,iBAAA;EAC3B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQA,IAAI7C,gBAAA,EACJ;IAIQ,OAAAF,WAAA,CAAY,SAAS,uGAAuG,GAGzH,KAAK6C,OAAA,CAAQ3C,eAAA;EACxB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,IAAI+C,sBAAA,EACJ;IAIQ,OAAAjD,WAAA,CAAY,SAAS,+GAA+G,GAGjI,KAAK6C,OAAA,CAAQI,qBAAA;EACxB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,IAAIC,gBAAA,EACJ;IAIQ,OAAAlD,WAAA,CAAY,SAAS,sFAAsF,GAGxG,KAAKgD,UAAA,CAAWG,KAAA;EAC3B;EAEA,IAAID,gBAAgBb,KAAA,EACpB;IAGQrC,WAAA,CAAY,SAAS,sFAAsF,GAG/G,KAAKgD,UAAA,CAAWG,KAAA,GAAQd,KAAA;EAC5B;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,IAAIlC,gBAAA,EACJ;IAIQ,OAAAH,WAAA,CAAY,SAAS,sFAAsF,GAGxG,KAAKgD,UAAA,CAAWI,KAAA;EAC3B;EAAA;AAAA;AAAA;EAKA,IAAIjD,gBAAgBkC,KAAA,EACpB;IAIQrC,WAAA,CAAY,SAAS,sFAAsF,GAG/G,KAAKgD,UAAA,CAAWI,KAAA,GAAQf,KAAA;EAC5B;EAAA;AAAA;AAAA;EAKA,IAAIgB,gBAAA,EACJ;IAIQ,OAAArD,WAAA,CAAY,SAAS,iGAAiG,GAGnH,KAAK6C,OAAA,CAAQQ,eAAA;EACxB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAaAC,gBAAgBzC,aAAA,EAAkCjC,OAAA,EAClD;IACI,OAAO,KAAK2E,gBAAA,CAAiBD,eAAA,CAAgBzC,aAAA,EAAejC,OAAO;EACvE;AAeJ;AAnjBaJ,SAAA,CAGFgF,SAAA,GAA+B;EAClC3E,IAAA,EAAM4E,aAAA,CAAcC,QAAA;EACpB5D,QAAA,EAAU;AACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANStB,SAAA,CA4iBO8B,SAAA,GAA8B,CAAC;AAAA;AAAA;AAAA;AAAA;AA5iBtC9B,SAAA,CAkjBOqB,SAAA,GAAiC;AAljB9C,IAAM6D,QAAA,GAANlF,SAAA;AAsjBPmF,UAAA,CAAWC,WAAA,CAAYH,aAAA,CAAcI,cAAA,EAAgBH,QAAA,CAASpD,SAAS;AACvEqD,UAAA,CAAWC,WAAA,CAAYH,aAAA,CAAcK,cAAA,EAAgBJ,QAAA,CAAS7D,SAAS;AACvE8D,UAAA,CAAWI,GAAA,CAAIL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}