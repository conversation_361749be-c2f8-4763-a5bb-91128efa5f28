{"ast": null, "code": "async function testImageFormat(imageData) {\n  if (\"Image\" in globalThis) return new Promise(resolve => {\n    const image = new Image();\n    image.onload = () => {\n      resolve(!0);\n    }, image.onerror = () => {\n      resolve(!1);\n    }, image.src = imageData;\n  });\n  if (\"createImageBitmap\" in globalThis && \"fetch\" in globalThis) {\n    try {\n      const blob = await (await fetch(imageData)).blob();\n      await createImageBitmap(blob);\n    } catch {\n      return !1;\n    }\n    return !0;\n  }\n  return !1;\n}\nexport { testImageFormat };", "map": {"version": 3, "names": ["testImageFormat", "imageData", "globalThis", "Promise", "resolve", "image", "Image", "onload", "onerror", "src", "blob", "fetch", "createImageBitmap"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\assets\\src\\detections\\utils\\testImageFormat.ts"], "sourcesContent": ["export async function testImageFormat(imageData: string): Promise<boolean>\n{\n    // Some browsers currently do not support createImageBitmap with Blob, so new Image() is preferred when exist.\n    // See https://caniuse.com/createimagebitmap for more information.\n\n    if ('Image' in globalThis)\n    {\n        return new Promise<boolean>((resolve) =>\n        {\n            const image = new Image();\n\n            image.onload = () =>\n            {\n                resolve(true);\n            };\n            image.onerror = () =>\n            {\n                resolve(false);\n            };\n            image.src = imageData;\n        });\n    }\n\n    if ('createImageBitmap' in globalThis && 'fetch' in globalThis)\n    {\n        try\n        {\n            const blob = await (await fetch(imageData)).blob();\n\n            await createImageBitmap(blob);\n        }\n        catch (e)\n        {\n            return false;\n        }\n\n        return true;\n    }\n\n    return false;\n}\n"], "mappings": "AAAA,eAAsBA,gBAAgBC,SAAA,EACtC;EAII,IAAI,WAAWC,UAAA,EAEJ,WAAIC,OAAA,CAAkBC,OAAA,IAC7B;IACU,MAAAC,KAAA,GAAQ,IAAIC,KAAA;IAElBD,KAAA,CAAME,MAAA,GAAS,MACf;MACIH,OAAA,CAAQ,EAAI;IAAA,GAEhBC,KAAA,CAAMG,OAAA,GAAU,MAChB;MACIJ,OAAA,CAAQ,EAAK;IAAA,GAEjBC,KAAA,CAAMI,GAAA,GAAMR,SAAA;EAAA,CACf;EAGD,2BAAuBC,UAAA,IAAc,WAAWA,UAAA,EACpD;IAEI;MACI,MAAMQ,IAAA,GAAO,OAAO,MAAMC,KAAA,CAAMV,SAAS,GAAGS,IAAA;MAE5C,MAAME,iBAAA,CAAkBF,IAAI;IAAA,QAGhC;MACW;IACX;IAEO;EACX;EAEO;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}