{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Projects\\\\Python\\\\EU4\\\\frontend\\\\src\\\\WorldMap.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport * as PIXI from 'pixi.js';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MAP_WIDTH = 800;\nconst MAP_HEIGHT = 600;\nconst colors = [0x88ccff, 0xe67e22, 0x2ecc71, 0xf39c12, 0x9b59b6, 0x1abc9c, 0xe74c3c, 0x34495e, 0x27ae60, 0x2980b9];\nconst WorldMap = ({\n  onSelectCountry,\n  selectedCountry\n}) => {\n  _s();\n  const pixiContainer = useRef(null);\n  const appRef = useRef(null);\n  const stageRef = useRef(null);\n  const [countries, setCountries] = React.useState([]);\n  const [dragging, setDragging] = React.useState(false);\n  const [lastPos, setLastPos] = React.useState({\n    x: 0,\n    y: 0\n  });\n  useEffect(() => {\n    axios.get('http://localhost:8000/countries').then(r => setCountries(r.data));\n  }, []);\n  useEffect(() => {\n    if (!countries.length) return;\n    const app = new PIXI.Application({\n      width: MAP_WIDTH,\n      height: MAP_HEIGHT,\n      backgroundColor: 0x222233,\n      antialias: true\n    });\n    appRef.current = app;\n    pixiContainer.current.appendChild(app.view);\n\n    // Create a container for panning/zooming\n    const stage = new PIXI.Container();\n    stageRef.current = stage;\n    app.stage.addChild(stage);\n\n    // Draw countries as polygons\n    countries.forEach((country, i) => {\n      var _country$properties, _selectedCountry$prop;\n      if (!country.geometry) return;\n      const countryName = (_country$properties = country.properties) === null || _country$properties === void 0 ? void 0 : _country$properties.NAME;\n      const isSelected = selectedCountry && countryName === ((_selectedCountry$prop = selectedCountry.properties) === null || _selectedCountry$prop === void 0 ? void 0 : _selectedCountry$prop.NAME);\n      const graphics = new PIXI.Graphics();\n      graphics.beginFill(isSelected ? 0xffff00 : colors[i % colors.length], isSelected ? 0.9 : 0.7);\n      if (country.geometry.type === 'Polygon') {\n        country.geometry.coordinates.forEach(ring => {\n          if (!ring.length) return;\n          graphics.moveTo(ring[0][0] * 4 + 400, 300 - ring[0][1] * 4);\n          for (let j = 1; j < ring.length; j++) {\n            graphics.lineTo(ring[j][0] * 4 + 400, 300 - ring[j][1] * 4);\n          }\n        });\n      } else if (country.geometry.type === 'MultiPolygon') {\n        country.geometry.coordinates.forEach(polygon => {\n          polygon.forEach(ring => {\n            if (!ring.length) return;\n            graphics.moveTo(ring[0][0] * 4 + 400, 300 - ring[0][1] * 4);\n            for (let j = 1; j < ring.length; j++) {\n              graphics.lineTo(ring[j][0] * 4 + 400, 300 - ring[j][1] * 4);\n            }\n          });\n        });\n      }\n      graphics.endFill();\n      graphics.eventMode = 'static';\n      graphics.buttonMode = true;\n      graphics.on('pointerdown', () => {\n        onSelectCountry(country);\n      });\n      stage.addChild(graphics);\n    });\n\n    // Pan\n    let dragging = false;\n    let last = {\n      x: 0,\n      y: 0\n    };\n    app.view.addEventListener('mousedown', e => {\n      dragging = true;\n      last = {\n        x: e.clientX,\n        y: e.clientY\n      };\n    });\n    app.view.addEventListener('mouseup', () => {\n      dragging = false;\n    });\n    app.view.addEventListener('mouseleave', () => {\n      dragging = false;\n    });\n    app.view.addEventListener('mousemove', e => {\n      if (!dragging) return;\n      const dx = e.clientX - last.x;\n      const dy = e.clientY - last.y;\n      stage.x += dx;\n      stage.y += dy;\n      last = {\n        x: e.clientX,\n        y: e.clientY\n      };\n    });\n\n    // Zoom\n    app.view.addEventListener('wheel', e => {\n      e.preventDefault();\n      const scale = Math.max(0.2, Math.min(5, stage.scale.x + (e.deltaY < 0 ? 0.1 : -0.1)));\n      stage.scale.set(scale);\n    });\n    return () => {\n      app.destroy(true, {\n        children: true\n      });\n    };\n  }, [countries, onSelectCountry, selectedCountry]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      minHeight: '80vh',\n      background: '#181830',\n      padding: 32\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: pixiContainer,\n      style: {\n        width: MAP_WIDTH,\n        height: MAP_HEIGHT,\n        border: '2px solid #444',\n        margin: 'auto',\n        cursor: 'grab',\n        background: '#222233'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n_s(WorldMap, \"batdOQRKuUjtCIY/BtAQiUoeapw=\");\n_c = WorldMap;\nexport default WorldMap;\nvar _c;\n$RefreshReg$(_c, \"WorldMap\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "PIXI", "axios", "jsxDEV", "_jsxDEV", "MAP_WIDTH", "MAP_HEIGHT", "colors", "WorldMap", "onSelectCountry", "selectedCountry", "_s", "pixiContainer", "appRef", "stageRef", "countries", "setCountries", "useState", "dragging", "setDragging", "lastPos", "setLastPos", "x", "y", "get", "then", "r", "data", "length", "app", "Application", "width", "height", "backgroundColor", "antialias", "current", "append<PERSON><PERSON><PERSON>", "view", "stage", "Container", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "country", "i", "_country$properties", "_selectedCountry$prop", "geometry", "countryName", "properties", "NAME", "isSelected", "graphics", "Graphics", "beginFill", "type", "coordinates", "ring", "moveTo", "j", "lineTo", "polygon", "endFill", "eventMode", "buttonMode", "on", "last", "addEventListener", "e", "clientX", "clientY", "dx", "dy", "preventDefault", "scale", "Math", "max", "min", "deltaY", "set", "destroy", "children", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "background", "padding", "ref", "border", "margin", "cursor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Projects/Python/EU4/frontend/src/WorldMap.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\nimport * as PIXI from 'pixi.js';\r\nimport axios from 'axios';\r\n\r\nconst MAP_WIDTH = 800;\r\nconst MAP_HEIGHT = 600;\r\n\r\nconst colors = [0x88ccff, 0xe67e22, 0x2ecc71, 0xf39c12, 0x9b59b6, 0x1abc9c, 0xe74c3c, 0x34495e, 0x27ae60, 0x2980b9];\r\n\r\nconst WorldMap = ({ onSelectCountry, selectedCountry }) => {\r\n  const pixiContainer = useRef(null);\r\n  const appRef = useRef(null);\r\n  const stageRef = useRef(null);\r\n  const [countries, setCountries] = React.useState([]);\r\n  const [dragging, setDragging] = React.useState(false);\r\n  const [lastPos, setLastPos] = React.useState({ x: 0, y: 0 });\r\n\r\n  useEffect(() => {\r\n    axios.get('http://localhost:8000/countries').then(r => setCountries(r.data));\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (!countries.length) return;\r\n    const app = new PIXI.Application({\r\n      width: MAP_WIDTH,\r\n      height: MAP_HEIGHT,\r\n      backgroundColor: 0x222233,\r\n      antialias: true,\r\n    });\r\n    appRef.current = app;\r\n    pixiContainer.current.appendChild(app.view);\r\n\r\n    // Create a container for panning/zooming\r\n    const stage = new PIXI.Container();\r\n    stageRef.current = stage;\r\n    app.stage.addChild(stage);\r\n\r\n    // Draw countries as polygons\r\n    countries.forEach((country, i) => {\r\n      if (!country.geometry) return;\r\n      const countryName = country.properties?.NAME;\r\n      const isSelected = selectedCountry && (countryName === selectedCountry.properties?.NAME);\r\n      const graphics = new PIXI.Graphics();\r\n      graphics.beginFill(isSelected ? 0xffff00 : colors[i % colors.length], isSelected ? 0.9 : 0.7);\r\n      if (country.geometry.type === 'Polygon') {\r\n        country.geometry.coordinates.forEach(ring => {\r\n          if (!ring.length) return;\r\n          graphics.moveTo(ring[0][0] * 4 + 400, 300 - ring[0][1] * 4);\r\n          for (let j = 1; j < ring.length; j++) {\r\n            graphics.lineTo(ring[j][0] * 4 + 400, 300 - ring[j][1] * 4);\r\n          }\r\n        });\r\n      } else if (country.geometry.type === 'MultiPolygon') {\r\n        country.geometry.coordinates.forEach(polygon => {\r\n          polygon.forEach(ring => {\r\n            if (!ring.length) return;\r\n            graphics.moveTo(ring[0][0] * 4 + 400, 300 - ring[0][1] * 4);\r\n            for (let j = 1; j < ring.length; j++) {\r\n              graphics.lineTo(ring[j][0] * 4 + 400, 300 - ring[j][1] * 4);\r\n            }\r\n          });\r\n        });\r\n      }\r\n      graphics.endFill();\r\n      graphics.eventMode = 'static';\r\n      graphics.buttonMode = true;\r\n      graphics.on('pointerdown', () => {\r\n        onSelectCountry(country);\r\n      });\r\n      stage.addChild(graphics);\r\n    });\r\n\r\n    // Pan\r\n    let dragging = false;\r\n    let last = { x: 0, y: 0 };\r\n    app.view.addEventListener('mousedown', (e) => {\r\n      dragging = true;\r\n      last = { x: e.clientX, y: e.clientY };\r\n    });\r\n    app.view.addEventListener('mouseup', () => { dragging = false; });\r\n    app.view.addEventListener('mouseleave', () => { dragging = false; });\r\n    app.view.addEventListener('mousemove', (e) => {\r\n      if (!dragging) return;\r\n      const dx = e.clientX - last.x;\r\n      const dy = e.clientY - last.y;\r\n      stage.x += dx;\r\n      stage.y += dy;\r\n      last = { x: e.clientX, y: e.clientY };\r\n    });\r\n\r\n    // Zoom\r\n    app.view.addEventListener('wheel', (e) => {\r\n      e.preventDefault();\r\n      const scale = Math.max(0.2, Math.min(5, stage.scale.x + (e.deltaY < 0 ? 0.1 : -0.1)));\r\n      stage.scale.set(scale);\r\n    });\r\n\r\n    return () => {\r\n      app.destroy(true, { children: true });\r\n    };\r\n  }, [countries, onSelectCountry, selectedCountry]);\r\n\r\n  return (\r\n    <div style={{\r\n      display: 'flex',\r\n      flexDirection: 'column',\r\n      alignItems: 'center',\r\n      justifyContent: 'center',\r\n      minHeight: '80vh',\r\n      background: '#181830',\r\n      padding: 32\r\n    }}>\r\n      <div\r\n        ref={pixiContainer}\r\n        style={{ width: MAP_WIDTH, height: MAP_HEIGHT, border: '2px solid #444', margin: 'auto', cursor: 'grab', background: '#222233' }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WorldMap;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAC/B,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAG,GAAG;AACrB,MAAMC,UAAU,GAAG,GAAG;AAEtB,MAAMC,MAAM,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;AAEnH,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EACzD,MAAMC,aAAa,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMa,MAAM,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC3B,MAAMc,QAAQ,GAAGd,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGlB,KAAK,CAACmB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,KAAK,CAACmB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAGvB,KAAK,CAACmB,QAAQ,CAAC;IAAEK,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAE5DxB,SAAS,CAAC,MAAM;IACdG,KAAK,CAACsB,GAAG,CAAC,iCAAiC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIV,YAAY,CAACU,CAAC,CAACC,IAAI,CAAC,CAAC;EAC9E,CAAC,EAAE,EAAE,CAAC;EAEN5B,SAAS,CAAC,MAAM;IACd,IAAI,CAACgB,SAAS,CAACa,MAAM,EAAE;IACvB,MAAMC,GAAG,GAAG,IAAI5B,IAAI,CAAC6B,WAAW,CAAC;MAC/BC,KAAK,EAAE1B,SAAS;MAChB2B,MAAM,EAAE1B,UAAU;MAClB2B,eAAe,EAAE,QAAQ;MACzBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFrB,MAAM,CAACsB,OAAO,GAAGN,GAAG;IACpBjB,aAAa,CAACuB,OAAO,CAACC,WAAW,CAACP,GAAG,CAACQ,IAAI,CAAC;;IAE3C;IACA,MAAMC,KAAK,GAAG,IAAIrC,IAAI,CAACsC,SAAS,CAAC,CAAC;IAClCzB,QAAQ,CAACqB,OAAO,GAAGG,KAAK;IACxBT,GAAG,CAACS,KAAK,CAACE,QAAQ,CAACF,KAAK,CAAC;;IAEzB;IACAvB,SAAS,CAAC0B,OAAO,CAAC,CAACC,OAAO,EAAEC,CAAC,KAAK;MAAA,IAAAC,mBAAA,EAAAC,qBAAA;MAChC,IAAI,CAACH,OAAO,CAACI,QAAQ,EAAE;MACvB,MAAMC,WAAW,IAAAH,mBAAA,GAAGF,OAAO,CAACM,UAAU,cAAAJ,mBAAA,uBAAlBA,mBAAA,CAAoBK,IAAI;MAC5C,MAAMC,UAAU,GAAGxC,eAAe,IAAKqC,WAAW,OAAAF,qBAAA,GAAKnC,eAAe,CAACsC,UAAU,cAAAH,qBAAA,uBAA1BA,qBAAA,CAA4BI,IAAI,CAAC;MACxF,MAAME,QAAQ,GAAG,IAAIlD,IAAI,CAACmD,QAAQ,CAAC,CAAC;MACpCD,QAAQ,CAACE,SAAS,CAACH,UAAU,GAAG,QAAQ,GAAG3C,MAAM,CAACoC,CAAC,GAAGpC,MAAM,CAACqB,MAAM,CAAC,EAAEsB,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC;MAC7F,IAAIR,OAAO,CAACI,QAAQ,CAACQ,IAAI,KAAK,SAAS,EAAE;QACvCZ,OAAO,CAACI,QAAQ,CAACS,WAAW,CAACd,OAAO,CAACe,IAAI,IAAI;UAC3C,IAAI,CAACA,IAAI,CAAC5B,MAAM,EAAE;UAClBuB,QAAQ,CAACM,MAAM,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,GAAGA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;UAC3D,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAAC5B,MAAM,EAAE8B,CAAC,EAAE,EAAE;YACpCP,QAAQ,CAACQ,MAAM,CAACH,IAAI,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,GAAGF,IAAI,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;UAC7D;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIhB,OAAO,CAACI,QAAQ,CAACQ,IAAI,KAAK,cAAc,EAAE;QACnDZ,OAAO,CAACI,QAAQ,CAACS,WAAW,CAACd,OAAO,CAACmB,OAAO,IAAI;UAC9CA,OAAO,CAACnB,OAAO,CAACe,IAAI,IAAI;YACtB,IAAI,CAACA,IAAI,CAAC5B,MAAM,EAAE;YAClBuB,QAAQ,CAACM,MAAM,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,GAAGA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3D,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAAC5B,MAAM,EAAE8B,CAAC,EAAE,EAAE;cACpCP,QAAQ,CAACQ,MAAM,CAACH,IAAI,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,GAAGF,IAAI,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC7D;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MACAP,QAAQ,CAACU,OAAO,CAAC,CAAC;MAClBV,QAAQ,CAACW,SAAS,GAAG,QAAQ;MAC7BX,QAAQ,CAACY,UAAU,GAAG,IAAI;MAC1BZ,QAAQ,CAACa,EAAE,CAAC,aAAa,EAAE,MAAM;QAC/BvD,eAAe,CAACiC,OAAO,CAAC;MAC1B,CAAC,CAAC;MACFJ,KAAK,CAACE,QAAQ,CAACW,QAAQ,CAAC;IAC1B,CAAC,CAAC;;IAEF;IACA,IAAIjC,QAAQ,GAAG,KAAK;IACpB,IAAI+C,IAAI,GAAG;MAAE3C,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACzBM,GAAG,CAACQ,IAAI,CAAC6B,gBAAgB,CAAC,WAAW,EAAGC,CAAC,IAAK;MAC5CjD,QAAQ,GAAG,IAAI;MACf+C,IAAI,GAAG;QAAE3C,CAAC,EAAE6C,CAAC,CAACC,OAAO;QAAE7C,CAAC,EAAE4C,CAAC,CAACE;MAAQ,CAAC;IACvC,CAAC,CAAC;IACFxC,GAAG,CAACQ,IAAI,CAAC6B,gBAAgB,CAAC,SAAS,EAAE,MAAM;MAAEhD,QAAQ,GAAG,KAAK;IAAE,CAAC,CAAC;IACjEW,GAAG,CAACQ,IAAI,CAAC6B,gBAAgB,CAAC,YAAY,EAAE,MAAM;MAAEhD,QAAQ,GAAG,KAAK;IAAE,CAAC,CAAC;IACpEW,GAAG,CAACQ,IAAI,CAAC6B,gBAAgB,CAAC,WAAW,EAAGC,CAAC,IAAK;MAC5C,IAAI,CAACjD,QAAQ,EAAE;MACf,MAAMoD,EAAE,GAAGH,CAAC,CAACC,OAAO,GAAGH,IAAI,CAAC3C,CAAC;MAC7B,MAAMiD,EAAE,GAAGJ,CAAC,CAACE,OAAO,GAAGJ,IAAI,CAAC1C,CAAC;MAC7Be,KAAK,CAAChB,CAAC,IAAIgD,EAAE;MACbhC,KAAK,CAACf,CAAC,IAAIgD,EAAE;MACbN,IAAI,GAAG;QAAE3C,CAAC,EAAE6C,CAAC,CAACC,OAAO;QAAE7C,CAAC,EAAE4C,CAAC,CAACE;MAAQ,CAAC;IACvC,CAAC,CAAC;;IAEF;IACAxC,GAAG,CAACQ,IAAI,CAAC6B,gBAAgB,CAAC,OAAO,EAAGC,CAAC,IAAK;MACxCA,CAAC,CAACK,cAAc,CAAC,CAAC;MAClB,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEtC,KAAK,CAACmC,KAAK,CAACnD,CAAC,IAAI6C,CAAC,CAACU,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACrFvC,KAAK,CAACmC,KAAK,CAACK,GAAG,CAACL,KAAK,CAAC;IACxB,CAAC,CAAC;IAEF,OAAO,MAAM;MACX5C,GAAG,CAACkD,OAAO,CAAC,IAAI,EAAE;QAAEC,QAAQ,EAAE;MAAK,CAAC,CAAC;IACvC,CAAC;EACH,CAAC,EAAE,CAACjE,SAAS,EAAEN,eAAe,EAAEC,eAAe,CAAC,CAAC;EAEjD,oBACEN,OAAA;IAAK6E,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,SAAS,EAAE,MAAM;MACjBC,UAAU,EAAE,SAAS;MACrBC,OAAO,EAAE;IACX,CAAE;IAAAR,QAAA,eACA5E,OAAA;MACEqF,GAAG,EAAE7E,aAAc;MACnBqE,KAAK,EAAE;QAAElD,KAAK,EAAE1B,SAAS;QAAE2B,MAAM,EAAE1B,UAAU;QAAEoF,MAAM,EAAE,gBAAgB;QAAEC,MAAM,EAAE,MAAM;QAAEC,MAAM,EAAE,MAAM;QAAEL,UAAU,EAAE;MAAU;IAAE;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACrF,EAAA,CA7GIH,QAAQ;AAAAyF,EAAA,GAARzF,QAAQ;AA+Gd,eAAeA,QAAQ;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}