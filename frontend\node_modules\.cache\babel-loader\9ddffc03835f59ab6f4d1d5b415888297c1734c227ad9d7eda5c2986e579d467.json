{"ast": null, "code": "import \"./index.mjs\";\nimport { uniformParsers } from \"./uniformParsers.mjs\";\nimport { mapSize } from \"./mapSize.mjs\";\nfunction uboUpdate(_ud, _uv, _renderer, _syncData, buffer) {\n  _renderer.buffer.update(buffer);\n}\nconst UBO_TO_SINGLE_SETTERS = {\n    float: `\n        data[offset] = v;\n    `,\n    vec2: `\n        data[offset] = v[0];\n        data[offset+1] = v[1];\n    `,\n    vec3: `\n        data[offset] = v[0];\n        data[offset+1] = v[1];\n        data[offset+2] = v[2];\n\n    `,\n    vec4: `\n        data[offset] = v[0];\n        data[offset+1] = v[1];\n        data[offset+2] = v[2];\n        data[offset+3] = v[3];\n    `,\n    mat2: `\n        data[offset] = v[0];\n        data[offset+1] = v[1];\n\n        data[offset+4] = v[2];\n        data[offset+5] = v[3];\n    `,\n    mat3: `\n        data[offset] = v[0];\n        data[offset+1] = v[1];\n        data[offset+2] = v[2];\n\n        data[offset + 4] = v[3];\n        data[offset + 5] = v[4];\n        data[offset + 6] = v[5];\n\n        data[offset + 8] = v[6];\n        data[offset + 9] = v[7];\n        data[offset + 10] = v[8];\n    `,\n    mat4: `\n        for(var i = 0; i < 16; i++)\n        {\n            data[offset + i] = v[i];\n        }\n    `\n  },\n  GLSL_TO_STD40_SIZE = {\n    float: 4,\n    vec2: 8,\n    vec3: 12,\n    vec4: 16,\n    int: 4,\n    ivec2: 8,\n    ivec3: 12,\n    ivec4: 16,\n    uint: 4,\n    uvec2: 8,\n    uvec3: 12,\n    uvec4: 16,\n    bool: 4,\n    bvec2: 8,\n    bvec3: 12,\n    bvec4: 16,\n    mat2: 16 * 2,\n    mat3: 16 * 3,\n    mat4: 16 * 4\n  };\nfunction createUBOElements(uniformData) {\n  const uboElements = uniformData.map(data => ({\n    data,\n    offset: 0,\n    dataLen: 0,\n    dirty: 0\n  }));\n  let size = 0,\n    chunkSize = 0,\n    offset = 0;\n  for (let i = 0; i < uboElements.length; i++) {\n    const uboElement = uboElements[i];\n    if (size = GLSL_TO_STD40_SIZE[uboElement.data.type], uboElement.data.size > 1 && (size = Math.max(size, 16) * uboElement.data.size), uboElement.dataLen = size, chunkSize % size !== 0 && chunkSize < 16) {\n      const lineUpValue = chunkSize % size % 16;\n      chunkSize += lineUpValue, offset += lineUpValue;\n    }\n    chunkSize + size > 16 ? (offset = Math.ceil(offset / 16) * 16, uboElement.offset = offset, offset += size, chunkSize = size) : (uboElement.offset = offset, chunkSize += size, offset += size);\n  }\n  return offset = Math.ceil(offset / 16) * 16, {\n    uboElements,\n    size: offset\n  };\n}\nfunction getUBOData(uniforms, uniformData) {\n  const usedUniformDatas = [];\n  for (const i in uniforms) uniformData[i] && usedUniformDatas.push(uniformData[i]);\n  return usedUniformDatas.sort((a, b) => a.index - b.index), usedUniformDatas;\n}\nfunction generateUniformBufferSync(group, uniformData) {\n  if (!group.autoManage) return {\n    size: 0,\n    syncFunc: uboUpdate\n  };\n  const usedUniformDatas = getUBOData(group.uniforms, uniformData),\n    {\n      uboElements,\n      size\n    } = createUBOElements(usedUniformDatas),\n    funcFragments = [`\n    var v = null;\n    var v2 = null;\n    var cv = null;\n    var t = 0;\n    var gl = renderer.gl\n    var index = 0;\n    var data = buffer.data;\n    `];\n  for (let i = 0; i < uboElements.length; i++) {\n    const uboElement = uboElements[i],\n      uniform = group.uniforms[uboElement.data.name],\n      name = uboElement.data.name;\n    let parsed = !1;\n    for (let j = 0; j < uniformParsers.length; j++) {\n      const uniformParser = uniformParsers[j];\n      if (uniformParser.codeUbo && uniformParser.test(uboElement.data, uniform)) {\n        funcFragments.push(`offset = ${uboElement.offset / 4};`, uniformParsers[j].codeUbo(uboElement.data.name, uniform)), parsed = !0;\n        break;\n      }\n    }\n    if (!parsed) if (uboElement.data.size > 1) {\n      const size2 = mapSize(uboElement.data.type),\n        rowSize = Math.max(GLSL_TO_STD40_SIZE[uboElement.data.type] / 16, 1),\n        elementSize = size2 / rowSize,\n        remainder = (4 - elementSize % 4) % 4;\n      funcFragments.push(`\n                cv = ud.${name}.value;\n                v = uv.${name};\n                offset = ${uboElement.offset / 4};\n\n                t = 0;\n\n                for(var i=0; i < ${uboElement.data.size * rowSize}; i++)\n                {\n                    for(var j = 0; j < ${elementSize}; j++)\n                    {\n                        data[offset++] = v[t++];\n                    }\n                    offset += ${remainder};\n                }\n\n                `);\n    } else {\n      const template = UBO_TO_SINGLE_SETTERS[uboElement.data.type];\n      funcFragments.push(`\n                cv = ud.${name}.value;\n                v = uv.${name};\n                offset = ${uboElement.offset / 4};\n                ${template};\n                `);\n    }\n  }\n  return funcFragments.push(`\n       renderer.buffer.update(buffer);\n    `), {\n    size,\n    // eslint-disable-next-line no-new-func\n    syncFunc: new Function(\"ud\", \"uv\", \"renderer\", \"syncData\", \"buffer\", funcFragments.join(`\n`))\n  };\n}\nexport { createUBOElements, generateUniformBufferSync, getUBOData };", "map": {"version": 3, "names": ["uboUpdate", "_ud", "_uv", "_renderer", "_syncData", "buffer", "update", "UBO_TO_SINGLE_SETTERS", "float", "vec2", "vec3", "vec4", "mat2", "mat3", "mat4", "GLSL_TO_STD40_SIZE", "int", "ivec2", "ivec3", "ivec4", "uint", "uvec2", "uvec3", "uvec4", "bool", "bvec2", "bvec3", "bvec4", "createUBOElements", "uniformData", "uboElements", "map", "data", "offset", "dataLen", "dirty", "size", "chunkSize", "i", "length", "uboElement", "type", "Math", "max", "lineUpValue", "ceil", "getUBOData", "uniforms", "usedUniformDatas", "push", "sort", "a", "b", "index", "generateUniformBufferSync", "group", "autoManage", "syncFunc", "funcFragments", "uniform", "name", "parsed", "j", "uniformParsers", "<PERSON><PERSON><PERSON><PERSON>", "codeUbo", "test", "size2", "mapSize", "rowSize", "elementSize", "remainder", "template", "Function", "join"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\shader\\utils\\generateUniformBufferSync.ts"], "sourcesContent": ["import { mapSize } from '../utils';\nimport { uniformParsers } from './uniformParsers';\n\nimport type { Dict } from '@pixi/utils';\nimport type { Buffer } from '../../geometry/Buffer';\nimport type { Renderer } from '../../Renderer';\nimport type { IUniformData } from '../Program';\nimport type { UniformGroup } from '../UniformGroup';\n\nexport type UniformsSyncCallback = (...args: any[]) => void;\n\nfunction uboUpdate(_ud: any, _uv: any, _renderer: Renderer, _syncData: any, buffer: Buffer): void\n{\n    _renderer.buffer.update(buffer);\n}\n\n// cv = CachedValue\n// v = value\n// ud = uniformData\n// uv = uniformValue\n// l = location\nconst UBO_TO_SINGLE_SETTERS: Dict<string> = {\n    float: `\n        data[offset] = v;\n    `,\n    vec2: `\n        data[offset] = v[0];\n        data[offset+1] = v[1];\n    `,\n    vec3: `\n        data[offset] = v[0];\n        data[offset+1] = v[1];\n        data[offset+2] = v[2];\n\n    `,\n    vec4: `\n        data[offset] = v[0];\n        data[offset+1] = v[1];\n        data[offset+2] = v[2];\n        data[offset+3] = v[3];\n    `,\n    mat2: `\n        data[offset] = v[0];\n        data[offset+1] = v[1];\n\n        data[offset+4] = v[2];\n        data[offset+5] = v[3];\n    `,\n    mat3: `\n        data[offset] = v[0];\n        data[offset+1] = v[1];\n        data[offset+2] = v[2];\n\n        data[offset + 4] = v[3];\n        data[offset + 5] = v[4];\n        data[offset + 6] = v[5];\n\n        data[offset + 8] = v[6];\n        data[offset + 9] = v[7];\n        data[offset + 10] = v[8];\n    `,\n    mat4: `\n        for(var i = 0; i < 16; i++)\n        {\n            data[offset + i] = v[i];\n        }\n    `\n};\n\nconst GLSL_TO_STD40_SIZE: Dict<number> = {\n    float:  4,\n    vec2:   8,\n    vec3:   12,\n    vec4:   16,\n\n    int:      4,\n    ivec2:    8,\n    ivec3:    12,\n    ivec4:    16,\n\n    uint:     4,\n    uvec2:    8,\n    uvec3:    12,\n    uvec4:    16,\n\n    bool:     4,\n    bvec2:    8,\n    bvec3:    12,\n    bvec4:    16,\n\n    mat2:     16 * 2,\n    mat3:     16 * 3,\n    mat4:     16 * 4,\n};\n\ninterface UBOElement\n{\n    data: IUniformData\n    offset: number,\n    dataLen: number,\n    dirty: number\n}\n\n/**\n * logic originally from here: https://github.com/sketchpunk/FunWithWebGL2/blob/master/lesson_022/Shaders.js\n * rewrote it, but this was a great starting point to get a solid understanding of whats going on :)\n * @ignore\n * @param uniformData\n */\nexport function createUBOElements(uniformData: IUniformData[]): {uboElements: UBOElement[], size: number}\n{\n    const uboElements: UBOElement[] = uniformData.map((data: IUniformData) =>\n        ({\n            data,\n            offset: 0,\n            dataLen: 0,\n            dirty: 0\n        }));\n\n    let size = 0;\n    let chunkSize = 0;\n    let offset = 0;\n\n    for (let i = 0; i < uboElements.length; i++)\n    {\n        const uboElement = uboElements[i];\n\n        size = GLSL_TO_STD40_SIZE[uboElement.data.type];\n\n        if (uboElement.data.size > 1)\n        {\n            size = Math.max(size, 16) * uboElement.data.size;\n        }\n\n        uboElement.dataLen = size;\n\n        // add some size offset..\n        // must align to the nearest 16 bytes or internally nearest round size\n\n        if (chunkSize % size !== 0 && chunkSize < 16)\n        {\n            // diff required to line up..\n            const lineUpValue = (chunkSize % size) % 16;\n\n            chunkSize += lineUpValue;\n            offset += lineUpValue;\n        }\n\n        if ((chunkSize + size) > 16)\n        {\n            offset = Math.ceil(offset / 16) * 16;\n            uboElement.offset = offset;\n            offset += size;\n            chunkSize = size;\n        }\n        else\n        {\n            uboElement.offset = offset;\n            chunkSize += size;\n            offset += size;\n        }\n    }\n\n    offset = Math.ceil(offset / 16) * 16;\n\n    return { uboElements, size: offset };\n}\n\nexport function getUBOData(uniforms: Dict<any>, uniformData: Dict<any>): any[]\n{\n    const usedUniformDatas = [];\n\n    // build..\n    for (const i in uniforms)\n    {\n        if (uniformData[i])\n        {\n            usedUniformDatas.push(uniformData[i]);\n        }\n    }\n\n    // sort them out by index!\n    usedUniformDatas.sort((a, b) => a.index - b.index);\n\n    return usedUniformDatas;\n}\n\nexport function generateUniformBufferSync(\n    group: UniformGroup,\n    uniformData: Dict<any>\n): {size: number, syncFunc: UniformsSyncCallback}\n{\n    if (!group.autoManage)\n    {\n        // if the group is nott automatically managed, we don't need to generate a special function for it...\n        return { size: 0, syncFunc: uboUpdate };\n    }\n\n    const usedUniformDatas = getUBOData(group.uniforms, uniformData);\n\n    const { uboElements, size } = createUBOElements(usedUniformDatas);\n\n    const funcFragments = [`\n    var v = null;\n    var v2 = null;\n    var cv = null;\n    var t = 0;\n    var gl = renderer.gl\n    var index = 0;\n    var data = buffer.data;\n    `];\n\n    for (let i = 0; i < uboElements.length; i++)\n    {\n        const uboElement = uboElements[i];\n        const uniform = group.uniforms[uboElement.data.name];\n\n        const name = uboElement.data.name;\n\n        let parsed = false;\n\n        for (let j = 0; j < uniformParsers.length; j++)\n        {\n            const uniformParser = uniformParsers[j];\n\n            if (uniformParser.codeUbo && uniformParser.test(uboElement.data, uniform))\n            {\n                funcFragments.push(\n                    `offset = ${uboElement.offset / 4};`,\n                    uniformParsers[j].codeUbo(uboElement.data.name, uniform));\n                parsed = true;\n\n                break;\n            }\n        }\n\n        if (!parsed)\n        {\n            if (uboElement.data.size > 1)\n            {\n                const size = mapSize(uboElement.data.type);\n                const rowSize = Math.max(GLSL_TO_STD40_SIZE[uboElement.data.type] / 16, 1);\n                const elementSize = size / rowSize;\n                const remainder = (4 - (elementSize % 4)) % 4;\n\n                funcFragments.push(`\n                cv = ud.${name}.value;\n                v = uv.${name};\n                offset = ${uboElement.offset / 4};\n\n                t = 0;\n\n                for(var i=0; i < ${uboElement.data.size * rowSize}; i++)\n                {\n                    for(var j = 0; j < ${elementSize}; j++)\n                    {\n                        data[offset++] = v[t++];\n                    }\n                    offset += ${remainder};\n                }\n\n                `);\n            }\n            else\n            {\n                const template = UBO_TO_SINGLE_SETTERS[uboElement.data.type];\n\n                funcFragments.push(`\n                cv = ud.${name}.value;\n                v = uv.${name};\n                offset = ${uboElement.offset / 4};\n                ${template};\n                `);\n            }\n        }\n    }\n\n    funcFragments.push(`\n       renderer.buffer.update(buffer);\n    `);\n\n    return {\n        size,\n        // eslint-disable-next-line no-new-func\n        syncFunc: new Function(\n            'ud',\n            'uv',\n            'renderer',\n            'syncData',\n            'buffer',\n            funcFragments.join('\\n')\n        ) as UniformsSyncCallback\n    };\n}\n"], "mappings": ";;;AAWA,SAASA,UAAUC,GAAA,EAAUC,GAAA,EAAUC,SAAA,EAAqBC,SAAA,EAAgBC,MAAA,EAC5E;EACcF,SAAA,CAAAE,MAAA,CAAOC,MAAA,CAAOD,MAAM;AAClC;AAOA,MAAME,qBAAA,GAAsC;IACxCC,KAAA,EAAO;AAAA;AAAA;IAGPC,IAAA,EAAM;AAAA;AAAA;AAAA;IAINC,IAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA;IAMNC,IAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA;IAMNC,IAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAONC,IAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAaNC,IAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA;EAMV;EAEMC,kBAAA,GAAmC;IACrCP,KAAA,EAAQ;IACRC,IAAA,EAAQ;IACRC,IAAA,EAAQ;IACRC,IAAA,EAAQ;IAERK,GAAA,EAAU;IACVC,KAAA,EAAU;IACVC,KAAA,EAAU;IACVC,KAAA,EAAU;IAEVC,IAAA,EAAU;IACVC,KAAA,EAAU;IACVC,KAAA,EAAU;IACVC,KAAA,EAAU;IAEVC,IAAA,EAAU;IACVC,KAAA,EAAU;IACVC,KAAA,EAAU;IACVC,KAAA,EAAU;IAEVf,IAAA,EAAU,KAAK;IACfC,IAAA,EAAU,KAAK;IACfC,IAAA,EAAU,KAAK;EACnB;AAgBO,SAASc,kBAAkBC,WAAA,EAClC;EACI,MAAMC,WAAA,GAA4BD,WAAA,CAAYE,GAAA,CAAKC,IAAA,KAC9C;IACGA,IAAA;IACAC,MAAA,EAAQ;IACRC,OAAA,EAAS;IACTC,KAAA,EAAO;EACT;EAEN,IAAIC,IAAA,GAAO;IACPC,SAAA,GAAY;IACZJ,MAAA,GAAS;EAEb,SAASK,CAAA,GAAI,GAAGA,CAAA,GAAIR,WAAA,CAAYS,MAAA,EAAQD,CAAA,IACxC;IACU,MAAAE,UAAA,GAAaV,WAAA,CAAYQ,CAAC;IAEhC,IAAAF,IAAA,GAAOrB,kBAAA,CAAmByB,UAAA,CAAWR,IAAA,CAAKS,IAAI,GAE1CD,UAAA,CAAWR,IAAA,CAAKI,IAAA,GAAO,MAEvBA,IAAA,GAAOM,IAAA,CAAKC,GAAA,CAAIP,IAAA,EAAM,EAAE,IAAII,UAAA,CAAWR,IAAA,CAAKI,IAAA,GAGhDI,UAAA,CAAWN,OAAA,GAAUE,IAAA,EAKjBC,SAAA,GAAYD,IAAA,KAAS,KAAKC,SAAA,GAAY,IAC1C;MAEU,MAAAO,WAAA,GAAeP,SAAA,GAAYD,IAAA,GAAQ;MAEzCC,SAAA,IAAaO,WAAA,EACbX,MAAA,IAAUW,WAAA;IACd;IAEKP,SAAA,GAAYD,IAAA,GAAQ,MAErBH,MAAA,GAASS,IAAA,CAAKG,IAAA,CAAKZ,MAAA,GAAS,EAAE,IAAI,IAClCO,UAAA,CAAWP,MAAA,GAASA,MAAA,EACpBA,MAAA,IAAUG,IAAA,EACVC,SAAA,GAAYD,IAAA,KAIZI,UAAA,CAAWP,MAAA,GAASA,MAAA,EACpBI,SAAA,IAAaD,IAAA,EACbH,MAAA,IAAUG,IAAA;EAElB;EAES,OAAAH,MAAA,GAAAS,IAAA,CAAKG,IAAA,CAAKZ,MAAA,GAAS,EAAE,IAAI,IAE3B;IAAEH,WAAA;IAAaM,IAAA,EAAMH;EAAO;AACvC;AAEgB,SAAAa,WAAWC,QAAA,EAAqBlB,WAAA,EAChD;EACI,MAAMmB,gBAAA,GAAmB;EAGzB,WAAWV,CAAA,IAAKS,QAAA,EAERlB,WAAA,CAAYS,CAAC,KAEbU,gBAAA,CAAiBC,IAAA,CAAKpB,WAAA,CAAYS,CAAC,CAAC;EAK3B,OAAAU,gBAAA,CAAAE,IAAA,CAAK,CAACC,CAAA,EAAGC,CAAA,KAAMD,CAAA,CAAEE,KAAA,GAAQD,CAAA,CAAEC,KAAK,GAE1CL,gBAAA;AACX;AAEgB,SAAAM,0BACZC,KAAA,EACA1B,WAAA,EAEJ;EACI,IAAI,CAAC0B,KAAA,CAAMC,UAAA,EAGP,OAAO;IAAEpB,IAAA,EAAM;IAAGqB,QAAA,EAAUzD;EAAU;EAG1C,MAAMgD,gBAAA,GAAmBF,UAAA,CAAWS,KAAA,CAAMR,QAAA,EAAUlB,WAAW;IAEzD;MAAEC,WAAA;MAAaM;IAAS,IAAAR,iBAAA,CAAkBoB,gBAAgB;IAE1DU,aAAA,GAAgB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAQtB;EAED,SAASpB,CAAA,GAAI,GAAGA,CAAA,GAAIR,WAAA,CAAYS,MAAA,EAAQD,CAAA,IACxC;IACI,MAAME,UAAA,GAAaV,WAAA,CAAYQ,CAAC;MAC1BqB,OAAA,GAAUJ,KAAA,CAAMR,QAAA,CAASP,UAAA,CAAWR,IAAA,CAAK4B,IAAI;MAE7CA,IAAA,GAAOpB,UAAA,CAAWR,IAAA,CAAK4B,IAAA;IAE7B,IAAIC,MAAA,GAAS;IAEb,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIC,cAAA,CAAexB,MAAA,EAAQuB,CAAA,IAC3C;MACU,MAAAE,aAAA,GAAgBD,cAAA,CAAeD,CAAC;MAEtC,IAAIE,aAAA,CAAcC,OAAA,IAAWD,aAAA,CAAcE,IAAA,CAAK1B,UAAA,CAAWR,IAAA,EAAM2B,OAAO,GACxE;QACkBD,aAAA,CAAAT,IAAA,CACV,YAAYT,UAAA,CAAWP,MAAA,GAAS,CAAC,KACjC8B,cAAA,CAAeD,CAAC,EAAEG,OAAA,CAAQzB,UAAA,CAAWR,IAAA,CAAK4B,IAAA,EAAMD,OAAO,IAC3DE,MAAA,GAAS;QAET;MACJ;IACJ;IAEA,IAAI,CAACA,MAAA,EAEG,IAAArB,UAAA,CAAWR,IAAA,CAAKI,IAAA,GAAO,GAC3B;MACU,MAAA+B,KAAA,GAAOC,OAAA,CAAQ5B,UAAA,CAAWR,IAAA,CAAKS,IAAI;QACnC4B,OAAA,GAAU3B,IAAA,CAAKC,GAAA,CAAI5B,kBAAA,CAAmByB,UAAA,CAAWR,IAAA,CAAKS,IAAI,IAAI,IAAI,CAAC;QACnE6B,WAAA,GAAcH,KAAA,GAAOE,OAAA;QACrBE,SAAA,IAAa,IAAKD,WAAA,GAAc,KAAM;MAE5CZ,aAAA,CAAcT,IAAA,CAAK;AAAA,0BACTW,IAAI;AAAA,yBACLA,IAAI;AAAA,2BACFpB,UAAA,CAAWP,MAAA,GAAS,CAAC;AAAA;AAAA;AAAA;AAAA,mCAIbO,UAAA,CAAWR,IAAA,CAAKI,IAAA,GAAOiC,OAAO;AAAA;AAAA,yCAExBC,WAAW;AAAA;AAAA;AAAA;AAAA,gCAIpBC,SAAS;AAAA;AAAA;AAAA,iBAGxB;IAAA,OAGL;MACI,MAAMC,QAAA,GAAWjE,qBAAA,CAAsBiC,UAAA,CAAWR,IAAA,CAAKS,IAAI;MAE3DiB,aAAA,CAAcT,IAAA,CAAK;AAAA,0BACTW,IAAI;AAAA,yBACLA,IAAI;AAAA,2BACFpB,UAAA,CAAWP,MAAA,GAAS,CAAC;AAAA,kBAC9BuC,QAAQ;AAAA,iBACT;IACL;EAER;EAEA,OAAAd,aAAA,CAAcT,IAAA,CAAK;AAAA;AAAA,KAElB,GAEM;IACHb,IAAA;IAAA;IAEAqB,QAAA,EAAU,IAAIgB,QAAA,CACV,MACA,MACA,YACA,YACA,UACAf,aAAA,CAAcgB,IAAA,CAAK;AAAA,CAAI,CAC3B;EAAA;AAER", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}