{"ast": null, "code": "import { Rectangle, utils, RenderTexture, FORMATS, ExtensionType, extensions } from \"@pixi/core\";\nconst TEMP_RECT = new Rectangle(),\n  BYTES_PER_PIXEL = 4,\n  _Extract = class _Extract2 {\n    /**\n     * @param renderer - A reference to the current renderer\n     */\n    constructor(renderer) {\n      this.renderer = renderer, this._rendererPremultipliedAlpha = !1;\n    }\n    contextChange() {\n      const attributes = this.renderer?.gl.getContextAttributes();\n      this._rendererPremultipliedAlpha = !!(attributes && attributes.alpha && attributes.premultipliedAlpha);\n    }\n    /**\n     * Will return a HTML Image of the target\n     * @param target - A displayObject or renderTexture\n     *  to convert. If left empty will use the main renderer\n     * @param format - Image format, e.g. \"image/jpeg\" or \"image/webp\".\n     * @param quality - JPEG or Webp compression from 0 to 1. Default is 0.92.\n     * @param frame - The frame the extraction is restricted to.\n     * @returns - HTML Image of the target\n     */\n    async image(target, format, quality, frame) {\n      const image = new Image();\n      return image.src = await this.base64(target, format, quality, frame), image;\n    }\n    /**\n     * Will return a base64 encoded string of this target. It works by calling\n     *  `Extract.canvas` and then running toDataURL on that.\n     * @param target - A displayObject or renderTexture\n     *  to convert. If left empty will use the main renderer\n     * @param format - Image format, e.g. \"image/jpeg\" or \"image/webp\".\n     * @param quality - JPEG or Webp compression from 0 to 1. Default is 0.92.\n     * @param frame - The frame the extraction is restricted to.\n     * @returns - A base64 encoded string of the texture.\n     */\n    async base64(target, format, quality, frame) {\n      const canvas = this.canvas(target, frame);\n      if (canvas.toBlob !== void 0) return new Promise((resolve, reject) => {\n        canvas.toBlob(blob => {\n          if (!blob) {\n            reject(new Error(\"ICanvas.toBlob failed!\"));\n            return;\n          }\n          const reader = new FileReader();\n          reader.onload = () => resolve(reader.result), reader.onerror = reject, reader.readAsDataURL(blob);\n        }, format, quality);\n      });\n      if (canvas.toDataURL !== void 0) return canvas.toDataURL(format, quality);\n      if (canvas.convertToBlob !== void 0) {\n        const blob = await canvas.convertToBlob({\n          type: format,\n          quality\n        });\n        return new Promise((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = () => resolve(reader.result), reader.onerror = reject, reader.readAsDataURL(blob);\n        });\n      }\n      throw new Error(\"Extract.base64() requires ICanvas.toDataURL, ICanvas.toBlob, or ICanvas.convertToBlob to be implemented\");\n    }\n    /**\n     * Creates a Canvas element, renders this target to it and then returns it.\n     * @param target - A displayObject or renderTexture\n     *  to convert. If left empty will use the main renderer\n     * @param frame - The frame the extraction is restricted to.\n     * @returns - A Canvas element with the texture rendered on.\n     */\n    canvas(target, frame) {\n      const {\n        pixels,\n        width,\n        height,\n        flipY,\n        premultipliedAlpha\n      } = this._rawPixels(target, frame);\n      flipY && _Extract2._flipY(pixels, width, height), premultipliedAlpha && _Extract2._unpremultiplyAlpha(pixels);\n      const canvasBuffer = new utils.CanvasRenderTarget(width, height, 1),\n        imageData = new ImageData(new Uint8ClampedArray(pixels.buffer), width, height);\n      return canvasBuffer.context.putImageData(imageData, 0, 0), canvasBuffer.canvas;\n    }\n    /**\n     * Will return a one-dimensional array containing the pixel data of the entire texture in RGBA\n     * order, with integer values between 0 and 255 (included).\n     * @param target - A displayObject or renderTexture\n     *  to convert. If left empty will use the main renderer\n     * @param frame - The frame the extraction is restricted to.\n     * @returns - One-dimensional array containing the pixel data of the entire texture\n     */\n    pixels(target, frame) {\n      const {\n        pixels,\n        width,\n        height,\n        flipY,\n        premultipliedAlpha\n      } = this._rawPixels(target, frame);\n      return flipY && _Extract2._flipY(pixels, width, height), premultipliedAlpha && _Extract2._unpremultiplyAlpha(pixels), pixels;\n    }\n    _rawPixels(target, frame) {\n      const renderer = this.renderer;\n      if (!renderer) throw new Error(\"The Extract has already been destroyed\");\n      let resolution,\n        flipY = !1,\n        premultipliedAlpha = !1,\n        renderTexture,\n        generated = !1;\n      target && (target instanceof RenderTexture ? renderTexture = target : (renderTexture = renderer.generateTexture(target, {\n        region: frame,\n        resolution: renderer.resolution,\n        multisample: renderer.multisample\n      }), generated = !0, frame && (TEMP_RECT.width = frame.width, TEMP_RECT.height = frame.height, frame = TEMP_RECT)));\n      const gl = renderer.gl;\n      if (renderTexture) {\n        if (resolution = renderTexture.baseTexture.resolution, frame = frame ?? renderTexture.frame, flipY = !1, premultipliedAlpha = renderTexture.baseTexture.alphaMode > 0 && renderTexture.baseTexture.format === FORMATS.RGBA, !generated) {\n          renderer.renderTexture.bind(renderTexture);\n          const fbo = renderTexture.framebuffer.glFramebuffers[renderer.CONTEXT_UID];\n          fbo.blitFramebuffer && renderer.framebuffer.bind(fbo.blitFramebuffer);\n        }\n      } else resolution = renderer.resolution, frame || (frame = TEMP_RECT, frame.width = renderer.width / resolution, frame.height = renderer.height / resolution), flipY = !0, premultipliedAlpha = this._rendererPremultipliedAlpha, renderer.renderTexture.bind();\n      const width = Math.max(Math.round(frame.width * resolution), 1),\n        height = Math.max(Math.round(frame.height * resolution), 1),\n        pixels = new Uint8Array(BYTES_PER_PIXEL * width * height);\n      return gl.readPixels(Math.round(frame.x * resolution), Math.round(frame.y * resolution), width, height, gl.RGBA, gl.UNSIGNED_BYTE, pixels), generated && renderTexture?.destroy(!0), {\n        pixels,\n        width,\n        height,\n        flipY,\n        premultipliedAlpha\n      };\n    }\n    /** Destroys the extract. */\n    destroy() {\n      this.renderer = null;\n    }\n    static _flipY(pixels, width, height) {\n      const w = width << 2,\n        h = height >> 1,\n        temp = new Uint8Array(w);\n      for (let y = 0; y < h; y++) {\n        const t = y * w,\n          b = (height - y - 1) * w;\n        temp.set(pixels.subarray(t, t + w)), pixels.copyWithin(t, b, b + w), pixels.set(temp, b);\n      }\n    }\n    static _unpremultiplyAlpha(pixels) {\n      pixels instanceof Uint8ClampedArray && (pixels = new Uint8Array(pixels.buffer));\n      const n = pixels.length;\n      for (let i = 0; i < n; i += 4) {\n        const alpha = pixels[i + 3];\n        if (alpha !== 0) {\n          const a = 255.001 / alpha;\n          pixels[i] = pixels[i] * a + 0.5, pixels[i + 1] = pixels[i + 1] * a + 0.5, pixels[i + 2] = pixels[i + 2] * a + 0.5;\n        }\n      }\n    }\n  };\n_Extract.extension = {\n  name: \"extract\",\n  type: ExtensionType.RendererSystem\n};\nlet Extract = _Extract;\nextensions.add(Extract);\nexport { Extract };", "map": {"version": 3, "names": ["TEMP_RECT", "Rectangle", "BYTES_PER_PIXEL", "_Extract", "_Extract2", "constructor", "renderer", "_rendererPremultipliedAlpha", "contextChange", "attributes", "gl", "getContextAttributes", "alpha", "premultipliedAlpha", "image", "target", "format", "quality", "frame", "Image", "src", "base64", "canvas", "toBlob", "Promise", "resolve", "reject", "blob", "Error", "reader", "FileReader", "onload", "result", "onerror", "readAsDataURL", "toDataURL", "convertToBlob", "type", "pixels", "width", "height", "flipY", "_rawPixels", "_flipY", "_unpremultiplyAlpha", "canvasBuffer", "utils", "CanvasRenderTarget", "imageData", "ImageData", "Uint8ClampedArray", "buffer", "context", "putImageData", "resolution", "renderTexture", "generated", "RenderTexture", "generateTexture", "region", "multisample", "baseTexture", "alphaMode", "FORMATS", "RGBA", "bind", "fbo", "framebuffer", "glFramebuffers", "CONTEXT_UID", "blit<PERSON><PERSON>ebuffer", "Math", "max", "round", "Uint8Array", "readPixels", "x", "y", "UNSIGNED_BYTE", "destroy", "w", "h", "temp", "t", "b", "set", "subarray", "copyWithin", "n", "length", "i", "a", "extension", "name", "ExtensionType", "RendererSystem", "Extract", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\extract\\src\\Extract.ts"], "sourcesContent": ["import { extensions, ExtensionType, FORMATS, Rectangle, RenderTexture, utils } from '@pixi/core';\n\nimport type { ExtensionMetadata, ICanvas, ISystem, Renderer } from '@pixi/core';\nimport type { DisplayObject } from '@pixi/display';\n\nconst TEMP_RECT = new Rectangle();\nconst BYTES_PER_PIXEL = 4;\n\nexport interface IExtract\n{\n    image(target?: DisplayObject | RenderTexture, format?: string, quality?: number,\n        frame?: Rectangle): Promise<HTMLImageElement>;\n    base64(target?: DisplayObject | RenderTexture, format?: string, quality?: number,\n        frame?: Rectangle): Promise<string>;\n    canvas(target?: DisplayObject | RenderTexture, frame?: Rectangle): ICanvas;\n    pixels(target?: DisplayObject | RenderTexture, frame?: Rectangle): Uint8Array | Uint8ClampedArray;\n}\n\n/**\n * This class provides renderer-specific plugins for exporting content from a renderer.\n * For instance, these plugins can be used for saving an Image, Canvas element or for exporting the raw image data (pixels).\n *\n * Do not instantiate these plugins directly. It is available from the `renderer.extract` property.\n * @example\n * import { Application, Graphics } from 'pixi.js';\n *\n * // Create a new application (extract will be auto-added to renderer)\n * const app = new Application();\n *\n * // Draw a red circle\n * const graphics = new Graphics()\n *     .beginFill(0xFF0000)\n *     .drawCircle(0, 0, 50);\n *\n * // Render the graphics as an HTMLImageElement\n * const image = await app.renderer.extract.image(graphics);\n * document.body.appendChild(image);\n * @memberof PIXI\n */\n\nexport class Extract implements ISystem, IExtract\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        name: 'extract',\n        type: ExtensionType.RendererSystem,\n    };\n\n    private renderer: Renderer | null;\n\n    /** Does the renderer have alpha and are its color channels stored premultipled by the alpha channel? */\n    private _rendererPremultipliedAlpha: boolean;\n\n    /**\n     * @param renderer - A reference to the current renderer\n     */\n    constructor(renderer: Renderer)\n    {\n        this.renderer = renderer;\n        this._rendererPremultipliedAlpha = false;\n    }\n\n    protected contextChange(): void\n    {\n        const attributes = this.renderer?.gl.getContextAttributes();\n\n        this._rendererPremultipliedAlpha = !!(attributes && attributes.alpha && attributes.premultipliedAlpha);\n    }\n\n    /**\n     * Will return a HTML Image of the target\n     * @param target - A displayObject or renderTexture\n     *  to convert. If left empty will use the main renderer\n     * @param format - Image format, e.g. \"image/jpeg\" or \"image/webp\".\n     * @param quality - JPEG or Webp compression from 0 to 1. Default is 0.92.\n     * @param frame - The frame the extraction is restricted to.\n     * @returns - HTML Image of the target\n     */\n    public async image(target?: DisplayObject | RenderTexture, format?: string, quality?: number,\n        frame?: Rectangle): Promise<HTMLImageElement>\n    {\n        const image = new Image();\n\n        image.src = await this.base64(target, format, quality, frame);\n\n        return image;\n    }\n\n    /**\n     * Will return a base64 encoded string of this target. It works by calling\n     *  `Extract.canvas` and then running toDataURL on that.\n     * @param target - A displayObject or renderTexture\n     *  to convert. If left empty will use the main renderer\n     * @param format - Image format, e.g. \"image/jpeg\" or \"image/webp\".\n     * @param quality - JPEG or Webp compression from 0 to 1. Default is 0.92.\n     * @param frame - The frame the extraction is restricted to.\n     * @returns - A base64 encoded string of the texture.\n     */\n    public async base64(target?: DisplayObject | RenderTexture, format?: string, quality?: number,\n        frame?: Rectangle): Promise<string>\n    {\n        const canvas = this.canvas(target, frame);\n\n        if (canvas.toBlob !== undefined)\n        {\n            return new Promise<string>((resolve, reject) =>\n            {\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                canvas.toBlob!((blob) =>\n                {\n                    if (!blob)\n                    {\n                        reject(new Error('ICanvas.toBlob failed!'));\n\n                        return;\n                    }\n\n                    const reader = new FileReader();\n\n                    reader.onload = () => resolve(reader.result as string);\n                    reader.onerror = reject;\n                    reader.readAsDataURL(blob);\n                }, format, quality);\n            });\n        }\n        if (canvas.toDataURL !== undefined)\n        {\n            return canvas.toDataURL(format, quality);\n        }\n        if (canvas.convertToBlob !== undefined)\n        {\n            const blob = await canvas.convertToBlob({ type: format, quality });\n\n            return new Promise<string>((resolve, reject) =>\n            {\n                const reader = new FileReader();\n\n                reader.onload = () => resolve(reader.result as string);\n                reader.onerror = reject;\n                reader.readAsDataURL(blob);\n            });\n        }\n\n        throw new Error('Extract.base64() requires ICanvas.toDataURL, ICanvas.toBlob, '\n            + 'or ICanvas.convertToBlob to be implemented');\n    }\n\n    /**\n     * Creates a Canvas element, renders this target to it and then returns it.\n     * @param target - A displayObject or renderTexture\n     *  to convert. If left empty will use the main renderer\n     * @param frame - The frame the extraction is restricted to.\n     * @returns - A Canvas element with the texture rendered on.\n     */\n    public canvas(target?: DisplayObject | RenderTexture, frame?: Rectangle): ICanvas\n    {\n        const { pixels, width, height, flipY, premultipliedAlpha } = this._rawPixels(target, frame);\n\n        // Flipping pixels\n        if (flipY)\n        {\n            Extract._flipY(pixels, width, height);\n        }\n\n        if (premultipliedAlpha)\n        {\n            Extract._unpremultiplyAlpha(pixels);\n        }\n\n        const canvasBuffer = new utils.CanvasRenderTarget(width, height, 1);\n\n        // Add the pixels to the canvas\n        const imageData = new ImageData(new Uint8ClampedArray(pixels.buffer), width, height);\n\n        canvasBuffer.context.putImageData(imageData, 0, 0);\n\n        // Send the canvas back\n        return canvasBuffer.canvas;\n    }\n\n    /**\n     * Will return a one-dimensional array containing the pixel data of the entire texture in RGBA\n     * order, with integer values between 0 and 255 (included).\n     * @param target - A displayObject or renderTexture\n     *  to convert. If left empty will use the main renderer\n     * @param frame - The frame the extraction is restricted to.\n     * @returns - One-dimensional array containing the pixel data of the entire texture\n     */\n    public pixels(target?: DisplayObject | RenderTexture, frame?: Rectangle): Uint8Array\n    {\n        const { pixels, width, height, flipY, premultipliedAlpha } = this._rawPixels(target, frame);\n\n        if (flipY)\n        {\n            Extract._flipY(pixels, width, height);\n        }\n\n        if (premultipliedAlpha)\n        {\n            Extract._unpremultiplyAlpha(pixels);\n        }\n\n        return pixels;\n    }\n\n    private _rawPixels(target?: DisplayObject | RenderTexture, frame?: Rectangle): {\n        pixels: Uint8Array, width: number, height: number, flipY: boolean, premultipliedAlpha: boolean\n    }\n    {\n        const renderer = this.renderer;\n\n        if (!renderer)\n        {\n            throw new Error('The Extract has already been destroyed');\n        }\n\n        let resolution;\n        let flipY = false;\n        let premultipliedAlpha = false;\n        let renderTexture;\n        let generated = false;\n\n        if (target)\n        {\n            if (target instanceof RenderTexture)\n            {\n                renderTexture = target;\n            }\n            else\n            {\n                renderTexture = renderer.generateTexture(target, {\n                    region: frame,\n                    resolution: renderer.resolution,\n                    multisample: renderer.multisample\n                });\n                generated = true;\n\n                if (frame)\n                {\n                    TEMP_RECT.width = frame.width;\n                    TEMP_RECT.height = frame.height;\n                    frame = TEMP_RECT;\n                }\n            }\n        }\n\n        const gl = renderer.gl;\n\n        if (renderTexture)\n        {\n            resolution = renderTexture.baseTexture.resolution;\n            frame = frame ?? renderTexture.frame;\n            flipY = false;\n            premultipliedAlpha = renderTexture.baseTexture.alphaMode > 0\n                && renderTexture.baseTexture.format === FORMATS.RGBA;\n\n            if (!generated)\n            {\n                renderer.renderTexture.bind(renderTexture);\n\n                const fbo = renderTexture.framebuffer.glFramebuffers[renderer.CONTEXT_UID];\n\n                if (fbo.blitFramebuffer)\n                {\n                    renderer.framebuffer.bind(fbo.blitFramebuffer);\n                }\n            }\n        }\n        else\n        {\n            resolution = renderer.resolution;\n\n            if (!frame)\n            {\n                frame = TEMP_RECT;\n                frame.width = renderer.width / resolution;\n                frame.height = renderer.height / resolution;\n            }\n\n            flipY = true;\n            premultipliedAlpha = this._rendererPremultipliedAlpha;\n            renderer.renderTexture.bind();\n        }\n\n        const width = Math.max(Math.round(frame.width * resolution), 1);\n        const height = Math.max(Math.round(frame.height * resolution), 1);\n\n        const pixels = new Uint8Array(BYTES_PER_PIXEL * width * height);\n\n        // Read pixels to the array\n        gl.readPixels(\n            Math.round(frame.x * resolution),\n            Math.round(frame.y * resolution),\n            width,\n            height,\n            gl.RGBA,\n            gl.UNSIGNED_BYTE,\n            pixels\n        );\n\n        if (generated)\n        {\n            renderTexture?.destroy(true);\n        }\n\n        return { pixels, width, height, flipY, premultipliedAlpha };\n    }\n\n    /** Destroys the extract. */\n    public destroy(): void\n    {\n        this.renderer = null;\n    }\n\n    private static _flipY(pixels: Uint8Array | Uint8ClampedArray, width: number, height: number): void\n    {\n        const w = width << 2;\n        const h = height >> 1;\n        const temp = new Uint8Array(w);\n\n        for (let y = 0; y < h; y++)\n        {\n            const t = y * w;\n            const b = (height - y - 1) * w;\n\n            temp.set(pixels.subarray(t, t + w));\n            pixels.copyWithin(t, b, b + w);\n            pixels.set(temp, b);\n        }\n    }\n\n    private static _unpremultiplyAlpha(pixels: Uint8Array | Uint8ClampedArray): void\n    {\n        if (pixels instanceof Uint8ClampedArray)\n        {\n            pixels = new Uint8Array(pixels.buffer);\n        }\n\n        const n = pixels.length;\n\n        for (let i = 0; i < n; i += 4)\n        {\n            const alpha = pixels[i + 3];\n\n            if (alpha !== 0)\n            {\n                const a = 255.001 / alpha;\n\n                pixels[i] = (pixels[i] * a) + 0.5;\n                pixels[i + 1] = (pixels[i + 1] * a) + 0.5;\n                pixels[i + 2] = (pixels[i + 2] * a) + 0.5;\n            }\n        }\n    }\n}\n\nextensions.add(Extract);\n"], "mappings": ";AAKA,MAAMA,SAAA,GAAY,IAAIC,SAAA;EAChBC,eAAA,GAAkB;EAkCXC,QAAA,GAAN,MAAMC,SAAA,CACb;IAAA;AAAA;AAAA;IAeIC,YAAYC,QAAA,EACZ;MACS,KAAAA,QAAA,GAAWA,QAAA,EAChB,KAAKC,2BAAA,GAA8B;IACvC;IAEUC,cAAA,EACV;MACI,MAAMC,UAAA,GAAa,KAAKH,QAAA,EAAUI,EAAA,CAAGC,oBAAA,CAAqB;MAE1D,KAAKJ,2BAAA,GAA8B,CAAC,EAAEE,UAAA,IAAcA,UAAA,CAAWG,KAAA,IAASH,UAAA,CAAWI,kBAAA;IACvF;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAWA,MAAaC,MAAMC,MAAA,EAAwCC,MAAA,EAAiBC,OAAA,EACxEC,KAAA,EACJ;MACU,MAAAJ,KAAA,GAAQ,IAAIK,KAAA;MAEZ,OAAAL,KAAA,CAAAM,GAAA,GAAM,MAAM,KAAKC,MAAA,CAAON,MAAA,EAAQC,MAAA,EAAQC,OAAA,EAASC,KAAK,GAErDJ,KAAA;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAYA,MAAaO,OAAON,MAAA,EAAwCC,MAAA,EAAiBC,OAAA,EACzEC,KAAA,EACJ;MACI,MAAMI,MAAA,GAAS,KAAKA,MAAA,CAAOP,MAAA,EAAQG,KAAK;MAExC,IAAII,MAAA,CAAOC,MAAA,KAAW,QAElB,OAAO,IAAIC,OAAA,CAAgB,CAACC,OAAA,EAASC,MAAA,KACrC;QAEWJ,MAAA,CAAAC,MAAA,CAASI,IAAA,IAChB;UACI,IAAI,CAACA,IAAA,EACL;YACWD,MAAA,KAAIE,KAAA,CAAM,wBAAwB,CAAC;YAE1C;UACJ;UAEM,MAAAC,MAAA,GAAS,IAAIC,UAAA;UAEZD,MAAA,CAAAE,MAAA,GAAS,MAAMN,OAAA,CAAQI,MAAA,CAAOG,MAAgB,GACrDH,MAAA,CAAOI,OAAA,GAAUP,MAAA,EACjBG,MAAA,CAAOK,aAAA,CAAcP,IAAI;QAAA,GAC1BX,MAAA,EAAQC,OAAO;MAAA,CACrB;MAEL,IAAIK,MAAA,CAAOa,SAAA,KAAc,QAEd,OAAAb,MAAA,CAAOa,SAAA,CAAUnB,MAAA,EAAQC,OAAO;MAEvC,IAAAK,MAAA,CAAOc,aAAA,KAAkB,QAC7B;QACU,MAAAT,IAAA,GAAO,MAAML,MAAA,CAAOc,aAAA,CAAc;UAAEC,IAAA,EAAMrB,MAAA;UAAQC;QAAA,CAAS;QAEjE,OAAO,IAAIO,OAAA,CAAgB,CAACC,OAAA,EAASC,MAAA,KACrC;UACU,MAAAG,MAAA,GAAS,IAAIC,UAAA;UAEZD,MAAA,CAAAE,MAAA,GAAS,MAAMN,OAAA,CAAQI,MAAA,CAAOG,MAAgB,GACrDH,MAAA,CAAOI,OAAA,GAAUP,MAAA,EACjBG,MAAA,CAAOK,aAAA,CAAcP,IAAI;QAAA,CAC5B;MACL;MAEM,UAAIC,KAAA,CAAM,yGACkC;IACtD;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IASON,OAAOP,MAAA,EAAwCG,KAAA,EACtD;MACU;QAAEoB,MAAA;QAAQC,KAAA;QAAOC,MAAA;QAAQC,KAAA;QAAO5B;MAAuB,SAAK6B,UAAA,CAAW3B,MAAA,EAAQG,KAAK;MAGtFuB,KAAA,IAEArC,SAAA,CAAQuC,MAAA,CAAOL,MAAA,EAAQC,KAAA,EAAOC,MAAM,GAGpC3B,kBAAA,IAEAT,SAAA,CAAQwC,mBAAA,CAAoBN,MAAM;MAGtC,MAAMO,YAAA,GAAe,IAAIC,KAAA,CAAMC,kBAAA,CAAmBR,KAAA,EAAOC,MAAA,EAAQ,CAAC;QAG5DQ,SAAA,GAAY,IAAIC,SAAA,CAAU,IAAIC,iBAAA,CAAkBZ,MAAA,CAAOa,MAAM,GAAGZ,KAAA,EAAOC,MAAM;MAEnF,OAAAK,YAAA,CAAaO,OAAA,CAAQC,YAAA,CAAaL,SAAA,EAAW,GAAG,CAAC,GAG1CH,YAAA,CAAavB,MAAA;IACxB;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAUOgB,OAAOvB,MAAA,EAAwCG,KAAA,EACtD;MACU;QAAEoB,MAAA;QAAQC,KAAA;QAAOC,MAAA;QAAQC,KAAA;QAAO5B;MAAuB,SAAK6B,UAAA,CAAW3B,MAAA,EAAQG,KAAK;MAEtF,OAAAuB,KAAA,IAEArC,SAAA,CAAQuC,MAAA,CAAOL,MAAA,EAAQC,KAAA,EAAOC,MAAM,GAGpC3B,kBAAA,IAEAT,SAAA,CAAQwC,mBAAA,CAAoBN,MAAM,GAG/BA,MAAA;IACX;IAEQI,WAAW3B,MAAA,EAAwCG,KAAA,EAG3D;MACI,MAAMZ,QAAA,GAAW,KAAKA,QAAA;MAEtB,IAAI,CAACA,QAAA,EAEK,UAAIsB,KAAA,CAAM,wCAAwC;MAG5D,IAAI0B,UAAA;QACAb,KAAA,GAAQ;QACR5B,kBAAA,GAAqB;QACrB0C,aAAA;QACAC,SAAA,GAAY;MAEZzC,MAAA,KAEIA,MAAA,YAAkB0C,aAAA,GAElBF,aAAA,GAAgBxC,MAAA,IAIhBwC,aAAA,GAAgBjD,QAAA,CAASoD,eAAA,CAAgB3C,MAAA,EAAQ;QAC7C4C,MAAA,EAAQzC,KAAA;QACRoC,UAAA,EAAYhD,QAAA,CAASgD,UAAA;QACrBM,WAAA,EAAatD,QAAA,CAASsD;MAAA,CACzB,GACDJ,SAAA,GAAY,IAERtC,KAAA,KAEAlB,SAAA,CAAUuC,KAAA,GAAQrB,KAAA,CAAMqB,KAAA,EACxBvC,SAAA,CAAUwC,MAAA,GAAStB,KAAA,CAAMsB,MAAA,EACzBtB,KAAA,GAAQlB,SAAA;MAKpB,MAAMU,EAAA,GAAKJ,QAAA,CAASI,EAAA;MAEhB,IAAA6C,aAAA;QAEA,IAAAD,UAAA,GAAaC,aAAA,CAAcM,WAAA,CAAYP,UAAA,EACvCpC,KAAA,GAAQA,KAAA,IAASqC,aAAA,CAAcrC,KAAA,EAC/BuB,KAAA,GAAQ,IACR5B,kBAAA,GAAqB0C,aAAA,CAAcM,WAAA,CAAYC,SAAA,GAAY,KACpDP,aAAA,CAAcM,WAAA,CAAY7C,MAAA,KAAW+C,OAAA,CAAQC,IAAA,EAEhD,CAACR,SAAA,EACL;UACalD,QAAA,CAAAiD,aAAA,CAAcU,IAAA,CAAKV,aAAa;UAEzC,MAAMW,GAAA,GAAMX,aAAA,CAAcY,WAAA,CAAYC,cAAA,CAAe9D,QAAA,CAAS+D,WAAW;UAErEH,GAAA,CAAII,eAAA,IAEJhE,QAAA,CAAS6D,WAAA,CAAYF,IAAA,CAAKC,GAAA,CAAII,eAAe;QAErD;MAAA,OAIahB,UAAA,GAAAhD,QAAA,CAASgD,UAAA,EAEjBpC,KAAA,KAEDA,KAAA,GAAQlB,SAAA,EACRkB,KAAA,CAAMqB,KAAA,GAAQjC,QAAA,CAASiC,KAAA,GAAQe,UAAA,EAC/BpC,KAAA,CAAMsB,MAAA,GAASlC,QAAA,CAASkC,MAAA,GAASc,UAAA,GAGrCb,KAAA,GAAQ,IACR5B,kBAAA,GAAqB,KAAKN,2BAAA,EAC1BD,QAAA,CAASiD,aAAA,CAAcU,IAAA,CAAK;MAG1B,MAAA1B,KAAA,GAAQgC,IAAA,CAAKC,GAAA,CAAID,IAAA,CAAKE,KAAA,CAAMvD,KAAA,CAAMqB,KAAA,GAAQe,UAAU,GAAG,CAAC;QACxDd,MAAA,GAAS+B,IAAA,CAAKC,GAAA,CAAID,IAAA,CAAKE,KAAA,CAAMvD,KAAA,CAAMsB,MAAA,GAASc,UAAU,GAAG,CAAC;QAE1DhB,MAAA,GAAS,IAAIoC,UAAA,CAAWxE,eAAA,GAAkBqC,KAAA,GAAQC,MAAM;MAG3D,OAAA9B,EAAA,CAAAiE,UAAA,CACCJ,IAAA,CAAKE,KAAA,CAAMvD,KAAA,CAAM0D,CAAA,GAAItB,UAAU,GAC/BiB,IAAA,CAAKE,KAAA,CAAMvD,KAAA,CAAM2D,CAAA,GAAIvB,UAAU,GAC/Bf,KAAA,EACAC,MAAA,EACA9B,EAAA,CAAGsD,IAAA,EACHtD,EAAA,CAAGoE,aAAA,EACHxC,MACJ,GAEIkB,SAAA,IAEAD,aAAA,EAAewB,OAAA,CAAQ,EAAI,GAGxB;QAAEzC,MAAA;QAAQC,KAAA;QAAOC,MAAA;QAAQC,KAAA;QAAO5B;MAAmB;IAC9D;IAAA;IAGOkE,QAAA,EACP;MACI,KAAKzE,QAAA,GAAW;IACpB;IAEA,OAAeqC,OAAOL,MAAA,EAAwCC,KAAA,EAAeC,MAAA,EAC7E;MACU,MAAAwC,CAAA,GAAIzC,KAAA,IAAS;QACb0C,CAAA,GAAIzC,MAAA,IAAU;QACd0C,IAAA,GAAO,IAAIR,UAAA,CAAWM,CAAC;MAE7B,SAASH,CAAA,GAAI,GAAGA,CAAA,GAAII,CAAA,EAAGJ,CAAA,IACvB;QACI,MAAMM,CAAA,GAAIN,CAAA,GAAIG,CAAA;UACRI,CAAA,IAAK5C,MAAA,GAASqC,CAAA,GAAI,KAAKG,CAAA;QAE7BE,IAAA,CAAKG,GAAA,CAAI/C,MAAA,CAAOgD,QAAA,CAASH,CAAA,EAAGA,CAAA,GAAIH,CAAC,CAAC,GAClC1C,MAAA,CAAOiD,UAAA,CAAWJ,CAAA,EAAGC,CAAA,EAAGA,CAAA,GAAIJ,CAAC,GAC7B1C,MAAA,CAAO+C,GAAA,CAAIH,IAAA,EAAME,CAAC;MACtB;IACJ;IAEA,OAAexC,oBAAoBN,MAAA,EACnC;MACQA,MAAA,YAAkBY,iBAAA,KAElBZ,MAAA,GAAS,IAAIoC,UAAA,CAAWpC,MAAA,CAAOa,MAAM;MAGzC,MAAMqC,CAAA,GAAIlD,MAAA,CAAOmD,MAAA;MAEjB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIF,CAAA,EAAGE,CAAA,IAAK,GAC5B;QACU,MAAA9E,KAAA,GAAQ0B,MAAA,CAAOoD,CAAA,GAAI,CAAC;QAE1B,IAAI9E,KAAA,KAAU,GACd;UACI,MAAM+E,CAAA,GAAI,UAAU/E,KAAA;UAEb0B,MAAA,CAAAoD,CAAC,IAAKpD,MAAA,CAAOoD,CAAC,IAAIC,CAAA,GAAK,KAC9BrD,MAAA,CAAOoD,CAAA,GAAI,CAAC,IAAKpD,MAAA,CAAOoD,CAAA,GAAI,CAAC,IAAIC,CAAA,GAAK,KACtCrD,MAAA,CAAOoD,CAAA,GAAI,CAAC,IAAKpD,MAAA,CAAOoD,CAAA,GAAI,CAAC,IAAIC,CAAA,GAAK;QAC1C;MACJ;IACJ;EACJ;AA1TaxF,QAAA,CAGFyF,SAAA,GAA+B;EAClCC,IAAA,EAAM;EACNxD,IAAA,EAAMyD,aAAA,CAAcC;AACxB;AANG,IAAMC,OAAA,GAAN7F,QAAA;AA4TP8F,UAAA,CAAWC,GAAA,CAAIF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}