{"ast": null, "code": "import { Runner } from \"@pixi/runner\";\nimport { Program } from \"./Program.mjs\";\nimport { UniformGroup } from \"./UniformGroup.mjs\";\nclass Shader {\n  /**\n   * @param program - The program the shader will use.\n   * @param uniforms - Custom uniforms to use to augment the built-in ones.\n   */\n  constructor(program, uniforms) {\n    this.uniformBindCount = 0, this.program = program, uniforms ? uniforms instanceof UniformGroup ? this.uniformGroup = uniforms : this.uniformGroup = new UniformGroup(uniforms) : this.uniformGroup = new UniformGroup({}), this.disposeRunner = new Runner(\"disposeShader\");\n  }\n  // TODO move to shader system..\n  checkUniformExists(name, group) {\n    if (group.uniforms[name]) return !0;\n    for (const i in group.uniforms) {\n      const uniform = group.uniforms[i];\n      if (uniform.group === !0 && this.checkUniformExists(name, uniform)) return !0;\n    }\n    return !1;\n  }\n  destroy() {\n    this.uniformGroup = null, this.disposeRunner.emit(this), this.disposeRunner.destroy();\n  }\n  /**\n   * Shader uniform values, shortcut for `uniformGroup.uniforms`.\n   * @readonly\n   */\n  get uniforms() {\n    return this.uniformGroup.uniforms;\n  }\n  /**\n   * A short hand function to create a shader based of a vertex and fragment shader.\n   * @param vertexSrc - The source of the vertex shader.\n   * @param fragmentSrc - The source of the fragment shader.\n   * @param uniforms - Custom uniforms to use to augment the built-in ones.\n   * @returns A shiny new PixiJS shader!\n   */\n  static from(vertexSrc, fragmentSrc, uniforms) {\n    const program = Program.from(vertexSrc, fragmentSrc);\n    return new Shader(program, uniforms);\n  }\n}\nexport { Shader };", "map": {"version": 3, "names": ["Shader", "constructor", "program", "uniforms", "uniformBindCount", "UniformGroup", "uniformGroup", "dispose<PERSON><PERSON><PERSON>", "Runner", "checkUniformExists", "name", "group", "i", "uniform", "destroy", "emit", "from", "vertexSrc", "fragmentSrc", "Program"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\shader\\Shader.ts"], "sourcesContent": ["import { Runner } from '@pixi/runner';\nimport { Program } from './Program';\nimport { UniformGroup } from './UniformGroup';\n\nimport type { Dict } from '@pixi/utils';\n\n/**\n * A helper class for shaders.\n * @memberof PIXI\n */\nexport class Shader\n{\n    /** Program that the shader uses. */\n    public program: Program;\n    public uniformGroup: UniformGroup;\n\n    /**\n     * Used internally to bind uniform buffer objects.\n     * @ignore\n     */\n    uniformBindCount = 0;\n\n    disposeRunner: Runner;\n\n    /**\n     * @param program - The program the shader will use.\n     * @param uniforms - Custom uniforms to use to augment the built-in ones.\n     */\n    constructor(program: Program, uniforms?: Dict<any>)\n    {\n        this.program = program;\n\n        // lets see whats been passed in\n        // uniforms should be converted to a uniform group\n        if (uniforms)\n        {\n            if (uniforms instanceof UniformGroup)\n            {\n                this.uniformGroup = uniforms;\n            }\n            else\n            {\n                this.uniformGroup = new UniformGroup(uniforms);\n            }\n        }\n        else\n        {\n            this.uniformGroup = new UniformGroup({});\n        }\n\n        this.disposeRunner = new Runner('disposeShader');\n    }\n\n    // TODO move to shader system..\n    checkUniformExists(name: string, group: UniformGroup): boolean\n    {\n        if (group.uniforms[name])\n        {\n            return true;\n        }\n\n        for (const i in group.uniforms)\n        {\n            const uniform = group.uniforms[i];\n\n            if (uniform.group === true) // strict check to desambiguate from Array.group\n            {\n                if (this.checkUniformExists(name, uniform))\n                {\n                    return true;\n                }\n            }\n        }\n\n        return false;\n    }\n\n    destroy(): void\n    {\n        // usage count on programs?\n        // remove if not used!\n        this.uniformGroup = null;\n\n        this.disposeRunner.emit(this);\n        this.disposeRunner.destroy();\n    }\n\n    /**\n     * Shader uniform values, shortcut for `uniformGroup.uniforms`.\n     * @readonly\n     */\n    get uniforms(): Dict<any>\n    {\n        return this.uniformGroup.uniforms;\n    }\n\n    /**\n     * A short hand function to create a shader based of a vertex and fragment shader.\n     * @param vertexSrc - The source of the vertex shader.\n     * @param fragmentSrc - The source of the fragment shader.\n     * @param uniforms - Custom uniforms to use to augment the built-in ones.\n     * @returns A shiny new PixiJS shader!\n     */\n    static from(vertexSrc?: string, fragmentSrc?: string, uniforms?: Dict<any>): Shader\n    {\n        const program = Program.from(vertexSrc, fragmentSrc);\n\n        return new Shader(program, uniforms);\n    }\n}\n"], "mappings": ";;;AAUO,MAAMA,MAAA,CACb;EAAA;AAAA;AAAA;AAAA;EAiBIC,YAAYC,OAAA,EAAkBC,QAAA,EAC9B;IATmB,KAAAC,gBAAA,MAUV,KAAAF,OAAA,GAAUA,OAAA,EAIXC,QAAA,GAEIA,QAAA,YAAoBE,YAAA,GAEpB,KAAKC,YAAA,GAAeH,QAAA,GAIpB,KAAKG,YAAA,GAAe,IAAID,YAAA,CAAaF,QAAQ,IAKjD,KAAKG,YAAA,GAAe,IAAID,YAAA,CAAa,EAAE,GAG3C,KAAKE,aAAA,GAAgB,IAAIC,MAAA,CAAO,eAAe;EACnD;EAAA;EAGAC,mBAAmBC,IAAA,EAAcC,KAAA,EACjC;IACQ,IAAAA,KAAA,CAAMR,QAAA,CAASO,IAAI,GAEZ;IAGA,WAAAE,CAAA,IAAKD,KAAA,CAAMR,QAAA,EACtB;MACU,MAAAU,OAAA,GAAUF,KAAA,CAAMR,QAAA,CAASS,CAAC;MAEhC,IAAIC,OAAA,CAAQF,KAAA,KAAU,MAEd,KAAKF,kBAAA,CAAmBC,IAAA,EAAMG,OAAO,GAE9B;IAGnB;IAEO;EACX;EAEAC,QAAA,EACA;IAGS,KAAAR,YAAA,GAAe,MAEpB,KAAKC,aAAA,CAAcQ,IAAA,CAAK,IAAI,GAC5B,KAAKR,aAAA,CAAcO,OAAA,CAAQ;EAC/B;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIX,SAAA,EACJ;IACI,OAAO,KAAKG,YAAA,CAAaH,QAAA;EAC7B;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASA,OAAOa,KAAKC,SAAA,EAAoBC,WAAA,EAAsBf,QAAA,EACtD;IACI,MAAMD,OAAA,GAAUiB,OAAA,CAAQH,IAAA,CAAKC,SAAA,EAAWC,WAAW;IAE5C,WAAIlB,MAAA,CAAOE,OAAA,EAASC,QAAQ;EACvC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}