{"ast": null, "code": "import { Matrix } from \"@pixi/math\";\nimport { TextureMatrix } from \"../../textures/TextureMatrix.mjs\";\nimport { Filter } from \"../Filter.mjs\";\nimport fragment from \"./spriteMaskFilter.frag.mjs\";\nimport vertex from \"./spriteMaskFilter.vert.mjs\";\nclass SpriteMaskFilter extends Filter {\n  /** @ignore */\n  constructor(vertexSrc, fragmentSrc, uniforms) {\n    let sprite = null;\n    typeof vertexSrc != \"string\" && fragmentSrc === void 0 && uniforms === void 0 && (sprite = vertexSrc, vertexSrc = void 0, fragmentSrc = void 0, uniforms = void 0), super(vertexSrc || vertex, fragmentSrc || fragment, uniforms), this.maskSprite = sprite, this.maskMatrix = new Matrix();\n  }\n  /**\n   * Sprite mask\n   * @type {PIXI.DisplayObject}\n   */\n  get maskSprite() {\n    return this._maskSprite;\n  }\n  set maskSprite(value) {\n    this._maskSprite = value, this._maskSprite && (this._maskSprite.renderable = !1);\n  }\n  /**\n   * Applies the filter\n   * @param filterManager - The renderer to retrieve the filter from\n   * @param input - The input render target.\n   * @param output - The target to output to.\n   * @param clearMode - Should the output be cleared before rendering to it.\n   */\n  apply(filterManager, input, output, clearMode) {\n    const maskSprite = this._maskSprite,\n      tex = maskSprite._texture;\n    tex.valid && (tex.uvMatrix || (tex.uvMatrix = new TextureMatrix(tex, 0)), tex.uvMatrix.update(), this.uniforms.npmAlpha = tex.baseTexture.alphaMode ? 0 : 1, this.uniforms.mask = tex, this.uniforms.otherMatrix = filterManager.calculateSpriteMatrix(this.maskMatrix, maskSprite).prepend(tex.uvMatrix.mapCoord), this.uniforms.alpha = maskSprite.worldAlpha, this.uniforms.maskClamp = tex.uvMatrix.uClampFrame, filterManager.applyFilter(this, input, output, clearMode));\n  }\n}\nexport { SpriteMaskFilter };", "map": {"version": 3, "names": ["SpriteMaskFilter", "Filter", "constructor", "vertexSrc", "fragmentSrc", "uniforms", "sprite", "vertex", "fragment", "maskSprite", "maskMatrix", "Matrix", "_maskSprite", "value", "renderable", "apply", "filterManager", "input", "output", "clearMode", "tex", "_texture", "valid", "uvMatrix", "TextureMatrix", "update", "npmAlpha", "baseTexture", "alphaMode", "mask", "otherMatrix", "calculateSpriteMatrix", "prepend", "mapCoord", "alpha", "worldAlpha", "maskClamp", "uClampFrame", "applyFilter"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\filters\\spriteMask\\SpriteMaskFilter.ts"], "sourcesContent": ["import { Matrix } from '@pixi/math';\nimport { TextureMatrix } from '../../textures/TextureMatrix';\nimport { Filter } from '../Filter';\nimport fragment from './spriteMaskFilter.frag';\nimport vertex from './spriteMaskFilter.vert';\n\nimport type { CLEAR_MODES } from '@pixi/constants';\nimport type { Point } from '@pixi/math';\nimport type { Dict } from '@pixi/utils';\nimport type { IMaskTarget } from '../../mask/MaskData';\nimport type { RenderTexture } from '../../renderTexture/RenderTexture';\nimport type { Texture } from '../../textures/Texture';\nimport type { FilterSystem } from '../FilterSystem';\n\nexport interface ISpriteMaskTarget extends IMaskTarget\n{\n    _texture: Texture;\n    worldAlpha: number;\n    anchor: Point;\n}\n\nexport interface ISpriteMaskFilter extends Filter\n{\n    maskSprite: IMaskTarget;\n}\n\n/**\n * This handles a Sprite acting as a mask, as opposed to a Graphic.\n *\n * WebGL only.\n * @memberof PIXI\n */\nexport class SpriteMaskFilter extends Filter\n{\n    /** @private */\n    _maskSprite: IMaskTarget;\n\n    /** Mask matrix */\n    maskMatrix: Matrix;\n\n    /**\n     * @param {PIXI.Sprite} sprite - The target sprite.\n     */\n    constructor(sprite: IMaskTarget);\n\n    /**\n     * @param vertexSrc - The source of the vertex shader.\n     * @param fragmentSrc - The source of the fragment shader.\n     * @param uniforms - Custom uniforms to use to augment the built-in ones.\n     */\n    constructor(vertexSrc?: string, fragmentSrc?: string, uniforms?: Dict<any>);\n\n    /** @ignore */\n    constructor(vertexSrc?: string | IMaskTarget, fragmentSrc?: string, uniforms?: Dict<any>)\n    {\n        let sprite = null;\n\n        if (typeof vertexSrc !== 'string' && fragmentSrc === undefined && uniforms === undefined)\n        {\n            sprite = vertexSrc as IMaskTarget;\n            vertexSrc = undefined;\n            fragmentSrc = undefined;\n            uniforms = undefined;\n        }\n\n        super(vertexSrc as string || vertex, fragmentSrc || fragment, uniforms);\n\n        this.maskSprite = sprite;\n        this.maskMatrix = new Matrix();\n    }\n\n    /**\n     * Sprite mask\n     * @type {PIXI.DisplayObject}\n     */\n    get maskSprite(): IMaskTarget\n    {\n        return this._maskSprite;\n    }\n\n    set maskSprite(value: IMaskTarget)\n    {\n        this._maskSprite = value;\n\n        if (this._maskSprite)\n        {\n            this._maskSprite.renderable = false;\n        }\n    }\n\n    /**\n     * Applies the filter\n     * @param filterManager - The renderer to retrieve the filter from\n     * @param input - The input render target.\n     * @param output - The target to output to.\n     * @param clearMode - Should the output be cleared before rendering to it.\n     */\n    apply(filterManager: FilterSystem, input: RenderTexture, output: RenderTexture, clearMode: CLEAR_MODES): void\n    {\n        const maskSprite = this._maskSprite as ISpriteMaskTarget;\n        const tex = maskSprite._texture;\n\n        if (!tex.valid)\n        {\n            return;\n        }\n        if (!tex.uvMatrix)\n        {\n            // margin = 0.0, let it bleed a bit, shader code becomes easier\n            // assuming that atlas textures were made with 1-pixel padding\n            tex.uvMatrix = new TextureMatrix(tex, 0.0);\n        }\n        tex.uvMatrix.update();\n\n        this.uniforms.npmAlpha = tex.baseTexture.alphaMode ? 0.0 : 1.0;\n        this.uniforms.mask = tex;\n        // get _normalized sprite texture coords_ and convert them to _normalized atlas texture coords_ with `prepend`\n        this.uniforms.otherMatrix = filterManager.calculateSpriteMatrix(this.maskMatrix, maskSprite)\n            .prepend(tex.uvMatrix.mapCoord);\n        this.uniforms.alpha = maskSprite.worldAlpha;\n        this.uniforms.maskClamp = tex.uvMatrix.uClampFrame;\n\n        filterManager.applyFilter(this, input, output, clearMode);\n    }\n}\n"], "mappings": ";;;;;AAgCO,MAAMA,gBAAA,SAAyBC,MAAA,CACtC;EAAA;EAoBIC,YAAYC,SAAA,EAAkCC,WAAA,EAAsBC,QAAA,EACpE;IACI,IAAIC,MAAA,GAAS;IAET,OAAOH,SAAA,IAAc,YAAYC,WAAA,KAAgB,UAAaC,QAAA,KAAa,WAE3EC,MAAA,GAASH,SAAA,EACTA,SAAA,GAAY,QACZC,WAAA,GAAc,QACdC,QAAA,GAAW,SAGf,MAAMF,SAAA,IAAuBI,MAAA,EAAQH,WAAA,IAAeI,QAAA,EAAUH,QAAQ,GAEtE,KAAKI,UAAA,GAAaH,MAAA,EAClB,KAAKI,UAAA,GAAa,IAAIC,MAAA;EAC1B;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIF,WAAA,EACJ;IACI,OAAO,KAAKG,WAAA;EAChB;EAEA,IAAIH,WAAWI,KAAA,EACf;IACI,KAAKD,WAAA,GAAcC,KAAA,EAEf,KAAKD,WAAA,KAEL,KAAKA,WAAA,CAAYE,UAAA,GAAa;EAEtC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAC,MAAMC,aAAA,EAA6BC,KAAA,EAAsBC,MAAA,EAAuBC,SAAA,EAChF;IACI,MAAMV,UAAA,GAAa,KAAKG,WAAA;MAClBQ,GAAA,GAAMX,UAAA,CAAWY,QAAA;IAElBD,GAAA,CAAIE,KAAA,KAIJF,GAAA,CAAIG,QAAA,KAILH,GAAA,CAAIG,QAAA,GAAW,IAAIC,aAAA,CAAcJ,GAAA,EAAK,CAAG,IAE7CA,GAAA,CAAIG,QAAA,CAASE,MAAA,IAEb,KAAKpB,QAAA,CAASqB,QAAA,GAAWN,GAAA,CAAIO,WAAA,CAAYC,SAAA,GAAY,IAAM,GAC3D,KAAKvB,QAAA,CAASwB,IAAA,GAAOT,GAAA,EAErB,KAAKf,QAAA,CAASyB,WAAA,GAAcd,aAAA,CAAce,qBAAA,CAAsB,KAAKrB,UAAA,EAAYD,UAAU,EACtFuB,OAAA,CAAQZ,GAAA,CAAIG,QAAA,CAASU,QAAQ,GAClC,KAAK5B,QAAA,CAAS6B,KAAA,GAAQzB,UAAA,CAAW0B,UAAA,EACjC,KAAK9B,QAAA,CAAS+B,SAAA,GAAYhB,GAAA,CAAIG,QAAA,CAASc,WAAA,EAEvCrB,aAAA,CAAcsB,WAAA,CAAY,MAAMrB,KAAA,EAAOC,MAAA,EAAQC,SAAS;EAC5D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}