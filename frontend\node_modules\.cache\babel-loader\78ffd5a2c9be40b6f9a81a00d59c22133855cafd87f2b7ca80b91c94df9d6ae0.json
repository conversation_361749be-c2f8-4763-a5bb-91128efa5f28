{"ast": null, "code": "import { ExtensionType, extensions } from \"@pixi/extensions\";\nimport { settings } from \"@pixi/settings\";\nimport { AbstractMaskSystem } from \"./AbstractMaskSystem.mjs\";\nclass StencilSystem extends AbstractMaskSystem {\n  /**\n   * @param renderer - The renderer this System works for.\n   */\n  constructor(renderer) {\n    super(renderer), this.glConst = settings.ADAPTER.getWebGLRenderingContext().STENCIL_TEST;\n  }\n  getStackLength() {\n    const maskData = this.maskStack[this.maskStack.length - 1];\n    return maskData ? maskData._stencilCounter : 0;\n  }\n  /**\n   * Applies the Mask and adds it to the current stencil stack.\n   * @param maskData - The mask data\n   */\n  push(maskData) {\n    const maskObject = maskData.maskObject,\n      {\n        gl\n      } = this.renderer,\n      prevMaskCount = maskData._stencilCounter;\n    prevMaskCount === 0 && (this.renderer.framebuffer.forceStencil(), gl.clearStencil(0), gl.clear(gl.STENCIL_BUFFER_BIT), gl.enable(gl.STENCIL_TEST)), maskData._stencilCounter++;\n    const colorMask = maskData._colorMask;\n    colorMask !== 0 && (maskData._colorMask = 0, gl.colorMask(!1, !1, !1, !1)), gl.stencilFunc(gl.EQUAL, prevMaskCount, 4294967295), gl.stencilOp(gl.KEEP, gl.KEEP, gl.INCR), maskObject.renderable = !0, maskObject.render(this.renderer), this.renderer.batch.flush(), maskObject.renderable = !1, colorMask !== 0 && (maskData._colorMask = colorMask, gl.colorMask((colorMask & 1) !== 0, (colorMask & 2) !== 0, (colorMask & 4) !== 0, (colorMask & 8) !== 0)), this._useCurrent();\n  }\n  /**\n   * Pops stencil mask. MaskData is already removed from stack\n   * @param {PIXI.DisplayObject} maskObject - object of popped mask data\n   */\n  pop(maskObject) {\n    const gl = this.renderer.gl;\n    if (this.getStackLength() === 0) gl.disable(gl.STENCIL_TEST);else {\n      const maskData = this.maskStack.length !== 0 ? this.maskStack[this.maskStack.length - 1] : null,\n        colorMask = maskData ? maskData._colorMask : 15;\n      colorMask !== 0 && (maskData._colorMask = 0, gl.colorMask(!1, !1, !1, !1)), gl.stencilOp(gl.KEEP, gl.KEEP, gl.DECR), maskObject.renderable = !0, maskObject.render(this.renderer), this.renderer.batch.flush(), maskObject.renderable = !1, colorMask !== 0 && (maskData._colorMask = colorMask, gl.colorMask((colorMask & 1) !== 0, (colorMask & 2) !== 0, (colorMask & 4) !== 0, (colorMask & 8) !== 0)), this._useCurrent();\n    }\n  }\n  /**\n   * Setup renderer to use the current stencil data.\n   * @private\n   */\n  _useCurrent() {\n    const gl = this.renderer.gl;\n    gl.stencilFunc(gl.EQUAL, this.getStackLength(), 4294967295), gl.stencilOp(gl.KEEP, gl.KEEP, gl.KEEP);\n  }\n}\nStencilSystem.extension = {\n  type: ExtensionType.RendererSystem,\n  name: \"stencil\"\n};\nextensions.add(StencilSystem);\nexport { StencilSystem };", "map": {"version": 3, "names": ["StencilSystem", "AbstractMaskSystem", "constructor", "renderer", "glConst", "settings", "ADAPTER", "getWebGLRenderingContext", "STENCIL_TEST", "getStackLength", "maskData", "maskStack", "length", "_stencilCounter", "push", "maskObject", "gl", "prevMaskCount", "framebuffer", "forceStencil", "clearStencil", "clear", "STENCIL_BUFFER_BIT", "enable", "colorMask", "_colorMask", "stencil<PERSON>unc", "EQUAL", "stencilOp", "KEEP", "INCR", "renderable", "render", "batch", "flush", "_useCurrent", "pop", "disable", "DECR", "extension", "type", "ExtensionType", "RendererSystem", "name", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\mask\\StencilSystem.ts"], "sourcesContent": ["import { extensions, ExtensionType } from '@pixi/extensions';\nimport { settings } from '@pixi/settings';\nimport { AbstractMaskSystem } from './AbstractMaskSystem';\n\nimport type { ExtensionMetadata } from '@pixi/extensions';\nimport type { Renderer } from '../Renderer';\nimport type { IMaskTarget, MaskData } from './MaskData';\n\n/**\n * System plugin to the renderer to manage stencils (used for masks).\n * @memberof PIXI\n */\nexport class StencilSystem extends AbstractMaskSystem\n{\n    /** @ignore */\n    static extension: ExtensionMetadata = {\n        type: ExtensionType.RendererSystem,\n        name: 'stencil',\n    };\n\n    /**\n     * @param renderer - The renderer this System works for.\n     */\n    constructor(renderer: Renderer)\n    {\n        super(renderer);\n\n        this.glConst = settings.ADAPTER.getWebGLRenderingContext().STENCIL_TEST;\n    }\n\n    getStackLength(): number\n    {\n        const maskData = this.maskStack[this.maskStack.length - 1];\n\n        if (maskData)\n        {\n            return maskData._stencilCounter;\n        }\n\n        return 0;\n    }\n\n    /**\n     * Applies the Mask and adds it to the current stencil stack.\n     * @param maskData - The mask data\n     */\n    push(maskData: MaskData): void\n    {\n        const maskObject = maskData.maskObject;\n        const { gl } = this.renderer;\n        const prevMaskCount = maskData._stencilCounter;\n\n        if (prevMaskCount === 0)\n        {\n            // force use stencil texture in current framebuffer\n            this.renderer.framebuffer.forceStencil();\n            gl.clearStencil(0);\n            gl.clear(gl.STENCIL_BUFFER_BIT);\n            gl.enable(gl.STENCIL_TEST);\n        }\n\n        maskData._stencilCounter++;\n\n        const colorMask = maskData._colorMask;\n\n        if (colorMask !== 0)\n        {\n            maskData._colorMask = 0;\n            gl.colorMask(false, false, false, false);\n        }\n\n        // Increment the reference stencil value where the new mask overlaps with the old ones.\n        gl.stencilFunc(gl.EQUAL, prevMaskCount, 0xFFFFFFFF);\n        gl.stencilOp(gl.KEEP, gl.KEEP, gl.INCR);\n\n        maskObject.renderable = true;\n        maskObject.render(this.renderer);\n        this.renderer.batch.flush();\n        maskObject.renderable = false;\n\n        if (colorMask !== 0)\n        {\n            maskData._colorMask = colorMask;\n            gl.colorMask(\n                (colorMask & 1) !== 0,\n                (colorMask & 2) !== 0,\n                (colorMask & 4) !== 0,\n                (colorMask & 8) !== 0\n            );\n        }\n\n        this._useCurrent();\n    }\n\n    /**\n     * Pops stencil mask. MaskData is already removed from stack\n     * @param {PIXI.DisplayObject} maskObject - object of popped mask data\n     */\n    pop(maskObject: IMaskTarget): void\n    {\n        const gl = this.renderer.gl;\n\n        if (this.getStackLength() === 0)\n        {\n            // the stack is empty!\n            gl.disable(gl.STENCIL_TEST);\n        }\n        else\n        {\n            const maskData = this.maskStack.length !== 0 ? this.maskStack[this.maskStack.length - 1] : null;\n            const colorMask = maskData ? maskData._colorMask : 0xf;\n\n            if (colorMask !== 0)\n            {\n                maskData._colorMask = 0;\n                gl.colorMask(false, false, false, false);\n            }\n\n            // Decrement the reference stencil value where the popped mask overlaps with the other ones\n            gl.stencilOp(gl.KEEP, gl.KEEP, gl.DECR);\n\n            maskObject.renderable = true;\n            maskObject.render(this.renderer);\n            this.renderer.batch.flush();\n            maskObject.renderable = false;\n\n            if (colorMask !== 0)\n            {\n                maskData._colorMask = colorMask;\n                gl.colorMask(\n                    (colorMask & 0x1) !== 0,\n                    (colorMask & 0x2) !== 0,\n                    (colorMask & 0x4) !== 0,\n                    (colorMask & 0x8) !== 0\n                );\n            }\n\n            this._useCurrent();\n        }\n    }\n\n    /**\n     * Setup renderer to use the current stencil data.\n     * @private\n     */\n    _useCurrent(): void\n    {\n        const gl = this.renderer.gl;\n\n        gl.stencilFunc(gl.EQUAL, this.getStackLength(), 0xFFFFFFFF);\n        gl.stencilOp(gl.KEEP, gl.KEEP, gl.KEEP);\n    }\n}\n\nextensions.add(StencilSystem);\n"], "mappings": ";;;AAYO,MAAMA,aAAA,SAAsBC,kBAAA,CACnC;EAAA;AAAA;AAAA;EAUIC,YAAYC,QAAA,EACZ;IACI,MAAMA,QAAQ,GAEd,KAAKC,OAAA,GAAUC,QAAA,CAASC,OAAA,CAAQC,wBAAA,CAA2B,EAAAC,YAAA;EAC/D;EAEAC,eAAA,EACA;IACI,MAAMC,QAAA,GAAW,KAAKC,SAAA,CAAU,KAAKA,SAAA,CAAUC,MAAA,GAAS,CAAC;IAErD,OAAAF,QAAA,GAEOA,QAAA,CAASG,eAAA,GAGb;EACX;EAAA;AAAA;AAAA;AAAA;EAMAC,KAAKJ,QAAA,EACL;IACU,MAAAK,UAAA,GAAaL,QAAA,CAASK,UAAA;MACtB;QAAEC;MAAA,IAAO,KAAKb,QAAA;MACdc,aAAA,GAAgBP,QAAA,CAASG,eAAA;IAE3BI,aAAA,KAAkB,MAGlB,KAAKd,QAAA,CAASe,WAAA,CAAYC,YAAA,CAAa,GACvCH,EAAA,CAAGI,YAAA,CAAa,CAAC,GACjBJ,EAAA,CAAGK,KAAA,CAAML,EAAA,CAAGM,kBAAkB,GAC9BN,EAAA,CAAGO,MAAA,CAAOP,EAAA,CAAGR,YAAY,IAG7BE,QAAA,CAASG,eAAA;IAET,MAAMW,SAAA,GAAYd,QAAA,CAASe,UAAA;IAEvBD,SAAA,KAAc,MAEdd,QAAA,CAASe,UAAA,GAAa,GACtBT,EAAA,CAAGQ,SAAA,CAAU,IAAO,IAAO,IAAO,EAAK,IAI3CR,EAAA,CAAGU,WAAA,CAAYV,EAAA,CAAGW,KAAA,EAAOV,aAAA,EAAe,UAAU,GAClDD,EAAA,CAAGY,SAAA,CAAUZ,EAAA,CAAGa,IAAA,EAAMb,EAAA,CAAGa,IAAA,EAAMb,EAAA,CAAGc,IAAI,GAEtCf,UAAA,CAAWgB,UAAA,GAAa,IACxBhB,UAAA,CAAWiB,MAAA,CAAO,KAAK7B,QAAQ,GAC/B,KAAKA,QAAA,CAAS8B,KAAA,CAAMC,KAAA,CACpB,GAAAnB,UAAA,CAAWgB,UAAA,GAAa,IAEpBP,SAAA,KAAc,MAEdd,QAAA,CAASe,UAAA,GAAaD,SAAA,EACtBR,EAAA,CAAGQ,SAAA,EACEA,SAAA,GAAY,OAAO,IACnBA,SAAA,GAAY,OAAO,IACnBA,SAAA,GAAY,OAAO,IACnBA,SAAA,GAAY,OAAO,KAI5B,KAAKW,WAAA;EACT;EAAA;AAAA;AAAA;AAAA;EAMAC,IAAIrB,UAAA,EACJ;IACU,MAAAC,EAAA,GAAK,KAAKb,QAAA,CAASa,EAAA;IAErB,SAAKP,cAAA,OAAqB,GAGvBO,EAAA,CAAAqB,OAAA,CAAQrB,EAAA,CAAGR,YAAY,OAG9B;MACI,MAAME,QAAA,GAAW,KAAKC,SAAA,CAAUC,MAAA,KAAW,IAAI,KAAKD,SAAA,CAAU,KAAKA,SAAA,CAAUC,MAAA,GAAS,CAAC,IAAI;QACrFY,SAAA,GAAYd,QAAA,GAAWA,QAAA,CAASe,UAAA,GAAa;MAE/CD,SAAA,KAAc,MAEdd,QAAA,CAASe,UAAA,GAAa,GACtBT,EAAA,CAAGQ,SAAA,CAAU,IAAO,IAAO,IAAO,EAAK,IAI3CR,EAAA,CAAGY,SAAA,CAAUZ,EAAA,CAAGa,IAAA,EAAMb,EAAA,CAAGa,IAAA,EAAMb,EAAA,CAAGsB,IAAI,GAEtCvB,UAAA,CAAWgB,UAAA,GAAa,IACxBhB,UAAA,CAAWiB,MAAA,CAAO,KAAK7B,QAAQ,GAC/B,KAAKA,QAAA,CAAS8B,KAAA,CAAMC,KAAA,CACpB,GAAAnB,UAAA,CAAWgB,UAAA,GAAa,IAEpBP,SAAA,KAAc,MAEdd,QAAA,CAASe,UAAA,GAAaD,SAAA,EACtBR,EAAA,CAAGQ,SAAA,EACEA,SAAA,GAAY,OAAS,IACrBA,SAAA,GAAY,OAAS,IACrBA,SAAA,GAAY,OAAS,IACrBA,SAAA,GAAY,OAAS,KAI9B,KAAKW,WAAA;IACT;EACJ;EAAA;AAAA;AAAA;AAAA;EAMAA,YAAA,EACA;IACU,MAAAnB,EAAA,GAAK,KAAKb,QAAA,CAASa,EAAA;IAEzBA,EAAA,CAAGU,WAAA,CAAYV,EAAA,CAAGW,KAAA,EAAO,KAAKlB,cAAA,CAAkB,aAAU,GAC1DO,EAAA,CAAGY,SAAA,CAAUZ,EAAA,CAAGa,IAAA,EAAMb,EAAA,CAAGa,IAAA,EAAMb,EAAA,CAAGa,IAAI;EAC1C;AACJ;AA5Ia7B,aAAA,CAGFuC,SAAA,GAA+B;EAClCC,IAAA,EAAMC,aAAA,CAAcC,cAAA;EACpBC,IAAA,EAAM;AACV;AAwIJC,UAAA,CAAWC,GAAA,CAAI7C,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}