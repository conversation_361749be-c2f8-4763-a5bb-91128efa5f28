{"ast": null, "code": "import \"../settings.mjs\";\nimport { settings } from \"@pixi/settings\";\nlet supported;\nfunction isWebGLSupported() {\n  return typeof supported > \"u\" && (supported = function () {\n    const contextOptions = {\n      stencil: !0,\n      failIfMajorPerformanceCaveat: settings.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT\n    };\n    try {\n      if (!settings.ADAPTER.getWebGLRenderingContext()) return !1;\n      const canvas = settings.ADAPTER.createCanvas();\n      let gl = canvas.getContext(\"webgl\", contextOptions) || canvas.getContext(\"experimental-webgl\", contextOptions);\n      const success = !!gl?.getContextAttributes()?.stencil;\n      if (gl) {\n        const loseContext = gl.getExtension(\"WEBGL_lose_context\");\n        loseContext && loseContext.loseContext();\n      }\n      return gl = null, success;\n    } catch {\n      return !1;\n    }\n  }()), supported;\n}\nexport { isWebGLSupported };", "map": {"version": 3, "names": ["supported", "isWebGLSupported", "contextOptions", "stencil", "failIfMajorPerformanceCaveat", "settings", "FAIL_IF_MAJOR_PERFORMANCE_CAVEAT", "ADAPTER", "getWebGLRenderingContext", "canvas", "createCanvas", "gl", "getContext", "success", "getContextAttributes", "loseContext", "getExtension"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\utils\\src\\browser\\isWebGLSupported.ts"], "sourcesContent": ["import { settings } from '../settings';\n\nlet supported: boolean | undefined;\n\n/**\n * Helper for checking for WebGL support.\n * @memberof PIXI.utils\n * @function isWebGLSupported\n * @returns {boolean} Is WebGL supported.\n */\nexport function isWebGLSupported(): boolean\n{\n    if (typeof supported === 'undefined')\n    {\n        supported = (function supported(): boolean\n        {\n            const contextOptions = {\n                stencil: true,\n                failIfMajorPerformanceCaveat: settings.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT,\n            };\n\n            try\n            {\n                if (!settings.ADAPTER.getWebGLRenderingContext())\n                {\n                    return false;\n                }\n\n                const canvas = settings.ADAPTER.createCanvas();\n                let gl = (\n                    canvas.getContext('webgl', contextOptions)\n                    || canvas.getContext('experimental-webgl', contextOptions)\n                ) as WebGLRenderingContext | null;\n\n                const success = !!gl?.getContextAttributes()?.stencil;\n\n                if (gl)\n                {\n                    const loseContext = gl.getExtension('WEBGL_lose_context');\n\n                    if (loseContext)\n                    {\n                        loseContext.loseContext();\n                    }\n                }\n\n                gl = null;\n\n                return success;\n            }\n            catch (e)\n            {\n                return false;\n            }\n        })();\n    }\n\n    return supported;\n}\n"], "mappings": ";;AAEA,IAAIA,SAAA;AAQG,SAASC,iBAAA,EAChB;EACI,OAAI,OAAOD,SAAA,GAAc,QAErBA,SAAA,GAAa,YACb;IACI,MAAME,cAAA,GAAiB;MACnBC,OAAA,EAAS;MACTC,4BAAA,EAA8BC,QAAA,CAASC;IAAA;IAI3C;MACQ,KAACD,QAAA,CAASE,OAAA,CAAQC,wBAAA,CAAyB,GAEpC;MAGL,MAAAC,MAAA,GAASJ,QAAA,CAASE,OAAA,CAAQG,YAAA,CAAa;MACzC,IAAAC,EAAA,GACAF,MAAA,CAAOG,UAAA,CAAW,SAASV,cAAc,KACtCO,MAAA,CAAOG,UAAA,CAAW,sBAAsBV,cAAc;MAG7D,MAAMW,OAAA,GAAU,CAAC,CAACF,EAAA,EAAIG,oBAAA,IAAwBX,OAAA;MAE9C,IAAIQ,EAAA,EACJ;QACU,MAAAI,WAAA,GAAcJ,EAAA,CAAGK,YAAA,CAAa,oBAAoB;QAEpDD,WAAA,IAEAA,WAAA,CAAYA,WAAA;MAEpB;MAEA,OAAAJ,EAAA,GAAK,MAEEE,OAAA;IAAA,QAGX;MACW;IACX;EAAA,EAID,IAAAb,SAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}