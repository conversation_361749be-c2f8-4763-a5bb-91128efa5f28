using UnityEngine;
using System.Collections.Generic;

[System.Serializable]
public class Province
{
    [Header("Basic Information")]
    public string Name;
    public string Owner;
    public string Culture;
    public string Religion;

    [Header("Demographics")]
    public int Population;
    public float Stability;
    public float Development;

    [Header("Economy")]
    public int Resources;
    public float TaxIncome;
    public float TradeValue;

    [Header("Military")]
    public int Manpower;
    public int Fortification;

    [Header("Geography")]
    public Vector2 Position;
    public List<string> AdjacentProvinces;
    public string TerrainType;
    public string Climate;

    [Header("Game State")]
    public bool IsPlayerControlled;
    public bool IsCapital;
    public int LastUpdatedTurn;

    public Province()
    {
        Name = "Unknown Province";
        Owner = "Uncolonized";
        Culture = "Local";
        Religion = "Animism";
        Population = 1000;
        Stability = 50f;
        Development = 1f;
        Resources = 10;
        TaxIncome = 0f;
        TradeValue = 0f;
        Manpower = 100;
        Fortification = 0;
        Position = Vector2.zero;
        AdjacentProvinces = new List<string>();
        TerrainType = "Plains";
        Climate = "Temperate";
        IsPlayerControlled = false;
        IsCapital = false;
        LastUpdatedTurn = 0;
    }

    public Province(string name, string owner, int population, int resources) : this()
    {
        Name = name;
        Owner = owner;
        Population = population;
        Resources = resources;
    }

    public void UpdateProvince(int currentTurn)
    {
        LastUpdatedTurn = currentTurn;

        // Basic growth calculations
        if (Stability > 50f)
        {
            Population = Mathf.RoundToInt(Population * 1.01f); // 1% growth if stable
            Resources += Mathf.RoundToInt(Development * 0.5f);
        }
        else if (Stability < 25f)
        {
            Population = Mathf.RoundToInt(Population * 0.99f); // 1% decline if unstable
            Resources = Mathf.Max(0, Resources - 1);
        }

        // Update manpower based on population
        Manpower = Mathf.RoundToInt(Population * 0.1f);

        // Calculate tax income
        TaxIncome = Population * 0.01f * (Stability / 100f);
    }

    public bool IsAdjacentTo(string provinceName)
    {
        return AdjacentProvinces.Contains(provinceName);
    }

    public void AddAdjacentProvince(string provinceName)
    {
        if (!AdjacentProvinces.Contains(provinceName))
        {
            AdjacentProvinces.Add(provinceName);
        }
    }

    public void RemoveAdjacentProvince(string provinceName)
    {
        AdjacentProvinces.Remove(provinceName);
    }

    public float GetDevelopmentLevel()
    {
        return Development;
    }

    public void InvestInDevelopment(float amount)
    {
        Development += amount;
        Development = Mathf.Clamp(Development, 0.1f, 10f);
    }

    public void ChangeStability(float amount)
    {
        Stability += amount;
        Stability = Mathf.Clamp(Stability, 0f, 100f);
    }

    public override string ToString()
    {
        return $"{Name} (Owner: {Owner}, Pop: {Population:N0}, Resources: {Resources}, Stability: {Stability:F1}%)";
    }
}
