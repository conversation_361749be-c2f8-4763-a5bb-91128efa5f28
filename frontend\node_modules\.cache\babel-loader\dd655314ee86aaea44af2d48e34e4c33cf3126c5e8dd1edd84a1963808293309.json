{"ast": null, "code": "import { SHAPES } from \"../const.mjs\";\nclass Polygon {\n  /**\n   * @param {PIXI.IPointData[]|number[]} points - This can be an array of Points\n   *  that form the polygon, a flat array of numbers that will be interpreted as [x,y, x,y, ...], or\n   *  the arguments passed can be all the points of the polygon e.g.\n   *  `new Polygon(new Point(), new Point(), ...)`, or the arguments passed can be flat\n   *  x,y values e.g. `new Polygon(x,y, x,y, x,y, ...)` where `x` and `y` are Numbers.\n   */\n  constructor(...points) {\n    let flat = Array.isArray(points[0]) ? points[0] : points;\n    if (typeof flat[0] != \"number\") {\n      const p = [];\n      for (let i = 0, il = flat.length; i < il; i++) p.push(flat[i].x, flat[i].y);\n      flat = p;\n    }\n    this.points = flat, this.type = SHAPES.POLY, this.closeStroke = !0;\n  }\n  /**\n   * Creates a clone of this polygon.\n   * @returns - A copy of the polygon.\n   */\n  clone() {\n    const points = this.points.slice(),\n      polygon = new Polygon(points);\n    return polygon.closeStroke = this.closeStroke, polygon;\n  }\n  /**\n   * Checks whether the x and y coordinates passed to this function are contained within this polygon.\n   * @param x - The X coordinate of the point to test.\n   * @param y - The Y coordinate of the point to test.\n   * @returns - Whether the x/y coordinates are within this polygon.\n   */\n  contains(x, y) {\n    let inside = !1;\n    const length = this.points.length / 2;\n    for (let i = 0, j = length - 1; i < length; j = i++) {\n      const xi = this.points[i * 2],\n        yi = this.points[i * 2 + 1],\n        xj = this.points[j * 2],\n        yj = this.points[j * 2 + 1];\n      yi > y != yj > y && x < (xj - xi) * ((y - yi) / (yj - yi)) + xi && (inside = !inside);\n    }\n    return inside;\n  }\n}\nPolygon.prototype.toString = function () {\n  return `[@pixi/math:PolygoncloseStroke=${this.closeStroke}points=${this.points.reduce((pointsDesc, currentPoint) => `${pointsDesc}, ${currentPoint}`, \"\")}]`;\n};\nexport { Polygon };", "map": {"version": 3, "names": ["Polygon", "constructor", "points", "flat", "Array", "isArray", "p", "i", "il", "length", "push", "x", "y", "type", "SHAPES", "POLY", "closeStroke", "clone", "slice", "polygon", "contains", "inside", "j", "xi", "yi", "xj", "yj", "prototype", "toString", "reduce", "pointsDesc", "currentPoint"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\math\\src\\shapes\\Polygon.ts"], "sourcesContent": ["import { SHAPES } from '../const';\n\nimport type { IPointData } from '../IPointData';\n\n/**\n * A class to define a shape via user defined coordinates.\n * @memberof PIXI\n */\nexport class Polygon\n{\n    /** An array of the points of this polygon. */\n    public points: number[];\n\n    /** `false` after moveTo, `true` after `closePath`. In all other cases it is `true`. */\n    public closeStroke: boolean;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.POLY\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.POLY;\n\n    constructor(points: IPointData[] | number[]);\n    constructor(...points: IPointData[] | number[]);\n\n    /**\n     * @param {PIXI.IPointData[]|number[]} points - This can be an array of Points\n     *  that form the polygon, a flat array of numbers that will be interpreted as [x,y, x,y, ...], or\n     *  the arguments passed can be all the points of the polygon e.g.\n     *  `new Polygon(new Point(), new Point(), ...)`, or the arguments passed can be flat\n     *  x,y values e.g. `new Polygon(x,y, x,y, x,y, ...)` where `x` and `y` are Numbers.\n     */\n    constructor(...points: any[])\n    {\n        let flat: IPointData[] | number[] = Array.isArray(points[0]) ? points[0] : points;\n\n        // if this is an array of points, convert it to a flat array of numbers\n        if (typeof flat[0] !== 'number')\n        {\n            const p: number[] = [];\n\n            for (let i = 0, il = flat.length; i < il; i++)\n            {\n                p.push((flat[i] as IPointData).x, (flat[i] as IPointData).y);\n            }\n\n            flat = p;\n        }\n\n        this.points = flat as number[];\n        this.type = SHAPES.POLY;\n        this.closeStroke = true;\n    }\n\n    /**\n     * Creates a clone of this polygon.\n     * @returns - A copy of the polygon.\n     */\n    clone(): Polygon\n    {\n        const points = this.points.slice();\n        const polygon = new Polygon(points);\n\n        polygon.closeStroke = this.closeStroke;\n\n        return polygon;\n    }\n\n    /**\n     * Checks whether the x and y coordinates passed to this function are contained within this polygon.\n     * @param x - The X coordinate of the point to test.\n     * @param y - The Y coordinate of the point to test.\n     * @returns - Whether the x/y coordinates are within this polygon.\n     */\n    contains(x: number, y: number): boolean\n    {\n        let inside = false;\n\n        // use some raycasting to test hits\n        // https://github.com/substack/point-in-polygon/blob/master/index.js\n        const length = this.points.length / 2;\n\n        for (let i = 0, j = length - 1; i < length; j = i++)\n        {\n            const xi = this.points[i * 2];\n            const yi = this.points[(i * 2) + 1];\n            const xj = this.points[j * 2];\n            const yj = this.points[(j * 2) + 1];\n            const intersect = ((yi > y) !== (yj > y)) && (x < ((xj - xi) * ((y - yi) / (yj - yi))) + xi);\n\n            if (intersect)\n            {\n                inside = !inside;\n            }\n        }\n\n        return inside;\n    }\n}\n\nif (process.env.DEBUG)\n{\n    Polygon.prototype.toString = function toString(): string\n    {\n        return `[@pixi/math:Polygon`\n            + `closeStroke=${this.closeStroke}`\n            + `points=${this.points.reduce((pointsDesc, currentPoint) => `${pointsDesc}, ${currentPoint}`, '')}]`;\n    };\n}\n"], "mappings": ";AAQO,MAAMA,OAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAwBIC,YAAA,GAAeC,MAAA,EACf;IACQ,IAAAC,IAAA,GAAgCC,KAAA,CAAMC,OAAA,CAAQH,MAAA,CAAO,CAAC,CAAC,IAAIA,MAAA,CAAO,CAAC,IAAIA,MAAA;IAG3E,IAAI,OAAOC,IAAA,CAAK,CAAC,KAAM,UACvB;MACI,MAAMG,CAAA,GAAc;MAEpB,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKL,IAAA,CAAKM,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAEpCD,CAAA,CAAAI,IAAA,CAAMP,IAAA,CAAKI,CAAC,EAAiBI,CAAA,EAAIR,IAAA,CAAKI,CAAC,EAAiBK,CAAC;MAGxDT,IAAA,GAAAG,CAAA;IACX;IAEA,KAAKJ,MAAA,GAASC,IAAA,EACd,KAAKU,IAAA,GAAOC,MAAA,CAAOC,IAAA,EACnB,KAAKC,WAAA,GAAc;EACvB;EAAA;AAAA;AAAA;AAAA;EAMAC,MAAA,EACA;IACU,MAAAf,MAAA,GAAS,KAAKA,MAAA,CAAOgB,KAAA;MACrBC,OAAA,GAAU,IAAInB,OAAA,CAAQE,MAAM;IAE1B,OAAAiB,OAAA,CAAAH,WAAA,GAAc,KAAKA,WAAA,EAEpBG,OAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAC,SAAST,CAAA,EAAWC,CAAA,EACpB;IACI,IAAIS,MAAA,GAAS;IAIP,MAAAZ,MAAA,GAAS,KAAKP,MAAA,CAAOO,MAAA,GAAS;IAE3B,SAAAF,CAAA,GAAI,GAAGe,CAAA,GAAIb,MAAA,GAAS,GAAGF,CAAA,GAAIE,MAAA,EAAQa,CAAA,GAAIf,CAAA,IAChD;MACU,MAAAgB,EAAA,GAAK,KAAKrB,MAAA,CAAOK,CAAA,GAAI,CAAC;QACtBiB,EAAA,GAAK,KAAKtB,MAAA,CAAQK,CAAA,GAAI,IAAK,CAAC;QAC5BkB,EAAA,GAAK,KAAKvB,MAAA,CAAOoB,CAAA,GAAI,CAAC;QACtBI,EAAA,GAAK,KAAKxB,MAAA,CAAQoB,CAAA,GAAI,IAAK,CAAC;MACdE,EAAA,GAAKZ,CAAA,IAAQc,EAAA,GAAKd,CAAA,IAAQD,CAAA,IAAMc,EAAA,GAAKF,EAAA,MAAQX,CAAA,GAAIY,EAAA,KAAOE,EAAA,GAAKF,EAAA,KAAQD,EAAA,KAIrFF,MAAA,GAAS,CAACA,MAAA;IAElB;IAEO,OAAAA,MAAA;EACX;AACJ;AAIIrB,OAAA,CAAQ2B,SAAA,CAAUC,QAAA,GAAW,YAC7B;EACI,OAAO,kCACc,KAAKZ,WAAW,UACrB,KAAKd,MAAA,CAAO2B,MAAA,CAAO,CAACC,UAAA,EAAYC,YAAA,KAAiB,GAAGD,UAAU,KAAKC,YAAY,IAAI,EAAE,CAAC;AAC1G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}