{"ast": null, "code": "import { BaseImageResource } from \"./BaseImageResource.mjs\";\nclass VideoFrameResource extends BaseImageResource {\n  /**\n   * @param source - Image element to use\n   */\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  constructor(source) {\n    super(source);\n  }\n  /**\n   * Used to auto-detect the type of resource.\n   * @param {*} source - The source object\n   * @returns {boolean} `true` if source is an VideoFrame\n   */\n  static test(source) {\n    return !!globalThis.VideoFrame && source instanceof globalThis.VideoFrame;\n  }\n}\nexport { VideoFrameResource };", "map": {"version": 3, "names": ["VideoFrameResource", "BaseImageResource", "constructor", "source", "test", "globalThis", "VideoFrame"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\textures\\resources\\VideoFrameResource.ts"], "sourcesContent": ["import { BaseImageResource } from './BaseImageResource';\n\n/**\n * Resource type for VideoFrame.\n * @memberof PIXI\n */\nexport class VideoFrameResource extends BaseImageResource\n{\n    /**\n     * @param source - Image element to use\n     */\n    // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n    constructor(source: VideoFrame)\n    {\n        super(source);\n    }\n\n    /**\n     * Used to auto-detect the type of resource.\n     * @param {*} source - The source object\n     * @returns {boolean} `true` if source is an VideoFrame\n     */\n    static test(source: unknown): source is VideoFrame\n    {\n        return !!globalThis.VideoFrame && source instanceof globalThis.VideoFrame;\n    }\n}\n"], "mappings": ";AAMO,MAAMA,kBAAA,SAA2BC,iBAAA,CACxC;EAAA;AAAA;AAAA;EAAA;EAKIC,YAAYC,MAAA,EACZ;IACI,MAAMA,MAAM;EAChB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA,OAAOC,KAAKD,MAAA,EACZ;IACI,OAAO,CAAC,CAACE,UAAA,CAAWC,UAAA,IAAcH,MAAA,YAAkBE,UAAA,CAAWC,UAAA;EACnE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}