{"ast": null, "code": "import { Point } from \"@pixi/core\";\nimport { FederatedEvent } from \"./FederatedEvent.mjs\";\nclass FederatedMouseEvent extends FederatedEvent {\n  constructor() {\n    super(...arguments), this.client = new Point(), this.movement = new Point(), this.offset = new Point(), this.global = new Point(), this.screen = new Point();\n  }\n  /** @readonly */\n  get clientX() {\n    return this.client.x;\n  }\n  /** @readonly */\n  get clientY() {\n    return this.client.y;\n  }\n  /**\n   * Alias for {@link PIXI.FederatedMouseEvent.clientX this.clientX}.\n   * @readonly\n   */\n  get x() {\n    return this.clientX;\n  }\n  /**\n   * Alias for {@link PIXI.FederatedMouseEvent.clientY this.clientY}.\n   * @readonly\n   */\n  get y() {\n    return this.clientY;\n  }\n  /** @readonly */\n  get movementX() {\n    return this.movement.x;\n  }\n  /** @readonly */\n  get movementY() {\n    return this.movement.y;\n  }\n  /** @readonly */\n  get offsetX() {\n    return this.offset.x;\n  }\n  /** @readonly */\n  get offsetY() {\n    return this.offset.y;\n  }\n  /** @readonly */\n  get globalX() {\n    return this.global.x;\n  }\n  /** @readonly */\n  get globalY() {\n    return this.global.y;\n  }\n  /**\n   * The pointer coordinates in the renderer's screen. Alias for {@code screen.x}.\n   * @readonly\n   */\n  get screenX() {\n    return this.screen.x;\n  }\n  /**\n   * The pointer coordinates in the renderer's screen. Alias for {@code screen.y}.\n   * @readonly\n   */\n  get screenY() {\n    return this.screen.y;\n  }\n  /**\n   * This will return the local coordinates of the specified displayObject for this InteractionData\n   * @param {PIXI.DisplayObject} displayObject - The DisplayObject that you would like the local\n   *  coords off\n   * @param {PIXI.IPointData} point - A Point object in which to store the value, optional (otherwise\n   *  will create a new point)\n   * @param {PIXI.IPointData} globalPos - A Point object containing your custom global coords, optional\n   *  (otherwise will use the current global coords)\n   * @returns - A point containing the coordinates of the InteractionData position relative\n   *  to the DisplayObject\n   */\n  getLocalPosition(displayObject, point, globalPos) {\n    return displayObject.worldTransform.applyInverse(globalPos || this.global, point);\n  }\n  /**\n   * Whether the modifier key was pressed when this event natively occurred.\n   * @param key - The modifier key.\n   */\n  getModifierState(key) {\n    return \"getModifierState\" in this.nativeEvent && this.nativeEvent.getModifierState(key);\n  }\n  /**\n   * Not supported.\n   * @param _typeArg\n   * @param _canBubbleArg\n   * @param _cancelableArg\n   * @param _viewArg\n   * @param _detailArg\n   * @param _screenXArg\n   * @param _screenYArg\n   * @param _clientXArg\n   * @param _clientYArg\n   * @param _ctrlKeyArg\n   * @param _altKeyArg\n   * @param _shiftKeyArg\n   * @param _metaKeyArg\n   * @param _buttonArg\n   * @param _relatedTargetArg\n   * @deprecated since 7.0.0\n   */\n  // eslint-disable-next-line max-params\n  initMouseEvent(_typeArg, _canBubbleArg, _cancelableArg, _viewArg, _detailArg, _screenXArg, _screenYArg, _clientXArg, _clientYArg, _ctrlKeyArg, _altKeyArg, _shiftKeyArg, _metaKeyArg, _buttonArg, _relatedTargetArg) {\n    throw new Error(\"Method not implemented.\");\n  }\n}\nexport { FederatedMouseEvent };", "map": {"version": 3, "names": ["FederatedMouseEvent", "FederatedEvent", "constructor", "arguments", "client", "Point", "movement", "offset", "global", "screen", "clientX", "x", "clientY", "y", "movementX", "movementY", "offsetX", "offsetY", "globalX", "globalY", "screenX", "screenY", "getLocalPosition", "displayObject", "point", "globalPos", "worldTransform", "applyInverse", "getModifierState", "key", "nativeEvent", "initMouseEvent", "_typeArg", "_canBubbleArg", "_cancelableArg", "_viewArg", "_detailArg", "_screenXArg", "_screenYArg", "_clientXArg", "_clientYArg", "_ctrlKeyArg", "_altKeyArg", "_shiftKeyArg", "_metaKeyArg", "_buttonArg", "_relatedTargetArg", "Error"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\events\\src\\FederatedMouseEvent.ts"], "sourcesContent": ["import { Point } from '@pixi/core';\nimport { FederatedEvent } from './FederatedEvent';\n\nimport type { IPointData } from '@pixi/core';\nimport type { DisplayObject } from '@pixi/display';\nimport type { PixiTouch } from './FederatedEvent';\n\n/**\n * A {@link PIXI.FederatedEvent} for mouse events.\n * @memberof PIXI\n */\nexport class FederatedMouseEvent extends FederatedEvent<\nMouseEvent | PointerEvent | PixiTouch\n> implements MouseEvent\n{\n    /** Whether the \"alt\" key was pressed when this mouse event occurred. */\n    altKey: boolean;\n\n    /** The specific button that was pressed in this mouse event. */\n    button: number;\n\n    /** The button depressed when this event occurred. */\n    buttons: number;\n\n    /** Whether the \"control\" key was pressed when this mouse event occurred. */\n    ctrlKey: boolean;\n\n    /** Whether the \"meta\" key was pressed when this mouse event occurred. */\n    metaKey: boolean;\n\n    /** This is currently not implemented in the Federated Events API. */\n    relatedTarget: EventTarget;\n\n    /** Whether the \"shift\" key was pressed when this mouse event occurred. */\n    shiftKey: boolean;\n\n    /** The coordinates of the mouse event relative to the canvas. */\n    public client: Point = new Point();\n\n    /** @readonly */\n    public get clientX(): number { return this.client.x; }\n\n    /** @readonly */\n    public get clientY(): number { return this.client.y; }\n\n    /**\n     * Alias for {@link PIXI.FederatedMouseEvent.clientX this.clientX}.\n     * @readonly\n     */\n    get x(): number { return this.clientX; }\n\n    /**\n     * Alias for {@link PIXI.FederatedMouseEvent.clientY this.clientY}.\n     * @readonly\n     */\n    get y(): number { return this.clientY; }\n\n    /** This is the number of clicks that occurs in 200ms/click of each other. */\n    public detail: number;\n\n    /** The movement in this pointer relative to the last `mousemove` event. */\n    public movement: Point = new Point();\n\n    /** @readonly */\n    get movementX(): number { return this.movement.x; }\n\n    /** @readonly */\n    get movementY(): number { return this.movement.y; }\n\n    /**\n     * The offset of the pointer coordinates w.r.t. target DisplayObject in world space. This is\n     * not supported at the moment.\n     */\n    public offset: Point = new Point();\n\n    /** @readonly */\n    get offsetX(): number { return this.offset.x; }\n\n    /** @readonly */\n    get offsetY(): number { return this.offset.y; }\n\n    /** The pointer coordinates in world space. */\n    public global: Point = new Point();\n\n    /** @readonly */\n    get globalX(): number { return this.global.x; }\n\n    /** @readonly */\n    get globalY(): number { return this.global.y; }\n\n    /**\n     * The pointer coordinates in the renderer's {@link PIXI.Renderer.screen screen}. This has slightly\n     * different semantics than native PointerEvent screenX/screenY.\n     */\n    public screen: Point = new Point();\n\n    /**\n     * The pointer coordinates in the renderer's screen. Alias for {@code screen.x}.\n     * @readonly\n     */\n    get screenX(): number { return this.screen.x; }\n\n    /**\n     * The pointer coordinates in the renderer's screen. Alias for {@code screen.y}.\n     * @readonly\n     */\n    get screenY(): number { return this.screen.y; }\n\n    /**\n     * This will return the local coordinates of the specified displayObject for this InteractionData\n     * @param {PIXI.DisplayObject} displayObject - The DisplayObject that you would like the local\n     *  coords off\n     * @param {PIXI.IPointData} point - A Point object in which to store the value, optional (otherwise\n     *  will create a new point)\n     * @param {PIXI.IPointData} globalPos - A Point object containing your custom global coords, optional\n     *  (otherwise will use the current global coords)\n     * @returns - A point containing the coordinates of the InteractionData position relative\n     *  to the DisplayObject\n     */\n    public getLocalPosition<P extends IPointData = Point>(displayObject: DisplayObject, point?: P, globalPos?: IPointData): P\n    {\n        return displayObject.worldTransform.applyInverse<P>(globalPos || this.global, point);\n    }\n\n    /**\n     * Whether the modifier key was pressed when this event natively occurred.\n     * @param key - The modifier key.\n     */\n    getModifierState(key: string): boolean\n    {\n        return 'getModifierState' in this.nativeEvent && this.nativeEvent.getModifierState(key);\n    }\n\n    /**\n     * Not supported.\n     * @param _typeArg\n     * @param _canBubbleArg\n     * @param _cancelableArg\n     * @param _viewArg\n     * @param _detailArg\n     * @param _screenXArg\n     * @param _screenYArg\n     * @param _clientXArg\n     * @param _clientYArg\n     * @param _ctrlKeyArg\n     * @param _altKeyArg\n     * @param _shiftKeyArg\n     * @param _metaKeyArg\n     * @param _buttonArg\n     * @param _relatedTargetArg\n     * @deprecated since 7.0.0\n     */\n    // eslint-disable-next-line max-params\n    initMouseEvent(\n        _typeArg: string,\n        _canBubbleArg: boolean,\n        _cancelableArg: boolean,\n        _viewArg: Window,\n        _detailArg: number,\n        _screenXArg: number,\n        _screenYArg: number,\n        _clientXArg: number,\n        _clientYArg: number,\n        _ctrlKeyArg: boolean,\n        _altKeyArg: boolean,\n        _shiftKeyArg: boolean,\n        _metaKeyArg: boolean,\n        _buttonArg: number,\n        _relatedTargetArg: EventTarget\n    ): void\n    {\n        throw new Error('Method not implemented.');\n    }\n}\n"], "mappings": ";;AAWO,MAAMA,mBAAA,SAA4BC,cAAA,CAGzC;EAHOC,YAAA;IAAA,SAAAC,SAAA,GA0BI,KAAAC,MAAA,GAAgB,IAAIC,KAAA,IAwBpB,KAAAC,QAAA,GAAkB,IAAID,KAAA,IAYtB,KAAAE,MAAA,GAAgB,IAAIF,KAAA,IASpB,KAAAG,MAAA,GAAgB,IAAIH,KAAA,IAYpB,KAAAI,MAAA,GAAgB,IAAIJ,KAAA;EAAM;EAAA;EAtDjC,IAAWK,QAAA,EAAkB;IAAE,OAAO,KAAKN,MAAA,CAAOO,CAAA;EAAG;EAAA;EAGrD,IAAWC,QAAA,EAAkB;IAAE,OAAO,KAAKR,MAAA,CAAOS,CAAA;EAAG;EAAA;AAAA;AAAA;AAAA;EAMrD,IAAIF,EAAA,EAAY;IAAE,OAAO,KAAKD,OAAA;EAAS;EAAA;AAAA;AAAA;AAAA;EAMvC,IAAIG,EAAA,EAAY;IAAE,OAAO,KAAKD,OAAA;EAAS;EAAA;EASvC,IAAIE,UAAA,EAAoB;IAAE,OAAO,KAAKR,QAAA,CAASK,CAAA;EAAG;EAAA;EAGlD,IAAII,UAAA,EAAoB;IAAE,OAAO,KAAKT,QAAA,CAASO,CAAA;EAAG;EAAA;EASlD,IAAIG,QAAA,EAAkB;IAAE,OAAO,KAAKT,MAAA,CAAOI,CAAA;EAAG;EAAA;EAG9C,IAAIM,QAAA,EAAkB;IAAE,OAAO,KAAKV,MAAA,CAAOM,CAAA;EAAG;EAAA;EAM9C,IAAIK,QAAA,EAAkB;IAAE,OAAO,KAAKV,MAAA,CAAOG,CAAA;EAAG;EAAA;EAG9C,IAAIQ,QAAA,EAAkB;IAAE,OAAO,KAAKX,MAAA,CAAOK,CAAA;EAAG;EAAA;AAAA;AAAA;AAAA;EAY9C,IAAIO,QAAA,EAAkB;IAAE,OAAO,KAAKX,MAAA,CAAOE,CAAA;EAAG;EAAA;AAAA;AAAA;AAAA;EAM9C,IAAIU,QAAA,EAAkB;IAAE,OAAO,KAAKZ,MAAA,CAAOI,CAAA;EAAG;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAavCS,iBAA+CC,aAAA,EAA8BC,KAAA,EAAWC,SAAA,EAC/F;IACI,OAAOF,aAAA,CAAcG,cAAA,CAAeC,YAAA,CAAgBF,SAAA,IAAa,KAAKjB,MAAA,EAAQgB,KAAK;EACvF;EAAA;AAAA;AAAA;AAAA;EAMAI,iBAAiBC,GAAA,EACjB;IACI,OAAO,sBAAsB,KAAKC,WAAA,IAAe,KAAKA,WAAA,CAAYF,gBAAA,CAAiBC,GAAG;EAC1F;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAAA;EAsBAE,eACIC,QAAA,EACAC,aAAA,EACAC,cAAA,EACAC,QAAA,EACAC,UAAA,EACAC,WAAA,EACAC,WAAA,EACAC,WAAA,EACAC,WAAA,EACAC,WAAA,EACAC,UAAA,EACAC,YAAA,EACAC,WAAA,EACAC,UAAA,EACAC,iBAAA,EAEJ;IACU,UAAIC,KAAA,CAAM,yBAAyB;EAC7C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}