{"ast": null, "code": "import { State, Color, BLEND_MODES, Texture, Polygon, PI_2, Rectangle, RoundedRectangle, Circle, Ellipse, SHAPES, Matrix, UniformGroup, Shader, Point } from \"@pixi/core\";\nimport { Container } from \"@pixi/display\";\nimport { LINE_CAP, LINE_JOIN, curves } from \"./const.mjs\";\nimport { GraphicsGeometry } from \"./GraphicsGeometry.mjs\";\nimport { FillStyle } from \"./styles/FillStyle.mjs\";\nimport { LineStyle } from \"./styles/LineStyle.mjs\";\nimport \"./utils/index.mjs\";\nimport { QuadraticUtils } from \"./utils/QuadraticUtils.mjs\";\nimport { BezierUtils } from \"./utils/BezierUtils.mjs\";\nimport { ArcUtils } from \"./utils/ArcUtils.mjs\";\nconst DEFAULT_SHADERS = {},\n  _Graphics = class _Graphics2 extends Container {\n    /**\n     * @param geometry - Geometry to use, if omitted will create a new GraphicsGeometry instance.\n     */\n    constructor(geometry = null) {\n      super(), this.shader = null, this.pluginName = \"batch\", this.currentPath = null, this.batches = [], this.batchTint = -1, this.batchDirty = -1, this.vertexData = null, this._fillStyle = new FillStyle(), this._lineStyle = new LineStyle(), this._matrix = null, this._holeMode = !1, this.state = State.for2d(), this._geometry = geometry || new GraphicsGeometry(), this._geometry.refCount++, this._transformID = -1, this._tintColor = new Color(16777215), this.blendMode = BLEND_MODES.NORMAL;\n    }\n    /**\n     * Includes vertex positions, face indices, normals, colors, UVs, and\n     * custom attributes within buffers, reducing the cost of passing all\n     * this data to the GPU. Can be shared between multiple Mesh or Graphics objects.\n     * @readonly\n     */\n    get geometry() {\n      return this._geometry;\n    }\n    /**\n     * Creates a new Graphics object with the same values as this one.\n     * Note that only the geometry of the object is cloned, not its transform (position,scale,etc)\n     * @returns - A clone of the graphics object\n     */\n    clone() {\n      return this.finishPoly(), new _Graphics2(this._geometry);\n    }\n    /**\n     * The blend mode to be applied to the graphic shape. Apply a value of\n     * `PIXI.BLEND_MODES.NORMAL` to reset the blend mode.  Note that, since each\n     * primitive in the GraphicsGeometry list is rendered sequentially, modes\n     * such as `PIXI.BLEND_MODES.ADD` and `PIXI.BLEND_MODES.MULTIPLY` will\n     * be applied per-primitive.\n     * @default PIXI.BLEND_MODES.NORMAL\n     */\n    set blendMode(value) {\n      this.state.blendMode = value;\n    }\n    get blendMode() {\n      return this.state.blendMode;\n    }\n    /**\n     * The tint applied to each graphic shape. This is a hex value. A value of\n     * 0xFFFFFF will remove any tint effect.\n     * @default 0xFFFFFF\n     */\n    get tint() {\n      return this._tintColor.value;\n    }\n    set tint(value) {\n      this._tintColor.setValue(value);\n    }\n    /**\n     * The current fill style.\n     * @readonly\n     */\n    get fill() {\n      return this._fillStyle;\n    }\n    /**\n     * The current line style.\n     * @readonly\n     */\n    get line() {\n      return this._lineStyle;\n    }\n    lineStyle(options = null, color = 0, alpha, alignment = 0.5, native = !1) {\n      return typeof options == \"number\" && (options = {\n        width: options,\n        color,\n        alpha,\n        alignment,\n        native\n      }), this.lineTextureStyle(options);\n    }\n    /**\n     * Like line style but support texture for line fill.\n     * @param [options] - Collection of options for setting line style.\n     * @param {number} [options.width=0] - width of the line to draw, will update the objects stored style\n     * @param {PIXI.Texture} [options.texture=PIXI.Texture.WHITE] - Texture to use\n     * @param {PIXI.ColorSource} [options.color=0x0] - color of the line to draw, will update the objects stored style.\n     *  Default 0xFFFFFF if texture present.\n     * @param {number} [options.alpha=1] - alpha of the line to draw, will update the objects stored style\n     * @param {PIXI.Matrix} [options.matrix=null] - Texture matrix to transform texture\n     * @param {number} [options.alignment=0.5] - alignment of the line to draw, (0 = inner, 0.5 = middle, 1 = outer).\n     *        WebGL only.\n     * @param {boolean} [options.native=false] - If true the lines will be draw using LINES instead of TRIANGLE_STRIP\n     * @param {PIXI.LINE_CAP}[options.cap=PIXI.LINE_CAP.BUTT] - line cap style\n     * @param {PIXI.LINE_JOIN}[options.join=PIXI.LINE_JOIN.MITER] - line join style\n     * @param {number}[options.miterLimit=10] - miter limit ratio\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    lineTextureStyle(options) {\n      const defaultLineStyleOptions = {\n        width: 0,\n        texture: Texture.WHITE,\n        color: options?.texture ? 16777215 : 0,\n        matrix: null,\n        alignment: 0.5,\n        native: !1,\n        cap: LINE_CAP.BUTT,\n        join: LINE_JOIN.MITER,\n        miterLimit: 10\n      };\n      options = Object.assign(defaultLineStyleOptions, options), this.normalizeColor(options), this.currentPath && this.startPoly();\n      const visible = options.width > 0 && options.alpha > 0;\n      return visible ? (options.matrix && (options.matrix = options.matrix.clone(), options.matrix.invert()), Object.assign(this._lineStyle, {\n        visible\n      }, options)) : this._lineStyle.reset(), this;\n    }\n    /**\n     * Start a polygon object internally.\n     * @protected\n     */\n    startPoly() {\n      if (this.currentPath) {\n        const points = this.currentPath.points,\n          len = this.currentPath.points.length;\n        len > 2 && (this.drawShape(this.currentPath), this.currentPath = new Polygon(), this.currentPath.closeStroke = !1, this.currentPath.points.push(points[len - 2], points[len - 1]));\n      } else this.currentPath = new Polygon(), this.currentPath.closeStroke = !1;\n    }\n    /**\n     * Finish the polygon object.\n     * @protected\n     */\n    finishPoly() {\n      this.currentPath && (this.currentPath.points.length > 2 ? (this.drawShape(this.currentPath), this.currentPath = null) : this.currentPath.points.length = 0);\n    }\n    /**\n     * Moves the current drawing position to x, y.\n     * @param x - the X coordinate to move to\n     * @param y - the Y coordinate to move to\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    moveTo(x, y) {\n      return this.startPoly(), this.currentPath.points[0] = x, this.currentPath.points[1] = y, this;\n    }\n    /**\n     * Draws a line using the current line style from the current drawing position to (x, y);\n     * The current drawing position is then set to (x, y).\n     * @param x - the X coordinate to draw to\n     * @param y - the Y coordinate to draw to\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    lineTo(x, y) {\n      this.currentPath || this.moveTo(0, 0);\n      const points = this.currentPath.points,\n        fromX = points[points.length - 2],\n        fromY = points[points.length - 1];\n      return (fromX !== x || fromY !== y) && points.push(x, y), this;\n    }\n    /**\n     * Initialize the curve\n     * @param x\n     * @param y\n     */\n    _initCurve(x = 0, y = 0) {\n      this.currentPath ? this.currentPath.points.length === 0 && (this.currentPath.points = [x, y]) : this.moveTo(x, y);\n    }\n    /**\n     * Calculate the points for a quadratic bezier curve and then draws it.\n     * Based on: https://stackoverflow.com/questions/785097/how-do-i-implement-a-bezier-curve-in-c\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    quadraticCurveTo(cpX, cpY, toX, toY) {\n      this._initCurve();\n      const points = this.currentPath.points;\n      return points.length === 0 && this.moveTo(0, 0), QuadraticUtils.curveTo(cpX, cpY, toX, toY, points), this;\n    }\n    /**\n     * Calculate the points for a bezier curve and then draws it.\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param cpX2 - Second Control point x\n     * @param cpY2 - Second Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @returns This Graphics object. Good for chaining method calls\n     */\n    bezierCurveTo(cpX, cpY, cpX2, cpY2, toX, toY) {\n      return this._initCurve(), BezierUtils.curveTo(cpX, cpY, cpX2, cpY2, toX, toY, this.currentPath.points), this;\n    }\n    /**\n     * The `arcTo` method creates an arc/curve between two tangents on the canvas.\n     * The first tangent is from the start point to the first control point,\n     * and the second tangent is from the first control point to the second control point.\n     * Note that the second control point is not necessarily the end point of the arc.\n     *\n     * \"borrowed\" from https://code.google.com/p/fxcanvas/ - thanks google!\n     * @param x1 - The x-coordinate of the first control point of the arc\n     * @param y1 - The y-coordinate of the first control point of the arc\n     * @param x2 - The x-coordinate of the second control point of the arc\n     * @param y2 - The y-coordinate of the second control point of the arc\n     * @param radius - The radius of the arc\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    arcTo(x1, y1, x2, y2, radius) {\n      this._initCurve(x1, y1);\n      const points = this.currentPath.points,\n        result = ArcUtils.curveTo(x1, y1, x2, y2, radius, points);\n      if (result) {\n        const {\n          cx,\n          cy,\n          radius: radius2,\n          startAngle,\n          endAngle,\n          anticlockwise\n        } = result;\n        this.arc(cx, cy, radius2, startAngle, endAngle, anticlockwise);\n      }\n      return this;\n    }\n    /**\n     * The arc method creates an arc/curve (used to create circles, or parts of circles).\n     * @param cx - The x-coordinate of the center of the circle\n     * @param cy - The y-coordinate of the center of the circle\n     * @param radius - The radius of the circle\n     * @param startAngle - The starting angle, in radians (0 is at the 3 o'clock position\n     *  of the arc's circle)\n     * @param endAngle - The ending angle, in radians\n     * @param anticlockwise - Specifies whether the drawing should be\n     *  counter-clockwise or clockwise. False is default, and indicates clockwise, while true\n     *  indicates counter-clockwise.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    arc(cx, cy, radius, startAngle, endAngle, anticlockwise = !1) {\n      if (startAngle === endAngle) return this;\n      if (!anticlockwise && endAngle <= startAngle ? endAngle += PI_2 : anticlockwise && startAngle <= endAngle && (startAngle += PI_2), endAngle - startAngle === 0) return this;\n      const startX = cx + Math.cos(startAngle) * radius,\n        startY = cy + Math.sin(startAngle) * radius,\n        eps = this._geometry.closePointEps;\n      let points = this.currentPath ? this.currentPath.points : null;\n      if (points) {\n        const xDiff = Math.abs(points[points.length - 2] - startX),\n          yDiff = Math.abs(points[points.length - 1] - startY);\n        xDiff < eps && yDiff < eps || points.push(startX, startY);\n      } else this.moveTo(startX, startY), points = this.currentPath.points;\n      return ArcUtils.arc(startX, startY, cx, cy, radius, startAngle, endAngle, anticlockwise, points), this;\n    }\n    /**\n     * Specifies a simple one-color fill that subsequent calls to other Graphics methods\n     * (such as lineTo() or drawCircle()) use when drawing.\n     * @param {PIXI.ColorSource} color - the color of the fill\n     * @param alpha - the alpha of the fill, will override the color's alpha\n     * @returns - This Graphics object. Suitable for chaining method calls\n     */\n    beginFill(color = 0, alpha) {\n      return this.beginTextureFill({\n        texture: Texture.WHITE,\n        color,\n        alpha\n      });\n    }\n    /**\n     * Normalize the color input from options for line style or fill\n     * @param {PIXI.IFillStyleOptions} options - Fill style object.\n     */\n    normalizeColor(options) {\n      const temp = Color.shared.setValue(options.color ?? 0);\n      options.color = temp.toNumber(), options.alpha ?? (options.alpha = temp.alpha);\n    }\n    /**\n     * Begin the texture fill.\n     * Note: The wrap mode of the texture is forced to REPEAT on render.\n     * @param options - Fill style object.\n     * @param {PIXI.Texture} [options.texture=PIXI.Texture.WHITE] - Texture to fill\n     * @param {PIXI.ColorSource} [options.color=0xffffff] - Background to fill behind texture\n     * @param {number} [options.alpha] - Alpha of fill, overrides the color's alpha\n     * @param {PIXI.Matrix} [options.matrix=null] - Transform matrix\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    beginTextureFill(options) {\n      const defaultOptions = {\n        texture: Texture.WHITE,\n        color: 16777215,\n        matrix: null\n      };\n      options = Object.assign(defaultOptions, options), this.normalizeColor(options), this.currentPath && this.startPoly();\n      const visible = options.alpha > 0;\n      return visible ? (options.matrix && (options.matrix = options.matrix.clone(), options.matrix.invert()), Object.assign(this._fillStyle, {\n        visible\n      }, options)) : this._fillStyle.reset(), this;\n    }\n    /**\n     * Applies a fill to the lines and shapes that were added since the last call to the beginFill() method.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    endFill() {\n      return this.finishPoly(), this._fillStyle.reset(), this;\n    }\n    /**\n     * Draws a rectangle shape.\n     * @param x - The X coord of the top-left of the rectangle\n     * @param y - The Y coord of the top-left of the rectangle\n     * @param width - The width of the rectangle\n     * @param height - The height of the rectangle\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    drawRect(x, y, width, height) {\n      return this.drawShape(new Rectangle(x, y, width, height));\n    }\n    /**\n     * Draw a rectangle shape with rounded/beveled corners.\n     * @param x - The X coord of the top-left of the rectangle\n     * @param y - The Y coord of the top-left of the rectangle\n     * @param width - The width of the rectangle\n     * @param height - The height of the rectangle\n     * @param radius - Radius of the rectangle corners\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    drawRoundedRect(x, y, width, height, radius) {\n      return this.drawShape(new RoundedRectangle(x, y, width, height, radius));\n    }\n    /**\n     * Draws a circle.\n     * @param x - The X coordinate of the center of the circle\n     * @param y - The Y coordinate of the center of the circle\n     * @param radius - The radius of the circle\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    drawCircle(x, y, radius) {\n      return this.drawShape(new Circle(x, y, radius));\n    }\n    /**\n     * Draws an ellipse.\n     * @param x - The X coordinate of the center of the ellipse\n     * @param y - The Y coordinate of the center of the ellipse\n     * @param width - The half width of the ellipse\n     * @param height - The half height of the ellipse\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    drawEllipse(x, y, width, height) {\n      return this.drawShape(new Ellipse(x, y, width, height));\n    }\n    /**\n     * Draws a polygon using the given path.\n     * @param {number[]|PIXI.IPointData[]|PIXI.Polygon} path - The path data used to construct the polygon.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    drawPolygon(...path) {\n      let points,\n        closeStroke = !0;\n      const poly = path[0];\n      poly.points ? (closeStroke = poly.closeStroke, points = poly.points) : Array.isArray(path[0]) ? points = path[0] : points = path;\n      const shape = new Polygon(points);\n      return shape.closeStroke = closeStroke, this.drawShape(shape), this;\n    }\n    /**\n     * Draw any shape.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - Shape to draw\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    drawShape(shape) {\n      return this._holeMode ? this._geometry.drawHole(shape, this._matrix) : this._geometry.drawShape(shape, this._fillStyle.clone(), this._lineStyle.clone(), this._matrix), this;\n    }\n    /**\n     * Clears the graphics that were drawn to this Graphics object, and resets fill and line style settings.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    clear() {\n      return this._geometry.clear(), this._lineStyle.reset(), this._fillStyle.reset(), this._boundsID++, this._matrix = null, this._holeMode = !1, this.currentPath = null, this;\n    }\n    /**\n     * True if graphics consists of one rectangle, and thus, can be drawn like a Sprite and\n     * masked with gl.scissor.\n     * @returns - True if only 1 rect.\n     */\n    isFastRect() {\n      const data = this._geometry.graphicsData;\n      return data.length === 1 && data[0].shape.type === SHAPES.RECT && !data[0].matrix && !data[0].holes.length && !(data[0].lineStyle.visible && data[0].lineStyle.width);\n    }\n    /**\n     * Renders the object using the WebGL renderer\n     * @param renderer - The renderer\n     */\n    _render(renderer) {\n      this.finishPoly();\n      const geometry = this._geometry;\n      geometry.updateBatches(), geometry.batchable ? (this.batchDirty !== geometry.batchDirty && this._populateBatches(), this._renderBatched(renderer)) : (renderer.batch.flush(), this._renderDirect(renderer));\n    }\n    /** Populating batches for rendering. */\n    _populateBatches() {\n      const geometry = this._geometry,\n        blendMode = this.blendMode,\n        len = geometry.batches.length;\n      this.batchTint = -1, this._transformID = -1, this.batchDirty = geometry.batchDirty, this.batches.length = len, this.vertexData = new Float32Array(geometry.points);\n      for (let i = 0; i < len; i++) {\n        const gI = geometry.batches[i],\n          color = gI.style.color,\n          vertexData = new Float32Array(this.vertexData.buffer, gI.attribStart * 4 * 2, gI.attribSize * 2),\n          uvs = new Float32Array(geometry.uvsFloat32.buffer, gI.attribStart * 4 * 2, gI.attribSize * 2),\n          indices = new Uint16Array(geometry.indicesUint16.buffer, gI.start * 2, gI.size),\n          batch = {\n            vertexData,\n            blendMode,\n            indices,\n            uvs,\n            _batchRGB: Color.shared.setValue(color).toRgbArray(),\n            _tintRGB: color,\n            _texture: gI.style.texture,\n            alpha: gI.style.alpha,\n            worldAlpha: 1\n          };\n        this.batches[i] = batch;\n      }\n    }\n    /**\n     * Renders the batches using the BathedRenderer plugin\n     * @param renderer - The renderer\n     */\n    _renderBatched(renderer) {\n      if (this.batches.length) {\n        renderer.batch.setObjectRenderer(renderer.plugins[this.pluginName]), this.calculateVertices(), this.calculateTints();\n        for (let i = 0, l = this.batches.length; i < l; i++) {\n          const batch = this.batches[i];\n          batch.worldAlpha = this.worldAlpha * batch.alpha, renderer.plugins[this.pluginName].render(batch);\n        }\n      }\n    }\n    /**\n     * Renders the graphics direct\n     * @param renderer - The renderer\n     */\n    _renderDirect(renderer) {\n      const shader = this._resolveDirectShader(renderer),\n        geometry = this._geometry,\n        worldAlpha = this.worldAlpha,\n        uniforms = shader.uniforms,\n        drawCalls = geometry.drawCalls;\n      uniforms.translationMatrix = this.transform.worldTransform, Color.shared.setValue(this._tintColor).premultiply(worldAlpha).toArray(uniforms.tint), renderer.shader.bind(shader), renderer.geometry.bind(geometry, shader), renderer.state.set(this.state);\n      for (let i = 0, l = drawCalls.length; i < l; i++) this._renderDrawCallDirect(renderer, geometry.drawCalls[i]);\n    }\n    /**\n     * Renders specific DrawCall\n     * @param renderer\n     * @param drawCall\n     */\n    _renderDrawCallDirect(renderer, drawCall) {\n      const {\n          texArray,\n          type,\n          size,\n          start\n        } = drawCall,\n        groupTextureCount = texArray.count;\n      for (let j = 0; j < groupTextureCount; j++) renderer.texture.bind(texArray.elements[j], j);\n      renderer.geometry.draw(type, size, start);\n    }\n    /**\n     * Resolves shader for direct rendering\n     * @param renderer - The renderer\n     */\n    _resolveDirectShader(renderer) {\n      let shader = this.shader;\n      const pluginName = this.pluginName;\n      if (!shader) {\n        if (!DEFAULT_SHADERS[pluginName]) {\n          const {\n              maxTextures\n            } = renderer.plugins[pluginName],\n            sampleValues = new Int32Array(maxTextures);\n          for (let i = 0; i < maxTextures; i++) sampleValues[i] = i;\n          const uniforms = {\n              tint: new Float32Array([1, 1, 1, 1]),\n              translationMatrix: new Matrix(),\n              default: UniformGroup.from({\n                uSamplers: sampleValues\n              }, !0)\n            },\n            program = renderer.plugins[pluginName]._shader.program;\n          DEFAULT_SHADERS[pluginName] = new Shader(program, uniforms);\n        }\n        shader = DEFAULT_SHADERS[pluginName];\n      }\n      return shader;\n    }\n    /**\n     * Retrieves the bounds of the graphic shape as a rectangle object.\n     * @see PIXI.GraphicsGeometry#bounds\n     */\n    _calculateBounds() {\n      this.finishPoly();\n      const geometry = this._geometry;\n      if (!geometry.graphicsData.length) return;\n      const {\n        minX,\n        minY,\n        maxX,\n        maxY\n      } = geometry.bounds;\n      this._bounds.addFrame(this.transform, minX, minY, maxX, maxY);\n    }\n    /**\n     * Tests if a point is inside this graphics object\n     * @param point - the point to test\n     * @returns - the result of the test\n     */\n    containsPoint(point) {\n      return this.worldTransform.applyInverse(point, _Graphics2._TEMP_POINT), this._geometry.containsPoint(_Graphics2._TEMP_POINT);\n    }\n    /** Recalculate the tint by applying tint to batches using Graphics tint. */\n    calculateTints() {\n      if (this.batchTint !== this.tint) {\n        this.batchTint = this._tintColor.toNumber();\n        for (let i = 0; i < this.batches.length; i++) {\n          const batch = this.batches[i];\n          batch._tintRGB = Color.shared.setValue(this._tintColor).multiply(batch._batchRGB).toLittleEndianNumber();\n        }\n      }\n    }\n    /** If there's a transform update or a change to the shape of the geometry, recalculate the vertices. */\n    calculateVertices() {\n      const wtID = this.transform._worldID;\n      if (this._transformID === wtID) return;\n      this._transformID = wtID;\n      const wt = this.transform.worldTransform,\n        a = wt.a,\n        b = wt.b,\n        c = wt.c,\n        d = wt.d,\n        tx = wt.tx,\n        ty = wt.ty,\n        data = this._geometry.points,\n        vertexData = this.vertexData;\n      let count = 0;\n      for (let i = 0; i < data.length; i += 2) {\n        const x = data[i],\n          y = data[i + 1];\n        vertexData[count++] = a * x + c * y + tx, vertexData[count++] = d * y + b * x + ty;\n      }\n    }\n    /**\n     * Closes the current path.\n     * @returns - Returns itself.\n     */\n    closePath() {\n      const currentPath = this.currentPath;\n      return currentPath && (currentPath.closeStroke = !0, this.finishPoly()), this;\n    }\n    /**\n     * Apply a matrix to the positional data.\n     * @param matrix - Matrix to use for transform current shape.\n     * @returns - Returns itself.\n     */\n    setMatrix(matrix) {\n      return this._matrix = matrix, this;\n    }\n    /**\n     * Begin adding holes to the last draw shape\n     * IMPORTANT: holes must be fully inside a shape to work\n     * Also weirdness ensues if holes overlap!\n     * Ellipses, Circles, Rectangles and Rounded Rectangles cannot be holes or host for holes in CanvasRenderer,\n     * please use `moveTo` `lineTo`, `quadraticCurveTo` if you rely on pixi-legacy bundle.\n     * @returns - Returns itself.\n     */\n    beginHole() {\n      return this.finishPoly(), this._holeMode = !0, this;\n    }\n    /**\n     * End adding holes to the last draw shape.\n     * @returns - Returns itself.\n     */\n    endHole() {\n      return this.finishPoly(), this._holeMode = !1, this;\n    }\n    /**\n     * Destroys the Graphics object.\n     * @param options - Options parameter. A boolean will act as if all\n     *  options have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have\n     *  their destroy method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the texture of the child sprite\n     * @param {boolean} [options.baseTexture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the base texture of the child sprite\n     */\n    destroy(options) {\n      this._geometry.refCount--, this._geometry.refCount === 0 && this._geometry.dispose(), this._matrix = null, this.currentPath = null, this._lineStyle.destroy(), this._lineStyle = null, this._fillStyle.destroy(), this._fillStyle = null, this._geometry = null, this.shader = null, this.vertexData = null, this.batches.length = 0, this.batches = null, super.destroy(options);\n    }\n  };\n_Graphics.curves = curves,\n/**\n* Temporary point to use for containsPoint.\n* @private\n*/\n_Graphics._TEMP_POINT = new Point();\nlet Graphics = _Graphics;\nexport { Graphics };", "map": {"version": 3, "names": ["DEFAULT_SHADERS", "_Graphics", "_Graphics2", "Container", "constructor", "geometry", "shader", "pluginName", "currentPath", "batches", "batchTint", "batchDirty", "vertexData", "_fillStyle", "FillStyle", "_lineStyle", "LineStyle", "_matrix", "_holeMode", "state", "State", "for2d", "_geometry", "GraphicsGeometry", "refCount", "_transformID", "_tintColor", "Color", "blendMode", "BLEND_MODES", "NORMAL", "clone", "finishPoly", "value", "tint", "setValue", "fill", "line", "lineStyle", "options", "color", "alpha", "alignment", "native", "width", "lineTextureStyle", "defaultLineStyleOptions", "texture", "Texture", "WHITE", "matrix", "cap", "LINE_CAP", "BUTT", "join", "LINE_JOIN", "MITER", "miterLimit", "Object", "assign", "normalizeColor", "startPoly", "visible", "invert", "reset", "points", "len", "length", "drawShape", "Polygon", "closeStroke", "push", "moveTo", "x", "y", "lineTo", "fromX", "fromY", "_initCurve", "quadraticCurveTo", "cpX", "cpY", "toX", "toY", "QuadraticUtils", "curveTo", "bezierCurveTo", "cpX2", "cpY2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arcTo", "x1", "y1", "x2", "y2", "radius", "result", "ArcUtils", "cx", "cy", "radius2", "startAngle", "endAngle", "anticlockwise", "arc", "PI_2", "startX", "Math", "cos", "startY", "sin", "eps", "closePointEps", "xDiff", "abs", "yDiff", "beginFill", "beginTextureFill", "temp", "shared", "toNumber", "defaultOptions", "endFill", "drawRect", "height", "Rectangle", "drawRoundedRect", "RoundedRectangle", "drawCircle", "Circle", "draw<PERSON><PERSON><PERSON>", "Ellipse", "drawPolygon", "path", "poly", "Array", "isArray", "shape", "drawHole", "clear", "_boundsID", "isFastRect", "data", "graphicsData", "type", "SHAPES", "RECT", "holes", "_render", "renderer", "updateBatches", "batchable", "_populateBatches", "_renderBatched", "batch", "flush", "_renderDirect", "Float32Array", "i", "gI", "style", "buffer", "attribStart", "attribSize", "uvs", "uvsFloat32", "indices", "Uint16Array", "indicesUint16", "start", "size", "_batchRGB", "toRgbArray", "_tintRGB", "_texture", "worldAlpha", "setObjectR<PERSON><PERSON>", "plugins", "calculateVertices", "calculateTints", "l", "render", "_resolveDirectShader", "uniforms", "drawCalls", "translationMatrix", "transform", "worldTransform", "premultiply", "toArray", "bind", "set", "_renderDrawCallDirect", "drawCall", "texArray", "groupTextureCount", "count", "j", "elements", "draw", "maxTextures", "sampleValues", "Int32Array", "Matrix", "default", "UniformGroup", "from", "uSamplers", "program", "_shader", "Shader", "_calculateBounds", "minX", "minY", "maxX", "maxY", "bounds", "_bounds", "addFrame", "containsPoint", "point", "applyInverse", "_TEMP_POINT", "multiply", "toLittleEndianNumber", "wtID", "_worldID", "wt", "a", "b", "c", "d", "tx", "ty", "closePath", "setMatrix", "beginHole", "endHole", "destroy", "dispose", "curves", "Point", "Graphics"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\graphics\\src\\Graphics.ts"], "sourcesContent": ["import {\n    BLEND_MODES,\n    Circle,\n    Color,\n    Ellipse,\n    Matrix,\n    PI_2,\n    Point,\n    Polygon,\n    Rectangle,\n    RoundedRectangle,\n    Shader,\n    SHAPES,\n    State,\n    Texture,\n    UniformGroup,\n} from '@pixi/core';\nimport { Container } from '@pixi/display';\nimport { curves, LINE_CAP, LINE_JOIN } from './const';\nimport { GraphicsGeometry } from './GraphicsGeometry';\nimport { FillStyle } from './styles/FillStyle';\nimport { LineStyle } from './styles/LineStyle';\nimport { ArcUtils, BezierUtils, QuadraticUtils } from './utils';\n\nimport type { BatchDrawCall, ColorSource, IPointData, IShape, Renderer } from '@pixi/core';\nimport type { IDestroyOptions } from '@pixi/display';\n\n/**\n * Batch element computed from Graphics geometry.\n * @memberof PIXI\n */\nexport interface IGraphicsBatchElement\n{\n    vertexData: Float32Array;\n    blendMode: BLEND_MODES;\n    indices: Uint16Array | Uint32Array;\n    uvs: Float32Array;\n    alpha: number;\n    worldAlpha: number;\n    _batchRGB: number[];\n    _tintRGB: number;\n    _texture: Texture;\n}\n\nexport interface IFillStyleOptions\n{\n    color?: ColorSource;\n    alpha?: number;\n    texture?: Texture;\n    matrix?: Matrix;\n}\n\nexport interface ILineStyleOptions extends IFillStyleOptions\n{\n    width?: number;\n    alignment?: number;\n    native?: boolean;\n    cap?: LINE_CAP;\n    join?: LINE_JOIN;\n    miterLimit?: number;\n}\n\n// a default shaders map used by graphics..\nconst DEFAULT_SHADERS: {[key: string]: Shader} = {};\n\nexport interface Graphics extends GlobalMixins.Graphics, Container {}\n\n/**\n * The Graphics class is primarily used to render primitive shapes such as lines, circles and\n * rectangles to the display, and to color and fill them.  However, you can also use a Graphics\n * object to build a list of primitives to use as a mask, or as a complex hitArea.\n *\n * Please note that due to legacy naming conventions, the behavior of some functions in this class\n * can be confusing.  Each call to `drawRect()`, `drawPolygon()`, etc. actually stores that primitive\n * in the Geometry class's GraphicsGeometry object for later use in rendering or hit testing - the\n * functions do not directly draw anything to the screen.  Similarly, the `clear()` function doesn't\n * change the screen, it simply resets the list of primitives, which can be useful if you want to\n * rebuild the contents of an existing Graphics object.\n *\n * Once a GraphicsGeometry list is built, you can re-use it in other Geometry objects as\n * an optimization, by passing it into a new Geometry object's constructor.  Because of this\n * ability, it's important to call `destroy()` on Geometry objects once you are done with them, to\n * properly dereference each GraphicsGeometry and prevent memory leaks.\n * @memberof PIXI\n */\nexport class Graphics extends Container\n{\n    /**\n     * Graphics curves resolution settings. If `adaptive` flag is set to `true`,\n     * the resolution is calculated based on the curve's length to ensure better visual quality.\n     * Adaptive draw works with `bezierCurveTo` and `quadraticCurveTo`.\n     * @static\n     * @property {boolean} [adaptive=true] - flag indicating if the resolution should be adaptive\n     * @property {number} [maxLength=10] - maximal length of a single segment of the curve (if adaptive = false, ignored)\n     * @property {number} [minSegments=8] - minimal number of segments in the curve (if adaptive = false, ignored)\n     * @property {number} [maxSegments=2048] - maximal number of segments in the curve (if adaptive = false, ignored)\n     * @property {number} [epsilon=0.0001] - precision of the curve fitting\n     */\n    public static readonly curves = curves;\n\n    /**\n     * Temporary point to use for containsPoint.\n     * @private\n     */\n    static _TEMP_POINT = new Point();\n\n    /**\n     * Represents the vertex and fragment shaders that processes the geometry and runs on the GPU.\n     * Can be shared between multiple Graphics objects.\n     */\n    public shader: Shader = null;\n\n    /** Renderer plugin for batching */\n    public pluginName = 'batch';\n\n    /**\n     * Current path\n     * @readonly\n     */\n    public currentPath: Polygon = null;\n\n    /** A collections of batches! These can be drawn by the renderer batch system. */\n    protected batches: Array<IGraphicsBatchElement> = [];\n\n    /** Update dirty for limiting calculating tints for batches. */\n    protected batchTint = -1;\n\n    /** Update dirty for limiting calculating batches.*/\n    protected batchDirty = -1;\n\n    /** Copy of the object vertex data. */\n    protected vertexData: Float32Array = null;\n\n    /** Current fill style. */\n    protected _fillStyle: FillStyle = new FillStyle();\n\n    /** Current line style. */\n    protected _lineStyle: LineStyle = new LineStyle();\n\n    /** Current shape transform matrix. */\n    protected _matrix: Matrix = null;\n\n    /** Current hole mode is enabled. */\n    protected _holeMode = false;\n    protected _transformID: number;\n    protected _tintColor: Color;\n\n    /**\n     * Represents the WebGL state the Graphics required to render, excludes shader and geometry. E.g.,\n     * blend mode, culling, depth testing, direction of rendering triangles, backface, etc.\n     */\n    private state: State = State.for2d();\n    private _geometry: GraphicsGeometry;\n\n    /**\n     * Includes vertex positions, face indices, normals, colors, UVs, and\n     * custom attributes within buffers, reducing the cost of passing all\n     * this data to the GPU. Can be shared between multiple Mesh or Graphics objects.\n     * @readonly\n     */\n    public get geometry(): GraphicsGeometry\n    {\n        return this._geometry;\n    }\n\n    /**\n     * @param geometry - Geometry to use, if omitted will create a new GraphicsGeometry instance.\n     */\n    constructor(geometry: GraphicsGeometry = null)\n    {\n        super();\n\n        this._geometry = geometry || new GraphicsGeometry();\n        this._geometry.refCount++;\n\n        /**\n         * When cacheAsBitmap is set to true the graphics object will be rendered as if it was a sprite.\n         * This is useful if your graphics element does not change often, as it will speed up the rendering\n         * of the object in exchange for taking up texture memory. It is also useful if you need the graphics\n         * object to be anti-aliased, because it will be rendered using canvas. This is not recommended if\n         * you are constantly redrawing the graphics element.\n         * @name cacheAsBitmap\n         * @member {boolean}\n         * @memberof PIXI.Graphics#\n         * @default false\n         */\n\n        this._transformID = -1;\n\n        // Set default\n        this._tintColor = new Color(0xFFFFFF);\n        this.blendMode = BLEND_MODES.NORMAL;\n    }\n\n    /**\n     * Creates a new Graphics object with the same values as this one.\n     * Note that only the geometry of the object is cloned, not its transform (position,scale,etc)\n     * @returns - A clone of the graphics object\n     */\n    public clone(): Graphics\n    {\n        this.finishPoly();\n\n        return new Graphics(this._geometry);\n    }\n\n    /**\n     * The blend mode to be applied to the graphic shape. Apply a value of\n     * `PIXI.BLEND_MODES.NORMAL` to reset the blend mode.  Note that, since each\n     * primitive in the GraphicsGeometry list is rendered sequentially, modes\n     * such as `PIXI.BLEND_MODES.ADD` and `PIXI.BLEND_MODES.MULTIPLY` will\n     * be applied per-primitive.\n     * @default PIXI.BLEND_MODES.NORMAL\n     */\n    public set blendMode(value: BLEND_MODES)\n    {\n        this.state.blendMode = value;\n    }\n\n    public get blendMode(): BLEND_MODES\n    {\n        return this.state.blendMode;\n    }\n\n    /**\n     * The tint applied to each graphic shape. This is a hex value. A value of\n     * 0xFFFFFF will remove any tint effect.\n     * @default 0xFFFFFF\n     */\n    public get tint(): ColorSource\n    {\n        return this._tintColor.value;\n    }\n\n    public set tint(value: ColorSource)\n    {\n        this._tintColor.setValue(value);\n    }\n\n    /**\n     * The current fill style.\n     * @readonly\n     */\n    public get fill(): FillStyle\n    {\n        return this._fillStyle;\n    }\n\n    /**\n     * The current line style.\n     * @readonly\n     */\n    public get line(): LineStyle\n    {\n        return this._lineStyle;\n    }\n\n    /**\n     * Specifies the line style used for subsequent calls to Graphics methods such as the lineTo()\n     * method or the drawCircle() method.\n     * @param [width=0] - width of the line to draw, will update the objects stored style\n     * @param [color=0x0] - color of the line to draw, will update the objects stored style\n     * @param [alpha=1] - alpha of the line to draw, will update the objects stored style\n     * @param [alignment=0.5] - alignment of the line to draw, (0 = inner, 0.5 = middle, 1 = outer).\n     *        WebGL only.\n     * @param [native=false] - If true the lines will be draw using LINES instead of TRIANGLE_STRIP\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public lineStyle(width: number, color?: ColorSource, alpha?: number, alignment?: number, native?: boolean): this;\n\n    /**\n     * Specifies the line style used for subsequent calls to Graphics methods such as the lineTo()\n     * method or the drawCircle() method.\n     * @param options - Line style options\n     * @param {number} [options.width=0] - width of the line to draw, will update the objects stored style\n     * @param {PIXI.ColorSource} [options.color=0x0] - color of the line to draw, will update the objects stored style\n     * @param {number} [options.alpha] - alpha of the line to draw, will update the objects stored style\n     * @param {number} [options.alignment=0.5] - alignment of the line to draw, (0 = inner, 0.5 = middle, 1 = outer).\n     *        WebGL only.\n     * @param {boolean} [options.native=false] - If true the lines will be draw using LINES instead of TRIANGLE_STRIP\n     * @param {PIXI.LINE_CAP}[options.cap=PIXI.LINE_CAP.BUTT] - line cap style\n     * @param {PIXI.LINE_JOIN}[options.join=PIXI.LINE_JOIN.MITER] - line join style\n     * @param {number}[options.miterLimit=10] - miter limit ratio\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    public lineStyle(options?: ILineStyleOptions): this;\n\n    public lineStyle(options: ILineStyleOptions | number = null,\n        color: ColorSource = 0x0, alpha?: number, alignment = 0.5, native = false): this\n    {\n        // Support non-object params: (width, color, alpha, alignment, native)\n        if (typeof options === 'number')\n        {\n            options = { width: options, color, alpha, alignment, native } as ILineStyleOptions;\n        }\n\n        return this.lineTextureStyle(options);\n    }\n\n    /**\n     * Like line style but support texture for line fill.\n     * @param [options] - Collection of options for setting line style.\n     * @param {number} [options.width=0] - width of the line to draw, will update the objects stored style\n     * @param {PIXI.Texture} [options.texture=PIXI.Texture.WHITE] - Texture to use\n     * @param {PIXI.ColorSource} [options.color=0x0] - color of the line to draw, will update the objects stored style.\n     *  Default 0xFFFFFF if texture present.\n     * @param {number} [options.alpha=1] - alpha of the line to draw, will update the objects stored style\n     * @param {PIXI.Matrix} [options.matrix=null] - Texture matrix to transform texture\n     * @param {number} [options.alignment=0.5] - alignment of the line to draw, (0 = inner, 0.5 = middle, 1 = outer).\n     *        WebGL only.\n     * @param {boolean} [options.native=false] - If true the lines will be draw using LINES instead of TRIANGLE_STRIP\n     * @param {PIXI.LINE_CAP}[options.cap=PIXI.LINE_CAP.BUTT] - line cap style\n     * @param {PIXI.LINE_JOIN}[options.join=PIXI.LINE_JOIN.MITER] - line join style\n     * @param {number}[options.miterLimit=10] - miter limit ratio\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    public lineTextureStyle(options?: ILineStyleOptions): this\n    {\n        // Apply defaults\n        const defaultLineStyleOptions: ILineStyleOptions = {\n            width: 0,\n            texture: Texture.WHITE,\n            color: options?.texture ? 0xFFFFFF : 0x0,\n            matrix: null,\n            alignment: 0.5,\n            native: false,\n            cap: LINE_CAP.BUTT,\n            join: LINE_JOIN.MITER,\n            miterLimit: 10,\n        };\n\n        options = Object.assign(defaultLineStyleOptions, options);\n\n        this.normalizeColor(options);\n\n        if (this.currentPath)\n        {\n            this.startPoly();\n        }\n\n        const visible = options.width > 0 && options.alpha > 0;\n\n        if (!visible)\n        {\n            this._lineStyle.reset();\n        }\n        else\n        {\n            if (options.matrix)\n            {\n                options.matrix = options.matrix.clone();\n                options.matrix.invert();\n            }\n\n            Object.assign(this._lineStyle, { visible }, options);\n        }\n\n        return this;\n    }\n\n    /**\n     * Start a polygon object internally.\n     * @protected\n     */\n    protected startPoly(): void\n    {\n        if (this.currentPath)\n        {\n            const points = this.currentPath.points;\n            const len = this.currentPath.points.length;\n\n            if (len > 2)\n            {\n                this.drawShape(this.currentPath);\n                this.currentPath = new Polygon();\n                this.currentPath.closeStroke = false;\n                this.currentPath.points.push(points[len - 2], points[len - 1]);\n            }\n        }\n        else\n        {\n            this.currentPath = new Polygon();\n            this.currentPath.closeStroke = false;\n        }\n    }\n\n    /**\n     * Finish the polygon object.\n     * @protected\n     */\n    finishPoly(): void\n    {\n        if (this.currentPath)\n        {\n            if (this.currentPath.points.length > 2)\n            {\n                this.drawShape(this.currentPath);\n                this.currentPath = null;\n            }\n            else\n            {\n                this.currentPath.points.length = 0;\n            }\n        }\n    }\n\n    /**\n     * Moves the current drawing position to x, y.\n     * @param x - the X coordinate to move to\n     * @param y - the Y coordinate to move to\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public moveTo(x: number, y: number): this\n    {\n        this.startPoly();\n        this.currentPath.points[0] = x;\n        this.currentPath.points[1] = y;\n\n        return this;\n    }\n\n    /**\n     * Draws a line using the current line style from the current drawing position to (x, y);\n     * The current drawing position is then set to (x, y).\n     * @param x - the X coordinate to draw to\n     * @param y - the Y coordinate to draw to\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public lineTo(x: number, y: number): this\n    {\n        if (!this.currentPath)\n        {\n            this.moveTo(0, 0);\n        }\n\n        // remove duplicates..\n        const points = this.currentPath.points;\n        const fromX = points[points.length - 2];\n        const fromY = points[points.length - 1];\n\n        if (fromX !== x || fromY !== y)\n        {\n            points.push(x, y);\n        }\n\n        return this;\n    }\n\n    /**\n     * Initialize the curve\n     * @param x\n     * @param y\n     */\n    protected _initCurve(x = 0, y = 0): void\n    {\n        if (this.currentPath)\n        {\n            if (this.currentPath.points.length === 0)\n            {\n                this.currentPath.points = [x, y];\n            }\n        }\n        else\n        {\n            this.moveTo(x, y);\n        }\n    }\n\n    /**\n     * Calculate the points for a quadratic bezier curve and then draws it.\n     * Based on: https://stackoverflow.com/questions/785097/how-do-i-implement-a-bezier-curve-in-c\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public quadraticCurveTo(cpX: number, cpY: number, toX: number, toY: number): this\n    {\n        this._initCurve();\n\n        const points = this.currentPath.points;\n\n        if (points.length === 0)\n        {\n            this.moveTo(0, 0);\n        }\n\n        QuadraticUtils.curveTo(cpX, cpY, toX, toY, points);\n\n        return this;\n    }\n\n    /**\n     * Calculate the points for a bezier curve and then draws it.\n     * @param cpX - Control point x\n     * @param cpY - Control point y\n     * @param cpX2 - Second Control point x\n     * @param cpY2 - Second Control point y\n     * @param toX - Destination point x\n     * @param toY - Destination point y\n     * @returns This Graphics object. Good for chaining method calls\n     */\n    public bezierCurveTo(cpX: number, cpY: number, cpX2: number, cpY2: number, toX: number, toY: number): this\n    {\n        this._initCurve();\n\n        BezierUtils.curveTo(cpX, cpY, cpX2, cpY2, toX, toY, this.currentPath.points);\n\n        return this;\n    }\n\n    /**\n     * The `arcTo` method creates an arc/curve between two tangents on the canvas.\n     * The first tangent is from the start point to the first control point,\n     * and the second tangent is from the first control point to the second control point.\n     * Note that the second control point is not necessarily the end point of the arc.\n     *\n     * \"borrowed\" from https://code.google.com/p/fxcanvas/ - thanks google!\n     * @param x1 - The x-coordinate of the first control point of the arc\n     * @param y1 - The y-coordinate of the first control point of the arc\n     * @param x2 - The x-coordinate of the second control point of the arc\n     * @param y2 - The y-coordinate of the second control point of the arc\n     * @param radius - The radius of the arc\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public arcTo(x1: number, y1: number, x2: number, y2: number, radius: number): this\n    {\n        this._initCurve(x1, y1);\n\n        const points = this.currentPath.points;\n\n        const result = ArcUtils.curveTo(x1, y1, x2, y2, radius, points);\n\n        if (result)\n        {\n            const { cx, cy, radius, startAngle, endAngle, anticlockwise } = result;\n\n            this.arc(cx, cy, radius, startAngle, endAngle, anticlockwise);\n        }\n\n        return this;\n    }\n\n    /**\n     * The arc method creates an arc/curve (used to create circles, or parts of circles).\n     * @param cx - The x-coordinate of the center of the circle\n     * @param cy - The y-coordinate of the center of the circle\n     * @param radius - The radius of the circle\n     * @param startAngle - The starting angle, in radians (0 is at the 3 o'clock position\n     *  of the arc's circle)\n     * @param endAngle - The ending angle, in radians\n     * @param anticlockwise - Specifies whether the drawing should be\n     *  counter-clockwise or clockwise. False is default, and indicates clockwise, while true\n     *  indicates counter-clockwise.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public arc(cx: number, cy: number, radius: number, startAngle: number, endAngle: number, anticlockwise = false): this\n    {\n        if (startAngle === endAngle)\n        {\n            return this;\n        }\n\n        if (!anticlockwise && endAngle <= startAngle)\n        {\n            endAngle += PI_2;\n        }\n        else if (anticlockwise && startAngle <= endAngle)\n        {\n            startAngle += PI_2;\n        }\n\n        const sweep = endAngle - startAngle;\n\n        if (sweep === 0)\n        {\n            return this;\n        }\n\n        const startX = cx + (Math.cos(startAngle) * radius);\n        const startY = cy + (Math.sin(startAngle) * radius);\n        const eps = this._geometry.closePointEps;\n\n        // If the currentPath exists, take its points. Otherwise call `moveTo` to start a path.\n        let points = this.currentPath ? this.currentPath.points : null;\n\n        if (points)\n        {\n            // TODO: make a better fix.\n\n            // We check how far our start is from the last existing point\n            const xDiff = Math.abs(points[points.length - 2] - startX);\n            const yDiff = Math.abs(points[points.length - 1] - startY);\n\n            if (xDiff < eps && yDiff < eps)\n            {\n                // If the point is very close, we don't add it, since this would lead to artifacts\n                // during tessellation due to floating point imprecision.\n            }\n            else\n            {\n                points.push(startX, startY);\n            }\n        }\n        else\n        {\n            this.moveTo(startX, startY);\n            points = this.currentPath.points;\n        }\n\n        ArcUtils.arc(startX, startY, cx, cy, radius, startAngle, endAngle, anticlockwise, points);\n\n        return this;\n    }\n\n    /**\n     * Specifies a simple one-color fill that subsequent calls to other Graphics methods\n     * (such as lineTo() or drawCircle()) use when drawing.\n     * @param {PIXI.ColorSource} color - the color of the fill\n     * @param alpha - the alpha of the fill, will override the color's alpha\n     * @returns - This Graphics object. Suitable for chaining method calls\n     */\n    public beginFill(color: ColorSource = 0, alpha?: number): this\n    {\n        return this.beginTextureFill({ texture: Texture.WHITE, color, alpha });\n    }\n\n    /**\n     * Normalize the color input from options for line style or fill\n     * @param {PIXI.IFillStyleOptions} options - Fill style object.\n     */\n    private normalizeColor(options: Pick<IFillStyleOptions, 'color' | 'alpha'>): void\n    {\n        const temp = Color.shared.setValue(options.color ?? 0);\n\n        options.color = temp.toNumber();\n        options.alpha ??= temp.alpha;\n    }\n\n    /**\n     * Begin the texture fill.\n     * Note: The wrap mode of the texture is forced to REPEAT on render.\n     * @param options - Fill style object.\n     * @param {PIXI.Texture} [options.texture=PIXI.Texture.WHITE] - Texture to fill\n     * @param {PIXI.ColorSource} [options.color=0xffffff] - Background to fill behind texture\n     * @param {number} [options.alpha] - Alpha of fill, overrides the color's alpha\n     * @param {PIXI.Matrix} [options.matrix=null] - Transform matrix\n     * @returns {PIXI.Graphics} This Graphics object. Good for chaining method calls\n     */\n    beginTextureFill(options?: IFillStyleOptions): this\n    {\n        // Apply defaults\n        const defaultOptions: IFillStyleOptions = {\n            texture: Texture.WHITE,\n            color: 0xFFFFFF,\n            matrix: null,\n        };\n\n        options = Object.assign(defaultOptions, options);\n\n        this.normalizeColor(options);\n\n        if (this.currentPath)\n        {\n            this.startPoly();\n        }\n\n        const visible = options.alpha > 0;\n\n        if (!visible)\n        {\n            this._fillStyle.reset();\n        }\n        else\n        {\n            if (options.matrix)\n            {\n                options.matrix = options.matrix.clone();\n                options.matrix.invert();\n            }\n\n            Object.assign(this._fillStyle, { visible }, options);\n        }\n\n        return this;\n    }\n\n    /**\n     * Applies a fill to the lines and shapes that were added since the last call to the beginFill() method.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public endFill(): this\n    {\n        this.finishPoly();\n\n        this._fillStyle.reset();\n\n        return this;\n    }\n\n    /**\n     * Draws a rectangle shape.\n     * @param x - The X coord of the top-left of the rectangle\n     * @param y - The Y coord of the top-left of the rectangle\n     * @param width - The width of the rectangle\n     * @param height - The height of the rectangle\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawRect(x: number, y: number, width: number, height: number): this\n    {\n        return this.drawShape(new Rectangle(x, y, width, height));\n    }\n\n    /**\n     * Draw a rectangle shape with rounded/beveled corners.\n     * @param x - The X coord of the top-left of the rectangle\n     * @param y - The Y coord of the top-left of the rectangle\n     * @param width - The width of the rectangle\n     * @param height - The height of the rectangle\n     * @param radius - Radius of the rectangle corners\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawRoundedRect(x: number, y: number, width: number, height: number, radius: number): this\n    {\n        return this.drawShape(new RoundedRectangle(x, y, width, height, radius));\n    }\n\n    /**\n     * Draws a circle.\n     * @param x - The X coordinate of the center of the circle\n     * @param y - The Y coordinate of the center of the circle\n     * @param radius - The radius of the circle\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawCircle(x: number, y: number, radius: number): this\n    {\n        return this.drawShape(new Circle(x, y, radius));\n    }\n\n    /**\n     * Draws an ellipse.\n     * @param x - The X coordinate of the center of the ellipse\n     * @param y - The Y coordinate of the center of the ellipse\n     * @param width - The half width of the ellipse\n     * @param height - The half height of the ellipse\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawEllipse(x: number, y: number, width: number, height: number): this\n    {\n        return this.drawShape(new Ellipse(x, y, width, height));\n    }\n\n    public drawPolygon(...path: Array<number> | Array<IPointData>): this;\n    public drawPolygon(path: Array<number> | Array<IPointData> | Polygon): this;\n\n    /**\n     * Draws a polygon using the given path.\n     * @param {number[]|PIXI.IPointData[]|PIXI.Polygon} path - The path data used to construct the polygon.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawPolygon(...path: any[]): this\n    {\n        let points: Array<number> | Array<IPointData>;\n        let closeStroke = true;// !!this._fillStyle;\n\n        const poly = path[0] as Polygon;\n\n        // check if data has points..\n        if (poly.points)\n        {\n            closeStroke = poly.closeStroke;\n            points = poly.points;\n        }\n        else\n            if (Array.isArray(path[0]))\n            {\n                points = path[0];\n            }\n            else\n            {\n                points = path;\n            }\n\n        const shape = new Polygon(points);\n\n        shape.closeStroke = closeStroke;\n\n        this.drawShape(shape);\n\n        return this;\n    }\n\n    /**\n     * Draw any shape.\n     * @param {PIXI.Circle|PIXI.Ellipse|PIXI.Polygon|PIXI.Rectangle|PIXI.RoundedRectangle} shape - Shape to draw\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public drawShape(shape: IShape): this\n    {\n        if (!this._holeMode)\n        {\n            this._geometry.drawShape(\n                shape,\n                this._fillStyle.clone(),\n                this._lineStyle.clone(),\n                this._matrix\n            );\n        }\n        else\n        {\n            this._geometry.drawHole(shape, this._matrix);\n        }\n\n        return this;\n    }\n\n    /**\n     * Clears the graphics that were drawn to this Graphics object, and resets fill and line style settings.\n     * @returns - This Graphics object. Good for chaining method calls\n     */\n    public clear(): this\n    {\n        this._geometry.clear();\n        this._lineStyle.reset();\n        this._fillStyle.reset();\n\n        this._boundsID++;\n        this._matrix = null;\n        this._holeMode = false;\n        this.currentPath = null;\n\n        return this;\n    }\n\n    /**\n     * True if graphics consists of one rectangle, and thus, can be drawn like a Sprite and\n     * masked with gl.scissor.\n     * @returns - True if only 1 rect.\n     */\n    public isFastRect(): boolean\n    {\n        const data = this._geometry.graphicsData;\n\n        return data.length === 1\n            && data[0].shape.type === SHAPES.RECT\n            && !data[0].matrix\n            && !data[0].holes.length\n            && !(data[0].lineStyle.visible && data[0].lineStyle.width);\n    }\n\n    /**\n     * Renders the object using the WebGL renderer\n     * @param renderer - The renderer\n     */\n    protected _render(renderer: Renderer): void\n    {\n        this.finishPoly();\n\n        const geometry = this._geometry;\n        // batch part..\n        // batch it!\n\n        geometry.updateBatches();\n\n        if (geometry.batchable)\n        {\n            if (this.batchDirty !== geometry.batchDirty)\n            {\n                this._populateBatches();\n            }\n\n            this._renderBatched(renderer);\n        }\n        else\n        {\n            // no batching...\n            renderer.batch.flush();\n\n            this._renderDirect(renderer);\n        }\n    }\n\n    /** Populating batches for rendering. */\n    protected _populateBatches(): void\n    {\n        const geometry = this._geometry;\n        const blendMode = this.blendMode;\n        const len = geometry.batches.length;\n\n        this.batchTint = -1;\n        this._transformID = -1;\n        this.batchDirty = geometry.batchDirty;\n        this.batches.length = len;\n\n        this.vertexData = new Float32Array(geometry.points);\n\n        for (let i = 0; i < len; i++)\n        {\n            const gI = geometry.batches[i];\n            const color = gI.style.color;\n            const vertexData = new Float32Array(this.vertexData.buffer,\n                gI.attribStart * 4 * 2,\n                gI.attribSize * 2);\n\n            const uvs = new Float32Array(geometry.uvsFloat32.buffer,\n                gI.attribStart * 4 * 2,\n                gI.attribSize * 2);\n\n            const indices = new Uint16Array(geometry.indicesUint16.buffer,\n                gI.start * 2,\n                gI.size);\n\n            const batch = {\n                vertexData,\n                blendMode,\n                indices,\n                uvs,\n                _batchRGB: Color.shared.setValue(color).toRgbArray(),\n                _tintRGB: color,\n                _texture: gI.style.texture,\n                alpha: gI.style.alpha,\n                worldAlpha: 1\n            };\n\n            this.batches[i] = batch;\n        }\n    }\n\n    /**\n     * Renders the batches using the BathedRenderer plugin\n     * @param renderer - The renderer\n     */\n    protected _renderBatched(renderer: Renderer): void\n    {\n        if (!this.batches.length)\n        {\n            return;\n        }\n\n        renderer.batch.setObjectRenderer(renderer.plugins[this.pluginName]);\n\n        this.calculateVertices();\n        this.calculateTints();\n\n        for (let i = 0, l = this.batches.length; i < l; i++)\n        {\n            const batch = this.batches[i];\n\n            batch.worldAlpha = this.worldAlpha * batch.alpha;\n\n            renderer.plugins[this.pluginName].render(batch);\n        }\n    }\n\n    /**\n     * Renders the graphics direct\n     * @param renderer - The renderer\n     */\n    protected _renderDirect(renderer: Renderer): void\n    {\n        const shader = this._resolveDirectShader(renderer);\n\n        const geometry = this._geometry;\n        const worldAlpha = this.worldAlpha;\n        const uniforms = shader.uniforms;\n        const drawCalls = geometry.drawCalls;\n\n        // lets set the transfomr\n        uniforms.translationMatrix = this.transform.worldTransform;\n\n        // and then lets set the tint..\n        Color.shared.setValue(this._tintColor)\n            .premultiply(worldAlpha)\n            .toArray(uniforms.tint);\n\n        // the first draw call, we can set the uniforms of the shader directly here.\n\n        // this means that we can tack advantage of the sync function of pixi!\n        // bind and sync uniforms..\n        // there is a way to optimise this..\n        renderer.shader.bind(shader);\n        renderer.geometry.bind(geometry, shader);\n\n        // set state..\n        renderer.state.set(this.state);\n\n        // then render the rest of them...\n        for (let i = 0, l = drawCalls.length; i < l; i++)\n        {\n            this._renderDrawCallDirect(renderer, geometry.drawCalls[i]);\n        }\n    }\n\n    /**\n     * Renders specific DrawCall\n     * @param renderer\n     * @param drawCall\n     */\n    protected _renderDrawCallDirect(renderer: Renderer, drawCall: BatchDrawCall): void\n    {\n        const { texArray, type, size, start } = drawCall;\n        const groupTextureCount = texArray.count;\n\n        for (let j = 0; j < groupTextureCount; j++)\n        {\n            renderer.texture.bind(texArray.elements[j], j);\n        }\n\n        renderer.geometry.draw(type, size, start);\n    }\n\n    /**\n     * Resolves shader for direct rendering\n     * @param renderer - The renderer\n     */\n    protected _resolveDirectShader(renderer: Renderer): Shader\n    {\n        let shader = this.shader;\n\n        const pluginName = this.pluginName;\n\n        if (!shader)\n        {\n            // if there is no shader here, we can use the default shader.\n            // and that only gets created if we actually need it..\n            // but may be more than one plugins for graphics\n            if (!DEFAULT_SHADERS[pluginName])\n            {\n                const { maxTextures } = renderer.plugins[pluginName];\n                const sampleValues = new Int32Array(maxTextures);\n\n                for (let i = 0; i < maxTextures; i++)\n                {\n                    sampleValues[i] = i;\n                }\n\n                const uniforms = {\n                    tint: new Float32Array([1, 1, 1, 1]),\n                    translationMatrix: new Matrix(),\n                    default: UniformGroup.from({ uSamplers: sampleValues }, true),\n                };\n\n                const program = renderer.plugins[pluginName]._shader.program;\n\n                DEFAULT_SHADERS[pluginName] = new Shader(program, uniforms);\n            }\n\n            shader = DEFAULT_SHADERS[pluginName];\n        }\n\n        return shader;\n    }\n\n    /**\n     * Retrieves the bounds of the graphic shape as a rectangle object.\n     * @see PIXI.GraphicsGeometry#bounds\n     */\n    protected _calculateBounds(): void\n    {\n        this.finishPoly();\n\n        const geometry = this._geometry;\n\n        // skipping when graphics is empty, like a container\n        if (!geometry.graphicsData.length)\n        {\n            return;\n        }\n\n        const { minX, minY, maxX, maxY } = geometry.bounds;\n\n        this._bounds.addFrame(this.transform, minX, minY, maxX, maxY);\n    }\n\n    /**\n     * Tests if a point is inside this graphics object\n     * @param point - the point to test\n     * @returns - the result of the test\n     */\n    public containsPoint(point: IPointData): boolean\n    {\n        this.worldTransform.applyInverse(point, Graphics._TEMP_POINT);\n\n        return this._geometry.containsPoint(Graphics._TEMP_POINT);\n    }\n\n    /** Recalculate the tint by applying tint to batches using Graphics tint. */\n    protected calculateTints(): void\n    {\n        if (this.batchTint !== this.tint)\n        {\n            this.batchTint = this._tintColor.toNumber();\n\n            for (let i = 0; i < this.batches.length; i++)\n            {\n                const batch = this.batches[i];\n\n                batch._tintRGB = Color.shared\n                    .setValue(this._tintColor)\n                    .multiply(batch._batchRGB)\n                    .toLittleEndianNumber();\n            }\n        }\n    }\n\n    /** If there's a transform update or a change to the shape of the geometry, recalculate the vertices. */\n    protected calculateVertices(): void\n    {\n        const wtID = this.transform._worldID;\n\n        if (this._transformID === wtID)\n        {\n            return;\n        }\n\n        this._transformID = wtID;\n\n        const wt = this.transform.worldTransform;\n        const a = wt.a;\n        const b = wt.b;\n        const c = wt.c;\n        const d = wt.d;\n        const tx = wt.tx;\n        const ty = wt.ty;\n\n        const data = this._geometry.points;// batch.vertexDataOriginal;\n        const vertexData = this.vertexData;\n\n        let count = 0;\n\n        for (let i = 0; i < data.length; i += 2)\n        {\n            const x = data[i];\n            const y = data[i + 1];\n\n            vertexData[count++] = (a * x) + (c * y) + tx;\n            vertexData[count++] = (d * y) + (b * x) + ty;\n        }\n    }\n\n    /**\n     * Closes the current path.\n     * @returns - Returns itself.\n     */\n    public closePath(): this\n    {\n        const currentPath = this.currentPath;\n\n        if (currentPath)\n        {\n            // we don't need to add extra point in the end because buildLine will take care of that\n            currentPath.closeStroke = true;\n            // ensure that the polygon is completed, and is available for hit detection\n            // (even if the graphics is not rendered yet)\n            this.finishPoly();\n        }\n\n        return this;\n    }\n\n    /**\n     * Apply a matrix to the positional data.\n     * @param matrix - Matrix to use for transform current shape.\n     * @returns - Returns itself.\n     */\n    public setMatrix(matrix: Matrix): this\n    {\n        this._matrix = matrix;\n\n        return this;\n    }\n\n    /**\n     * Begin adding holes to the last draw shape\n     * IMPORTANT: holes must be fully inside a shape to work\n     * Also weirdness ensues if holes overlap!\n     * Ellipses, Circles, Rectangles and Rounded Rectangles cannot be holes or host for holes in CanvasRenderer,\n     * please use `moveTo` `lineTo`, `quadraticCurveTo` if you rely on pixi-legacy bundle.\n     * @returns - Returns itself.\n     */\n    public beginHole(): this\n    {\n        this.finishPoly();\n        this._holeMode = true;\n\n        return this;\n    }\n\n    /**\n     * End adding holes to the last draw shape.\n     * @returns - Returns itself.\n     */\n    public endHole(): this\n    {\n        this.finishPoly();\n        this._holeMode = false;\n\n        return this;\n    }\n\n    /**\n     * Destroys the Graphics object.\n     * @param options - Options parameter. A boolean will act as if all\n     *  options have been set to that value\n     * @param {boolean} [options.children=false] - if set to true, all the children will have\n     *  their destroy method called as well. 'options' will be passed on to those calls.\n     * @param {boolean} [options.texture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the texture of the child sprite\n     * @param {boolean} [options.baseTexture=false] - Only used for child Sprites if options.children is set to true\n     *  Should it destroy the base texture of the child sprite\n     */\n    public destroy(options?: IDestroyOptions | boolean): void\n    {\n        this._geometry.refCount--;\n        if (this._geometry.refCount === 0)\n        {\n            this._geometry.dispose();\n        }\n\n        this._matrix = null;\n        this.currentPath = null;\n        this._lineStyle.destroy();\n        this._lineStyle = null;\n        this._fillStyle.destroy();\n        this._fillStyle = null;\n        this._geometry = null;\n        this.shader = null;\n        this.vertexData = null;\n        this.batches.length = 0;\n        this.batches = null;\n\n        super.destroy(options);\n    }\n}\n"], "mappings": ";;;;;;;;;;AA+DA,MAAMA,eAAA,GAA2C;EAsBpCC,SAAA,GAAN,MAAMC,UAAA,SAAiBC,SAAA,CAC9B;IAAA;AAAA;AAAA;IAkFIC,YAAYC,QAAA,GAA6B,MACzC;MACU,SA5DV,KAAOC,MAAA,GAAiB,MAGxB,KAAOC,UAAA,GAAa,SAMpB,KAAOC,WAAA,GAAuB,MAG9B,KAAUC,OAAA,GAAwC,IAGlD,KAAUC,SAAA,GAAY,IAGtB,KAAUC,UAAA,GAAa,IAGvB,KAAUC,UAAA,GAA2B,MAG3B,KAAAC,UAAA,GAAwB,IAAIC,SAAA,IAG5B,KAAAC,UAAA,GAAwB,IAAIC,SAAA,IAGtC,KAAUC,OAAA,GAAkB,MAG5B,KAAUC,SAAA,GAAY,IAQd,KAAAC,KAAA,GAAeC,KAAA,CAAMC,KAAA,IAqBzB,KAAKC,SAAA,GAAYjB,QAAA,IAAY,IAAIkB,gBAAA,IACjC,KAAKD,SAAA,CAAUE,QAAA,IAcf,KAAKC,YAAA,GAAe,IAGpB,KAAKC,UAAA,GAAa,IAAIC,KAAA,CAAM,QAAQ,GACpC,KAAKC,SAAA,GAAYC,WAAA,CAAYC,MAAA;IACjC;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAhCA,IAAWzB,SAAA,EACX;MACI,OAAO,KAAKiB,SAAA;IAChB;IAAA;AAAA;AAAA;AAAA;AAAA;IAoCOS,MAAA,EACP;MACI,YAAKC,UAAA,CAAW,GAET,IAAI9B,UAAA,CAAS,KAAKoB,SAAS;IACtC;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAUA,IAAWM,UAAUK,KAAA,EACrB;MACI,KAAKd,KAAA,CAAMS,SAAA,GAAYK,KAAA;IAC3B;IAEA,IAAWL,UAAA,EACX;MACI,OAAO,KAAKT,KAAA,CAAMS,SAAA;IACtB;IAAA;AAAA;AAAA;AAAA;AAAA;IAOA,IAAWM,KAAA,EACX;MACI,OAAO,KAAKR,UAAA,CAAWO,KAAA;IAC3B;IAEA,IAAWC,KAAKD,KAAA,EAChB;MACS,KAAAP,UAAA,CAAWS,QAAA,CAASF,KAAK;IAClC;IAAA;AAAA;AAAA;AAAA;IAMA,IAAWG,KAAA,EACX;MACI,OAAO,KAAKvB,UAAA;IAChB;IAAA;AAAA;AAAA;AAAA;IAMA,IAAWwB,KAAA,EACX;MACI,OAAO,KAAKtB,UAAA;IAChB;IAgCOuB,UAAUC,OAAA,GAAsC,MACnDC,KAAA,GAAqB,GAAKC,KAAA,EAAgBC,SAAA,GAAY,KAAKC,MAAA,GAAS,IACxE;MAEI,OAAI,OAAOJ,OAAA,IAAY,aAEnBA,OAAA,GAAU;QAAEK,KAAA,EAAOL,OAAA;QAASC,KAAA;QAAOC,KAAA;QAAOC,SAAA;QAAWC;MAGlD,SAAKE,gBAAA,CAAiBN,OAAO;IACxC;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAmBOM,iBAAiBN,OAAA,EACxB;MAEI,MAAMO,uBAAA,GAA6C;QAC/CF,KAAA,EAAO;QACPG,OAAA,EAASC,OAAA,CAAQC,KAAA;QACjBT,KAAA,EAAOD,OAAA,EAASQ,OAAA,GAAU,WAAW;QACrCG,MAAA,EAAQ;QACRR,SAAA,EAAW;QACXC,MAAA,EAAQ;QACRQ,GAAA,EAAKC,QAAA,CAASC,IAAA;QACdC,IAAA,EAAMC,SAAA,CAAUC,KAAA;QAChBC,UAAA,EAAY;MAAA;MAGhBlB,OAAA,GAAUmB,MAAA,CAAOC,MAAA,CAAOb,uBAAA,EAAyBP,OAAO,GAExD,KAAKqB,cAAA,CAAerB,OAAO,GAEvB,KAAK/B,WAAA,IAEL,KAAKqD,SAAA,CAAU;MAGnB,MAAMC,OAAA,GAAUvB,OAAA,CAAQK,KAAA,GAAQ,KAAKL,OAAA,CAAQE,KAAA,GAAQ;MAEhD,OAAAqB,OAAA,IAMGvB,OAAA,CAAQW,MAAA,KAERX,OAAA,CAAQW,MAAA,GAASX,OAAA,CAAQW,MAAA,CAAOnB,KAAA,IAChCQ,OAAA,CAAQW,MAAA,CAAOa,MAAA,KAGnBL,MAAA,CAAOC,MAAA,CAAO,KAAK5C,UAAA,EAAY;QAAE+C;MAAA,GAAWvB,OAAO,KAVnD,KAAKxB,UAAA,CAAWiD,KAAA,IAab;IACX;IAAA;AAAA;AAAA;AAAA;IAMUH,UAAA,EACV;MACI,IAAI,KAAKrD,WAAA,EACT;QACI,MAAMyD,MAAA,GAAS,KAAKzD,WAAA,CAAYyD,MAAA;UAC1BC,GAAA,GAAM,KAAK1D,WAAA,CAAYyD,MAAA,CAAOE,MAAA;QAEhCD,GAAA,GAAM,MAEN,KAAKE,SAAA,CAAU,KAAK5D,WAAW,GAC/B,KAAKA,WAAA,GAAc,IAAI6D,OAAA,IACvB,KAAK7D,WAAA,CAAY8D,WAAA,GAAc,IAC/B,KAAK9D,WAAA,CAAYyD,MAAA,CAAOM,IAAA,CAAKN,MAAA,CAAOC,GAAA,GAAM,CAAC,GAAGD,MAAA,CAAOC,GAAA,GAAM,CAAC,CAAC;MAErE,OAGI,KAAK1D,WAAA,GAAc,IAAI6D,OAAA,CACvB,QAAK7D,WAAA,CAAY8D,WAAA,GAAc;IAEvC;IAAA;AAAA;AAAA;AAAA;IAMAtC,WAAA,EACA;MACQ,KAAKxB,WAAA,KAED,KAAKA,WAAA,CAAYyD,MAAA,CAAOE,MAAA,GAAS,KAEjC,KAAKC,SAAA,CAAU,KAAK5D,WAAW,GAC/B,KAAKA,WAAA,GAAc,QAInB,KAAKA,WAAA,CAAYyD,MAAA,CAAOE,MAAA,GAAS;IAG7C;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAQOK,OAAOC,CAAA,EAAWC,CAAA,EACzB;MACI,YAAKb,SAAA,CAAU,GACf,KAAKrD,WAAA,CAAYyD,MAAA,CAAO,CAAC,IAAIQ,CAAA,EAC7B,KAAKjE,WAAA,CAAYyD,MAAA,CAAO,CAAC,IAAIS,CAAA,EAEtB;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IASOC,OAAOF,CAAA,EAAWC,CAAA,EACzB;MACS,KAAKlE,WAAA,IAEN,KAAKgE,MAAA,CAAO,GAAG,CAAC;MAIpB,MAAMP,MAAA,GAAS,KAAKzD,WAAA,CAAYyD,MAAA;QAC1BW,KAAA,GAAQX,MAAA,CAAOA,MAAA,CAAOE,MAAA,GAAS,CAAC;QAChCU,KAAA,GAAQZ,MAAA,CAAOA,MAAA,CAAOE,MAAA,GAAS,CAAC;MAElC,QAAAS,KAAA,KAAUH,CAAA,IAAKI,KAAA,KAAUH,CAAA,KAEzBT,MAAA,CAAOM,IAAA,CAAKE,CAAA,EAAGC,CAAC,GAGb;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;IAOUI,WAAWL,CAAA,GAAI,GAAGC,CAAA,GAAI,GAChC;MACQ,KAAKlE,WAAA,GAED,KAAKA,WAAA,CAAYyD,MAAA,CAAOE,MAAA,KAAW,MAEnC,KAAK3D,WAAA,CAAYyD,MAAA,GAAS,CAACQ,CAAA,EAAGC,CAAC,KAKnC,KAAKF,MAAA,CAAOC,CAAA,EAAGC,CAAC;IAExB;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAWOK,iBAAiBC,GAAA,EAAaC,GAAA,EAAaC,GAAA,EAAaC,GAAA,EAC/D;MACI,KAAKL,UAAA,CAAW;MAEV,MAAAb,MAAA,GAAS,KAAKzD,WAAA,CAAYyD,MAAA;MAEhC,OAAIA,MAAA,CAAOE,MAAA,KAAW,KAElB,KAAKK,MAAA,CAAO,GAAG,CAAC,GAGpBY,cAAA,CAAeC,OAAA,CAAQL,GAAA,EAAKC,GAAA,EAAKC,GAAA,EAAKC,GAAA,EAAKlB,MAAM,GAE1C;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAYOqB,cAAcN,GAAA,EAAaC,GAAA,EAAaM,IAAA,EAAcC,IAAA,EAAcN,GAAA,EAAaC,GAAA,EACxF;MACI,YAAKL,UAAA,CAAW,GAEhBW,WAAA,CAAYJ,OAAA,CAAQL,GAAA,EAAKC,GAAA,EAAKM,IAAA,EAAMC,IAAA,EAAMN,GAAA,EAAKC,GAAA,EAAK,KAAK3E,WAAA,CAAYyD,MAAM,GAEpE;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAgBOyB,MAAMC,EAAA,EAAYC,EAAA,EAAYC,EAAA,EAAYC,EAAA,EAAYC,MAAA,EAC7D;MACS,KAAAjB,UAAA,CAAWa,EAAA,EAAIC,EAAE;MAEtB,MAAM3B,MAAA,GAAS,KAAKzD,WAAA,CAAYyD,MAAA;QAE1B+B,MAAA,GAASC,QAAA,CAASZ,OAAA,CAAQM,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,MAAA,EAAQ9B,MAAM;MAE9D,IAAI+B,MAAA,EACJ;QACU;UAAEE,EAAA;UAAIC,EAAA;UAAIJ,MAAA,EAAAK,OAAA;UAAQC,UAAA;UAAYC,QAAA;UAAUC;QAAkB,IAAAP,MAAA;QAEhE,KAAKQ,GAAA,CAAIN,EAAA,EAAIC,EAAA,EAAIC,OAAA,EAAQC,UAAA,EAAYC,QAAA,EAAUC,aAAa;MAChE;MAEO;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAeOC,IAAIN,EAAA,EAAYC,EAAA,EAAYJ,MAAA,EAAgBM,UAAA,EAAoBC,QAAA,EAAkBC,aAAA,GAAgB,IACzG;MACI,IAAIF,UAAA,KAAeC,QAAA,EAER;MAcX,IAXI,CAACC,aAAA,IAAiBD,QAAA,IAAYD,UAAA,GAE9BC,QAAA,IAAYG,IAAA,GAEPF,aAAA,IAAiBF,UAAA,IAAcC,QAAA,KAEpCD,UAAA,IAAcI,IAAA,GAGJH,QAAA,GAAWD,UAAA,KAEX,GAEH;MAGX,MAAMK,MAAA,GAASR,EAAA,GAAMS,IAAA,CAAKC,GAAA,CAAIP,UAAU,IAAIN,MAAA;QACtCc,MAAA,GAASV,EAAA,GAAMQ,IAAA,CAAKG,GAAA,CAAIT,UAAU,IAAIN,MAAA;QACtCgB,GAAA,GAAM,KAAKzF,SAAA,CAAU0F,aAAA;MAG3B,IAAI/C,MAAA,GAAS,KAAKzD,WAAA,GAAc,KAAKA,WAAA,CAAYyD,MAAA,GAAS;MAE1D,IAAIA,MAAA,EACJ;QAII,MAAMgD,KAAA,GAAQN,IAAA,CAAKO,GAAA,CAAIjD,MAAA,CAAOA,MAAA,CAAOE,MAAA,GAAS,CAAC,IAAIuC,MAAM;UACnDS,KAAA,GAAQR,IAAA,CAAKO,GAAA,CAAIjD,MAAA,CAAOA,MAAA,CAAOE,MAAA,GAAS,CAAC,IAAI0C,MAAM;QAErDI,KAAA,GAAQF,GAAA,IAAOI,KAAA,GAAQJ,GAAA,IAOvB9C,MAAA,CAAOM,IAAA,CAAKmC,MAAA,EAAQG,MAAM;MAElC,OAGI,KAAKrC,MAAA,CAAOkC,MAAA,EAAQG,MAAM,GAC1B5C,MAAA,GAAS,KAAKzD,WAAA,CAAYyD,MAAA;MAGrB,OAAAgC,QAAA,CAAAO,GAAA,CAAIE,MAAA,EAAQG,MAAA,EAAQX,EAAA,EAAIC,EAAA,EAAIJ,MAAA,EAAQM,UAAA,EAAYC,QAAA,EAAUC,aAAA,EAAetC,MAAM,GAEjF;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IASOmD,UAAU5E,KAAA,GAAqB,GAAGC,KAAA,EACzC;MACW,YAAK4E,gBAAA,CAAiB;QAAEtE,OAAA,EAASC,OAAA,CAAQC,KAAA;QAAOT,KAAA;QAAOC;MAAA,CAAO;IACzE;IAAA;AAAA;AAAA;AAAA;IAMQmB,eAAerB,OAAA,EACvB;MACI,MAAM+E,IAAA,GAAO3F,KAAA,CAAM4F,MAAA,CAAOpF,QAAA,CAASI,OAAA,CAAQC,KAAA,IAAS,CAAC;MAE7CD,OAAA,CAAAC,KAAA,GAAQ8E,IAAA,CAAKE,QAAA,IACrBjF,OAAA,CAAQE,KAAA,KAARF,OAAA,CAAQE,KAAA,GAAU6E,IAAA,CAAK7E,KAAA;IAC3B;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAYA4E,iBAAiB9E,OAAA,EACjB;MAEI,MAAMkF,cAAA,GAAoC;QACtC1E,OAAA,EAASC,OAAA,CAAQC,KAAA;QACjBT,KAAA,EAAO;QACPU,MAAA,EAAQ;MAAA;MAGZX,OAAA,GAAUmB,MAAA,CAAOC,MAAA,CAAO8D,cAAA,EAAgBlF,OAAO,GAE/C,KAAKqB,cAAA,CAAerB,OAAO,GAEvB,KAAK/B,WAAA,IAEL,KAAKqD,SAAA,CAAU;MAGb,MAAAC,OAAA,GAAUvB,OAAA,CAAQE,KAAA,GAAQ;MAE3B,OAAAqB,OAAA,IAMGvB,OAAA,CAAQW,MAAA,KAERX,OAAA,CAAQW,MAAA,GAASX,OAAA,CAAQW,MAAA,CAAOnB,KAAA,IAChCQ,OAAA,CAAQW,MAAA,CAAOa,MAAA,KAGnBL,MAAA,CAAOC,MAAA,CAAO,KAAK9C,UAAA,EAAY;QAAEiD;MAAA,GAAWvB,OAAO,KAVnD,KAAK1B,UAAA,CAAWmD,KAAA,IAab;IACX;IAAA;AAAA;AAAA;AAAA;IAMO0D,QAAA,EACP;MACI,YAAK1F,UAAA,IAEL,KAAKnB,UAAA,CAAWmD,KAAA,CAET;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAUO2D,SAASlD,CAAA,EAAWC,CAAA,EAAW9B,KAAA,EAAegF,MAAA,EACrD;MACW,YAAKxD,SAAA,CAAU,IAAIyD,SAAA,CAAUpD,CAAA,EAAGC,CAAA,EAAG9B,KAAA,EAAOgF,MAAM,CAAC;IAC5D;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAWOE,gBAAgBrD,CAAA,EAAWC,CAAA,EAAW9B,KAAA,EAAegF,MAAA,EAAgB7B,MAAA,EAC5E;MACW,YAAK3B,SAAA,CAAU,IAAI2D,gBAAA,CAAiBtD,CAAA,EAAGC,CAAA,EAAG9B,KAAA,EAAOgF,MAAA,EAAQ7B,MAAM,CAAC;IAC3E;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IASOiC,WAAWvD,CAAA,EAAWC,CAAA,EAAWqB,MAAA,EACxC;MACI,OAAO,KAAK3B,SAAA,CAAU,IAAI6D,MAAA,CAAOxD,CAAA,EAAGC,CAAA,EAAGqB,MAAM,CAAC;IAClD;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAUOmC,YAAYzD,CAAA,EAAWC,CAAA,EAAW9B,KAAA,EAAegF,MAAA,EACxD;MACW,YAAKxD,SAAA,CAAU,IAAI+D,OAAA,CAAQ1D,CAAA,EAAGC,CAAA,EAAG9B,KAAA,EAAOgF,MAAM,CAAC;IAC1D;IAAA;AAAA;AAAA;AAAA;AAAA;IAUOQ,YAAA,GAAeC,IAAA,EACtB;MACI,IAAIpE,MAAA;QACAK,WAAA,GAAc;MAEZ,MAAAgE,IAAA,GAAOD,IAAA,CAAK,CAAC;MAGfC,IAAA,CAAKrE,MAAA,IAELK,WAAA,GAAcgE,IAAA,CAAKhE,WAAA,EACnBL,MAAA,GAASqE,IAAA,CAAKrE,MAAA,IAGVsE,KAAA,CAAMC,OAAA,CAAQH,IAAA,CAAK,CAAC,CAAC,IAErBpE,MAAA,GAASoE,IAAA,CAAK,CAAC,IAIfpE,MAAA,GAASoE,IAAA;MAGX,MAAAI,KAAA,GAAQ,IAAIpE,OAAA,CAAQJ,MAAM;MAEhC,OAAAwE,KAAA,CAAMnE,WAAA,GAAcA,WAAA,EAEpB,KAAKF,SAAA,CAAUqE,KAAK,GAEb;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;IAOOrE,UAAUqE,KAAA,EACjB;MACS,YAAKvH,SAAA,GAWN,KAAKI,SAAA,CAAUoH,QAAA,CAASD,KAAA,EAAO,KAAKxH,OAAO,IAT3C,KAAKK,SAAA,CAAU8C,SAAA,CACXqE,KAAA,EACA,KAAK5H,UAAA,CAAWkB,KAAA,CAAM,GACtB,KAAKhB,UAAA,CAAWgB,KAAA,CAAM,GACtB,KAAKd,OAQN;IACX;IAAA;AAAA;AAAA;AAAA;IAMO0H,MAAA,EACP;MACS,YAAArH,SAAA,CAAUqH,KAAA,IACf,KAAK5H,UAAA,CAAWiD,KAAA,IAChB,KAAKnD,UAAA,CAAWmD,KAAA,IAEhB,KAAK4E,SAAA,IACL,KAAK3H,OAAA,GAAU,MACf,KAAKC,SAAA,GAAY,IACjB,KAAKV,WAAA,GAAc,MAEZ;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;IAOOqI,WAAA,EACP;MACU,MAAAC,IAAA,GAAO,KAAKxH,SAAA,CAAUyH,YAAA;MAE5B,OAAOD,IAAA,CAAK3E,MAAA,KAAW,KAChB2E,IAAA,CAAK,CAAC,EAAEL,KAAA,CAAMO,IAAA,KAASC,MAAA,CAAOC,IAAA,IAC9B,CAACJ,IAAA,CAAK,CAAC,EAAE5F,MAAA,IACT,CAAC4F,IAAA,CAAK,CAAC,EAAEK,KAAA,CAAMhF,MAAA,IACf,EAAE2E,IAAA,CAAK,CAAC,EAAExG,SAAA,CAAUwB,OAAA,IAAWgF,IAAA,CAAK,CAAC,EAAExG,SAAA,CAAUM,KAAA;IAC5D;IAAA;AAAA;AAAA;AAAA;IAMUwG,QAAQC,QAAA,EAClB;MACI,KAAKrH,UAAA,CAAW;MAEhB,MAAM3B,QAAA,GAAW,KAAKiB,SAAA;MAIbjB,QAAA,CAAAiJ,aAAA,IAELjJ,QAAA,CAASkJ,SAAA,IAEL,KAAK5I,UAAA,KAAeN,QAAA,CAASM,UAAA,IAE7B,KAAK6I,gBAAA,IAGT,KAAKC,cAAA,CAAeJ,QAAQ,MAK5BA,QAAA,CAASK,KAAA,CAAMC,KAAA,CAAM,GAErB,KAAKC,aAAA,CAAcP,QAAQ;IAEnC;IAAA;IAGUG,iBAAA,EACV;MACU,MAAAnJ,QAAA,GAAW,KAAKiB,SAAA;QAChBM,SAAA,GAAY,KAAKA,SAAA;QACjBsC,GAAA,GAAM7D,QAAA,CAASI,OAAA,CAAQ0D,MAAA;MAE7B,KAAKzD,SAAA,GAAY,IACjB,KAAKe,YAAA,GAAe,IACpB,KAAKd,UAAA,GAAaN,QAAA,CAASM,UAAA,EAC3B,KAAKF,OAAA,CAAQ0D,MAAA,GAASD,GAAA,EAEtB,KAAKtD,UAAA,GAAa,IAAIiJ,YAAA,CAAaxJ,QAAA,CAAS4D,MAAM;MAElD,SAAS6F,CAAA,GAAI,GAAGA,CAAA,GAAI5F,GAAA,EAAK4F,CAAA,IACzB;QACU,MAAAC,EAAA,GAAK1J,QAAA,CAASI,OAAA,CAAQqJ,CAAC;UACvBtH,KAAA,GAAQuH,EAAA,CAAGC,KAAA,CAAMxH,KAAA;UACjB5B,UAAA,GAAa,IAAIiJ,YAAA,CAAa,KAAKjJ,UAAA,CAAWqJ,MAAA,EAChDF,EAAA,CAAGG,WAAA,GAAc,IAAI,GACrBH,EAAA,CAAGI,UAAA,GAAa;UAEdC,GAAA,GAAM,IAAIP,YAAA,CAAaxJ,QAAA,CAASgK,UAAA,CAAWJ,MAAA,EAC7CF,EAAA,CAAGG,WAAA,GAAc,IAAI,GACrBH,EAAA,CAAGI,UAAA,GAAa;UAEdG,OAAA,GAAU,IAAIC,WAAA,CAAYlK,QAAA,CAASmK,aAAA,CAAcP,MAAA,EACnDF,EAAA,CAAGU,KAAA,GAAQ,GACXV,EAAA,CAAGW,IAAA;UAEDhB,KAAA,GAAQ;YACV9I,UAAA;YACAgB,SAAA;YACA0I,OAAA;YACAF,GAAA;YACAO,SAAA,EAAWhJ,KAAA,CAAM4F,MAAA,CAAOpF,QAAA,CAASK,KAAK,EAAEoI,UAAA,CAAW;YACnDC,QAAA,EAAUrI,KAAA;YACVsI,QAAA,EAAUf,EAAA,CAAGC,KAAA,CAAMjH,OAAA;YACnBN,KAAA,EAAOsH,EAAA,CAAGC,KAAA,CAAMvH,KAAA;YAChBsI,UAAA,EAAY;UAAA;QAGX,KAAAtK,OAAA,CAAQqJ,CAAC,IAAIJ,KAAA;MACtB;IACJ;IAAA;AAAA;AAAA;AAAA;IAMUD,eAAeJ,QAAA,EACzB;MACS,SAAK5I,OAAA,CAAQ0D,MAAA,EAKlB;QAAAkF,QAAA,CAASK,KAAA,CAAMsB,iBAAA,CAAkB3B,QAAA,CAAS4B,OAAA,CAAQ,KAAK1K,UAAU,CAAC,GAElE,KAAK2K,iBAAA,CACL,QAAKC,cAAA,CAAe;QAEX,SAAArB,CAAA,GAAI,GAAGsB,CAAA,GAAI,KAAK3K,OAAA,CAAQ0D,MAAA,EAAQ2F,CAAA,GAAIsB,CAAA,EAAGtB,CAAA,IAChD;UACU,MAAAJ,KAAA,GAAQ,KAAKjJ,OAAA,CAAQqJ,CAAC;UAEtBJ,KAAA,CAAAqB,UAAA,GAAa,KAAKA,UAAA,GAAarB,KAAA,CAAMjH,KAAA,EAE3C4G,QAAA,CAAS4B,OAAA,CAAQ,KAAK1K,UAAU,EAAE8K,MAAA,CAAO3B,KAAK;QAClD;MAAA;IACJ;IAAA;AAAA;AAAA;AAAA;IAMUE,cAAcP,QAAA,EACxB;MACI,MAAM/I,MAAA,GAAS,KAAKgL,oBAAA,CAAqBjC,QAAQ;QAE3ChJ,QAAA,GAAW,KAAKiB,SAAA;QAChByJ,UAAA,GAAa,KAAKA,UAAA;QAClBQ,QAAA,GAAWjL,MAAA,CAAOiL,QAAA;QAClBC,SAAA,GAAYnL,QAAA,CAASmL,SAAA;MAG3BD,QAAA,CAASE,iBAAA,GAAoB,KAAKC,SAAA,CAAUC,cAAA,EAG5ChK,KAAA,CAAM4F,MAAA,CAAOpF,QAAA,CAAS,KAAKT,UAAU,EAChCkK,WAAA,CAAYb,UAAU,EACtBc,OAAA,CAAQN,QAAA,CAASrJ,IAAI,GAO1BmH,QAAA,CAAS/I,MAAA,CAAOwL,IAAA,CAAKxL,MAAM,GAC3B+I,QAAA,CAAShJ,QAAA,CAASyL,IAAA,CAAKzL,QAAA,EAAUC,MAAM,GAGvC+I,QAAA,CAASlI,KAAA,CAAM4K,GAAA,CAAI,KAAK5K,KAAK;MAG7B,SAAS2I,CAAA,GAAI,GAAGsB,CAAA,GAAII,SAAA,CAAUrH,MAAA,EAAQ2F,CAAA,GAAIsB,CAAA,EAAGtB,CAAA,IAEzC,KAAKkC,qBAAA,CAAsB3C,QAAA,EAAUhJ,QAAA,CAASmL,SAAA,CAAU1B,CAAC,CAAC;IAElE;IAAA;AAAA;AAAA;AAAA;AAAA;IAOUkC,sBAAsB3C,QAAA,EAAoB4C,QAAA,EACpD;MACU;UAAEC,QAAA;UAAUlD,IAAA;UAAM0B,IAAA;UAAMD;QAAU,IAAAwB,QAAA;QAClCE,iBAAA,GAAoBD,QAAA,CAASE,KAAA;MAE1B,SAAAC,CAAA,GAAI,GAAGA,CAAA,GAAIF,iBAAA,EAAmBE,CAAA,IAEnChD,QAAA,CAAStG,OAAA,CAAQ+I,IAAA,CAAKI,QAAA,CAASI,QAAA,CAASD,CAAC,GAAGA,CAAC;MAGjDhD,QAAA,CAAShJ,QAAA,CAASkM,IAAA,CAAKvD,IAAA,EAAM0B,IAAA,EAAMD,KAAK;IAC5C;IAAA;AAAA;AAAA;AAAA;IAMUa,qBAAqBjC,QAAA,EAC/B;MACI,IAAI/I,MAAA,GAAS,KAAKA,MAAA;MAElB,MAAMC,UAAA,GAAa,KAAKA,UAAA;MAExB,IAAI,CAACD,MAAA,EACL;QAIQ,KAACN,eAAA,CAAgBO,UAAU,GAC/B;UACU;cAAEiM;YAAA,IAAgBnD,QAAA,CAAS4B,OAAA,CAAQ1K,UAAU;YAC7CkM,YAAA,GAAe,IAAIC,UAAA,CAAWF,WAAW;UAEtC,SAAA1C,CAAA,GAAI,GAAGA,CAAA,GAAI0C,WAAA,EAAa1C,CAAA,IAE7B2C,YAAA,CAAa3C,CAAC,IAAIA,CAAA;UAGtB,MAAMyB,QAAA,GAAW;cACbrJ,IAAA,EAAM,IAAI2H,YAAA,CAAa,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;cACnC4B,iBAAA,EAAmB,IAAIkB,MAAA,CAAO;cAC9BC,OAAA,EAASC,YAAA,CAAaC,IAAA,CAAK;gBAAEC,SAAA,EAAWN;cAAA,GAAgB,EAAI;YAAA;YAG1DO,OAAA,GAAU3D,QAAA,CAAS4B,OAAA,CAAQ1K,UAAU,EAAE0M,OAAA,CAAQD,OAAA;UAErDhN,eAAA,CAAgBO,UAAU,IAAI,IAAI2M,MAAA,CAAOF,OAAA,EAASzB,QAAQ;QAC9D;QAEAjL,MAAA,GAASN,eAAA,CAAgBO,UAAU;MACvC;MAEO,OAAAD,MAAA;IACX;IAAA;AAAA;AAAA;AAAA;IAMU6M,iBAAA,EACV;MACI,KAAKnL,UAAA,CAAW;MAEhB,MAAM3B,QAAA,GAAW,KAAKiB,SAAA;MAGlB,KAACjB,QAAA,CAAS0I,YAAA,CAAa5E,MAAA,EAEvB;MAGJ,MAAM;QAAEiJ,IAAA;QAAMC,IAAA;QAAMC,IAAA;QAAMC;MAAA,IAASlN,QAAA,CAASmN,MAAA;MAE5C,KAAKC,OAAA,CAAQC,QAAA,CAAS,KAAKhC,SAAA,EAAW0B,IAAA,EAAMC,IAAA,EAAMC,IAAA,EAAMC,IAAI;IAChE;IAAA;AAAA;AAAA;AAAA;AAAA;IAOOI,cAAcC,KAAA,EACrB;MACS,YAAAjC,cAAA,CAAekC,YAAA,CAAaD,KAAA,EAAO1N,UAAA,CAAS4N,WAAW,GAErD,KAAKxM,SAAA,CAAUqM,aAAA,CAAczN,UAAA,CAAS4N,WAAW;IAC5D;IAAA;IAGU3C,eAAA,EACV;MACQ,SAAKzK,SAAA,KAAc,KAAKwB,IAAA,EAC5B;QACS,KAAAxB,SAAA,GAAY,KAAKgB,UAAA,CAAW8F,QAAA,CAAS;QAE1C,SAASsC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKrJ,OAAA,CAAQ0D,MAAA,EAAQ2F,CAAA,IACzC;UACU,MAAAJ,KAAA,GAAQ,KAAKjJ,OAAA,CAAQqJ,CAAC;UAEtBJ,KAAA,CAAAmB,QAAA,GAAWlJ,KAAA,CAAM4F,MAAA,CAClBpF,QAAA,CAAS,KAAKT,UAAU,EACxBqM,QAAA,CAASrE,KAAA,CAAMiB,SAAS,EACxBqD,oBAAA,CAAqB;QAC9B;MACJ;IACJ;IAAA;IAGU9C,kBAAA,EACV;MACU,MAAA+C,IAAA,GAAO,KAAKvC,SAAA,CAAUwC,QAAA;MAE5B,IAAI,KAAKzM,YAAA,KAAiBwM,IAAA,EAEtB;MAGJ,KAAKxM,YAAA,GAAewM,IAAA;MAEd,MAAAE,EAAA,GAAK,KAAKzC,SAAA,CAAUC,cAAA;QACpByC,CAAA,GAAID,EAAA,CAAGC,CAAA;QACPC,CAAA,GAAIF,EAAA,CAAGE,CAAA;QACPC,CAAA,GAAIH,EAAA,CAAGG,CAAA;QACPC,CAAA,GAAIJ,EAAA,CAAGI,CAAA;QACPC,EAAA,GAAKL,EAAA,CAAGK,EAAA;QACRC,EAAA,GAAKN,EAAA,CAAGM,EAAA;QAER3F,IAAA,GAAO,KAAKxH,SAAA,CAAU2C,MAAA;QACtBrD,UAAA,GAAa,KAAKA,UAAA;MAExB,IAAIwL,KAAA,GAAQ;MAEZ,SAAStC,CAAA,GAAI,GAAGA,CAAA,GAAIhB,IAAA,CAAK3E,MAAA,EAAQ2F,CAAA,IAAK,GACtC;QACI,MAAMrF,CAAA,GAAIqE,IAAA,CAAKgB,CAAC;UACVpF,CAAA,GAAIoE,IAAA,CAAKgB,CAAA,GAAI,CAAC;QAEpBlJ,UAAA,CAAWwL,KAAA,EAAO,IAAKgC,CAAA,GAAI3J,CAAA,GAAM6J,CAAA,GAAI5J,CAAA,GAAK8J,EAAA,EAC1C5N,UAAA,CAAWwL,KAAA,EAAO,IAAKmC,CAAA,GAAI7J,CAAA,GAAM2J,CAAA,GAAI5J,CAAA,GAAKgK,EAAA;MAC9C;IACJ;IAAA;AAAA;AAAA;AAAA;IAMOC,UAAA,EACP;MACI,MAAMlO,WAAA,GAAc,KAAKA,WAAA;MAEzB,OAAIA,WAAA,KAGAA,WAAA,CAAY8D,WAAA,GAAc,IAG1B,KAAKtC,UAAA,KAGF;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;IAOO2M,UAAUzL,MAAA,EACjB;MACI,YAAKjC,OAAA,GAAUiC,MAAA,EAER;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAUO0L,UAAA,EACP;MACI,YAAK5M,UAAA,IACL,KAAKd,SAAA,GAAY,IAEV;IACX;IAAA;AAAA;AAAA;AAAA;IAMO2N,QAAA,EACP;MACI,YAAK7M,UAAA,IACL,KAAKd,SAAA,GAAY,IAEV;IACX;IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAaO4N,QAAQvM,OAAA,EACf;MACS,KAAAjB,SAAA,CAAUE,QAAA,IACX,KAAKF,SAAA,CAAUE,QAAA,KAAa,KAE5B,KAAKF,SAAA,CAAUyN,OAAA,IAGnB,KAAK9N,OAAA,GAAU,MACf,KAAKT,WAAA,GAAc,MACnB,KAAKO,UAAA,CAAW+N,OAAA,IAChB,KAAK/N,UAAA,GAAa,MAClB,KAAKF,UAAA,CAAWiO,OAAA,IAChB,KAAKjO,UAAA,GAAa,MAClB,KAAKS,SAAA,GAAY,MACjB,KAAKhB,MAAA,GAAS,MACd,KAAKM,UAAA,GAAa,MAClB,KAAKH,OAAA,CAAQ0D,MAAA,GAAS,GACtB,KAAK1D,OAAA,GAAU,MAEf,MAAMqO,OAAA,CAAQvM,OAAO;IACzB;EACJ;AA7nCatC,SAAA,CAac+O,MAAA,GAASA,MAAA;AAAA;AAAA;AAAA;AAAA;AAbvB/O,SAAA,CAmBF6N,WAAA,GAAc,IAAImB,KAAA;AAnBtB,IAAMC,QAAA,GAANjP,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}