{"ast": null, "code": "import { Mesh } from \"./Mesh.mjs\";\nimport { MeshBatchUvs } from \"./MeshBatchUvs.mjs\";\nimport { MeshGeometry } from \"./MeshGeometry.mjs\";\nimport { MeshMaterial } from \"./MeshMaterial.mjs\";\nexport { Mesh, MeshBatchUvs, MeshGeometry, MeshMaterial };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import { Mesh } from \"./Mesh.mjs\";\nimport { MeshBatchUvs } from \"./MeshBatchUvs.mjs\";\nimport { MeshGeometry } from \"./MeshGeometry.mjs\";\nimport { MeshMaterial } from \"./MeshMaterial.mjs\";\nexport {\n  Mesh,\n  MeshBatchUvs,\n  MeshGeometry,\n  MeshMaterial\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}