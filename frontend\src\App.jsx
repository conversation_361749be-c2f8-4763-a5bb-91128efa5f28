import React, { useEffect, useState } from "react";
import axios from "axios";
import WorldMap from './WorldMap';

// Enhanced game constants
const WIN_CONDITIONS = {
  treasury: 10000,
  prestige: 100,
  provinces: 10
};

const LOSE_CONDITIONS = {
  stability: 10,
  treasury: -5000,
  legitimacy: 20
};

export default function App() {
  // Core game state
  const [countries, setCountries] = useState([]);
  const [provinces, setProvinces] = useState([]);
  const [playerCountry, setPlayerCountry] = useState(null);
  const [turn, setTurn] = useState(1);
  const [message, setMessage] = useState("");
  const [gamePhase, setGamePhase] = useState("loading"); // loading, country_selection, playing, victory, defeat

  // Enhanced game data
  const [globalResources, setGlobalResources] = useState({});
  const [technologies, setTechnologies] = useState({});
  const [selectedProvince, setSelectedProvince] = useState(null);
  const [activeTab, setActiveTab] = useState("overview"); // overview, economy, military, diplomacy, technology

  // UI state
  const [actionTaken, setActionTaken] = useState(false);
  const [event, setEvent] = useState(null);
  const [notifications, setNotifications] = useState([]);

  // Load initial game data
  useEffect(() => {
    const loadGameData = async () => {
      try {
        setGamePhase("loading");

        // Load all game data in parallel
        const [countriesRes, provincesRes, resourcesRes, techRes] = await Promise.all([
          axios.get("http://localhost:8000/countries"),
          axios.get("http://localhost:8000/provinces"),
          axios.get("http://localhost:8000/resources"),
          axios.get("http://localhost:8000/technologies")
        ]);

        console.log("Game data loaded:", {
          countries: countriesRes.data.length,
          provinces: provincesRes.data.length,
          resources: Object.keys(resourcesRes.data).length,
          technologies: Object.keys(techRes.data).length
        });

        setCountries(countriesRes.data);
        setProvinces(provincesRes.data);
        setGlobalResources(resourcesRes.data);
        setTechnologies(techRes.data);
        setGamePhase("country_selection");

      } catch (error) {
        console.error("Failed to load game data:", error);
        setMessage("Failed to load game data. Please refresh the page.");
      }
    };

    loadGameData();
  }, []);

  // Fetch events when turn changes
  useEffect(() => {
    if (!playerCountry || gamePhase !== "playing") return;

    const fetchTurnEvents = async () => {
      try {
        const eventRes = await axios.get(`http://localhost:8000/event?country=${encodeURIComponent(playerCountry.name)}`);
        setEvent(eventRes.data);
      } catch (error) {
        console.error("Failed to fetch events:", error);
      }
    };

    fetchTurnEvents();
  }, [turn, playerCountry, gamePhase]);

  // Game phase handlers
  const selectCountry = (country) => {
    setPlayerCountry(country);
    setGamePhase("playing");
    setActiveTab("overview");
    addNotification(`Welcome, ${country.ruler_name} of ${country.name}!`, "success");
  };

  const addNotification = (text, type = "info") => {
    const notification = {
      id: Date.now(),
      text,
      type,
      timestamp: new Date().toLocaleTimeString()
    };
    setNotifications(prev => [notification, ...prev.slice(0, 4)]); // Keep only 5 notifications
  };

  // Enhanced turn advancement
  const advanceTurn = async () => {
    try {
      setMessage("Processing turn...");
      const response = await axios.post("http://localhost:8000/turn");

      // Update all game state
      setTurn(response.data.turn);
      setCountries(response.data.countries);
      setProvinces(response.data.provinces);
      setGlobalResources(response.data.global_resources);

      // Update player country reference
      const updatedPlayerCountry = response.data.countries.find(c => c.name === playerCountry.name);
      if (updatedPlayerCountry) {
        setPlayerCountry(updatedPlayerCountry);
      }

      setActionTaken(false);
      setMessage("");
      addNotification(`Turn ${response.data.turn} begins`, "info");

      // Check win/lose conditions
      checkGameEnd(updatedPlayerCountry);

    } catch (error) {
      console.error("Failed to advance turn:", error);
      setMessage("Failed to advance turn. Please try again.");
    }
  };

  const checkGameEnd = (country) => {
    if (!country) return;

    // Check victory conditions
    if (country.treasury >= WIN_CONDITIONS.treasury ||
        country.prestige >= WIN_CONDITIONS.prestige ||
        country.provinces.length >= WIN_CONDITIONS.provinces) {
      setGamePhase("victory");
      return;
    }

    // Check defeat conditions
    if (country.stability <= LOSE_CONDITIONS.stability ||
        country.treasury <= LOSE_CONDITIONS.treasury ||
        country.legitimacy <= LOSE_CONDITIONS.legitimacy) {
      setGamePhase("defeat");
      return;
    }
  };

  // Research technology
  const startResearch = async (category, techName) => {
    try {
      const response = await axios.post("http://localhost:8000/research", {
        country: playerCountry.name,
        category,
        technology: techName
      });

      if (response.data.success) {
        addNotification(response.data.message, "success");
        // Refresh country data
        const countryRes = await axios.get(`http://localhost:8000/country/${playerCountry.name}`);
        setPlayerCountry(countryRes.data);
      } else {
        addNotification(response.data.error, "error");
      }
    } catch (error) {
      console.error("Research failed:", error);
      addNotification("Failed to start research", "error");
    }
  };

  // Tab content renderer
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div>
            <h2>🏛️ Nation Overview</h2>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>
              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>
                <h3>💰 Economy</h3>
                <p><strong>Monthly Income:</strong> {playerCountry.monthly_income.toFixed(1)} gold</p>
                <p><strong>Monthly Expenses:</strong> {playerCountry.monthly_expenses.toFixed(1)} gold</p>
                <p><strong>Net Income:</strong> {(playerCountry.monthly_income - playerCountry.monthly_expenses).toFixed(1)} gold</p>
                <p><strong>Inflation:</strong> {(playerCountry.inflation * 100).toFixed(1)}%</p>
              </div>

              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>
                <h3>⚔️ Military</h3>
                <p><strong>Army Size:</strong> {playerCountry.army_size.toLocaleString()} troops</p>
                <p><strong>Navy Size:</strong> {playerCountry.navy_size} ships</p>
                <p><strong>Military Tradition:</strong> {playerCountry.military_tradition.toFixed(1)}</p>
                <p><strong>War Exhaustion:</strong> {playerCountry.war_exhaustion.toFixed(1)}</p>
              </div>
            </div>

            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>🏛️ Provinces ({playerCountry.provinces.length})</h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
                {provinces.filter(p => p.owner === playerCountry.name).map(province => (
                  <div
                    key={province.name}
                    style={{
                      background: '#1a1a2e',
                      padding: '10px',
                      borderRadius: '5px',
                      cursor: 'pointer',
                      border: selectedProvince?.name === province.name ? '2px solid #4a9eff' : '1px solid #444'
                    }}
                    onClick={() => setSelectedProvince(province)}
                  >
                    <strong>{province.name}</strong>
                    <br />
                    <small>Dev: {province.development.toFixed(1)} | Pop: {province.population_groups.reduce((sum, pop) => sum + pop.size, 0).toLocaleString()}</small>
                  </div>
                ))}
              </div>
            </div>

            {/* Recent Events */}
            {event && (
              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>
                <h3>📰 Current Event</h3>
                <div style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>
                  <h4>{event.title}</h4>
                  <p>{event.description}</p>
                  <p style={{ color: '#4a9eff' }}><strong>Effect:</strong> {event.effect}</p>
                  <button
                    onClick={() => {
                      // Apply event logic here
                      addNotification(`Applied: ${event.effect}`, "success");
                      setEvent(null);
                    }}
                    style={{
                      background: '#4a9eff',
                      color: '#fff',
                      border: 'none',
                      padding: '8px 15px',
                      borderRadius: '5px',
                      cursor: 'pointer'
                    }}
                  >
                    Accept
                  </button>
                </div>
              </div>
            )}
          </div>
        );

      case 'population':
        return (
          <div>
            <h2>👥 Population Management</h2>

            {/* Population Overview */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>📊 Population Summary</h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '15px' }}>
                {provinces.filter(p => p.owner === playerCountry.name).map(province => {
                  const totalPop = province.population_groups.reduce((sum, pop) => sum + pop.size, 0);
                  const avgHappiness = province.population_groups.reduce((sum, pop) => sum + pop.happiness, 0) / province.population_groups.length;
                  const avgMilitancy = province.population_groups.reduce((sum, pop) => sum + pop.militancy, 0) / province.population_groups.length;

                  return (
                    <div
                      key={province.name}
                      style={{
                        background: '#1a1a2e',
                        padding: '12px',
                        borderRadius: '5px',
                        cursor: 'pointer',
                        border: selectedProvince?.name === province.name ? '2px solid #4a9eff' : '1px solid #444'
                      }}
                      onClick={() => setSelectedProvince(province)}
                    >
                      <h4 style={{ margin: '0 0 8px 0' }}>{province.name}</h4>
                      <p style={{ margin: '2px 0', fontSize: '0.9rem' }}>
                        <strong>Population:</strong> {totalPop.toLocaleString()}
                      </p>
                      <p style={{ margin: '2px 0', fontSize: '0.9rem' }}>
                        <strong>Happiness:</strong> {avgHappiness.toFixed(1)}/10
                      </p>
                      <p style={{ margin: '2px 0', fontSize: '0.9rem' }}>
                        <strong>Unrest:</strong> {province.unrest.toFixed(1)}
                      </p>
                      <div style={{
                        background: avgHappiness > 6 ? '#51cf66' : avgHappiness > 4 ? '#ffd43b' : '#ff6b6b',
                        height: '4px',
                        borderRadius: '2px',
                        marginTop: '8px'
                      }} />
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Detailed Province Population */}
            {selectedProvince && (
              <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
                <h3>🏘️ {selectedProvince.name} - Population Details</h3>

                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
                  {selectedProvince.population_groups.map((popGroup, index) => (
                    <div
                      key={index}
                      style={{
                        background: '#1a1a2e',
                        padding: '15px',
                        borderRadius: '5px',
                        border: '1px solid #444'
                      }}
                    >
                      <h4 style={{ margin: '0 0 10px 0', textTransform: 'capitalize' }}>
                        {popGroup.social_class.replace('_', ' ')}
                      </h4>

                      <div style={{ fontSize: '0.9rem' }}>
                        <p><strong>Size:</strong> {popGroup.size.toLocaleString()}</p>
                        <p><strong>Culture:</strong> {popGroup.culture}</p>
                        <p><strong>Religion:</strong> {popGroup.religion}</p>
                        <p><strong>Profession:</strong> {popGroup.profession}</p>
                        <p><strong>Wealth:</strong> {popGroup.wealth.toFixed(1)}</p>

                        <div style={{ margin: '10px 0' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                            <span>Happiness:</span>
                            <span style={{ color: popGroup.happiness > 6 ? '#51cf66' : popGroup.happiness > 4 ? '#ffd43b' : '#ff6b6b' }}>
                              {popGroup.happiness.toFixed(1)}/10
                            </span>
                          </div>
                          <div style={{
                            background: '#333',
                            height: '6px',
                            borderRadius: '3px',
                            overflow: 'hidden'
                          }}>
                            <div style={{
                              background: popGroup.happiness > 6 ? '#51cf66' : popGroup.happiness > 4 ? '#ffd43b' : '#ff6b6b',
                              height: '100%',
                              width: `${(popGroup.happiness / 10) * 100}%`,
                              transition: 'width 0.3s ease'
                            }} />
                          </div>
                        </div>

                        <div style={{ margin: '10px 0' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                            <span>Militancy:</span>
                            <span style={{ color: popGroup.militancy > 6 ? '#ff6b6b' : popGroup.militancy > 3 ? '#ffd43b' : '#51cf66' }}>
                              {popGroup.militancy.toFixed(1)}/10
                            </span>
                          </div>
                          <div style={{
                            background: '#333',
                            height: '6px',
                            borderRadius: '3px',
                            overflow: 'hidden'
                          }}>
                            <div style={{
                              background: popGroup.militancy > 6 ? '#ff6b6b' : popGroup.militancy > 3 ? '#ffd43b' : '#51cf66',
                              height: '100%',
                              width: `${(popGroup.militancy / 10) * 100}%`,
                              transition: 'width 0.3s ease'
                            }} />
                          </div>
                        </div>

                        <div style={{ margin: '10px 0' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                            <span>Consciousness:</span>
                            <span style={{ color: '#4a9eff' }}>
                              {popGroup.consciousness.toFixed(1)}/10
                            </span>
                          </div>
                          <div style={{
                            background: '#333',
                            height: '6px',
                            borderRadius: '3px',
                            overflow: 'hidden'
                          }}>
                            <div style={{
                              background: '#4a9eff',
                              height: '100%',
                              width: `${(popGroup.consciousness / 10) * 100}%`,
                              transition: 'width 0.3s ease'
                            }} />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Population Actions */}
                <div style={{ marginTop: '20px' }}>
                  <h4>👑 Population Policies</h4>
                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
                    <button
                      onClick={() => {
                        if (!actionTaken) {
                          addNotification("Implemented education reforms", "success");
                          setActionTaken(true);
                        }
                      }}
                      disabled={actionTaken}
                      style={{
                        background: actionTaken ? '#666' : '#4a9eff',
                        color: '#fff',
                        border: 'none',
                        padding: '12px',
                        borderRadius: '5px',
                        cursor: actionTaken ? 'not-allowed' : 'pointer'
                      }}
                    >
                      📚 Education Reform<br />
                      <small>Increase consciousness</small>
                    </button>

                    <button
                      onClick={() => {
                        if (!actionTaken) {
                          addNotification("Promoted cultural integration", "success");
                          setActionTaken(true);
                        }
                      }}
                      disabled={actionTaken}
                      style={{
                        background: actionTaken ? '#666' : '#51cf66',
                        color: '#fff',
                        border: 'none',
                        padding: '12px',
                        borderRadius: '5px',
                        cursor: actionTaken ? 'not-allowed' : 'pointer'
                      }}
                    >
                      🤝 Cultural Integration<br />
                      <small>Reduce cultural tensions</small>
                    </button>

                    <button
                      onClick={() => {
                        if (!actionTaken) {
                          addNotification("Implemented welfare programs", "success");
                          setActionTaken(true);
                        }
                      }}
                      disabled={actionTaken}
                      style={{
                        background: actionTaken ? '#666' : '#ffd43b',
                        color: '#000',
                        border: 'none',
                        padding: '12px',
                        borderRadius: '5px',
                        cursor: actionTaken ? 'not-allowed' : 'pointer'
                      }}
                    >
                      🏥 Welfare Programs<br />
                      <small>Increase happiness</small>
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* National Demographics */}
            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>
              <h3>🌍 National Demographics</h3>

              {(() => {
                const allPops = provinces
                  .filter(p => p.owner === playerCountry.name)
                  .flatMap(p => p.population_groups);

                const totalPop = allPops.reduce((sum, pop) => sum + pop.size, 0);
                const classCounts = {};
                const cultureCounts = {};
                const religionCounts = {};

                allPops.forEach(pop => {
                  classCounts[pop.social_class] = (classCounts[pop.social_class] || 0) + pop.size;
                  cultureCounts[pop.culture] = (cultureCounts[pop.culture] || 0) + pop.size;
                  religionCounts[pop.religion] = (religionCounts[pop.religion] || 0) + pop.size;
                });

                return (
                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
                    <div>
                      <h4>👑 Social Classes</h4>
                      {Object.entries(classCounts).map(([className, count]) => (
                        <div key={className} style={{ margin: '8px 0' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <span style={{ textTransform: 'capitalize' }}>{className.replace('_', ' ')}</span>
                            <span>{((count / totalPop) * 100).toFixed(1)}%</span>
                          </div>
                          <div style={{ background: '#333', height: '6px', borderRadius: '3px', overflow: 'hidden' }}>
                            <div style={{
                              background: '#4a9eff',
                              height: '100%',
                              width: `${(count / totalPop) * 100}%`
                            }} />
                          </div>
                        </div>
                      ))}
                    </div>

                    <div>
                      <h4>🏛️ Cultures</h4>
                      {Object.entries(cultureCounts).map(([culture, count]) => (
                        <div key={culture} style={{ margin: '8px 0' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <span style={{ textTransform: 'capitalize' }}>{culture}</span>
                            <span>{((count / totalPop) * 100).toFixed(1)}%</span>
                          </div>
                          <div style={{ background: '#333', height: '6px', borderRadius: '3px', overflow: 'hidden' }}>
                            <div style={{
                              background: '#51cf66',
                              height: '100%',
                              width: `${(count / totalPop) * 100}%`
                            }} />
                          </div>
                        </div>
                      ))}
                    </div>

                    <div>
                      <h4>⛪ Religions</h4>
                      {Object.entries(religionCounts).map(([religion, count]) => (
                        <div key={religion} style={{ margin: '8px 0' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <span style={{ textTransform: 'capitalize' }}>{religion}</span>
                            <span>{((count / totalPop) * 100).toFixed(1)}%</span>
                          </div>
                          <div style={{ background: '#333', height: '6px', borderRadius: '3px', overflow: 'hidden' }}>
                            <div style={{
                              background: '#ffd43b',
                              height: '100%',
                              width: `${(count / totalPop) * 100}%`
                            }} />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        );

      case 'economy':
        return (
          <div>
            <h2>💰 Economic Management</h2>

            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>📊 Financial Summary</h3>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
                <div>
                  <p><strong>Treasury:</strong> {playerCountry.treasury.toLocaleString()} gold</p>
                  <p><strong>Monthly Income:</strong> +{playerCountry.monthly_income.toFixed(1)} gold</p>
                  <p><strong>Monthly Expenses:</strong> -{playerCountry.monthly_expenses.toFixed(1)} gold</p>
                  <p><strong>Net Balance:</strong> {(playerCountry.monthly_income - playerCountry.monthly_expenses).toFixed(1)} gold</p>
                </div>
                <div>
                  <p><strong>Trade Efficiency:</strong> {(playerCountry.trade_efficiency * 100).toFixed(1)}%</p>
                  <p><strong>Inflation Rate:</strong> {(playerCountry.inflation * 100).toFixed(1)}%</p>
                </div>
              </div>
            </div>

            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>🌍 Global Resource Prices</h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '10px' }}>
                {Object.entries(globalResources).map(([name, resource]) => (
                  <div key={name} style={{ background: '#1a1a2e', padding: '10px', borderRadius: '5px', textAlign: 'center' }}>
                    <strong>{name.replace('_', ' ')}</strong>
                    <br />
                    <span style={{ color: '#4a9eff' }}>{resource.current_price.toFixed(2)} gold</span>
                    <br />
                    <small style={{ opacity: 0.7 }}>
                      {resource.current_price > resource.base_value ? '📈' : '📉'}
                    </small>
                  </div>
                ))}
              </div>
            </div>

            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px' }}>
              <h3>🏭 Economic Actions</h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                <button
                  onClick={() => {
                    if (!actionTaken) {
                      addNotification("Invested in infrastructure development", "success");
                      setActionTaken(true);
                    }
                  }}
                  disabled={actionTaken}
                  style={{
                    background: actionTaken ? '#666' : '#4a9eff',
                    color: '#fff',
                    border: 'none',
                    padding: '15px',
                    borderRadius: '5px',
                    cursor: actionTaken ? 'not-allowed' : 'pointer'
                  }}
                >
                  🏗️ Build Infrastructure<br />
                  <small>Cost: 200 gold</small>
                </button>

                <button
                  onClick={() => {
                    if (!actionTaken) {
                      addNotification("Promoted trade and commerce", "success");
                      setActionTaken(true);
                    }
                  }}
                  disabled={actionTaken}
                  style={{
                    background: actionTaken ? '#666' : '#51cf66',
                    color: '#fff',
                    border: 'none',
                    padding: '15px',
                    borderRadius: '5px',
                    cursor: actionTaken ? 'not-allowed' : 'pointer'
                  }}
                >
                  🏪 Promote Trade<br />
                  <small>Cost: 150 gold</small>
                </button>

                <button
                  onClick={() => {
                    if (!actionTaken) {
                      addNotification("Implemented tax reforms", "success");
                      setActionTaken(true);
                    }
                  }}
                  disabled={actionTaken}
                  style={{
                    background: actionTaken ? '#666' : '#ffd43b',
                    color: '#000',
                    border: 'none',
                    padding: '15px',
                    borderRadius: '5px',
                    cursor: actionTaken ? 'not-allowed' : 'pointer'
                  }}
                >
                  📋 Tax Reform<br />
                  <small>Cost: 100 gold</small>
                </button>
              </div>
            </div>
          </div>
        );

      case 'technology':
        return (
          <div>
            <h2>🔬 Technology & Research</h2>

            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>📚 Research Progress</h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                {Object.entries(playerCountry.research_points).map(([category, points]) => (
                  <div key={category} style={{ background: '#1a1a2e', padding: '15px', borderRadius: '5px' }}>
                    <h4 style={{ textTransform: 'capitalize', margin: '0 0 10px 0' }}>
                      {category === 'military' && '⚔️'}
                      {category === 'economic' && '💰'}
                      {category === 'social' && '👥'}
                      {category === 'administrative' && '🏛️'}
                      {' ' + category}
                    </h4>
                    <p><strong>Points:</strong> {points.toFixed(1)}</p>
                    {playerCountry.current_research[category] && (
                      <p><strong>Researching:</strong> {playerCountry.current_research[category]}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>

            <div style={{ background: '#2d2d44', padding: '15px', borderRadius: '8px', marginBottom: '20px' }}>
              <h3>🧪 Available Technologies</h3>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '15px' }}>
                {Object.entries(technologies).map(([name, tech]) => {
                  const isResearched = playerCountry.technologies.includes(name);
                  const canResearch = tech.prerequisites.every(prereq => playerCountry.technologies.includes(prereq));
                  const isCurrentlyResearching = Object.values(playerCountry.current_research).includes(name);

                  return (
                    <div
                      key={name}
                      style={{
                        background: isResearched ? '#2d5a2d' : '#1a1a2e',
                        padding: '15px',
                        borderRadius: '5px',
                        border: isCurrentlyResearching ? '2px solid #4a9eff' : '1px solid #444',
                        opacity: canResearch || isResearched ? 1 : 0.5
                      }}
                    >
                      <h4 style={{ margin: '0 0 10px 0' }}>{name}</h4>
                      <p style={{ fontSize: '0.9rem', margin: '5px 0' }}>{tech.description}</p>
                      <p><strong>Category:</strong> {tech.category}</p>
                      <p><strong>Cost:</strong> {tech.cost} points</p>

                      {tech.prerequisites.length > 0 && (
                        <p><strong>Prerequisites:</strong> {tech.prerequisites.join(', ')}</p>
                      )}

                      {isResearched && (
                        <span style={{ color: '#51cf66', fontWeight: 'bold' }}>✅ Researched</span>
                      )}

                      {!isResearched && canResearch && !isCurrentlyResearching && (
                        <button
                          onClick={() => startResearch(tech.category, name)}
                          style={{
                            background: '#4a9eff',
                            color: '#fff',
                            border: 'none',
                            padding: '8px 15px',
                            borderRadius: '5px',
                            cursor: 'pointer',
                            marginTop: '10px'
                          }}
                        >
                          Start Research
                        </button>
                      )}

                      {isCurrentlyResearching && (
                        <span style={{ color: '#ffd43b', fontWeight: 'bold' }}>🔬 Researching...</span>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div>
            <h2>🚧 Coming Soon</h2>
            <p>This feature is under development and will be available in future updates.</p>
          </div>
        );
    }
  };

  // Game phase rendering
  if (gamePhase === "loading") {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh', background: '#1a1a2e' }}>
        <div style={{ textAlign: 'center', color: '#fff' }}>
          <h1>🏰 Empires & Revolutions</h1>
          <p>Loading the world of Aeterra...</p>
          <div style={{ margin: '20px 0' }}>⚔️ 🏛️ 💰 🔬 🌍</div>
        </div>
      </div>
    );
  }

  if (gamePhase === "country_selection") {
    return (
      <div style={{ background: '#1a1a2e', minHeight: '100vh', color: '#fff' }}>
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <h1>🏰 Choose Your Destiny</h1>
          <p>Select a nation to lead through the Age of Revolutions</p>
        </div>

        <WorldMap
          onSelectCountry={selectCountry}
          selectedCountry={null}
          countries={countries}
        />

        <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
          <h2>Available Nations</h2>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
            {countries.slice(0, 5).map(country => (
              <div
                key={country.name}
                style={{
                  background: '#2d2d44',
                  padding: '20px',
                  borderRadius: '10px',
                  cursor: 'pointer',
                  border: '2px solid transparent',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => e.target.style.borderColor = '#4a9eff'}
                onMouseLeave={(e) => e.target.style.borderColor = 'transparent'}
                onClick={() => selectCountry(country)}
              >
                <h3>{country.name}</h3>
                <p><strong>Ruler:</strong> {country.ruler_name}</p>
                <p><strong>Government:</strong> {country.government_type.replace('_', ' ')}</p>
                <p><strong>Treasury:</strong> {country.treasury.toLocaleString()} gold</p>
                <p><strong>Provinces:</strong> {country.provinces.length}</p>
                <p><strong>Prestige:</strong> {country.prestige}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (gamePhase === "victory") {
    return (
      <div style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        minHeight: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        color: '#fff',
        textAlign: 'center'
      }}>
        <div>
          <h1 style={{ fontSize: '4rem', marginBottom: '20px' }}>🎉 VICTORY! 🎉</h1>
          <h2>The {playerCountry.name} Triumphant!</h2>
          <p style={{ fontSize: '1.5rem', margin: '20px 0' }}>
            Under the wise rule of {playerCountry.ruler_name}, your nation has achieved greatness!
          </p>
          <div style={{ background: 'rgba(0,0,0,0.3)', padding: '20px', borderRadius: '10px', margin: '20px 0' }}>
            <p><strong>Final Treasury:</strong> {playerCountry.treasury.toLocaleString()} gold</p>
            <p><strong>Final Prestige:</strong> {playerCountry.prestige}</p>
            <p><strong>Provinces Controlled:</strong> {playerCountry.provinces.length}</p>
            <p><strong>Turns Survived:</strong> {turn}</p>
          </div>
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '15px 30px',
              fontSize: '1.2rem',
              background: '#4a9eff',
              color: '#fff',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Play Again
          </button>
        </div>
      </div>
    );
  }

  if (gamePhase === "defeat") {
    return (
      <div style={{
        background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
        minHeight: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        color: '#fff',
        textAlign: 'center'
      }}>
        <div>
          <h1 style={{ fontSize: '4rem', marginBottom: '20px' }}>💀 DEFEAT 💀</h1>
          <h2>The Fall of {playerCountry.name}</h2>
          <p style={{ fontSize: '1.5rem', margin: '20px 0' }}>
            The reign of {playerCountry.ruler_name} has come to an end...
          </p>
          <div style={{ background: 'rgba(0,0,0,0.3)', padding: '20px', borderRadius: '10px', margin: '20px 0' }}>
            <p><strong>Final Treasury:</strong> {playerCountry.treasury.toLocaleString()} gold</p>
            <p><strong>Final Stability:</strong> {playerCountry.stability}</p>
            <p><strong>Final Legitimacy:</strong> {playerCountry.legitimacy}</p>
            <p><strong>Turns Survived:</strong> {turn}</p>
          </div>
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '15px 30px',
              fontSize: '1.2rem',
              background: '#4a9eff',
              color: '#fff',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Main game interface - sophisticated tabbed layout
  return (
    <div style={{ background: '#1a1a2e', minHeight: '100vh', color: '#fff' }}>
      {/* Header */}
      <div style={{
        background: '#2d2d44',
        padding: '15px 20px',
        borderBottom: '2px solid #4a9eff',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          <h1 style={{ margin: 0, fontSize: '1.8rem' }}>🏰 {playerCountry.name}</h1>
          <p style={{ margin: 0, opacity: 0.8 }}>{playerCountry.ruler_name} • {playerCountry.government_type.replace('_', ' ')}</p>
        </div>

        <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>💰 {playerCountry.treasury.toLocaleString()}</div>
            <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>Treasury</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>⭐ {playerCountry.prestige}</div>
            <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>Prestige</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>🏛️ {playerCountry.stability}</div>
            <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>Stability</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>👑 {playerCountry.legitimacy}</div>
            <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>Legitimacy</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>📅 {turn}</div>
            <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>Turn</div>
          </div>
        </div>
      </div>

      {/* Notifications */}
      {notifications.length > 0 && (
        <div style={{ padding: '10px 20px' }}>
          {notifications.map(notification => (
            <div
              key={notification.id}
              style={{
                background: notification.type === 'error' ? '#ff6b6b' :
                           notification.type === 'success' ? '#51cf66' : '#4a9eff',
                padding: '8px 15px',
                margin: '5px 0',
                borderRadius: '5px',
                fontSize: '0.9rem',
                display: 'flex',
                justifyContent: 'space-between'
              }}
            >
              <span>{notification.text}</span>
              <span style={{ opacity: 0.7 }}>{notification.timestamp}</span>
            </div>
          ))}
        </div>
      )}

      {/* Tab Navigation */}
      <div style={{
        background: '#2d2d44',
        padding: '0 20px',
        borderBottom: '1px solid #444'
      }}>
        {['overview', 'population', 'economy', 'military', 'diplomacy', 'technology'].map(tab => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            style={{
              background: activeTab === tab ? '#4a9eff' : 'transparent',
              color: '#fff',
              border: 'none',
              padding: '15px 25px',
              margin: '0 5px',
              cursor: 'pointer',
              borderRadius: '5px 5px 0 0',
              textTransform: 'capitalize',
              fontSize: '1rem'
            }}
          >
            {tab === 'overview' && '🏛️'}
            {tab === 'population' && '👥'}
            {tab === 'economy' && '💰'}
            {tab === 'military' && '⚔️'}
            {tab === 'diplomacy' && '🤝'}
            {tab === 'technology' && '🔬'}
            {' ' + tab}
          </button>
        ))}
      </div>

      {/* Main Content Area */}
      <div style={{ display: 'flex', height: 'calc(100vh - 200px)' }}>
        {/* Left Panel - Map */}
        <div style={{ flex: '1', background: '#16213e', padding: '20px' }}>
          <WorldMap
            onSelectCountry={() => {}}
            selectedCountry={playerCountry}
            countries={countries}
            onSelectProvince={setSelectedProvince}
            selectedProvince={selectedProvince}
          />

          {selectedProvince && (
            <div style={{
              background: '#2d2d44',
              padding: '15px',
              margin: '10px 0',
              borderRadius: '8px',
              maxHeight: '200px',
              overflowY: 'auto'
            }}>
              <h3>{selectedProvince.name}</h3>
              <p><strong>Owner:</strong> {selectedProvince.owner}</p>
              <p><strong>Development:</strong> {selectedProvince.development.toFixed(1)}</p>
              <p><strong>Population:</strong> {selectedProvince.population_groups.reduce((sum, pop) => sum + pop.size, 0).toLocaleString()}</p>
              <p><strong>Terrain:</strong> {selectedProvince.terrain}</p>
              <p><strong>Unrest:</strong> {selectedProvince.unrest.toFixed(1)}</p>
            </div>
          )}
        </div>

        {/* Right Panel - Tab Content */}
        <div style={{ flex: '1', background: '#1a1a2e', padding: '20px', overflowY: 'auto' }}>
          {renderTabContent()}
        </div>
      </div>

      {/* Bottom Action Bar */}
      <div style={{
        background: '#2d2d44',
        padding: '15px 20px',
        borderTop: '2px solid #4a9eff',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          {message && (
            <span style={{ color: '#ffd43b', fontSize: '1rem' }}>{message}</span>
          )}
        </div>

        <button
          onClick={advanceTurn}
          disabled={actionTaken}
          style={{
            background: actionTaken ? '#666' : '#4a9eff',
            color: '#fff',
            border: 'none',
            padding: '12px 30px',
            fontSize: '1.1rem',
            borderRadius: '5px',
            cursor: actionTaken ? 'not-allowed' : 'pointer',
            fontWeight: 'bold'
          }}
        >
          {actionTaken ? '⏳ Processing...' : '▶️ End Turn'}
        </button>
      </div>
    </div>
  );
}
