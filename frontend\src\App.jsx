import React, { useEffect, useState } from "react";
import axios from "axios";
import WorldMap from './WorldMap';

const INITIAL_STATS = { population: 1000000, resources: 100, stability: 100 };
const WIN_RESOURCES = 1000;
const LOSE_STABILITY = 0;

export default function App() {
  const [countries, setCountries] = useState([]);
  const [playerCountry, setPlayerCountry] = useState(null);
  const [countryStats, setCountryStats] = useState({});
  const [turn, setTurn] = useState(1);
  const [message, setMessage] = useState("");
  const [actionTaken, setActionTaken] = useState(false);
  const [event, setEvent] = useState(null);
  const [diplomacy, setDiplomacy] = useState([]);

  // Load countries on mount
  useEffect(() => {
    axios.get("http://localhost:8000/countries")
      .then(r => r.data)
      .then(data => {
        console.log("Fetched countries:", data); // DEBUG LOG
        setCountries(data);
        // Initialize stats for all countries with a valid NAME
        const stats = {};
        data.forEach(c => {
          const name = c.name || c.properties?.NAME;
          if (name) {
            stats[name] = { ...INITIAL_STATS };
          }
        });
        setCountryStats(stats);
      });
  }, []);

  // Fetch event and diplomacy at start and on turn change
  useEffect(() => {
    const countryName = playerCountry?.name || playerCountry?.properties?.NAME;
    if (!countryName) return;
    axios.get(`http://localhost:8000/event?country=${encodeURIComponent(countryName)}`)
      .then(r => setEvent(r.data));
    axios.get(`http://localhost:8000/diplomacy?country=${encodeURIComponent(countryName)}`)
      .then(r => setDiplomacy(r.data));
  }, [turn, playerCountry]);

  // Player picks a country at start
  if (!countries.length || !Object.keys(countryStats).length) {
    return <div>Loading world data...</div>;
  }
  const playerCountryName = playerCountry?.name || playerCountry?.properties?.NAME;
  if (!playerCountryName) {
    return (
      <div>
        <h1>Choose Your Country</h1>
        <WorldMap onSelectCountry={c => {
          const name = c?.name || c?.properties?.NAME;
          if (name) setPlayerCountry(c);
        }} selectedCountry={null} />
        <p>Click a country to play as it.</p>
      </div>
    );
  }

  const stats = countryStats[playerCountryName];
  if (!stats) {
    return <div>Loading country stats...</div>;
  }

  // Player actions
  const invest = () => {
    if (actionTaken) return;
    setCountryStats(stats => ({
      ...stats,
      [playerCountryName]: {
        ...stats[playerCountryName],
        resources: stats[playerCountryName].resources + 50,
        stability: stats[playerCountryName].stability - 2
      }
    }));
    setMessage("You invested in your economy! Resources +50, Stability -2");
    setActionTaken(true);
  };
  const build = () => {
    if (actionTaken) return;
    setCountryStats(stats => ({
      ...stats,
      [playerCountryName]: {
        ...stats[playerCountryName],
        population: Math.floor(stats[playerCountryName].population * 1.02),
        resources: stats[playerCountryName].resources - 20,
        stability: stats[playerCountryName].stability + 1
      }
    }));
    setMessage("You built infrastructure! Population +2%, Resources -20, Stability +1");
    setActionTaken(true);
  };
  const propaganda = () => {
    if (actionTaken) return;
    setCountryStats(stats => ({
      ...stats,
      [playerCountryName]: {
        ...stats[playerCountryName],
        stability: stats[playerCountryName].stability + 10,
        resources: stats[playerCountryName].resources - 10
      }
    }));
    setMessage("You ran propaganda! Stability +10, Resources -10");
    setActionTaken(true);
  };

  // Apply event effect to player country
  const applyEvent = () => {
    if (!event) return;
    axios.post("http://localhost:8000/event/apply", {
      country: playerCountryName,
      effect: event.effect
    }).then(res => {
      if (res.data.success && res.data.country) {
        setCountryStats(stats => ({
          ...stats,
          [playerCountryName]: {
            ...stats[playerCountryName],
            ...res.data.country
          }
        }));
        setMessage(`Event applied: ${event.effect}`);
      } else {
        setMessage("Failed to apply event.");
      }
    });
  };

  // Perform diplomacy action
  const performDiplomacy = (action, target) => {
    axios.post("http://localhost:8000/diplomacy/perform", {
      country: playerCountryName,
      action,
      target
    }).then(res => {
      setMessage(res.data.result || "Diplomacy action performed.");
    });
  };

  // End turn: AI and player stats update
  const nextTurn = () => {
    setTurn(t => t + 1);
    setActionTaken(false);
    setMessage("");
    setCountryStats(stats => {
      const newStats = { ...stats };
      Object.keys(newStats).forEach(name => {
        // AI: grow pop/resources, random stability change
        if (name !== playerCountryName) {
          newStats[name] = {
            ...newStats[name],
            population: Math.floor(newStats[name].population * (1.01 + Math.random() * 0.01)),
            resources: newStats[name].resources + Math.floor(Math.random() * 20),
            stability: Math.max(0, Math.min(100, newStats[name].stability + Math.floor(Math.random() * 5 - 2)))
          };
        } else {
          // Player: small passive growth
          newStats[name] = {
            ...newStats[name],
            population: Math.floor(newStats[name].population * 1.01),
            resources: newStats[name].resources + 5,
            stability: Math.max(0, Math.min(100, newStats[name].stability))
          };
        }
      });
      return newStats;
    });
  };

  // Win/Lose conditions
  if (stats.resources >= WIN_RESOURCES) {
    return <h1>Victory! You built a prosperous nation.</h1>;
  }
  if (stats.stability <= LOSE_STABILITY) {
    return <h1>Your country collapsed due to instability. Game Over.</h1>;
  }

  return (
    <div>
      <h1>Empires & Revolutions</h1>
      <div>Turn: {turn}</div>
      <WorldMap onSelectCountry={() => {}} selectedCountry={playerCountry} />
      {/* Event UI */}
      {event && (
        <div style={{ background: '#333', color: '#fff', padding: 10, margin: 10, borderRadius: 8 }}>
          <h3>Event: {event.title}</h3>
          <p>{event.description}</p>
          <p style={{ color: 'lightgreen' }}>{event.effect}</p>
          <button onClick={applyEvent}>Apply Event Effect</button>
        </div>
      )}
      {/* Country stats and actions */}
      <div style={{ background: '#222', color: '#fff', padding: 10, margin: 10, borderRadius: 8 }}>
        <h2>{playerCountryName}</h2>
        <p>Population: {stats.population.toLocaleString()}</p>
        <p>Resources: {stats.resources}</p>
        <p>Stability: {stats.stability}</p>
        <button onClick={invest} disabled={actionTaken}>Invest</button>
        <button onClick={build} disabled={actionTaken} style={{marginLeft: 8}}>Build</button>
        <button onClick={propaganda} disabled={actionTaken} style={{marginLeft: 8}}>Propaganda</button>
      </div>
      {/* Diplomacy UI */}
      <div style={{ background: '#223', color: '#fff', padding: 10, margin: 10, borderRadius: 8 }}>
        <h3>Diplomacy</h3>
        {diplomacy.length === 0 && <p>No diplomatic actions available.</p>}
        <ul>
          {diplomacy.map((d, i) => (
            <li key={i}>
              {d.action} {d.target && <b>with {d.target}</b>}
              <button style={{marginLeft: 8}} onClick={() => performDiplomacy(d.action, d.target)}>Do</button>
            </li>
          ))}
        </ul>
      </div>
      <button onClick={nextTurn}>End Turn</button>
      {message && <div style={{ color: 'yellow', margin: 10 }}>{message}</div>}
    </div>
  );
}
