{"ast": null, "code": "import { Filter, Matrix, Point } from \"@pixi/core\";\nimport fragment from \"./displacement.frag.mjs\";\nimport vertex from \"./displacement.vert.mjs\";\nclass DisplacementFilter extends Filter {\n  /**\n   * @param {PIXI.Sprite} sprite - The sprite used for the displacement map. (make sure its added to the scene!)\n   * @param scale - The scale of the displacement\n   */\n  constructor(sprite, scale) {\n    const maskMatrix = new Matrix();\n    sprite.renderable = !1, super(vertex, fragment, {\n      mapSampler: sprite._texture,\n      filterMatrix: maskMatrix,\n      scale: {\n        x: 1,\n        y: 1\n      },\n      rotation: new Float32Array([1, 0, 0, 1])\n    }), this.maskSprite = sprite, this.maskMatrix = maskMatrix, scale == null && (scale = 20), this.scale = new Point(scale, scale);\n  }\n  /**\n   * Applies the filter.\n   * @param filterManager - The manager.\n   * @param input - The input target.\n   * @param output - The output target.\n   * @param clearMode - clearMode.\n   */\n  apply(filterManager, input, output, clearMode) {\n    this.uniforms.filterMatrix = filterManager.calculateSpriteMatrix(this.maskMatrix, this.maskSprite), this.uniforms.scale.x = this.scale.x, this.uniforms.scale.y = this.scale.y;\n    const wt = this.maskSprite.worldTransform,\n      lenX = Math.sqrt(wt.a * wt.a + wt.b * wt.b),\n      lenY = Math.sqrt(wt.c * wt.c + wt.d * wt.d);\n    lenX !== 0 && lenY !== 0 && (this.uniforms.rotation[0] = wt.a / lenX, this.uniforms.rotation[1] = wt.b / lenX, this.uniforms.rotation[2] = wt.c / lenY, this.uniforms.rotation[3] = wt.d / lenY), filterManager.applyFilter(this, input, output, clearMode);\n  }\n  /** The texture used for the displacement map. Must be power of 2 sized texture. */\n  get map() {\n    return this.uniforms.mapSampler;\n  }\n  set map(value) {\n    this.uniforms.mapSampler = value;\n  }\n}\nexport { DisplacementFilter };", "map": {"version": 3, "names": ["DisplacementFilter", "Filter", "constructor", "sprite", "scale", "maskMatrix", "Matrix", "renderable", "vertex", "fragment", "mapSampler", "_texture", "filterMatrix", "x", "y", "rotation", "Float32Array", "maskSprite", "Point", "apply", "filterManager", "input", "output", "clearMode", "uniforms", "calculateSpriteMatrix", "wt", "worldTransform", "lenX", "Math", "sqrt", "a", "b", "lenY", "c", "d", "applyFilter", "map", "value"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\filter-displacement\\src\\DisplacementFilter.ts"], "sourcesContent": ["import { Filter, Matrix, Point } from '@pixi/core';\nimport fragment from './displacement.frag';\nimport vertex from './displacement.vert';\n\nimport type { CLEAR_MODES, FilterSystem, ISpriteMaskTarget, RenderTexture, Texture } from '@pixi/core';\n\n/**\n * The DisplacementFilter class uses the pixel values from the specified texture\n * (called the displacement map) to perform a displacement of an object.\n *\n * You can use this filter to apply all manor of crazy warping effects.\n * Currently the `r` property of the texture is used to offset the `x`\n * and the `g` property of the texture is used to offset the `y`.\n *\n * The way it works is it uses the values of the displacement map to look up the\n * correct pixels to output. This means it's not technically moving the original.\n * Instead, it's starting at the output and asking \"which pixel from the original goes here\".\n * For example, if a displacement map pixel has `red = 1` and the filter scale is `20`,\n * this filter will output the pixel approximately 20 pixels to the right of the original.\n * @memberof PIXI\n */\nexport class DisplacementFilter extends Filter\n{\n    public maskSprite: ISpriteMaskTarget;\n    public maskMatrix: Matrix;\n    public scale: Point;\n\n    /**\n     * @param {PIXI.Sprite} sprite - The sprite used for the displacement map. (make sure its added to the scene!)\n     * @param scale - The scale of the displacement\n     */\n    constructor(sprite: ISpriteMaskTarget, scale?: number)\n    {\n        const maskMatrix = new Matrix();\n\n        sprite.renderable = false;\n\n        super(vertex, fragment, {\n            mapSampler: sprite._texture,\n            filterMatrix: maskMatrix,\n            scale: { x: 1, y: 1 },\n            rotation: new Float32Array([1, 0, 0, 1]),\n        });\n\n        this.maskSprite = sprite;\n        this.maskMatrix = maskMatrix;\n\n        if (scale === null || scale === undefined)\n        {\n            scale = 20;\n        }\n\n        /**\n         * scaleX, scaleY for displacements\n         * @member {PIXI.Point}\n         */\n        this.scale = new Point(scale, scale);\n    }\n\n    /**\n     * Applies the filter.\n     * @param filterManager - The manager.\n     * @param input - The input target.\n     * @param output - The output target.\n     * @param clearMode - clearMode.\n     */\n    public apply(\n        filterManager: FilterSystem, input: RenderTexture, output: RenderTexture, clearMode: CLEAR_MODES\n    ): void\n    {\n        // fill maskMatrix with _normalized sprite texture coords_\n        this.uniforms.filterMatrix = filterManager.calculateSpriteMatrix(this.maskMatrix, this.maskSprite);\n        this.uniforms.scale.x = this.scale.x;\n        this.uniforms.scale.y = this.scale.y;\n\n        // Extract rotation from world transform\n        const wt = this.maskSprite.worldTransform;\n        const lenX = Math.sqrt((wt.a * wt.a) + (wt.b * wt.b));\n        const lenY = Math.sqrt((wt.c * wt.c) + (wt.d * wt.d));\n\n        if (lenX !== 0 && lenY !== 0)\n        {\n            this.uniforms.rotation[0] = wt.a / lenX;\n            this.uniforms.rotation[1] = wt.b / lenX;\n            this.uniforms.rotation[2] = wt.c / lenY;\n            this.uniforms.rotation[3] = wt.d / lenY;\n        }\n\n        // draw the filter...\n        filterManager.applyFilter(this, input, output, clearMode);\n    }\n\n    /** The texture used for the displacement map. Must be power of 2 sized texture. */\n    get map(): Texture\n    {\n        return this.uniforms.mapSampler;\n    }\n\n    set map(value: Texture)\n    {\n        this.uniforms.mapSampler = value;\n    }\n}\n"], "mappings": ";;;AAqBO,MAAMA,kBAAA,SAA2BC,MAAA,CACxC;EAAA;AAAA;AAAA;AAAA;EASIC,YAAYC,MAAA,EAA2BC,KAAA,EACvC;IACU,MAAAC,UAAA,GAAa,IAAIC,MAAA;IAEvBH,MAAA,CAAOI,UAAA,GAAa,IAEpB,MAAMC,MAAA,EAAQC,QAAA,EAAU;MACpBC,UAAA,EAAYP,MAAA,CAAOQ,QAAA;MACnBC,YAAA,EAAcP,UAAA;MACdD,KAAA,EAAO;QAAES,CAAA,EAAG;QAAGC,CAAA,EAAG;MAAE;MACpBC,QAAA,EAAU,IAAIC,YAAA,CAAa,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;IAAA,CAC1C,GAED,KAAKC,UAAA,GAAad,MAAA,EAClB,KAAKE,UAAA,GAAaA,UAAA,EAEdD,KAAA,IAAU,SAEVA,KAAA,GAAQ,KAOZ,KAAKA,KAAA,GAAQ,IAAIc,KAAA,CAAMd,KAAA,EAAOA,KAAK;EACvC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASOe,MACHC,aAAA,EAA6BC,KAAA,EAAsBC,MAAA,EAAuBC,SAAA,EAE9E;IAES,KAAAC,QAAA,CAASZ,YAAA,GAAeQ,aAAA,CAAcK,qBAAA,CAAsB,KAAKpB,UAAA,EAAY,KAAKY,UAAU,GACjG,KAAKO,QAAA,CAASpB,KAAA,CAAMS,CAAA,GAAI,KAAKT,KAAA,CAAMS,CAAA,EACnC,KAAKW,QAAA,CAASpB,KAAA,CAAMU,CAAA,GAAI,KAAKV,KAAA,CAAMU,CAAA;IAG7B,MAAAY,EAAA,GAAK,KAAKT,UAAA,CAAWU,cAAA;MACrBC,IAAA,GAAOC,IAAA,CAAKC,IAAA,CAAMJ,EAAA,CAAGK,CAAA,GAAIL,EAAA,CAAGK,CAAA,GAAML,EAAA,CAAGM,CAAA,GAAIN,EAAA,CAAGM,CAAE;MAC9CC,IAAA,GAAOJ,IAAA,CAAKC,IAAA,CAAMJ,EAAA,CAAGQ,CAAA,GAAIR,EAAA,CAAGQ,CAAA,GAAMR,EAAA,CAAGS,CAAA,GAAIT,EAAA,CAAGS,CAAE;IAEhDP,IAAA,KAAS,KAAKK,IAAA,KAAS,MAEvB,KAAKT,QAAA,CAAST,QAAA,CAAS,CAAC,IAAIW,EAAA,CAAGK,CAAA,GAAIH,IAAA,EACnC,KAAKJ,QAAA,CAAST,QAAA,CAAS,CAAC,IAAIW,EAAA,CAAGM,CAAA,GAAIJ,IAAA,EACnC,KAAKJ,QAAA,CAAST,QAAA,CAAS,CAAC,IAAIW,EAAA,CAAGQ,CAAA,GAAID,IAAA,EACnC,KAAKT,QAAA,CAAST,QAAA,CAAS,CAAC,IAAIW,EAAA,CAAGS,CAAA,GAAIF,IAAA,GAIvCb,aAAA,CAAcgB,WAAA,CAAY,MAAMf,KAAA,EAAOC,MAAA,EAAQC,SAAS;EAC5D;EAAA;EAGA,IAAIc,IAAA,EACJ;IACI,OAAO,KAAKb,QAAA,CAASd,UAAA;EACzB;EAEA,IAAI2B,IAAIC,KAAA,EACR;IACI,KAAKd,QAAA,CAASd,UAAA,GAAa4B,KAAA;EAC/B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}