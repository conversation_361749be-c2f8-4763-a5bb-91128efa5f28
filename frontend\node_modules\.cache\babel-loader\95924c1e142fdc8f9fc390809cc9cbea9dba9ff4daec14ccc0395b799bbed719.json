{"ast": null, "code": "import { ExtensionType, settings, utils, extensions } from \"@pixi/core\";\nimport \"../../loader/index.mjs\";\nimport { loadTextures } from \"../../loader/parsers/textures/loadTextures.mjs\";\nconst resolveTextureUrl = {\n  extension: ExtensionType.ResolveParser,\n  test: loadTextures.test,\n  parse: value => ({\n    resolution: parseFloat(settings.RETINA_PREFIX.exec(value)?.[1] ?? \"1\"),\n    format: utils.path.extname(value).slice(1),\n    src: value\n  })\n};\nextensions.add(resolveTextureUrl);\nexport { resolveTextureUrl };", "map": {"version": 3, "names": ["resolveTextureUrl", "extension", "ExtensionType", "Resolve<PERSON><PERSON>er", "test", "loadTextures", "parse", "value", "resolution", "parseFloat", "settings", "RETINA_PREFIX", "exec", "format", "utils", "path", "extname", "slice", "src", "extensions", "add"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\assets\\src\\resolver\\parsers\\resolveTextureUrl.ts"], "sourcesContent": ["import { extensions, ExtensionType, settings, utils } from '@pixi/core';\nimport { loadTextures } from '../../loader';\n\nimport type { UnresolvedAsset } from '../../types';\nimport type { ResolveURLParser } from '../types';\n\nexport const resolveTextureUrl = {\n    extension: ExtensionType.ResolveParser,\n    test: loadTextures.test,\n    parse: (value: string): UnresolvedAsset =>\n        ({\n            resolution: parseFloat(settings.RETINA_PREFIX.exec(value)?.[1] ?? '1'),\n            format: utils.path.extname(value).slice(1),\n            src: value,\n        }),\n} as ResolveURLParser;\n\nextensions.add(resolveTextureUrl);\n"], "mappings": ";;;AAMO,MAAMA,iBAAA,GAAoB;EAC7BC,SAAA,EAAWC,aAAA,CAAcC,aAAA;EACzBC,IAAA,EAAMC,YAAA,CAAaD,IAAA;EACnBE,KAAA,EAAQC,KAAA,KACH;IACGC,UAAA,EAAYC,UAAA,CAAWC,QAAA,CAASC,aAAA,CAAcC,IAAA,CAAKL,KAAK,IAAI,CAAC,KAAK,GAAG;IACrEM,MAAA,EAAQC,KAAA,CAAMC,IAAA,CAAKC,OAAA,CAAQT,KAAK,EAAEU,KAAA,CAAM,CAAC;IACzCC,GAAA,EAAKX;EAAA;AAEjB;AAEAY,UAAA,CAAWC,GAAA,CAAIpB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}