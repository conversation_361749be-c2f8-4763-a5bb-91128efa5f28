{"ast": null, "code": "import { Spritesheet } from \"./Spritesheet.mjs\";\nimport { spritesheetAsset } from \"./spritesheetAsset.mjs\";\nexport { Spritesheet, spritesheetAsset };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import { Spritesheet } from \"./Spritesheet.mjs\";\nimport { spritesheetAsset } from \"./spritesheetAsset.mjs\";\nexport {\n  Spritesheet,\n  spritesheetAsset\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}