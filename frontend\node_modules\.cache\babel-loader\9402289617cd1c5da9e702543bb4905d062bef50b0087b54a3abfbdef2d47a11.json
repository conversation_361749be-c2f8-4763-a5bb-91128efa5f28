{"ast": null, "code": "import { SHAPES } from \"../const.mjs\";\nimport { Point } from \"../Point.mjs\";\nconst tempPoints = [new Point(), new Point(), new Point(), new Point()];\nclass Rectangle {\n  /**\n   * @param x - The X coordinate of the upper-left corner of the rectangle\n   * @param y - The Y coordinate of the upper-left corner of the rectangle\n   * @param width - The overall width of the rectangle\n   * @param height - The overall height of the rectangle\n   */\n  constructor(x = 0, y = 0, width = 0, height = 0) {\n    this.x = Number(x), this.y = Number(y), this.width = Number(width), this.height = Number(height), this.type = SHAPES.RECT;\n  }\n  /** Returns the left edge of the rectangle. */\n  get left() {\n    return this.x;\n  }\n  /** Returns the right edge of the rectangle. */\n  get right() {\n    return this.x + this.width;\n  }\n  /** Returns the top edge of the rectangle. */\n  get top() {\n    return this.y;\n  }\n  /** Returns the bottom edge of the rectangle. */\n  get bottom() {\n    return this.y + this.height;\n  }\n  /** A constant empty rectangle. */\n  static get EMPTY() {\n    return new Rectangle(0, 0, 0, 0);\n  }\n  /**\n   * Creates a clone of this Rectangle\n   * @returns a copy of the rectangle\n   */\n  clone() {\n    return new Rectangle(this.x, this.y, this.width, this.height);\n  }\n  /**\n   * Copies another rectangle to this one.\n   * @param rectangle - The rectangle to copy from.\n   * @returns Returns itself.\n   */\n  copyFrom(rectangle) {\n    return this.x = rectangle.x, this.y = rectangle.y, this.width = rectangle.width, this.height = rectangle.height, this;\n  }\n  /**\n   * Copies this rectangle to another one.\n   * @param rectangle - The rectangle to copy to.\n   * @returns Returns given parameter.\n   */\n  copyTo(rectangle) {\n    return rectangle.x = this.x, rectangle.y = this.y, rectangle.width = this.width, rectangle.height = this.height, rectangle;\n  }\n  /**\n   * Checks whether the x and y coordinates given are contained within this Rectangle\n   * @param x - The X coordinate of the point to test\n   * @param y - The Y coordinate of the point to test\n   * @returns Whether the x/y coordinates are within this Rectangle\n   */\n  contains(x, y) {\n    return this.width <= 0 || this.height <= 0 ? !1 : x >= this.x && x < this.x + this.width && y >= this.y && y < this.y + this.height;\n  }\n  /**\n   * Determines whether the `other` Rectangle transformed by `transform` intersects with `this` Rectangle object.\n   * Returns true only if the area of the intersection is >0, this means that Rectangles\n   * sharing a side are not overlapping. Another side effect is that an arealess rectangle\n   * (width or height equal to zero) can't intersect any other rectangle.\n   * @param {Rectangle} other - The Rectangle to intersect with `this`.\n   * @param {Matrix} transform - The transformation matrix of `other`.\n   * @returns {boolean} A value of `true` if the transformed `other` Rectangle intersects with `this`; otherwise `false`.\n   */\n  intersects(other, transform) {\n    if (!transform) {\n      const x02 = this.x < other.x ? other.x : this.x;\n      if ((this.right > other.right ? other.right : this.right) <= x02) return !1;\n      const y02 = this.y < other.y ? other.y : this.y;\n      return (this.bottom > other.bottom ? other.bottom : this.bottom) > y02;\n    }\n    const x0 = this.left,\n      x1 = this.right,\n      y0 = this.top,\n      y1 = this.bottom;\n    if (x1 <= x0 || y1 <= y0) return !1;\n    const lt = tempPoints[0].set(other.left, other.top),\n      lb = tempPoints[1].set(other.left, other.bottom),\n      rt = tempPoints[2].set(other.right, other.top),\n      rb = tempPoints[3].set(other.right, other.bottom);\n    if (rt.x <= lt.x || lb.y <= lt.y) return !1;\n    const s = Math.sign(transform.a * transform.d - transform.b * transform.c);\n    if (s === 0 || (transform.apply(lt, lt), transform.apply(lb, lb), transform.apply(rt, rt), transform.apply(rb, rb), Math.max(lt.x, lb.x, rt.x, rb.x) <= x0 || Math.min(lt.x, lb.x, rt.x, rb.x) >= x1 || Math.max(lt.y, lb.y, rt.y, rb.y) <= y0 || Math.min(lt.y, lb.y, rt.y, rb.y) >= y1)) return !1;\n    const nx = s * (lb.y - lt.y),\n      ny = s * (lt.x - lb.x),\n      n00 = nx * x0 + ny * y0,\n      n10 = nx * x1 + ny * y0,\n      n01 = nx * x0 + ny * y1,\n      n11 = nx * x1 + ny * y1;\n    if (Math.max(n00, n10, n01, n11) <= nx * lt.x + ny * lt.y || Math.min(n00, n10, n01, n11) >= nx * rb.x + ny * rb.y) return !1;\n    const mx = s * (lt.y - rt.y),\n      my = s * (rt.x - lt.x),\n      m00 = mx * x0 + my * y0,\n      m10 = mx * x1 + my * y0,\n      m01 = mx * x0 + my * y1,\n      m11 = mx * x1 + my * y1;\n    return !(Math.max(m00, m10, m01, m11) <= mx * lt.x + my * lt.y || Math.min(m00, m10, m01, m11) >= mx * rb.x + my * rb.y);\n  }\n  /**\n   * Pads the rectangle making it grow in all directions.\n   * If paddingY is omitted, both paddingX and paddingY will be set to paddingX.\n   * @param paddingX - The horizontal padding amount.\n   * @param paddingY - The vertical padding amount.\n   * @returns Returns itself.\n   */\n  pad(paddingX = 0, paddingY = paddingX) {\n    return this.x -= paddingX, this.y -= paddingY, this.width += paddingX * 2, this.height += paddingY * 2, this;\n  }\n  /**\n   * Fits this rectangle around the passed one.\n   * @param rectangle - The rectangle to fit.\n   * @returns Returns itself.\n   */\n  fit(rectangle) {\n    const x1 = Math.max(this.x, rectangle.x),\n      x2 = Math.min(this.x + this.width, rectangle.x + rectangle.width),\n      y1 = Math.max(this.y, rectangle.y),\n      y2 = Math.min(this.y + this.height, rectangle.y + rectangle.height);\n    return this.x = x1, this.width = Math.max(x2 - x1, 0), this.y = y1, this.height = Math.max(y2 - y1, 0), this;\n  }\n  /**\n   * Enlarges rectangle that way its corners lie on grid\n   * @param resolution - resolution\n   * @param eps - precision\n   * @returns Returns itself.\n   */\n  ceil(resolution = 1, eps = 1e-3) {\n    const x2 = Math.ceil((this.x + this.width - eps) * resolution) / resolution,\n      y2 = Math.ceil((this.y + this.height - eps) * resolution) / resolution;\n    return this.x = Math.floor((this.x + eps) * resolution) / resolution, this.y = Math.floor((this.y + eps) * resolution) / resolution, this.width = x2 - this.x, this.height = y2 - this.y, this;\n  }\n  /**\n   * Enlarges this rectangle to include the passed rectangle.\n   * @param rectangle - The rectangle to include.\n   * @returns Returns itself.\n   */\n  enlarge(rectangle) {\n    const x1 = Math.min(this.x, rectangle.x),\n      x2 = Math.max(this.x + this.width, rectangle.x + rectangle.width),\n      y1 = Math.min(this.y, rectangle.y),\n      y2 = Math.max(this.y + this.height, rectangle.y + rectangle.height);\n    return this.x = x1, this.width = x2 - x1, this.y = y1, this.height = y2 - y1, this;\n  }\n}\nRectangle.prototype.toString = function () {\n  return `[@pixi/math:Rectangle x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`;\n};\nexport { Rectangle };", "map": {"version": 3, "names": ["tempPoints", "Point", "Rectangle", "constructor", "x", "y", "width", "height", "Number", "type", "SHAPES", "RECT", "left", "right", "top", "bottom", "EMPTY", "clone", "copyFrom", "rectangle", "copyTo", "contains", "intersects", "other", "transform", "x02", "y02", "x0", "x1", "y0", "y1", "lt", "set", "lb", "rt", "rb", "s", "Math", "sign", "a", "d", "b", "c", "apply", "max", "min", "nx", "ny", "n00", "n10", "n01", "n11", "mx", "my", "m00", "m10", "m01", "m11", "pad", "paddingX", "paddingY", "fit", "x2", "y2", "ceil", "resolution", "eps", "floor", "enlarge", "prototype", "toString"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\math\\src\\shapes\\Rectangle.ts"], "sourcesContent": ["import { SHAPES } from '../const';\nimport { Point } from '../Point';\n\nimport type { Matrix } from '../Matrix';\n\nconst tempPoints = [new Point(), new Point(), new Point(), new Point()];\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface Rectangle extends GlobalMixins.Rectangle {}\n\n/**\n * Size object, contains width and height\n * @memberof PIXI\n * @typedef {object} ISize\n * @property {number} width - Width component\n * @property {number} height - Height component\n */\n\n/**\n * Rectangle object is an area defined by its position, as indicated by its top-left corner\n * point (x, y) and by its width and its height.\n * @memberof PIXI\n */\nexport class Rectangle\n{\n    /** @default 0 */\n    public x: number;\n\n    /** @default 0 */\n    public y: number;\n    /** @default 0 */\n    public width: number;\n\n    /** @default 0 */\n    public height: number;\n\n    /**\n     * The type of the object, mainly used to avoid `instanceof` checks\n     * @default PIXI.SHAPES.RECT\n     * @see PIXI.SHAPES\n     */\n    public readonly type: SHAPES.RECT;\n\n    /**\n     * @param x - The X coordinate of the upper-left corner of the rectangle\n     * @param y - The Y coordinate of the upper-left corner of the rectangle\n     * @param width - The overall width of the rectangle\n     * @param height - The overall height of the rectangle\n     */\n    constructor(x: string | number = 0, y: string | number = 0, width: string | number = 0, height: string | number = 0)\n    {\n        this.x = Number(x);\n        this.y = Number(y);\n        this.width = Number(width);\n        this.height = Number(height);\n        this.type = SHAPES.RECT;\n    }\n\n    /** Returns the left edge of the rectangle. */\n    get left(): number\n    {\n        return this.x;\n    }\n\n    /** Returns the right edge of the rectangle. */\n    get right(): number\n    {\n        return this.x + this.width;\n    }\n\n    /** Returns the top edge of the rectangle. */\n    get top(): number\n    {\n        return this.y;\n    }\n\n    /** Returns the bottom edge of the rectangle. */\n    get bottom(): number\n    {\n        return this.y + this.height;\n    }\n\n    /** A constant empty rectangle. */\n    static get EMPTY(): Rectangle\n    {\n        return new Rectangle(0, 0, 0, 0);\n    }\n\n    /**\n     * Creates a clone of this Rectangle\n     * @returns a copy of the rectangle\n     */\n    clone(): Rectangle\n    {\n        return new Rectangle(this.x, this.y, this.width, this.height);\n    }\n\n    /**\n     * Copies another rectangle to this one.\n     * @param rectangle - The rectangle to copy from.\n     * @returns Returns itself.\n     */\n    copyFrom(rectangle: Rectangle): Rectangle\n    {\n        this.x = rectangle.x;\n        this.y = rectangle.y;\n        this.width = rectangle.width;\n        this.height = rectangle.height;\n\n        return this;\n    }\n\n    /**\n     * Copies this rectangle to another one.\n     * @param rectangle - The rectangle to copy to.\n     * @returns Returns given parameter.\n     */\n    copyTo(rectangle: Rectangle): Rectangle\n    {\n        rectangle.x = this.x;\n        rectangle.y = this.y;\n        rectangle.width = this.width;\n        rectangle.height = this.height;\n\n        return rectangle;\n    }\n\n    /**\n     * Checks whether the x and y coordinates given are contained within this Rectangle\n     * @param x - The X coordinate of the point to test\n     * @param y - The Y coordinate of the point to test\n     * @returns Whether the x/y coordinates are within this Rectangle\n     */\n    contains(x: number, y: number): boolean\n    {\n        if (this.width <= 0 || this.height <= 0)\n        {\n            return false;\n        }\n\n        if (x >= this.x && x < this.x + this.width)\n        {\n            if (y >= this.y && y < this.y + this.height)\n            {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    /**\n     * Determines whether the `other` Rectangle transformed by `transform` intersects with `this` Rectangle object.\n     * Returns true only if the area of the intersection is >0, this means that Rectangles\n     * sharing a side are not overlapping. Another side effect is that an arealess rectangle\n     * (width or height equal to zero) can't intersect any other rectangle.\n     * @param {Rectangle} other - The Rectangle to intersect with `this`.\n     * @param {Matrix} transform - The transformation matrix of `other`.\n     * @returns {boolean} A value of `true` if the transformed `other` Rectangle intersects with `this`; otherwise `false`.\n     */\n    intersects(other: Rectangle, transform?: Matrix): boolean\n    {\n        if (!transform)\n        {\n            const x0 = this.x < other.x ? other.x : this.x;\n            const x1 = this.right > other.right ? other.right : this.right;\n\n            if (x1 <= x0)\n            {\n                return false;\n            }\n\n            const y0 = this.y < other.y ? other.y : this.y;\n            const y1 = this.bottom > other.bottom ? other.bottom : this.bottom;\n\n            return y1 > y0;\n        }\n\n        const x0 = this.left;\n        const x1 = this.right;\n        const y0 = this.top;\n        const y1 = this.bottom;\n\n        if (x1 <= x0 || y1 <= y0)\n        {\n            return false;\n        }\n\n        const lt = tempPoints[0].set(other.left, other.top);\n        const lb = tempPoints[1].set(other.left, other.bottom);\n        const rt = tempPoints[2].set(other.right, other.top);\n        const rb = tempPoints[3].set(other.right, other.bottom);\n\n        if (rt.x <= lt.x || lb.y <= lt.y)\n        {\n            return false;\n        }\n\n        const s = Math.sign((transform.a * transform.d) - (transform.b * transform.c));\n\n        if (s === 0)\n        {\n            return false;\n        }\n\n        transform.apply(lt, lt);\n        transform.apply(lb, lb);\n        transform.apply(rt, rt);\n        transform.apply(rb, rb);\n\n        if (Math.max(lt.x, lb.x, rt.x, rb.x) <= x0\n            || Math.min(lt.x, lb.x, rt.x, rb.x) >= x1\n            || Math.max(lt.y, lb.y, rt.y, rb.y) <= y0\n            || Math.min(lt.y, lb.y, rt.y, rb.y) >= y1)\n        {\n            return false;\n        }\n\n        const nx = s * (lb.y - lt.y);\n        const ny = s * (lt.x - lb.x);\n        const n00 = (nx * x0) + (ny * y0);\n        const n10 = (nx * x1) + (ny * y0);\n        const n01 = (nx * x0) + (ny * y1);\n        const n11 = (nx * x1) + (ny * y1);\n\n        if (Math.max(n00, n10, n01, n11) <= (nx * lt.x) + (ny * lt.y)\n            || Math.min(n00, n10, n01, n11) >= (nx * rb.x) + (ny * rb.y))\n        {\n            return false;\n        }\n\n        const mx = s * (lt.y - rt.y);\n        const my = s * (rt.x - lt.x);\n        const m00 = (mx * x0) + (my * y0);\n        const m10 = (mx * x1) + (my * y0);\n        const m01 = (mx * x0) + (my * y1);\n        const m11 = (mx * x1) + (my * y1);\n\n        if (Math.max(m00, m10, m01, m11) <= (mx * lt.x) + (my * lt.y)\n            || Math.min(m00, m10, m01, m11) >= (mx * rb.x) + (my * rb.y))\n        {\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * Pads the rectangle making it grow in all directions.\n     * If paddingY is omitted, both paddingX and paddingY will be set to paddingX.\n     * @param paddingX - The horizontal padding amount.\n     * @param paddingY - The vertical padding amount.\n     * @returns Returns itself.\n     */\n    pad(paddingX = 0, paddingY = paddingX): this\n    {\n        this.x -= paddingX;\n        this.y -= paddingY;\n\n        this.width += paddingX * 2;\n        this.height += paddingY * 2;\n\n        return this;\n    }\n\n    /**\n     * Fits this rectangle around the passed one.\n     * @param rectangle - The rectangle to fit.\n     * @returns Returns itself.\n     */\n    fit(rectangle: Rectangle): this\n    {\n        const x1 = Math.max(this.x, rectangle.x);\n        const x2 = Math.min(this.x + this.width, rectangle.x + rectangle.width);\n        const y1 = Math.max(this.y, rectangle.y);\n        const y2 = Math.min(this.y + this.height, rectangle.y + rectangle.height);\n\n        this.x = x1;\n        this.width = Math.max(x2 - x1, 0);\n        this.y = y1;\n        this.height = Math.max(y2 - y1, 0);\n\n        return this;\n    }\n\n    /**\n     * Enlarges rectangle that way its corners lie on grid\n     * @param resolution - resolution\n     * @param eps - precision\n     * @returns Returns itself.\n     */\n    ceil(resolution = 1, eps = 0.001): this\n    {\n        const x2 = Math.ceil((this.x + this.width - eps) * resolution) / resolution;\n        const y2 = Math.ceil((this.y + this.height - eps) * resolution) / resolution;\n\n        this.x = Math.floor((this.x + eps) * resolution) / resolution;\n        this.y = Math.floor((this.y + eps) * resolution) / resolution;\n\n        this.width = x2 - this.x;\n        this.height = y2 - this.y;\n\n        return this;\n    }\n\n    /**\n     * Enlarges this rectangle to include the passed rectangle.\n     * @param rectangle - The rectangle to include.\n     * @returns Returns itself.\n     */\n    enlarge(rectangle: Rectangle): this\n    {\n        const x1 = Math.min(this.x, rectangle.x);\n        const x2 = Math.max(this.x + this.width, rectangle.x + rectangle.width);\n        const y1 = Math.min(this.y, rectangle.y);\n        const y2 = Math.max(this.y + this.height, rectangle.y + rectangle.height);\n\n        this.x = x1;\n        this.width = x2 - x1;\n        this.y = y1;\n        this.height = y2 - y1;\n\n        return this;\n    }\n}\n\nif (process.env.DEBUG)\n{\n    Rectangle.prototype.toString = function toString(): string\n    {\n        return `[@pixi/math:Rectangle x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`;\n    };\n}\n"], "mappings": ";;AAKA,MAAMA,UAAA,GAAa,CAAC,IAAIC,KAAA,IAAS,IAAIA,KAAA,IAAS,IAAIA,KAAA,CAAS,OAAIA,KAAA,EAAO;AAkB/D,MAAMC,SAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAyBIC,YAAYC,CAAA,GAAqB,GAAGC,CAAA,GAAqB,GAAGC,KAAA,GAAyB,GAAGC,MAAA,GAA0B,GAClH;IACS,KAAAH,CAAA,GAAII,MAAA,CAAOJ,CAAC,GACjB,KAAKC,CAAA,GAAIG,MAAA,CAAOH,CAAC,GACjB,KAAKC,KAAA,GAAQE,MAAA,CAAOF,KAAK,GACzB,KAAKC,MAAA,GAASC,MAAA,CAAOD,MAAM,GAC3B,KAAKE,IAAA,GAAOC,MAAA,CAAOC,IAAA;EACvB;EAAA;EAGA,IAAIC,KAAA,EACJ;IACI,OAAO,KAAKR,CAAA;EAChB;EAAA;EAGA,IAAIS,MAAA,EACJ;IACW,YAAKT,CAAA,GAAI,KAAKE,KAAA;EACzB;EAAA;EAGA,IAAIQ,IAAA,EACJ;IACI,OAAO,KAAKT,CAAA;EAChB;EAAA;EAGA,IAAIU,OAAA,EACJ;IACW,YAAKV,CAAA,GAAI,KAAKE,MAAA;EACzB;EAAA;EAGA,WAAWS,MAAA,EACX;IACI,OAAO,IAAId,SAAA,CAAU,GAAG,GAAG,GAAG,CAAC;EACnC;EAAA;AAAA;AAAA;AAAA;EAMAe,MAAA,EACA;IACW,WAAIf,SAAA,CAAU,KAAKE,CAAA,EAAG,KAAKC,CAAA,EAAG,KAAKC,KAAA,EAAO,KAAKC,MAAM;EAChE;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAW,SAASC,SAAA,EACT;IACI,YAAKf,CAAA,GAAIe,SAAA,CAAUf,CAAA,EACnB,KAAKC,CAAA,GAAIc,SAAA,CAAUd,CAAA,EACnB,KAAKC,KAAA,GAAQa,SAAA,CAAUb,KAAA,EACvB,KAAKC,MAAA,GAASY,SAAA,CAAUZ,MAAA,EAEjB;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAa,OAAOD,SAAA,EACP;IACI,OAAAA,SAAA,CAAUf,CAAA,GAAI,KAAKA,CAAA,EACnBe,SAAA,CAAUd,CAAA,GAAI,KAAKA,CAAA,EACnBc,SAAA,CAAUb,KAAA,GAAQ,KAAKA,KAAA,EACvBa,SAAA,CAAUZ,MAAA,GAAS,KAAKA,MAAA,EAEjBY,SAAA;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAE,SAASjB,CAAA,EAAWC,CAAA,EACpB;IACQ,YAAKC,KAAA,IAAS,KAAK,KAAKC,MAAA,IAAU,IAE3B,KAGPH,CAAA,IAAK,KAAKA,CAAA,IAAKA,CAAA,GAAI,KAAKA,CAAA,GAAI,KAAKE,KAAA,IAE7BD,CAAA,IAAK,KAAKA,CAAA,IAAKA,CAAA,GAAI,KAAKA,CAAA,GAAI,KAAKE,MAAA;EAO7C;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWAe,WAAWC,KAAA,EAAkBC,SAAA,EAC7B;IACI,IAAI,CAACA,SAAA,EACL;MACI,MAAMC,GAAA,GAAK,KAAKrB,CAAA,GAAImB,KAAA,CAAMnB,CAAA,GAAImB,KAAA,CAAMnB,CAAA,GAAI,KAAKA,CAAA;MAG7C,KAFW,KAAKS,KAAA,GAAQU,KAAA,CAAMV,KAAA,GAAQU,KAAA,CAAMV,KAAA,GAAQ,KAAKA,KAAA,KAE/CY,GAAA,EAEC;MAGX,MAAMC,GAAA,GAAK,KAAKrB,CAAA,GAAIkB,KAAA,CAAMlB,CAAA,GAAIkB,KAAA,CAAMlB,CAAA,GAAI,KAAKA,CAAA;MAG7C,QAFW,KAAKU,MAAA,GAASQ,KAAA,CAAMR,MAAA,GAASQ,KAAA,CAAMR,MAAA,GAAS,KAAKA,MAAA,IAEhDW,GAAA;IAChB;IAEM,MAAAC,EAAA,GAAK,KAAKf,IAAA;MACVgB,EAAA,GAAK,KAAKf,KAAA;MACVgB,EAAA,GAAK,KAAKf,GAAA;MACVgB,EAAA,GAAK,KAAKf,MAAA;IAEZ,IAAAa,EAAA,IAAMD,EAAA,IAAMG,EAAA,IAAMD,EAAA,EAEX;IAGX,MAAME,EAAA,GAAK/B,UAAA,CAAW,CAAC,EAAEgC,GAAA,CAAIT,KAAA,CAAMX,IAAA,EAAMW,KAAA,CAAMT,GAAG;MAC5CmB,EAAA,GAAKjC,UAAA,CAAW,CAAC,EAAEgC,GAAA,CAAIT,KAAA,CAAMX,IAAA,EAAMW,KAAA,CAAMR,MAAM;MAC/CmB,EAAA,GAAKlC,UAAA,CAAW,CAAC,EAAEgC,GAAA,CAAIT,KAAA,CAAMV,KAAA,EAAOU,KAAA,CAAMT,GAAG;MAC7CqB,EAAA,GAAKnC,UAAA,CAAW,CAAC,EAAEgC,GAAA,CAAIT,KAAA,CAAMV,KAAA,EAAOU,KAAA,CAAMR,MAAM;IAEtD,IAAImB,EAAA,CAAG9B,CAAA,IAAK2B,EAAA,CAAG3B,CAAA,IAAK6B,EAAA,CAAG5B,CAAA,IAAK0B,EAAA,CAAG1B,CAAA,EAEpB;IAGL,MAAA+B,CAAA,GAAIC,IAAA,CAAKC,IAAA,CAAMd,SAAA,CAAUe,CAAA,GAAIf,SAAA,CAAUgB,CAAA,GAAMhB,SAAA,CAAUiB,CAAA,GAAIjB,SAAA,CAAUkB,CAAE;IAY7E,IAVIN,CAAA,KAAM,MAKVZ,SAAA,CAAUmB,KAAA,CAAMZ,EAAA,EAAIA,EAAE,GACtBP,SAAA,CAAUmB,KAAA,CAAMV,EAAA,EAAIA,EAAE,GACtBT,SAAA,CAAUmB,KAAA,CAAMT,EAAA,EAAIA,EAAE,GACtBV,SAAA,CAAUmB,KAAA,CAAMR,EAAA,EAAIA,EAAE,GAElBE,IAAA,CAAKO,GAAA,CAAIb,EAAA,CAAG3B,CAAA,EAAG6B,EAAA,CAAG7B,CAAA,EAAG8B,EAAA,CAAG9B,CAAA,EAAG+B,EAAA,CAAG/B,CAAC,KAAKuB,EAAA,IACjCU,IAAA,CAAKQ,GAAA,CAAId,EAAA,CAAG3B,CAAA,EAAG6B,EAAA,CAAG7B,CAAA,EAAG8B,EAAA,CAAG9B,CAAA,EAAG+B,EAAA,CAAG/B,CAAC,KAAKwB,EAAA,IACpCS,IAAA,CAAKO,GAAA,CAAIb,EAAA,CAAG1B,CAAA,EAAG4B,EAAA,CAAG5B,CAAA,EAAG6B,EAAA,CAAG7B,CAAA,EAAG8B,EAAA,CAAG9B,CAAC,KAAKwB,EAAA,IACpCQ,IAAA,CAAKQ,GAAA,CAAId,EAAA,CAAG1B,CAAA,EAAG4B,EAAA,CAAG5B,CAAA,EAAG6B,EAAA,CAAG7B,CAAA,EAAG8B,EAAA,CAAG9B,CAAC,KAAKyB,EAAA,GAEhC;IAGX,MAAMgB,EAAA,GAAKV,CAAA,IAAKH,EAAA,CAAG5B,CAAA,GAAI0B,EAAA,CAAG1B,CAAA;MACpB0C,EAAA,GAAKX,CAAA,IAAKL,EAAA,CAAG3B,CAAA,GAAI6B,EAAA,CAAG7B,CAAA;MACpB4C,GAAA,GAAOF,EAAA,GAAKnB,EAAA,GAAOoB,EAAA,GAAKlB,EAAA;MACxBoB,GAAA,GAAOH,EAAA,GAAKlB,EAAA,GAAOmB,EAAA,GAAKlB,EAAA;MACxBqB,GAAA,GAAOJ,EAAA,GAAKnB,EAAA,GAAOoB,EAAA,GAAKjB,EAAA;MACxBqB,GAAA,GAAOL,EAAA,GAAKlB,EAAA,GAAOmB,EAAA,GAAKjB,EAAA;IAE1B,IAAAO,IAAA,CAAKO,GAAA,CAAII,GAAA,EAAKC,GAAA,EAAKC,GAAA,EAAKC,GAAG,KAAML,EAAA,GAAKf,EAAA,CAAG3B,CAAA,GAAM2C,EAAA,GAAKhB,EAAA,CAAG1B,CAAA,IACpDgC,IAAA,CAAKQ,GAAA,CAAIG,GAAA,EAAKC,GAAA,EAAKC,GAAA,EAAKC,GAAG,KAAML,EAAA,GAAKX,EAAA,CAAG/B,CAAA,GAAM2C,EAAA,GAAKZ,EAAA,CAAG9B,CAAA,EAEnD;IAGX,MAAM+C,EAAA,GAAKhB,CAAA,IAAKL,EAAA,CAAG1B,CAAA,GAAI6B,EAAA,CAAG7B,CAAA;MACpBgD,EAAA,GAAKjB,CAAA,IAAKF,EAAA,CAAG9B,CAAA,GAAI2B,EAAA,CAAG3B,CAAA;MACpBkD,GAAA,GAAOF,EAAA,GAAKzB,EAAA,GAAO0B,EAAA,GAAKxB,EAAA;MACxB0B,GAAA,GAAOH,EAAA,GAAKxB,EAAA,GAAOyB,EAAA,GAAKxB,EAAA;MACxB2B,GAAA,GAAOJ,EAAA,GAAKzB,EAAA,GAAO0B,EAAA,GAAKvB,EAAA;MACxB2B,GAAA,GAAOL,EAAA,GAAKxB,EAAA,GAAOyB,EAAA,GAAKvB,EAAA;IAE1B,SAAAO,IAAA,CAAKO,GAAA,CAAIU,GAAA,EAAKC,GAAA,EAAKC,GAAA,EAAKC,GAAG,KAAML,EAAA,GAAKrB,EAAA,CAAG3B,CAAA,GAAMiD,EAAA,GAAKtB,EAAA,CAAG1B,CAAA,IACpDgC,IAAA,CAAKQ,GAAA,CAAIS,GAAA,EAAKC,GAAA,EAAKC,GAAA,EAAKC,GAAG,KAAML,EAAA,GAAKjB,EAAA,CAAG/B,CAAA,GAAMiD,EAAA,GAAKlB,EAAA,CAAG9B,CAAA;EAMlE;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASAqD,IAAIC,QAAA,GAAW,GAAGC,QAAA,GAAWD,QAAA,EAC7B;IACI,YAAKvD,CAAA,IAAKuD,QAAA,EACV,KAAKtD,CAAA,IAAKuD,QAAA,EAEV,KAAKtD,KAAA,IAASqD,QAAA,GAAW,GACzB,KAAKpD,MAAA,IAAUqD,QAAA,GAAW,GAEnB;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAC,IAAI1C,SAAA,EACJ;IACI,MAAMS,EAAA,GAAKS,IAAA,CAAKO,GAAA,CAAI,KAAKxC,CAAA,EAAGe,SAAA,CAAUf,CAAC;MACjC0D,EAAA,GAAKzB,IAAA,CAAKQ,GAAA,CAAI,KAAKzC,CAAA,GAAI,KAAKE,KAAA,EAAOa,SAAA,CAAUf,CAAA,GAAIe,SAAA,CAAUb,KAAK;MAChEwB,EAAA,GAAKO,IAAA,CAAKO,GAAA,CAAI,KAAKvC,CAAA,EAAGc,SAAA,CAAUd,CAAC;MACjC0D,EAAA,GAAK1B,IAAA,CAAKQ,GAAA,CAAI,KAAKxC,CAAA,GAAI,KAAKE,MAAA,EAAQY,SAAA,CAAUd,CAAA,GAAIc,SAAA,CAAUZ,MAAM;IAEnE,YAAAH,CAAA,GAAIwB,EAAA,EACT,KAAKtB,KAAA,GAAQ+B,IAAA,CAAKO,GAAA,CAAIkB,EAAA,GAAKlC,EAAA,EAAI,CAAC,GAChC,KAAKvB,CAAA,GAAIyB,EAAA,EACT,KAAKvB,MAAA,GAAS8B,IAAA,CAAKO,GAAA,CAAImB,EAAA,GAAKjC,EAAA,EAAI,CAAC,GAE1B;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAkC,KAAKC,UAAA,GAAa,GAAGC,GAAA,GAAM,MAC3B;IACU,MAAAJ,EAAA,GAAKzB,IAAA,CAAK2B,IAAA,EAAM,KAAK5D,CAAA,GAAI,KAAKE,KAAA,GAAQ4D,GAAA,IAAOD,UAAU,IAAIA,UAAA;MAC3DF,EAAA,GAAK1B,IAAA,CAAK2B,IAAA,EAAM,KAAK3D,CAAA,GAAI,KAAKE,MAAA,GAAS2D,GAAA,IAAOD,UAAU,IAAIA,UAAA;IAElE,YAAK7D,CAAA,GAAIiC,IAAA,CAAK8B,KAAA,EAAO,KAAK/D,CAAA,GAAI8D,GAAA,IAAOD,UAAU,IAAIA,UAAA,EACnD,KAAK5D,CAAA,GAAIgC,IAAA,CAAK8B,KAAA,EAAO,KAAK9D,CAAA,GAAI6D,GAAA,IAAOD,UAAU,IAAIA,UAAA,EAEnD,KAAK3D,KAAA,GAAQwD,EAAA,GAAK,KAAK1D,CAAA,EACvB,KAAKG,MAAA,GAASwD,EAAA,GAAK,KAAK1D,CAAA,EAEjB;EACX;EAAA;AAAA;AAAA;AAAA;AAAA;EAOA+D,QAAQjD,SAAA,EACR;IACI,MAAMS,EAAA,GAAKS,IAAA,CAAKQ,GAAA,CAAI,KAAKzC,CAAA,EAAGe,SAAA,CAAUf,CAAC;MACjC0D,EAAA,GAAKzB,IAAA,CAAKO,GAAA,CAAI,KAAKxC,CAAA,GAAI,KAAKE,KAAA,EAAOa,SAAA,CAAUf,CAAA,GAAIe,SAAA,CAAUb,KAAK;MAChEwB,EAAA,GAAKO,IAAA,CAAKQ,GAAA,CAAI,KAAKxC,CAAA,EAAGc,SAAA,CAAUd,CAAC;MACjC0D,EAAA,GAAK1B,IAAA,CAAKO,GAAA,CAAI,KAAKvC,CAAA,GAAI,KAAKE,MAAA,EAAQY,SAAA,CAAUd,CAAA,GAAIc,SAAA,CAAUZ,MAAM;IAExE,YAAKH,CAAA,GAAIwB,EAAA,EACT,KAAKtB,KAAA,GAAQwD,EAAA,GAAKlC,EAAA,EAClB,KAAKvB,CAAA,GAAIyB,EAAA,EACT,KAAKvB,MAAA,GAASwD,EAAA,GAAKjC,EAAA,EAEZ;EACX;AACJ;AAII5B,SAAA,CAAUmE,SAAA,CAAUC,QAAA,GAAW,YAC/B;EACW,kCAA2B,KAAKlE,CAAC,MAAM,KAAKC,CAAC,UAAU,KAAKC,KAAK,WAAW,KAAKC,MAAM;AAClG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}