{"ast": null, "code": "class BitmapFontData {\n  constructor() {\n    this.info = [], this.common = [], this.page = [], this.char = [], this.kerning = [], this.distanceField = [];\n  }\n}\nexport { BitmapFontData };", "map": {"version": 3, "names": ["BitmapFontData", "constructor", "info", "common", "page", "char", "kerning", "distanceField"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\text-bitmap\\src\\BitmapFontData.ts"], "sourcesContent": ["/* eslint-disable max-len */\n\n/**\n * Normalized parsed data from .fnt files.\n * @memberof PIXI\n */\nexport class BitmapFontData\n{\n    /** @readonly */\n    public info: IBitmapFontDataInfo[];\n\n    /** @readonly */\n    public common: IBitmapFontDataCommon[];\n\n    /** @readonly */\n    public page: IBitmapFontDataPage[];\n\n    /** @readonly */\n    public char: IBitmapFontDataChar[];\n\n    /** @readonly */\n    public kerning: IBitmapFontDataKerning[];\n\n    /** @readonly */\n    public distanceField: IBitmapFontDataDistanceField[];\n\n    constructor()\n    {\n        this.info = [];\n        this.common = [];\n        this.page = [];\n        this.char = [];\n        this.kerning = [];\n        this.distanceField = [];\n    }\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataInfo\n{\n    /** Font face */\n    face: string;\n\n    /** Font size */\n    size: number;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataCommon\n{\n    /** Line height, in pixels. */\n    lineHeight: number;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataPage\n{\n    /** Unique id for bitmap texture */\n    id: number;\n\n    /** File name */\n    file: string;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataChar\n{\n    /** Unique id of character */\n    id: number;\n\n    /** {@link PIXI.IBitmapFontDataPage} id */\n    page: number;\n\n    /** x-position of character in page. */\n    x: number;\n\n    /** y-position of character in page. */\n    y: number;\n\n    /** Width of character in page. */\n    width: number;\n\n    /** Height of character in page. */\n    height: number;\n\n    /** x-offset to apply when rendering character */\n    xoffset: number;\n\n    /** y-offset to apply when rendering character. */\n    yoffset: number;\n\n    /** Advancement to apply to next character. */\n    xadvance: number;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataKerning\n{\n    /** First character of pair */\n    first: number;\n\n    /** Second character of pair */\n    second: number;\n\n    /** x-offset to apply between first & second characters when they are next to each other. */\n    amount: number;\n}\n\n/** @memberof PIXI */\nexport interface IBitmapFontDataDistanceField\n{\n    /** Type of distance field */\n    fieldType: string;\n\n    /** Range of distance */\n    distanceRange: number;\n}\n"], "mappings": "AAMO,MAAMA,cAAA,CACb;EAmBIC,YAAA,EACA;IACS,KAAAC,IAAA,GAAO,IACZ,KAAKC,MAAA,GAAS,EAAC,EACf,KAAKC,IAAA,GAAO,IACZ,KAAKC,IAAA,GAAO,EACZ,OAAKC,OAAA,GAAU,IACf,KAAKC,aAAA,GAAgB;EACzB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}