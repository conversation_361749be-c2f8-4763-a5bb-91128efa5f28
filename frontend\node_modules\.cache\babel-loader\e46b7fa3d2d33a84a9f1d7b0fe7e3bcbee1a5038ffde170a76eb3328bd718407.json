{"ast": null, "code": "import { Geo<PERSON>, TYPES, Buffer, utils } from \"@pixi/core\";\nclass ParticleBuffer {\n  /**\n   * @param {object} properties - The properties to upload.\n   * @param {boolean[]} dynamicPropertyFlags - Flags for which properties are dynamic.\n   * @param {number} size - The size of the batch.\n   */\n  constructor(properties, dynamicPropertyFlags, size) {\n    this.geometry = new Geometry(), this.indexBuffer = null, this.size = size, this.dynamicProperties = [], this.staticProperties = [];\n    for (let i = 0; i < properties.length; ++i) {\n      let property = properties[i];\n      property = {\n        attributeName: property.attributeName,\n        size: property.size,\n        uploadFunction: property.uploadFunction,\n        type: property.type || TYPES.FLOAT,\n        offset: property.offset\n      }, dynamicPropertyFlags[i] ? this.dynamicProperties.push(property) : this.staticProperties.push(property);\n    }\n    this.staticStride = 0, this.staticBuffer = null, this.staticData = null, this.staticDataUint32 = null, this.dynamicStride = 0, this.dynamicBuffer = null, this.dynamicData = null, this.dynamicDataUint32 = null, this._updateID = 0, this.initBuffers();\n  }\n  /** Sets up the renderer context and necessary buffers. */\n  initBuffers() {\n    const geometry = this.geometry;\n    let dynamicOffset = 0;\n    this.indexBuffer = new Buffer(utils.createIndicesForQuads(this.size), !0, !0), geometry.addIndex(this.indexBuffer), this.dynamicStride = 0;\n    for (let i = 0; i < this.dynamicProperties.length; ++i) {\n      const property = this.dynamicProperties[i];\n      property.offset = dynamicOffset, dynamicOffset += property.size, this.dynamicStride += property.size;\n    }\n    const dynBuffer = new ArrayBuffer(this.size * this.dynamicStride * 4 * 4);\n    this.dynamicData = new Float32Array(dynBuffer), this.dynamicDataUint32 = new Uint32Array(dynBuffer), this.dynamicBuffer = new Buffer(this.dynamicData, !1, !1);\n    let staticOffset = 0;\n    this.staticStride = 0;\n    for (let i = 0; i < this.staticProperties.length; ++i) {\n      const property = this.staticProperties[i];\n      property.offset = staticOffset, staticOffset += property.size, this.staticStride += property.size;\n    }\n    const statBuffer = new ArrayBuffer(this.size * this.staticStride * 4 * 4);\n    this.staticData = new Float32Array(statBuffer), this.staticDataUint32 = new Uint32Array(statBuffer), this.staticBuffer = new Buffer(this.staticData, !0, !1);\n    for (let i = 0; i < this.dynamicProperties.length; ++i) {\n      const property = this.dynamicProperties[i];\n      geometry.addAttribute(property.attributeName, this.dynamicBuffer, 0, property.type === TYPES.UNSIGNED_BYTE, property.type, this.dynamicStride * 4, property.offset * 4);\n    }\n    for (let i = 0; i < this.staticProperties.length; ++i) {\n      const property = this.staticProperties[i];\n      geometry.addAttribute(property.attributeName, this.staticBuffer, 0, property.type === TYPES.UNSIGNED_BYTE, property.type, this.staticStride * 4, property.offset * 4);\n    }\n  }\n  /**\n   * Uploads the dynamic properties.\n   * @param children - The children to upload.\n   * @param startIndex - The index to start at.\n   * @param amount - The number to upload.\n   */\n  uploadDynamic(children, startIndex, amount) {\n    for (let i = 0; i < this.dynamicProperties.length; i++) {\n      const property = this.dynamicProperties[i];\n      property.uploadFunction(children, startIndex, amount, property.type === TYPES.UNSIGNED_BYTE ? this.dynamicDataUint32 : this.dynamicData, this.dynamicStride, property.offset);\n    }\n    this.dynamicBuffer._updateID++;\n  }\n  /**\n   * Uploads the static properties.\n   * @param children - The children to upload.\n   * @param startIndex - The index to start at.\n   * @param amount - The number to upload.\n   */\n  uploadStatic(children, startIndex, amount) {\n    for (let i = 0; i < this.staticProperties.length; i++) {\n      const property = this.staticProperties[i];\n      property.uploadFunction(children, startIndex, amount, property.type === TYPES.UNSIGNED_BYTE ? this.staticDataUint32 : this.staticData, this.staticStride, property.offset);\n    }\n    this.staticBuffer._updateID++;\n  }\n  /** Destroys the ParticleBuffer. */\n  destroy() {\n    this.indexBuffer = null, this.dynamicProperties = null, this.dynamicBuffer = null, this.dynamicData = null, this.dynamicDataUint32 = null, this.staticProperties = null, this.staticBuffer = null, this.staticData = null, this.staticDataUint32 = null, this.geometry.destroy();\n  }\n}\nexport { ParticleBuffer };", "map": {"version": 3, "names": ["ParticleBuffer", "constructor", "properties", "dynamicPropertyFlags", "size", "geometry", "Geometry", "indexBuffer", "dynamicProperties", "staticProperties", "i", "length", "property", "attributeName", "uploadFunction", "type", "TYPES", "FLOAT", "offset", "push", "staticStride", "staticBuffer", "staticData", "staticDataUint32", "dynamicStride", "dynamicBuffer", "dynamicData", "dynamicDataUint32", "_updateID", "initBuffers", "dynamicOffset", "<PERSON><PERSON><PERSON>", "utils", "createIndicesForQuads", "addIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Float32Array", "Uint32Array", "staticOffset", "statBuffer", "addAttribute", "UNSIGNED_BYTE", "uploadDynamic", "children", "startIndex", "amount", "uploadStatic", "destroy"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\particle-container\\src\\ParticleBuffer.ts"], "sourcesContent": ["import { Buffer, Geometry, TYPES, utils } from '@pixi/core';\n\nimport type { Sprite } from '@pixi/sprite';\nimport type { IParticleRendererProperty } from './ParticleRenderer';\n\n/*\n * <AUTHOR>\n *\n * Big thanks to the very clever <PERSON> <mattdesl> https://github.com/mattdesl/\n * for creating the original PixiJS version!\n * Also a thanks to https://github.com/bchevalier for tweaking the tint and alpha so that\n * they now share 4 bytes on the vertex buffer\n *\n * Heavily inspired by LibGDX's ParticleBuffer:\n * https://github.com/libgdx/libgdx/blob/master/gdx/src/com/badlogic/gdx/graphics/g2d/ParticleBuffer.java\n */\n\n/**\n * The particle buffer manages the static and dynamic buffers for a particle container.\n * @private\n * @memberof PIXI\n */\nexport class ParticleBuffer\n{\n    public geometry: Geometry;\n    public staticStride: number;\n    public staticBuffer: Buffer;\n    public staticData: Float32Array;\n    public staticDataUint32: Uint32Array;\n    public dynamicStride: number;\n    public dynamicBuffer: Buffer;\n    public dynamicData: Float32Array;\n    public dynamicDataUint32: Uint32Array;\n    public _updateID: number;\n\n    /** Holds the indices of the geometry (quads) to draw. */\n    indexBuffer: Buffer;\n\n    /** The number of particles the buffer can hold. */\n    private size: number;\n\n    /** A list of the properties that are dynamic. */\n    private dynamicProperties: IParticleRendererProperty[];\n\n    /** A list of the properties that are static. */\n    private staticProperties: IParticleRendererProperty[];\n\n    /**\n     * @param {object} properties - The properties to upload.\n     * @param {boolean[]} dynamicPropertyFlags - Flags for which properties are dynamic.\n     * @param {number} size - The size of the batch.\n     */\n    constructor(properties: IParticleRendererProperty[], dynamicPropertyFlags: boolean[], size: number)\n    {\n        this.geometry = new Geometry();\n\n        this.indexBuffer = null;\n\n        this.size = size;\n        this.dynamicProperties = [];\n        this.staticProperties = [];\n\n        for (let i = 0; i < properties.length; ++i)\n        {\n            let property = properties[i];\n\n            // Make copy of properties object so that when we edit the offset it doesn't\n            // change all other instances of the object literal\n            property = {\n                attributeName: property.attributeName,\n                size: property.size,\n                uploadFunction: property.uploadFunction,\n                type: property.type || TYPES.FLOAT,\n                offset: property.offset,\n            };\n\n            if (dynamicPropertyFlags[i])\n            {\n                this.dynamicProperties.push(property);\n            }\n            else\n            {\n                this.staticProperties.push(property);\n            }\n        }\n\n        this.staticStride = 0;\n        this.staticBuffer = null;\n        this.staticData = null;\n        this.staticDataUint32 = null;\n\n        this.dynamicStride = 0;\n        this.dynamicBuffer = null;\n        this.dynamicData = null;\n        this.dynamicDataUint32 = null;\n\n        this._updateID = 0;\n\n        this.initBuffers();\n    }\n\n    /** Sets up the renderer context and necessary buffers. */\n    private initBuffers(): void\n    {\n        const geometry = this.geometry;\n\n        let dynamicOffset = 0;\n\n        this.indexBuffer = new Buffer(utils.createIndicesForQuads(this.size), true, true);\n        geometry.addIndex(this.indexBuffer);\n\n        this.dynamicStride = 0;\n\n        for (let i = 0; i < this.dynamicProperties.length; ++i)\n        {\n            const property = this.dynamicProperties[i];\n\n            property.offset = dynamicOffset;\n            dynamicOffset += property.size;\n            this.dynamicStride += property.size;\n        }\n\n        const dynBuffer = new ArrayBuffer(this.size * this.dynamicStride * 4 * 4);\n\n        this.dynamicData = new Float32Array(dynBuffer);\n        this.dynamicDataUint32 = new Uint32Array(dynBuffer);\n        this.dynamicBuffer = new Buffer(this.dynamicData, false, false);\n\n        // static //\n        let staticOffset = 0;\n\n        this.staticStride = 0;\n\n        for (let i = 0; i < this.staticProperties.length; ++i)\n        {\n            const property = this.staticProperties[i];\n\n            property.offset = staticOffset;\n            staticOffset += property.size;\n            this.staticStride += property.size;\n        }\n\n        const statBuffer = new ArrayBuffer(this.size * this.staticStride * 4 * 4);\n\n        this.staticData = new Float32Array(statBuffer);\n        this.staticDataUint32 = new Uint32Array(statBuffer);\n        this.staticBuffer = new Buffer(this.staticData, true, false);\n\n        for (let i = 0; i < this.dynamicProperties.length; ++i)\n        {\n            const property = this.dynamicProperties[i];\n\n            geometry.addAttribute(\n                property.attributeName,\n                this.dynamicBuffer,\n                0,\n                property.type === TYPES.UNSIGNED_BYTE,\n                property.type,\n                this.dynamicStride * 4,\n                property.offset * 4\n            );\n        }\n\n        for (let i = 0; i < this.staticProperties.length; ++i)\n        {\n            const property = this.staticProperties[i];\n\n            geometry.addAttribute(\n                property.attributeName,\n                this.staticBuffer,\n                0,\n                property.type === TYPES.UNSIGNED_BYTE,\n                property.type,\n                this.staticStride * 4,\n                property.offset * 4\n            );\n        }\n    }\n\n    /**\n     * Uploads the dynamic properties.\n     * @param children - The children to upload.\n     * @param startIndex - The index to start at.\n     * @param amount - The number to upload.\n     */\n    uploadDynamic(children: Sprite[], startIndex: number, amount: number): void\n    {\n        for (let i = 0; i < this.dynamicProperties.length; i++)\n        {\n            const property = this.dynamicProperties[i];\n\n            property.uploadFunction(children, startIndex, amount,\n                property.type === TYPES.UNSIGNED_BYTE ? this.dynamicDataUint32 : this.dynamicData,\n                this.dynamicStride, property.offset);\n        }\n\n        this.dynamicBuffer._updateID++;\n    }\n\n    /**\n     * Uploads the static properties.\n     * @param children - The children to upload.\n     * @param startIndex - The index to start at.\n     * @param amount - The number to upload.\n     */\n    uploadStatic(children: Sprite[], startIndex: number, amount: number): void\n    {\n        for (let i = 0; i < this.staticProperties.length; i++)\n        {\n            const property = this.staticProperties[i];\n\n            property.uploadFunction(children, startIndex, amount,\n                property.type === TYPES.UNSIGNED_BYTE ? this.staticDataUint32 : this.staticData,\n                this.staticStride, property.offset);\n        }\n\n        this.staticBuffer._updateID++;\n    }\n\n    /** Destroys the ParticleBuffer. */\n    destroy(): void\n    {\n        this.indexBuffer = null;\n\n        this.dynamicProperties = null;\n        this.dynamicBuffer = null;\n        this.dynamicData = null;\n        this.dynamicDataUint32 = null;\n\n        this.staticProperties = null;\n        this.staticBuffer = null;\n        this.staticData = null;\n        this.staticDataUint32 = null;\n        // all buffers are destroyed inside geometry\n        this.geometry.destroy();\n    }\n}\n"], "mappings": ";AAsBO,MAAMA,cAAA,CACb;EAAA;AAAA;AAAA;AAAA;AAAA;EA6BIC,YAAYC,UAAA,EAAyCC,oBAAA,EAAiCC,IAAA,EACtF;IACI,KAAKC,QAAA,GAAW,IAAIC,QAAA,CAEpB,QAAKC,WAAA,GAAc,MAEnB,KAAKH,IAAA,GAAOA,IAAA,EACZ,KAAKI,iBAAA,GAAoB,EACzB,OAAKC,gBAAA,GAAmB;IAExB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIR,UAAA,CAAWS,MAAA,EAAQ,EAAED,CAAA,EACzC;MACQ,IAAAE,QAAA,GAAWV,UAAA,CAAWQ,CAAC;MAIhBE,QAAA;QACPC,aAAA,EAAeD,QAAA,CAASC,aAAA;QACxBT,IAAA,EAAMQ,QAAA,CAASR,IAAA;QACfU,cAAA,EAAgBF,QAAA,CAASE,cAAA;QACzBC,IAAA,EAAMH,QAAA,CAASG,IAAA,IAAQC,KAAA,CAAMC,KAAA;QAC7BC,MAAA,EAAQN,QAAA,CAASM;MAGjB,GAAAf,oBAAA,CAAqBO,CAAC,IAEtB,KAAKF,iBAAA,CAAkBW,IAAA,CAAKP,QAAQ,IAIpC,KAAKH,gBAAA,CAAiBU,IAAA,CAAKP,QAAQ;IAE3C;IAEK,KAAAQ,YAAA,GAAe,GACpB,KAAKC,YAAA,GAAe,MACpB,KAAKC,UAAA,GAAa,MAClB,KAAKC,gBAAA,GAAmB,MAExB,KAAKC,aAAA,GAAgB,GACrB,KAAKC,aAAA,GAAgB,MACrB,KAAKC,WAAA,GAAc,MACnB,KAAKC,iBAAA,GAAoB,MAEzB,KAAKC,SAAA,GAAY,GAEjB,KAAKC,WAAA,CAAY;EACrB;EAAA;EAGQA,YAAA,EACR;IACI,MAAMxB,QAAA,GAAW,KAAKA,QAAA;IAEtB,IAAIyB,aAAA,GAAgB;IAEpB,KAAKvB,WAAA,GAAc,IAAIwB,MAAA,CAAOC,KAAA,CAAMC,qBAAA,CAAsB,KAAK7B,IAAI,GAAG,IAAM,EAAI,GAChFC,QAAA,CAAS6B,QAAA,CAAS,KAAK3B,WAAW,GAElC,KAAKiB,aAAA,GAAgB;IAErB,SAASd,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKF,iBAAA,CAAkBG,MAAA,EAAQ,EAAED,CAAA,EACrD;MACU,MAAAE,QAAA,GAAW,KAAKJ,iBAAA,CAAkBE,CAAC;MAEzCE,QAAA,CAASM,MAAA,GAASY,aAAA,EAClBA,aAAA,IAAiBlB,QAAA,CAASR,IAAA,EAC1B,KAAKoB,aAAA,IAAiBZ,QAAA,CAASR,IAAA;IACnC;IAEM,MAAA+B,SAAA,GAAY,IAAIC,WAAA,CAAY,KAAKhC,IAAA,GAAO,KAAKoB,aAAA,GAAgB,IAAI,CAAC;IAExE,KAAKE,WAAA,GAAc,IAAIW,YAAA,CAAaF,SAAS,GAC7C,KAAKR,iBAAA,GAAoB,IAAIW,WAAA,CAAYH,SAAS,GAClD,KAAKV,aAAA,GAAgB,IAAIM,MAAA,CAAO,KAAKL,WAAA,EAAa,IAAO,EAAK;IAG9D,IAAIa,YAAA,GAAe;IAEnB,KAAKnB,YAAA,GAAe;IAEpB,SAASV,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKD,gBAAA,CAAiBE,MAAA,EAAQ,EAAED,CAAA,EACpD;MACU,MAAAE,QAAA,GAAW,KAAKH,gBAAA,CAAiBC,CAAC;MAExCE,QAAA,CAASM,MAAA,GAASqB,YAAA,EAClBA,YAAA,IAAgB3B,QAAA,CAASR,IAAA,EACzB,KAAKgB,YAAA,IAAgBR,QAAA,CAASR,IAAA;IAClC;IAEM,MAAAoC,UAAA,GAAa,IAAIJ,WAAA,CAAY,KAAKhC,IAAA,GAAO,KAAKgB,YAAA,GAAe,IAAI,CAAC;IAExE,KAAKE,UAAA,GAAa,IAAIe,YAAA,CAAaG,UAAU,GAC7C,KAAKjB,gBAAA,GAAmB,IAAIe,WAAA,CAAYE,UAAU,GAClD,KAAKnB,YAAA,GAAe,IAAIU,MAAA,CAAO,KAAKT,UAAA,EAAY,IAAM,EAAK;IAE3D,SAASZ,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKF,iBAAA,CAAkBG,MAAA,EAAQ,EAAED,CAAA,EACrD;MACU,MAAAE,QAAA,GAAW,KAAKJ,iBAAA,CAAkBE,CAAC;MAEhCL,QAAA,CAAAoC,YAAA,CACL7B,QAAA,CAASC,aAAA,EACT,KAAKY,aAAA,EACL,GACAb,QAAA,CAASG,IAAA,KAASC,KAAA,CAAM0B,aAAA,EACxB9B,QAAA,CAASG,IAAA,EACT,KAAKS,aAAA,GAAgB,GACrBZ,QAAA,CAASM,MAAA,GAAS;IAE1B;IAEA,SAASR,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKD,gBAAA,CAAiBE,MAAA,EAAQ,EAAED,CAAA,EACpD;MACU,MAAAE,QAAA,GAAW,KAAKH,gBAAA,CAAiBC,CAAC;MAE/BL,QAAA,CAAAoC,YAAA,CACL7B,QAAA,CAASC,aAAA,EACT,KAAKQ,YAAA,EACL,GACAT,QAAA,CAASG,IAAA,KAASC,KAAA,CAAM0B,aAAA,EACxB9B,QAAA,CAASG,IAAA,EACT,KAAKK,YAAA,GAAe,GACpBR,QAAA,CAASM,MAAA,GAAS;IAE1B;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAyB,cAAcC,QAAA,EAAoBC,UAAA,EAAoBC,MAAA,EACtD;IACI,SAASpC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKF,iBAAA,CAAkBG,MAAA,EAAQD,CAAA,IACnD;MACU,MAAAE,QAAA,GAAW,KAAKJ,iBAAA,CAAkBE,CAAC;MAEhCE,QAAA,CAAAE,cAAA,CAAe8B,QAAA,EAAUC,UAAA,EAAYC,MAAA,EAC1ClC,QAAA,CAASG,IAAA,KAASC,KAAA,CAAM0B,aAAA,GAAgB,KAAKf,iBAAA,GAAoB,KAAKD,WAAA,EACtE,KAAKF,aAAA,EAAeZ,QAAA,CAASM,MAAA;IACrC;IAEA,KAAKO,aAAA,CAAcG,SAAA;EACvB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAmB,aAAaH,QAAA,EAAoBC,UAAA,EAAoBC,MAAA,EACrD;IACI,SAASpC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKD,gBAAA,CAAiBE,MAAA,EAAQD,CAAA,IAClD;MACU,MAAAE,QAAA,GAAW,KAAKH,gBAAA,CAAiBC,CAAC;MAE/BE,QAAA,CAAAE,cAAA,CAAe8B,QAAA,EAAUC,UAAA,EAAYC,MAAA,EAC1ClC,QAAA,CAASG,IAAA,KAASC,KAAA,CAAM0B,aAAA,GAAgB,KAAKnB,gBAAA,GAAmB,KAAKD,UAAA,EACrE,KAAKF,YAAA,EAAcR,QAAA,CAASM,MAAA;IACpC;IAEA,KAAKG,YAAA,CAAaO,SAAA;EACtB;EAAA;EAGAoB,QAAA,EACA;IACI,KAAKzC,WAAA,GAAc,MAEnB,KAAKC,iBAAA,GAAoB,MACzB,KAAKiB,aAAA,GAAgB,MACrB,KAAKC,WAAA,GAAc,MACnB,KAAKC,iBAAA,GAAoB,MAEzB,KAAKlB,gBAAA,GAAmB,MACxB,KAAKY,YAAA,GAAe,MACpB,KAAKC,UAAA,GAAa,MAClB,KAAKC,gBAAA,GAAmB,MAExB,KAAKlB,QAAA,CAAS2C,OAAA,CAAQ;EAC1B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}