{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Projects\\\\Python\\\\EU4\\\\frontend\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport axios from \"axios\";\nimport WorldMap from './WorldMap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst INITIAL_STATS = {\n  population: 1000000,\n  resources: 100,\n  stability: 100\n};\nconst WIN_RESOURCES = 1000;\nconst LOSE_STABILITY = 0;\nexport default function App() {\n  _s();\n  var _playerCountry$proper;\n  const [countries, setCountries] = useState([]);\n  const [playerCountry, setPlayerCountry] = useState(null);\n  const [countryStats, setCountryStats] = useState({});\n  const [turn, setTurn] = useState(1);\n  const [message, setMessage] = useState(\"\");\n  const [actionTaken, setActionTaken] = useState(false);\n\n  // Load countries on mount\n  useEffect(() => {\n    axios.get(\"http://localhost:8000/countries\").then(r => r.data).then(data => {\n      setCountries(data);\n      // Initialize stats for all countries\n      const stats = {};\n      data.forEach(c => {\n        var _c$properties;\n        const name = (_c$properties = c.properties) === null || _c$properties === void 0 ? void 0 : _c$properties.NAME;\n        stats[name] = {\n          ...INITIAL_STATS\n        };\n      });\n      setCountryStats(stats);\n    });\n  }, []);\n\n  // Player picks a country at start\n  if (!playerCountry) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Choose Your Country\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(WorldMap, {\n        onSelectCountry: c => setPlayerCountry(c),\n        selectedCountry: null\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Click a country to play as it.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Player actions\n  const invest = () => {\n    if (actionTaken) return;\n    setCountryStats(stats => ({\n      ...stats,\n      [playerCountry.properties.NAME]: {\n        ...stats[playerCountry.properties.NAME],\n        resources: stats[playerCountry.properties.NAME].resources + 50,\n        stability: stats[playerCountry.properties.NAME].stability - 2\n      }\n    }));\n    setMessage(\"You invested in your economy! Resources +50, Stability -2\");\n    setActionTaken(true);\n  };\n  const build = () => {\n    if (actionTaken) return;\n    setCountryStats(stats => ({\n      ...stats,\n      [playerCountry.properties.NAME]: {\n        ...stats[playerCountry.properties.NAME],\n        population: Math.floor(stats[playerCountry.properties.NAME].population * 1.02),\n        resources: stats[playerCountry.properties.NAME].resources - 20,\n        stability: stats[playerCountry.properties.NAME].stability + 1\n      }\n    }));\n    setMessage(\"You built infrastructure! Population +2%, Resources -20, Stability +1\");\n    setActionTaken(true);\n  };\n  const propaganda = () => {\n    if (actionTaken) return;\n    setCountryStats(stats => ({\n      ...stats,\n      [playerCountry.properties.NAME]: {\n        ...stats[playerCountry.properties.NAME],\n        stability: stats[playerCountry.properties.NAME].stability + 10,\n        resources: stats[playerCountry.properties.NAME].resources - 10\n      }\n    }));\n    setMessage(\"You ran propaganda! Stability +10, Resources -10\");\n    setActionTaken(true);\n  };\n\n  // End turn: AI and player stats update\n  const nextTurn = () => {\n    setTurn(t => t + 1);\n    setActionTaken(false);\n    setMessage(\"\");\n    setCountryStats(stats => {\n      const newStats = {\n        ...stats\n      };\n      Object.keys(newStats).forEach(name => {\n        // AI: grow pop/resources, random stability change\n        if (name !== playerCountry.properties.NAME) {\n          newStats[name] = {\n            ...newStats[name],\n            population: Math.floor(newStats[name].population * (1.01 + Math.random() * 0.01)),\n            resources: newStats[name].resources + Math.floor(Math.random() * 20),\n            stability: Math.max(0, Math.min(100, newStats[name].stability + Math.floor(Math.random() * 5 - 2)))\n          };\n        } else {\n          // Player: small passive growth\n          newStats[name] = {\n            ...newStats[name],\n            population: Math.floor(newStats[name].population * 1.01),\n            resources: newStats[name].resources + 5,\n            stability: Math.max(0, Math.min(100, newStats[name].stability))\n          };\n        }\n      });\n      return newStats;\n    });\n  };\n\n  // Win/Lose conditions\n  const stats = countryStats[playerCountry.properties.NAME];\n  if (stats.resources >= WIN_RESOURCES) {\n    return /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Victory! You built a prosperous nation.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 12\n    }, this);\n  }\n  if (stats.stability <= LOSE_STABILITY) {\n    return /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Your country collapsed due to instability. Game Over.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Empires & Revolutions\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [\"Turn: \", turn]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(WorldMap, {\n      onSelectCountry: () => {},\n      selectedCountry: playerCountry\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#222',\n        color: '#fff',\n        padding: 10,\n        margin: 10,\n        borderRadius: 8\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: (_playerCountry$proper = playerCountry.properties) === null || _playerCountry$proper === void 0 ? void 0 : _playerCountry$proper.NAME\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Population: \", stats.population.toLocaleString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Resources: \", stats.resources]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Stability: \", stats.stability]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: invest,\n        disabled: actionTaken,\n        children: \"Invest\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: build,\n        disabled: actionTaken,\n        style: {\n          marginLeft: 8\n        },\n        children: \"Build\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: propaganda,\n        disabled: actionTaken,\n        style: {\n          marginLeft: 8\n        },\n        children: \"Propaganda\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: nextTurn,\n      children: \"End Turn\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        color: 'yellow',\n        margin: 10\n      },\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 19\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"WYr2gpaVacVC02uE2zJqp5JYKKI=\");\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "WorldMap", "jsxDEV", "_jsxDEV", "INITIAL_STATS", "population", "resources", "stability", "WIN_RESOURCES", "LOSE_STABILITY", "App", "_s", "_playerCountry$proper", "countries", "setCountries", "playerCountry", "setPlayerCountry", "countryStats", "setCountryStats", "turn", "setTurn", "message", "setMessage", "actionTaken", "setActionTaken", "get", "then", "r", "data", "stats", "for<PERSON>ach", "c", "_c$properties", "name", "properties", "NAME", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSelectCountry", "selectedCountry", "invest", "build", "Math", "floor", "propaganda", "nextTurn", "t", "newStats", "Object", "keys", "random", "max", "min", "style", "background", "color", "padding", "margin", "borderRadius", "toLocaleString", "onClick", "disabled", "marginLeft", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Projects/Python/EU4/frontend/src/App.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport axios from \"axios\";\r\nimport WorldMap from './WorldMap';\r\n\r\nconst INITIAL_STATS = { population: 1000000, resources: 100, stability: 100 };\r\nconst WIN_RESOURCES = 1000;\r\nconst LOSE_STABILITY = 0;\r\n\r\nexport default function App() {\r\n  const [countries, setCountries] = useState([]);\r\n  const [playerCountry, setPlayerCountry] = useState(null);\r\n  const [countryStats, setCountryStats] = useState({});\r\n  const [turn, setTurn] = useState(1);\r\n  const [message, setMessage] = useState(\"\");\r\n  const [actionTaken, setActionTaken] = useState(false);\r\n\r\n  // Load countries on mount\r\n  useEffect(() => {\r\n    axios.get(\"http://localhost:8000/countries\")\r\n      .then(r => r.data)\r\n      .then(data => {\r\n        setCountries(data);\r\n        // Initialize stats for all countries\r\n        const stats = {};\r\n        data.forEach(c => {\r\n          const name = c.properties?.NAME;\r\n          stats[name] = { ...INITIAL_STATS };\r\n        });\r\n        setCountryStats(stats);\r\n      });\r\n  }, []);\r\n\r\n  // Player picks a country at start\r\n  if (!playerCountry) {\r\n    return (\r\n      <div>\r\n        <h1>Choose Your Country</h1>\r\n        <WorldMap onSelectCountry={c => setPlayerCountry(c)} selectedCountry={null} />\r\n        <p>Click a country to play as it.</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Player actions\r\n  const invest = () => {\r\n    if (actionTaken) return;\r\n    setCountryStats(stats => ({\r\n      ...stats,\r\n      [playerCountry.properties.NAME]: {\r\n        ...stats[playerCountry.properties.NAME],\r\n        resources: stats[playerCountry.properties.NAME].resources + 50,\r\n        stability: stats[playerCountry.properties.NAME].stability - 2\r\n      }\r\n    }));\r\n    setMessage(\"You invested in your economy! Resources +50, Stability -2\");\r\n    setActionTaken(true);\r\n  };\r\n  const build = () => {\r\n    if (actionTaken) return;\r\n    setCountryStats(stats => ({\r\n      ...stats,\r\n      [playerCountry.properties.NAME]: {\r\n        ...stats[playerCountry.properties.NAME],\r\n        population: Math.floor(stats[playerCountry.properties.NAME].population * 1.02),\r\n        resources: stats[playerCountry.properties.NAME].resources - 20,\r\n        stability: stats[playerCountry.properties.NAME].stability + 1\r\n      }\r\n    }));\r\n    setMessage(\"You built infrastructure! Population +2%, Resources -20, Stability +1\");\r\n    setActionTaken(true);\r\n  };\r\n  const propaganda = () => {\r\n    if (actionTaken) return;\r\n    setCountryStats(stats => ({\r\n      ...stats,\r\n      [playerCountry.properties.NAME]: {\r\n        ...stats[playerCountry.properties.NAME],\r\n        stability: stats[playerCountry.properties.NAME].stability + 10,\r\n        resources: stats[playerCountry.properties.NAME].resources - 10\r\n      }\r\n    }));\r\n    setMessage(\"You ran propaganda! Stability +10, Resources -10\");\r\n    setActionTaken(true);\r\n  };\r\n\r\n  // End turn: AI and player stats update\r\n  const nextTurn = () => {\r\n    setTurn(t => t + 1);\r\n    setActionTaken(false);\r\n    setMessage(\"\");\r\n    setCountryStats(stats => {\r\n      const newStats = { ...stats };\r\n      Object.keys(newStats).forEach(name => {\r\n        // AI: grow pop/resources, random stability change\r\n        if (name !== playerCountry.properties.NAME) {\r\n          newStats[name] = {\r\n            ...newStats[name],\r\n            population: Math.floor(newStats[name].population * (1.01 + Math.random() * 0.01)),\r\n            resources: newStats[name].resources + Math.floor(Math.random() * 20),\r\n            stability: Math.max(0, Math.min(100, newStats[name].stability + Math.floor(Math.random() * 5 - 2)))\r\n          };\r\n        } else {\r\n          // Player: small passive growth\r\n          newStats[name] = {\r\n            ...newStats[name],\r\n            population: Math.floor(newStats[name].population * 1.01),\r\n            resources: newStats[name].resources + 5,\r\n            stability: Math.max(0, Math.min(100, newStats[name].stability))\r\n          };\r\n        }\r\n      });\r\n      return newStats;\r\n    });\r\n  };\r\n\r\n  // Win/Lose conditions\r\n  const stats = countryStats[playerCountry.properties.NAME];\r\n  if (stats.resources >= WIN_RESOURCES) {\r\n    return <h1>Victory! You built a prosperous nation.</h1>;\r\n  }\r\n  if (stats.stability <= LOSE_STABILITY) {\r\n    return <h1>Your country collapsed due to instability. Game Over.</h1>;\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <h1>Empires & Revolutions</h1>\r\n      <div>Turn: {turn}</div>\r\n      <WorldMap onSelectCountry={() => {}} selectedCountry={playerCountry} />\r\n      <div style={{ background: '#222', color: '#fff', padding: 10, margin: 10, borderRadius: 8 }}>\r\n        <h2>{playerCountry.properties?.NAME}</h2>\r\n        <p>Population: {stats.population.toLocaleString()}</p>\r\n        <p>Resources: {stats.resources}</p>\r\n        <p>Stability: {stats.stability}</p>\r\n        <button onClick={invest} disabled={actionTaken}>Invest</button>\r\n        <button onClick={build} disabled={actionTaken} style={{marginLeft: 8}}>Build</button>\r\n        <button onClick={propaganda} disabled={actionTaken} style={{marginLeft: 8}}>Propaganda</button>\r\n      </div>\r\n      <button onClick={nextTurn}>End Turn</button>\r\n      {message && <div style={{ color: 'yellow', margin: 10 }}>{message}</div>}\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,aAAa,GAAG;EAAEC,UAAU,EAAE,OAAO;EAAEC,SAAS,EAAE,GAAG;EAAEC,SAAS,EAAE;AAAI,CAAC;AAC7E,MAAMC,aAAa,GAAG,IAAI;AAC1B,MAAMC,cAAc,GAAG,CAAC;AAExB,eAAe,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAC5B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACoB,IAAI,EAAEC,OAAO,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACAD,SAAS,CAAC,MAAM;IACdE,KAAK,CAACyB,GAAG,CAAC,iCAAiC,CAAC,CACzCC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CACjBF,IAAI,CAACE,IAAI,IAAI;MACZd,YAAY,CAACc,IAAI,CAAC;MAClB;MACA,MAAMC,KAAK,GAAG,CAAC,CAAC;MAChBD,IAAI,CAACE,OAAO,CAACC,CAAC,IAAI;QAAA,IAAAC,aAAA;QAChB,MAAMC,IAAI,IAAAD,aAAA,GAAGD,CAAC,CAACG,UAAU,cAAAF,aAAA,uBAAZA,aAAA,CAAcG,IAAI;QAC/BN,KAAK,CAACI,IAAI,CAAC,GAAG;UAAE,GAAG7B;QAAc,CAAC;MACpC,CAAC,CAAC;MACFc,eAAe,CAACW,KAAK,CAAC;IACxB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAI,CAACd,aAAa,EAAE;IAClB,oBACEZ,OAAA;MAAAiC,QAAA,gBACEjC,OAAA;QAAAiC,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BrC,OAAA,CAACF,QAAQ;QAACwC,eAAe,EAAEV,CAAC,IAAIf,gBAAgB,CAACe,CAAC,CAAE;QAACW,eAAe,EAAE;MAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9ErC,OAAA;QAAAiC,QAAA,EAAG;MAA8B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAEV;;EAEA;EACA,MAAMG,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAIpB,WAAW,EAAE;IACjBL,eAAe,CAACW,KAAK,KAAK;MACxB,GAAGA,KAAK;MACR,CAACd,aAAa,CAACmB,UAAU,CAACC,IAAI,GAAG;QAC/B,GAAGN,KAAK,CAACd,aAAa,CAACmB,UAAU,CAACC,IAAI,CAAC;QACvC7B,SAAS,EAAEuB,KAAK,CAACd,aAAa,CAACmB,UAAU,CAACC,IAAI,CAAC,CAAC7B,SAAS,GAAG,EAAE;QAC9DC,SAAS,EAAEsB,KAAK,CAACd,aAAa,CAACmB,UAAU,CAACC,IAAI,CAAC,CAAC5B,SAAS,GAAG;MAC9D;IACF,CAAC,CAAC,CAAC;IACHe,UAAU,CAAC,2DAA2D,CAAC;IACvEE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAMoB,KAAK,GAAGA,CAAA,KAAM;IAClB,IAAIrB,WAAW,EAAE;IACjBL,eAAe,CAACW,KAAK,KAAK;MACxB,GAAGA,KAAK;MACR,CAACd,aAAa,CAACmB,UAAU,CAACC,IAAI,GAAG;QAC/B,GAAGN,KAAK,CAACd,aAAa,CAACmB,UAAU,CAACC,IAAI,CAAC;QACvC9B,UAAU,EAAEwC,IAAI,CAACC,KAAK,CAACjB,KAAK,CAACd,aAAa,CAACmB,UAAU,CAACC,IAAI,CAAC,CAAC9B,UAAU,GAAG,IAAI,CAAC;QAC9EC,SAAS,EAAEuB,KAAK,CAACd,aAAa,CAACmB,UAAU,CAACC,IAAI,CAAC,CAAC7B,SAAS,GAAG,EAAE;QAC9DC,SAAS,EAAEsB,KAAK,CAACd,aAAa,CAACmB,UAAU,CAACC,IAAI,CAAC,CAAC5B,SAAS,GAAG;MAC9D;IACF,CAAC,CAAC,CAAC;IACHe,UAAU,CAAC,uEAAuE,CAAC;IACnFE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAMuB,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIxB,WAAW,EAAE;IACjBL,eAAe,CAACW,KAAK,KAAK;MACxB,GAAGA,KAAK;MACR,CAACd,aAAa,CAACmB,UAAU,CAACC,IAAI,GAAG;QAC/B,GAAGN,KAAK,CAACd,aAAa,CAACmB,UAAU,CAACC,IAAI,CAAC;QACvC5B,SAAS,EAAEsB,KAAK,CAACd,aAAa,CAACmB,UAAU,CAACC,IAAI,CAAC,CAAC5B,SAAS,GAAG,EAAE;QAC9DD,SAAS,EAAEuB,KAAK,CAACd,aAAa,CAACmB,UAAU,CAACC,IAAI,CAAC,CAAC7B,SAAS,GAAG;MAC9D;IACF,CAAC,CAAC,CAAC;IACHgB,UAAU,CAAC,kDAAkD,CAAC;IAC9DE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMwB,QAAQ,GAAGA,CAAA,KAAM;IACrB5B,OAAO,CAAC6B,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC;IACnBzB,cAAc,CAAC,KAAK,CAAC;IACrBF,UAAU,CAAC,EAAE,CAAC;IACdJ,eAAe,CAACW,KAAK,IAAI;MACvB,MAAMqB,QAAQ,GAAG;QAAE,GAAGrB;MAAM,CAAC;MAC7BsB,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACpB,OAAO,CAACG,IAAI,IAAI;QACpC;QACA,IAAIA,IAAI,KAAKlB,aAAa,CAACmB,UAAU,CAACC,IAAI,EAAE;UAC1Ce,QAAQ,CAACjB,IAAI,CAAC,GAAG;YACf,GAAGiB,QAAQ,CAACjB,IAAI,CAAC;YACjB5B,UAAU,EAAEwC,IAAI,CAACC,KAAK,CAACI,QAAQ,CAACjB,IAAI,CAAC,CAAC5B,UAAU,IAAI,IAAI,GAAGwC,IAAI,CAACQ,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YACjF/C,SAAS,EAAE4C,QAAQ,CAACjB,IAAI,CAAC,CAAC3B,SAAS,GAAGuC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACQ,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;YACpE9C,SAAS,EAAEsC,IAAI,CAACS,GAAG,CAAC,CAAC,EAAET,IAAI,CAACU,GAAG,CAAC,GAAG,EAAEL,QAAQ,CAACjB,IAAI,CAAC,CAAC1B,SAAS,GAAGsC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACQ,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;UACpG,CAAC;QACH,CAAC,MAAM;UACL;UACAH,QAAQ,CAACjB,IAAI,CAAC,GAAG;YACf,GAAGiB,QAAQ,CAACjB,IAAI,CAAC;YACjB5B,UAAU,EAAEwC,IAAI,CAACC,KAAK,CAACI,QAAQ,CAACjB,IAAI,CAAC,CAAC5B,UAAU,GAAG,IAAI,CAAC;YACxDC,SAAS,EAAE4C,QAAQ,CAACjB,IAAI,CAAC,CAAC3B,SAAS,GAAG,CAAC;YACvCC,SAAS,EAAEsC,IAAI,CAACS,GAAG,CAAC,CAAC,EAAET,IAAI,CAACU,GAAG,CAAC,GAAG,EAAEL,QAAQ,CAACjB,IAAI,CAAC,CAAC1B,SAAS,CAAC;UAChE,CAAC;QACH;MACF,CAAC,CAAC;MACF,OAAO2C,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMrB,KAAK,GAAGZ,YAAY,CAACF,aAAa,CAACmB,UAAU,CAACC,IAAI,CAAC;EACzD,IAAIN,KAAK,CAACvB,SAAS,IAAIE,aAAa,EAAE;IACpC,oBAAOL,OAAA;MAAAiC,QAAA,EAAI;IAAuC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EACzD;EACA,IAAIX,KAAK,CAACtB,SAAS,IAAIE,cAAc,EAAE;IACrC,oBAAON,OAAA;MAAAiC,QAAA,EAAI;IAAqD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EACvE;EAEA,oBACErC,OAAA;IAAAiC,QAAA,gBACEjC,OAAA;MAAAiC,QAAA,EAAI;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC9BrC,OAAA;MAAAiC,QAAA,GAAK,QAAM,EAACjB,IAAI;IAAA;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACvBrC,OAAA,CAACF,QAAQ;MAACwC,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAE;MAACC,eAAe,EAAE3B;IAAc;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACvErC,OAAA;MAAKqD,KAAK,EAAE;QAAEC,UAAU,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,YAAY,EAAE;MAAE,CAAE;MAAAzB,QAAA,gBAC1FjC,OAAA;QAAAiC,QAAA,GAAAxB,qBAAA,GAAKG,aAAa,CAACmB,UAAU,cAAAtB,qBAAA,uBAAxBA,qBAAA,CAA0BuB;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACzCrC,OAAA;QAAAiC,QAAA,GAAG,cAAY,EAACP,KAAK,CAACxB,UAAU,CAACyD,cAAc,CAAC,CAAC;MAAA;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtDrC,OAAA;QAAAiC,QAAA,GAAG,aAAW,EAACP,KAAK,CAACvB,SAAS;MAAA;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCrC,OAAA;QAAAiC,QAAA,GAAG,aAAW,EAACP,KAAK,CAACtB,SAAS;MAAA;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCrC,OAAA;QAAQ4D,OAAO,EAAEpB,MAAO;QAACqB,QAAQ,EAAEzC,WAAY;QAAAa,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC/DrC,OAAA;QAAQ4D,OAAO,EAAEnB,KAAM;QAACoB,QAAQ,EAAEzC,WAAY;QAACiC,KAAK,EAAE;UAACS,UAAU,EAAE;QAAC,CAAE;QAAA7B,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACrFrC,OAAA;QAAQ4D,OAAO,EAAEhB,UAAW;QAACiB,QAAQ,EAAEzC,WAAY;QAACiC,KAAK,EAAE;UAACS,UAAU,EAAE;QAAC,CAAE;QAAA7B,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,eACNrC,OAAA;MAAQ4D,OAAO,EAAEf,QAAS;MAAAZ,QAAA,EAAC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAC3CnB,OAAO,iBAAIlB,OAAA;MAAKqD,KAAK,EAAE;QAAEE,KAAK,EAAE,QAAQ;QAAEE,MAAM,EAAE;MAAG,CAAE;MAAAxB,QAAA,EAAEf;IAAO;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrE,CAAC;AAEV;AAAC7B,EAAA,CAtIuBD,GAAG;AAAAwD,EAAA,GAAHxD,GAAG;AAAA,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}