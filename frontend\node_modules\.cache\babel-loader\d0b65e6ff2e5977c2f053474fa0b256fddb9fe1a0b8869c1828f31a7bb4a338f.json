{"ast": null, "code": "import { BLEND_MODES } from \"@pixi/constants\";\nfunction mapWebGLBlendModesToPixi(gl, array = []) {\n  return array[BLEND_MODES.NORMAL] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.ADD] = [gl.ONE, gl.ONE], array[BLEND_MODES.MULTIPLY] = [gl.DST_COLOR, gl.ONE_MINUS_SRC_ALPHA, gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.SCREEN] = [gl.ONE, gl.ONE_MINUS_SRC_COLOR, gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.OVERLAY] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.DARKEN] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.LIGHTEN] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.COLOR_DODGE] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.COLOR_BURN] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.HARD_LIGHT] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.SOFT_LIGHT] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.DIFFERENCE] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.EXCLUSION] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.HUE] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.SATURATION] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.COLOR] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.LUMINOSITY] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.NONE] = [0, 0], array[BLEND_MODES.NORMAL_NPM] = [gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA, gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.ADD_NPM] = [gl.SRC_ALPHA, gl.ONE, gl.ONE, gl.ONE], array[BLEND_MODES.SCREEN_NPM] = [gl.SRC_ALPHA, gl.ONE_MINUS_SRC_COLOR, gl.ONE, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.SRC_IN] = [gl.DST_ALPHA, gl.ZERO], array[BLEND_MODES.SRC_OUT] = [gl.ONE_MINUS_DST_ALPHA, gl.ZERO], array[BLEND_MODES.SRC_ATOP] = [gl.DST_ALPHA, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.DST_OVER] = [gl.ONE_MINUS_DST_ALPHA, gl.ONE], array[BLEND_MODES.DST_IN] = [gl.ZERO, gl.SRC_ALPHA], array[BLEND_MODES.DST_OUT] = [gl.ZERO, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.DST_ATOP] = [gl.ONE_MINUS_DST_ALPHA, gl.SRC_ALPHA], array[BLEND_MODES.XOR] = [gl.ONE_MINUS_DST_ALPHA, gl.ONE_MINUS_SRC_ALPHA], array[BLEND_MODES.SUBTRACT] = [gl.ONE, gl.ONE, gl.ONE, gl.ONE, gl.FUNC_REVERSE_SUBTRACT, gl.FUNC_ADD], array;\n}\nexport { mapWebGLBlendModesToPixi };", "map": {"version": 3, "names": ["mapWebGLBlendModesToPixi", "gl", "array", "BLEND_MODES", "NORMAL", "ONE", "ONE_MINUS_SRC_ALPHA", "ADD", "MULTIPLY", "DST_COLOR", "SCREEN", "ONE_MINUS_SRC_COLOR", "OVERLAY", "DARKEN", "LIGHTEN", "COLOR_DODGE", "COLOR_BURN", "HARD_LIGHT", "SOFT_LIGHT", "DIFFERENCE", "EXCLUSION", "HUE", "SATURATION", "COLOR", "LUMINOSITY", "NONE", "NORMAL_NPM", "SRC_ALPHA", "ADD_NPM", "SCREEN_NPM", "SRC_IN", "DST_ALPHA", "ZERO", "SRC_OUT", "ONE_MINUS_DST_ALPHA", "SRC_ATOP", "DST_OVER", "DST_IN", "DST_OUT", "DST_ATOP", "XOR", "SUBTRACT", "FUNC_REVERSE_SUBTRACT", "FUNC_ADD"], "sources": ["C:\\Users\\<USER>\\Projects\\Python\\EU4\\frontend\\node_modules\\@pixi\\core\\src\\state\\utils\\mapWebGLBlendModesToPixi.ts"], "sourcesContent": ["import { BLEND_MODES } from '@pixi/constants';\n\n/**\n * Maps gl blend combinations to WebGL.\n * @memberof PIXI\n * @function mapWebGLBlendModesToPixi\n * @private\n * @param {WebGLRenderingContext} gl - The rendering context.\n * @param {number[][]} [array=[]] - The array to output into.\n * @returns {number[][]} Mapped modes.\n */\nexport function mapWebGLBlendModesToPixi(gl: WebGLRenderingContextBase, array: number[][] = []): number[][]\n{\n    // TODO - premultiply alpha would be different.\n    // add a boolean for that!\n    array[BLEND_MODES.NORMAL] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.ADD] = [gl.ONE, gl.ONE];\n    array[BLEND_MODES.MULTIPLY] = [gl.DST_COLOR, gl.ONE_MINUS_SRC_ALPHA, gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.SCREEN] = [gl.ONE, gl.ONE_MINUS_SRC_COLOR, gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.OVERLAY] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.DARKEN] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.LIGHTEN] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.COLOR_DODGE] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.COLOR_BURN] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.HARD_LIGHT] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.SOFT_LIGHT] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.DIFFERENCE] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.EXCLUSION] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.HUE] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.SATURATION] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.COLOR] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.LUMINOSITY] = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.NONE] = [0, 0];\n\n    // not-premultiplied blend modes\n    array[BLEND_MODES.NORMAL_NPM] = [gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA, gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.ADD_NPM] = [gl.SRC_ALPHA, gl.ONE, gl.ONE, gl.ONE];\n    array[BLEND_MODES.SCREEN_NPM] = [gl.SRC_ALPHA, gl.ONE_MINUS_SRC_COLOR, gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n\n    // composite operations\n    array[BLEND_MODES.SRC_IN] = [gl.DST_ALPHA, gl.ZERO];\n    array[BLEND_MODES.SRC_OUT] = [gl.ONE_MINUS_DST_ALPHA, gl.ZERO];\n    array[BLEND_MODES.SRC_ATOP] = [gl.DST_ALPHA, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.DST_OVER] = [gl.ONE_MINUS_DST_ALPHA, gl.ONE];\n    array[BLEND_MODES.DST_IN] = [gl.ZERO, gl.SRC_ALPHA];\n    array[BLEND_MODES.DST_OUT] = [gl.ZERO, gl.ONE_MINUS_SRC_ALPHA];\n    array[BLEND_MODES.DST_ATOP] = [gl.ONE_MINUS_DST_ALPHA, gl.SRC_ALPHA];\n    array[BLEND_MODES.XOR] = [gl.ONE_MINUS_DST_ALPHA, gl.ONE_MINUS_SRC_ALPHA];\n\n    // SUBTRACT from flash\n    array[BLEND_MODES.SUBTRACT] = [gl.ONE, gl.ONE, gl.ONE, gl.ONE, gl.FUNC_REVERSE_SUBTRACT, gl.FUNC_ADD];\n\n    return array;\n}\n"], "mappings": ";AAWO,SAASA,yBAAyBC,EAAA,EAA+BC,KAAA,GAAoB,IAC5F;EAGI,OAAAA,KAAA,CAAMC,WAAA,CAAYC,MAAM,IAAI,CAACH,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GAC3DJ,KAAA,CAAMC,WAAA,CAAYI,GAAG,IAAI,CAACN,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGI,GAAG,GACxCH,KAAA,CAAMC,WAAA,CAAYK,QAAQ,IAAI,CAACP,EAAA,CAAGQ,SAAA,EAAWR,EAAA,CAAGK,mBAAA,EAAqBL,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GACnGJ,KAAA,CAAMC,WAAA,CAAYO,MAAM,IAAI,CAACT,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGU,mBAAA,EAAqBV,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GAC3FJ,KAAA,CAAMC,WAAA,CAAYS,OAAO,IAAI,CAACX,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GAC5DJ,KAAA,CAAMC,WAAA,CAAYU,MAAM,IAAI,CAACZ,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GAC3DJ,KAAA,CAAMC,WAAA,CAAYW,OAAO,IAAI,CAACb,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GAC5DJ,KAAA,CAAMC,WAAA,CAAYY,WAAW,IAAI,CAACd,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GAChEJ,KAAA,CAAMC,WAAA,CAAYa,UAAU,IAAI,CAACf,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GAC/DJ,KAAA,CAAMC,WAAA,CAAYc,UAAU,IAAI,CAAChB,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GAC/DJ,KAAA,CAAMC,WAAA,CAAYe,UAAU,IAAI,CAACjB,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GAC/DJ,KAAA,CAAMC,WAAA,CAAYgB,UAAU,IAAI,CAAClB,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GAC/DJ,KAAA,CAAMC,WAAA,CAAYiB,SAAS,IAAI,CAACnB,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GAC9DJ,KAAA,CAAMC,WAAA,CAAYkB,GAAG,IAAI,CAACpB,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GACxDJ,KAAA,CAAMC,WAAA,CAAYmB,UAAU,IAAI,CAACrB,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GAC/DJ,KAAA,CAAMC,WAAA,CAAYoB,KAAK,IAAI,CAACtB,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GAC1DJ,KAAA,CAAMC,WAAA,CAAYqB,UAAU,IAAI,CAACvB,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GAC/DJ,KAAA,CAAMC,WAAA,CAAYsB,IAAI,IAAI,CAAC,GAAG,CAAC,GAG/BvB,KAAA,CAAMC,WAAA,CAAYuB,UAAU,IAAI,CAACzB,EAAA,CAAG0B,SAAA,EAAW1B,EAAA,CAAGK,mBAAA,EAAqBL,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GACrGJ,KAAA,CAAMC,WAAA,CAAYyB,OAAO,IAAI,CAAC3B,EAAA,CAAG0B,SAAA,EAAW1B,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGI,GAAG,GAClEH,KAAA,CAAMC,WAAA,CAAY0B,UAAU,IAAI,CAAC5B,EAAA,CAAG0B,SAAA,EAAW1B,EAAA,CAAGU,mBAAA,EAAqBV,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGK,mBAAmB,GAGrGJ,KAAA,CAAMC,WAAA,CAAY2B,MAAM,IAAI,CAAC7B,EAAA,CAAG8B,SAAA,EAAW9B,EAAA,CAAG+B,IAAI,GAClD9B,KAAA,CAAMC,WAAA,CAAY8B,OAAO,IAAI,CAAChC,EAAA,CAAGiC,mBAAA,EAAqBjC,EAAA,CAAG+B,IAAI,GAC7D9B,KAAA,CAAMC,WAAA,CAAYgC,QAAQ,IAAI,CAAClC,EAAA,CAAG8B,SAAA,EAAW9B,EAAA,CAAGK,mBAAmB,GACnEJ,KAAA,CAAMC,WAAA,CAAYiC,QAAQ,IAAI,CAACnC,EAAA,CAAGiC,mBAAA,EAAqBjC,EAAA,CAAGI,GAAG,GAC7DH,KAAA,CAAMC,WAAA,CAAYkC,MAAM,IAAI,CAACpC,EAAA,CAAG+B,IAAA,EAAM/B,EAAA,CAAG0B,SAAS,GAClDzB,KAAA,CAAMC,WAAA,CAAYmC,OAAO,IAAI,CAACrC,EAAA,CAAG+B,IAAA,EAAM/B,EAAA,CAAGK,mBAAmB,GAC7DJ,KAAA,CAAMC,WAAA,CAAYoC,QAAQ,IAAI,CAACtC,EAAA,CAAGiC,mBAAA,EAAqBjC,EAAA,CAAG0B,SAAS,GACnEzB,KAAA,CAAMC,WAAA,CAAYqC,GAAG,IAAI,CAACvC,EAAA,CAAGiC,mBAAA,EAAqBjC,EAAA,CAAGK,mBAAmB,GAGxEJ,KAAA,CAAMC,WAAA,CAAYsC,QAAQ,IAAI,CAACxC,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGI,GAAA,EAAKJ,EAAA,CAAGyC,qBAAA,EAAuBzC,EAAA,CAAG0C,QAAQ,GAE7FzC,KAAA;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}